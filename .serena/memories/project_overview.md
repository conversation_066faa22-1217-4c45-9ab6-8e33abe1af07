# 采购系统项目概览

这是一个基于Java Spring Boot的采购管理系统，整合了原微服务架构中的所有功能成为单体应用。

## 核心业务模块
- **用户系统**: 用户注册、登录、邀请机制、提现功能
- **采购需求**: 采购需求发布、管理、分类体系
- **竞价系统**: 样品竞价、管理员竞价管理
- **订单管理**: 采购订单、样品订单、物流跟踪
- **佣金系统**: 基于DDD架构的新佣金计算机制
- **货代转运**: 货代服务、运输状态跟踪
- **消息通信**: 实时聊天、通知系统、WebSocket支持
- **文件管理**: 文件上传下载、OSS集成
- **结算系统**: 采购货代结算

## 技术特色
- 单体应用架构，整合了原微服务功能
- DDD(领域驱动设计)架构应用于佣金模块
- 支持多种用户角色和权限管理
- 完整的审计日志和监控系统
- 邮箱验证和JWT认证机制