# 开发环境配置

## 必需软件
- **JDK 8+**: Java开发环境
- **Maven 3.6+**: 构建工具  
- **MySQL 5.7+**: 数据库
- **Redis**: 缓存服务
- **Git**: 版本控制

## 数据库配置
- 数据库名: `purchase_system`
- 默认端口: 3306
- 字符集: UTF-8
- 时区: Asia/Shanghai

## Redis配置
- 默认端口: 6379
- 数据库: 0

## 应用配置
- 默认端口: 8080
- 环境: monolith-app
- 文件上传限制: 500MB
- JWT过期时间: 2小时

## IDE建议
- IntelliJ IDEA
- Eclipse with Spring Tools
- VS Code with Java Extensions

## 项目启动步骤
1. 确保MySQL和Redis服务运行
2. 创建数据库并执行迁移脚本
3. 修改application.yml中的数据库连接信息
4. 运行 `mvn spring-boot:run` 启动应用
5. 访问 http://localhost:8080 验证启动成功