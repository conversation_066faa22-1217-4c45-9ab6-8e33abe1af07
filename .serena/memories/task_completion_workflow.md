# 任务完成工作流程

## 代码开发完成后的检查清单

### 1. 代码质量检查
- 确保代码符合项目的架构模式
- 检查是否正确实现DDD分层(如果涉及佣金模块)
- 验证异常处理是否完整
- 确认日志记录是否适当

### 2. 文档更新
- 根据用户规则，每次更新Java文件后都需要修改对应的同名MarkDown文件内容
- 更新API文档(如果有接口变更)
- 更新业务流程文档(如果有业务逻辑变更)

### 3. 数据库变更
- 如有数据库结构变更，需要提供迁移脚本
- 确保新增字段有默认值或非空约束
- 验证索引设计是否合理

### 4. 测试验证
- 运行单元测试：`mvn test`
- 进行集成测试验证
- 验证业务流程的端到端测试

### 5. 安全检查
- 确认新接口是否正确配置权限
- 验证数据验证和过滤
- 检查是否有敏感信息泄露

### 6. 性能考虑
- 检查数据库查询性能
- 验证缓存策略是否合理
- 考虑并发访问的影响

### 7. 部署准备
- 确认配置文件更新
- 检查环境变量设置
- 准备发布说明