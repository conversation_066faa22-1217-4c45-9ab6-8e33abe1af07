# 代码库结构

## 主要目录结构
```
backend/src/main/java/com/purchase/
├── PurchaseApplication.java          # 主启动类
├── bidding/                          # 竞价模块
├── category/                         # 分类管理
├── commission/                       # 佣金系统(DDD架构)
├── common/                          # 通用组件
├── config/                          # 配置类
├── file/                            # 文件管理
├── forwarding/                      # 货代转运
├── message/                         # 消息通信
├── order/                           # 订单管理
├── ports/                           # 港口管理
├── requirement/                     # 采购需求
├── settlement/                      # 结算系统
└── user/                            # 用户系统
```

## 关键配置文件
- `application.yml`: 主配置文件
- `pom.xml`: Maven项目配置
- `logback-spring.xml`: 日志配置

## 数据库脚本位置
- `src/main/resources/db/`: 数据库迁移脚本
- `src/main/resources/mapper/`: MyBatis XML映射文件

## 特殊架构模块
### commission模块(DDD架构)
```
commission/
├── application/service/              # 应用服务
├── domain/                          # 领域层
│   ├── entity/                      # 实体
│   ├── valueobject/                 # 值对象
│   ├── service/                     # 领域服务
│   ├── repository/                  # 仓储接口
│   └── event/                       # 领域事件
├── infrastructure/                  # 基础设施层
│   ├── po/                         # 持久化对象
│   ├── mapper/                     # 数据访问
│   ├── repository/                 # 仓储实现
│   └── config/                     # 配置
└── interfaces/                      # 接口层
    ├── web/                        # Web接口
    └── event/                      # 事件监听器
```