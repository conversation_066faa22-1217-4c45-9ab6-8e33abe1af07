# 建议的开发命令

## Maven构建命令
```bash
# 编译项目
mvn compile

# 运行测试
mvn test

# 打包应用
mvn package

# 清理并重新构建
mvn clean package

# 启动应用
mvn spring-boot:run

# 跳过测试构建
mvn package -DskipTests
```

## Windows系统命令
```cmd
# 查看目录内容
dir

# 创建目录
mkdir dirname

# 删除文件
del filename

# 复制文件
copy source destination

# 查找文件
where filename

# 查看进程
tasklist

# 结束进程
taskkill /f /pid processid
```

## Git操作命令
```bash
# 查看状态
git status

# 添加文件
git add .

# 提交更改
git commit -m "commit message"

# 推送代码
git push origin branch-name

# 拉取最新代码
git pull

# 查看分支
git branch

# 切换分支
git checkout branch-name
```

## 数据库操作
```bash
# 连接MySQL
mysql -u root -p

# 执行SQL脚本
mysql -u root -p database_name < script.sql

# 备份数据库
mysqldump -u root -p database_name > backup.sql
```