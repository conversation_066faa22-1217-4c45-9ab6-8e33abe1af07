# 测试指南

## 测试结构
```
src/test/java/com/purchase/
├── commission/                       # 佣金模块测试(重点)
│   ├── domain/                      # 领域层测试
│   └── integration/                 # 集成测试
├── forwarding/                      # 货代模块测试
└── [其他模块测试]
```

## 测试类型

### 单元测试
- 使用JUnit 5
- Mockito进行模拟
- 重点测试业务逻辑
- 值对象和实体的测试

### 集成测试  
- Spring Boot Test支持
- 数据库集成测试
- API接口测试
- 端到端业务流程测试

## 测试命令
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=ClassName

# 运行特定包的测试
mvn test -Dtest="com.purchase.commission.**"

# 生成测试报告
mvn surefire-report:report
```

## 测试重点关注
- 佣金计算逻辑的准确性
- 订单状态流转的正确性  
- 权限控制的有效性
- 数据库事务的一致性
- 异步事件处理的可靠性