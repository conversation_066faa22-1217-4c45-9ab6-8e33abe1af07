# 技术栈

## 核心框架
- **Java 8+**: 主要编程语言
- **Spring Boot**: 应用框架
- **Spring Security**: 安全认证
- **Spring WebSocket**: 实时通信
- **Maven**: 构建工具

## 数据存储
- **MySQL**: 主数据库
- **Redis**: 缓存和会话存储
- **MyBatis-Plus**: ORM框架
- **HikariCP**: 数据库连接池

## 安全认证
- **JWT**: Token认证机制
- **邮箱验证**: 用户验证系统
- **角色权限**: 基于Spring Security的权限控制

## 通信和文件
- **WebSocket**: 实时消息通信
- **STOMP**: 消息协议
- **阿里云OSS**: 文件存储
- **邮件服务**: 阿里云邮件推送

## 监控和日志
- **Logback**: 日志框架
- **Spring Actuator**: 健康检查
- **审计日志**: 自定义业务审计