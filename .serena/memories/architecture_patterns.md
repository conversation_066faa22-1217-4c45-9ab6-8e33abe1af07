# 架构模式和设计规范

## DDD架构(佣金模块)
采用领域驱动设计，分层清晰：
- **Domain层**: 实体、值对象、领域服务、仓储接口
- **Application层**: 应用服务，协调领域服务
- **Infrastructure层**: 数据访问、外部服务集成
- **Interfaces层**: 控制器、DTO、事件监听器

## 包结构规范
- **Controller**: 控制层，处理HTTP请求
- **Service**: 业务逻辑层，分为接口和实现
- **Mapper**: 数据访问层，MyBatis接口
- **Entity**: 实体类，数据库表映射
- **DTO**: 数据传输对象
- **VO**: 视图对象，用于前端展示

## 代码风格约定
- 使用驼峰命名法
- 接口与实现类分离
- 统一异常处理机制
- 分层架构严格分离
- 完整的JavaDoc注释
- 事件驱动架构应用于关键业务流程

## 数据库设计
- 逻辑删除标记：deleted字段
- 统一时间戳：created_at、updated_at
- 主键使用BIGINT自增ID
- 外键关系明确定义