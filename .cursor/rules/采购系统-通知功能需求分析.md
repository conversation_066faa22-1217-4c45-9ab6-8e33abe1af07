# 采购系统 - 通知（Notification）功能需求分析

## 1. 业务背景与需求

- **发布需求**：不需要通知。
- **发布采购竞价/货代竞价**：需要通知管理员审核。
- **审核通过后**：再通知需求发布者。

---

## 2. 相关业务流程梳理

### 2.1 采购竞价/货代竞价的业务流程
- 买家发布采购需求（不需要通知）
- 卖家/货代对需求进行竞价（此时需要通知管理员审核）
- 管理员审核竞价（审核通过/拒绝）
- 审核通过后，通知需求发布者（买家）

### 2.2 涉及的模块与接口

#### 2.2.1 招投标模块（Bidding）
- **竞价提交**（`POST /api/v1/biddings`）
  - 卖家/货代提交竞价时，需触发"通知管理员审核"。
- **竞价审核**（`PUT /api/v1/biddings/admin/{id}/audit`）
  - 管理员审核通过后，需触发"通知需求发布者"。

#### 2.2.2 角色梳理
- **需求发布者**：买家（buyer）
- **竞价方**：卖家（seller）或货代（forwarder）
- **管理员**：admin

---

## 3. 需要通知的关键节点

### 3.1 竞价提交时
- **触发时机**：卖家/货代提交采购竞价或货代竞价
- **通知对象**：管理员
- **通知内容**：有新的竞价待审核，包含竞价详情、需求信息、竞价方信息等

### 3.2 管理员审核通过时
- **触发时机**：管理员审核通过某条竞价
- **通知对象**：需求发布者（买家）
- **通知内容**：你的需求有新的竞价已通过审核，包含竞价详情、竞价方信息等

---

## 4. 代码层面需要插入通知的地方

### 4.1 竞价提交接口
- **BiddingController.submitBidding**（`POST /api/v1/biddings`）
  - 在竞价数据入库成功后，调用通知服务，推送"待审核"通知给管理员。

### 4.2 竞价审核接口
- **BiddingController.auditBidding**（`PUT /api/v1/biddings/admin/{id}/audit`）
  - 审核通过时，调用通知服务，推送"审核通过"通知给需求发布者（买家）。

---

## 5. 其他注意点

- **通知方式**：建议优先用站内信+WebSocket推送，也可扩展为邮件等。
- **通知内容**：应包含竞价ID、需求ID、竞价方、金额、时间等关键信息，便于管理员和买家快速定位。
- **通知记录**：建议在数据库中有通知表，便于后续查阅和消息中心展示。

---

## 6. 总结

**需要通知的地方只有两处：**
1. 卖家/货代提交竞价后，通知管理员审核。
2. 管理员审核通过后，通知需求发布者（买家）。

**不需要通知的地方：**
- 需求发布（买家发布需求时）

---

如需进一步细化通知内容、接口参数或数据库表结构，可在本基础上补充。
如需代码插桩点或伪代码，也可随时补充。 