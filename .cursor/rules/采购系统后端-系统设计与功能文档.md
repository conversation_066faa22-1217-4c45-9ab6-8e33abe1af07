# 采购系统后端 - 系统设计与功能文档

## 1. 系统概述

### 1.1 项目介绍
采购系统（Purchase System）是一个基于Spring Boot的单体应用，整合了原微服务架构中的所有功能。该系统支持多角色用户进行采购、供应、招投标等业务流程，并提供完整的订单管理、消息通知、结算等功能。

### 1.2 技术架构
- **框架版本**: Spring Boot 2.7.18
- **Java版本**: JDK 17
- **数据库**: MySQL 8.0.28
- **缓存**: Redis
- **ORM框架**: MyBatis Plus 3.5.3.1
- **认证授权**: Spring Security + JWT
- **文档**: Springdoc OpenAPI (Swagger 3)
- **消息通信**: WebSocket
- **文件存储**: 阿里云OSS
- **PDF处理**: Apache PDFBox

### 1.3 系统特点
- 单体应用架构，部署简单
- 基于角色的权限控制
- 支持多种业务场景（采购、样品、招投标）
- 完整的邀请佣金体系
- 实时消息通信
- 电子签名支持

## 2. 业务模块设计

### 2.1 用户管理模块 (User)

#### 核心功能
- **用户注册与登录**
  - 支持用户名/邮箱/手机号注册
  - 支持邮箱验证码登录
  - JWT令牌认证
  
- **用户信息管理**
  - 基本信息：用户名、邮箱、手机、头像等
  - 公司信息：公司名称、地址、营业执照、资质证书等
  - 联系信息：联系人、国家、省市、邮编等

- **角色权限**
  - `buyer`: 买家，可发布需求、查看投标、接受报价
  - `seller`: 卖家，可查看需求、提交投标、管理订单生产
  - `admin`: 管理员，具备所有权限
  - `forwarder`: 货代，负责物流环节

- **邀请佣金体系**
  - 支持三级邀请关系（邀请人->直接下级->间接下级）
  - 佣金计算：直接佣金、间接佣金
  - 佣金提现管理

#### 关键实体字段
```java
// 用户基本信息
private String username, password, email, phone, role, avatar;
private Integer status; // 0-禁用，1-启用
private String company, industry;

// 公司详细信息
private String contactPerson, country, province, city, postalCode;
private String detailedAddress, companyLicense, taxId, socialCreditCode;
private String businessLicenseImage, qualificationImages, companyEnvironmentImages;

// 邀请佣金相关
private String inviteCode;
private Long inviterId, grandparentId;
private BigDecimal commissionBalance, totalCommission, directCommission, indirectCommission;
```

### 2.2 采购需求模块 (Requirement)

#### 核心功能
- **需求发布**
  - 普通采购需求（purchase）
  - 样品需求（sample）
  - 需求分类管理
  - 服务级别配置（基础/标准/高级）

- **需求管理**
  - 需求状态控制
  - 规格说明、图片视频附件
  - 价格范围、交付时间
  - 海关商品编码(HS Code)

#### 关键实体字段
```java
private Long buyerId, categoryId;
private String title, description, specification;
private BigDecimal minPrice, maxPrice, quantity;
private String unit, hsCode; // 数量单位、海关编码
private Date expectedDeliveryTime;
private String images, videos;
private String requirementType; // purchase/sample
private String serviceLevel; // basic/standard/premium
private BigDecimal serviceFeeRate;
```

### 2.3 招投标模块 (Bidding)

#### 核心功能
- **投标管理**
  - 卖家投标提交
  - 投标信息更新和取消
  - 买家投标查看和选择
  - 管理员投标审核

- **投标流程**
  1. 卖家查看需求列表
  2. 提交投标报价和方案
  3. 买家查看所有投标
  4. 买家接受/拒绝投标
  5. 系统自动拒绝其他投标

#### 权限控制
- **买家**: 查看自己需求的投标，接受/拒绝投标
- **卖家**: 查看可投标需求，提交/更新/取消自己的投标
- **管理员**: 查看所有投标，审核投标状态

### 2.4 订单管理模块 (Order)

#### 核心功能
- **订单创建**
  - 基于中标结果创建订单
  - 订单项目管理
  - 电子签名支持

- **订单进度管理**
  - 阶段化进度控制（0-100%）
  - 各阶段权限分离：
    - 0%: 订单款项 - 买家负责
    - 20%: 营业执照和生产环境 - 卖家负责
    - 40%: 生产过程 - 卖家负责  
    - 60%: 检验和包装 - 管理员负责
    - 80%: 物流信息 - 货代负责
    - 100%: 验收 - 买家负责

- **订单状态**
  - `draft`: 草稿状态
  - `pending`: 待签署
  - `confirmed`: 已确认
  - `in_progress`: 进行中
  - `completed`: 已完成
  - `cancelled`: 已取消

#### 关键功能
- 订单PDF文档生成
- 付款凭证管理
- 订单进度活动记录
- 样品订单特殊处理

### 2.5 消息通信模块 (Message)

#### 核心功能
- **聊天室管理**
  - 基于订单/需求创建聊天室
  - 支持文本、图片、文件消息
  - 实时消息推送（WebSocket）

- **消息类型**
  - 系统通知
  - 用户间私聊
  - 群聊功能

### 2.6 结算模块 (Settlement)

#### 核心功能
- **采购货代结算**
  - 服务费计算
  - 结算周期管理
  - 财务对账功能

### 2.7 文件管理模块 (File)

#### 核心功能
- **文件上传**
  - 阿里云OSS集成
  - 支持图片、文档、视频
  - 文件类型和大小限制

- **文件管理**
  - 文件分类管理
  - 访问权限控制
  - 临时URL生成

### 2.8 推送通知模块 (Push)

#### 核心功能
- **消息推送**
  - WebSocket实时推送
  - 邮件通知
  - 系统消息

## 3. 数据库设计

### 3.1 核心数据表

#### 用户表 (user)
```sql
- id: 用户ID (主键)
- username: 用户名
- password: 密码
- email: 邮箱
- phone: 手机号
- role: 角色 (buyer/seller/admin/forwarder)
- company: 公司名称
- invite_code: 邀请码
- inviter_id: 邀请人ID
- commission_balance: 佣金余额
```

#### 采购需求表 (purchase_requirement)
```sql
- id: 需求ID (主键)  
- buyer_id: 买家ID
- category_id: 分类ID
- title: 需求标题
- requirement_type: 需求类型 (purchase/sample)
- service_level: 服务级别
- status: 需求状态
```

#### 投标表 (bidding)
```sql
- id: 投标ID (主键)
- requirement_id: 需求ID
- seller_id: 卖家ID
- price: 投标价格
- status: 投标状态
- submit_time: 提交时间
```

#### 订单表 (unified_order)
```sql
- id: 订单ID (主键)
- buyer_id: 买家ID
- seller_id: 卖家ID
- requirement_id: 需求ID
- status: 订单状态
- progress_percentage: 进度百分比
- total_amount: 订单总金额
```

## 4. API接口设计

### 4.1 RESTful API规范
- 统一前缀：`/api/v1/`
- 统一响应格式：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 4.2 主要接口分组

#### 用户相关接口 (`/api/v1/users`)
- `POST /register`: 用户注册
- `POST /login`: 用户登录  
- `POST /email-register`: 邮箱注册
- `GET /{id}`: 获取用户信息
- `PUT /{id}`: 更新用户信息
- `POST /{id}/password`: 修改密码

#### 需求相关接口 (`/api/v1/requirements`)
- `POST /`: 创建需求
- `GET /`: 获取需求列表
- `GET /{id}`: 获取需求详情
- `PUT /{id}`: 更新需求
- `DELETE /{id}`: 删除需求

#### 投标相关接口 (`/api/v1/biddings`)
- `POST /`: 提交投标
- `GET /requirements/{id}/biddings`: 获取需求的投标列表
- `PUT /{id}/accept`: 接受投标
- `PUT /{id}/reject`: 拒绝投标

#### 订单相关接口 (`/api/v1/orders`)
- `POST /`: 创建订单
- `GET /{id}`: 获取订单详情
- `PUT /{id}/progress`: 更新订单进度
- `POST /{id}/sign`: 签署订单

## 5. 安全设计

### 5.1 认证授权
- **JWT令牌**: 无状态认证，支持跨域
- **角色权限**: 基于Spring Security的RBAC
- **接口权限**: 使用`@PreAuthorize`注解控制

### 5.2 数据安全
- **密码加密**: BCrypt算法
- **敏感信息**: 避免明文存储
- **输入验证**: JSR-303注解验证
- **SQL注入**: MyBatis Plus预编译

### 5.3 权限细分
```java
// 阶段权限映射
private static final Map<Integer, String> STAGE_ROLE_MAPPING = Map.of(
    0, "buyer",        // 订单款项
    20, "seller",      // 营业执照和生产环境  
    40, "seller",      // 生产过程
    60, "admin",       // 检验和包装
    80, "forwarder",   // 物流信息
    100, "buyer"       // 验收
);
```

## 6. 系统配置

### 6.1 关键配置类
- `SecurityConfig`: 安全配置
- `WebConfig`: Web配置和跨域
- `RedisConfig`: Redis缓存配置
- `MyBatisPlusConfig`: 数据库配置
- `OpenApiConfig`: API文档配置

### 6.2 异步处理
- `AsyncConfig`: 异步任务配置
- 支持异步邮件发送
- 异步消息推送

## 7. 部署与运维

### 7.1 项目结构
```
purchase-system/
├── backend/           # 后端应用
├── frontend/          # 前端应用  
├── portal/           # 门户网站
└── sql/              # 数据库脚本
```

### 7.2 部署要求
- JDK 17+
- MySQL 8.0+
- Redis 6.0+
- 阿里云OSS配置

### 7.3 监控运维
- Spring Boot Actuator健康检查
- 结构化日志（Logback + Logstash）
- 请求链路追踪（MDC）

## 8. 扩展规划

### 8.1 微服务改造
当前为单体应用，未来可按业务模块拆分为微服务：
- 用户服务 (User Service)
- 需求服务 (Requirement Service)  
- 订单服务 (Order Service)
- 消息服务 (Message Service)

### 8.2 功能增强
- 移动端APP支持
- 多语言国际化
- 高级搜索和推荐
- 数据统计分析
- 第三方支付集成

---

*文档版本: 1.0*  
*更新时间: 2024年12月*  
*作者: 系统架构团队* 