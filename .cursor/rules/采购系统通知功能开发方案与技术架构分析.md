# 采购系统通知（Notification）功能开发方案与技术架构分析

## 1. 需求回顾

- **场景一**：卖家/货代提交采购竞价或货代竞价时，需通知管理员审核。
- **场景二**：管理员审核通过竞价后，需通知需求发布者（买家）。
- **场景三**：买家发布需求时，不需要通知。

## 2. 现有系统能力梳理

- **后端**：已具备WebSocket消息推送、RESTful接口、统一认证、角色权限体系。
- **前端**：已实现WebSocket长连接、Pinia状态管理、消息中心、未读数统计、系统消息展示。
- **消息模块**：支持多角色聊天室、系统消息、点对点和广播推送。
- **通知需求**：与"系统消息"高度重合，但需有独立的通知表和业务流转。

## 3. 技术方案设计

### 3.1 通知数据模型设计

建议新增一张`notification`表，核心字段如下：

| 字段名         | 类型         | 说明                 |
|----------------|--------------|----------------------|
| id             | bigint       | 主键                 |
| type           | varchar      | 通知类型（如bidding_audit等）|
| title          | varchar      | 通知标题             |
| content        | text         | 通知内容             |
| status         | int          | 状态（未读/已读/处理）|
| receiver_id    | bigint       | 接收者用户ID         |
| receiver_role  | varchar      | 接收者角色           |
| related_id     | bigint       | 关联业务ID（如竞价ID）|
| created_at     | datetime     | 创建时间             |
| read_at        | datetime     | 阅读时间             |

### 3.2 通知触发与推送流程

#### 3.2.1 竞价提交时

- **后端流程**：
  1. 卖家/货代提交竞价（BiddingController.submitBidding）。
  2. 竞价入库后，创建一条"待审核"通知，receiver为管理员。
  3. 通过WebSocket（或站内信）推送给所有管理员。
  4. 管理员在前端消息中心/通知中心收到提醒。

#### 3.2.2 竞价审核通过时

- **后端流程**：
  1. 管理员审核通过竞价（BiddingController.auditBidding）。
  2. 创建一条"审核通过"通知，receiver为需求发布者（买家）。
  3. 通过WebSocket推送给买家。
  4. 买家在前端消息中心/通知中心收到提醒。

#### 3.2.3 通知已读/处理

- 用户在前端点击通知时，调用接口将通知状态置为已读，更新read_at。

### 3.3 推送技术选型

- **WebSocket（STOMP）**：与现有消息推送体系一致，实时推送通知。
- **RESTful API**：用于通知的增删查改、已读状态变更、历史通知分页查询。
- **数据库持久化**：所有通知均入库，便于消息中心展示和历史查阅。
- **（可选）邮件/短信**：如需更强提醒，可扩展为多渠道推送。

### 3.4 前端集成方案

- **通知中心组件**：在现有消息中心基础上，增加"通知"tab，展示所有通知。
- **WebSocket订阅**：前端订阅如`/user/queue/notifications`主题，接收推送。
- **未读数展示**：Pinia store维护通知未读数，页面角标实时更新。
- **通知详情页**：点击通知可跳转到相关业务页面（如竞价详情、审核结果等）。

### 3.5 与现有消息系统的关系

- **复用WebSocket推送通道**：与聊天消息共用连接，区分不同消息类型（如type=notification）。
- **系统消息与通知分离**：系统消息用于聊天室内广播，通知用于业务流转和提醒。
- **权限控制**：通知仅推送给有权处理的用户（如管理员、需求发布者）。

---

## 4. 技术架构图（简化）

```mermaid
graph TD
A[业务事件触发] --> B[后端创建通知记录]
B --> C[WebSocket推送]
C --> D[前端通知中心]
D --> E[用户点击/处理]
E --> F[后端更新通知状态]
```

---

## 5. 推荐实现步骤

1. **后端**
   - 新增`notification`实体、表、Mapper、Service、Controller。
   - 在竞价提交、审核通过等业务流程中插入通知创建与推送逻辑。
   - 提供通知的RESTful接口（分页查询、已读、删除等）。
   - WebSocket推送通知到指定用户。

2. **前端**
   - 新增通知中心页面/组件，展示通知列表、详情、未读数。
   - WebSocket订阅通知主题，收到新通知时本地更新。
   - 支持通知已读、跳转、批量操作等交互。
   - 与现有消息中心、用户中心集成。

3. **权限与安全**
   - 通知推送前校验receiver身份与权限。
   - REST接口需鉴权，防止越权访问。

4. **可扩展性**
   - 支持多种通知类型（如竞价、订单、系统公告等）。
   - 支持多渠道推送（如邮件、短信）。

---

## 6. 关键技术选型

- **后端**：Spring Boot + MyBatis Plus + WebSocket（STOMP）+ JWT
- **前端**：Vue3 + Pinia + SockJS + STOMP.js
- **数据库**：MySQL（通知表）
- **消息推送**：WebSocket为主，REST为辅

---

## 7. 总结与建议

- 充分复用现有WebSocket和消息中心架构，降低开发成本。
- 通知与聊天消息解耦，便于后续扩展和权限管理。
- 前后端均需关注通知的未读数、已读状态、历史记录等用户体验细节。
- 后续可根据业务需要扩展更多通知类型和推送渠道。

---

*本方案为采购系统通知功能的技术实现建议，适用于当前系统架构和业务需求。*

---

如需详细表结构、接口定义或伪代码，可进一步补充。 