# 采购系统 - 消息功能模块分析文档

## 1. 模块概述

### 1.1 功能定位
消息功能模块是采购系统中的**实时通信核心组件**，为平台上的买家、卖家、货代、管理员提供完整的即时通讯能力，支持订单沟通、业务协调、客服支持等场景。

### 1.2 技术特点
- **实时通信**: 基于WebSocket + STOMP协议
- **多角色支持**: 支持4种角色同时参与聊天
- **多媒体消息**: 文本、图片、文件、系统通知
- **权限控制**: 严格的角色权限验证
- **高并发**: 支持多会话连接和在线状态管理

## 2. 架构设计

### 2.1 整体架构
```
前端WebSocket客户端
    ↓ (STOMP协议)
WebSocket端点 (/ws/message)
    ↓ (JWT认证)
认证拦截器 (AuthChannelInterceptor)
    ↓ (消息路由)
消息控制器 (ChatWebSocketController)
    ↓ (业务处理)
服务层 (ChatMessageService/ChatRoomService)
    ↓ (数据持久化)
数据访问层 (MyBatis Plus)
    ↓
MySQL数据库
```

### 2.2 核心组件

#### 2.2.1 WebSocket配置 (`WebSocketConfig`)
- **消息代理路径**:
  - `/topic`: 广播主题
  - `/queue`: 用户专属队列
  - `/user`: 用户独享消息前缀
- **客户端发送前缀**: `/app`
- **连接端点**: `/ws/message` (支持SockJS)

#### 2.2.2 认证拦截器 (`AuthChannelInterceptor`)
- JWT令牌验证
- 用户身份认证
- 权限信息设置

#### 2.2.3 事件处理器 (`WebSocketEventHandler`)
- 连接/断开事件处理
- 在线状态管理
- 用户状态广播

## 3. 数据模型设计

### 3.1 聊天室实体 (`ChatRoom`)
```java
@TableName("chat_room")
public class ChatRoom {
    private Long id;              // 聊天室ID
    private Long buyerId;         // 买家ID
    private Long sellerId;        // 卖家ID  
    private Long forwarderId;     // 货代ID
    private Long adminId;         // 管理员ID
    private LocalDateTime lastMessageTime;  // 最后消息时间
    private Integer status;       // 状态：0-关闭，1-开启
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
```

**设计特点**:
- 支持最多4个角色同时参与
- 灵活的参与者组合（至少2个角色）
- 记录最后活跃时间

### 3.2 聊天消息实体 (`ChatMessage`)
```java
@TableName("chat_message") 
public class ChatMessage {
    private Long id;              // 消息ID
    private Long roomId;          // 聊天室ID
    private Long senderId;        // 发送者ID
    private String senderRole;    // 发送者角色
    private String messageType;   // 消息类型
    private String textContent;   // 文本内容
    private String imageUrl;      // 图片URL
    private String fileUrl;       // 文件URL
    private String systemContent; // 系统消息内容
    private LocalDateTime createdAt;
}
```

**消息类型支持**:
- `text`: 普通文本消息
- `image`: 图片消息
- `file`: 文件消息
- `contract`: 合同文档
- `purchase-order`: 采购订单
- `system`: 系统通知消息

### 3.3 未读计数实体 (`ChatRoomUnread`)
```java
public class ChatRoomUnread {
    private Long id;
    private Long roomId;          // 聊天室ID
    private Long userId;          // 用户ID
    private Integer unreadCount;  // 未读消息数
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
```

## 4. 核心功能实现

### 4.1 WebSocket通信机制

#### 4.1.1 消息路由配置
```java
// 服务端推送路径
registry.enableSimpleBroker("/topic", "/queue");
// 客户端发送路径
registry.setApplicationDestinationPrefixes("/app");
// 用户专享路径
registry.setUserDestinationPrefix("/user");
```

#### 4.1.2 消息发送流程
1. 客户端发送消息到 `/app/chat.send`
2. 服务器验证用户权限和聊天室状态
3. 保存消息到数据库
4. 向聊天室所有成员推送消息
5. 更新未读计数

#### 4.1.3 消息推送机制
```java
// 发送给特定用户
messagingTemplate.convertAndSendToUser(
    userId.toString(), 
    "/queue/messages", 
    message
);

// 广播给所有用户
messagingTemplate.convertAndSend("/topic/user.status", message);
```

### 4.2 聊天室管理

#### 4.2.1 聊天室创建
**权限验证流程**:
1. 验证参与者数量（至少2个）
2. 验证创建者是参与者之一
3. 验证所有参与者的角色身份
4. 检查角色匹配正确性

**API接口**:
```java
POST /api/v1/message/rooms
参数: buyerId, sellerId, forwarderId, adminId
```

#### 4.2.2 聊天室搜索
支持多种搜索组合:
- 买家+卖家搜索
- 买家+货代搜索  
- 卖家+货代搜索

### 4.3 实时功能

#### 4.3.1 在线状态管理
```java
// 会话管理
private final ConcurrentHashMap<String, Long> sessionUsers;
// 在线用户管理（支持多会话）
private final ConcurrentHashMap<Long, Integer> onlineUsers;
```

#### 4.3.2 输入状态提示
- 客户端发送: `/app/chat.typing`
- 服务器广播给其他聊天室成员
- 支持输入开始/结束状态

#### 4.3.3 未读消息管理
- 自动递增未读计数
- 进入聊天室时清零
- 提供全局未读统计API

## 5. 权限控制机制

### 5.1 角色权限矩阵

| 角色 | 创建聊天室 | 查看聊天室 | 发送消息 | 管理权限 |
|------|------------|------------|----------|----------|
| buyer | ✅ | 自己参与的 | ✅ | ❌ |
| seller | ✅ | 自己参与的 | ✅ | ❌ |
| forwarder | ✅ | 自己参与的 | ✅ | ❌ |
| admin | ✅ | 所有 | ✅ | ✅ |

### 5.2 权限验证机制
- **聊天室参与权限**: 只有参与者才能查看和发送消息
- **角色身份验证**: 创建聊天室时验证角色匹配
- **JWT认证**: WebSocket连接必须携带有效token

## 6. 业务集成场景

### 6.1 订单沟通场景
- **参与者**: 买家 + 卖家 + 管理员
- **消息类型**: 文本、图片、文件、合同
- **使用场景**: 订单细节讨论、需求澄清、文档共享

### 6.2 物流协调场景  
- **参与者**: 买家 + 卖家 + 货代 + 管理员
- **消息类型**: 文本、图片、物流文档
- **使用场景**: 物流安排、运输状态更新、单据传递

### 6.3 客服支持场景
- **参与者**: 任意角色 + 管理员
- **消息类型**: 文本、图片、系统通知
- **使用场景**: 问题咨询、投诉处理、系统通知

## 7. 技术实现亮点

### 7.1 高并发支持
- **线程安全**: 使用ConcurrentHashMap管理状态
- **多会话支持**: 同一用户可以多设备同时在线
- **连接池管理**: WebSocket连接的生命周期管理

### 7.2 消息可靠性
- **消息持久化**: 所有消息保存到数据库
- **重连机制**: SockJS提供自动重连
- **消息确认**: 未读计数确保消息到达

### 7.3 性能优化
- **分页查询**: 历史消息按需加载
- **异步处理**: 消息发送异步执行
- **缓存优化**: 在线状态内存缓存

## 8. API接口文档

### 8.1 REST API接口

#### 聊天室管理
```http
# 获取用户聊天室列表
GET /api/v1/message/rooms?userId={userId}&role={role}&page={page}&size={size}

# 创建聊天室
POST /api/v1/message/rooms
Body: {
  "buyerId": 1,
  "sellerId": 2, 
  "forwarderId": 3,
  "adminId": 3
}

# 搜索聊天室
GET /api/v1/message/rooms/search/buyer-seller?buyerId={buyerId}&sellerId={sellerId}

# 获取聊天室详情
GET /api/v1/message/rooms/{roomId}

# 获取未读消息数
GET /api/v1/message/rooms/unread-count
```

#### 聊天消息
```http
# 获取聊天消息列表  
GET /api/v1/message/messages?roomId={roomId}&page={page}&size={size}

# 标记消息已读
POST /api/v1/message/messages/read
Body: {
  "roomId": 1,
  "messageIds": [1, 2, 3]
}
```

### 8.2 WebSocket接口

#### 连接建立
```javascript
// 连接WebSocket
const socket = new SockJS('/ws/message');
const stompClient = Stomp.over(socket);

// 连接时传递JWT token
stompClient.connect({
  'Authorization': 'Bearer ' + token
}, onConnect, onError);
```

#### 消息操作
```javascript
// 发送消息
stompClient.send('/app/chat.send', {}, JSON.stringify({
  roomId: 1,
  messageType: 'text',
  textContent: '你好',
  senderRole: 'buyer'
}));

// 发送输入状态
stompClient.send('/app/chat.typing', {}, JSON.stringify({
  roomId: 1,
  typing: true
}));

// 进入聊天室
stompClient.send('/app/chat.enter', {}, roomId);
```

#### 消息接收
```javascript
// 接收消息
stompClient.subscribe('/user/queue/messages', function(message) {
  const data = JSON.parse(message.body);
  // 处理接收到的消息
});

// 接收状态通知
stompClient.subscribe('/user/queue/status', function(message) {
  const data = JSON.parse(message.body);
  // 处理状态变化
});

// 接收用户状态广播
stompClient.subscribe('/topic/user.status', function(message) {
  const data = JSON.parse(message.body);
  // 处理用户上线/离线
});
```

## 9. 部署与配置

### 9.1 依赖配置
```xml
<!-- WebSocket支持 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-websocket</artifactId>
</dependency>

<!-- 消息模板 -->
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-messaging</artifactId>
</dependency>
```

### 9.2 配置参数
```yaml
# WebSocket配置
spring:
  websocket:
    broker:
      simple:
        prefix: /topic,/queue
      application-prefix: /app
      user-prefix: /user
    stomp:
      endpoint: /ws/message
      allowed-origins: "*"
```

## 10. 监控与运维

### 10.1 关键指标监控
- WebSocket连接数
- 在线用户数量
- 消息发送频率
- 消息失败率
- 连接断开率

### 10.2 日志记录
- 连接建立/断开日志
- 消息发送/接收日志
- 权限验证失败日志
- 异常错误日志

### 10.3 性能调优
- 连接池大小调整
- 消息队列缓冲区配置
- 心跳检测间隔设置
- 超时时间配置

## 11. 扩展规划

### 11.1 功能扩展
- **语音通话**: 集成WebRTC语音通话
- **视频通话**: 支持视频会议功能
- **文件预览**: 在线文档预览功能
- **消息翻译**: 多语言实时翻译
- **消息搜索**: 历史消息全文搜索

### 11.2 技术升级
- **微服务拆分**: 独立的消息服务
- **消息队列**: 引入RabbitMQ/Kafka
- **分布式缓存**: Redis集群支持
- **CDN加速**: 多媒体文件CDN分发

---

*文档版本: 1.0*  
*更新时间: 2024年12月*  
*作者: 系统架构团队* 