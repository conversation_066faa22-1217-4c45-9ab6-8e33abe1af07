---
description: 
globs: frontend/**/*
alwaysApply: false
---
---
description: Backend开发规范
globs: ["frontend/**/*"]
alwaysApply: true



---




 - 数据
 直接使用后端响应的字段，不要使用前端映射

 - 布局
 使用Vuetify官方布局系统


# 布局组件指南

## 导航抽屉组件

前端使用Vuetify 3实现了响应式的导航抽屉。主要布局组件位于[frontend/src/views/buyer/Layout.vue](mdc:frontend/src/views/buyer/Layout.vue)。

这个布局组件实现了以下功能：
- 响应式导航抽屉，在PC端固定显示，在移动端可折叠
- 支持导航抽屉收起/展开功能（rail模式）
- 确保导航抽屉不随页面滚动
- 根据屏幕尺寸自动调整布局

### 关键属性

Vuetify导航抽屉的关键属性：
- `:rail` - 控制导航抽屉的收起状态（窄模式）
- `:temporary` - 在小屏幕上设置为临时抽屉
- `:location="start"` - 设置导航抽屉位置
- `border` 和 `elevation` - 控制视觉效果
### 注意事项

- 不要使用自定义CSS来固定导航抽屉位置，应该使用Vuetify内置的布局系统
- 切换抽屉状态时使用`@click:rail`事件
- 使用`useDisplay`钩子检测屏幕尺寸变化
