# 采购系统前端消息功能与后端集成分析

## 1. 前端消息功能架构

### 1.1 主要模块与组件

- **API层**（`src/api/chat.js`）
  - 提供聊天室、消息、未读统计等RESTful接口的封装。
  - 通过`request`模块与后端HTTP接口交互。

- **WebSocket服务**（`src/utils/websocket.js`）
  - 使用`SockJS`和`@stomp/stompjs`实现WebSocket长连接。
  - 支持JWT认证，自动重连，主题订阅与消息发送。
  - 通过STOMP协议与后端WebSocket服务集成，支持点对点和广播消息。

- **状态管理**（`src/store/chat.js`）
  - 使用Pinia管理聊天室、消息、未读数、在线状态等。
  - 负责WebSocket连接、消息推送、聊天室和消息的本地状态同步。

- **核心组件**
  - `components/chat/ChatRoom.vue`：聊天室主界面，集成消息列表、输入框、聊天室信息等。
  - `components/chat/MessageList.vue`：消息展示，支持文本、图片、文件、系统消息等多种类型。
  - `components/chat/MessageInput.vue`：消息输入与发送，支持文本、图片、文件上传，输入状态提示。

---

## 2. 前后端集成方式

### 2.1 RESTful API集成

- **获取聊天室列表**：`GET /message/rooms`
- **创建聊天室**：`POST /message/rooms`
- **获取消息列表**：`GET /message/room/{roomId}`
- **发送消息**：`POST /message/send`
- **获取未读统计**：`GET /message/rooms/unread-count`

前端通过`src/api/chat.js`封装的函数调用这些接口，数据流入Pinia store并驱动组件渲染。

### 2.2 WebSocket集成

- **连接建立**：前端通过`/ws/message`端点建立WebSocket连接，携带JWT token进行认证。
- **消息订阅**：
  - `/user/queue/messages`：接收点对点消息
  - `/user/queue/status`：接收输入状态等
  - `/topic/user.status`：接收全局用户在线状态
- **消息发送**：
  - 通过`stompClient.send('/app/chat.send', {}, body)`发送聊天消息
  - 通过`/app/chat.typing`等发送输入状态

WebSocket连接和消息分发由`webSocketService`统一管理，store负责处理和分发到各组件。

---

## 3. 主要业务流程

### 3.1 聊天室与消息

- 用户进入聊天页面，store拉取聊天室列表和历史消息。
- 组件通过WebSocket订阅消息主题，实时接收新消息。
- 用户发送消息时，先通过WebSocket推送到后端，后端再广播到相关用户。
- 支持多种消息类型（文本、图片、文件、系统通知等）。

### 3.2 输入状态与在线状态

- 输入框监听用户输入，实时通过WebSocket推送"正在输入"状态。
- 其他成员收到后可显示"对方正在输入"提示。
- 用户上线/离线状态通过WebSocket全局广播，前端实时更新。

### 3.3 未读消息统计

- 进入聊天室时自动清零未读数。
- store定时拉取未读统计，或通过WebSocket推送更新。

---

## 4. 技术亮点

- **WebSocket与RESTful API结合**：历史消息、聊天室管理用REST，实时消息用WebSocket。
- **JWT安全认证**：WebSocket连接和API请求均需携带token，保证安全。
- **多端同步**：支持多设备同时在线，消息和状态实时同步。
- **高可用**：自动重连、断线重试、消息本地缓存。
- **丰富的消息类型和交互体验**：支持多媒体、系统通知、输入状态、在线状态等。

---

## 5. 与后端的集成要点

- 前端WebSocket与后端`/ws/message`端点对接，认证和消息协议完全兼容。
- 消息、聊天室、未读数等接口与后端REST API一一对应。
- 消息推送、输入状态、在线状态等通过STOMP主题与后端事件联动。
- store和组件解耦，便于维护和扩展。

---

*本分析为后续前后端联调、功能扩展和问题排查提供参考。*

---

如需更细致的代码级流程或某一具体功能的交互细节，可进一步补充。 