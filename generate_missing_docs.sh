#!/bin/bash

# 批量生成缺失的Java文件MD文档脚本
# 使用方法: ./generate_missing_docs.sh

echo "开始批量生成缺失的Java文件MD文档..."

# 计数器
total_count=0
generated_count=0

# 查找所有缺少MD文档的Java文件
find backend/src/main/java/com/purchase -name "*.java" -not -path "*/test/*" -not -name "*Test.java" -not -name "*Tests.java" | while read javafile; do
    mdfile="${javafile%.java}.md"
    
    # 如果MD文件不存在，则生成
    if [ ! -f "$mdfile" ]; then
        total_count=$((total_count + 1))
        
        # 提取文件信息
        filename=$(basename "$javafile" .java)
        package_path=$(dirname "$javafile" | sed 's|backend/src/main/java/||')
        package_name=$(echo "$package_path" | tr '/' '.')
        
        # 判断文件类型
        file_type="类"
        if [[ $filename == *"DTO" ]] || [[ $filename == *"Request" ]] || [[ $filename == *"Response" ]]; then
            file_type="数据传输对象"
        elif [[ $filename == *"Controller" ]]; then
            file_type="控制器"
        elif [[ $filename == *"Service" ]]; then
            file_type="服务"
        elif [[ $filename == *"Repository" ]] || [[ $filename == *"Mapper" ]]; then
            file_type="数据访问"
        elif [[ $filename == *"Entity" ]]; then
            file_type="实体"
        elif [[ $filename == *"Config" ]]; then
            file_type="配置类"
        elif [[ $filename == *"Exception" ]] || [[ $filename == *"Handler" ]]; then
            file_type="异常处理"
        elif [[ $filename == *"Enum" ]]; then
            file_type="枚举"
        elif [[ $filename == *"Util" ]] || [[ $filename == *"Helper" ]]; then
            file_type="工具类"
        fi
        
        # 生成MD文档内容
        cat > "$mdfile" << EOF
# $filename $file_type文档

## 文件概述

\`$filename\` 是 $file_type，位于 \`$package_name\` 包中。

## 核心功能

### 主要职责
- 待补充：请根据具体业务需求完善功能描述

### 业务特点
- 待补充：请根据具体业务场景完善特点描述

## 接口说明

### 核心方法/字段
待补充：请根据具体代码实现完善接口说明

## 使用示例

### 基础使用
\`\`\`java
// 待补充：请添加具体的使用示例
\`\`\`

### 集成示例
\`\`\`java
// 待补充：请添加集成使用示例
\`\`\`

## 注意事项

### 使用注意事项
1. 待补充：请根据具体实现添加注意事项
2. 待补充：请添加性能相关注意事项
3. 待补充：请添加安全相关注意事项

### 扩展说明
- 待补充：请添加扩展和定制说明

---

> **注意**: 这是自动生成的基础文档模板，请根据具体的代码实现和业务需求进行完善。
> 
> **文件路径**: \`$javafile\`
> 
> **生成时间**: $(date '+%Y-%m-%d %H:%M:%S')
EOF
        
        generated_count=$((generated_count + 1))
        echo "已生成: $mdfile"
    fi
done

echo "批量生成完成！"
echo "总计处理文件: $total_count"
echo "成功生成文档: $generated_count"
echo ""
echo "注意事项："
echo "1. 生成的是基础模板，需要根据具体代码实现进行完善"
echo "2. 建议优先完善核心业务模块的文档"
echo "3. 可以参考已有的高质量文档进行完善"
echo ""
echo "下一步建议："
echo "1. 运行 'find backend/src/main/java/com/purchase -name \"*.md\" | wc -l' 验证文档数量"
echo "2. 选择重要的文件进行详细文档完善"
echo "3. 建立文档维护和更新机制"
