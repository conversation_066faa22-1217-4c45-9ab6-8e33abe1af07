---
description:
globs:
alwaysApply: false
---
# Java文档标准模板

使用以下模板创建Java文件的Markdown文档：

## Controller类文档模板

```markdown
# [ClassName]Controller

## 文件概述

该控制器负责处理[具体业务领域]相关的HTTP请求。作为MVC架构中的控制层，它接收前端请求，调用相应的服务层方法，并返回处理结果。

### 设计思路
- 遵循RESTful API设计原则
- 统一的异常处理和响应格式
- 参数验证和权限控制

## 核心功能

### 主要接口

#### 1. [方法名称]
- **路径**: `[HTTP方法] [URL路径]`
- **功能**: [描述功能]
- **参数**: [参数说明]
- **返回**: [返回值说明]

### 关键方法

#### [方法名]([参数])
- **功能**: [方法功能描述]
- **参数说明**:
  - `param1`: [参数1说明]
  - `param2`: [参数2说明]
- **返回值**: [返回值类型和说明]
- **异常**: [可能抛出的异常]

## 使用示例

### API调用示例

\```bash
# [具体的curl命令示例]
curl -X POST http://localhost:8080/api/[path] \
  -H "Content-Type: application/json" \
  -d '{
    "[字段名]": "[值]"
  }'
\```

### 响应示例

\```json
{
  "code": 200,
  "message": "success",
  "data": {
    "[字段名]": "[值]"
  }
}
\```

## 注意事项

- **权限验证**: [权限相关说明]
- **参数验证**: [参数验证规则]
- **错误处理**: [错误处理机制]
- **性能考虑**: [性能相关注意事项]
```

## Service类文档模板

```markdown
# [ClassName]Service

## 文件概述

该服务类实现[具体业务领域]的核心业务逻辑。作为业务层组件，它封装了复杂的业务规则，协调多个数据访问对象，确保业务操作的完整性和一致性。

### 设计思路
- 业务逻辑封装
- 事务管理
- 数据转换和验证

## 核心功能

### 主要业务方法

#### [方法名]([参数])
- **功能**: [业务功能描述]
- **业务规则**: [相关业务规则]
- **事务处理**: [事务处理说明]

## 使用示例

\```java
@Autowired
private [ClassName]Service [serviceName]Service;

// 使用示例
[ResultType] result = [serviceName]Service.[methodName]([parameters]);
\```

## 注意事项

- **事务管理**: [事务相关说明]
- **异常处理**: [异常处理策略]
- **性能优化**: [性能优化建议]
```

## Entity类文档模板

```markdown
# [ClassName]

## 文件概述

该实体类对应数据库中的[表名]表，用于[业务用途描述]。实现了JPA实体规范，支持对象关系映射。

### 设计思路
- 领域驱动设计
- JPA实体映射
- 数据验证

## 核心功能

### 主要字段

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| [field1] | [type] | [description] | [constraints] |
| [field2] | [type] | [description] | [constraints] |

### 关联关系

- **[关联名称]**: [关联描述]

## 使用示例

\```java
// 创建实体
[ClassName] [objectName] = new [ClassName]();
[objectName].set[Field]([value]);

// JPA查询示例
List<[ClassName]> results = repository.findBy[Field]([parameter]);
\```

## 注意事项

- **数据库约束**: [约束说明]
- **级联操作**: [级联操作说明]
- **索引优化**: [索引相关建议]
```

## DTO类文档模板

```markdown
# [ClassName]DTO

## 文件概述

该数据传输对象用于[具体传输场景]，确保前后端数据交互的标准化和安全性。

### 设计思路
- 数据传输优化
- 安全性考虑
- 验证规则统一

## 核心功能

### 字段说明

| 字段名 | 类型 | 必填 | 说明 | 验证规则 |
|--------|------|------|------|----------|
| [field1] | [type] | [required] | [description] | [validation] |

## 使用示例

\```java
// 创建DTO
[ClassName]DTO dto = new [ClassName]DTO();
dto.set[Field]([value]);

// 验证
if (dto.isValid()) {
    // 处理逻辑
}
\```

## 注意事项

- **数据验证**: [验证规则说明]
- **安全考虑**: [安全相关注意事项]
- **性能影响**: [性能相关说明]
```

## 使用说明

1. 根据Java文件类型选择对应模板
2. 替换模板中的占位符`[内容]`为实际内容
3. 根据具体情况调整章节内容
4. 保持文档与代码同步更新
