---
description:
globs:
alwaysApply: false
---
# User包文档维护规则

针对 `com.purchase.user` 包的特殊文档要求。

## 用户模块特殊要求

### 安全性文档
由于用户模块涉及敏感信息，每个文件的文档必须包含：
- **安全考虑**章节，说明数据保护措施
- **权限控制**说明，明确访问权限要求
- **敏感数据处理**，说明个人信息保护措施

### 业务流程文档
- **用户注册流程**相关文件需详细说明业务流程
- **身份验证**相关文件需说明认证机制
- **权限管理**相关文件需说明授权流程

### API文档要求
Controller文件必须包含：
- 完整的API端点说明
- 请求/响应示例
- 错误代码说明
- 权限要求说明

### 数据库设计文档
Entity文件必须包含：
- 表结构设计说明
- 字段约束详细说明
- 索引设计考虑
- 数据迁移注意事项

## 文档检查清单

每次修改user包中的Java文件时，确保：
- [ ] 对应的.md文档已创建/更新
- [ ] 安全相关说明已完善
- [ ] API文档已同步更新
- [ ] 示例代码已验证
- [ ] 注意事项已补充

@documentation-template.mdc
