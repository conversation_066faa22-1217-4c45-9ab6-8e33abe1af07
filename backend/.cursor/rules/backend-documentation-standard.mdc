---
description: 
globs: 
alwaysApply: false
---
# Backend项目文档维护标准

## 强制要求

**每次修改backend项目中的Java代码后，都必须同时修改对应的同名Markdown说明文档。**

### 文档命名规则
- Java文件: `ClassName.java`
- 文档文件: `ClassName.md`
- 位置: 与Java文件在同一目录

### 必需的文档内容

#### 1. 文件概述
- **作用说明**: 解释该文件在包中的作用和设计思路
- **架构位置**: 说明该类/接口在系统架构中的位置
- **交互关系**: 与其他模块的交互关系
- **设计思路**: 关键设计决策和考虑因素

#### 2. 核心功能
- **主要方法**: 列举并详细说明主要类型、接口和函数
- **字段属性**: 重要字段和属性的说明
- **业务逻辑**: 关键业务逻辑的解释
- **技术实现**: 重要技术实现细节

#### 3. 使用示例
- **代码示例**: 提供清晰的代码示例（如果适用）
- **API调用**: API调用示例
- **配置示例**: 配置示例
- **集成示例**: 与其他组件的集成示例

#### 4. 注意事项
- **使用陷阱**: 提示潜在的使用陷阱或特殊情况
- **性能考虑**: 性能相关注意事项
- **安全注意**: 安全注意事项
- **异常处理**: 异常处理说明
- **最佳实践**: 使用最佳实践建议

### 包级别README.md要求

每个包都需要包含README.md，内容包括：

#### 必需章节
- **包的整体介绍和主要功能**
- **环境要求和依赖**
- **快速上手示例**
- **目录结构概览**
- **核心组件说明**
- **API接口示例**
- **配置参数说明**
- **性能优化建议**
- **故障排查指南**
- **扩展开发指南**

## 文档质量标准

### 语言要求
- **中文编写**: 所有文档必须使用中文
- **表达清晰**: 避免技术黑话，表达清晰易懂
- **逻辑连贯**: 文档结构逻辑清晰，层次分明

### 格式要求
- **Markdown格式**: 使用标准Markdown语法
- **标题层级**: 使用清晰的标题层级（#, ##, ###）
- **代码高亮**: 代码示例使用适当的语法高亮
- **表格格式**: 复杂数据使用表格展示
- **链接引用**: 包含必要的内部和外部链接

### 内容要求
- **准确性**: 确保文档内容与代码实现保持一致
- **完整性**: 覆盖所有重要功能和使用场景
- **实用性**: 提供实际可用的示例和指导
- **时效性**: 及时更新，反映最新的代码变更

## 执行流程

### 代码修改时的文档维护流程

1. **修改Java代码**
2. **立即检查对应的.md文档是否存在**
3. **如果文档不存在，必须创建新文档**
4. **如果文档存在，必须更新以反映代码变更**
5. **确保文档内容完整且准确**
6. **提交代码时同时提交文档更新**

### 文档审查要点

#### 技术审查
- [ ] 文档是否与代码实现一致
- [ ] 示例代码是否可以运行
- [ ] API文档是否准确
- [ ] 配置说明是否正确

#### 内容审查
- [ ] 文档结构是否完整
- [ ] 语言表达是否清晰
- [ ] 示例是否实用
- [ ] 注意事项是否充分

#### 格式审查
- [ ] Markdown语法是否正确
- [ ] 代码高亮是否适当
- [ ] 表格格式是否规范
- [ ] 链接是否有效

## 特殊文件类型的文档要求

### Controller类
- **API端点说明**: 完整的REST API文档
- **请求响应示例**: 包含请求和响应的JSON示例
- **错误码说明**: 详细的错误处理说明
- **权限要求**: 明确的权限和认证要求

### Service类
- **业务逻辑说明**: 详细的业务流程描述
- **事务处理**: 事务边界和回滚策略
- **性能考虑**: 性能优化建议
- **依赖关系**: 与其他服务的依赖关系

### Entity类
- **数据库映射**: 详细的表结构说明
- **字段约束**: 数据库约束和验证规则
- **关联关系**: JPA关联关系说明
- **索引设计**: 推荐的索引配置

### Mapper类
- **查询说明**: 自定义SQL查询的说明
- **性能优化**: 查询性能优化建议
- **参数说明**: 方法参数的详细说明
- **返回值说明**: 返回值格式和含义

### DTO类
- **传输场景**: 数据传输的具体使用场景
- **验证规则**: 字段验证规则和约束
- **转换说明**: 与Entity的转换关系
- **版本兼容**: API版本兼容性说明

## 工具和模板

### 推荐工具
- **Markdown编辑器**: Typora, Mark Text
- **代码格式化**: Prettier, IntelliJ IDEA
- **文档预览**: GitHub, GitLab预览
- **图表工具**: Mermaid, PlantUML

### 文档模板
参考 @documentation-template.mdc 中提供的标准模板

## 违规处理

### 检查机制
- **Code Review**: 代码审查时检查文档完整性
- **CI/CD集成**: 自动检查文档同步性
- **定期审计**: 定期检查文档质量

### 责任分工
- **开发者**: 负责文档的创建和维护
- **Tech Lead**: 负责文档质量审查
- **项目经理**: 负责文档标准执行监督

## 持续改进

### 文档质量指标
- 文档覆盖率: 目标100%
- 文档准确性: 定期验证
- 文档使用频率: 统计和分析
- 用户反馈: 收集和改进

### 标准更新
- 定期回顾和更新文档标准
- 收集团队反馈和建议
- 结合项目实际情况调整要求
- 保持与行业最佳实践同步

严格遵守此标准，确保代码和文档的高质量和一致性！

