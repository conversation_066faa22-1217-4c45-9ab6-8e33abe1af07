---
description:
globs:
alwaysApply: false
---
# Java代码文档维护规则

当修改backend项目中的Java代码时，必须同时创建或更新对应的同名Markdown说明文档。

## 文档要求

### 对于每个Java文件，需要创建同名的.md文档，包含以下内容：

1. **文件概述**
   - 解释该文件在包中的作用和设计思路
   - 说明该类/接口在系统架构中的位置
   - 与其他模块的交互关系

2. **核心功能**
   - 列举并详细说明主要类型、接口和函数
   - 重要字段和属性的说明
   - 关键业务逻辑的解释

3. **使用示例**
   - 提供清晰的代码示例（如果适用）
   - API调用示例
   - 配置示例

4. **注意事项**
   - 提示潜在的使用陷阱或特殊情况
   - 性能考虑
   - 安全注意事项
   - 异常处理说明

### 对于包级别的README.md，需要包含：

- **包的整体介绍和主要功能**
- **安装方法**（如果适用）
- **快速上手示例**
- **目录结构概览**
- **API文档链接**
- **变更日志**

## 文档格式要求

- 使用中文编写
- 保持风格一致
- 避免冗余内容
- 使用清晰的标题层级
- 代码示例使用适当的语法高亮
- 包含必要的链接和交叉引用

## 执行要求

每次修改Java文件时：
1. 立即检查是否存在对应的.md文档
2. 如果不存在，必须创建
3. 如果存在，必须更新以反映代码变更
4. 确保文档与代码保持同步

## 示例结构

```
UserController.java
UserController.md

UserService.java  
UserService.md

User.java
User.md
```

严格遵守此规则，确保代码和文档的一致性！
