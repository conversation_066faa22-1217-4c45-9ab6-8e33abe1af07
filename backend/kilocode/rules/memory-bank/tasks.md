## 为 Java 包生成文档

**上次执行时间:** 2025-07-01

**任务描述:** 为指定 Java 包及其所有子包中的 Java 文件生成详细的 Markdown 说明文档。每个文档应包含文件概述、核心功能、使用示例和注意事项。包级别的 README.md 需包含包的整体介绍、主要功能、安装方法、快速上手示例和目录结构概览。

**文件修改范围:**
- `kilocode/rules/memory-bank/{java_package_path}/README.md`
- `kilocode/rules/memory-bank/{java_package_path}/{sub_package_path}/{FileName}.md`

**工作流程:**
1.  获取目标 Java 包下的所有文件列表。
2.  为目标 Java 包创建包级别的 `README.md` 文件。
3.  遍历目标 Java 包下的每个 Java 文件，并为它们生成同名的 Markdown 说明文档。
    -   读取 Java 文件内容以理解其作用、核心功能、使用示例和注意事项。
    -   文档内容应包含：文件概述、核心功能、使用示例（如果适用）、注意事项。
    -   所有文档都应使用中文，保持风格一致，避免冗余。
4.  将所有生成的 Markdown 文档写入 `kilocode/rules/memory-bank/` 目录下的对应子目录中。

**重要注意事项:**
-   生成的文档应放置在 `kilocode/rules/memory-bank/` 目录下，并遵循与源代码相同的包结构。
-   避免在项目源代码目录（如 `src/main/java`）下直接生成 Markdown 文件，以防止与 Maven 构建冲突。
-   如果遇到文件写入冲突，可能需要手动清理冲突文件。