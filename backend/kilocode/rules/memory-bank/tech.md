# 技术栈

## 核心技术
-   **后端框架**: Spring Boot 2.7.18
-   **编程语言**: Java 17
-   **数据库**: MySQL 8.0.28
-   **ORM 框架**: MyBatis-Plus 3.5.3.1
-   **缓存**: Redis (通过 `spring-boot-starter-data-redis`)
-   **API 文档**: Springdoc OpenAPI UI 1.7.0 (Swagger 3)
-   **安全**: Spring Security (通过 `spring-boot-starter-security`)
-   **JSON Web Token (JWT)**: jjwt 0.9.1
-   **实时通信**: Spring WebSocket (通过 `spring-boot-starter-websocket`)
-   **文件存储**: 阿里云 OSS SDK 3.18.1
-   **日志**: SLF4J 1.7.30, Logback Classic 1.2.3, Logstash Logback Encoder 7.4
-   **邮件服务**: Spring Boot Starter Mail, Thymeleaf (用于邮件模板)
-   **PDF 处理**: Apache PDFBox 2.0.24
-   **工具库**: Lombok, Apache Commons Lang3

## 开发环境设置
-   **构建工具**: Maven
-   **IDE**: 推荐使用 IntelliJ IDEA 或 VS Code (配合 Java 扩展)
-   **数据库客户端**: MySQL Workbench, DataGrip 或其他兼容工具
-   **Redis 客户端**: Redis Desktop Manager 或其他兼容工具

## 依赖管理
所有项目依赖均通过 `pom.xml` 文件进行管理。主要依赖包括：
-   `spring-boot-starter-web`: 构建 Web 应用
-   `spring-boot-starter-security`: 安全框架
-   `spring-boot-starter-data-redis`: Redis 集成
-   `mybatis-plus-boot-starter`: MyBatis-Plus 集成
-   `mysql-connector-java`: MySQL 驱动
-   `spring-boot-starter-websocket`: WebSocket 支持
-   `spring-boot-starter-validation`: 数据校验
-   `spring-boot-starter-actuator`: 生产就绪特性
-   `spring-boot-starter-mail`: 邮件发送
-   `spring-boot-starter-thymeleaf`: 模板引擎
-   `aliyun-sdk-oss`: 阿里云 OSS
-   `springdoc-openapi-ui`: API 文档
-   `jjwt`: JWT 库
-   `lombok`: 简化 Java 代码
-   `jackson-datatype-jsr310`: Jackson 对 Java 8 日期时间 API 的支持
-   `commons-lang3`: Apache 通用工具类

## 测试框架
-   **单元/集成测试**: Spring Boot Starter Test, JUnit 4/5, Mockito, AssertJ
-   **内存数据库**: H2 Database (用于测试环境)
-   **容器化测试**: TestContainers (用于集成测试，支持 MySQL 容器)

## 技术约束与注意事项
-   **Java 版本**: 必须使用 Java 17 或更高版本。
-   **JAXB 兼容性**: `jaxb-runtime` 版本限制在 2.3.3，以避免与 Java 17 模块化冲突。
-   **Swagger/OpenAPI 版本**: 使用 `springdoc-openapi-ui` 替代旧版 `springfox-swagger2`，提供更好的 OpenAPI 3 支持。