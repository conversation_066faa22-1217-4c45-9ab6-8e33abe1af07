# 系统架构

## 架构概述
本项目是一个基于 Spring Boot 的单体应用，采用经典的三层架构（Controller、Service、Mapper），并在此基础上根据业务领域划分为多个模块。系统通过 RESTful API 提供服务，并利用 WebSocket 实现实时通信。

## 主要模块
系统根据业务功能划分为以下核心模块：

-   **`bidding` (竞价)**: 处理产品竞价相关的业务逻辑。
-   **`category` (分类)**: 管理产品和需求的分类信息。
-   **`common` (通用)**: 提供跨模块的通用功能，如统一响应结构、异常处理、日志工具等。
-   **`config` (配置)**: 集中管理 Spring Security、Redis、MyBatis-Plus、WebSocket 等核心配置。
-   **`file` (文件)**: 负责文件的上传、下载和管理，集成阿里云 OSS。
-   **`forwarding` (货代)**: 包含货代竞价、货代订单、货代需求等子模块，处理物流相关业务。
-   **`message` (消息)**: 提供站内信、聊天消息、通知等功能，通过 WebSocket 实现实时消息推送。
-   **`order` (订单)**: 管理采购订单、订单进度、支付凭证和样品订单等。
-   **`ports` (港口)**: 管理港口信息。
-   **`requirement` (需求)**: 处理采购需求的发布和管理。
-   **`settlement` (结算)**: 处理结算相关的业务逻辑。
-   **`user` (用户)**: 管理用户认证、授权和用户信息。

## 模块关系图
```mermaid
graph TD
    User --> Bidding;
    User --> Requirement;
    User --> Order;
    User --> Message;
    User --> File;
    User --> Forwarding;
    User --> Settlement;

    Requirement -- 发布 --> Bidding;
    Bidding -- 审核 --> Admin;
    Bidding -- 接受 --> Order;
    Order -- 自动生成 --> ForwardingRequirement;
    ForwardingRequirement -- 竞价 --> ForwardingBidding;
    ForwardingBidding -- 审核 --> Admin;
    ForwardingBidding -- 接受 --> ForwardingOrder;

    subgraph Core Modules
        Bidding;
        Category;
        File;
        Forwarding;
        Message;
        Order;
        Ports;
        Requirement;
        Settlement;
        User;
    end

    subgraph Infrastructure
        Config;
        Common;
    end

    CoreModules --> Infrastructure;
    Message -- WebSocket --> Client;
    File -- OSS --> ExternalStorage;
    Config -- Security --> User;
    Config -- Redis --> Cache;
    Config -- Database --> MySQL;
```

## 关键技术决策
-   **数据访问**: 使用 MyBatis-Plus 作为 ORM 框架，简化数据库操作。
-   **缓存机制**: 采用 Redis 作为分布式缓存，提高数据访问速度。
-   **安全认证**: 基于 Spring Security 和 JWT (JSON Web Token) 实现无状态认证和授权。
-   **API 文档**: 引入 Springdoc OpenAPI (Swagger 3) 自动生成和管理 API 文档。
-   **实时通信**: 利用 Spring WebSocket 和 STOMP 协议实现客户端与服务端的双向实时消息通信。
-   **文件存储**: 集成阿里云 OSS SDK，实现文件的云端存储。
-   **统一响应**: 定义 `Result<T>` 类作为所有 API 接口的统一响应格式，确保前后端数据交互的一致性。
-   **结构化日志**: 引入 Logstash Logback Encoder，支持结构化日志输出，便于日志分析和监控。
-   **链路追踪**: 利用 Spring AOP 和 MDC (Mapped Diagnostic Context) 实现请求链路追踪。

## 部署环境
目前作为单体应用部署，未来可能考虑 Docker 容器化部署。