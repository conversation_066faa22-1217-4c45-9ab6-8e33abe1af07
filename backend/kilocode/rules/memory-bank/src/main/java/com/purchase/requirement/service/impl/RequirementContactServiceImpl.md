# `RequirementContactServiceImpl.java` 说明文档

## 文件概述
`RequirementContactServiceImpl.java` 是 `RequirementContactService` 接口的实现类，负责处理需求联系信息的权限控制和脱敏显示。该服务通过查询竞价记录和需求信息，判断当前用户（买家、卖家、管理员）是否有权查看完整的联系信息，并在无权限时返回脱敏后的信息。

## 核心功能

### 1. 权限检查
-   **`checkSellerPermission(Long requirementId, Long sellerId)`**:
    -   **业务逻辑**: 查询 `bidding_record` 表，判断指定卖家是否对指定需求有“已接受”（中标）且未删除的竞价记录。这是判断卖家是否有权查看完整联系信息的关键逻辑。

### 2. 获取联系信息
-   **`getContactInfo(Long requirementId, Long userId, boolean isBuyer)`**:
    -   **业务逻辑**: 重载方法，内部调用更详细的 `getContactInfo(Long requirementId, Long userId, boolean isBuyer, boolean isAdmin)` 方法，默认 `isAdmin` 为 `false`。
-   **`getContactInfo(Long requirementId, Long userId, boolean isBuyer, boolean isAdmin)`**:
    -   **业务逻辑**:
        1.  首先检查需求是否存在以及联系信息是否为空。
        2.  如果当前用户是管理员 (`isAdmin` 为 `true`)，直接返回完整的联系信息。
        3.  如果当前用户是该需求的买家 (`isBuyer` 为 `true` 且 `userId` 与 `requirement.getBuyerId()` 匹配)，直接返回完整的联系信息。
        4.  如果当前用户是卖家 (`isBuyer` 为 `false`)，则调用 `checkSellerPermission` 方法判断其是否有中标记录，如果有，则返回完整的联系信息。
        5.  以上条件均不满足时，调用 `ContactInfoUtils.maskContactInfo()` 对联系信息进行脱敏处理后返回。

## 辅助工具
-   **`ContactInfoUtils`**: 用于对联系信息进行脱敏处理的工具类。

## 注意事项
-   **权限优先级**: 管理员权限最高，其次是需求所有者（买家），然后是中标卖家。
-   **数据敏感性**: 由于处理的是用户联系信息，该服务对权限控制和数据脱敏的实现至关重要，以保护用户隐私。
-   **依赖注入**: 通过 `@Resource` 注解注入了 `BiddingRecordMapper` 和 `PurchaseRequirementMapper`，用于查询相关数据。
-   **日志记录**: 使用 `slf4j` 记录关键操作日志，便于追踪和调试。