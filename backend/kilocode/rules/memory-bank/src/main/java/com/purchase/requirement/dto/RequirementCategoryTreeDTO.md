# `RequirementCategoryTreeDTO.java` 说明文档

## 文件概述
`RequirementCategoryTreeDTO.java` 是一个数据传输对象（DTO），专门用于表示**需求分类的树形结构**。它继承自 `RequirementCategoryDTO`，并在此基础上添加了一个 `children` 列表，用于存储子分类，从而构建出完整的分类层级关系。这个 DTO 主要用于前端展示分类树，例如在选择分类时提供层级选择器。

## 核心功能
-   **继承 `RequirementCategoryDTO`**: 继承了 `RequirementCategoryDTO` 的所有字段，包括 `id`, `name`, `description`, `parentId` 等，确保每个节点都包含完整的分类信息。
-   **`children`**: 一个 `List<RequirementCategoryTreeDTO>` 类型的列表，用于存储当前分类的子分类。通过递归引用自身，实现了无限层级的树形结构。

## 使用示例

### 获取分类树响应示例
```json
[
    {
        "id": 1,
        "name": "电子产品",
        "parentId": null,
        "description": "包含手机、电脑、平板等电子设备。",
        "children": [
            {
                "id": 2,
                "name": "手机",
                "parentId": 1,
                "description": "各类智能手机。",
                "children": []
            },
            {
                "id": 3,
                "name": "电脑",
                "parentId": 1,
                "description": "台式机、笔记本电脑。",
                "children": []
            }
        ]
    },
    {
        "id": 4,
        "name": "办公用品",
        "parentId": null,
        "description": "文具、耗材等办公所需物品。",
        "children": []
    }
]
```

## 注意事项
-   **Lombok 注解**: 使用 `@Data` 自动生成 getter/setter、`equals`、`hashCode` 和 `toString` 方法。`@EqualsAndHashCode(callSuper = true)` 确保在比较对象时也考虑父类的字段。
-   **递归结构**: `children` 字段的递归定义是实现树形结构的关键。
-   **前端渲染**: 这个 DTO 的设计直接服务于前端的树形组件渲染，简化了前端处理层级数据的复杂性。