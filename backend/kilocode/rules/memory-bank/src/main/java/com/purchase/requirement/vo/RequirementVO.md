# `RequirementVO.java` 说明文档

## 文件概述
`RequirementVO.java` 是一个视图对象（VO），用于在前端展示**采购需求的详细信息**。它包含了采购需求的所有核心属性，并对部分字段进行了格式化或组合，例如将价格范围封装为 `PriceRange` 对象，将图片/视频字符串转换为列表，以及包含分类名称和竞价数量等。此外，它还定义了一个内部类 `StatisticsInfo` 用于样品需求的统计信息。

## 核心功能

### 1. 需求基本信息
-   **`id`**: 需求ID。
-   **`buyerId`**: 买家ID。
-   **`categoryId`**: 分类ID。
-   **`categoryName`**: 分类名称，方便前端直接显示。
-   **`title`**: 需求标题。
-   **`requirementType`**: 需求类型（`purchase` 或 `sample`）。
-   **`description`**: 需求描述。
-   **`specification`**: 需求规格。
-   **`price`**: 价格范围，一个 `PriceRange` 内部对象，包含 `min` 和 `max` 价格。
-   **`expectedDeliveryTime`**: 期望交付时间。
-   **`status`**: 需求状态。
-   **`createdAt`**: 创建时间。
-   **`updatedAt`**: 更新时间。
-   **`images`**: 图片URL列表。
-   **`videos`**: 视频URL列表。
-   **`pdfUrl`**: PDF文档URL。
-   **`attributesJson`**: 需求属性的 JSON 字符串。
-   **`serviceLevelCode`**: 服务级别代码。
-   **`serviceFeeRate`**: 服务费率。
-   **`contactInfo`**: 买家联系信息（可能被脱敏）。
-   **`quantity`**: 采购数量。
-   **`unit`**: 数量单位。
-   **`hsCode`**: 海关商品编码。
-   **`deliveryTerms`**: 交付条款。
-   **`biddingCount`**: 竞价数量，表示该需求收到的竞价总数。

### 2. `PriceRange` 内部类
-   **作用**: 封装需求的最低价格 (`min`) 和最高价格 (`max`)。
-   **`of(BigDecimal min, BigDecimal max)` 静态工厂方法**: 便捷创建 `PriceRange` 实例。

### 3. `StatisticsInfo` 内部类
-   **作用**: 专门用于样品需求的统计信息，例如总竞价数量、已接受的竞价数量和待处理的竞价数量。
-   **主要字段**:
    -   `totalBiddings`: 总竞价数量。
    -   `acceptedBiddings`: 已接受的竞价数量。
    -   `pendingBiddings`: 待处理的竞价数量。

## 使用示例

### 需求详情展示示例
```json
{
    "id": 123,
    "buyerId": 101,
    "categoryId": 1,
    "categoryName": "办公用品",
    "title": "采购一批办公用品",
    "requirementType": "purchase",
    "description": "需要采购A4纸、签字笔、订书机等办公用品。",
    "specification": "A4纸：80g，500张/包；签字笔：黑色，0.5mm。",
    "price": {
        "min": 100.00,
        "max": 500.00
    },
    "expectedDeliveryTime": "2024-12-31T00:00:00",
    "status": "in_progress",
    "createdAt": "2024-07-01T10:00:00",
    "updatedAt": "2024-07-01T10:00:00",
    "images": ["http://example.com/img1.jpg", "http://example.com/img2.jpg"],
    "videos": [],
    "pdfUrl": "http://example.com/doc.pdf",
    "attributesJson": "{\"color\":\"白色\",\"material\":\"塑料\"}",
    "serviceLevelCode": "basic",
    "serviceFeeRate": 0.025,
    "contactInfo": "{\"name\":\"张**\",\"phone\":\"138****5678\"}",
    "quantity": 100,
    "unit": "件",
    "hsCode": "4802560000",
    "deliveryTerms": "FOB",
    "biddingCount": 5
}
```

## 注意事项
-   **视图层职责**: VO 专注于数据展示，不包含业务逻辑或数据库操作。
-   **数据组装**: 许多字段（如 `categoryName`, `price` 对象，`images` 列表，`contactInfo` 脱敏）通常由后端服务层在从实体转换为 VO 时进行组装和填充。
-   **Lombok 注解**: 使用 `@Data` 自动生成 getter/setter、`equals`、`hashCode` 和 `toString` 方法。
-   **时间类型**: 使用 `java.time.LocalDateTime` 替代 `java.util.Date`，提供更好的日期时间处理能力。