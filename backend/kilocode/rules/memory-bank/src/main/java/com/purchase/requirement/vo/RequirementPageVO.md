# `RequirementPageVO.java` 说明文档

## 文件概述
`RequirementPageVO.java` 是一个视图对象（VO），专门用于封装**采购需求分页查询的结果**。它包含了总记录数 (`total`) 和当前页的需求列表 (`items`)。这个 VO 旨在为前端提供一个简洁、统一的分页数据结构，方便前端组件（如表格、列表）的渲染。

## 核心功能
-   **`total`**: 总记录数，表示满足查询条件的所有采购需求的总量。
-   **`items`**: 一个 `List<RequirementVO>` 类型的列表，包含当前页的采购需求详情数据。
-   **`of(Long total, List<RequirementVO> items)` 静态工厂方法**:
    -   **作用**: 提供一个便捷的方式来创建 `RequirementPageVO` 实例。
    -   **参数**: `total` (总记录数), `items` (需求列表)。
    -   **返回**: 一个新的 `RequirementPageVO` 对象。

## 使用示例

### 采购需求分页响应示例
```json
{
    "total": 150,
    "items": [
        {
            "id": 1,
            "title": "采购一批办公用品",
            "status": "in_progress",
            // ... 其他 RequirementVO 字段
        },
        {
            "id": 2,
            "title": "定制T恤样品",
            "status": "completed",
            // ... 其他 RequirementVO 字段
        }
    ]
}
```

## 注意事项
-   **视图层职责**: VO 专注于数据展示，不包含业务逻辑或数据库操作。
-   **简洁性**: 该 VO 的设计非常简洁，只包含分页结果的核心信息，避免了不必要的复杂性。
-   **Lombok 注解**: 使用 `@Data` 自动生成 getter/setter、`equals`、`hashCode` 和 `toString` 方法。
-   **泛用性**: 这种分页 VO 模式可以应用于其他模块的分页查询结果封装。