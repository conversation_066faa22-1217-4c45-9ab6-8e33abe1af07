# `PurchaseRequirementService.java` 说明文档

## 文件概述
`PurchaseRequirementService.java` 是采购需求模块的服务层接口。它定义了与采购需求相关的核心业务操作，包括需求的创建、查询、更新、删除、状态管理以及各种统计功能。该接口旨在提供一个清晰的业务逻辑抽象层，供控制器层调用，并隐藏底层数据访问的细节。

## 核心功能

### 1. 需求统计与查询
-   **`getRequirementStats()`**: 获取系统层面的采购需求统计数据。
-   **`getRequirementStatsByBuyerId(Long buyerId)`**: 根据买家ID获取其个人采购需求统计数据。
-   **`searchRequirements(PurchaseRequirementDTO.SearchRequest request)`**: 根据多维度条件搜索采购需求，并返回分页结果。
-   **`getRequirementsByBuyerId(Long buyerId, PurchaseRequirementDTO.SearchRequest request)`**: 根据买家ID和搜索条件获取其采购需求列表。
-   **`getRequirementPage(Integer page, Integer size, Long categoryId)`**: 获取采购需求的分页数据，支持按分类ID筛选。
-   **`getRequirementsByIds(List<Long> ids)`**: 批量获取指定ID的采购需求详情。
-   **`getFeaturedRequirements(Integer limit)`**: 获取热门采购需求列表。
-   **`getRequirementById(Long id)`**: 根据ID获取单个采购需求的详细信息。
-   **`getLatestRequirementsByBuyerId(Long buyerId, Integer limit)`**: 获取买家最新发布的采购需求列表。
-   **`getLatestRequirements(Integer limit)`**: 获取系统中最新发布的采购需求列表。
-   **`getStatistics()`**: 获取采购需求统计数据（更详细的统计）。

### 2. 需求操作
-   **`createRequirement(PurchaseRequirementDTO.CreateRequest request)`**: 创建新的采购需求。
-   **`updateRequirement(Long id, PurchaseRequirementDTO.CreateRequest request)`**: 更新现有采购需求。
-   **`deleteRequirement(Long id, Long buyerId)`**: 逻辑删除采购需求，并验证操作权限。
-   **`cancelRequirement(Long id, Long userId)`**: 取消一个采购需求，包含权限和状态校验。
-   **`updateRequirementStatusByAdmin(Long id, String status)`**: 管理员更新采购需求状态。
-   **`deleteRequirementByAdmin(Long id)`**: 管理员删除任何采购需求。
-   **`updateRequirementStatus(Long id, String status)`**: 更新需求状态（通用方法）。

## 使用示例

### 创建采购需求示例
```java
// 在 Controller 层调用 Service
PurchaseRequirementDTO.CreateRequest createRequest = new PurchaseRequirementDTO.CreateRequest();
createRequest.setTitle("采购一批办公用品");
createRequest.setCategoryId(1L);
createRequest.setQuantity(new BigDecimal("100"));
createRequest.setUnit("件");
createRequest.setContactInfo("{\"name\":\"张三\",\"phone\":\"13812345678\"}");
createRequest.setAttributesJson("{\"color\":\"白色\"}");
createRequest.setBuyerId(101L); // 由控制器设置
PurchaseRequirementDTO.CreateResponse response = purchaseRequirementService.createRequirement(createRequest);
```

### 搜索采购需求示例
```java
// 在 Controller 层调用 Service
PurchaseRequirementDTO.SearchRequest searchRequest = new PurchaseRequirementDTO.SearchRequest();
searchRequest.setKeyword("电脑");
searchRequest.setStatus("in_progress");
searchRequest.setPage(1);
searchRequest.setSize(10);
PurchaseRequirementDTO.SearchResponse searchResponse = purchaseRequirementService.searchRequirements(searchRequest);
```

## 注意事项
-   **业务逻辑封装**: 服务层负责封装复杂的业务逻辑，例如权限校验、状态流转、数据转换等。
-   **事务管理**: 服务层的方法通常会配置事务，确保数据的一致性。
-   **异常处理**: 服务层会捕获并处理业务异常，并向上层抛出更友好的异常信息。
-   **DTO 转换**: 服务层负责将 DTO 对象转换为实体对象进行持久化操作，并将实体对象转换为 DTO 或 VO 对象返回给控制器层。