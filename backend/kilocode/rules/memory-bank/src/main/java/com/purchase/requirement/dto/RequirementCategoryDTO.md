# `RequirementCategoryDTO.java` 说明文档

## 文件概述
`RequirementCategoryDTO.java` 是一个数据传输对象（DTO），用于在需求分类模块中封装分类信息。它定义了需求分类的各种属性，包括名称、描述、图标、排序、父分类ID、状态以及用于存储分类属性模板的 JSON 字段。该 DTO 主要用于前端与后端之间进行分类数据的传输和校验。

## 核心功能
-   **`id`**: 分类ID，唯一标识一个需求分类。
-   **`name`**: 分类名称 (中文)，通过 `@NotBlank` 和 `@Size` 进行非空和长度校验。
-   **`nameEn`**: 分类英文名称，用于国际化或多语言支持。
-   **`description`**: 分类描述，提供对分类的详细说明。
-   **`icon`**: 分类图标的 URL 或标识符。
-   **`sortOrder`**: 排序顺序，用于控制分类在列表或树形结构中的显示顺序。
-   **`parentId`**: 父分类ID，用于构建分类的树形结构。如果为 `null`，则表示为根分类。
-   **`status`**: 分类状态（0-禁用，1-启用），控制分类的可用性。
-   **`attributesJson`**: 分类属性模板的 JSON 字符串，用于定义该分类下需求的特有属性结构。
-   **`attributesJsonEn`**: 分类英文属性模板的 JSON 字符串。
-   **`createdAt`**: 创建时间。
-   **`updatedAt`**: 更新时间。

## 使用示例

### 创建/更新分类请求示例
```json
{
    "name": "电子产品",
    "nameEn": "Electronics",
    "description": "包含手机、电脑、平板等电子设备。",
    "icon": "http://example.com/icons/electronics.png",
    "sortOrder": 1,
    "parentId": null,
    "status": 1,
    "attributesJson": "{\"brand\":{\"type\":\"string\",\"required\":true},\"model\":{\"type\":\"string\",\"required\":false}}",
    "attributesJsonEn": "{\"brand\":{\"type\":\"string\",\"required\":true},\"model\":{\"type\":\"string\",\"required\":false}}"
}
```

## 注意事项
-   **数据校验**: `name` 字段强制要求非空且长度不超过100个字符，确保数据的完整性和规范性。
-   **树形结构支持**: `parentId` 字段的设计支持构建多级分类树。
-   **属性模板**: `attributesJson` 和 `attributesJsonEn` 字段提供了灵活的机制来定义不同分类下需求的特有属性，这对于支持多样化的采购需求非常重要。
-   **Lombok 注解**: 使用 `@Data` 自动生成 getter/setter、`equals`、`hashCode` 和 `toString` 方法，简化了代码。