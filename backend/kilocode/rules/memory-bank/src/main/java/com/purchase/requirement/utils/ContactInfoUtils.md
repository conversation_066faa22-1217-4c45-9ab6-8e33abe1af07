# `ContactInfoUtils.java` 说明文档

## 文件概述
`ContactInfoUtils.java` 是一个工具类，专门用于处理**联系信息的脱敏和格式校验**。它提供了多种脱敏策略，能够对手机号、邮箱、公司名称、联系人姓名以及地址信息（包括国家、省份、城市和详细地址）进行智能脱敏，以保护用户隐私。同时，也提供了基本的联系信息格式校验功能。

## 核心功能

### 1. 联系信息脱敏
-   **`maskContactInfo(String contactInfo)`**:
    -   **作用**: 对非 JSON 格式的联系信息字符串进行脱敏。
    -   **脱敏策略**:
        -   **手机号**: 保留前3位和后4位，中间用 `****` 替换 (例如 `138****5678`)。
        -   **邮箱**: 保留用户名前缀和完整域名，用户名前缀根据长度进行脱敏 (例如 `exa****@email.com`)。
        -   **其他格式**: 默认保留前3个和后3个字符，中间用 `****` 替换。
-   **`maskJsonContactInfo(String jsonContactInfo)`**:
    -   **作用**: 对 JSON 格式的联系信息字符串进行脱敏。
    -   **支持字段**: `companyName`, `contactPerson`, `phone`, `phoneNumber`, `email`, `address`, `country`, `province`, `city`, `detailedAddress`。
    -   **脱敏策略**: 针对不同字段类型采用不同的智能脱敏规则，例如：
        -   **电话号码**: 智能识别国际号码、中国手机号、固话，并采用不同脱敏策略。
        -   **公司名称**: 保留首字符和后缀，中间用星号替换。
        -   **联系人姓名**: 中文姓名保留姓氏，英文姓名保留首字母。
        -   **邮箱**: 保留用户名前缀和完整域名，用户名前缀根据长度进行脱敏。
        -   **地址信息**: 根据地理位置层级（国家、省份、城市）和详细地址采用不同脱敏策略，保留部分可识别信息。

### 2. 联系信息校验
-   **`isValidContactInfo(String contactInfo)`**:
    -   **作用**: 验证联系信息字符串的基本格式是否合法。
    -   **校验规则**: 支持手机号、邮箱和“联系人+电话”格式的校验，并要求至少5个字符。

## 辅助方法 (私有)
-   `maskPhoneNumberField(ObjectNode contactInfo, String fieldName)`: 脱敏电话号码字段。
-   `maskCompanyNameField(ObjectNode contactInfo)`: 脱敏公司名称字段。
-   `maskContactPersonField(ObjectNode contactInfo)`: 脱敏联系人姓名字段。
-   `maskAddressField(ObjectNode contactInfo)`: 脱敏地址字段。
-   `maskEmailField(ObjectNode contactInfo)`: 脱敏邮箱字段。
-   `maskLocationField(ObjectNode contactInfo, String fieldName)`: 脱敏地理位置字段（国家、省份、城市）。
-   `maskDetailedAddressField(ObjectNode contactInfo)`: 脱敏详细地址字段。

## 使用示例

### 脱敏 JSON 格式联系信息示例
```java
String originalContactInfo = "{\"companyName\":\"示例科技有限公司\",\"contactPerson\":\"张三\",\"phone\":\"13812345678\",\"email\":\"<EMAIL>\",\"city\":\"深圳市\"}";
String maskedContactInfo = ContactInfoUtils.maskJsonContactInfo(originalContactInfo);
// maskedContactInfo 可能会是类似 "{\"companyName\":\"示***司\",\"contactPerson\":\"张**\",\"phone\":\"138***5678\",\"email\":\"zha***@example.com\",\"city\":\"深**\"}"
```

### 校验联系信息示例
```java
boolean isValidPhone = ContactInfoUtils.isValidContactInfo("13812345678"); // true
boolean isValidEmail = ContactInfoUtils.isValidContactInfo("<EMAIL>"); // true
boolean isValidInvalid = ContactInfoUtils.isValidContactInfo("abc"); // false
```

## 注意事项
-   **隐私保护**: 该工具类的核心目标是保护用户敏感信息，通过多种脱敏策略实现。
-   **JSON 处理**: 使用 `ObjectMapper` 处理 JSON 字符串的解析和修改。
-   **正则表达式**: 内部使用了正则表达式进行手机号和邮箱的匹配和脱敏。
-   **日志记录**: 使用 `slf4j` 记录脱敏过程中可能出现的异常。