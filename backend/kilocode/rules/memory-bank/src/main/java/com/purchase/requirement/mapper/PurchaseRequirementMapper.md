# `PurchaseRequirementMapper.java` 说明文档

## 文件概述
`PurchaseRequirementMapper.java` 是采购需求模块的 MyBatis-Plus Mapper 接口。它继承了 `BaseMapper<PurchaseRequirement>`，提供了对 `purchase_requirement` 表进行基本 CRUD (创建、读取、更新、删除) 操作的能力。此外，该接口还定义了多个自定义的数据库查询方法，用于满足复杂的业务需求，例如带分类信息的查询、按分类ID统计数量、检查卖家竞价记录以及获取最新需求列表等。

## 核心功能

### 1. 继承 `BaseMapper`
-   自动拥有 `insert`, `deleteById`, `updateById`, `selectById`, `selectList` 等基本 CRUD 方法。

### 2. 自定义查询方法
-   **`selectRequirementWithCategory(@Param("keyword") String keyword, @Param("categoryId") Long categoryId, @Param("status") String status, @Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice)`**:
    -   **作用**: 查询采购需求列表，并可能包含关联的分类信息。
    -   **参数**: 支持按关键词、分类ID、状态、最低价格和最高价格进行筛选。
-   **`countByCategoryId(@Param("categoryId") Long categoryId)`**:
    -   **作用**: 根据分类ID统计该分类下的采购需求数量。
-   **`countSellerBiddingRecords(@Param("requirementId") Long requirementId, @Param("sellerId") Long sellerId)`**:
    -   **作用**: 检查特定卖家是否对某个采购需求发起过竞价。
    -   **返回**: 返回竞价记录的数量。
-   **`selectLatestRequirementsByBuyerId(@Param("buyerId") Long buyerId, @Param("limit") Integer limit)`**:
    -   **作用**: 获取指定买家最新发布的采购需求列表。
    -   **参数**: `buyerId` 为买家ID，`limit` 限制返回的需求数量。
-   **`selectLatestRequirements(@Param("limit") Integer limit)`**:
    -   **作用**: 获取系统中最新发布的采购需求列表。
    -   **参数**: `limit` 限制返回的需求数量。

## 使用示例

### 查询带分类信息的需求列表示例
```java
// 在 Service 层调用 Mapper
List<PurchaseRequirement> requirements = purchaseRequirementMapper.selectRequirementWithCategory(
    "办公用品", // keyword
    1L,         // categoryId
    "in_progress", // status
    new BigDecimal("100"), // minPrice
    new BigDecimal("1000") // maxPrice
);
```

### 统计分类下需求数量示例
```java
// 在 Service 层调用 Mapper
int count = purchaseRequirementMapper.countByCategoryId(1L);
```

## 注意事项
-   **`@Mapper` 注解**: 标记该接口为一个 MyBatis Mapper，Spring 会自动扫描并注册。
-   **`@Param` 注解**: 当方法有多个参数时，建议使用 `@Param` 注解为参数指定名称，以便在 XML Mapper 文件中引用。
-   **XML Mapper 文件**: 实际的 SQL 语句通常定义在与此接口同名的 XML 文件中（例如 `PurchaseRequirementMapper.xml`），通过命名空间和方法ID进行关联。
-   **MyBatis-Plus 扩展**: 继承 `BaseMapper` 极大地简化了常见的数据库操作，减少了样板代码。