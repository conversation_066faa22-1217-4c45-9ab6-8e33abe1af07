# `ServiceLevelService.java` 说明文档

## 文件概述
`ServiceLevelService.java` 是服务级别模块的服务层接口。它定义了与服务级别相关的核心业务操作，包括服务级别的查询（所有、启用状态、按ID、按代码）、创建、更新和删除。该接口旨在提供一个清晰的业务逻辑抽象层，供控制器层调用，并隐藏底层数据访问的细节。

## 核心功能

### 1. 服务级别查询
-   **`getAllActiveLevels()`**: 获取所有**启用状态**的服务级别列表。
-   **`getAllLevels()`**: 获取所有服务级别列表，包括启用和禁用的。
-   **`getLevelById(Integer id)`**: 根据服务级别ID获取单个服务级别的详细信息。
-   **`getLevelByCode(String levelCode)`**: 根据服务级别代码获取单个服务级别的详细信息。

### 2. 服务级别操作
-   **`createServiceLevel(ServiceLevelDTO serviceLevelDTO)`**: 创建新的服务级别。
    -   **参数**: `serviceLevelDTO` 包含服务级别信息。
    -   **返回**: 创建后的 `ServiceLevelDTO` 对象。
-   **`updateServiceLevel(Integer id, ServiceLevelDTO serviceLevelDTO)`**: 更新现有服务级别。
    -   **参数**: `id` 为服务级别ID，`serviceLevelDTO` 包含更新后的服务级别信息。
    -   **返回**: 更新后的 `ServiceLevelDTO` 对象。
-   **`deleteServiceLevel(Integer id)`**: 根据服务级别ID删除服务级别。
    -   **返回**: `true` 表示删除成功，`false` 表示失败。

## 使用示例

### 创建服务级别示例
```java
// 在 Controller 层调用 Service
ServiceLevelDTO newLevel = new ServiceLevelDTO();
newLevel.setLevelCode("VIP");
newLevel.setName("VIP服务");
newLevel.setDescription("提供最高级别的专属服务。");
newLevel.setFeeRate(0.15);
newLevel.setStatus(1); // 启用
ServiceLevelDTO createdLevel = serviceLevelService.createServiceLevel(newLevel);
System.out.println("新服务级别创建成功，ID: " + createdLevel.getId());
```

### 获取所有启用服务级别示例
```java
// 在 Controller 层调用 Service
List<ServiceLevelDTO> activeLevels = serviceLevelService.getAllActiveLevels();
// 进一步处理 activeLevels 数据
```

## 注意事项
-   **业务逻辑封装**: 服务层负责处理服务级别相关的业务规则，例如在创建或更新时进行数据校验。
-   **DTO 与 Entity 转换**: 服务层负责将 DTO 对象转换为实体对象进行持久化操作，并将实体对象转换为 DTO 对象返回给控制器层。
-   **事务管理**: 服务层的方法通常会配置事务，确保数据的一致性。
-   **权限控制**: 尽管接口本身不包含 `@PreAuthorize`，但其实现类或上层调用者会负责权限校验（如 `ServiceLevelController`）。