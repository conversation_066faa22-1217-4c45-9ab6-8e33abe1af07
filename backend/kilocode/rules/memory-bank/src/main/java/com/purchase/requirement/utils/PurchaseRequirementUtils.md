# `PurchaseRequirementUtils.java` 说明文档

## 文件概述
`PurchaseRequirementUtils.java` 是一个辅助工具类，主要用于提供与采购需求相关的通用实用功能。目前，它包含一个用于检查卖家是否对特定需求发起过竞价的方法。这个工具类旨在封装一些通用的、可复用的业务逻辑，以保持服务层代码的简洁性。

## 核心功能
-   **`isSellerWithBidding(Long requirementId, Long sellerId)`**:
    -   **作用**: 检查指定的卖家是否对指定的采购需求发起过竞价。
    -   **业务逻辑**: 通过调用 `PurchaseRequirementMapper` 的 `countSellerBiddingRecords` 方法来判断。如果存在竞价记录，则返回 `true`。
    -   **参数**: `requirementId` (采购需求ID), `sellerId` (卖家ID)。
    -   **返回**: `true` 表示卖家已对该需求发起过竞价，`false` 表示未发起。

## 使用示例

### 检查卖家是否已竞价示例
```java
// 在 Service 层或其他需要判断的逻辑中调用
Long currentRequirementId = 123L;
Long currentSellerId = 456L;

if (purchaseRequirementUtils.isSellerWithBidding(currentRequirementId, currentSellerId)) {
    System.out.println("该卖家已对当前需求发起过竞价。");
} else {
    System.out.println("该卖家尚未对当前需求发起竞价。");
}
```

## 注意事项
-   **`@Component` 注解**: 标记该类为一个 Spring 组件，使其可以被 Spring 容器管理和注入。
-   **依赖注入**: 通过 `@Resource` 注解注入 `PurchaseRequirementMapper`，以便进行数据库查询。
-   **业务逻辑封装**: 将特定的业务判断逻辑封装到工具类中，提高了代码的复用性和可维护性。
-   **简洁性**: 工具类应保持简洁，只包含单一职责或相关联的辅助功能。