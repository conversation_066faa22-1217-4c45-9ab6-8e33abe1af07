# `ServiceLevelServiceImpl.java` 说明文档

## 文件概述
`ServiceLevelServiceImpl.java` 是 `ServiceLevelService` 接口的实现类，负责处理服务级别模块的业务逻辑。它实现了服务级别的查询（所有、启用状态、按ID、按代码）、创建、更新和删除功能。该实现类还包含了实体与 DTO 之间的转换逻辑，特别是对服务特性 JSON 字符串与列表之间的转换。

## 核心功能

### 1. 服务级别查询
-   **`getAllActiveLevels()`**: 获取所有启用状态的服务级别列表。
    -   **实现逻辑**: 优先尝试使用 Mapper 的 `selectByStatus` 方法，如果失败则回退到 MyBatis-Plus 的 `LambdaQueryWrapper` 进行查询。
-   **`getAllLevels()`**: 获取所有服务级别列表（包括禁用状态的）。
-   **`getLevelById(Integer id)`**: 根据服务级别ID获取单个服务级别的详细信息。
    -   **异常处理**: 如果服务级别不存在，抛出 `ResourceNotFoundException`。
-   **`getLevelByCode(String levelCode)`**: 根据服务级别代码获取单个服务级别的详细信息。
    -   **异常处理**: 如果服务级别不存在，抛出 `ResourceNotFoundException`。

### 2. 服务级别操作
-   **`createServiceLevel(ServiceLevelDTO serviceLevelDTO)`**: 创建新的服务级别。
    -   **业务逻辑**: 将 DTO 转换为实体，将服务特性列表转换为 JSON 字符串，然后插入数据库。
    -   **异常处理**: 如果 JSON 转换失败或插入数据库失败，抛出 `BusinessException`。
-   **`updateServiceLevel(Integer id, ServiceLevelDTO serviceLevelDTO)`**: 更新现有服务级别。
    -   **业务逻辑**: 验证服务级别是否存在，将 DTO 属性复制到现有实体，将服务特性列表转换为 JSON 字符串，然后更新数据库。
    -   **异常处理**: 如果服务级别不存在，抛出 `ResourceNotFoundException`；如果 JSON 转换失败或更新数据库失败，抛出 `BusinessException`。
-   **`deleteServiceLevel(Integer id)`**: 根据服务级别ID删除服务级别。
    -   **业务逻辑**: 调用 Mapper 进行删除操作。

## 辅助方法
-   **`convertToDTO(ServiceLevel serviceLevel)`**: 将 `ServiceLevel` 实体转换为 `ServiceLevelDTO` 对象。
    -   **数据转换**: 特别处理 `featuresJson` 和 `featuresJsonEn` 字段，将其从 JSON 字符串转换为 `List<String>`。如果转换失败，则设置为空列表。

## 注意事项
-   **依赖注入**: 通过 `@Autowired` 注入 `ServiceLevelMapper` 和 `ObjectMapper`。
-   **异常处理**: 对资源未找到 (`ResourceNotFoundException`) 和业务逻辑错误 (`BusinessException`) 进行了明确的异常处理。
-   **JSON 转换**: 使用 `ObjectMapper` 进行 `List<String>` 和 JSON 字符串之间的转换，确保数据在持久化和传输过程中的正确性。
-   **日志记录**: 使用 `Slf4j` 注解和 `log.warn()`, `log.error()` 记录警告和错误信息，便于调试和问题排查。
-   **MyBatis-Plus 兼容性**: 在 `getAllActiveLevels` 方法中展示了对 MyBatis-Plus 查询方式的兼容性处理。