# `SampleRequirementServiceImpl.java` 说明文档

## 文件概述
`SampleRequirementServiceImpl.java` 是 `SampleRequirementService` 接口的实现类，负责处理样品需求模块的所有业务逻辑。它管理样品需求的整个生命周期，包括创建、查询、修改、结束竞价（并生成样品订单）、关闭和重新开放。该服务还包含了权限验证和统计信息获取的功能，并与竞价、订单和用户模块进行交互。

## 核心功能

### 1. 样品需求生命周期管理
-   **`createSampleRequirement(PurchaseRequirementDTO.CreateRequest request, Long buyerId)`**: 创建新的样品需求。
    -   **业务逻辑**: 验证买家信息，构建样品需求实体，并持久化到数据库。
-   **`updateSampleRequirement(Long requirementId, PurchaseRequirementDTO.CreateRequest request, Long buyerId)`**: 修改已存在的样品需求。
    -   **业务逻辑**: 验证权限、需求类型和状态（仅 `in_progress` 状态允许修改），更新需求信息。
-   **`finishSampleBidding(Long requirementId, Long buyerId)`**: 结束样品竞价，并触发样品订单的生成。
    -   **业务逻辑**: 验证权限和需求状态，检查是否有已接受的样品竞价，检查是否已生成样品订单，更新需求状态为 `completed`，并调用 `SampleOrderService` 创建样品订单。
-   **`closeSampleRequirement(Long requirementId, Long buyerId, String reason)`**: 关闭样品需求，不生成订单。
    -   **业务逻辑**: 验证权限和需求状态，更新需求状态为 `completed`，并可选地将关闭原因添加到需求描述中。
-   **`reopenSampleRequirement(Long requirementId, Long buyerId)`**: 重新开放已关闭的样品需求。
    -   **业务逻辑**: 验证权限和需求状态（仅 `completed` 状态且未生成订单的需求可重新开放），更新需求状态为 `in_progress`。

### 2. 样品需求查询
-   **`getSampleRequirements(Integer page, Integer size, Long categoryId, String keyword)`**: 获取公开的样品需求列表。
    -   **查询逻辑**: 只显示状态为 `in_progress` 且未删除的样品需求，支持分页、分类和关键词搜索。
-   **`getBuyerSampleRequirements(Long buyerId, Integer page, Integer size, String status)`**: 买家获取自己发布的样品需求列表。
    -   **查询逻辑**: 过滤出指定买家的样品需求，支持分页和状态筛选。
-   **`getSampleRequirementDetail(Long requirementId)`**: 获取单个样品需求的详细信息。
    -   **业务逻辑**: 验证需求是否存在且为样品类型。
-   **`getSampleRequirementStatistics(Long requirementId)`**: 获取样品需求的统计信息。
    -   **统计内容**: 统计总竞价数量、已接受的样品竞价数量和待处理的样品竞价数量。

### 3. 权限与状态检查
-   **`validateSampleRequirement(Long requirementId, Long buyerId)`**: 验证指定的需求是否为样品类型且属于指定买家。
-   **`canFinishBidding(Long requirementId)`**: 检查样品需求是否满足结束竞价的条件（即是否有已接受的样品竞价）。

## 辅助方法
-   **`buildSampleRequirement(PurchaseRequirementDTO.CreateRequest request, Long buyerId)`**: 根据请求和买家ID构建 `PurchaseRequirement` 实体。
-   **`updateRequirementFromRequest(PurchaseRequirement requirement, PurchaseRequirementDTO.CreateRequest request)`**: 从请求对象更新 `PurchaseRequirement` 实体。
-   **`getAcceptedSampleBiddingIds(Long requirementId)`**: 获取指定需求下所有已接受的样品竞价ID列表。
-   **`convertToRequirementVO(PurchaseRequirement requirement)`**: 将 `PurchaseRequirement` 实体转换为 `RequirementVO` 对象，并处理时间格式、价格范围、图片/视频列表和联系信息。

## 注意事项
-   **事务管理**: 关键的写操作（如 `createSampleRequirement`, `updateSampleRequirement`, `finishSampleBidding`, `closeSampleRequirement`, `reopenSampleRequirement`）都通过 `@Transactional` 注解进行事务管理。
-   **业务异常**: 使用 `BusinessException` 抛出业务相关的异常信息。
-   **日志记录**: 使用 `Slf4j` 注解和 `log.info()` 记录关键业务操作和异常信息。
-   **模块间协作**: 通过 `@Resource` 注入 `PurchaseRequirementMapper`, `BiddingRecordMapper`, `UserMapper`, `SampleBiddingService`, `SampleOrderService` 等，实现跨模块的业务协作。
-   **状态流转**: 严格控制样品需求的状态流转，确保业务逻辑的正确性。