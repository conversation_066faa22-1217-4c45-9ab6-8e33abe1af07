# `RequirementCategoryController.java` 说明文档

## 文件概述
`RequirementCategoryController.java` 是 `com.purchase.requirement` 包中负责**需求分类管理**的控制器。它提供了对采购需求分类的增、删、改、查以及获取分类树结构等 RESTful API 接口。

## 核心功能

### 1. 分类查询
-   **`listCategories()`**: 获取所有需求分类的列表。
-   **`listEnabledCategories()`**: 获取所有启用状态的需求分类列表。
-   **`getCategoryTree()`**: 获取所有需求分类的树形结构。
-   **`getEnabledCategoryTree()`**: 获取所有启用状态的需求分类的树形结构。
-   **`getCategoryById(@PathVariable Long id)`**: 根据分类ID获取单个分类的详细信息。

### 2. 分类操作
-   **`addCategory(@Valid @RequestBody RequirementCategoryDTO categoryDTO)`**: 添加新的需求分类。
    -   **参数**: `categoryDTO` 包含分类名称、父分类ID等信息，并进行数据校验。
-   **`updateCategory(@PathVariable Long id, @Valid @RequestBody RequirementCategoryDTO categoryDTO)`**: 更新现有需求分类。
    -   **参数**: `id` 为分类ID，`categoryDTO` 包含更新后的分类信息，并进行数据校验。
-   **`deleteCategory(@PathVariable Long id)`**: 根据分类ID删除需求分类。
-   **`updateCategoryStatus(@PathVariable Long id, @RequestParam Integer status)`**: 更新需求分类的启用/禁用状态。

## 使用示例

### 获取启用状态的分类树示例 (GET /api/v1/requirement-categories/tree/enabled)
```http
GET /api/v1/requirement-categories/tree/enabled
```

### 响应示例
```json
[
    {
        "id": 1,
        "name": "电子产品",
        "parentId": null,
        "children": [
            {
                "id": 2,
                "name": "手机",
                "parentId": 1,
                "children": []
            },
            {
                "id": 3,
                "name": "电脑",
                "parentId": 1,
                "children": []
            }
        ]
    },
    {
        "id": 4,
        "name": "办公用品",
        "parentId": null,
        "children": []
    }
]
```

## 注意事项
-   **数据校验**: `addCategory` 和 `updateCategory` 方法使用了 `@Valid` 注解，确保请求体中的数据符合预期的格式和约束。
-   **树形结构**: 提供了获取分类树的功能，方便前端展示层级结构。
-   **状态管理**: 支持对分类进行启用/禁用状态的更新，便于灵活管理分类的可见性。