# `RequirementCategoryVO.java` 说明文档

## 文件概述
`RequirementCategoryVO.java` 是一个视图对象（VO），用于在前端展示需求分类信息。它包含了分类的基本属性，如ID、名称、描述、图标、排序顺序、父分类ID，并额外增加了 `parentName`（父分类名称）和 `statusText`（状态文本）字段，以及 `children` 列表以支持树形结构展示。这个 VO 旨在为前端提供一个扁平化但包含层级信息的分类数据结构。

## 核心功能

### 1. 基本分类信息
-   **`id`**: 分类ID。
-   **`name`**: 分类名称 (中文)。
-   **`nameEn`**: 分类英文名称。
-   **`description`**: 分类描述。
-   **`icon`**: 分类图标。
-   **`sortOrder`**: 排序顺序。
-   **`parentId`**: 父分类ID。
-   **`attributesJson`**: 分类属性模板的 JSON 字符串。
-   **`attributesJsonEn`**: 分类英文属性模板的 JSON 字符串。
-   **`createdAt`**: 创建时间。
-   **`updatedAt`**: 更新时间。

### 2. 扩展展示字段
-   **`parentName`**: 父分类的名称。这个字段通常在后端服务层进行填充，方便前端直接显示父子关系。
-   **`status`**: 状态（0-禁用，1-启用）。
-   **`statusText`**: 状态的文本描述（例如“启用”或“禁用”），方便前端直接显示可读的状态。

### 3. 树形结构支持
-   **`children`**: 一个 `List<RequirementCategoryVO>` 类型的列表，用于存储当前分类的子分类。这使得前端可以递归地渲染分类树。

## 使用示例

### 分类列表展示示例
```json
[
    {
        "id": 1,
        "name": "电子产品",
        "nameEn": "Electronics",
        "description": "包含手机、电脑、平板等电子设备。",
        "icon": "http://example.com/icons/electronics.png",
        "sortOrder": 1,
        "parentId": null,
        "parentName": null,
        "status": 1,
        "statusText": "启用",
        "attributesJson": "{\"brand\":\"string\"}",
        "attributesJsonEn": "{\"brand\":\"string\"}",
        "createdAt": "2023-01-01T10:00:00",
        "updatedAt": "2023-01-01T10:00:00",
        "children": [
            {
                "id": 2,
                "name": "手机",
                "nameEn": "Mobile Phones",
                "parentId": 1,
                "parentName": "电子产品",
                "status": 1,
                "statusText": "启用",
                "children": []
            }
        ]
    }
]
```

## 注意事项
-   **视图层职责**: VO 专注于数据展示，不包含业务逻辑或数据库操作。
-   **数据组装**: `parentName` 和 `statusText` 等字段通常由后端服务层在从实体转换为 VO 时进行组装和填充。
-   **Lombok 注解**: 使用 `@Data` 自动生成 getter/setter、`equals`、`hashCode` 和 `toString` 方法。
-   **序列化**: 实现了 `Serializable` 接口，支持对象的序列化。