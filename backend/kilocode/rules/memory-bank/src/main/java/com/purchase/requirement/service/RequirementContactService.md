# `RequirementContactService.java` 说明文档

## 文件概述
`RequirementContactService.java` 是需求模块中专门处理**需求联系信息**的服务接口。它定义了管理和显示需求联系信息的业务逻辑，特别是涉及到权限控制和信息脱敏。该服务确保只有授权用户（如买家、中标卖家或管理员）才能查看完整的联系信息，而其他用户可能只能看到脱敏后的信息。

## 核心功能

### 1. 权限检查
-   **`checkSellerPermission(Long requirementId, Long sellerId)`**:
    -   **作用**: 检查特定卖家是否有权限查看指定采购需求的完整联系信息。
    -   **判断逻辑**: 通常通过查询卖家是否有对应需求的中标记录来判断其权限。
    -   **参数**: `requirementId` (需求ID), `sellerId` (卖家ID)。
    -   **返回**: `true` 表示有权限，`false` 表示无权限。

### 2. 获取联系信息
-   **`getContactInfo(Long requirementId, Long userId, boolean isBuyer)`**:
    -   **作用**: 根据用户权限获取采购需求的联系信息。
    -   **功能**: 如果用户有权限，返回完整的联系信息；否则返回脱敏信息。
    -   **参数**: `requirementId` (需求ID), `userId` (当前用户ID), `isBuyer` (是否是买家)。
    -   **返回**: 联系信息字符串（可能已脱敏）。
-   **`getContactInfo(Long requirementId, Long userId, boolean isBuyer, boolean isAdmin)`**:
    -   **作用**: 扩展的获取联系信息方法，支持管理员角色。
    -   **功能**: 在 `getContactInfo(Long requirementId, Long userId, boolean isBuyer)` 的基础上，增加了对管理员角色的判断。管理员通常拥有查看所有完整联系信息的权限。
    -   **参数**: `requirementId` (需求ID), `userId` (当前用户ID), `isBuyer` (是否是买家), `isAdmin` (是否是管理员)。
    -   **返回**: 联系信息字符串（可能已脱敏）。

## 使用示例

### 在控制器中调用获取联系信息示例
```java
// 假设在 PurchaseRequirementController 中
Long userId = SecurityContextUtil.getCurrentUserId();
boolean isBuyer = SecurityContextUtil.hasRole("buyer");
boolean isAdmin = SecurityContextUtil.hasRole("admin");

String contactInfo = requirementContactService.getContactInfo(requirementId, userId, isBuyer, isAdmin);

if (contactInfo == null) {
    return Result.error("联系信息不存在或无权查看");
}
return Result.success(contactInfo);
```

## 注意事项
-   **权限敏感性**: 该服务处理的是敏感的联系信息，因此权限控制是其核心。
-   **脱敏逻辑**: 具体的脱敏逻辑（例如，隐藏部分手机号、邮箱）会在实现类中完成。
-   **业务规则**: `checkSellerPermission` 方法的实现会依赖于具体的业务规则，例如只有中标的卖家才能查看联系方式。