# `PurchaseRequirementServiceImpl.java` 说明文档

## 文件概述
`PurchaseRequirementServiceImpl.java` 是 `PurchaseRequirementService` 接口的实现类，负责处理采购需求模块的所有核心业务逻辑。它协调 Mapper 层进行数据持久化操作，并集成其他服务（如分类服务、服务级别服务、通知服务、用户服务和竞价记录服务）来完成复杂的业务流程。该实现类包含了数据校验、状态管理、权限判断、数据转换和异步通知等功能。

## 核心功能

### 1. 需求统计
-   **`getRequirementStats()`**: 获取系统层面的采购需求统计数据（总数、进行中、已完成）。
-   **`getRequirementStatsByBuyerId(Long buyerId)`**: 获取指定买家的采购需求统计数据。
-   **`getStatistics()`**: 获取更详细的采购需求统计数据，包括今日新增、高价值、有附件和活跃需求数。

### 2. 需求创建与更新
-   **`createRequirement(PurchaseRequirementDTO.CreateRequest request)`**: 创建新的采购需求。
    -   **业务逻辑**: 验证必填字段、需求类型、属性JSON格式和内容、联系信息、服务级别。
    -   **数据处理**: 将图片/视频列表转换为逗号分隔字符串，设置默认需求类型和状态，根据服务级别设置默认服务费率。
    -   **通知**: 异步发送需求发布通知给所有卖家和管理员。
-   **`updateRequirement(Long id, PurchaseRequirementDTO.CreateRequest request)`**: 更新现有采购需求。
    -   **业务逻辑**: 验证需求是否存在、买家权限、属性JSON格式和内容、数量有效性、需求类型。
    -   **数据处理**: 更新图片/视频列表，更新属性JSON。

### 3. 需求查询与搜索
-   **`searchRequirements(PurchaseRequirementDTO.SearchRequest request)`**: 根据多维度条件搜索采购需求，并返回分页结果。
    -   **查询构建**: 动态构建查询条件，支持关键词、分类、价格/数量范围、状态、服务级别、排序等。
    -   **数据转换**: 将实体转换为 `SearchResponse` DTO。
-   **`getRequirementsByBuyerId(Long buyerId, PurchaseRequirementDTO.SearchRequest request)`**: 根据买家ID获取其采购需求列表。
    -   **权限处理**: 对于买家本人查询，显示完整联系信息。
-   **`getRequirementPage(Integer page, Integer size, Long categoryId)`**: 获取采购需求的分页数据（通用方法）。
    -   **权限处理**: 根据用户角色和权限对联系信息进行脱敏处理。
-   **`getRequirementsByIds(List<Long> ids)`**: 批量获取指定ID的采购需求详情。
-   **`getFeaturedRequirements(Integer limit)`**: 获取特色/推荐采购需求列表。
-   **`getRequirementById(Long id)`**: 根据ID获取单个需求详情。
    -   **权限处理**: 根据当前用户角色（买家、卖家、管理员）和是否已投标，决定联系信息是否脱敏，并控制服务级别信息的显示。
-   **`getLatestRequirementsByBuyerId(Long buyerId, Integer limit)`**: 获取指定买家的最新采购需求列表。
-   **`getLatestRequirements(Integer limit)`**: 获取系统最新采购需求列表（公开接口）。

### 4. 需求状态管理与删除
-   **`deleteRequirement(Long id, Long buyerId)`**: 买家逻辑删除采购需求，并验证权限。
-   **`cancelRequirement(Long id, Long userId)`**: 取消一个采购需求。
    -   **业务逻辑**: 验证权限和需求状态（仅进行中可取消）。
-   **`updateRequirementStatusByAdmin(Long id, String status)`**: 管理员更新采购需求状态。
-   **`deleteRequirementByAdmin(Long id)`**: 管理员逻辑删除任何采购需求。
-   **`updateRequirementStatus(Long id, String status)`**: 更新需求状态（通用方法）。

## 辅助方法
-   **`getCategoryAttributesTemplate(Long categoryId)`**: 获取分类的属性模板JSON字符串。
-   **`validateRequirementAttributes(String attributesJson, Long categoryId)`**: 验证需求属性是否符合分类模板要求。
-   **`isValidJson(String jsonStr)`**: 验证JSON格式是否合法。
-   **`getDefaultFeeRateByServiceLevel(String serviceLevelCode)`**: 根据服务级别获取默认服务费率（优先从数据库获取，否则使用硬编码）。
-   **`buildSearchQueryWrapper(PurchaseRequirementDTO.SearchRequest request)`**: 构建搜索查询条件包装器。
-   **`convertToCreateResponse(PurchaseRequirement requirement)`**: 将实体转换为创建响应DTO。
-   **`convertToSearchResponse(IPage<PurchaseRequirement> pageResult)` / `convertToSearchResponse(IPage<PurchaseRequirement> pageResult, Long userId, boolean isBuyer)`**: 将分页结果转换为搜索响应DTO。
-   **`convertToSearchItem(PurchaseRequirement requirement, Long userId, boolean isBuyer)`**: 将实体转换为搜索结果项DTO，并处理联系信息脱敏和竞价数量。
-   **`convertStringToList(String str)`**: 将逗号分隔的字符串转换为列表。
-   **`convertToLocalDateTime(Date date)`**: 将 `java.util.Date` 转换为 `java.time.LocalDateTime`。
-   **`sendRequirementPublishedNotifications(PurchaseRequirement requirement)`**: 异步发送采购需求发布通知。

## 注意事项
-   **事务管理**: 关键的写操作（如 `createRequirement`, `deleteRequirement`, `cancelRequirement` 等）都通过 `@Transactional` 注解进行事务管理，确保数据一致性。
-   **权限控制**: 在多个方法中通过 `SecurityContextUtil` 和业务逻辑进行用户权限（买家、卖家、管理员）的判断和校验。
-   **数据转换**: 广泛使用 `BeanUtils.copyProperties` 进行 DTO 与实体之间的属性复制，并手动处理列表和JSON字符串的转换。
-   **日志记录**: 使用 `slf4j` 记录关键业务操作和异常信息，便于追踪和调试。
-   **异步通知**: `sendRequirementPublishedNotifications` 方法通过 `@Async` 注解实现异步发送通知，避免阻塞主业务流程。
-   **依赖注入**: 通过 `@Resource` 注解注入了多个 Mapper 和 Service 依赖，实现了模块间的协作。