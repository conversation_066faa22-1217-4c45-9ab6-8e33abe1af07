# `com.purchase.requirement` 包说明

## 整体介绍与主要功能
`com.purchase.requirement` 包是采购系统中负责**采购需求管理**的核心模块。它涵盖了采购需求的发布、查询、统计、分类管理以及服务等级定义等一系列功能。本模块旨在为采购商提供一个便捷的平台，发布其采购意向，并为后续的竞价和订单流程奠定基础。

## 主要功能
-   **采购需求管理**: 实现采购需求的创建、查询、更新、删除等生命周期管理。
-   **需求分类管理**: 对采购需求进行分类，便于组织和检索。
-   **服务等级定义**: 定义不同采购需求的服务等级或优先级。
-   **需求统计**: 提供采购需求的统计分析功能。
-   **样品需求管理**: 处理与样品相关的采购需求。

## 安装方法
本模块是 `purchase-system` 单体应用的一部分，无需单独安装。它会随整个 Spring Boot 应用的启动而自动加载和运行。确保您的开发环境已配置好 Java 17、Maven 和 MySQL 数据库，并已正确配置数据库连接信息。

## 快速上手示例
以下是一个通过 API 发布采购需求的简化示例（假设您已启动后端服务）：

**请求示例 (POST /api/v1/requirements)**

```json
{
    "title": "采购一批办公用品",
    "description": "需要采购A4纸、签字笔、订书机等办公用品，数量和具体型号待定。",
    "categoryId": 1,
    "serviceLevelId": 1,
    "expectedCompletionDate": "2024-12-31",
    "contactInfo": {
        "name": "张三",
        "phone": "13812345678",
        "email": "<EMAIL>"
    }
}
```

**响应示例 (HTTP 200 OK)**

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 123,
        "title": "采购一批办公用品",
        "status": "PENDING_REVIEW",
        "buyerId": 101,
        "publishTime": "2024-07-01T10:00:00Z"
    }
}
```

## 目录结构概览
-   `controller/`: 包含处理 HTTP 请求的 RESTful API 控制器。
-   `dto/`: 数据传输对象 (Data Transfer Objects)，用于请求和响应数据封装。
-   `entity/`: 数据库实体类，与数据库表结构对应。
-   `mapper/`: MyBatis-Plus 的 Mapper 接口，用于数据库操作。
-   `service/`: 业务逻辑层接口。
-   `service/impl/`: 业务逻辑层接口的实现。
-   `utils/`: 包含辅助工具类，提供通用功能。
-   `vo/`: 视图对象 (View Objects)，用于封装前端展示所需的数据。