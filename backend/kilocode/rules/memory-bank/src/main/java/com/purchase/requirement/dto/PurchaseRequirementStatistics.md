# `PurchaseRequirementStatistics.java` 说明文档

## 文件概述
`PurchaseRequirementStatistics.java` 是一个数据传输对象（DTO），用于封装采购需求的统计数据。它提供了多种维度的统计信息，例如总需求数、今日新增需求数、高价值需求数、有附件的需求数和活跃需求数。这个类通常用于在管理界面或仪表盘上展示采购需求的概览。

## 核心功能
-   **`totalRequirements`**: 总需求数，表示系统中所有采购需求的总量。
-   **`todayRequirements`**: 今日新增需求数，表示当天新发布的采购需求数量。
-   **`highValueRequirements`**: 高价值需求数，特指预算金额大于等于 10000 的采购需求数量。
-   **`requirementsWithAttachments`**: 有附件的需求数，表示包含图片、PDF 文档等附件的采购需求数量。
-   **`activeRequirements`**: 活跃需求数，表示在最近 7 天内有更新操作的采购需求数量。

## 使用示例

### 获取采购需求统计数据响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "totalRequirements": 1250,
        "todayRequirements": 15,
        "highValueRequirements": 80,
        "requirementsWithAttachments": 300,
        "activeRequirements": 120
    }
}
```

## 注意事项
-   **Lombok 注解**: 使用 `@Data`, `@Builder`, `@NoArgsConstructor`, `@AllArgsConstructor` 等 Lombok 注解，自动生成 getter/setter、构造函数和 Builder 模式，简化了代码。
-   **数据聚合**: 这个 DTO 的数据通常由服务层通过聚合数据库查询结果来填充。
-   **业务定义**: “高价值需求”和“活跃需求”的定义是业务逻辑的一部分，可能在服务层或业务规则中实现。