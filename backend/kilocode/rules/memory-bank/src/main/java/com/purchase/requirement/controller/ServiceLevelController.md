# `ServiceLevelController.java` 说明文档

## 文件概述
`ServiceLevelController.java` 是 `com.purchase.requirement` 包中负责**服务级别管理**的控制器。它提供了对采购需求服务级别的查询、创建、更新和删除等 RESTful API 接口。服务级别通常用于定义采购需求的不同优先级或服务承诺。

## 核心功能

### 1. 服务级别查询
-   **`getAllActiveLevels()`**: 获取所有**启用状态**的服务级别列表。
    -   **权限**: 无需特定权限。
-   **`getAllLevels()`**: 获取所有服务级别列表，包括启用和禁用的。
    -   **权限**: 仅限管理员 (`ADMIN`)。
-   **`getLevelById(@PathVariable Integer id)`**: 根据服务级别ID获取单个服务级别的详细信息。
-   **`getLevelByCode(@PathVariable String levelCode)`**: 根据服务级别代码获取单个服务级别的详细信息。

### 2. 服务级别操作
-   **`createServiceLevel(@RequestBody ServiceLevelDTO serviceLevelDTO)`**: 创建新的服务级别。
    -   **权限**: 仅限管理员 (`ADMIN`)。
    -   **参数**: `serviceLevelDTO` 包含服务级别的名称、代码、描述等信息。
-   **`updateServiceLevel(@PathVariable Integer id, @RequestBody ServiceLevelDTO serviceLevelDTO)`**: 更新现有服务级别。
    -   **权限**: 仅限管理员 (`ADMIN`)。
    -   **参数**: `id` 为服务级别ID，`serviceLevelDTO` 包含更新后的服务级别信息。
-   **`deleteServiceLevel(@PathVariable Integer id)`**: 根据服务级别ID删除服务级别。
    -   **权限**: 仅限管理员 (`ADMIN`)。

## 使用示例

### 获取所有启用服务级别示例 (GET /api/v1/service-levels)
```http
GET /api/v1/service-levels
```

### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "name": "基础服务",
            "code": "BASIC",
            "description": "提供标准采购服务",
            "enabled": true
        },
        {
            "id": 2,
            "name": "高级服务",
            "code": "PREMIUM",
            "description": "提供优先处理和专属支持",
            "enabled": true
        }
    ]
}
```

## 注意事项
-   **权限控制**: 对创建、更新和删除服务级别的操作进行了严格的管理员权限 (`@PreAuthorize("hasRole('ADMIN')")`) 控制，确保只有授权用户才能修改系统配置。
-   **统一响应**: 所有接口都使用 `Result<T>` 统一封装响应，提供一致的返回格式。
-   **查询灵活性**: 提供了按ID和按代码查询服务级别的接口，方便不同场景下的数据获取。