# `RequirementStatsDTO.java` 说明文档

## 文件概述
`RequirementStatsDTO.java` 是一个轻量级的数据传输对象（DTO），用于封装采购需求的简要统计信息。它主要包含总需求数、进行中需求数和已完成需求数，旨在为前端提供快速概览数据，例如在仪表盘或统计页面展示。

## 核心功能
-   **`total`**: 总采购需求数，表示系统中所有采购需求的总量。
-   **`inProgress`**: 进行中数量，表示当前处于“进行中”状态的采购需求数量。
-   **`completed`**: 已完成数量，表示已经“完成”的采购需求数量。

## 使用示例

### 获取需求统计数据响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 1500,
        "inProgress": 300,
        "completed": 1200
    }
}
```

## 注意事项
-   **Lombok 注解**: 使用 `@Data` 自动生成 getter/setter、`equals`、`hashCode` 和 `toString` 方法，简化了代码。
-   **简洁性**: 这个 DTO 的设计非常简洁，只包含最核心的统计指标，避免了不必要的复杂性。
-   **数据来源**: 这些统计数据通常由服务层通过聚合数据库查询结果来计算和填充。