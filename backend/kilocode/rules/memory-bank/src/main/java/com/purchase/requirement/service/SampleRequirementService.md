# `SampleRequirementService.java` 说明文档

## 文件概述
`SampleRequirementService.java` 是样品需求模块的服务层接口。它定义了与样品需求相关的核心业务操作，包括样品需求的创建、查询（公开和买家专属）、修改、结束竞价（并生成订单）、关闭、重新开放、权限验证以及统计信息获取。该接口旨在为控制器层提供样品需求业务逻辑的抽象。

## 核心功能

### 1. 样品需求生命周期管理
-   **`createSampleRequirement(PurchaseRequirementDTO.CreateRequest request, Long buyerId)`**: 创建新的样品需求。
-   **`updateSampleRequirement(Long requirementId, PurchaseRequirementDTO.CreateRequest request, Long buyerId)`**: 修改已存在的样品需求。
    -   **限制**: 仅在样品需求处于“开放”状态时允许修改。
-   **`finishSampleBidding(Long requirementId, Long buyerId)`**: 结束样品竞价，并触发样品订单的生成。
-   **`closeSampleRequirement(Long requirementId, Long buyerId, String reason)`**: 关闭样品需求，不生成订单。
-   **`reopenSampleRequirement(Long requirementId, Long buyerId)`**: 重新开放已关闭的样品需求。

### 2. 样品需求查询
-   **`getSampleRequirements(Integer page, Integer size, Long categoryId, String keyword)`**: 获取公开的样品需求列表，支持分页、分类和关键词搜索。
-   **`getBuyerSampleRequirements(Long buyerId, Integer page, Integer size, String status)`**: 买家获取自己发布的样品需求列表，支持分页和状态筛选。
-   **`getSampleRequirementDetail(Long requirementId)`**: 获取单个样品需求的详细信息。
-   **`getSampleRequirementStatistics(Long requirementId)`**: 获取样品需求的统计信息，如竞价数量、已接受数量等。

### 3. 权限与状态检查
-   **`validateSampleRequirement(Long requirementId, Long buyerId)`**: 验证指定的需求是否为样品类型，并且属于指定的买家。
-   **`canFinishBidding(Long requirementId)`**: 检查样品需求是否满足结束竞价的条件。

## 使用示例

### 创建样品需求示例
```java
// 在 Controller 层调用 Service
PurchaseRequirementDTO.CreateRequest createRequest = new PurchaseRequirementDTO.CreateRequest();
createRequest.setTitle("样品采购：定制T恤");
createRequest.setDescription("需要定制一批纯棉T恤样品。");
createRequest.setCategoryId(10L);
createRequest.setQuantity(new BigDecimal("5"));
createRequest.setUnit("件");
createRequest.setContactInfo("{\"name\":\"李四\",\"phone\":\"13987654321\"}");
createRequest.setAttributesJson("{\"size\":\"L\",\"color\":\"黑色\"}");

RequirementVO newSampleRequirement = sampleRequirementService.createSampleRequirement(createRequest, 102L); // 假设买家ID为102
```

### 结束样品竞价示例
```java
// 在 Controller 层调用 Service
Long sampleOrderId = sampleRequirementService.finishSampleBidding(201L, 102L); // 假设需求ID为201，买家ID为102
System.out.println("样品订单已生成，ID为: " + sampleOrderId);
```

## 注意事项
-   **业务逻辑复杂性**: 样品需求涉及的业务流程（如竞价结束生成订单）相对复杂，服务层负责协调多个组件完成这些操作。
-   **状态流转**: 样品需求的状态管理是核心，服务层会确保状态转换的合法性。
-   **权限校验**: 在执行敏感操作（如创建、修改、结束竞价）时，服务层会进行买家ID的权限校验。
-   **与订单模块集成**: `finishSampleBidding` 方法会与订单模块进行集成，生成实际的样品订单。