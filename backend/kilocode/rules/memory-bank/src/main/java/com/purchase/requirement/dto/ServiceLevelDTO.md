# `ServiceLevelDTO.java` 说明文档

## 文件概述
`ServiceLevelDTO.java` 是一个数据传输对象（DTO），用于封装采购需求的服务级别信息。它定义了服务级别的唯一标识、代码、名称、描述、服务费率、特性列表以及启用状态。这个 DTO 主要用于在前端展示和管理不同类型的服务级别，例如在发布需求时供采购商选择。

## 核心功能
-   **`id`**: 服务级别ID，唯一标识一个服务级别。
-   **`levelCode`**: 服务级别代码，例如 "BASIC", "STANDARD", "PREMIUM"，通常用于后端逻辑判断。
-   **`name`**: 服务级别名称 (中文)。
-   **`nameEn`**: 服务级别英文名称，用于国际化或多语言支持。
-   **`description`**: 服务描述 (中文)。
-   **`descriptionEn`**: 服务英文描述。
-   **`feeRate`**: 服务费比例（百分比），表示该服务级别可能产生的额外费用。
-   **`features`**: 服务特性列表 (中文)，描述该服务级别包含的具体服务内容。
-   **`featuresEn`**: 服务特性英文列表。
-   **`status`**: 状态（0-禁用，1-启用），控制服务级别的可用性。

## 使用示例

### 服务级别数据示例
```json
{
    "id": 1,
    "levelCode": "BASIC",
    "name": "基础服务",
    "nameEn": "Basic Service",
    "description": "提供标准采购服务，适用于一般采购需求。",
    "descriptionEn": "Provides standard procurement services, suitable for general procurement needs.",
    "feeRate": 0.05,
    "features": ["需求发布", "供应商匹配", "在线沟通"],
    "featuresEn": ["Requirement Posting", "Supplier Matching", "Online Communication"],
    "status": 1
}
```

## 注意事项
-   **Lombok 注解**: 使用 `@Data` 自动生成 getter/setter、`equals`、`hashCode` 和 `toString` 方法，简化了代码。
-   **多语言支持**: 提供了中文和英文的名称、描述和特性列表字段，便于实现国际化。
-   **费率管理**: `feeRate` 字段允许为不同的服务级别配置不同的服务费率。
-   **特性列表**: `features` 字段以列表形式存储服务特性，提供了灵活的扩展性。