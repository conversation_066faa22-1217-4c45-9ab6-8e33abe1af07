# `PurchaseRequirementDTO.java` 说明文档

## 文件概述
`PurchaseRequirementDTO.java` 文件定义了在采购需求模块中用于数据传输的对象（DTOs）。它包含了多个静态内部类，分别对应采购需求在不同业务场景下的数据结构，例如创建请求、搜索请求、搜索响应、快速创建请求和最新需求响应。这些 DTOs 负责封装前端与后端之间交互的数据，并包含数据校验注解。

## 核心功能

### 1. `PurchaseRequirementDTO.CreateRequest`
-   **作用**: 用于封装创建或更新采购需求时前端提交的数据。
-   **主要字段**:
    -   `buyerId`: 买家ID (后端设置)。
    -   `categoryId`: 分类ID。
    -   `title`: 需求标题。
    -   `description`: 需求描述。
    -   `specification`: 规格说明。
    -   `minPrice`, `maxPrice`: 期望价格范围。
    -   `expectedDeliveryTime`: 期望交付时间。
    -   `images`, `videos`: 图片和视频URL列表。
    -   `pdfUrl`: PDF文档URL。
    -   `status`: 需求状态。
    -   `requirementType`: 需求类型（`purchase` 或 `sample`）。
    -   `contactInfo`: 买家联系信息 (必填)。
    -   `attributesJson`: 需求属性的JSON字符串 (必填)。
    -   `serviceLevelCode`: 服务级别代码（`basic`, `standard`, `premium`）。
    -   `serviceFeeRate`: 服务费率。
    -   `quantity`: 采购数量 (必填)。
    -   `unit`: 数量单位 (必填)。
    -   `hsCode`: 海关商品编码。
    -   `deliveryTerms`: 交付条款。
-   **注意事项**: `contactInfo`, `attributesJson`, `quantity`, `unit` 字段通过 `@NotNull` 进行非空校验。

### 2. `PurchaseRequirementDTO.CreateResponse`
-   **作用**: 用于封装创建或更新采购需求后返回给前端的响应数据。
-   **主要字段**: 包含 `CreateRequest` 的大部分字段，并额外包含 `requirementId` (需求ID)、`categoryName` (分类名称)、`createdAt` (创建时间) 和 `updatedAt` (更新时间)。

### 3. `PurchaseRequirementDTO.SearchRequest`
-   **作用**: 用于封装搜索采购需求时前端提交的查询参数。
-   **主要字段**:
    -   `keyword`: 关键词（在标题和描述中模糊匹配）。
    -   `categoryId`: 分类ID。
    -   `minPrice`, `maxPrice`: 价格范围。
    -   `minQuantity`, `maxQuantity`: 数量范围。
    -   `status`: 需求状态（`pending`, `in_progress`, `completed`）。
    -   `page`, `size`: 分页参数（默认 `page=1`, `size=12`）。
    -   `serviceLevelCode`: 服务级别筛选。
    -   `sortBy`, `sortOrder`: 排序方式和方向（默认按 `publishTime` 降序）。
    -   `onlyMine`: 是否只查询当前用户的需求。
    -   `currentUserId`: 当前用户ID (后端自动设置)。

### 4. `PurchaseRequirementDTO.SearchResponse`
-   **作用**: 用于封装搜索采购需求后返回给前端的分页结果。
-   **主要字段**:
    -   `total`: 总记录数。
    -   `size`: 每页大小。
    -   `current`: 当前页码。
    -   `pages`: 总页数。
    -   `items`: 当前页的数据列表，每个 `item` 是一个 `SearchItem` 对象。
-   **`SearchItem` 内部类**: 包含单个搜索结果的详细信息，与 `CreateResponse` 类似，并额外包含 `biddingCount` (竞价数量) 和 `PriceRange` 内部类。

### 5. `PurchaseRequirementDTO.QuickCreateRequest`
-   **作用**: 用于首页快速创建采购需求的简化DTO，只包含必要字段。
-   **主要字段**: `title`, `categoryId`, `quantity`, `unit`, `attributesJson`, `contactInfo`, `images`, `pdfUrl`, `hsCode`, `deliveryTerms`。
-   **`toCreateRequest()` 方法**: 将简化请求转换为完整的 `CreateRequest` 对象，并设置默认值（如 `serviceLevelCode` 为 `basic`，`status` 为默认状态）。

### 6. `PurchaseRequirementDTO.LatestRequirementResponse`
-   **作用**: 用于封装最新采购需求的响应数据。
-   **主要字段**: `id`, `title`, `images`, `createdAt`, `status`。

## 使用示例

### 创建需求请求示例 (`CreateRequest`)
```json
{
    "categoryId": 1,
    "title": "采购一批办公用品",
    "description": "需要采购A4纸、签字笔、订书机等办公用品。",
    "quantity": 100,
    "unit": "件",
    "contactInfo": "{\"name\":\"张三\",\"phone\":\"13812345678\"}",
    "attributesJson": "{\"color\":\"白色\",\"material\":\"塑料\"}"
}
```

### 搜索需求请求示例 (`SearchRequest`)
```json
{
    "keyword": "办公用品",
    "categoryId": 1,
    "status": "in_progress",
    "page": 1,
    "size": 10,
    "onlyMine": true
}
```

## 注意事项
-   **数据封装**: DTOs 负责将前端数据与后端实体解耦，确保数据传输的清晰和安全。
-   **校验注解**: 使用 `javax.validation.constraints` 包下的注解对字段进行校验，例如 `@NotNull`。
-   **内部类**: 通过静态内部类组织不同场景下的数据结构，提高了代码的可读性和内聚性。
-   **JSON 字段**: `attributesJson` 字段用于存储灵活的需求属性，以 JSON 字符串形式表示。