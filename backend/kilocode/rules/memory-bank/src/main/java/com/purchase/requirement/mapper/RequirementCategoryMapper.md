# `RequirementCategoryMapper.java` 说明文档

## 文件概述
`RequirementCategoryMapper.java` 是需求分类模块的 MyBatis-Plus Mapper 接口。它继承了 `BaseMapper<RequirementCategory>`，提供了对 `requirement_category` 表进行基本 CRUD 操作的能力。此外，该接口还定义了用于查询子分类和所有启用分类的自定义方法，以支持需求分类的树形结构和状态管理。

## 核心功能

### 1. 继承 `BaseMapper`
-   自动拥有 `insert`, `deleteById`, `updateById`, `selectById`, `selectList` 等基本 CRUD 方法。

### 2. 自定义查询方法
-   **`selectByParentId(@Param("parentId") Long parentId)`**:
    -   **作用**: 根据父分类ID查询其直接子分类的列表。
    -   **参数**: `parentId` 为父分类的ID。
    -   **返回**: 返回子分类实体列表。
-   **`selectAllEnabled()`**:
    -   **作用**: 查询所有状态为“启用”的需求分类列表。
    -   **返回**: 返回启用状态的分类实体列表。

## 使用示例

### 查询子分类示例
```java
// 在 Service 层调用 Mapper
List<RequirementCategory> children = requirementCategoryMapper.selectByParentId(1L);
```

### 查询所有启用分类示例
```java
// 在 Service 层调用 Mapper
List<RequirementCategory> enabledCategories = requirementCategoryMapper.selectAllEnabled();
```

## 注意事项
-   **`@Repository` 注解**: 标记该接口为一个 Spring Repository 组件，通常与 `@Mapper` 或 `@Component` 配合使用。
-   **`@Param` 注解**: 当方法有多个参数时，建议使用 `@Param` 注解为参数指定名称，以便在 XML Mapper 文件中引用。
-   **XML Mapper 文件**: 实际的 SQL 语句通常定义在与此接口同名的 XML 文件中（例如 `RequirementCategoryMapper.xml`），通过命名空间和方法ID进行关联。
-   **MyBatis-Plus 扩展**: 继承 `BaseMapper` 极大地简化了常见的数据库操作。