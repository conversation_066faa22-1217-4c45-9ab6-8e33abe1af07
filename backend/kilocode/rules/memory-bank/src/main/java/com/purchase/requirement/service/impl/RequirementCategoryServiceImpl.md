# `RequirementCategoryServiceImpl.java` 说明文档

## 文件概述
`RequirementCategoryServiceImpl.java` 是 `RequirementCategoryService` 接口的实现类，负责处理需求分类模块的业务逻辑。它继承了 MyBatis-Plus 的 `ServiceImpl`，简化了对 `RequirementCategory` 实体的数据库操作。该实现类包含了分类的查询（列表和树形结构）、添加、更新、删除和状态管理等功能，并处理了分类删除时的业务约束（如检查子分类和关联需求）。

## 核心功能

### 1. 分类查询
-   **`listCategories()`**: 查询所有需求分类的列表，并按排序顺序和ID排序。
-   **`listEnabledCategories()`**: 查询所有启用状态的需求分类列表。
-   **`getCategoryTree()`**: 获取所有需求分类的树形结构。
-   **`getEnabledCategoryTree()`**: 获取所有启用状态的需求分类的树形结构。
-   **`getCategoryById(Long id)`**: 根据分类ID查询单个分类的详细信息，并获取父分类名称。

### 2. 分类操作
-   **`addCategory(RequirementCategoryDTO categoryDTO)`**: 添加新的需求分类。
    -   **业务逻辑**: 将 DTO 转换为实体并保存。
-   **`updateCategory(RequirementCategoryDTO categoryDTO)`**: 更新现有需求分类。
    -   **业务逻辑**: 将 DTO 转换为实体并更新。
-   **`deleteCategory(Long id)`**: 根据分类ID删除需求分类。
    -   **业务约束**: 在删除前检查是否存在子分类或关联的需求，如果存在则抛出运行时异常。
-   **`updateCategoryStatus(Long id, Integer status)`**: 更新需求分类的启用/禁用状态。

## 辅助方法
-   **`buildCategoryTree(List<RequirementCategory> categoryList)`**: 将扁平化的分类列表构建成树形结构。
-   **`convertToVO(RequirementCategory category)`**: 将 `RequirementCategory` 实体转换为 `RequirementCategoryVO` 对象，并设置状态文本。
-   **`convertToVOList(List<RequirementCategory> categoryList)`**: 将 `RequirementCategory` 实体列表转换为 `RequirementCategoryVO` 列表，并设置父分类名称。
-   **`convertToTreeDTO(RequirementCategory category)`**: 将 `RequirementCategory` 实体转换为 `RequirementCategoryTreeDTO` 对象。

## 注意事项
-   **MyBatis-Plus `ServiceImpl`**: 继承 `ServiceImpl` 提供了便捷的 CRUD 操作，减少了 Mapper 层的直接调用。
-   **事务管理**: `addCategory`, `updateCategory`, `deleteCategory`, `updateCategoryStatus` 方法都通过 `@Transactional` 注解进行事务管理，确保数据一致性。
-   **业务约束**: `deleteCategory` 方法中实现了重要的业务约束，防止删除有依赖关系的分类，保证数据完整性。
-   **数据转换**: 内部辅助方法负责实体、DTO 和 VO 之间的转换，保持了业务逻辑的清晰。
-   **树形结构构建**: `buildCategoryTree` 方法是构建分类树的关键，通过递归或迭代方式将扁平数据转换为层级结构。