# `ServiceLevelMapper.java` 说明文档

## 文件概述
`ServiceLevelMapper.java` 是服务级别模块的 MyBatis-Plus Mapper 接口。它继承了 `BaseMapper<ServiceLevel>`，提供了对 `service_level_config` 表进行基本 CRUD 操作的能力。此外，该接口还定义了多个自定义的数据库查询方法，用于满足服务级别的查询需求，例如按状态查询、按ID查询和按级别代码查询。

## 核心功能

### 1. 继承 `BaseMapper`
-   自动拥有 `insert`, `deleteById`, `updateById`, `selectById`, `selectList` 等基本 CRUD 方法。

### 2. 自定义查询方法
-   **`selectAll()`**:
    -   **作用**: 查询所有服务级别列表，包括启用和禁用的。
-   **`selectByStatus(@Param("status") Integer status)`**:
    -   **作用**: 根据服务级别状态（0-禁用，1-启用）查询服务级别列表。
    -   **参数**: `status` 为服务级别状态。
-   **`selectById(@Param("id") Integer id)`**:
    -   **作用**: 根据服务级别ID查询单个服务级别。
    -   **参数**: `id` 为服务级别ID。
-   **`selectByLevelCode(@Param("levelCode") String levelCode)`**:
    -   **作用**: 根据服务级别代码查询单个服务级别。
    -   **参数**: `levelCode` 为服务级别代码。

### 3. 自定义操作方法
-   **`insert(ServiceLevel serviceLevel)`**:
    -   **作用**: 插入新的服务级别记录。
-   **`update(ServiceLevel serviceLevel)`**:
    -   **作用**: 更新现有服务级别记录。
-   **`deleteById(@Param("id") Integer id)`**:
    -   **作用**: 根据服务级别ID删除服务级别记录。

## 使用示例

### 查询所有启用服务级别示例
```java
// 在 Service 层调用 Mapper
List<ServiceLevel> activeLevels = serviceLevelMapper.selectByStatus(1); // 1 表示启用
```

### 根据级别代码查询服务级别示例
```java
// 在 Service 层调用 Mapper
ServiceLevel basicLevel = serviceLevelMapper.selectByLevelCode("BASIC");
```

## 注意事项
-   **`@Mapper` 注解**: 标记该接口为一个 MyBatis Mapper，Spring 会自动扫描并注册。
-   **`@Param` 注解**: 当方法有多个参数时，建议使用 `@Param` 注解为参数指定名称，以便在 XML Mapper 文件中引用。
-   **XML Mapper 文件**: 实际的 SQL 语句通常定义在与此接口同名的 XML 文件中（例如 `ServiceLevelMapper.xml`），通过命名空间和方法ID进行关联。
-   **MyBatis-Plus 扩展**: 继承 `BaseMapper` 极大地简化了常见的数据库操作。