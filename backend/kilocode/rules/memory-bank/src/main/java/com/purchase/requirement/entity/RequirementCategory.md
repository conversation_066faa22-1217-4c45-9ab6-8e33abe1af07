# `RequirementCategory.java` 说明文档

## 文件概述
`RequirementCategory.java` 是需求分类模块的数据库实体类，对应数据库中的 `requirement_category` 表。它定义了需求分类的所有持久化属性，包括分类ID、名称、描述、图标、排序顺序、父分类ID、状态以及用于存储分类属性模板的 JSON 字段。该实体类通过 MyBatis-Plus 注解与数据库进行映射。

## 核心功能

### 实体属性
-   **`id`**: 分类ID，主键，自增长 (`@TableId(value = "id", type = IdType.AUTO)`).
-   **`name`**: 分类名称 (中文)，映射到 `name` 字段。
-   **`nameEn`**: 分类英文名称，映射到 `name_en` 字段。
-   **`description`**: 分类描述，映射到 `description` 字段。
-   **`icon`**: 分类图标的 URL 或标识符，映射到 `icon` 字段。
-   **`sortOrder`**: 排序顺序，映射到 `sort_order` 字段。
-   **`parentId`**: 父分类ID，用于构建分类的树形结构，映射到 `parent_id` 字段。
-   **`status`**: 分类状态（0-禁用，1-启用），映射到 `status` 字段。
-   **`attributesJson`**: 分类属性模板的 JSON 字符串，映射到 `attributes_json` 字段。
-   **`attributesJsonEn`**: 分类英文属性模板的 JSON 字符串，映射到 `attributes_json_en` 字段。
-   **`createdAt`**: 创建时间，映射到 `created_at` 字段。
-   **`updatedAt`**: 更新时间，映射到 `updated_at` 字段。

## 使用示例

### `RequirementCategory` 实体示例
```java
RequirementCategory category = new RequirementCategory();
category.setName("电子产品");
category.setNameEn("Electronics");
category.setDescription("包含手机、电脑、平板等电子设备。");
category.setParentId(null); // 根分类
category.setStatus(1); // 启用
category.setAttributesJson("{\"brand\":\"string\",\"warranty\":\"integer\"}");
// ... 设置其他属性
```

## 注意事项
-   **MyBatis-Plus 注解**: `@TableName` 用于指定实体类对应的数据库表名。`@TableId` 用于指定主键及其生成策略。`@TableField` 用于指定实体属性与数据库字段的映射关系。
-   **Lombok 注解**: `@Data` 自动生成 getter/setter、`equals`、`hashCode` 和 `toString` 方法。`@EqualsAndHashCode(callSuper = false)` 确保在比较对象时不会调用父类的 `equals` 和 `hashCode` 方法。`@Accessors(chain = true)` 允许链式调用 setter 方法。
-   **序列化**: 实现了 `Serializable` 接口，支持对象的序列化。
-   **多语言支持**: `nameEn`, `descriptionEn`, `attributesJsonEn` 字段提供了多语言支持。