# `ServiceLevel.java` 说明文档

## 文件概述
`ServiceLevel.java` 是服务级别模块的数据库实体类，对应数据库中的 `service_level_config` 表。它定义了服务级别的所有持久化属性，包括服务级别ID、代码、名称、描述、服务费比例、服务特性（JSON 格式）以及启用状态。该实体类通过 MyBatis-Plus 注解与数据库进行映射。

## 核心功能

### 实体属性
-   **`id`**: 服务级别ID，主键，自增长 (`@TableId(value = "id", type = IdType.AUTO)`).
-   **`levelCode`**: 服务级别代码，例如 "BASIC", "STANDARD", "PREMIUM"，映射到 `level_code` 字段。
-   **`name`**: 服务级别名称 (中文)，映射到 `name` 字段。
-   **`nameEn`**: 服务级别英文名称，映射到 `name_en` 字段。
-   **`description`**: 服务描述 (中文)，映射到 `description` 字段。
-   **`descriptionEn`**: 服务英文描述，映射到 `description_en` 字段。
-   **`feeRate`**: 服务费比例（百分比），映射到 `fee_rate` 字段。
-   **`featuresJson`**: 服务特性列表的 JSON 字符串 (中文)，映射到 `features_json` 字段。
-   **`featuresJsonEn`**: 服务特性英文列表的 JSON 字符串，映射到 `features_json_en` 字段。
-   **`status`**: 状态（0-禁用，1-启用），映射到 `status` 字段。
-   **`createdAt`**: 创建时间，映射到 `created_at` 字段。
-   **`updatedAt`**: 更新时间，映射到 `updated_at` 字段。

## 使用示例

### `ServiceLevel` 实体示例
```java
ServiceLevel serviceLevel = new ServiceLevel();
serviceLevel.setLevelCode("PREMIUM");
serviceLevel.setName("高级服务");
serviceLevel.setDescription("提供专属客户经理和快速响应。");
serviceLevel.setFeeRate(0.10);
serviceLevel.setFeaturesJson("[\"专属客户经理\", \"24/7支持\"]");
serviceLevel.setStatus(1); // 启用
// ... 设置其他属性
```

## 注意事项
-   **MyBatis-Plus 注解**: `@TableName` 用于指定实体类对应的数据库表名。`@TableId` 用于指定主键及其生成策略。`@TableField` 用于指定实体属性与数据库字段的映射关系。
-   **Lombok 注解**: `@Data` 自动生成 getter/setter、`equals`、`hashCode` 和 `toString` 方法。
-   **多语言支持**: `nameEn`, `descriptionEn`, `featuresJsonEn` 字段提供了多语言支持。
-   **JSON 字段**: `featuresJson` 和 `featuresJsonEn` 字段以 JSON 字符串形式存储服务特性，提供了灵活的扩展性。