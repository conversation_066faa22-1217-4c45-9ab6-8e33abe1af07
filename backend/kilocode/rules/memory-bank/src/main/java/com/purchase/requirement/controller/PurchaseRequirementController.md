# PurchaseRequirementController.java

## 文件概述 (File Overview)
`PurchaseRequirementController.java` 是采购需求管理的REST控制器，位于 `com.purchase.requirement.controller` 包中。该控制器作为采购需求管理模块的HTTP接口层，负责处理所有采购需求相关的REST API请求。通过集成 `PurchaseRequirementService`、`AuditLogService` 等业务服务，提供了采购需求的完整生命周期管理功能，包括创建、查询、更新、删除、统计、搜索、状态管理等。该控制器实现了基于Spring Security的多层权限控制，支持买家、卖家、管理员等不同角色的差异化访问，并提供了完善的需求匹配和业务流程管理功能。

## 核心功能

### 1. 需求统计与查询
-   **`getRequirementStats()`**: 获取系统层面的采购需求统计数据。
    -   **权限**: 仅限管理员 (`admin`)。
-   **`getAllRequirements()`**: 获取所有采购需求列表。
    -   **功能**: 支持分页和按分类ID筛选。
    -   **权限**: 买家 (`buyer`), 卖家 (`seller`), 管理员 (`admin`)。
-   **`getMyRequirementStats()`**: 获取当前登录买家的需求统计数据。
    -   **权限**: 仅限买家 (`buyer`)。
-   **`searchRequirements()`**: 灵活搜索采购需求。
    -   **功能**: 支持关键词、分类、状态、价格范围、数量范围、服务级别、排序和“仅我的需求”等多种筛选条件。
    -   **权限**: 买家 (`buyer`), 卖家 (`seller`), 管理员 (`admin`)。
-   **`getAdminRequirements()`**: 管理员专用接口，获取所有需求列表。
    -   **功能**: 支持状态、价格范围、搜索关键词等筛选。
    -   **权限**: 仅限管理员 (`admin`)。
-   **`getFeaturedRequirements()`**: 获取热门采购需求列表。
    -   **功能**: 公开接口，用于首页展示，可限制返回数量。
    -   **权限**: 无需认证。
-   **`getRequirementById()`**: 根据ID获取单个采购需求的详细信息。
    -   **权限**: 管理员 (`admin`), 买家 (`buyer`), 卖家 (`seller`)。
-   **`getMyLatestRequirements()`**: 获取当前买家最新发布的采购需求。
    -   **权限**: 仅限买家 (`buyer`)。
-   **`getLatestRequirements()`**: 获取最新发布的采购需求。
    -   **权限**: 卖家 (`seller`), 管理员 (`admin`)。
-   **`getStatistics()`**: 获取采购需求统计数据。
    -   **权限**: 无特定权限要求。

### 2. 需求操作
-   **`createRequirement()`**: 创建新的采购需求。
    -   **权限**: 仅限买家 (`buyer`)。
    -   **审计**: 记录创建操作的审计日志。
-   **`updateRequirement()`**: 更新现有采购需求。
    -   **权限**: 仅限买家 (`buyer`)。
    -   **审计**: 记录更新操作的审计日志。
-   **`cancelRequirement()`**: 取消采购需求。
    -   **功能**: 仅允许取消处于 `in_progress` 状态的需求。
    -   **权限**: 买家 (`buyer`), 管理员 (`admin`)。
    -   **审计**: 记录取消操作的审计日志。
-   **`deleteRequirement()`**: 删除采购需求。
    -   **权限**: 仅限买家 (`buyer`)。
    -   **审计**: 记录删除操作的审计日志。
-   **`deleteRequirementByAdmin()`**: 管理员删除任何采购需求。
    -   **权限**: 仅限管理员 (`admin`)。
-   **`updateRequirementStatus()`**: 管理员更新采购需求状态。
    -   **功能**: 可将需求状态更新为 "in_progress" 或 "completed"。
    -   **权限**: 仅限管理员 (`admin`)。
-   **`quickCreateRequirement()`**: 首页快速创建采购需求。
    -   **功能**: 允许未登录用户使用简化信息快速创建，提高转化率。
    -   **权限**: 买家 (`buyer`), 管理员 (`admin`)。

### 3. 其他功能
-   **`getRequirementsByIds()`**: 批量获取多个采购需求的详细信息。
    -   **权限**: 买家 (`buyer`), 卖家 (`seller`)。
-   **`getRequirementContact()`**: 获取采购需求的联系信息。
    -   **功能**: 根据用户角色（买家、管理员）对联系信息进行脱敏处理。
    -   **权限**: 已登录用户。

## 使用示例 (Usage Examples)

```java
// 1. 前端JavaScript调用示例
const RequirementAPI = {
    // 创建采购需求
    async createRequirement(requirementData) {
        const response = await fetch('/api/v1/requirements', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                title: requirementData.title,
                description: requirementData.description,
                categoryId: requirementData.categoryId,
                quantity: requirementData.quantity,
                unit: requirementData.unit,
                budgetMin: requirementData.budgetMin,
                budgetMax: requirementData.budgetMax,
                deliveryAddress: requirementData.deliveryAddress,
                expectedDeliveryDate: requirementData.expectedDeliveryDate,
                serviceLevel: requirementData.serviceLevel,
                attachments: requirementData.attachments,
                contactName: requirementData.contactName,
                contactPhone: requirementData.contactPhone,
                contactEmail: requirementData.contactEmail
            })
        });

        const result = await response.json();
        if (result.code === 200) {
            showSuccessMessage('采购需求创建成功，等待审核');
            return result.data;
        } else {
            showErrorMessage('创建失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 搜索采购需求
    async searchRequirements(searchParams = {}) {
        const queryParams = new URLSearchParams({
            page: searchParams.page || 1,
            size: searchParams.size || 20,
            keyword: searchParams.keyword || '',
            categoryId: searchParams.categoryId || '',
            status: searchParams.status || '',
            minPrice: searchParams.minPrice || '',
            maxPrice: searchParams.maxPrice || '',
            minQuantity: searchParams.minQuantity || '',
            maxQuantity: searchParams.maxQuantity || '',
            serviceLevel: searchParams.serviceLevel || '',
            sortBy: searchParams.sortBy || 'publishTime',
            sortOrder: searchParams.sortOrder || 'desc',
            onlyMine: searchParams.onlyMine || false
        });

        const response = await fetch(`/api/v1/requirements/search?${queryParams}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.code === 200) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 获取采购需求详情
    async getRequirementById(requirementId) {
        const response = await fetch(`/api/v1/requirements/${requirementId}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.code === 200) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 更新采购需求
    async updateRequirement(requirementId, updateData) {
        const response = await fetch(`/api/v1/requirements/${requirementId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify(updateData)
        });

        const result = await response.json();
        if (result.code === 200) {
            showSuccessMessage('采购需求更新成功');
            return result.data;
        } else {
            showErrorMessage('更新失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 取消采购需求
    async cancelRequirement(requirementId, reason) {
        const response = await fetch(`/api/v1/requirements/${requirementId}/cancel`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                reason: reason
            })
        });

        const result = await response.json();
        if (result.code === 200) {
            showSuccessMessage('采购需求已取消');
            return result.data;
        } else {
            showErrorMessage('取消失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 获取热门需求
    async getFeaturedRequirements(limit = 10) {
        const response = await fetch(`/api/v1/requirements/featured?limit=${limit}`, {
            method: 'GET'
        });

        const result = await response.json();
        if (result.code === 200) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 快速创建需求（首页）
    async quickCreateRequirement(quickData) {
        const response = await fetch('/api/v1/requirements/quick-create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                title: quickData.title,
                description: quickData.description,
                contactPhone: quickData.contactPhone,
                contactEmail: quickData.contactEmail,
                estimatedBudget: quickData.estimatedBudget
            })
        });

        const result = await response.json();
        if (result.code === 200) {
            showSuccessMessage('需求提交成功，我们会尽快联系您');
            return result.data;
        } else {
            showErrorMessage('提交失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 渲染需求列表
    renderRequirementList(requirements, containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        requirements.forEach(requirement => {
            const requirementDiv = document.createElement('div');
            requirementDiv.className = 'requirement-item';

            requirementDiv.innerHTML = `
                <div class="requirement-header">
                    <h3>${requirement.title}</h3>
                    <span class="status ${requirement.status.toLowerCase()}">${requirement.statusText}</span>
                </div>
                <div class="requirement-content">
                    <div class="requirement-info">
                        <p class="description">${requirement.description}</p>
                        <div class="requirement-details">
                            <span class="category">${requirement.categoryName}</span>
                            <span class="quantity">${requirement.quantity} ${requirement.unit}</span>
                            <span class="budget">预算: ¥${requirement.budgetMin} - ¥${requirement.budgetMax}</span>
                            <span class="delivery-date">期望交期: ${new Date(requirement.expectedDeliveryDate).toLocaleDateString()}</span>
                        </div>
                        <p class="publish-time">发布时间: ${new Date(requirement.publishTime).toLocaleString()}</p>
                    </div>
                </div>
                <div class="requirement-actions">
                    <button onclick="viewRequirementDetails(${requirement.id})">查看详情</button>
                    <button onclick="getContactInfo(${requirement.id})">获取联系方式</button>
                    ${requirement.canEdit ?
                        `<button onclick="editRequirement(${requirement.id})">编辑</button>` : ''}
                    ${requirement.canCancel ?
                        `<button onclick="cancelRequirement(${requirement.id})" class="cancel-btn">取消</button>` : ''}
                </div>
            `;

            container.appendChild(requirementDiv);
        });
    }
};

// 2. Java客户端调用示例
@Service
public class RequirementClientService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${api.base-url}")
    private String baseUrl;

    // 创建采购需求
    public PurchaseRequirementDTO createRequirement(CreateRequirementRequest request, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<CreateRequirementRequest> entity = new HttpEntity<>(request, headers);

        try {
            ResponseEntity<ApiResponse<PurchaseRequirementDTO>> response = restTemplate.exchange(
                baseUrl + "/api/v1/requirements",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<ApiResponse<PurchaseRequirementDTO>>() {}
            );

            ApiResponse<PurchaseRequirementDTO> result = response.getBody();
            if (result != null && result.getCode() == 200) {
                return result.getData();
            } else {
                throw new BusinessException("创建采购需求失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用创建采购需求API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }

    // 搜索采购需求
    public PageResult<PurchaseRequirementDTO> searchRequirements(RequirementSearchRequest request, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl + "/api/v1/requirements/search")
            .queryParam("page", request.getPage())
            .queryParam("size", request.getSize())
            .queryParam("keyword", request.getKeyword())
            .queryParam("categoryId", request.getCategoryId())
            .queryParam("status", request.getStatus());

        HttpEntity<?> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<ApiResponse<PageResult<PurchaseRequirementDTO>>> response = restTemplate.exchange(
                builder.toUriString(),
                HttpMethod.GET,
                entity,
                new ParameterizedTypeReference<ApiResponse<PageResult<PurchaseRequirementDTO>>>() {}
            );

            ApiResponse<PageResult<PurchaseRequirementDTO>> result = response.getBody();
            if (result != null && result.getCode() == 200) {
                return result.getData();
            } else {
                throw new BusinessException("搜索采购需求失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用搜索采购需求API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }
}

// 3. 业务服务集成示例
@Service
public class RequirementWorkflowService {

    @Autowired
    private PurchaseRequirementService requirementService;

    @Autowired
    private NotificationService notificationService;

    // 需求创建后的业务流程
    @EventListener
    @Async
    public void handleRequirementCreated(RequirementCreatedEvent event) {
        try {
            PurchaseRequirementDTO requirement = event.getRequirement();

            // 1. 发送创建通知
            notificationService.sendRequirementCreatedNotification(requirement);

            // 2. 自动匹配潜在供应商
            List<SupplierDTO> potentialSuppliers = matchPotentialSuppliers(requirement);

            // 3. 发送匹配通知给供应商
            for (SupplierDTO supplier : potentialSuppliers) {
                notificationService.sendRequirementMatchNotification(supplier, requirement);
            }

            // 4. 发送审核提醒给管理员
            notificationService.sendRequirementReviewReminder(requirement);

            log.info("采购需求创建后处理完成: requirementId={}", requirement.getId());

        } catch (Exception e) {
            log.error("采购需求创建后处理失败: requirementId={}", event.getRequirement().getId(), e);
        }
    }

    // 需求审核后的业务流程
    @EventListener
    @Async
    public void handleRequirementReviewed(RequirementReviewedEvent event) {
        try {
            PurchaseRequirementDTO requirement = event.getRequirement();
            boolean approved = event.isApproved();

            // 1. 发送审核结果通知
            if (approved) {
                notificationService.sendRequirementApprovedNotification(requirement);

                // 2. 发布到公开市场
                publishToMarketplace(requirement);

                // 3. 推送给相关供应商
                pushToRelevantSuppliers(requirement);
            } else {
                notificationService.sendRequirementRejectedNotification(requirement, event.getReason());
            }

            log.info("采购需求审核后处理完成: requirementId={}, approved={}", requirement.getId(), approved);

        } catch (Exception e) {
            log.error("采购需求审核后处理失败: requirementId={}", event.getRequirement().getId(), e);
        }
    }
}

// 4. 定时任务示例
@Component
public class RequirementScheduledTasks {

    @Autowired
    private PurchaseRequirementService requirementService;

    // 检查过期需求
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点
    public void checkExpiredRequirements() {
        log.info("开始检查过期采购需求");

        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30);
            List<PurchaseRequirementDTO> expiredRequirements =
                requirementService.findRequirementsExpiredBefore(cutoffDate);

            for (PurchaseRequirementDTO requirement : expiredRequirements) {
                // 自动关闭过期需求
                requirementService.closeRequirement(requirement.getId(), "系统自动关闭：需求已过期");

                // 发送过期通知
                notificationService.sendRequirementExpiredNotification(requirement);
            }

            log.info("过期采购需求检查完成，处理数量: {}", expiredRequirements.size());

        } catch (Exception e) {
            log.error("检查过期采购需求失败", e);
        }
    }

    // 生成需求统计报告
    @Scheduled(cron = "0 0 8 * * MON") // 每周一早上8点
    public void generateWeeklyRequirementReport() {
        log.info("开始生成周度采购需求报告");

        try {
            WeeklyRequirementReport report = new WeeklyRequirementReport();
            report.setReportPeriod("最近一周");
            report.setNewRequirementCount(requirementService.getNewRequirementCountThisWeek());
            report.setActiveRequirementCount(requirementService.getActiveRequirementCountThisWeek());
            report.setCategoryDistribution(requirementService.getCategoryDistributionThisWeek());
            report.setBudgetDistribution(requirementService.getBudgetDistributionThisWeek());
            report.setTopCategories(requirementService.getTopCategoriesThisWeek(10));

            // 发送报告给管理员
            notificationService.sendWeeklyRequirementReport(report);

            log.info("周度采购需求报告生成完成");

        } catch (Exception e) {
            log.error("生成周度采购需求报告失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class PurchaseRequirementControllerTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @MockBean
    private PurchaseRequirementService requirementService;

    @Test
    void testCreateRequirement() {
        // 准备测试数据
        CreateRequirementRequest request = new CreateRequirementRequest();
        request.setTitle("采购办公用品");
        request.setDescription("需要采购一批办公用品，包括文具、纸张等");
        request.setCategoryId(1L);
        request.setQuantity(100);
        request.setUnit("套");
        request.setBudgetMin(new BigDecimal("5000"));
        request.setBudgetMax(new BigDecimal("10000"));

        PurchaseRequirementDTO expectedRequirement = new PurchaseRequirementDTO();
        expectedRequirement.setId(1L);
        expectedRequirement.setTitle("采购办公用品");
        expectedRequirement.setStatus("PENDING_REVIEW");

        // Mock服务调用
        when(requirementService.createRequirement(any(CreateRequirementRequest.class)))
            .thenReturn(expectedRequirement);

        // 执行测试
        HttpEntity<CreateRequirementRequest> entity = new HttpEntity<>(request);
        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
            "/api/v1/requirements", entity, ApiResponse.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getCode()).isEqualTo(200);

        // 验证服务调用
        verify(requirementService, times(1)).createRequirement(any(CreateRequirementRequest.class));
    }
}
```

## 注意事项 (Notes)
*   **权限控制**: 使用Spring Security的@PreAuthorize注解进行权限验证，确保只有相应角色的用户才能访问对应功能
*   **审计日志**: 关键业务操作通过@AuditLog注解记录详细的审计日志，便于追踪和回溯操作历史
*   **统一响应**: 所有接口都返回统一的ApiResponse格式，包含code、message和data字段
*   **用户身份**: 通过SecurityContextUtil.getCurrentUserId()获取当前登录用户ID，确保操作与用户身份关联
*   **参数校验**: 对请求参数进行严格的合法性校验，包括分页参数、需求类型、预算范围等
*   **数据脱敏**: 联系信息根据用户角色进行脱敏处理，保护用户隐私
*   **状态管理**: 需求状态包括待审核、已发布、进行中、已完成、已取消等，需要严格控制状态流转
*   **搜索优化**: 支持多维度搜索和排序，需要合理的数据库索引设计提高查询性能
*   **缓存策略**: 热门需求等频繁查询的数据使用缓存优化，减少数据库压力
*   **异步处理**: 需求创建后的通知和匹配等操作使用异步处理，提高响应性能
*   **业务委托**: 控制器只负责HTTP请求处理，具体业务逻辑委托给服务层处理
*   **异常处理**: 完善的异常处理机制，确保在出现错误时返回友好的错误信息
*   **批量操作**: 支持批量获取需求信息，需要控制批量大小避免性能问题
*   **定时任务**: 过期需求检查等定时任务需要考虑分布式环境下的任务调度
*   **国际化**: 需求状态和错误消息需要支持多语言，便于国际化部署
*   **性能监控**: 搜索和查询操作需要监控性能，及时发现和解决慢查询问题