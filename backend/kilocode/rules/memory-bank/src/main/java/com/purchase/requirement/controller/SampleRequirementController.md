# `SampleRequirementController.java` 说明文档

## 文件概述
`SampleRequirementController.java` 是 `com.purchase.requirement` 包中负责**样品需求管理**的控制器。它提供了样品需求的发布、查询、修改、结束竞价、关闭、重新开放以及权限验证和统计等一系列 RESTful API 接口。该控制器主要面向买家用户，并对部分接口进行了权限控制。

## 核心功能

### 1. 样品需求创建与修改
-   **`createSampleRequirement(@Valid @RequestBody PurchaseRequirementDTO.CreateRequest request)`**: 买家创建新的样品需求。
    -   **权限**: 仅限买家 (`buyer`)。
-   **`updateSampleRequirement(@PathVariable Long requirementId, @Valid @RequestBody PurchaseRequirementDTO.CreateRequest request)`**: 买家修改已存在的样品需求。
    -   **权限**: 仅限买家 (`buyer`)。

### 2. 样品需求查询
-   **`getSampleRequirements(Integer page, Integer size, Long categoryId, String keyword)`**: 获取公开的样品需求列表。
    -   **功能**: 支持分页、按分类ID和关键词筛选。
    -   **权限**: 无需认证。
-   **`getBuyerSampleRequirements(Integer page, Integer size, String status)`**: 买家获取自己发布的样品需求列表。
    -   **功能**: 支持分页和按需求状态筛选。
    -   **权限**: 仅限买家 (`buyer`)。
-   **`getSampleRequirementDetail(@PathVariable Long requirementId)`**: 获取单个样品需求的详细信息。
-   **`getSampleRequirementStatistics(@PathVariable Long requirementId)`**: 获取样品需求的统计信息（如竞价数量等）。
    -   **权限**: 买家 (`buyer`), 管理员 (`admin`)。

### 3. 样品需求状态管理
-   **`finishSampleBidding(@PathVariable Long requirementId)`**: 买家结束样品竞价，并自动生成样品订单。
    -   **权限**: 仅限买家 (`buyer`)。
-   **`closeSampleRequirement(@PathVariable Long requirementId, String reason)`**: 买家关闭样品需求。
    -   **功能**: 可选提供关闭原因。
    -   **权限**: 仅限买家 (`buyer`)。
-   **`reopenSampleRequirement(@PathVariable Long requirementId)`**: 买家重新开放已关闭的样品需求。
    -   **权限**: 仅限买家 (`buyer`)。

### 4. 权限与状态检查
-   **`validateSampleRequirement(@PathVariable Long requirementId)`**: 验证当前买家是否有权限操作指定的样品需求。
    -   **权限**: 仅限买家 (`buyer`)。
-   **`canFinishBidding(@PathVariable Long requirementId)`**: 检查样品需求是否可以结束竞价。
    -   **权限**: 仅限买家 (`buyer`)。

## 使用示例

### 买家创建样品需求示例 (POST /api/v1/sample-requirements/create)
```json
{
    "title": "样品采购：定制T恤",
    "description": "需要定制一批纯棉T恤样品，用于质量和工艺评估。",
    "categoryId": 10,
    "expectedCompletionDate": "2024-08-15"
}
```

### 响应示例
```json
{
    "code": 200,
    "message": "样品需求创建成功",
    "data": {
        "id": 201,
        "title": "样品采购：定制T恤",
        "status": "PENDING_REVIEW",
        "buyerId": 101
    }
}
```

## 注意事项
-   **用户ID获取**: 控制器内部通过 `getCurrentUserId()` 方法从 Spring Security Context 中获取当前登录用户的ID，确保操作与用户身份关联。
-   **日志记录**: 使用 `Slf4j` 注解和 `log.info()` 记录关键操作日志，便于追踪和调试。
-   **Swagger 注解**: 使用 `@Api`, `@ApiOperation`, `@ApiParam` 等 Swagger 注解，为 API 接口提供详细的文档说明。
-   **数据校验**: `create` 和 `update` 方法使用了 `@Valid` 注解，对请求体进行数据校验。