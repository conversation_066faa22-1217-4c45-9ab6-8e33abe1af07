# `RequirementCategoryService.java` 说明文档

## 文件概述
`RequirementCategoryService.java` 是需求分类模块的服务层接口。它继承了 MyBatis-Plus 的 `IService<RequirementCategory>` 接口，提供了对需求分类进行业务操作的抽象。该接口定义了分类的查询（包括列表和树形结构）、添加、更新、删除和状态管理等核心业务方法。

## 核心功能

### 1. 继承 `IService`
-   自动拥有 `save`, `removeById`, `updateById`, `getById`, `list` 等基本 CRUD 方法。

### 2. 自定义业务方法
-   **`listCategories()`**: 查询所有需求分类的列表。
-   **`listEnabledCategories()`**: 查询所有启用状态的需求分类列表。
-   **`getCategoryTree()`**: 获取所有需求分类的树形结构。
-   **`getEnabledCategoryTree()`**: 获取所有启用状态的需求分类的树形结构。
-   **`getCategoryById(Long id)`**: 根据分类ID查询单个分类的详细信息。
-   **`addCategory(RequirementCategoryDTO categoryDTO)`**: 添加新的需求分类。
    -   **参数**: `categoryDTO` 包含分类信息。
    -   **返回**: `true` 表示添加成功，`false` 表示失败。
-   **`updateCategory(RequirementCategoryDTO categoryDTO)`**: 更新现有需求分类。
    -   **参数**: `categoryDTO` 包含更新后的分类信息。
    -   **返回**: `true` 表示更新成功，`false` 表示失败。
-   **`deleteCategory(Long id)`**: 根据分类ID删除需求分类。
    -   **返回**: `true` 表示删除成功，`false` 表示失败。
-   **`updateCategoryStatus(Long id, Integer status)`**: 更新需求分类的启用/禁用状态。
    -   **返回**: `true` 表示更新成功，`false` 表示失败。

## 使用示例

### 获取启用状态的分类树示例
```java
// 在 Controller 层调用 Service
List<RequirementCategoryTreeDTO> categoryTree = requirementCategoryService.getEnabledCategoryTree();
// 进一步处理 categoryTree 数据，例如返回给前端
```

### 添加分类示例
```java
// 在 Controller 层构建 DTO 并调用 Service
RequirementCategoryDTO newCategory = new RequirementCategoryDTO();
newCategory.setName("新的分类");
newCategory.setParentId(1L); // 假设父分类ID为1
newCategory.setStatus(1); // 启用
boolean success = requirementCategoryService.addCategory(newCategory);
if (success) {
    System.out.println("分类添加成功！");
} else {
    System.out.println("分类添加失败！");
}
```

## 注意事项
-   **业务逻辑封装**: 服务层负责处理分类相关的业务规则，例如在删除分类时检查是否有子分类或关联的需求。
-   **DTO 与 Entity 转换**: 服务层负责将 DTO 对象转换为实体对象进行持久化操作，并将实体对象转换为 DTO 或 VO 对象返回给控制器层。
-   **事务管理**: 服务层的方法通常会配置事务，确保数据的一致性。
-   **MyBatis-Plus 扩展**: 继承 `IService` 接口，可以利用 MyBatis-Plus 提供的便捷方法进行数据库操作，减少了大量样板代码。