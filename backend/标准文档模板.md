# 标准文档模板

## 📋 模板说明

这是基于已完成的8个高质量文档总结出的标准模板，请严格按照此模板创建新文档。

## 🎯 Controller类文档模板 (目标：500-600行)

```markdown
# ControllerName.java

## 文件概述 (File Overview)
`ControllerName.java` 是[业务模块]的REST控制器，位于 `com.purchase.xxx.controller` 包中。该控制器作为[业务模块]的HTTP接口层，负责处理所有[业务功能]相关的REST API请求。通过集成 `ServiceA`、`ServiceB` 等业务服务，提供了[业务对象]的完整生命周期管理功能，包括[功能1、功能2、功能3、功能4]等。该控制器实现了基于Spring Security的多层权限控制，支持[角色1、角色2、角色3]等不同角色的差异化访问，并提供了完善的[特色功能]管理功能。

## 核心功能 (Core Functionality)
*   **功能管理1**: 提供[具体功能]的完整CRUD操作，支持[特性]
*   **功能管理2**: 支持[具体功能]和[相关功能]，简化[业务流程]
*   **功能管理3**: [管理员/用户]可对[业务对象]进行[操作]，确保[质量/安全性]
*   **权限分级控制**: 基于用户角色的细粒度权限控制，保护[数据]安全
*   **多[类型]支持**: 支持[类型1、类型2]等多种[业务对象]
*   **[状态]管理**: 完整的[业务对象]状态流转，包括[状态1、状态2、状态3]等
*   **安全验证机制**: [业务对象]信息的[加密/验证]和敏感数据脱敏处理
*   **批量操作支持**: 支持[管理员]批量[操作]和状态更新操作
*   **审计日志记录**: 详细的[业务对象]操作审计日志，满足合规要求
*   **数据一致性保障**: 确保[业务对象]数据的一致性和完整性
*   **异常处理机制**: 完善的异常捕获和用户友好的错误提示
*   **统一响应格式**: 使用标准化的API响应格式，便于前端处理

## 接口说明 (Interface Description)

### [业务对象]管理方法

#### create[BusinessObject] - 创建[业务对象]
*   **方法签名**: `Result<BusinessObjectDTO> create[BusinessObject](@Valid @RequestBody Create[BusinessObject]Request request)`
*   **HTTP方法**: POST
*   **路径**: `/api/v1/[business-objects]`
*   **权限**: `@PreAuthorize("hasAnyAuthority('[role1]', '[role2]', 'admin')")`
*   **参数**: `request` (Create[BusinessObject]Request) - 创建[业务对象]的请求对象，包含[字段1、字段2、字段3]等信息
*   **返回值**: `Result<BusinessObjectDTO>` - 包含新创建[业务对象]详细信息的响应对象
*   **业务逻辑**: 
    *   验证请求参数的完整性和有效性
    *   检查[相关对象]的有效性
    *   验证[关联数据]的存在性和状态
    *   [具体业务处理步骤]
    *   创建[业务对象]记录
    *   初始化[业务对象]状态和[相关属性]
    *   发送[业务对象]创建通知
    *   返回完整的[业务对象]详情

#### get[BusinessObject]ById - 获取[业务对象]详情
*   **方法签名**: `Result<BusinessObjectDetailResponse> get[BusinessObject]ById(@PathVariable Long id)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/[business-objects]/{id}`
*   **权限**: `@PreAuthorize("hasAnyAuthority('[role1]', '[role2]', 'admin')")`
*   **参数**: `id` (Long) - [业务对象]ID
*   **返回值**: `Result<BusinessObjectDetailResponse>` - [业务对象]详细信息响应对象
*   **业务逻辑**: 
    *   验证[业务对象]ID的有效性
    *   检查用户是否有权限查看该[业务对象]
    *   查询[业务对象]基本信息和关联数据
    *   获取[相关数据]列表和详细信息
    *   查询[业务对象][状态/历史]记录
    *   组装完整的[业务对象]详情响应

### 查询方法

#### getAll[BusinessObjects] - 分页获取[业务对象]列表
*   **方法签名**: `Result<IPage<BusinessObjectSimpleResponse>> getAll[BusinessObjects](@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "10") Integer size)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/[business-objects]`
*   **权限**: `@PreAuthorize("hasAnyAuthority('[role1]', '[role2]', 'admin')")`
*   **参数**: 
    *   `page` (Integer) - 页码，默认为1
    *   `size` (Integer) - 每页大小，默认为10
*   **返回值**: `Result<IPage<BusinessObjectSimpleResponse>>` - 分页的[业务对象]简要信息列表
*   **业务逻辑**: 
    *   验证分页参数的有效性
    *   根据用户角色过滤可见[业务对象]
    *   执行分页查询
    *   转换为简要响应格式
    *   返回分页结果

## 使用示例 (Usage Examples)

```java
// 1. 前端JavaScript调用示例
const [BusinessObject]API = {
    // 创建[业务对象]
    async create[BusinessObject]([businessObject]Data) {
        const response = await fetch('/api/v1/[business-objects]', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                [field1]: [businessObject]Data.[field1],
                [field2]: [businessObject]Data.[field2],
                [field3]: [businessObject]Data.[field3]
            })
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('[业务对象]创建成功');
            return result.data;
        } else {
            showErrorMessage('创建失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 获取[业务对象]详情
    async get[BusinessObject]ById([businessObject]Id) {
        const response = await fetch(`/api/v1/[business-objects]/${[businessObject]Id}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    }
};

// 2. Java客户端调用示例
@Service
public class [BusinessObject]ClientService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Value("${api.base-url}")
    private String baseUrl;
    
    // 创建[业务对象]
    public [BusinessObject]DTO create[BusinessObject](Create[BusinessObject]Request request, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Create[BusinessObject]Request> entity = new HttpEntity<>(request, headers);

        try {
            ResponseEntity<Result<[BusinessObject]DTO>> response = restTemplate.exchange(
                baseUrl + "/api/v1/[business-objects]",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<Result<[BusinessObject]DTO>>() {}
            );

            Result<[BusinessObject]DTO> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("创建[业务对象]失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用创建[业务对象]API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }
}

// 3. 业务服务集成示例
@Service
public class [BusinessObject]WorkflowService {
    
    @Autowired
    private [BusinessObject]Service [businessObject]Service;
    
    @Autowired
    private NotificationService notificationService;
    
    // [业务对象]创建后的业务流程
    @EventListener
    @Async
    public void handle[BusinessObject]Created([BusinessObject]CreatedEvent event) {
        try {
            [BusinessObject]DTO [businessObject] = event.get[BusinessObject]();
            
            // 1. 发送创建通知
            notificationService.send[BusinessObject]CreatedNotification([businessObject]);
            
            // 2. 初始化相关数据
            initialize[RelatedData]([businessObject].getId());
            
            // 3. 记录业务日志
            auditLogService.log[BusinessObject]Creation([businessObject]);
            
            log.info("[业务对象]创建后处理完成: [businessObject]Id={}", [businessObject].getId());
            
        } catch (Exception e) {
            log.error("[业务对象]创建后处理失败: [businessObject]Id={}", event.get[BusinessObject]().getId(), e);
        }
    }
}

// 4. 定时任务示例
@Component
public class [BusinessObject]ScheduledTasks {
    
    @Autowired
    private [BusinessObject]Service [businessObject]Service;
    
    // 检查[特定状态][业务对象]
    @Scheduled(cron = "0 0 */6 * * ?") // 每6小时执行一次
    public void check[SpecificStatus][BusinessObjects]() {
        log.info("开始检查[特定状态][业务对象]");
        
        try {
            List<[BusinessObject]DTO> [specificStatus][BusinessObjects] = [businessObject]Service.find[SpecificStatus][BusinessObjects]();
            
            for ([BusinessObject]DTO [businessObject] : [specificStatus][BusinessObjects]) {
                handle[SpecificStatus][BusinessObject]([businessObject]);
            }
            
            log.info("[特定状态][业务对象]检查完成，处理数量: {}", [specificStatus][BusinessObjects].size());
            
        } catch (Exception e) {
            log.error("检查[特定状态][业务对象]失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class [BusinessObject]ControllerTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @MockBean
    private [BusinessObject]Service [businessObject]Service;
    
    @Test
    void testCreate[BusinessObject]() {
        // 准备测试数据
        Create[BusinessObject]Request request = new Create[BusinessObject]Request();
        request.set[Field1]([value1]);
        request.set[Field2]([value2]);
        
        [BusinessObject]DTO expected[BusinessObject] = new [BusinessObject]DTO();
        expected[BusinessObject].setId(1L);
        expected[BusinessObject].set[Field1]([value1]);
        
        // Mock服务调用
        when([businessObject]Service.create[BusinessObject](any(Create[BusinessObject]Request.class)))
            .thenReturn(expected[BusinessObject]);
        
        // 执行测试
        HttpEntity<Create[BusinessObject]Request> entity = new HttpEntity<>(request);
        ResponseEntity<Result> response = restTemplate.postForEntity(
            "/api/v1/[business-objects]", entity, Result.class);
        
        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
        
        // 验证服务调用
        verify([businessObject]Service, times(1)).create[BusinessObject](any(Create[BusinessObject]Request.class));
    }
}
```

## 注意事项 (Notes)
*   **权限控制**: 使用Spring Security的@PreAuthorize注解进行权限验证，确保只有相应角色的用户才能访问对应功能
*   **数据安全**: [业务对象]信息涉及[敏感数据类型]，需要严格的加密存储和传输保护
*   **[特定流程]**: 所有[业务对象]都需要经过[特定流程]，确保[业务对象]信息的[质量要求]
*   **参数验证**: 使用@Valid注解对请求参数进行验证，确保数据的完整性和有效性
*   **统一响应**: 使用[ResponseType]作为统一的响应格式，确保前端能够统一处理响应数据
*   **[特色功能]**: [用户]可以[操作]，[特殊处理]，但需要确保[约束条件]
*   **[业务规则]**: 支持多种[类型]（[类型1、类型2]等），需要根据类型进行不同的[处理]
*   **状态管理**: [业务对象]状态包括[状态1、状态2、状态3]等，需要严格控制状态流转
*   **数据脱敏**: 在返回[业务对象]信息时需要对敏感信息进行脱敏处理，如[敏感字段]部分隐藏
*   **并发控制**: [业务对象]操作需要考虑并发控制，避免数据不一致问题
*   **审计日志**: 重要的[业务对象]操作需要记录详细的审计日志，满足合规要求
*   **异常处理**: 完善的异常处理机制，确保在出现错误时返回友好的错误信息
*   **业务委托**: 控制器只负责HTTP请求处理和响应封装，具体业务逻辑委托给[业务服务]处理
*   **缓存策略**: [业务对象][特定数据]等频繁查询的数据可以考虑使用缓存提高性能
*   **国际化**: [业务对象][相关内容]需要支持多语言，便于国际化部署
*   **[技术特性]**: 使用完整的[技术]注解为API文档提供清晰的接口说明
```

## 🎯 Service类文档模板 (目标：600-700行)

Service类文档模板结构与Controller类似，但重点在于：
1. **业务逻辑实现**: 详细的业务规则和处理流程
2. **事务管理**: @Transactional的使用和事务边界
3. **数据访问**: 与Repository/Mapper的交互
4. **异常处理**: 业务异常的定义和处理
5. **性能优化**: 缓存、批处理等优化策略

## 🎯 Entity/Domain类文档模板 (目标：400-500行)

Entity类文档重点在于：
1. **领域模型**: DDD领域模型的设计理念
2. **属性说明**: 每个属性的业务含义和约束
3. **关系映射**: 与其他实体的关系
4. **业务规则**: 实体内的业务规则和验证
5. **生命周期**: 实体的创建、更新、删除规则

## 🎯 Mapper接口文档模板 (目标：300-400行)

Mapper接口文档重点在于：
1. **数据访问**: SQL查询的设计和优化
2. **参数映射**: MyBatis参数映射规则
3. **结果映射**: 查询结果的映射关系
4. **性能考虑**: 索引使用和查询优化
5. **事务支持**: 数据访问的事务特性

## 📝 使用说明

1. **选择合适的模板**: 根据文件类型选择对应模板
2. **替换占位符**: 将模板中的占位符替换为实际内容
3. **保持结构一致**: 严格按照模板结构组织内容
4. **确保内容完整**: 每个部分都要详细填写
5. **检查质量标准**: 确保达到目标行数和质量要求
