# 佣金系统 API 接口文档

## 概述

佣金系统提供完整的邀请佣金管理功能，包括佣金记录查询、优惠券管理、月度处理等核心功能。系统采用DDD架构设计，提供RESTful API接口。

**基础信息：**
- 基础URL: `/api/v1`
- 认证方式: JWT Token
- 响应格式: JSON
- 字符编码: UTF-8

## 通用响应格式

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2025-07-06T21:00:00"
}
```

## 1. 佣金记录管理 API

### 1.1 根据ID查询佣金记录

**接口地址：** `GET /api/v1/commission/records/{id}`

**请求参数：**
- `id` (Path参数): 佣金记录ID，必填，正整数

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "inviterId": 100,
    "inviterName": "张三",
    "inviteeId": 200,
    "inviteeName": "李四",
    "orderId": 1001,
    "orderType": "UNIFIED",
    "orderTypeDescription": "统一订单",
    "orderAmount": 1000.00,
    "commissionRate": 1.2,
    "commissionAmount": 12.00,
    "status": "PENDING",
    "statusDescription": "待确认",
    "monthKey": "2025-07",
    "buyerRoleVerified": true,
    "createdAt": "2025-07-06 21:00:00",
    "updatedAt": "2025-07-06 21:00:00",
    "deleted": false
  }
}
```

### 1.2 根据邀请者ID查询佣金记录（分页）

**接口地址：** `GET /api/v1/commission/records/inviter/{inviterId}`

**请求参数：**
- `inviterId` (Path参数): 邀请者ID，必填，正整数
- `page` (Query参数): 页码，默认1
- `size` (Query参数): 页大小，默认20

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

**响应示例：**
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "id": 1,
        "inviterId": 100,
        "orderId": 1001,
        "commissionAmount": 12.00,
        "status": "PENDING",
        "monthKey": "2025-07",
        "createdAt": "2025-07-06 21:00:00"
      }
    ],
    "page": 1,
    "size": 20,
    "totalElements": 50,
    "totalPages": 3,
    "first": true,
    "last": false
  }
}
```

### 1.3 根据邀请者ID和月份查询佣金记录

**接口地址：** `GET /api/v1/commission/records/inviter/{inviterId}/month/{monthKey}`

**请求参数：**
- `inviterId` (Path参数): 邀请者ID，必填，正整数
- `monthKey` (Path参数): 月份键，格式YYYY-MM，如"2025-07"

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

### 1.4 取消佣金记录

**接口地址：** `POST /api/v1/commission/records/{id}/cancel`

**请求参数：**
- `id` (Path参数): 佣金记录ID，必填，正整数

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

**响应示例：**
```json
{
  "success": true,
  "message": "佣金记录取消成功"
}
```

### 1.5 批量取消佣金记录

**接口地址：** `POST /api/v1/commission/records/batch/cancel`

**请求体：**
```json
[1, 2, 3, 4, 5]
```

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

### 1.6 获取佣金统计信息

**接口地址：** `GET /api/v1/commission/records/summary/{inviterId}`

**请求参数：**
- `inviterId` (Path参数): 邀请者ID，必填，正整数

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

**响应示例：**
```json
{
  "success": true,
  "data": {
    "totalCommission": 1200.50,
    "currentMonthCommission": 150.00,
    "pendingCommission": 80.00,
    "confirmedCommission": 1120.50,
    "paidCommission": 0.00,
    "totalCount": 45,
    "pendingCount": 5,
    "confirmedCount": 40,
    "currentMonthAmount": 150.00
  }
}
```

### 1.7 获取月度佣金报表

**接口地址：** `GET /api/v1/commission/records/monthly-report/{inviterId}/{monthKey}`

**请求参数：**
- `inviterId` (Path参数): 邀请者ID，必填，正整数
- `monthKey` (Path参数): 月份键，格式YYYY-MM

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

### 1.8 获取佣金趋势数据

**接口地址：** `GET /api/v1/commission/records/trend/{inviterId}`

**请求参数：**
- `inviterId` (Path参数): 邀请者ID，必填，正整数
- `months` (Query参数): 月份数量，默认12

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

### 1.9 获取佣金阶梯配置

**接口地址：** `GET /api/v1/commission/records/config/tiers`

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "tier": 1,
      "minAmount": 0,
      "maxAmount": 1000,
      "rate": 1.0,
      "description": "基础阶梯"
    },
    {
      "tier": 2,
      "minAmount": 1000,
      "maxAmount": 5000,
      "rate": 1.2,
      "description": "进阶阶梯"
    }
  ]
}
```

### 1.10 获取月度奖金配置

**接口地址：** `GET /api/v1/commission/records/config/bonus`

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

## 2. 邀请人优惠券管理 API

### 2.1 根据ID查询优惠券

**接口地址：** `GET /api/commission/coupons/{id}`

**请求参数：**
- `id` (Path参数): 优惠券ID，必填，正整数

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "inviterId": 100,
    "inviteeId": 200,
    "triggerOrderId": 1001,
    "couponCode": "INVITE2025070001",
    "couponType": "FIXED_AMOUNT",
    "couponTypeDescription": "固定金额",
    "discountValue": 50.00,
    "minimumAmount": 200.00,
    "maximumDiscount": null,
    "status": "ACTIVE",
    "statusDescription": "可用",
    "monthKey": "2025-07",
    "validFrom": "2025-07-06T21:00:00",
    "validUntil": "2025-08-06T21:00:00",
    "usedAt": null,
    "usedOrderId": null,
    "createdAt": "2025-07-06T21:00:00",
    "updatedAt": "2025-07-06T21:00:00",
    "valueDescription": "满200减50",
    "usable": true,
    "expiringSoon": false
  }
}
```

### 2.2 根据优惠券代码查询

**接口地址：** `GET /api/commission/coupons/code/{couponCode}`

**请求参数：**
- `couponCode` (Path参数): 优惠券代码，必填

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

### 2.3 根据邀请人ID查询优惠券列表

**接口地址：** `GET /api/commission/coupons/inviter/{inviterId}`

**请求参数：**
- `inviterId` (Path参数): 邀请人ID，必填，正整数
- `page` (Query参数): 页码，默认1
- `size` (Query参数): 页大小，默认20

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

### 2.4 查询有效优惠券

**接口地址：** `GET /api/commission/coupons/inviter/{inviterId}/active`

**请求参数：**
- `inviterId` (Path参数): 邀请人ID，必填，正整数

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

### 2.5 验证优惠券使用条件

**接口地址：** `POST /api/commission/coupons/{id}/validate`

**请求参数：**
- `id` (Path参数): 优惠券ID，必填，正整数
- `orderAmount` (Query参数): 订单金额，必填
- `userId` (Query参数): 用户ID，必填，正整数

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

**响应示例：**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "message": "优惠券可以使用",
    "discountAmount": 50.00
  }
}
```

### 2.6 使用优惠券

**接口地址：** `POST /api/commission/coupons/{id}/use`

**请求参数：**
- `id` (Path参数): 优惠券ID，必填，正整数
- `orderId` (Query参数): 使用订单ID，必填，正整数

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

### 2.7 取消优惠券

**接口地址：** `POST /api/commission/coupons/{id}/cancel`

**请求参数：**
- `id` (Path参数): 优惠券ID，必填，正整数

**权限要求：** `buyer`, `seller`, `forwarder`, `admin`

## 3. 月度处理管理 API

### 3.1 手动触发月度处理

**接口地址：** `POST /api/v1/commission/monthly/process/{monthKey}`

**请求参数：**
- `monthKey` (Path参数): 月份键，格式YYYY-MM

**权限要求：** `admin`

**响应示例：**
```json
{
  "success": true,
  "message": "月度佣金处理已启动: 2025-07"
}
```

### 3.2 获取处理状态

**接口地址：** `GET /api/v1/commission/monthly/status/{monthKey}`

**请求参数：**
- `monthKey` (Path参数): 月份键，格式YYYY-MM

**权限要求：** `admin`, `seller`, `forwarder`

**响应示例：**
```json
{
  "success": true,
  "data": {
    "monthKey": "2025-07",
    "status": "COMPLETED",
    "statusDescription": "已完成",
    "totalRecords": 100,
    "successCount": 98,
    "failureCount": 2,
    "errorMessage": null,
    "startedAt": "2025-07-06T21:00:00",
    "completedAt": "2025-07-06T21:05:00",
    "processingTimeMs": 300000,
    "successRate": 98.0
  }
}
```

### 3.3 获取最近的处理日志

**接口地址：** `GET /api/v1/commission/monthly/logs`

**请求参数：**
- `limit` (Query参数): 限制数量，默认10

**权限要求：** `admin`, `seller`, `forwarder`

### 3.4 手动数据校验

**接口地址：** `POST /api/v1/commission/monthly/validate/{monthKey}`

**请求参数：**
- `monthKey` (Path参数): 月份键，格式YYYY-MM

**权限要求：** `admin`

### 3.5 获取最近几个月的校验状态

**接口地址：** `GET /api/v1/commission/monthly/validation/recent`

**请求参数：**
- `months` (Query参数): 月份数量，默认3

**权限要求：** `admin`, `seller`, `forwarder`

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 未授权访问 | 检查JWT Token是否有效 |
| 403 | 权限不足 | 检查用户角色权限 |
| 404 | 资源不存在 | 检查请求的资源ID是否正确 |
| 500 | 服务器内部错误 | 联系系统管理员 |

## 数据模型

### CommissionRecord（佣金记录）

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Long | 佣金记录ID |
| inviterId | Long | 邀请者ID |
| inviteeId | Long | 被邀请者ID |
| orderId | Long | 订单ID |
| orderType | String | 订单类型 |
| orderAmount | BigDecimal | 订单金额 |
| commissionRate | BigDecimal | 佣金比例 |
| commissionAmount | BigDecimal | 佣金金额 |
| status | String | 状态 |
| monthKey | String | 月份键 |
| buyerRoleVerified | Boolean | 买家角色验证 |
| createdAt | LocalDateTime | 创建时间 |
| updatedAt | LocalDateTime | 更新时间 |

### InviterCoupon（邀请人优惠券）

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Long | 优惠券ID |
| inviterId | Long | 邀请人ID |
| inviteeId | Long | 被邀请者ID |
| triggerOrderId | Long | 触发订单ID |
| couponCode | String | 优惠券代码 |
| couponType | String | 优惠券类型 |
| discountValue | BigDecimal | 优惠金额 |
| minimumAmount | BigDecimal | 最低使用金额 |
| status | String | 状态 |
| validFrom | LocalDateTime | 有效期开始 |
| validUntil | LocalDateTime | 有效期结束 |
| usedAt | LocalDateTime | 使用时间 |
| usedOrderId | Long | 使用订单ID |

## 业务规则

1. **佣金计算规则**：
   - 基于订单金额和佣金阶梯配置计算
   - 支持多级阶梯佣金率
   - 月度奖金根据月度业绩计算

2. **优惠券生成规则**：
   - 邀请人下单后自动生成优惠券
   - 优惠券有效期为30天
   - 支持固定金额和百分比折扣

3. **月度处理规则**：
   - 每月1号自动触发月度处理
   - 支持手动触发和重新处理
   - 处理过程包括数据校验和状态更新

## 使用示例

### 查询用户佣金统计

```bash
curl -X GET "http://localhost:8080/api/v1/commission/records/summary/100" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 使用优惠券

```bash
curl -X POST "http://localhost:8080/api/commission/coupons/1/use?orderId=1001" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 触发月度处理

```bash
curl -X POST "http://localhost:8080/api/v1/commission/monthly/process/2025-07" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

---

**文档版本：** v1.0  
**最后更新：** 2025-07-06  
**维护人员：** 系统开发团队
