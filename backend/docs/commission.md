## commission 模块文档

### 1. 模块概述
佣金计算与管理模块，负责处理交易佣金的计算、结算和统计功能

### 2. 核心类说明
#### Controller类
- **CommissionController**:
  - 功能: 提供佣金管理的REST API接口
  - 主要接口:
    - `GET /commissions`: 查询佣金记录
    - `POST /commissions/calculate`: 计算交易佣金
    - `PUT /commissions/{id}`: 更新佣金记录
    - `GET /commissions/summary`: 获取佣金汇总统计

#### Service类
- **CommissionService**:
  - 核心方法:
    - `calculateCommission`: 根据交易金额计算佣金
    - `settleCommissions`: 执行佣金结算
    - `getCommissionSummary`: 获取佣金汇总数据
    - `updateCommissionStatus`: 更新佣金状态

#### Repository类
- **CommissionRepository**:
  - 核心查询方法:
    - `findByOrderId`: 按订单ID查询
    - `findByStatus`: 按状态查询
    - `sumAmountByStatus`: 按状态汇总金额

#### Entity类
- **CommissionRecord**:
  - 主要字段说明:
    - `id`: 记录ID
    - `orderId`: 关联订单ID  
    - `amount`: 佣金金额
    - `status`: 结算状态(UNPAID/PAID/CANCELLED)
    - `createTime`: 创建时间
    - `updateTime`: 更新时间

### 3. 使用示例
```java
// 计算佣金示例
CommissionRequest request = new CommissionRequest();
request.setOrderId(12345L);
request.setTransactionAmount(new BigDecimal("1000.00"));
CommissionResult result = commissionService.calculateCommission(request);

// 结算佣金示例  
commissionService.settleCommissions(LocalDate.now());

// 查询佣金汇总
CommissionSummary summary = commissionService.getCommissionSummary(
    LocalDate.now().minusMonths(1), 
    LocalDate.now()
);
```

### 4. 业务规则
- 佣金率配置在application.yml中:
  ```yaml
  commission:
    rate: 0.05 # 5%佣金率
    min-amount: 10.00 # 最低佣金金额
  ```
- 支持按交易金额阶梯计算佣金
- 结算周期为T+1

### 5. 注意事项
- 佣金计算后需人工审核确认
- 结算前需校验订单状态
- 支持佣金冲正操作
- 提供对账报表功能