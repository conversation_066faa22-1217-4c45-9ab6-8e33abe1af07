# 管理员账户管理指南

## 🔍 当前系统分析

### 密码加密机制
- 使用 `BCryptPasswordEncoder` 进行密码加密
- 每次加密结果都不同，但验证时会正确匹配
- 密码存储在 `user` 表的 `password` 字段中

### 管理员创建限制
- 管理员角色不能通过注册接口创建
- 需要直接在数据库中创建或通过特殊方法创建

## 🛠️ 修改管理员密码的方法

### 方法一：使用 Java 工具类生成密码（推荐）

1. **运行密码生成工具**：
   ```bash
   cd backend
   mvn compile exec:java -Dexec.mainClass="com.purchase.util.PasswordGenerator"
   ```

2. **工具会输出**：
   - 原始密码
   - 加密后的密码
   - 可直接执行的 SQL 语句

### 方法二：直接数据库操作

#### 查看现有管理员
```sql
-- 查看所有管理员账户
SELECT id, username, email, role, status, created_at 
FROM user 
WHERE role = 'admin' AND deleted = '0';
```

#### 修改现有管理员密码
```sql
-- 注意：这里的加密密码需要通过 Java 工具生成
-- 示例：将 admin123 用户的密码改为 "newpassword123"
UPDATE user 
SET password = '$2a$10$example.encrypted.password.hash.here', 
    updated_at = NOW() 
WHERE username = 'admin123' AND role = 'admin';
```

#### 创建新管理员账户
```sql
-- 创建新的管理员账户
INSERT INTO user (
    username, 
    password, 
    email, 
    role, 
    status, 
    invite_code, 
    child_count, 
    grandchild_count, 
    created_at, 
    updated_at,
    deleted
) VALUES (
    'admin',                                    -- 用户名
    '$2a$10$example.encrypted.password.here',  -- 加密密码（需要用工具生成）
    '<EMAIL>',                        -- 邮箱
    'admin',                                    -- 角色
    1,                                          -- 状态（1=启用）
    'ADMIN001',                                 -- 邀请码
    0,                                          -- 直接邀请数
    0,                                          -- 间接邀请数
    NOW(),                                      -- 创建时间
    NOW(),                                      -- 更新时间
    '0'                                         -- 删除标记
);
```

## 🔧 具体操作步骤

### 步骤1：生成加密密码

1. **编译并运行密码生成工具**：
   ```bash
   cd backend
   mvn compile
   java -cp target/classes com.purchase.util.PasswordGenerator
   ```

2. **修改工具中的密码**：
   - 编辑 `PasswordGenerator.java` 文件
   - 修改 `newPassword` 变量为您想要的密码
   - 重新运行工具

### 步骤2：执行数据库操作

1. **连接到 MySQL 数据库**：
   ```bash
   mysql -u root -p purchase_system
   ```

2. **执行生成的 SQL 语句**：
   - 复制工具输出的 SQL 语句
   - 在 MySQL 命令行中执行

### 步骤3：验证修改结果

1. **查询管理员账户**：
   ```sql
   SELECT username, email, role, status FROM user WHERE role = 'admin';
   ```

2. **测试登录**：
   - 使用新密码尝试登录管理员后台
   - 确认登录成功

## 🔐 安全建议

### 密码要求
- 至少8位字符
- 包含大小写字母、数字和特殊字符
- 避免使用常见密码

### 账户安全
- 定期更换管理员密码
- 限制管理员账户数量
- 记录管理员操作日志

### 示例强密码
```
Admin@2024!Secure
Manager#2024$Safe
SuperAdmin@2024#
```

## 🚨 注意事项

1. **备份数据库**：修改前请备份数据库
2. **测试环境**：先在测试环境验证
3. **密码复杂度**：确保密码足够复杂
4. **权限控制**：确认管理员权限设置正确

## 📝 常见问题

### Q: 忘记了管理员密码怎么办？
A: 使用上述方法重新生成加密密码并更新数据库

### Q: 可以有多个管理员吗？
A: 可以，但建议限制数量并做好权限管理

### Q: 如何禁用管理员账户？
A: 将 status 字段设为 0：
```sql
UPDATE user SET status = 0 WHERE username = 'admin_username';
```

### Q: 如何删除管理员账户？
A: 设置删除标记：
```sql
UPDATE user SET deleted = '1' WHERE username = 'admin_username';
```

## 🔄 完整示例

假设要创建用户名为 `admin`，密码为 `Admin@2024!` 的管理员：

1. **修改 PasswordGenerator.java**：
   ```java
   String newPassword = "Admin@2024!";
   ```

2. **运行工具获取加密密码**

3. **执行 SQL**：
   ```sql
   INSERT INTO user (username, password, email, role, status, invite_code, child_count, grandchild_count, created_at, updated_at, deleted) 
   VALUES ('admin', '生成的加密密码', '<EMAIL>', 'admin', 1, 'ADMIN001', 0, 0, NOW(), NOW(), '0');
   ```

4. **验证登录**：使用 `admin` / `Admin@2024!` 登录系统
