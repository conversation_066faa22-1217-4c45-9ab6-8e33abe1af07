# 佣金系统数据流程详解：从记录生成到月度结算

## 📋 概述

本文档详细描述了佣金系统从订单完成触发佣金记录生成，到每月初进行结算的完整数据流程。通过流程图和详细说明，展现数据在各个表之间的流向和状态变化。

## 🎯 核心数据表概览

### 📊 **主要数据表**
- **`commission_record`** - 佣金记录明细表（核心业务数据）
- **`monthly_commission_summary`** - 月度汇总表（聚合数据 + 结算状态管理）
- **`commission_config`** - 配置表（佣金规则配置）
- **`inviter_coupon`** - 优惠券表（买家邀请人奖励）
- **`commission_processing_log`** - 处理日志表（月度处理记录）

## 🔄 完整数据流程图

### 1. 总体数据流向概览

```mermaid
graph TD
    A[订单完成事件] --> B{邀请人角色判断}
    B -->|卖家/货代| C[佣金计算流程]
    B -->|买家| D[优惠券生成流程<br/>暂时注释]
    
    C --> E[commission_record<br/>创建佣金记录]
    E --> F[monthly_commission_summary<br/>更新月度汇总]
    
    D --> G[inviter_coupon<br/>创建优惠券记录]
    
    H[月初定时任务] --> I[commission_processing_log<br/>记录处理开始]
    I --> J[批量处理月度汇总]
    J --> K[更新结算状态]
    K --> L[标记佣金记录已支付]
    
    M[commission_config<br/>配置数据] --> C
    M --> F
    
    style E fill:#e8f5e8
    style F fill:#f3e5f5
    style G fill:#fce4ec
    style I fill:#f1f8e9
    style M fill:#fff3e0
```

### 2. 佣金记录生成详细流程

```mermaid
sequenceDiagram
    participant Event as 订单完成事件
    participant Service as CommissionApplicationService
    participant Config as commission_config
    participant Record as commission_record
    participant Summary as monthly_commission_summary
    participant Log as 系统日志
    
    Event->>Service: 订单完成通知
    Service->>Service: 验证订单有效性
    Service->>Service: 查找邀请关系
    Service->>Service: 检查邀请人角色
    
    alt 邀请人是卖家/货代
        Service->>Config: 查询佣金配置
        Config-->>Service: 返回佣金比例配置
        Service->>Service: 计算佣金金额
        Service->>Record: 创建佣金记录
        Record-->>Service: 返回记录ID
        Service->>Summary: 更新月度汇总
        Summary-->>Service: 更新成功
        Service->>Log: 记录成功日志
    else 邀请人是买家
        Service->>Log: 记录跳过日志（功能未开放）
    end
```

## 📊 详细数据流向分析

### 阶段1：订单完成 → 佣金记录创建

#### **数据流向**：
```
OrderCompletedEvent
    ↓ (事件监听)
CommissionApplicationService.handleOrderCompleted()
    ↓ (查询配置)
commission_config 表 (读取)
    ↓ (计算佣金)
commission_record 表 (写入)
    ↓ (触发汇总更新)
monthly_commission_summary 表 (写入/更新)
```

#### **具体操作步骤**：

1. **订单完成事件触发**
   ```java
   // 事件数据
   OrderCompletedEvent {
       orderId: 12345,
       buyerId: 1001,
       orderAmount: $1000,
       orderType: "purchase",
       completedAt: "2024-03-15"
   }
   ```

2. **查询佣金配置**
   ```sql
   -- 从 commission_config 表读取
   SELECT rate_value FROM commission_config 
   WHERE config_type = 'commission_rate' 
   AND min_amount <= 1000 
   AND (max_amount IS NULL OR max_amount > 1000)
   AND status = 1
   ORDER BY sort_order LIMIT 1;
   ```

3. **创建佣金记录**
   ```sql
   -- 向 commission_record 表写入
   INSERT INTO commission_record (
       inviter_id, invitee_id, order_id, order_type,
       order_amount, commission_rate, commission_amount,
       status, month_key, buyer_role_verified,
       created_at, updated_at
   ) VALUES (
       2001, 1001, 12345, 'purchase',
       1000.00, 0.0120, 12.00,
       'PENDING', '2024-03', TRUE,
       NOW(), NOW()
   );
   ```

4. **更新月度汇总**
   ```sql
   -- 更新 monthly_commission_summary 表
   INSERT INTO monthly_commission_summary (
       inviter_id, month_key, order_count, total_order_amount,
       commission_rate, commission_amount, bonus_amount,
       total_amount, status, created_at, updated_at
   ) VALUES (
       2001, '2024-03', 1, 1000.00,
       0.0120, 12.00, 0.00,
       12.00, 'PENDING', NOW(), NOW()
   )
   ON DUPLICATE KEY UPDATE
       order_count = order_count + 1,
       total_order_amount = total_order_amount + 1000.00,
       commission_amount = commission_amount + 12.00,
       total_amount = commission_amount + bonus_amount,
       updated_at = NOW();
   ```

### 阶段2：月度汇总数据实时更新

#### **触发条件**：每次新增或修改佣金记录时

#### **数据流向**：
```
commission_record 表变更
    ↓ (触发重新计算)
CommissionApplicationService.updateMonthlySummary()
    ↓ (聚合计算)
commission_config 表 (读取最新配置)
    ↓ (更新汇总)
monthly_commission_summary 表 (更新)
```

#### **重新计算逻辑**：
```sql
-- 1. 聚合该月所有有效佣金记录
SELECT 
    COUNT(*) as order_count,
    SUM(order_amount) as total_order_amount,
    SUM(commission_amount) as total_commission
FROM commission_record 
WHERE inviter_id = 2001 
  AND month_key = '2024-03' 
  AND status IN ('PENDING', 'CONFIRMED')
  AND deleted = FALSE;

-- 2. 根据累计金额重新计算佣金比例
SELECT rate_value FROM commission_config 
WHERE config_type = 'commission_rate' 
  AND min_amount <= 15000  -- 假设累计金额
  AND status = 1
ORDER BY sort_order DESC LIMIT 1;

-- 3. 计算月度奖金
SELECT bonus_amount FROM commission_config 
WHERE config_type = 'monthly_bonus' 
  AND min_amount <= 15000
  AND status = 1
ORDER BY sort_order DESC LIMIT 1;

-- 4. 更新月度汇总
UPDATE monthly_commission_summary 
SET order_count = 5,
    total_order_amount = 15000.00,
    commission_rate = 0.0120,
    commission_amount = 180.00,
    bonus_amount = 50.00,
    total_amount = 230.00,
    updated_at = NOW()
WHERE inviter_id = 2001 AND month_key = '2024-03';
```

### 阶段3：月初结算处理

#### **触发时机**：每月1号定时任务

#### **数据流向**：
```
定时任务启动
    ↓ (记录处理开始)
commission_processing_log 表 (写入)
    ↓ (查找待结算汇总)
monthly_commission_summary 表 (读取 status='PENDING')
    ↓ (开始结算)
monthly_commission_summary 表 (更新 status='PROCESSING')
    ↓ (查找相关佣金记录)
commission_record 表 (读取)
    ↓ (标记已支付)
commission_record 表 (更新 status='PAID')
    ↓ (完成结算)
monthly_commission_summary 表 (更新 status='COMPLETED')
    ↓ (记录处理完成)
commission_processing_log 表 (更新)
```

#### **详细结算步骤**：

1. **记录处理开始**
   ```sql
   -- 向 commission_processing_log 表写入
   INSERT INTO commission_processing_log (
       month_key, processing_date, status, 
       start_time, created_at
   ) VALUES (
       '2024-02', '2024-03-01', 'PROCESSING',
       NOW(), NOW()
   );
   ```

2. **查找待结算汇总**
   ```sql
   -- 从 monthly_commission_summary 表查询
   SELECT id, inviter_id, month_key, total_amount
   FROM monthly_commission_summary 
   WHERE month_key = '2024-02' 
     AND status = 'PENDING'
     AND total_amount > 0;
   ```

3. **开始结算处理**
   ```sql
   -- 更新 monthly_commission_summary 状态
   UPDATE monthly_commission_summary 
   SET status = 'PROCESSING',
       updated_at = NOW()
   WHERE id IN (1, 2, 3, ...);
   ```

4. **查找相关佣金记录**
   ```sql
   -- 从 commission_record 表查询
   SELECT id FROM commission_record 
   WHERE inviter_id = 2001 
     AND month_key = '2024-02'
     AND status = 'CONFIRMED'
     AND deleted = FALSE;
   ```

5. **标记佣金记录已支付**
   ```sql
   -- 更新 commission_record 状态
   UPDATE commission_record 
   SET status = 'PAID',
       processed_at = NOW(),
       updated_at = NOW()
   WHERE id IN (101, 102, 103, ...);
   ```

6. **完成结算**
   ```sql
   -- 更新 monthly_commission_summary 状态
   UPDATE monthly_commission_summary 
   SET status = 'COMPLETED',
       settled_at = NOW(),
       updated_at = NOW()
   WHERE inviter_id = 2001 AND month_key = '2024-02';
   ```

7. **记录处理完成**
   ```sql
   -- 更新 commission_processing_log
   UPDATE commission_processing_log 
   SET status = 'COMPLETED',
       end_time = NOW(),
       total_records = 150,
       success_count = 148,
       failure_count = 2,
       updated_at = NOW()
   WHERE month_key = '2024-02';

## 🔍 **重要澄清：月初不是"佣金计算"，而是"佣金结算"**

### 📊 **两个不同的概念**

1. **佣金计算** - 订单完成时实时进行
2. **佣金结算** - 月初进行，处理上个月的佣金支付

## 🎯 **月初佣金结算的完整数据流向**

### **阶段1：数据获取（从哪个表读取）**

```java
// 1. 从 commission_record 表获取待处理记录
List<CommissionRecord> pendingRecords = commissionRecordRepository
    .findByMonthKeyAndStatusAndDeleted(monthKey, CommissionRecord.Status.PENDING, false);
```

**数据来源**：
- **表名**：`commission_record`
- **查询条件**：
  - `month_key = '2024-02'` （上个月）
  - `status = 'PENDING'` （待处理状态）
  - `deleted = false` （未删除）

### **阶段2：数据处理和计算**

```java
// 2. 按邀请者分组
Map<Long, List<CommissionRecord>> groupedRecords = pendingRecords.stream()
    .collect(Collectors.groupingBy(CommissionRecord::getInviterId));

// 3. 批量处理每个邀请者的佣金
ProcessingResult result = batchService.batchProcessCommissions(groupedRecords, monthKey);
```

**处理逻辑**：
- 从 `commission_config` 表读取最新的佣金配置
- 重新计算月度汇总数据
- 确定最终的佣金比例和月度奖金

### **阶段3：数据存储（存放到哪里）**

#### **3.1 更新佣金记录状态**
```java
// 更新 commission_record 表
commissionRecordRepository.batchUpdateStatusToConfirmed(recordIds, LocalDateTime.now());
```
- **表名**：`commission_record`
- **操作**：UPDATE
- **变更**：`status: PENDING → CONFIRMED`

#### **3.2 更新/创建月度汇总**
```java
// 更新 monthly_commission_summary 表
monthlySummaryRepository.save(summary);
```
- **表名**：`monthly_commission_summary`
- **操作**：INSERT/UPDATE
- **内容**：最终的月度汇总数据

#### **3.3 记录处理日志**
```java
// 更新 commission_processing_log 表
processingLogRepository.save(processingLog);
```
- **表名**：`commission_processing_log`
- **操作**：INSERT/UPDATE
- **内容**：处理结果和统计信息

## 📋 **详细的数据流向图**

```mermaid
graph TD
    A[月初定时任务<br/>每月1-3号凌晨2点] --> B[commission_processing_log<br/>INSERT 开始处理日志]
    B --> C[commission_record<br/>SELECT 待处理记录<br/>WHERE status='PENDING']
    C --> D[按邀请者分组处理]
    D --> E[commission_config<br/>SELECT 最新配置]
    E --> F[重新计算月度数据]
    F --> G[commission_record<br/>UPDATE status='CONFIRMED']
    G --> H[monthly_commission_summary<br/>INSERT/UPDATE 汇总数据]
    H --> I[commission_processing_log<br/>UPDATE 处理结果]

    style C fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    style E fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style G fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style H fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style I fill:#f1f8e9,stroke:#689f38,stroke-width:2px
```

## 🔄 **具体的SQL操作示例**

### **1. 查询待处理记录**
```sql
-- 从 commission_record 表读取
SELECT * FROM commission_record
WHERE month_key = '2024-02'
  AND status = 'PENDING'
  AND deleted = FALSE;
```

### **2. 查询配置信息**
```sql
-- 从 commission_config 表读取
SELECT * FROM commission_config
WHERE config_type IN ('commission_rate', 'monthly_bonus')
  AND enabled = TRUE;
```

### **3. 更新佣金记录状态**
```sql
-- 更新 commission_record 表
UPDATE commission_record
SET status = 'CONFIRMED',
    processed_at = NOW(),
    updated_at = NOW()
WHERE id IN (101, 102, 103, ...);
```

### **4. 更新月度汇总**
```sql
-- 更新 monthly_commission_summary 表
INSERT INTO monthly_commission_summary (
    inviter_id, month_key, total_order_amount,
    commission_rate, commission_amount, monthly_bonus,
    total_amount, order_count, status, created_at, updated_at
) VALUES (
    2001, '2024-02', 25000.00,
    0.0120, 300.00, 100.00,
    400.00, 8, 'PENDING', NOW(), NOW()
)
ON DUPLICATE KEY UPDATE
    total_order_amount = VALUES(total_order_amount),
    commission_amount = VALUES(commission_amount),
    monthly_bonus = VALUES(monthly_bonus),
    total_amount = VALUES(total_amount),
    order_count = VALUES(order_count),
    updated_at = NOW();
```

### **5. 记录处理日志**
```sql
-- 更新 commission_processing_log 表
UPDATE commission_processing_log
SET status = 'SUCCESS',
    end_time = NOW(),
    total_records = 150,
    success_count = 148,
    failure_count = 2,
    updated_at = NOW()
WHERE month_key = '2024-02';

## 📈 状态流转图

### commission_record 状态流转
```mermaid
stateDiagram-v2
    [*] --> PENDING: 创建佣金记录
    PENDING --> CONFIRMED: 月初批量确认
    PENDING --> CANCELLED: 取消佣金
    CONFIRMED --> PAID: 月度结算完成
    CANCELLED --> [*]: 流程结束
    PAID --> [*]: 流程结束

    note right of PENDING: 初始状态，等待月初处理
    note right of CONFIRMED: 月初确认，等待结算
    note right of PAID: 已支付，流程完成
```

### monthly_commission_summary 状态流转
```mermaid
stateDiagram-v2
    [*] --> PENDING: 创建月度汇总
    PENDING --> PROCESSING: 开始月度结算
    PROCESSING --> COMPLETED: 结算完成
    PROCESSING --> CANCELLED: 结算取消
    CANCELLED --> PENDING: 重置状态
    COMPLETED --> [*]: 流程结束

    note right of PENDING: 待结算状态
    note right of PROCESSING: 结算处理中
    note right of COMPLETED: 结算已完成
```

## 🔍 关键数据关联关系

### 表间关联查询示例

```sql
-- 查询用户2024年3月的完整佣金信息
SELECT
    cr.id as record_id,
    cr.order_id,
    cr.order_amount,
    cr.commission_amount,
    cr.status as record_status,
    ms.total_amount as monthly_total,
    ms.status as summary_status,
    ms.settled_at
FROM commission_record cr
LEFT JOIN monthly_commission_summary ms
    ON cr.inviter_id = ms.inviter_id
    AND cr.month_key = ms.month_key
WHERE cr.inviter_id = 2001
  AND cr.month_key = '2024-03'
  AND cr.deleted = FALSE
ORDER BY cr.created_at DESC;
```

## 💡 数据一致性保证

### 1. **事务管理**
- 佣金记录创建和月度汇总更新在同一事务中
- 月度结算的状态更新使用事务保证一致性

### 2. **重复处理防护**
- `commission_record` 表有 `(order_id, inviter_id)` 唯一约束
- `commission_processing_log` 表记录月度处理状态，防止重复处理

### 3. **数据校验**
- 月度汇总数据定期与明细记录进行校验
- 异常数据通过日志记录和监控告警

## 📊 性能优化设计

### 1. **索引设计**
```sql
-- commission_record 关键索引
CREATE INDEX idx_inviter_month ON commission_record(inviter_id, month_key);
CREATE INDEX idx_status ON commission_record(status);
CREATE INDEX idx_order_inviter ON commission_record(order_id, inviter_id);

-- monthly_commission_summary 关键索引
CREATE INDEX idx_inviter_month ON monthly_commission_summary(inviter_id, month_key);
CREATE INDEX idx_status ON monthly_commission_summary(status);
```

### 2. **分页查询**
- 大数据量查询支持分页
- 月度处理支持批量处理

### 3. **缓存策略**
- 佣金配置数据缓存
- 月度汇总数据适当缓存

## 💡 **总结**

### **数据获取来源**：
1. **`commission_record`** - 获取待处理的佣金记录
2. **`commission_config`** - 获取最新的佣金配置规则

### **数据存储目标**：
1. **`commission_record`** - 更新记录状态（PENDING → CONFIRMED）
2. **`monthly_commission_summary`** - 存储最终的月度汇总数据
3. **`commission_processing_log`** - 记录处理过程和结果

### **关键特点**：
- ✅ **不是重新计算佣金**，而是确认和汇总已有的佣金记录
- ✅ **批量处理**，提高处理效率
- ✅ **状态管理**，确保不重复处理
- ✅ **完整日志**，支持审计和问题排查

### **核心价值**：
- 🚀 **提升性能**：O(1)查询月度汇总，而不是O(n)聚合计算
- 🎯 **简化业务**：将复杂的月度计算逻辑封装在汇总表中
- 🔒 **保证一致性**：作为明细记录和结算处理之间的桥梁
- 📈 **支持分析**：为业务分析和报表提供高效的数据源

这个设计确保了佣金数据的准确性和处理的可靠性！

---

**文档版本**: v1.0
**创建日期**: 2025-07-06
**维护团队**: 后端开发团队
**更新说明**: 基于commission模块代码分析和测试代码验证创建
```
   ```
