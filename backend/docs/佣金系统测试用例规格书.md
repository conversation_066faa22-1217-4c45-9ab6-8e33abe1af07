# 佣金系统测试用例规格书

## 1. 测试概述

### 1.1 测试目标
基于TDD（测试驱动开发）和DDD（领域驱动设计）原则，确保佣金系统的所有业务逻辑正确实现，数据一致性得到保障，系统性能满足要求。

### 1.2 测试范围
- 佣金计算服务的核心算法
- 优惠券生成和管理逻辑
- 事件驱动的业务流程
- 数据持久化和事务管理
- API接口的功能和性能

### 1.3 测试分层
```
┌─────────────────────────────────────┐
│           端到端测试 (E2E)            │
│     完整业务流程验证                  │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│           集成测试 (Integration)      │
│     组件间协作验证                    │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│           单元测试 (Unit)             │
│     单个组件功能验证                  │
└─────────────────────────────────────┘
```

## 2. 单元测试用例

### 2.1 CommissionCalculationService测试

#### 2.1.1 佣金比例计算测试
**测试类**: `CommissionCalculationServiceTest`
**测试方法数量**: 88个

**核心测试场景**:

| 测试场景 | 输入金额 | 期望比例 | 测试目的 |
|----------|----------|----------|----------|
| 第一阶梯下限 | $0 | 0.90% | 验证最低阶梯 |
| 第一阶梯上限 | $7,000 | 0.90% | 验证阶梯边界 |
| 第二阶梯下限 | $7,000.01 | 1.00% | 验证阶梯跳跃 |
| 第三阶梯中值 | $17,500 | 1.10% | 验证中间值 |
| 最高阶梯 | $100,000 | 1.40% | 验证最高比例 |

**边界值测试**:
```java
@ParameterizedTest
@MethodSource("commissionRateBoundaryTestData")
@DisplayName("参数化测试：佣金比例边界值")
void calculateCommissionRate_BoundaryValues_ReturnsCorrectRate(
    String amountStr, String expectedRateStr, String description) {
    
    // Given
    Money monthlyAmount = Money.of(amountStr);
    
    // When
    CommissionRate result = calculationService.calculateCommissionRate(monthlyAmount);
    
    // Then
    assertEquals(new BigDecimal(expectedRateStr), result.getRate());
}
```

#### 2.1.2 月度奖金计算测试
**奖金阶梯验证**:

| 月度金额范围 | 奖金金额 | 测试用例 |
|-------------|----------|----------|
| $0 - $7,000 | $0 | 无奖金场景 |
| $7,000.01 - $15,000 | $50 | 第一奖金阶梯 |
| $15,000.01 - $30,000 | $100 | 第二奖金阶梯 |
| $150,000.01以上 | $900 | 最高奖金阶梯 |

#### 2.1.3 订单验证测试
**验证规则测试**:
```java
@Test
@DisplayName("正常场景：有效的采购订单")
void isValidCommissionOrder_ValidPurchaseOrder_ReturnsTrue() {
    // Given
    String orderType = "purchase";
    String orderStatus = "completed";
    boolean buyerIsInvitee = true;
    
    // When
    boolean result = calculationService.isValidCommissionOrder(
        orderType, orderStatus, buyerIsInvitee);
    
    // Then
    assertTrue(result);
}

@ParameterizedTest
@ValueSource(strings = {"sample", "SAMPLE", "Sample_Order"})
@DisplayName("异常场景：样品订单应被排除")
void isValidCommissionOrder_SampleOrder_ReturnsFalse(String orderType) {
    // 测试样品订单检测逻辑
}
```

### 2.2 InviterCouponService测试

#### 2.2.1 优惠券生成测试
**测试类**: `InviterCouponServiceTest`
**测试方法数量**: 47个

**优惠券代码格式测试**:
```java
@Test
@DisplayName("正常场景：优惠券代码格式验证")
void generateCouponForBuyerInviter_CouponCodeFormat_IsCorrect() {
    // Given
    Long inviterId = 123L;
    Long triggerOrderId = 456L;
    
    // When
    InviterCoupon result = couponService.generateCouponForBuyerInviter(
        inviterId, inviteeId, triggerOrderId, orderAmount, orderType, testMonthKey);
    
    // Then
    String couponCode = result.getCouponCode();
    assertTrue(couponCode.startsWith("INV-123-"));
    String[] parts = couponCode.split("-");
    assertEquals(4, parts.length);
    assertEquals("INV", parts[0]);
    assertEquals("123", parts[1]);
    assertTrue(parts[2].matches("\\d+")); // timestamp
    assertTrue(parts[3].matches("[A-Z0-9]{8}")); // random part
}
```

#### 2.2.2 优惠券价值计算测试
**订单类型基础比例测试**:

| 订单类型 | 基础比例 | 5000元订单期望值 |
|----------|----------|------------------|
| purchase | 0.5% | $25.00 |
| logistics | 0.3% | $15.00 |
| other | 0.2% | $10.00 |

**金额阶梯调整测试**:
```java
@ParameterizedTest
@MethodSource("amountTierTestData")
@DisplayName("参数化测试：订单金额阶梯调整")
void generateCouponForBuyerInviter_AmountTiers_CorrectMultipliers(
    String orderAmountStr, String orderType, double expectedMultiplier) {
    
    // 验证不同金额阶梯的倍数调整
    // 1万以下: 1.0倍, 1万以上: 1.1倍, 2万以上: 1.2倍等
}
```

#### 2.2.3 优惠券验证测试
**使用条件验证**:
```java
@Test
@DisplayName("正常场景：优惠券验证成功")
void validateCouponUsage_ValidConditions_ReturnsSuccess() {
    // Given
    InviterCoupon coupon = createValidCoupon();
    Money orderAmount = Money.of("600.00"); // 满足最低使用金额
    Long userId = coupon.getInviterId();
    
    // When
    CouponValidationResult result = couponService.validateCouponUsage(
        coupon, orderAmount, userId);
    
    // Then
    assertTrue(result.isValid());
    assertEquals("验证成功", result.getMessage());
}
```

### 2.3 实体测试

#### 2.3.1 CommissionRecord实体测试
**状态流转测试**:
```java
@Test
@DisplayName("正常场景：佣金记录状态流转")
void statusTransition_ValidFlow_UpdatesCorrectly() {
    // Given
    CommissionRecord record = createPendingRecord();
    
    // When & Then - 确认流程
    record.confirm();
    assertEquals(Status.CONFIRMED, record.getStatus());
    assertNotNull(record.getConfirmedDate());
    
    // When & Then - 支付流程
    record.markAsPaid();
    assertEquals(Status.PAID, record.getStatus());
    assertNotNull(record.getPaidDate());
}
```

#### 2.3.2 InviterCoupon实体测试
**优惠券状态管理**:
```java
@Test
@DisplayName("正常场景：优惠券使用流程")
void useCoupon_ValidConditions_UpdatesStatus() {
    // Given
    InviterCoupon coupon = createActiveCoupon();
    Long orderId = 12345L;
    
    // When
    coupon.use(orderId);
    
    // Then
    assertEquals(Status.USED, coupon.getStatus());
    assertEquals(orderId, coupon.getUsedOrderId());
    assertNotNull(coupon.getUsedDate());
}
```

## 3. 集成测试用例

### 3.1 事件驱动流程测试

#### 3.1.1 订单完成事件处理
**测试类**: `OrderCompletedEventListenerTest`

```java
@Test
@DisplayName("正常场景：处理订单完成事件")
void handleOrderCompleted_ValidEvent_CallsApplicationService() {
    // Given
    CommissionCalculationEvent event = new CommissionCalculationEvent(
        ORDER_ID, BUYER_ID, ORDER_AMOUNT, ORDER_TYPE, COMPLETED_DATE);
    
    // When
    eventListener.handleOrderCompleted(event);
    
    // Then
    verify(commissionApplicationService).handleOrderCompleted(
        ORDER_ID, BUYER_ID, ORDER_AMOUNT, ORDER_TYPE, 
        COMPLETED_DATE.toLocalDate());
}
```

#### 3.1.2 异步处理验证
```java
@Test
@DisplayName("异步场景：事件处理不阻塞主流程")
void handleOrderCompleted_AsyncProcessing_DoesNotBlock() {
    // 验证事件处理的异步特性
    // 确保佣金计算失败不影响订单确认
}
```

### 3.2 应用服务集成测试

#### 3.2.1 完整佣金处理流程
**测试类**: `CommissionApplicationServiceTest`

```java
@Test
@DisplayName("正常场景：处理订单完成事件 - 创建佣金记录")
void handleOrderCompleted_ValidOrder_CreatesCommissionRecord() {
    // Given - 模拟所有依赖
    when(calculationService.isValidCommissionOrder(ORDER_TYPE, "completed", true))
        .thenReturn(true);
    when(userMapper.selectRoleById(INVITER_ID))
        .thenReturn("seller"); // 卖家角色
    
    // When
    applicationService.handleOrderCompleted(
        ORDER_ID, INVITEE_ID, ORDER_AMOUNT, ORDER_TYPE, ORDER_DATE);
    
    // Then - 验证佣金记录创建
    verify(commissionRecordRepository).save(any(CommissionRecord.class));
    verify(monthlyCommissionSummaryRepository).save(any(MonthlyCommissionSummary.class));
}
```

#### 3.2.2 角色差异化处理
```java
@Test
@DisplayName("正常场景：买家邀请人生成优惠券")
void handleOrderCompleted_BuyerInviter_GeneratesCoupon() {
    // Given
    when(userMapper.selectRoleById(INVITER_ID))
        .thenReturn("buyer"); // 买家角色
    
    // When
    applicationService.handleOrderCompleted(
        ORDER_ID, INVITEE_ID, ORDER_AMOUNT, ORDER_TYPE, ORDER_DATE);
    
    // Then - 验证优惠券生成
    verify(inviterCouponService).generateCouponForBuyerInviter(
        eq(INVITER_ID), eq(INVITEE_ID), eq(ORDER_ID), 
        eq(ORDER_AMOUNT), eq(ORDER_TYPE), any(MonthKey.class));
}
```

## 4. 端到端测试用例

### 4.1 完整业务流程测试

#### 4.1.1 采购订单佣金流程
```java
@Test
@DisplayName("端到端：完整的采购订单佣金处理流程")
void shouldProcessPurchaseOrderCommissionFlow() {
    // 1. 创建订单完成事件
    // 2. 事件监听器处理
    // 3. 应用服务处理业务逻辑
    // 4. 创建佣金记录
    // 5. 更新月度汇总
    // 6. 验证数据一致性
}
```

#### 4.1.2 重复处理防护测试
```java
@Test
@DisplayName("端到端：重复订单处理防护")
void shouldPreventDuplicateOrderProcessing() {
    // 验证同一订单多次处理的幂等性
    // 确保不会产生重复的佣金记录
}
```

## 5. 性能测试用例

### 5.1 并发处理测试
```java
@Test
@DisplayName("性能测试：并发处理1000个订单事件")
void performanceTest_ConcurrentOrderProcessing() {
    // 模拟高并发场景
    // 验证系统吞吐量和响应时间
    // 检查数据一致性
}
```

### 5.2 大数据量测试
```java
@Test
@DisplayName("性能测试：月度汇总计算性能")
void performanceTest_MonthlyCalculationWithLargeDataset() {
    // 测试大量佣金记录的汇总计算性能
    // 验证数据库查询优化效果
}
```

## 6. 测试数据管理

### 6.1 测试数据准备
```java
@TestConfiguration
public class CommissionTestDataConfig {
    
    @Bean
    @Primary
    public TestDataBuilder testDataBuilder() {
        return TestDataBuilder.builder()
            .withUsers(createTestUsers())
            .withOrders(createTestOrders())
            .withInviteRelations(createInviteRelations())
            .build();
    }
}
```

### 6.2 测试数据清理
```java
@AfterEach
void cleanupTestData() {
    commissionRecordRepository.deleteAll();
    inviterCouponRepository.deleteAll();
    monthlyCommissionSummaryRepository.deleteAll();
}
```

## 7. 测试覆盖率要求

### 7.1 覆盖率目标
- **行覆盖率**: ≥ 90%
- **分支覆盖率**: ≥ 85%
- **方法覆盖率**: ≥ 95%

### 7.2 关键组件覆盖率
| 组件 | 当前覆盖率 | 目标覆盖率 | 状态 |
|------|------------|------------|------|
| CommissionCalculationService | 85%+ | 90% | ✅ 达标 |
| InviterCouponService | 90%+ | 90% | ✅ 达标 |
| CommissionRecord | 95%+ | 95% | ✅ 达标 |
| InviterCoupon | 95%+ | 95% | ✅ 达标 |

## 8. 测试执行策略

### 8.1 持续集成测试
- 每次代码提交自动运行单元测试
- 每日构建运行完整测试套件
- 发布前运行性能测试

### 8.2 测试环境管理
- **开发环境**: 开发者本地测试
- **测试环境**: 集成测试和回归测试
- **预生产环境**: 性能测试和用户验收测试

---

**文档版本**: 1.0  
**创建日期**: 2025-07-06  
**最后更新**: 2025-07-06  
**文档状态**: 正式版
