# Commission Settlement 结算单据功能 TDD 开发指南 (MyBatis-Plus版)

## 📋 开发概述

本文档基于 `Commission_Settlement_Requirements.md` 需求文档，采用 **TDD（测试驱动开发）** 方式和 **DDD（领域驱动设计）** 规范标准，使用 **Spring Boot + MySQL + MyBatis-Plus** 技术栈，详细说明Commission Settlement结算单据功能的技术实现方案。

### 核心设计原则
- **TDD优先**：先写测试，再写实现
- **DDD分层**：严格按照领域驱动设计分层架构
- **MyBatis-Plus**：使用MyBatis-Plus作为ORM框架
- **业务导向**：以业务需求为驱动进行设计
- **测试覆盖**：确保高质量的测试覆盖率

### 技术栈
- **框架**：Spring Boot 2.7+
- **数据库**：MySQL 8.0+
- **ORM**：MyBatis-Plus 3.5+
- **测试**：JUnit 5 + Mockito
- **构建**：Maven

## 🏗️ DDD架构设计

### 1. 分层架构图

```mermaid
graph TD
    A[interfaces 接口层] --> B[application 应用层]
    B --> C[domain 领域层]
    B --> D[infrastructure 基础设施层]
    C --> D
    
    A1[CommissionSettlementController] --> B1[CommissionSettlementService]
    A2[SettlementDTO] --> B1
    B1 --> C1[CommissionSettlement Entity]
    B1 --> C2[SettlementDomainService]
    B1 --> D1[CommissionSettlementMapper]
    C1 --> D2[CommissionSettlementPO]
    C2 --> D1
    
    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#f3e5f5
    style D fill:#fff3e0
```

### 2. 包结构设计

```
src/main/java/com/purchase/commission/settlement/
├── interfaces/                          # 接口层
│   ├── web/
│   │   ├── CommissionSettlementController.java
│   │   └── dto/
│   │       ├── SettlementCreateRequestDTO.java
│   │       ├── SettlementResponseDTO.java
│   │       └── ReceiptFileDTO.java
│   └── event/
│       └── SettlementCompletedEventListener.java
├── application/                         # 应用层
│   ├── service/
│   │   ├── CommissionSettlementService.java
│   │   └── SettlementFileService.java
│   └── command/
│       ├── CreateSettlementCommand.java
│       └── UploadReceiptCommand.java
├── domain/                             # 领域层
│   ├── entity/
│   │   ├── CommissionSettlement.java
│   │   └── ReceiptFile.java
│   ├── service/
│   │   └── SettlementDomainService.java
│   ├── repository/
│   │   └── CommissionSettlementRepository.java
│   └── event/
│       └── SettlementCompletedEvent.java
└── infrastructure/                     # 基础设施层
    ├── repository/
    │   └── CommissionSettlementRepositoryImpl.java
    ├── mapper/
    │   ├── CommissionSettlementMapper.java
    │   └── xml/
    │       └── CommissionSettlementMapper.xml
    └── po/
        └── CommissionSettlementPO.java
```

## 🧪 TDD开发流程

### 阶段1：领域实体测试驱动开发

#### 1.1 CommissionSettlement实体测试

**第一步：编写失败测试**

```java
@DisplayName("CommissionSettlement实体测试")
class CommissionSettlementTest {
    
    @Test
    @DisplayName("正常场景：创建结算单据")
    void create_ValidParameters_CreatesSettlement() {
        // Given
        Long inviterId = 1001L;
        String monthKey = "2024-03";
        BigDecimal totalCommission = new BigDecimal("300.00");
        BigDecimal monthlyBonus = new BigDecimal("100.00");
        String settlementMethod = "bank_transfer"; // 使用数据库实际值
        String accountInfo = "中国银行 **************** 张三";
        List<ReceiptFile> receiptFiles = Arrays.asList(
            ReceiptFile.create("https://oss.example.com/receipt1.pdf", "银行转账回执", "admin001")
        );
        String notes = "2024年3月佣金结算";
        String paymentReference = "***********";

        // When
        CommissionSettlement settlement = CommissionSettlement.create(
            inviterId, monthKey, totalCommission, monthlyBonus,
            settlementMethod, accountInfo, receiptFiles, notes, paymentReference, "admin001"
        );

        // Then
        assertNotNull(settlement);
        assertNotNull(settlement.getSettlementNumber());
        assertTrue(settlement.getSettlementNumber().startsWith("SETTLE-202403-"));
        assertEquals(inviterId, settlement.getInviterId());
        assertEquals(monthKey, settlement.getMonthKey());
        assertEquals(new BigDecimal("400.00"), settlement.getTotalAmount());
        assertEquals("COMPLETED", settlement.getStatus()); // 与数据库默认值一致
        assertEquals(1, settlement.getReceiptFiles().size());
        assertNotNull(settlement.getCreatedAt());
        assertEquals("admin001", settlement.getCreatedBy());
        assertEquals("admin001", settlement.getCompletedBy());
        assertNotNull(settlement.getCompletedAt());
    }
    
    @Test
    @DisplayName("异常场景：创建结算单据时回执文件为空")
    void create_EmptyReceiptFiles_ThrowsException() {
        // Given
        Long inviterId = 1001L;
        String monthKey = "2024-03";
        BigDecimal totalCommission = new BigDecimal("300.00");
        BigDecimal monthlyBonus = new BigDecimal("100.00");
        String settlementMethod = "bank_transfer"; // 使用数据库实际值
        String accountInfo = "中国银行 **************** 张三";
        List<ReceiptFile> receiptFiles = Collections.emptyList(); // 空列表
        String notes = "2024年3月佣金结算";
        String paymentReference = "***********";
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> CommissionSettlement.create(
                inviterId, monthKey, totalCommission, monthlyBonus,
                settlementMethod, accountInfo, receiptFiles, notes, paymentReference, "admin001"
            )
        );
        
        assertTrue(exception.getMessage().contains("转账回执文件不能为空"));
    }
    
    @Test
    @DisplayName("异常场景：创建结算单据时金额为负数")
    void create_NegativeAmount_ThrowsException() {
        // Given
        BigDecimal totalCommission = new BigDecimal("-100.00"); // 负数金额

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> CommissionSettlement.create(
                1001L, "2024-03", totalCommission, BigDecimal.ZERO,
                "bank_transfer", "账户信息", // 使用数据库实际值
                Arrays.asList(ReceiptFile.create("url", "desc", "admin")),
                "备注", "ref", "admin001"
            )
        );

        assertTrue(exception.getMessage().contains("结算金额必须大于0"));
    }

    @Test
    @DisplayName("异常场景：回执文件超过最大数量限制")
    void create_ExceedsMaxFileLimit_ThrowsException() {
        // Given
        List<ReceiptFile> receiptFiles = IntStream.range(0, 6)
            .mapToObj(i -> ReceiptFile.create("url" + i, "desc" + i, "admin"))
            .collect(Collectors.toList());

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> CommissionSettlement.create(
                1001L, "2024-03", new BigDecimal("100"), BigDecimal.ZERO,
                "BANK_TRANSFER", "账户信息", receiptFiles,
                "备注", "ref", "admin001"
            )
        );

        assertTrue(exception.getMessage().contains("转账回执文件数量不能超过5个"));
    }

    @Test
    @DisplayName("边界值测试：结算编号唯一性")
    void generateSettlementNumber_ConcurrentGeneration_EnsuresUniqueness() {
        // Given
        String monthKey = "2024-03";
        Set<String> generatedNumbers = ConcurrentHashMap.newKeySet();

        // When - 并发生成100个结算编号
        IntStream.range(0, 100).parallel().forEach(i -> {
            String number = CommissionSettlement.generateSettlementNumber(monthKey);
            generatedNumbers.add(number);
        });

        // Then - 确保所有编号都是唯一的
        assertEquals(100, generatedNumbers.size());
        generatedNumbers.forEach(number ->
            assertTrue(number.startsWith("SETTLE-202403-")));
    }

    @Test
    @DisplayName("业务规则测试：金额精度验证")
    void create_AmountPrecision_HandlesCorrectly() {
        // Given
        BigDecimal totalCommission = new BigDecimal("300.123456"); // 超过2位小数
        BigDecimal monthlyBonus = new BigDecimal("100.789");

        // When
        CommissionSettlement settlement = CommissionSettlement.create(
            1001L, "2024-03", totalCommission, monthlyBonus,
            "bank_transfer", "账户信息", // 使用数据库实际值
            Arrays.asList(ReceiptFile.create("url", "desc", "admin")),
            "备注", "ref", "admin001"
        );

        // Then - 验证金额精度处理
        assertEquals(0, settlement.getTotalCommission().scale()); // 应该处理精度
        assertEquals(0, settlement.getMonthlyBonus().scale());
    }
}
```

**第二步：编写实体实现（MyBatis-Plus版）**

```java
@TableName("commission_settlement")
public class CommissionSettlement {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("settlement_number")
    private String settlementNumber;
    
    @TableField("inviter_id")
    private Long inviterId;
    
    @TableField("month_key")
    private String monthKey; // 格式：YYYY-MM
    
    @TableField("total_commission")
    private BigDecimal totalCommission;
    
    @TableField("monthly_bonus")
    private BigDecimal monthlyBonus;
    
    @TableField("total_amount")
    private BigDecimal totalAmount;
    
    @TableField("settlement_method")
    private String settlementMethod; // bank_transfer/alipay/wechat (数据库实际值)

    @TableField("account_info")
    private String accountInfo;

    @TableField("settlement_status")
    private String status; // COMPLETED (与数据库默认值一致)
    
    @TableField("settlement_time")
    private LocalDateTime settlementTime;
    
    @TableField(value = "receipt_files", typeHandler = ReceiptFileListTypeHandler.class)
    private List<ReceiptFile> receiptFiles;
    
    @TableField("notes")
    private String notes;
    
    @TableField("payment_reference")
    private String paymentReference;
    
    // 审计字段
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField("created_by")
    private String createdBy;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableField("updated_by")
    private String updatedBy;
    
    @TableField("completed_by")
    private String completedBy;
    
    @TableField("completed_at")
    private LocalDateTime completedAt;
    
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;
    
    // 私有构造函数，强制使用工厂方法
    private CommissionSettlement() {}
    
    // 工厂方法
    public static CommissionSettlement create(Long inviterId, String monthKey,
                                            BigDecimal totalCommission, BigDecimal monthlyBonus,
                                            String settlementMethod, String accountInfo,
                                            List<ReceiptFile> receiptFiles, String notes,
                                            String paymentReference, String createdBy) {
        
        // 业务规则验证
        validateCreateParameters(inviterId, monthKey, totalCommission, monthlyBonus, 
                               receiptFiles, notes, paymentReference);
        
        CommissionSettlement settlement = new CommissionSettlement();
        settlement.settlementNumber = generateSettlementNumber(monthKey);
        settlement.inviterId = inviterId;
        settlement.monthKey = monthKey;
        settlement.totalCommission = totalCommission;
        settlement.monthlyBonus = monthlyBonus;
        settlement.totalAmount = totalCommission.add(monthlyBonus);
        settlement.settlementMethod = settlementMethod; // 支持: bank_transfer/alipay/wechat
        settlement.accountInfo = accountInfo;
        settlement.status = "COMPLETED"; // 生成即完成，与数据库默认值一致
        settlement.settlementTime = LocalDateTime.now();
        settlement.receiptFiles = new ArrayList<>(receiptFiles);
        settlement.notes = notes;
        settlement.paymentReference = paymentReference;
        settlement.createdBy = createdBy;
        settlement.updatedBy = createdBy;
        settlement.completedBy = createdBy; // 生成即完成，完成人就是创建人
        settlement.completedAt = LocalDateTime.now(); // 生成即完成，完成时间就是创建时间
        settlement.deleted = false;
        
        return settlement;
    }
    
    // 业务方法
    public void addReceiptFile(ReceiptFile receiptFile) {
        if (this.receiptFiles.size() >= 5) {
            throw new IllegalStateException("回执文件数量不能超过5个");
        }
        this.receiptFiles.add(receiptFile);
        this.updatedAt = LocalDateTime.now();
    }
    
    // 验证方法
    public boolean hasReceiptFiles() {
        return receiptFiles != null && !receiptFiles.isEmpty();
    }
    
    public boolean isCompleted() {
        return "COMPLETED".equals(this.status);
    }
    
    // 静态方法
    public static String generateSettlementNumber(String monthKey) {
        String prefix = "SETTLE-" + monthKey.replace("-", "") + "-";
        String sequence = String.format("%03d", System.currentTimeMillis() % 1000);
        return prefix + sequence;
    }
    
    // 私有验证方法
    private static void validateCreateParameters(Long inviterId, String monthKey,
                                               BigDecimal totalCommission, BigDecimal monthlyBonus,
                                               List<ReceiptFile> receiptFiles, String notes,
                                               String paymentReference) {
        if (inviterId == null || inviterId <= 0) {
            throw new IllegalArgumentException("邀请者ID不能为空且必须大于0");
        }
        if (StringUtils.isBlank(monthKey)) {
            throw new IllegalArgumentException("月份不能为空");
        }
        if (totalCommission == null || totalCommission.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("结算金额必须大于0");
        }
        if (monthlyBonus == null || monthlyBonus.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("月度奖金不能为负数");
        }
        if (receiptFiles == null || receiptFiles.isEmpty()) {
            throw new IllegalArgumentException("转账回执文件不能为空");
        }
        if (receiptFiles.size() > 5) {
            throw new IllegalArgumentException("转账回执文件数量不能超过5个");
        }
        if (StringUtils.isBlank(notes)) {
            throw new IllegalArgumentException("结算备注不能为空");
        }
        if (StringUtils.isBlank(paymentReference)) {
            throw new IllegalArgumentException("支付参考号不能为空");
        }
    }
    
    // getters and setters...
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public String getSettlementNumber() { return settlementNumber; }
    public void setSettlementNumber(String settlementNumber) { this.settlementNumber = settlementNumber; }
    public Long getInviterId() { return inviterId; }
    public void setInviterId(Long inviterId) { this.inviterId = inviterId; }
    public String getMonthKey() { return monthKey; }
    public void setMonthKey(String monthKey) { this.monthKey = monthKey; }
    public BigDecimal getTotalCommission() { return totalCommission; }
    public void setTotalCommission(BigDecimal totalCommission) { this.totalCommission = totalCommission; }
    public BigDecimal getMonthlyBonus() { return monthlyBonus; }
    public void setMonthlyBonus(BigDecimal monthlyBonus) { this.monthlyBonus = monthlyBonus; }
    public BigDecimal getTotalAmount() { return totalAmount; }
    public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
    public String getSettlementMethod() { return settlementMethod; }
    public void setSettlementMethod(String settlementMethod) { this.settlementMethod = settlementMethod; }
    public String getAccountInfo() { return accountInfo; }
    public void setAccountInfo(String accountInfo) { this.accountInfo = accountInfo; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    public LocalDateTime getSettlementTime() { return settlementTime; }
    public void setSettlementTime(LocalDateTime settlementTime) { this.settlementTime = settlementTime; }
    public List<ReceiptFile> getReceiptFiles() { return receiptFiles; }
    public void setReceiptFiles(List<ReceiptFile> receiptFiles) { this.receiptFiles = receiptFiles; }
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    public String getPaymentReference() { return paymentReference; }
    public void setPaymentReference(String paymentReference) { this.paymentReference = paymentReference; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    public String getUpdatedBy() { return updatedBy; }
    public void setUpdatedBy(String updatedBy) { this.updatedBy = updatedBy; }
    public String getCompletedBy() { return completedBy; }
    public void setCompletedBy(String completedBy) { this.completedBy = completedBy; }
    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }
    public Boolean getDeleted() { return deleted; }
    public void setDeleted(Boolean deleted) { this.deleted = deleted; }
}

#### 1.2 ReceiptFile值对象

```java
public class ReceiptFile {

    private final String fileUrl;
    private final String fileName;
    private final String fileType;
    private final String description;
    private final LocalDateTime uploadedAt;
    private final String uploadedBy;

    private static final Set<String> SUPPORTED_FILE_TYPES =
        Set.of("pdf", "jpg", "jpeg", "png");

    private ReceiptFile(String fileUrl, String fileName, String fileType,
                       String description, LocalDateTime uploadedAt, String uploadedBy) {
        this.fileUrl = fileUrl;
        this.fileName = fileName;
        this.fileType = fileType;
        this.description = description;
        this.uploadedAt = uploadedAt;
        this.uploadedBy = uploadedBy;
    }

    public static ReceiptFile create(String fileUrl, String description, String uploadedBy) {
        validateParameters(fileUrl, description, uploadedBy);

        String fileName = extractFileName(fileUrl);
        String fileType = extractFileType(fileUrl);

        if (!SUPPORTED_FILE_TYPES.contains(fileType.toLowerCase())) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileType);
        }

        return new ReceiptFile(
            fileUrl, fileName, fileType, description,
            LocalDateTime.now(), uploadedBy
        );
    }

    private static void validateParameters(String fileUrl, String description, String uploadedBy) {
        if (StringUtils.isBlank(fileUrl)) {
            throw new IllegalArgumentException("文件URL不能为空");
        }
        if (StringUtils.isBlank(description)) {
            throw new IllegalArgumentException("文件描述不能为空");
        }
        if (StringUtils.isBlank(uploadedBy)) {
            throw new IllegalArgumentException("上传人不能为空");
        }

        // 验证文件大小（通过URL获取文件信息）
        validateFileSize(fileUrl);
    }

    private static void validateFileSize(String fileUrl) {
        // 注意：这里应该调用文件服务获取文件大小
        // 简化实现，实际应该通过HTTP HEAD请求或文件服务API获取
        try {
            // 模拟文件大小检查逻辑
            // 实际实现应该调用 FileService.getFileSize(fileUrl)
            long fileSize = getFileSizeFromUrl(fileUrl);
            if (fileSize > 10 * 1024 * 1024) { // 10MB限制
                throw new IllegalArgumentException("文件大小不能超过10MB");
            }
        } catch (Exception e) {
            log.warn("无法验证文件大小: {}", fileUrl);
            // 不阻断流程，只记录警告
        }
    }

    private static long getFileSizeFromUrl(String fileUrl) {
        // 这里应该实现真实的文件大小获取逻辑
        // 可以通过HTTP HEAD请求或调用OSS服务API
        return 0; // 占位实现
    }

    private static String extractFileName(String fileUrl) {
        return fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
    }

    private static String extractFileType(String fileUrl) {
        String fileName = extractFileName(fileUrl);
        int dotIndex = fileName.lastIndexOf('.');
        return dotIndex > 0 ? fileName.substring(dotIndex + 1) : "";
    }

    // getters...
    public String getFileUrl() { return fileUrl; }
    public String getFileName() { return fileName; }
    public String getFileType() { return fileType; }
    public String getDescription() { return description; }
    public LocalDateTime getUploadedAt() { return uploadedAt; }
    public String getUploadedBy() { return uploadedBy; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReceiptFile that = (ReceiptFile) o;
        return Objects.equals(fileUrl, that.fileUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fileUrl);
    }
}
```

#### 1.4 领域事件定义

```java
/**
 * 结算完成事件
 */
public class SettlementCompletedEvent extends ApplicationEvent {

    private final Long settlementId;
    private final Long inviterId;
    private final String monthKey;
    private final BigDecimal totalAmount;
    private final LocalDateTime completedAt;

    public SettlementCompletedEvent(Object source, Long settlementId, Long inviterId,
                                   String monthKey, BigDecimal totalAmount) {
        super(source);
        this.settlementId = settlementId;
        this.inviterId = inviterId;
        this.monthKey = monthKey;
        this.totalAmount = totalAmount;
        this.completedAt = LocalDateTime.now();
    }

    // getters...
    public Long getSettlementId() { return settlementId; }
    public Long getInviterId() { return inviterId; }
    public String getMonthKey() { return monthKey; }
    public BigDecimal getTotalAmount() { return totalAmount; }
    public LocalDateTime getCompletedAt() { return completedAt; }
}

/**
 * 批量结算完成事件
 */
public class BatchSettlementCompletedEvent extends ApplicationEvent {

    private final List<Long> settlementIds;
    private final int successCount;
    private final int failureCount;
    private final BigDecimal totalAmount;
    private final LocalDateTime completedAt;

    public BatchSettlementCompletedEvent(Object source, List<Long> settlementIds,
                                        int successCount, int failureCount, BigDecimal totalAmount) {
        super(source);
        this.settlementIds = settlementIds;
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.totalAmount = totalAmount;
        this.completedAt = LocalDateTime.now();
    }

    // getters...
}

/**
 * 事件监听器
 */
@Component
@Slf4j
public class SettlementEventListener {

    private final NotificationService notificationService;
    private final AuditService auditService;

    @EventListener
    @Async
    public void handleSettlementCompleted(SettlementCompletedEvent event) {
        log.info("处理结算完成事件: settlementId={}, inviterId={}, amount={}",
                event.getSettlementId(), event.getInviterId(), event.getTotalAmount());

        // 发送通知
        notificationService.notifySettlementCompleted(
            event.getInviterId(), event.getMonthKey(), event.getTotalAmount());

        // 记录审计日志
        auditService.recordSettlementCompleted(event);

        // 更新统计数据
        updateSettlementStatistics(event);
    }

    @EventListener
    @Async
    public void handleBatchSettlementCompleted(BatchSettlementCompletedEvent event) {
        log.info("处理批量结算完成事件: 成功={}, 失败={}, 总金额={}",
                event.getSuccessCount(), event.getFailureCount(), event.getTotalAmount());

        // 发送批量处理结果通知
        notificationService.notifyBatchSettlementCompleted(event);

        // 记录批量操作审计日志
        auditService.recordBatchSettlementCompleted(event);
    }

    private void updateSettlementStatistics(SettlementCompletedEvent event) {
        // 更新实时统计数据
        // 可以使用Redis或其他缓存来维护统计信息
    }
}
```

#### 1.3 JSON类型处理器

```java
@Component
public class ReceiptFileListTypeHandler extends BaseTypeHandler<List<ReceiptFile>> {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<ReceiptFile> parameter, JdbcType jdbcType) throws SQLException {
        try {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        } catch (JsonProcessingException e) {
            throw new SQLException("Error converting ReceiptFile list to JSON", e);
        }
    }

    @Override
    public List<ReceiptFile> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public List<ReceiptFile> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public List<ReceiptFile> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private List<ReceiptFile> parseJson(String json) throws SQLException {
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }
        try {
            TypeReference<List<ReceiptFile>> typeRef = new TypeReference<List<ReceiptFile>>() {};
            return objectMapper.readValue(json, typeRef);
        } catch (JsonProcessingException e) {
            throw new SQLException("Error parsing JSON to ReceiptFile list", e);
        }
    }
}
```

### 阶段2：仓储层测试驱动开发

#### 2.1 仓储接口定义（DDD规范）

```java
/**
 * 结算单据仓储接口 - 领域层
 */
public interface CommissionSettlementRepository {

    /**
     * 保存结算单据
     */
    CommissionSettlement save(CommissionSettlement settlement);

    /**
     * 根据ID查找
     */
    Optional<CommissionSettlement> findById(Long id);

    /**
     * 根据邀请人和月份查找
     */
    Optional<CommissionSettlement> findByInviterIdAndMonthKey(Long inviterId, String monthKey);

    /**
     * 检查是否存在
     */
    boolean existsByInviterIdAndMonthKey(Long inviterId, String monthKey);

    /**
     * 分页查询
     */
    Page<CommissionSettlement> findAll(Pageable pageable);

    /**
     * 根据条件查询
     */
    Page<CommissionSettlement> findByCriteria(SettlementQueryCriteria criteria, Pageable pageable);

    /**
     * 批量保存
     */
    List<CommissionSettlement> saveAll(List<CommissionSettlement> settlements);

    /**
     * 统计查询
     */
    SettlementStatistics getStatistics(LocalDateTime startDate, LocalDateTime endDate);
}

/**
 * 仓储实现类 - 基础设施层
 */
@Repository
public class CommissionSettlementRepositoryImpl implements CommissionSettlementRepository {

    private final CommissionSettlementMapper settlementMapper;

    public CommissionSettlementRepositoryImpl(CommissionSettlementMapper settlementMapper) {
        this.settlementMapper = settlementMapper;
    }

    @Override
    public CommissionSettlement save(CommissionSettlement settlement) {
        if (settlement.getId() == null) {
            settlementMapper.insert(settlement);
        } else {
            settlementMapper.updateById(settlement);
        }
        return settlement;
    }

    @Override
    public Optional<CommissionSettlement> findById(Long id) {
        CommissionSettlement settlement = settlementMapper.selectById(id);
        return Optional.ofNullable(settlement);
    }

    @Override
    public Optional<CommissionSettlement> findByInviterIdAndMonthKey(Long inviterId, String monthKey) {
        CommissionSettlement settlement = settlementMapper.selectByInviterIdAndMonthKey(inviterId, monthKey);
        return Optional.ofNullable(settlement);
    }

    @Override
    public boolean existsByInviterIdAndMonthKey(Long inviterId, String monthKey) {
        return settlementMapper.existsByInviterIdAndMonthKey(inviterId, monthKey);
    }

    @Override
    public Page<CommissionSettlement> findAll(Pageable pageable) {
        IPage<CommissionSettlement> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        IPage<CommissionSettlement> result = settlementMapper.selectPage(page,
            Wrappers.<CommissionSettlement>lambdaQuery()
                .eq(CommissionSettlement::getDeleted, false)
                .orderByDesc(CommissionSettlement::getCreatedAt));

        return new PageImpl<>(result.getRecords(), pageable, result.getTotal());
    }

    @Override
    public Page<CommissionSettlement> findByCriteria(SettlementQueryCriteria criteria, Pageable pageable) {
        IPage<CommissionSettlement> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        IPage<CommissionSettlement> result = settlementMapper.selectByCriteria(page, criteria);

        return new PageImpl<>(result.getRecords(), pageable, result.getTotal());
    }

    @Override
    @Transactional
    public List<CommissionSettlement> saveAll(List<CommissionSettlement> settlements) {
        settlements.forEach(this::save);
        return settlements;
    }

    @Override
    public SettlementStatistics getStatistics(LocalDateTime startDate, LocalDateTime endDate) {
        BigDecimal totalAmount = settlementMapper.sumTotalAmountByDateRange(startDate, endDate);
        List<SettlementMethodCount> methodCounts = settlementMapper.countBySettlementMethod(startDate, endDate);

        long totalCount = methodCounts.stream().mapToLong(SettlementMethodCount::getCount).sum();

        return SettlementStatistics.builder()
            .totalAmount(totalAmount)
            .totalCount(totalCount)
            .methodCounts(methodCounts)
            .build();
    }
}
```

#### 2.2 CommissionSettlementMapper接口

```java
@Mapper
public interface CommissionSettlementMapper extends BaseMapper<CommissionSettlement> {

    /**
     * 根据邀请人ID和月份查询结算单据
     */
    CommissionSettlement selectByInviterIdAndMonthKey(@Param("inviterId") Long inviterId,
                                                     @Param("monthKey") String monthKey);

    /**
     * 检查结算单据是否存在
     */
    boolean existsByInviterIdAndMonthKey(@Param("inviterId") Long inviterId,
                                        @Param("monthKey") String monthKey);

    /**
     * 根据条件分页查询
     */
    IPage<CommissionSettlement> selectByCriteria(IPage<CommissionSettlement> page,
                                                @Param("criteria") SettlementQueryCriteria criteria);

    /**
     * 统计结算金额
     */
    BigDecimal sumTotalAmountByDateRange(@Param("startDate") LocalDateTime startDate,
                                        @Param("endDate") LocalDateTime endDate);

    /**
     * 根据结算方式统计数量
     */
    List<SettlementMethodCount> countBySettlementMethod(@Param("startDate") LocalDateTime startDate,
                                                       @Param("endDate") LocalDateTime endDate);
}
```

#### 2.2 XML映射文件

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.purchase.commission.settlement.infrastructure.mapper.CommissionSettlementMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.purchase.commission.settlement.domain.entity.CommissionSettlement">
        <id column="id" property="id" />
        <result column="settlement_number" property="settlementNumber" />
        <result column="inviter_id" property="inviterId" />
        <result column="month_key" property="monthKey" />
        <result column="total_commission" property="totalCommission" />
        <result column="monthly_bonus" property="monthlyBonus" />
        <result column="total_amount" property="totalAmount" />
        <result column="settlement_method" property="settlementMethod" />
        <result column="account_info" property="accountInfo" />
        <result column="settlement_status" property="status" />
        <result column="settlement_time" property="settlementTime" />
        <result column="receipt_files" property="receiptFiles"
                typeHandler="com.purchase.commission.settlement.infrastructure.typehandler.ReceiptFileListTypeHandler" />
        <result column="notes" property="notes" />
        <result column="payment_reference" property="paymentReference" />
        <result column="created_at" property="createdAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="completed_by" property="completedBy" />
        <result column="completed_at" property="completedAt" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 根据邀请人ID和月份查询 -->
    <select id="selectByInviterIdAndMonthKey" resultMap="BaseResultMap">
        SELECT * FROM commission_settlement
        WHERE inviter_id = #{inviterId}
        AND month_key = #{monthKey}
        AND deleted = 0
    </select>

    <!-- 检查是否存在 -->
    <select id="existsByInviterIdAndMonthKey" resultType="boolean">
        SELECT COUNT(1) > 0 FROM commission_settlement
        WHERE inviter_id = #{inviterId}
        AND month_key = #{monthKey}
        AND deleted = 0
    </select>

    <!-- 根据条件分页查询 -->
    <select id="selectByCriteria" resultMap="BaseResultMap">
        SELECT * FROM commission_settlement
        WHERE deleted = 0
        <if test="criteria.inviterId != null">
            AND inviter_id = #{criteria.inviterId}
        </if>
        <if test="criteria.monthKey != null and criteria.monthKey != ''">
            AND month_key = #{criteria.monthKey}
        </if>
        <if test="criteria.settlementMethod != null and criteria.settlementMethod != ''">
            AND settlement_method = #{criteria.settlementMethod}
        </if>
        <if test="criteria.status != null and criteria.status != ''">
            AND settlement_status = #{criteria.status}
        </if>
        <if test="criteria.startDate != null">
            AND created_at >= #{criteria.startDate}
        </if>
        <if test="criteria.endDate != null">
            AND created_at &lt;= #{criteria.endDate}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 统计结算金额 -->
    <select id="sumTotalAmountByDateRange" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(total_amount), 0) FROM commission_settlement
        WHERE deleted = 0
        AND created_at >= #{startDate}
        AND created_at &lt;= #{endDate}
    </select>

    <!-- 根据结算方式统计数量 -->
    <select id="countBySettlementMethod" resultType="com.purchase.commission.settlement.domain.vo.SettlementMethodCount">
        SELECT
            settlement_method as settlementMethod,
            COUNT(*) as count,
            SUM(total_amount) as totalAmount
        FROM commission_settlement
        WHERE deleted = 0
        AND created_at >= #{startDate}
        AND created_at &lt;= #{endDate}
        GROUP BY settlement_method
    </select>

</mapper>
```

### 阶段3：应用服务测试驱动开发

#### 3.1 CommissionSettlementService测试

```java
@ExtendWith(MockitoExtension.class)
@DisplayName("CommissionSettlementService应用服务测试")
class CommissionSettlementServiceTest {

    @Mock
    private CommissionSettlementMapper settlementMapper;

    @Mock
    private MonthlyCommissionSummaryMapper summaryMapper;

    @Mock
    private SettlementDomainService settlementDomainService;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @InjectMocks
    private CommissionSettlementService settlementService;

    private static final Long INVITER_ID = 1001L;
    private static final String MONTH_KEY = "2024-03";
    private static final String ADMIN_USER = "admin001";

    @Test
    @DisplayName("正常场景：创建结算单据")
    void createSettlement_ValidCommand_CreatesSuccessfully() {
        // Given
        CreateSettlementCommand command = CreateSettlementCommand.builder()
            .inviterId(INVITER_ID)
            .monthKey(MONTH_KEY)
            .settlementMethod("bank_transfer") // 使用数据库实际值
            .accountInfo("中国银行 **************** 张三")
            .receiptFiles(Arrays.asList(
                ReceiptFileDTO.builder()
                    .fileUrl("https://oss.example.com/receipt1.pdf")
                    .description("银行转账回执")
                    .build()
            ))
            .notes("2024年3月佣金结算")
            .paymentReference("***********")
            .createdBy(ADMIN_USER)
            .build();

        MonthlyCommissionSummary summary = new MonthlyCommissionSummary();
        summary.setInviterId(INVITER_ID);
        summary.setMonthKey(MONTH_KEY);
        summary.setCommissionAmount(new BigDecimal("300"));
        summary.setBonusAmount(new BigDecimal("100"));
        summary.setStatus("PENDING");

        when(summaryMapper.selectByInviterIdAndMonthKey(INVITER_ID, MONTH_KEY))
            .thenReturn(summary);

        CommissionSettlement settlement = new CommissionSettlement();
        settlement.setId(1L);
        settlement.setSettlementNumber("SETTLE-202403-001");

        when(settlementDomainService.createSettlement(any(), any())).thenReturn(settlement);
        when(settlementMapper.insert(any())).thenReturn(1);

        // When
        CommissionSettlement result = settlementService.createSettlement(command);

        // Then
        assertNotNull(result);
        verify(summaryMapper).selectByInviterIdAndMonthKey(INVITER_ID, MONTH_KEY);
        verify(settlementDomainService).createSettlement(eq(summary), eq(command));
        verify(settlementMapper).insert(settlement);
        verify(eventPublisher).publishEvent(any(SettlementCompletedEvent.class));
    }

    @Test
    @DisplayName("异常场景：月度汇总不存在")
    void createSettlement_SummaryNotFound_ThrowsException() {
        // Given
        CreateSettlementCommand command = CreateSettlementCommand.builder()
            .inviterId(INVITER_ID)
            .monthKey(MONTH_KEY)
            .build();

        when(summaryMapper.selectByInviterIdAndMonthKey(INVITER_ID, MONTH_KEY))
            .thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> settlementService.createSettlement(command)
        );

        assertTrue(exception.getMessage().contains("未找到月度汇总记录"));
        verify(settlementMapper, never()).insert(any());
    }
}
```

#### 3.2 CommissionSettlementService实现

```java
@Service
@Transactional
@Slf4j
public class CommissionSettlementService {

    private final CommissionSettlementMapper settlementMapper;
    private final MonthlyCommissionSummaryMapper summaryMapper;
    private final SettlementDomainService settlementDomainService;
    private final ApplicationEventPublisher eventPublisher;

    public CommissionSettlementService(
            CommissionSettlementMapper settlementMapper,
            MonthlyCommissionSummaryMapper summaryMapper,
            SettlementDomainService settlementDomainService,
            ApplicationEventPublisher eventPublisher) {
        this.settlementMapper = settlementMapper;
        this.summaryMapper = summaryMapper;
        this.settlementDomainService = settlementDomainService;
        this.eventPublisher = eventPublisher;
    }

    /**
     * 结算预览 - 生成前预览结算信息
     */
    @Transactional(readOnly = true)
    public SettlementPreviewDTO previewSettlement(Long inviterId, String monthKey) {
        // 1. 获取月度汇总数据
        MonthlyCommissionSummary summary = summaryMapper
            .selectByInviterIdAndMonthKey(inviterId, monthKey);

        if (summary == null) {
            throw new IllegalArgumentException("未找到月度汇总记录");
        }

        if (!"PENDING".equals(summary.getStatus())) {
            throw new IllegalStateException("只能预览待结算状态的汇总");
        }

        // 2. 检查是否已存在结算单据
        boolean exists = settlementMapper.existsByInviterIdAndMonthKey(inviterId, monthKey);
        if (exists) {
            throw new IllegalStateException("该月度汇总已存在结算单据");
        }

        // 3. 生成预览信息
        return SettlementPreviewDTO.builder()
            .inviterId(inviterId)
            .monthKey(monthKey)
            .totalCommission(summary.getCommissionAmount())
            .monthlyBonus(summary.getBonusAmount())
            .totalAmount(summary.getCommissionAmount().add(summary.getBonusAmount()))
            .suggestedSettlementNumber(CommissionSettlement.generateSettlementNumber(monthKey))
            .orderCount(summary.getOrderCount())
            .build();
    }

    /**
     * 批量结算预览
     */
    @Transactional(readOnly = true)
    public List<SettlementPreviewDTO> batchPreviewSettlements(List<SettlementBatchItem> items) {
        return items.stream()
            .map(item -> {
                try {
                    return previewSettlement(item.getInviterId(), item.getMonthKey());
                } catch (Exception e) {
                    log.warn("批量预览失败: inviterId={}, monthKey={}, error={}",
                            item.getInviterId(), item.getMonthKey(), e.getMessage());
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 创建结算单据
     */
    public CommissionSettlement createSettlement(CreateSettlementCommand command) {
        // 1. 验证月度汇总存在且状态正确
        MonthlyCommissionSummary summary = summaryMapper
            .selectByInviterIdAndMonthKey(command.getInviterId(), command.getMonthKey());

        if (summary == null) {
            throw new IllegalArgumentException(
                String.format("未找到月度汇总记录: inviterId=%d, monthKey=%s",
                             command.getInviterId(), command.getMonthKey()));
        }

        if (!"PENDING".equals(summary.getStatus())) {
            throw new IllegalStateException("只能为待结算状态的汇总创建结算单据");
        }

        // 2. 验证结算单据不存在
        if (settlementMapper.existsByInviterIdAndMonthKey(
                command.getInviterId(), command.getMonthKey())) {
            throw new IllegalStateException("该月度汇总已存在结算单据");
        }

        // 3. 创建结算单据
        CommissionSettlement settlement = settlementDomainService
            .createSettlement(summary, command);

        // 4. 保存结算单据
        settlementMapper.insert(settlement);

        // 5. 更新月度汇总状态
        summary.setStatus("PROCESSING");
        summaryMapper.updateById(summary);

        // 6. 发布领域事件
        eventPublisher.publishEvent(new SettlementCompletedEvent(
            settlement.getId(), settlement.getInviterId(),
            settlement.getMonthKey(), settlement.getTotalAmount()));

        log.info("创建结算单据成功: settlementNumber={}, inviterId={}, monthKey={}",
                settlement.getSettlementNumber(), command.getInviterId(), command.getMonthKey());

        return settlement;
    }

    /**
     * 分页查询结算单据
     */
    @Transactional(readOnly = true)
    public IPage<CommissionSettlement> getSettlements(int page, int size) {
        IPage<CommissionSettlement> pageParam = new Page<>(page, size);
        return settlementMapper.selectPage(pageParam,
            Wrappers.<CommissionSettlement>lambdaQuery()
                .eq(CommissionSettlement::getDeleted, false)
                .orderByDesc(CommissionSettlement::getCreatedAt));
    }

    /**
     * 根据条件查询结算单据
     */
    @Transactional(readOnly = true)
    public IPage<CommissionSettlement> getSettlements(SettlementQueryCriteria criteria,
                                                    int page, int size) {
        IPage<CommissionSettlement> pageParam = new Page<>(page, size);
        return settlementMapper.selectByCriteria(pageParam, criteria);
    }

    /**
     * 根据ID获取结算单据
     */
    @Transactional(readOnly = true)
    public CommissionSettlement getSettlementById(Long settlementId) {
        CommissionSettlement settlement = settlementMapper.selectById(settlementId);
        if (settlement == null) {
            throw new IllegalArgumentException("结算单据不存在: " + settlementId);
        }
        return settlement;
    }

    /**
     * 根据邀请人和月份获取结算单据
     */
    @Transactional(readOnly = true)
    public CommissionSettlement getSettlementByInviterAndMonth(
            Long inviterId, String monthKey) {
        return settlementMapper.selectByInviterIdAndMonthKey(inviterId, monthKey);
    }

    /**
     * 获取待结算的月度汇总列表
     */
    @Transactional(readOnly = true)
    public IPage<MonthlyCommissionSummary> getPendingSummaries(int page, int size) {
        IPage<MonthlyCommissionSummary> pageParam = new Page<>(page, size);
        return summaryMapper.selectPage(pageParam,
            Wrappers.<MonthlyCommissionSummary>lambdaQuery()
                .eq(MonthlyCommissionSummary::getStatus, "PENDING")
                .orderByDesc(MonthlyCommissionSummary::getMonthKey));
    }

    /**
     * 批量创建结算单据
     */
    public List<CommissionSettlement> batchCreateSettlements(
            List<CreateSettlementCommand> commands) {

        List<CommissionSettlement> settlements = new ArrayList<>();

        for (CreateSettlementCommand command : commands) {
            try {
                CommissionSettlement settlement = createSettlement(command);
                settlements.add(settlement);
            } catch (Exception e) {
                log.error("批量创建结算单据失败: inviterId={}, monthKey={}, error={}",
                         command.getInviterId(), command.getMonthKey(), e.getMessage());
                // 继续处理下一个，不中断整个批量操作
            }
        }

        return settlements;
    }

    /**
     * 获取结算统计信息
     */
    @Transactional(readOnly = true)
    public SettlementStatistics getStatistics(LocalDateTime startDate, LocalDateTime endDate) {
        BigDecimal totalAmount = settlementMapper.sumTotalAmountByDateRange(startDate, endDate);
        List<SettlementMethodCount> methodCounts = settlementMapper.countBySettlementMethod(startDate, endDate);

        long totalCount = methodCounts.stream().mapToLong(SettlementMethodCount::getCount).sum();

        return SettlementStatistics.builder()
            .totalAmount(totalAmount)
            .totalCount(totalCount)
            .methodCounts(methodCounts)
            .build();
    }
}
```

### 阶段4：控制器层测试驱动开发

#### 4.1 CommissionSettlementController

```java
@RestController
@RequestMapping("/api/v1/commission/settlement")
@Validated
@Slf4j
public class CommissionSettlementController {

    private final CommissionSettlementService settlementService;

    public CommissionSettlementController(CommissionSettlementService settlementService) {
        this.settlementService = settlementService;
    }

    /**
     * 创建结算单据
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<SettlementResponseDTO>> createSettlement(
            @Valid @RequestBody SettlementCreateRequestDTO request,
            Authentication authentication) {

        CreateSettlementCommand command = CreateSettlementCommand.builder()
            .inviterId(request.getInviterId())
            .monthKey(request.getMonthKey())
            .settlementMethod(request.getSettlementMethod())
            .accountInfo(request.getAccountInfo())
            .receiptFiles(request.getReceiptFiles())
            .notes(request.getNotes())
            .paymentReference(request.getPaymentReference())
            .createdBy(authentication.getName())
            .build();

        CommissionSettlement settlement = settlementService.createSettlement(command);
        SettlementResponseDTO response = SettlementResponseDTO.fromEntity(settlement);

        return ResponseEntity.status(HttpStatus.CREATED)
            .body(ApiResponse.success(response));
    }

    /**
     * 分页查询结算单据
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<IPage<SettlementResponseDTO>>> getSettlements(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            SettlementQueryCriteria criteria) {

        IPage<CommissionSettlement> settlements = settlementService.getSettlements(criteria, page, size);
        IPage<SettlementResponseDTO> response = settlements.convert(SettlementResponseDTO::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据ID获取结算单据详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<SettlementResponseDTO>> getSettlementById(
            @PathVariable Long id) {

        CommissionSettlement settlement = settlementService.getSettlementById(id);
        SettlementResponseDTO response = SettlementResponseDTO.fromEntity(settlement);

        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取结算统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<SettlementStatistics>> getStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {

        SettlementStatistics statistics = settlementService.getStatistics(startDate, endDate);
        return ResponseEntity.ok(ApiResponse.success(statistics));
    }

    /**
     * 获取待结算的月度汇总
     */
    @GetMapping("/pending-summaries")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<IPage<MonthlyCommissionSummaryDTO>>> getPendingSummaries(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        IPage<MonthlyCommissionSummary> summaries = settlementService.getPendingSummaries(page, size);
        IPage<MonthlyCommissionSummaryDTO> response = summaries.convert(MonthlyCommissionSummaryDTO::fromEntity);

        return ResponseEntity.ok(ApiResponse.success(response));
    }
}
```

## � 数据库字段映射说明

### 重要：枚举值与数据库一致性

为确保与实际数据库结构完全匹配，请注意以下字段的枚举值：

| 字段名 | 数据库值 | 说明 |
|--------|----------|------|
| `settlement_method` | `bank_transfer` | 银行转账 |
| `settlement_method` | `alipay` | 支付宝转账 |
| `settlement_method` | `wechat` | 微信转账 |
| `settlement_status` | `COMPLETED` | 已完成（默认值） |
| `status` (月度汇总) | `PENDING` | 待结算 |
| `status` (月度汇总) | `PROCESSING` | 处理中 |
| `status` (月度汇总) | `COMPLETED` | 已完成 |
| `status` (月度汇总) | `CANCELLED` | 已取消 |

### MyBatis-Plus字段映射

```java
// 正确的字段映射示例
@TableField("settlement_method")
private String settlementMethod; // 值: "bank_transfer", "alipay", "wechat"

@TableField("settlement_status")
private String status; // 值: "COMPLETED"

// 错误示例（不要使用）
// private String settlementMethod = "BANK_TRANSFER"; // ❌ 与数据库不一致
```

### API接口枚举值说明

在API文档和前端交互中，也应使用与数据库一致的枚举值：

```json
// 正确的API请求示例
{
  "inviterId": 1001,
  "monthKey": "2024-03",
  "settlementMethod": "bank_transfer",  // ✅ 使用数据库实际值
  "accountInfo": "中国银行 **************** 张三",
  "notes": "2024年3月佣金结算",
  "paymentReference": "***********",
  "receiptFiles": [
    {
      "fileUrl": "https://oss.example.com/receipt.pdf",
      "description": "银行转账回执"
    }
  ]
}

// 错误示例（不要使用）
{
  "settlementMethod": "BANK_TRANSFER"  // ❌ 与数据库不一致
}
```

## �🔧 MyBatis-Plus配置

### 1. application.yml配置

```yaml
# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.purchase.commission.settlement.domain.entity
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
    banner: false

# 数据源配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}

  # 连接池配置
  hikari:
    minimum-idle: 5
    maximum-pool-size: 20
    auto-commit: true
    idle-timeout: 30000
    pool-name: DatebookHikariCP
    max-lifetime: 1800000
    connection-timeout: 30000
```

### 2. MyBatis-Plus配置类

```java
@Configuration
@MapperScan("com.purchase.commission.settlement.infrastructure.mapper")
public class MyBatisPlusConfig {

    /**
     * 分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

    /**
     * 自动填充配置
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, LocalDateTime.now());
                this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
            }
        };
    }
}
```

### 3. 类型处理器注册

```java
@Configuration
public class TypeHandlerConfig {

    @Bean
    @ConfigurationProperties(prefix = "mybatis-plus.configuration")
    public org.apache.ibatis.session.Configuration mybatisConfiguration() {
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();

        // 注册自定义类型处理器
        configuration.getTypeHandlerRegistry().register(
            List.class,
            JdbcType.VARCHAR,
            ReceiptFileListTypeHandler.class
        );

        return configuration;
    }
}
```

## 📊 测试策略

### 1. 单元测试覆盖率要求
- **实体类测试**：100%覆盖率
- **服务类测试**：90%以上覆盖率
- **控制器测试**：85%以上覆盖率
- **Mapper测试**：80%以上覆盖率

### 2. 集成测试
```java
@SpringBootTest
@Transactional
@TestPropertySource(locations = "classpath:application-test.properties")
class CommissionSettlementIntegrationTest {

    @Autowired
    private CommissionSettlementService settlementService;

    @Autowired
    private CommissionSettlementMapper settlementMapper;

    @Autowired
    private MonthlyCommissionSummaryMapper summaryMapper;

    @Autowired
    private TestEntityManager entityManager;

    @Test
    @DisplayName("集成测试：完整的结算单据创建流程")
    void createSettlement_IntegrationTest_Success() {
        // Given - 准备测试数据
        MonthlyCommissionSummary summary = new MonthlyCommissionSummary();
        summary.setInviterId(1001L);
        summary.setMonthKey("2024-03");
        summary.setCommissionAmount(new BigDecimal("300.00"));
        summary.setBonusAmount(new BigDecimal("100.00"));
        summary.setStatus("PENDING");
        summary.setOrderCount(5);
        summaryMapper.insert(summary);

        CreateSettlementCommand command = CreateSettlementCommand.builder()
            .inviterId(1001L)
            .monthKey("2024-03")
            .settlementMethod("bank_transfer") // 使用数据库实际值
            .accountInfo("中国银行 **************** 张三")
            .receiptFiles(Arrays.asList(
                ReceiptFileDTO.builder()
                    .fileUrl("https://oss.example.com/receipt1.pdf")
                    .description("银行转账回执")
                    .build()
            ))
            .notes("2024年3月佣金结算")
            .paymentReference("***********")
            .createdBy("admin001")
            .build();

        // When - 执行结算创建
        CommissionSettlement result = settlementService.createSettlement(command);
        entityManager.flush();

        // Then - 验证结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals("COMPLETED", result.getStatus());
        assertEquals(new BigDecimal("400.00"), result.getTotalAmount());

        // 验证数据库状态
        CommissionSettlement saved = settlementMapper.selectById(result.getId());
        assertNotNull(saved);
        assertEquals(1, saved.getReceiptFiles().size());

        // 验证月度汇总状态更新
        MonthlyCommissionSummary updatedSummary = summaryMapper.selectByInviterIdAndMonthKey(1001L, "2024-03");
        assertEquals("PROCESSING", updatedSummary.getStatus());
    }

    @Test
    @DisplayName("集成测试：批量结算流程")
    void batchCreateSettlements_IntegrationTest_Success() {
        // Given - 准备多个月度汇总
        List<MonthlyCommissionSummary> summaries = Arrays.asList(
            createSummary(1001L, "2024-01", "200.00", "50.00"),
            createSummary(1002L, "2024-01", "300.00", "100.00"),
            createSummary(1003L, "2024-01", "150.00", "25.00")
        );
        summaries.forEach(summaryMapper::insert);

        List<CreateSettlementCommand> commands = summaries.stream()
            .map(this::createCommand)
            .collect(Collectors.toList());

        // When - 执行批量结算
        List<CommissionSettlement> results = settlementService.batchCreateSettlements(commands);
        entityManager.flush();

        // Then - 验证结果
        assertEquals(3, results.size());
        results.forEach(settlement -> {
            assertNotNull(settlement.getId());
            assertEquals("COMPLETED", settlement.getStatus());
        });

        // 验证数据库中的记录
        long count = settlementMapper.selectCount(
            Wrappers.<CommissionSettlement>lambdaQuery()
                .eq(CommissionSettlement::getDeleted, false));
        assertEquals(3, count);
    }

    @Test
    @DisplayName("集成测试：结算预览功能")
    void previewSettlement_IntegrationTest_Success() {
        // Given
        MonthlyCommissionSummary summary = createSummary(1001L, "2024-03", "500.00", "200.00");
        summaryMapper.insert(summary);

        // When
        SettlementPreviewDTO preview = settlementService.previewSettlement(1001L, "2024-03");

        // Then
        assertNotNull(preview);
        assertEquals(1001L, preview.getInviterId());
        assertEquals("2024-03", preview.getMonthKey());
        assertEquals(new BigDecimal("500.00"), preview.getTotalCommission());
        assertEquals(new BigDecimal("200.00"), preview.getMonthlyBonus());
        assertEquals(new BigDecimal("700.00"), preview.getTotalAmount());
        assertTrue(preview.getSuggestedSettlementNumber().startsWith("SETTLE-202403-"));
    }

    private MonthlyCommissionSummary createSummary(Long inviterId, String monthKey,
                                                  String commission, String bonus) {
        MonthlyCommissionSummary summary = new MonthlyCommissionSummary();
        summary.setInviterId(inviterId);
        summary.setMonthKey(monthKey);
        summary.setCommissionAmount(new BigDecimal(commission));
        summary.setBonusAmount(new BigDecimal(bonus));
        summary.setStatus("PENDING");
        summary.setOrderCount(3);
        return summary;
    }

    private CreateSettlementCommand createCommand(MonthlyCommissionSummary summary) {
        return CreateSettlementCommand.builder()
            .inviterId(summary.getInviterId())
            .monthKey(summary.getMonthKey())
            .settlementMethod("bank_transfer") // 使用数据库实际值
            .accountInfo("测试银行账户")
            .receiptFiles(Arrays.asList(
                ReceiptFileDTO.builder()
                    .fileUrl("https://oss.example.com/receipt.pdf")
                    .description("转账回执")
                    .build()
            ))
            .notes("测试结算")
            .paymentReference("TEST" + System.currentTimeMillis())
            .createdBy("admin001")
            .build();
    }
}
```

### 3. 性能测试
```java
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.properties")
class CommissionSettlementPerformanceTest {

    @Autowired
    private CommissionSettlementService settlementService;

    @Autowired
    private MonthlyCommissionSummaryMapper summaryMapper;

    @Test
    @DisplayName("性能测试：批量创建1000个结算单据")
    void batchCreateSettlements_PerformanceTest() {
        // Given - 准备1000个月度汇总数据
        List<MonthlyCommissionSummary> summaries = IntStream.range(0, 1000)
            .mapToObj(i -> {
                MonthlyCommissionSummary summary = new MonthlyCommissionSummary();
                summary.setInviterId((long) (1000 + i));
                summary.setMonthKey("2024-03");
                summary.setCommissionAmount(new BigDecimal("100.00"));
                summary.setBonusAmount(new BigDecimal("20.00"));
                summary.setStatus("PENDING");
                summary.setOrderCount(2);
                return summary;
            })
            .collect(Collectors.toList());

        // 批量插入汇总数据
        summaries.forEach(summaryMapper::insert);

        List<CreateSettlementCommand> commands = summaries.stream()
            .map(summary -> CreateSettlementCommand.builder()
                .inviterId(summary.getInviterId())
                .monthKey(summary.getMonthKey())
                .settlementMethod("bank_transfer") // 使用数据库实际值
                .accountInfo("测试账户")
                .receiptFiles(Arrays.asList(
                    ReceiptFileDTO.builder()
                        .fileUrl("https://oss.example.com/receipt.pdf")
                        .description("回执")
                        .build()
                ))
                .notes("性能测试")
                .paymentReference("PERF" + summary.getInviterId())
                .createdBy("admin001")
                .build())
            .collect(Collectors.toList());

        // When - 执行性能测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        List<CommissionSettlement> results = settlementService.batchCreateSettlements(commands);

        stopWatch.stop();

        // Then - 验证性能指标
        long executionTime = stopWatch.getTotalTimeMillis();
        log.info("批量创建1000个结算单据耗时: {}ms", executionTime);

        // 性能要求：1000个结算单据在10秒内完成
        assertTrue(executionTime < 10000, "批量创建耗时超过10秒: " + executionTime + "ms");

        // 验证成功率
        assertEquals(1000, results.size());

        // 验证平均处理时间
        double avgTimePerRecord = (double) executionTime / 1000;
        assertTrue(avgTimePerRecord < 10, "平均每个记录处理时间超过10ms: " + avgTimePerRecord + "ms");
    }

    @Test
    @DisplayName("性能测试：并发创建结算单据")
    void createSettlement_ConcurrencyTest() throws InterruptedException {
        // Given - 准备并发测试数据
        int threadCount = 10;
        int recordsPerThread = 50;
        CountDownLatch latch = new CountDownLatch(threadCount);
        List<Exception> exceptions = Collections.synchronizedList(new ArrayList<>());
        List<CommissionSettlement> results = Collections.synchronizedList(new ArrayList<>());

        // 准备测试数据
        for (int i = 0; i < threadCount * recordsPerThread; i++) {
            MonthlyCommissionSummary summary = new MonthlyCommissionSummary();
            summary.setInviterId((long) (2000 + i));
            summary.setMonthKey("2024-04");
            summary.setCommissionAmount(new BigDecimal("150.00"));
            summary.setBonusAmount(new BigDecimal("30.00"));
            summary.setStatus("PENDING");
            summary.setOrderCount(3);
            summaryMapper.insert(summary);
        }

        // When - 执行并发测试
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        for (int t = 0; t < threadCount; t++) {
            final int threadIndex = t;
            executor.submit(() -> {
                try {
                    for (int i = 0; i < recordsPerThread; i++) {
                        Long inviterId = (long) (2000 + threadIndex * recordsPerThread + i);
                        CreateSettlementCommand command = CreateSettlementCommand.builder()
                            .inviterId(inviterId)
                            .monthKey("2024-04")
                            .settlementMethod("bank_transfer") // 使用数据库实际值
                            .accountInfo("并发测试账户")
                            .receiptFiles(Arrays.asList(
                                ReceiptFileDTO.builder()
                                    .fileUrl("https://oss.example.com/receipt.pdf")
                                    .description("并发测试回执")
                                    .build()
                            ))
                            .notes("并发测试")
                            .paymentReference("CONC" + inviterId)
                            .createdBy("admin001")
                            .build();

                        CommissionSettlement result = settlementService.createSettlement(command);
                        results.add(result);
                    }
                } catch (Exception e) {
                    exceptions.add(e);
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        latch.await(30, TimeUnit.SECONDS);
        stopWatch.stop();
        executor.shutdown();

        // Then - 验证并发测试结果
        long executionTime = stopWatch.getTotalTimeMillis();
        log.info("并发创建{}个结算单据耗时: {}ms", threadCount * recordsPerThread, executionTime);

        // 验证没有异常
        if (!exceptions.isEmpty()) {
            log.error("并发测试出现异常: {}", exceptions.get(0).getMessage());
            fail("并发测试出现异常: " + exceptions.get(0).getMessage());
        }

        // 验证结果数量
        assertEquals(threadCount * recordsPerThread, results.size());

        // 验证结算编号唯一性
        Set<String> settlementNumbers = results.stream()
            .map(CommissionSettlement::getSettlementNumber)
            .collect(Collectors.toSet());
        assertEquals(results.size(), settlementNumbers.size(), "结算编号存在重复");

        // 性能要求：并发处理在20秒内完成
        assertTrue(executionTime < 20000, "并发处理耗时超过20秒: " + executionTime + "ms");
    }

    @Test
    @DisplayName("性能测试：大数据量查询")
    void querySettlements_LargeDataTest() {
        // Given - 准备大量数据（这里简化为模拟）
        // 实际测试中应该准备10万+数据

        // When - 执行分页查询
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        SettlementQueryCriteria criteria = new SettlementQueryCriteria();
        criteria.setStartDate(LocalDateTime.now().minusMonths(1));
        criteria.setEndDate(LocalDateTime.now());

        IPage<CommissionSettlement> result = settlementService.getSettlements(criteria, 0, 20);

        stopWatch.stop();

        // Then - 验证查询性能
        long executionTime = stopWatch.getTotalTimeMillis();
        log.info("大数据量分页查询耗时: {}ms", executionTime);

        // 性能要求：分页查询在1秒内完成
        assertTrue(executionTime < 1000, "分页查询耗时超过1秒: " + executionTime + "ms");

        assertNotNull(result);
        assertTrue(result.getSize() <= 20);
    }
}
```

## 🎯 开发检查清单

### ✅ 数据库一致性
- [ ] 字段映射注解正确（@TableField）
- [ ] 枚举值与数据库一致（bank_transfer而非BANK_TRANSFER）
- [ ] JSON字段TypeHandler配置
- [ ] 索引和约束验证

### ✅ 实体层
- [ ] CommissionSettlement实体类（MyBatis-Plus注解）
- [ ] ReceiptFile值对象
- [ ] JSON类型处理器
- [ ] 实体测试（100%覆盖率）

### ✅ 基础设施层
- [ ] CommissionSettlementMapper接口
- [ ] XML映射文件
- [ ] 自定义查询方法
- [ ] Mapper测试

### ✅ 应用层
- [ ] CommissionSettlementService
- [ ] 领域服务
- [ ] 命令对象
- [ ] 事件发布
- [ ] 服务测试

### ✅ 接口层
- [ ] CommissionSettlementController
- [ ] DTO对象
- [ ] 参数验证
- [ ] 权限控制
- [ ] 控制器测试

### ✅ 配置
- [ ] MyBatis-Plus配置
- [ ] 数据源配置
- [ ] 类型处理器注册
- [ ] 分页插件配置

## 🚀 部署和监控

### 1. 数据库监控
```sql
-- 监控结算单据表的性能
SHOW INDEX FROM commission_settlement;
EXPLAIN SELECT * FROM commission_settlement WHERE inviter_id = 1001 AND month_key = '2024-03';
```

### 2. 应用监控
```java
@Component
public class SettlementMetrics {

    private final MeterRegistry meterRegistry;

    @EventListener
    public void handleSettlementCompleted(SettlementCompletedEvent event) {
        meterRegistry.counter("settlement.completed").increment();
        meterRegistry.gauge("settlement.amount", event.getTotalAmount().doubleValue());
    }
}
```

这份MyBatis-Plus版本的TDD开发文档完全符合您的技术栈要求，包含了完整的测试驱动开发流程和最佳实践。
```
