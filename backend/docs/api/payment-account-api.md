# 收款账户管理API文档

## 概述

收款账户管理模块提供用户收款账户的完整管理功能，包括账户的创建、查询、更新、验证和删除等操作。

## 基础信息

- **基础URL**: `/api/v1/payment-accounts`
- **认证方式**: Spring Security
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "code": 200
}
```

## 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "data": null,
  "code": 400
}
```

## 1. 创建收款账户

### 接口描述
创建新的收款账户，支持银行转账、支付宝、微信等多种账户类型。

### 请求信息
- **URL**: `POST /api/v1/payment-accounts`
- **权限**: 需要登录用户权限
- **Content-Type**: `application/json`

### 请求参数

```json
{
  "userId" : 1001,
  "accountType" : "bank_transfer",
  "accountName" : "ICBC Savings Account",
  "accountHolder" : "Zhang San",
  "bankName" : "Industrial and Commercial Bank of China",
  "accountNumber" : "****************",
  "branchName" : "Beijing Branch",
  "swiftCode" : "ICBKCNBJ",
  "alipayAccount" : null,
  "wechatAccount" : null,
  "isDefault" : false
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |
| accountType | String | 是 | 账户类型：bank_transfer(银行转账)、alipay(支付宝)、wechat(微信) |
| accountName | String | 是 | 账户名称 |
| accountHolder | String | 是 | 账户持有人姓名 |
| bankName | String | 否 | 银行名称（银行转账类型必填） |
| accountNumber | String | 否 | 银行账号（银行转账类型必填） |
| branchName | String | 否 | 开户行名称 |
| swiftCode | String | 否 | SWIFT代码（国际转账） |
| alipayAccount | String | 否 | 支付宝账号（支付宝类型必填） |
| wechatAccount | String | 否 | 微信账号（微信类型必填） |
| isDefault | Boolean | 否 | 是否设为默认账户，默认false |

### 响应示例

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "id": "1",
    "userId": "1001",
    "accountType": "bank_transfer",
    "accountName": "ICBC Savings Account",
    "accountHolder": "Zhang San",
    "bankName": "Industrial and Commercial Bank of China",
    "accountNumber": "****************",
    "branchName": "Beijing Branch",
    "swiftCode": "ICBKCNBJ",
    "alipayAccount": null,
    "wechatAccount": null,
    "isDefault": false,
    "isVerified": false,
    "status": "pending",
    "verificationNotes": null,
    "verifiedAt": null,
    "verifiedBy": null,
    "createdAt": "2025-07-08T21:42:20.356",
    "updatedAt": "2025-07-08T21:42:20.356"
  },
  "code": 200
}
```

## 2. 查询用户收款账户

### 接口描述
查询指定用户的所有收款账户列表。

### 请求信息
- **URL**: `GET /api/v1/payment-accounts?userId={userId}`
- **权限**: 需要登录用户权限

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

### 响应示例

```json
{
  "success": true,
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "userId": "1001",
      "accountType": "bank_transfer",
      "accountName": "ICBC Savings Account",
      "accountHolder": "Zhang San",
      "bankName": "Industrial and Commercial Bank of China",
      "accountNumber": "****************",
      "branchName": "Beijing Branch",
      "swiftCode": "ICBKCNBJ",
      "alipayAccount": null,
      "wechatAccount": null,
      "isDefault": false,
      "isVerified": false,
      "status": "pending",
      "verificationNotes": null,
      "verifiedAt": null,
      "verifiedBy": null,
      "createdAt": "2025-07-08T21:42:20.356",
      "updatedAt": "2025-07-08T21:42:20.356"
    }
  ],
  "code": 200
}
```

## 3. 更新收款账户

### 接口描述
更新指定的收款账户信息。

### 请求信息
- **URL**: `PUT /api/v1/payment-accounts/{accountId}`
- **权限**: 需要登录用户权限
- **Content-Type**: `application/json`

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| accountId | Long | 是 | 账户ID |

### 请求体参数
参数格式与创建账户接口相同，参考第1节。

### 响应示例

```json
{
  "success": true,
  "message": "账户更新成功",
  "data": {
    "id": "1",
    "userId": "1001",
    "accountType": "bank_transfer",
    "accountName": "更新后的账户名称",
    "updatedAt": "2025-07-08T21:30:00"
  },
  "code": 200
}
```

## 4. 设置默认收款账户

### 接口描述
将指定账户设置为用户的默认收款账户，同时取消其他账户的默认状态。

### 请求信息
- **URL**: `PUT /api/v1/payment-accounts/{accountId}/default`
- **权限**: 需要登录用户权限

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| accountId | Long | 是 | 账户ID |

### 查询参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

### 响应示例

```json
{
  "success": true,
  "message": "设置默认账户成功",
  "data": null,
  "code": 200
}
```

## 5. 验证收款账户

### 接口描述
管理员验证用户提交的收款账户信息。

### 请求信息
- **URL**: `PUT /api/v1/payment-accounts/{accountId}/verify`
- **权限**: 需要管理员权限

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| accountId | Long | 是 | 账户ID |

### 查询参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| approved | Boolean | 是 | 是否通过验证 |
| notes | String | 否 | 验证备注 |
| verifiedBy | String | 是 | 验证人员 |

### 响应示例

```json
{
  "success": true,
  "message": "账户验证成功",
  "data": null,
  "code": 200
}
```

## 6. 删除收款账户

### 接口描述
软删除指定的收款账户（逻辑删除）。

### 请求信息
- **URL**: `DELETE /api/v1/payment-accounts/{accountId}`
- **权限**: 需要登录用户权限

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| accountId | Long | 是 | 账户ID |

### 查询参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

### 响应示例

```json
{
  "success": true,
  "message": "账户删除成功",
  "data": null,
  "code": 200
}
```

## 7. 数据模型

### 7.1 收款账户对象 (PaymentAccount)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 账户ID |
| userId | String | 用户ID |
| accountType | String | 账户类型：bank_transfer、alipay、wechat |
| accountName | String | 账户名称 |
| accountHolder | String | 账户持有人姓名 |
| bankName | String | 银行名称 |
| accountNumber | String | 银行账号 |
| branchName | String | 开户行名称 |
| swiftCode | String | SWIFT代码 |
| alipayAccount | String | 支付宝账号 |
| wechatAccount | String | 微信账号 |
| isDefault | Boolean | 是否为默认账户 |
| isVerified | Boolean | 是否已验证 |
| status | String | 账户状态：pending、active、inactive |
| verificationNotes | String | 验证备注 |
| verifiedAt | String | 验证时间 |
| verifiedBy | String | 验证人员 |
| createdAt | String | 创建时间 |
| updatedAt | String | 更新时间 |

### 7.2 账户类型说明

| 类型值 | 说明 | 必填字段 |
|--------|------|----------|
| bank_transfer | 银行转账 | bankName, accountNumber |
| alipay | 支付宝 | alipayAccount |
| wechat | 微信支付 | wechatAccount |

### 7.3 账户状态说明

| 状态值 | 说明 |
|--------|------|
| pending | 待验证 |
| active | 已激活 |
| inactive | 已停用 |

## 8. 错误码说明

### 8.1 HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 8.2 业务错误码

| 错误码 | 说明 |
|--------|------|
| 40001 | 用户ID不能为空 |
| 40002 | 账户类型不能为空 |
| 40003 | 账户名称不能为空 |
| 40004 | 账户持有人不能为空 |
| 40005 | 银行账户信息不完整 |
| 40006 | 支付宝账号不能为空 |
| 40007 | 微信账号不能为空 |
| 40008 | 账户不存在 |
| 40009 | 无权限操作此账户 |
| 40010 | 账户已被删除 |

## 9. 使用示例

### 9.1 创建银行账户示例

```bash
curl -X POST \
  http://localhost:8080/api/v1/payment-accounts \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your-token' \
  -d '{
    "userId": 1001,
    "accountType": "bank_transfer",
    "accountName": "工商银行储蓄卡",
    "accountHolder": "张三",
    "bankName": "中国工商银行",
    "accountNumber": "****************",
    "branchName": "北京分行营业部",
    "isDefault": false
  }'
```

### 9.2 查询用户账户示例

```bash
curl -X GET \
  'http://localhost:8080/api/v1/payment-accounts?userId=1001' \
  -H 'Authorization: Bearer your-token'
```

---

**文档版本**: v1.0  
**最后更新**: 2025-07-17T22:25:25.*********  
**维护人员**: 开发团队  
