# 采购系统DDD重写 - 业务需求分析 (Kotlin实现)

## 1. 长远业务愿景

### 1.1 战略定位
构建一个智能化、平台化的B2B采购生态系统，成为连接全球买家、供应商和服务商的数字化基础设施。

### 1.2 长远价值主张
- **买家价值**：智能供应商匹配、透明化采购流程、风险可控的交易环境
- **供应商价值**：精准商机推送、数字化营销工具、全球市场准入
- **服务商价值**：专业化服务平台、智能化运营工具、生态化收益模式
- **平台价值**：数据驱动决策、生态协同效应、可持续商业模式

### 1.3 发展方向
- **智能化**：AI驱动的需求匹配、风险评估、价格预测
- **生态化**：构建完整的采购服务生态链
- **全球化**：支持跨境贸易、多语言、多币种
- **标准化**：建立行业标准和最佳实践

## 2. 业务领域分析

### 2.1 核心业务流程

#### 2.1.1 传统采购流程
```
采购需求发布 → 供应商竞价 → 管理员审核 → 采购商选择中标者 → 生成采购订单
    ↓
自动生成货代需求 → 货代竞价 → 审核 → 选择货代 → 生成货代订单 → 多方结算
    ↓
订单完成 → 自动入库 → 建立供应商信任关系
```

#### 2.1.2 基于信任关系的补货流程
```
库存预警 → 推荐信任供应商 → 买家确认数量和价格 → 一键生成订单 → 直接通知供应商
    ↓
订单执行 → 货物入库 → 更新信任评分
```

### 2.2 用户角色定义

#### 2.2.1 采购商（Buyer）
- **权限**：发布采购需求、查看竞价、选择中标者、管理订单、管理库存、一键补货
- **核心需求**：快速找到优质供应商，控制采购成本和质量，智能化库存管理，基于信任关系的快速补货

#### 2.2.2 供应商（Seller）  
- **权限**：查看需求、提交竞价、管理订单、上传产品资料
- **核心需求**：获得商机、展示产品优势、及时收款

#### 2.2.3 货代商（Forwarder）
- **权限**：查看货代需求、提交运输方案、管理运输订单
- **核心需求**：承接运输业务、优化运输成本、提供增值服务

#### 2.2.4 系统管理员（Admin）
- **权限**：审核竞价、管理用户、系统配置、数据分析
- **核心需求**：维护平台秩序、提升用户体验、监控业务指标

## 3. 功能需求

### 3.1 用户管理功能
- **用户注册登录**：支持邮箱/手机号注册，JWT认证
- **用户资料管理**：基本信息、公司信息、资质认证
- **权限管理**：基于角色的访问控制（RBAC）
- **收款账户管理**：支持多种收款方式

### 3.2 需求管理功能
- **需求发布**：支持详细的产品规格、数量、交期要求
- **需求分类**：按产品类别组织需求
- **需求搜索**：多维度搜索和筛选
- **需求状态管理**：草稿、发布、竞价中、已完成等状态

### 3.3 竞价交易功能
- **竞价投标**：供应商提交报价和产品方案
- **样品竞价**：支持样品采购的特殊流程
- **竞价审核**：管理员审核竞价合规性
- **中标选择**：采购商选择最优方案
- **竞价统计**：竞价数据分析和报表

### 3.4 订单履约功能
- **订单创建**：基于中标竞价自动生成订单
- **订单确认**：买卖双方确认订单条款
- **支付管理**：定金和尾款分期支付
- **订单跟踪**：实时跟踪订单执行进度
- **样品订单**：简化的样品采购流程

### 3.5 物流运输功能
- **货代需求**：基于采购订单自动生成
- **货代竞价**：货代提交运输方案和报价
- **路线优化**：智能推荐最优运输路线
- **运输跟踪**：实时跟踪货物运输状态
- **港口管理**：全球港口信息数据库

### 3.6 财务结算功能
- **多方结算**：采购商、供应商、货代三方结算
- **佣金计算**：基于订单金额计算平台佣金
- **支付凭证**：上传和审核支付凭证
- **财务报表**：收支明细和统计分析

### 3.7 通信协作功能
- **实时聊天**：基于WebSocket的即时通讯
- **消息通知**：业务事件触发的系统通知
- **文件共享**：聊天中的文件传输和共享
- **消息历史**：聊天记录的存储和搜索

### 3.8 库存管理功能
- **库存录入**：买家录入现有库存信息，关联原始采购记录
- **库存监控**：实时监控库存水位，自动预警低库存商品
- **信任关系管理**：基于历史采购建立买家-供应商信任关系
- **一键补货**：信任供应商享受跳过竞价的快速补货流程
- **定制化补货**：上传PM PDF文档，先付定金后供应商完善订单详情
- **智能补货建议**：基于消耗模式和历史价格提供补货建议
- **供应商信任评分**：基于交易历史、质量表现、交期准确性评分
- **文档智能解析**：AI辅助解析PDF需求文档，提取关键产品规格
- **库存分析**：ABC分析、周转率分析、滞销商品识别
- **补货价格保护**：限制补货价格涨幅，超出范围自动转竞价流程

### 3.9 基础设施功能
- **文件管理**：集成阿里云OSS的文件存储
- **系统配置**：灵活的系统参数配置
- **数据分析**：业务数据统计和可视化
- **日志审计**：操作日志记录和审计

## 4. 长远质量属性

### 4.1 可扩展性（Scalability）
- **业务扩展**：支持新业务模式、新产品类型、新服务形态
- **地域扩展**：支持多区域部署、就近服务、数据本地化
- **用户扩展**：支持百万级用户、千万级交易、PB级数据
- **技术扩展**：支持新技术栈、新架构模式、新集成方式

### 4.2 可维护性（Maintainability）
- **模块化架构**：清晰的模块边界，低耦合高内聚
- **代码质量**：高测试覆盖率、清晰的代码结构、完善的文档
- **监控可观测**：全链路监控、性能分析、业务指标跟踪
- **故障处理**：快速定位、自动恢复、预防性维护

### 4.3 可用性（Availability）
- **高可用架构**：99.99%可用性目标，支持多活部署
- **容灾能力**：跨区域容灾、数据备份、业务连续性
- **性能保障**：毫秒级响应、高并发支持、弹性伸缩
- **用户体验**：渐进式加载、离线能力、智能缓存

### 4.4 安全性（Security）
- **数据安全**：端到端加密、数据脱敏、隐私保护
- **访问控制**：零信任架构、细粒度权限、动态授权
- **合规要求**：GDPR、SOX、等保合规、审计追踪
- **威胁防护**：DDoS防护、入侵检测、安全扫描

### 4.5 互操作性（Interoperability）
- **标准协议**：RESTful API、GraphQL、消息队列标准
- **数据格式**：JSON、XML、EDI等标准格式支持
- **第三方集成**：ERP、CRM、财务系统、物流系统
- **生态开放**：开放平台、API市场、合作伙伴集成

## 5. 约束条件

### 5.1 技术约束
- 基于Kotlin + JVM生态系统
- 使用Spring Boot 3.5.4 + Kotlin 2.1.x
- PostgreSQL作为主数据库（支持JSON和数组类型）
- Redis作为缓存和会话存储
- Elasticsearch作为搜索引擎
- Kafka作为消息队列
- 支持Docker容器化部署

### 5.2 业务约束
- 保持现有业务流程不变
- 数据迁移零丢失
- 用户体验平滑过渡
- 支持现有API兼容性

### 5.3 时间约束
- 项目周期：16-20周
- 分阶段交付
- 关键里程碑不可延期

## 6. 验收标准

### 6.1 功能验收
- 所有核心功能正常运行
- 业务流程端到端测试通过
- 用户界面友好易用
- API接口文档完整

### 6.2 性能验收
- 满足性能需求指标
- 负载测试通过
- 内存和CPU使用率合理
- 数据库查询优化

### 6.3 安全验收
- 安全测试通过
- 权限控制有效
- 数据加密正确
- 审计日志完整

### 6.4 运维验收
- 部署脚本完整
- 监控告警配置
- 备份恢复流程
- 运维文档齐全

## 7. Kotlin业务建模设计

### 7.1 核心业务实体建模

#### 7.1.1 用户角色建模
```kotlin
// 使用密封类定义用户角色
sealed class UserRole {
    object Buyer : UserRole()
    object Seller : UserRole()
    object Forwarder : UserRole()
    object Admin : UserRole()

    val permissions: Set<Permission>
        get() = when (this) {
            is Buyer -> setOf(
                Permission.PUBLISH_REQUIREMENT,
                Permission.SELECT_WINNER,
                Permission.MANAGE_INVENTORY
            )
            is Seller -> setOf(
                Permission.VIEW_REQUIREMENTS,
                Permission.SUBMIT_BID,
                Permission.MANAGE_PRODUCTS
            )
            is Forwarder -> setOf(
                Permission.VIEW_LOGISTICS_NEEDS,
                Permission.SUBMIT_LOGISTICS_BID
            )
            is Admin -> Permission.values().toSet()
        }
}

// 使用枚举定义权限
enum class Permission {
    PUBLISH_REQUIREMENT,
    VIEW_REQUIREMENTS,
    SUBMIT_BID,
    SELECT_WINNER,
    MANAGE_INVENTORY,
    MANAGE_PRODUCTS,
    VIEW_LOGISTICS_NEEDS,
    SUBMIT_LOGISTICS_BID,
    SYSTEM_ADMIN
}
```

#### 7.1.2 业务状态建模
```kotlin
// 使用密封类定义需求状态
sealed class RequirementStatus {
    object Draft : RequirementStatus()
    object Published : RequirementStatus()
    object Bidding : RequirementStatus()
    object Evaluating : RequirementStatus()
    object Completed : RequirementStatus()
    object Cancelled : RequirementStatus()

    // 状态转换验证
    fun canTransitionTo(newStatus: RequirementStatus): Boolean = when (this) {
        is Draft -> newStatus is Published || newStatus is Cancelled
        is Published -> newStatus is Bidding || newStatus is Cancelled
        is Bidding -> newStatus is Evaluating || newStatus is Cancelled
        is Evaluating -> newStatus is Completed || newStatus is Cancelled
        is Completed -> false
        is Cancelled -> false
    }
}

// 使用密封类定义订单状态
sealed class OrderStatus {
    object Created : OrderStatus()
    object Confirmed : OrderStatus()
    object InProduction : OrderStatus()
    object Shipped : OrderStatus()
    object Delivered : OrderStatus()
    object Completed : OrderStatus()

    data class Cancelled(val reason: String) : OrderStatus()
}
```

#### 7.1.3 业务值对象建模
```kotlin
// 使用数据类定义金额值对象
@JvmInline
value class Money(private val cents: Long) {
    constructor(amount: BigDecimal, currency: Currency = Currency.getInstance("CNY")) : this(
        (amount * BigDecimal(100)).toLong()
    )

    val amount: BigDecimal get() = BigDecimal(cents).divide(BigDecimal(100))

    operator fun plus(other: Money): Money = Money(cents + other.cents)
    operator fun minus(other: Money): Money = Money(cents - other.cents)
    operator fun times(multiplier: Int): Money = Money(cents * multiplier)

    companion object {
        val ZERO = Money(0L)

        fun BigDecimal.cny(): Money = Money(this)
        fun Int.cny(): Money = Money(this.toBigDecimal())
    }
}

// 使用数据类定义地址值对象
data class Address(
    val country: String,
    val province: String,
    val city: String,
    val district: String,
    val street: String,
    val postalCode: String
) {
    val fullAddress: String
        get() = "$country $province $city $district $street $postalCode"

    fun isInSameCity(other: Address): Boolean =
        country == other.country && province == other.province && city == other.city
}

// 使用数据类定义联系方式值对象
data class ContactInfo(
    val email: String,
    val phone: String,
    val wechat: String? = null
) {
    init {
        require(email.isValidEmail()) { "Invalid email format" }
        require(phone.isValidPhone()) { "Invalid phone format" }
    }

    private fun String.isValidEmail(): Boolean =
        matches(Regex("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"))

    private fun String.isValidPhone(): Boolean =
        matches(Regex("^\\+?[1-9]\\d{1,14}$"))
}
```

### 7.2 业务规则建模

#### 7.2.1 竞价规则
```kotlin
// 使用数据类定义竞价规则
data class BiddingRules(
    val minBidAmount: Money,
    val maxBidAmount: Money,
    val biddingDuration: Duration,
    val allowedBidderTypes: Set<UserRole>,
    val requireSamples: Boolean = false
) {
    fun validateBid(bid: Bid, bidder: User): Result<Unit> = runCatching {
        require(bidder.role in allowedBidderTypes) {
            "User role ${bidder.role} is not allowed to bid"
        }

        require(bid.amount in minBidAmount..maxBidAmount) {
            "Bid amount ${bid.amount} is not within allowed range [$minBidAmount, $maxBidAmount]"
        }

        if (requireSamples) {
            require(bid.sampleInfo != null) {
                "Sample information is required for this bidding"
            }
        }
    }
}

// 使用扩展函数定义范围检查
operator fun Money.rangeTo(other: Money): ClosedRange<Money> =
    object : ClosedRange<Money> {
        override val start: Money = this@rangeTo
        override val endInclusive: Money = other
        override fun contains(value: Money): Boolean =
            value.amount >= start.amount && value.amount <= endInclusive.amount
    }
```

#### 7.2.2 信任关系建模
```kotlin
// 使用数据类定义信任关系
data class TrustRelationship(
    val buyerId: UserId,
    val sellerId: UserId,
    val trustScore: TrustScore,
    val establishedAt: LocalDateTime,
    val lastUpdated: LocalDateTime,
    val transactionHistory: List<TransactionRecord>
) {
    // 使用扩展属性定义业务逻辑
    val isHighTrust: Boolean get() = trustScore.value >= 80
    val canAutoReplenish: Boolean get() = isHighTrust && transactionHistory.size >= 5

    fun updateTrustScore(newTransaction: TransactionRecord): TrustRelationship {
        val newScore = calculateNewTrustScore(newTransaction)
        return copy(
            trustScore = newScore,
            lastUpdated = LocalDateTime.now(),
            transactionHistory = transactionHistory + newTransaction
        )
    }

    private fun calculateNewTrustScore(transaction: TransactionRecord): TrustScore {
        // 信任分数计算逻辑
        val baseScore = trustScore.value
        val adjustment = when {
            transaction.isSuccessful && transaction.isOnTime -> 5
            transaction.isSuccessful && !transaction.isOnTime -> 2
            !transaction.isSuccessful -> -10
            else -> 0
        }
        return TrustScore((baseScore + adjustment).coerceIn(0, 100))
    }
}

@JvmInline
value class TrustScore(val value: Int) {
    init {
        require(value in 0..100) { "Trust score must be between 0 and 100" }
    }

    val level: TrustLevel
        get() = when (value) {
            in 0..20 -> TrustLevel.VERY_LOW
            in 21..40 -> TrustLevel.LOW
            in 41..60 -> TrustLevel.MEDIUM
            in 61..80 -> TrustLevel.HIGH
            in 81..100 -> TrustLevel.VERY_HIGH
            else -> TrustLevel.UNKNOWN
        }
}

enum class TrustLevel {
    VERY_LOW, LOW, MEDIUM, HIGH, VERY_HIGH, UNKNOWN
}
```

### 7.3 业务事件建模

#### 7.3.1 领域事件定义
```kotlin
// 使用密封类定义领域事件
sealed interface DomainEvent {
    val eventId: String
    val occurredAt: LocalDateTime
    val aggregateId: String
    val aggregateType: String
    val version: Long
}

// 需求相关事件
sealed class RequirementEvent : DomainEvent {
    data class RequirementCreated(
        override val eventId: String = UUID.randomUUID().toString(),
        override val occurredAt: LocalDateTime = LocalDateTime.now(),
        override val aggregateId: String,
        override val version: Long,
        val buyerId: UserId,
        val title: String,
        val category: String,
        val estimatedValue: Money
    ) : RequirementEvent() {
        override val aggregateType: String = "ProcurementRequirement"
    }

    data class BidSubmitted(
        override val eventId: String = UUID.randomUUID().toString(),
        override val occurredAt: LocalDateTime = LocalDateTime.now(),
        override val aggregateId: String,
        override val version: Long,
        val bidId: String,
        val sellerId: UserId,
        val bidAmount: Money,
        val deliveryTime: Duration
    ) : RequirementEvent() {
        override val aggregateType: String = "ProcurementRequirement"
    }
}

// 订单相关事件
sealed class OrderEvent : DomainEvent {
    data class OrderCreated(
        override val eventId: String = UUID.randomUUID().toString(),
        override val occurredAt: LocalDateTime = LocalDateTime.now(),
        override val aggregateId: String,
        override val version: Long,
        val buyerId: UserId,
        val sellerId: UserId,
        val totalAmount: Money
    ) : OrderEvent() {
        override val aggregateType: String = "Order"
    }
}
```

这个Kotlin业务建模设计充分利用了Kotlin的语言特性，包括密封类、数据类、值类、扩展函数等，为采购系统提供了类型安全、简洁优雅的业务模型基础。
