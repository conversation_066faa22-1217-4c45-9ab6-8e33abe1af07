# 采购系统DDD重写 - 开发标准规范

## 1. 长远代码组织原则

### 1.1 模块化架构
```
purchase-system/
├── purchase-system-shared/              # 共享内核
├── purchase-system-identity/            # 身份认证上下文
├── purchase-system-procurement/         # 采购需求上下文
├── purchase-system-procurement-bidding/ # 采购竞价上下文
├── purchase-system-order/              # 订单履约上下文
├── purchase-system-logistics/          # 物流运输上下文
├── purchase-system-logistics-bidding/  # 物流竞价上下文
├── purchase-system-settlement/         # 财务结算上下文
├── purchase-system-communication/      # 通信协作上下文
└── purchase-system-platform/          # 平台启动模块
```

### 1.2 设计原则
- **单一职责**：每个模块专注于一个业务领域
- **开闭原则**：对扩展开放，对修改封闭
- **依赖倒置**：依赖抽象而非具体实现
- **接口隔离**：客户端不应依赖它不需要的接口

### 1.2 模块内部结构
```
purchase-system-identity/
├── src/main/kotlin/com/purchase/identity/
│   ├── interfaces/                  # 接口层
│   │   ├── rest/                   # REST控制器 (协程支持)
│   │   ├── event/                  # 事件监听器 (协程处理)
│   │   └── dto/                    # 数据传输对象 (数据类)
│   ├── application/                # 应用层
│   │   ├── service/                # 应用服务 (suspend函数)
│   │   ├── command/                # 命令对象 (数据类)
│   │   └── query/                  # 查询对象 (数据类)
│   ├── domain/                     # 领域层
│   │   ├── model/                  # 领域模型 (数据类/密封类)
│   │   ├── service/                # 领域服务 (扩展函数)
│   │   ├── repository/             # 仓储接口 (协程支持)
│   │   └── event/                  # 领域事件 (密封类)
│   └── infrastructure/             # 基础设施层
│       ├── repository/             # 仓储实现 (协程优化)
│       ├── config/                 # 配置 (数据类)
│       └── external/               # 外部服务适配器 (协程客户端)
└── src/test/kotlin/                # 测试代码 (协程测试)
```

## 2. 命名规范

### 2.1 包命名
- **基础包名**：`com.purchase.{context}`
- **接口层**：`com.purchase.{context}.interfaces`
- **应用层**：`com.purchase.{context}.application`
- **领域层**：`com.purchase.{context}.domain`
- **基础设施层**：`com.purchase.{context}.infrastructure`

### 2.2 类命名
- **聚合根**：`{EntityName}` (如：`User`, `ProcurementRequirement`)
- **实体**：`{EntityName}` (如：`UserProfile`, `RequirementItem`)
- **值对象**：`{ValueName}` (如：`Email`, `Money`, `Address`)
- **领域服务**：`{ServiceName}Service` (如：`AuthenticationService`)
- **应用服务**：`{Context}ApplicationService` (如：`UserApplicationService`)
- **仓储接口**：`{EntityName}Repository` (如：`UserRepository`)
- **仓储实现**：`{EntityName}RepositoryImpl` (如：`UserRepositoryImpl`)

### 2.3 方法命名 (Kotlin风格)
- **命令方法**：动词开头 (如：`createUser`, `updateProfile`, `deleteOrder`)
- **查询方法**：`get`/`find`开头 (如：`getUserById`, `findUsersByRole`)
- **布尔方法**：`is`/`has`/`can`开头，使用属性语法 (如：`val isActive`, `val hasPermission`)
- **扩展函数**：动词开头，体现扩展的功能 (如：`User.activate()`, `String.toEmail()`)

## 3. DDD建模规范

### 3.1 聚合根设计 (Kotlin风格)
```kotlin
@Entity
@Table(name = "users")
class User(
    @EmbeddedId
    val id: UserId,

    @Embedded
    var email: Email,

    @Embedded
    var profile: UserProfile,

    @ElementCollection
    @Enumerated(EnumType.STRING)
    private val _roles: MutableSet<Role> = mutableSetOf()
) : AggregateRoot<UserId>() {

    // 只读属性暴露角色集合
    val roles: Set<Role> get() = _roles.toSet()

    // 使用init块进行初始化验证
    init {
        // 发布领域事件
        registerEvent(UserCreatedEvent(id, email))
    }

    // 业务方法 - 使用Kotlin的简洁语法
    fun updateProfile(newProfile: UserProfile) {
        val oldProfile = profile
        profile = newProfile

        registerEvent(UserProfileUpdatedEvent(id, oldProfile, newProfile))
    }

    // 使用扩展属性提供便利访问
    val isActive: Boolean get() = profile.status == UserStatus.ACTIVE
    val hasAdminRole: Boolean get() = Role.ADMIN in roles

    // 使用高阶函数处理角色操作
    fun addRole(role: Role): User = apply {
        if (role !in _roles) {
            _roles.add(role)
            registerEvent(UserRoleAddedEvent(id, role))
        }
    }

    fun removeRole(role: Role): User = apply {
        if (_roles.remove(role)) {
            registerEvent(UserRoleRemovedEvent(id, role))
        }
    }

    // JPA需要的无参构造函数
    protected constructor() : this(
        UserId.empty(),
        Email.empty(),
        UserProfile.empty()
    )
}
```

### 3.2 值对象设计 (Kotlin数据类)
```kotlin
@Embeddable
data class Email(
    @Column(name = "email")
    val value: String
) {
    // 使用init块进行验证
    init {
        require(isValidEmail(value)) {
            "Invalid email format: $value"
        }
    }

    // 使用扩展属性提供便利访问
    val domain: String get() = value.substringAfter("@")
    val localPart: String get() = value.substringBefore("@")

    // 使用伴生对象提供工厂方法
    companion object {
        fun empty(): Email = Email("")

        fun fromString(email: String): Email = Email(email)

        // 使用扩展函数提供DSL风格的构造
        fun String.toEmail(): Email = Email(this)

        private fun isValidEmail(email: String): Boolean {
            return email.matches(Regex("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"))
        }
    }

    // 数据类自动生成equals、hashCode、toString
    // 无需手动实现
}

// 使用值类优化性能的简单值对象
@JvmInline
value class UserId(val value: Long) {
    init {
        require(value > 0) { "UserId must be positive" }
    }

    companion object {
        fun empty(): UserId = UserId(0L)
        fun generate(): UserId = UserId(System.currentTimeMillis())
    }
}
```

### 3.3 领域服务设计 (Kotlin风格)
```kotlin
@Component
class AuthenticationService(
    private val passwordEncoder: PasswordEncoder,
    private val tokenProvider: JwtTokenProvider,
    private val userRepository: UserRepository
) {

    suspend fun authenticate(email: Email, password: String): Result<AuthenticationResult> =
        runCatching {
            // 使用扩展函数让查找更自然
            val user = userRepository.findByEmailOrThrow(email)

            // 使用require进行验证
            require(passwordEncoder.matches(password, user.passwordHash)) {
                "Invalid password"
            }

            require(user.isActive) {
                "User account is inactive"
            }

            // 生成令牌并返回结果
            val token = tokenProvider.generateToken(user)
            AuthenticationResult(user.id, token)
        }

    // 使用扩展函数让仓储操作更自然
    private suspend fun UserRepository.findByEmailOrThrow(email: Email): User =
        findByEmail(email) ?: throw UserNotFoundException("User not found: ${email.value}")

    // 使用密封类表示认证结果
    sealed class AuthenticationResult {
        data class Success(val userId: UserId, val token: String) : AuthenticationResult()
        data class Failure(val reason: String) : AuthenticationResult()
    }
}
```

### 3.4 仓储接口设计 (Kotlin协程支持)
```kotlin
interface UserRepository : Repository<User, UserId> {

    // 基本CRUD操作 - 使用协程
    suspend fun save(user: User)
    suspend fun findById(id: UserId): User?
    suspend fun delete(user: User)

    // 业务查询方法 - 返回可空类型而非Optional
    suspend fun findByEmail(email: Email): User?
    suspend fun findByRole(role: Role): List<User>
    suspend fun findByStatus(status: UserStatus, pageable: Pageable): Page<User>

    // 复杂查询方法
    suspend fun findActiveUsersByCompany(company: String): List<User>
    suspend fun countByRoleAndStatus(role: Role, status: UserStatus): Long

    // 使用扩展函数提供便利方法
    suspend fun findByEmailOrThrow(email: Email): User =
        findByEmail(email) ?: throw UserNotFoundException("User not found: ${email.value}")

    // 批量操作
    suspend fun saveAll(users: List<User>): List<User>
    suspend fun findAllById(ids: List<UserId>): List<User>
}

// 使用扩展函数为仓储添加便利方法
suspend fun UserRepository.existsByEmail(email: Email): Boolean =
    findByEmail(email) != null

suspend fun UserRepository.findActiveUsers(): List<User> =
    findByStatus(UserStatus.ACTIVE, Pageable.unpaged()).content
```

## 4. Spring Data JPA + Hibernate + Kotlin 开发规范

### 4.1 实体类设计规范

#### 4.1.1 聚合根实体 (Kotlin + Hibernate 6.6.x)
```kotlin
// 使用Kotlin + Hibernate 6.6.x的现代特性
@Entity
@Table(name = "procurement_requirements")
class ProcurementRequirement(
    // 使用嵌入式ID（强类型ID）
    @EmbeddedId
    val id: RequirementId,

    // 基础属性
    @Column(name = "title", nullable = false, length = 200)
    var title: String,

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    var status: RequirementStatus,

    // 利用PostgreSQL的JSON类型存储复杂值对象
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "specification", columnDefinition = "jsonb")
    var specification: RequirementSpecification,

    // 利用PostgreSQL数组类型
    @JdbcTypeCode(SqlTypes.ARRAY)
    @Column(name = "tags", columnDefinition = "text[]")
    var tags: MutableList<String> = mutableListOf(),

    // 聚合内实体的级联操作
    @OneToMany(cascade = [CascadeType.ALL], fetch = FetchType.LAZY,
               mappedBy = "requirement", orphanRemoval = true)
    private val _items: MutableList<RequirementItem> = mutableListOf(),

    // 审计字段
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime? = null,

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime? = null,

    @Version
    @Column(name = "version")
    var version: Long? = null

) : AggregateRoot<RequirementId>() {

    // 只读属性暴露items集合
    val items: List<RequirementItem> get() = _items.toList()

    // 使用扩展属性提供便利访问
    val isDraft: Boolean get() = status == RequirementStatus.DRAFT
    val isSubmitted: Boolean get() = status == RequirementStatus.SUBMITTED
    val totalEstimatedValue: Money get() = items.sumOf { it.estimatedPrice }

    // 业务方法 - 使用Kotlin的简洁语法
    fun addItem(item: RequirementItem): ProcurementRequirement = apply {
        _items.add(item)
        item.requirement = this
        addDomainEvent(RequirementItemAddedEvent(id, item.id))
    }

    fun submit(): Result<Unit> = runCatching {
        require(isDraft) { "只有草稿状态的需求才能提交" }

        status = RequirementStatus.SUBMITTED
        addDomainEvent(RequirementSubmittedEvent(id))
    }

    // JPA自动发布领域事件
    @DomainEvents
    fun domainEvents(): Collection<DomainEvent> = uncommittedEvents

    @AfterDomainEventPublication
    fun clearDomainEvents() {
        markEventsAsCommitted()
    }

    // JPA需要的无参构造函数
    protected constructor() : this(
        RequirementId.empty(),
        "",
        RequirementStatus.DRAFT,
        RequirementSpecification.empty()
    )
}
```

#### 4.1.2 值对象设计 (使用Kotlin数据类)
```kotlin
// 使用Kotlin数据类实现值对象 - 比传统类更强大
data class RequirementSpecification(
    @field:NotBlank
    val description: String,

    @field:NotNull
    val technicalSpecs: Map<String, Any>,

    @field:NotNull
    val quality: QualityRequirements,

    @field:NotNull
    val delivery: DeliveryRequirements
) {
    // 数据类自动提供：
    // - 不可变性
    // - equals()和hashCode()
    // - toString()
    // - copy()方法

    // 构造函数验证
    init {
        require(description.isNotBlank()) { "描述不能为空" }
        require(technicalSpecs.isNotEmpty()) { "技术规格不能为空" }
    }

    // 业务方法 - 使用copy()方法
    fun updateDescription(newDescription: String): RequirementSpecification =
        copy(description = newDescription)

    // 使用扩展属性提供便利访问
    val hasQualityRequirements: Boolean get() = quality.requirements.isNotEmpty()
    val isUrgentDelivery: Boolean get() = delivery.isUrgent
}

// 嵌套值对象 - 使用Kotlin数据类
data class Money(
    @field:NotNull @field:DecimalMin("0.00")
    val amount: BigDecimal,

    @field:NotNull
    val currency: Currency
) {
    // 构造函数验证
    init {
        require(amount >= BigDecimal.ZERO) { "金额不能为负数" }
    }

    // 运算符重载 - 更自然的语法
    operator fun plus(other: Money): Money {
        requireSameCurrency(other)
        return Money(amount + other.amount, currency)
    }

    operator fun compareTo(other: Money): Int {
        requireSameCurrency(other)
        return amount.compareTo(other.amount)
    }

    // 私有辅助方法
    private fun requireSameCurrency(other: Money) {
        require(currency == other.currency) { "货币类型不匹配: $currency vs ${other.currency}" }
    }

    // 扩展属性
    val isZero: Boolean get() = amount == BigDecimal.ZERO
    val isPositive: Boolean get() = amount > BigDecimal.ZERO

    companion object {
        val ZERO = Money(BigDecimal.ZERO, Currency.getInstance("CNY"))

        // 扩展函数提供DSL风格的构造
        fun BigDecimal.cny() = Money(this, Currency.getInstance("CNY"))
        fun BigDecimal.usd() = Money(this, Currency.getInstance("USD"))
    }
}
```

#### 4.1.3 强类型ID设计
```kotlin
// 使用值类实现强类型ID - 零开销抽象
@JvmInline
value class RequirementId(val value: String) {

    init {
        require(value.isNotBlank()) { "RequirementId不能为空" }
    }

    companion object {
        fun generate(): RequirementId = RequirementId(UUID.randomUUID().toString())

        fun of(value: String): RequirementId = RequirementId(value)

        fun empty(): RequirementId = RequirementId("")
    }
}

// 在实体中使用 - 直接嵌入
@Embeddable
data class RequirementIdEmbeddable(
    @Column(name = "id")
    val value: String
) {
    // JPA需要的默认构造函数
    constructor() : this("")

    constructor(id: RequirementId) : this(id.value)

    fun toRequirementId(): RequirementId = RequirementId.of(value)
}

// 或者使用属性转换器
@Converter(autoApply = true)
class RequirementIdConverter : AttributeConverter<RequirementId, String> {

    override fun convertToDatabaseColumn(attribute: RequirementId?): String? =
        attribute?.value

    override fun convertToEntityAttribute(dbData: String?): RequirementId? =
        dbData?.let { RequirementId.of(it) }
}
```

### 4.2 Repository设计规范

#### 4.2.1 Repository接口定义
```kotlin
// 继承JpaRepository获得基础CRUD操作 - 使用协程支持
interface ProcurementRequirementRepository :
    JpaRepository<ProcurementRequirement, RequirementId> {

    // 方法名自动生成查询 - 使用协程
    suspend fun findByStatus(status: RequirementStatus): List<ProcurementRequirement>

    suspend fun findByStatusAndCreatedAtBetween(
        status: RequirementStatus,
        start: LocalDateTime,
        end: LocalDateTime
    ): List<ProcurementRequirement>

    suspend fun findByIdAndStatus(
        id: RequirementId,
        status: RequirementStatus
    ): ProcurementRequirement?

    // 使用@Query进行复杂查询
    @Query("""
        SELECT r FROM ProcurementRequirement r
        WHERE JSON_EXTRACT(r.specification, '$.category') = :category
        AND r.status = :status
        """)
    suspend fun findBySpecificationCategoryAndStatus(
        @Param("category") category: String,
        @Param("status") status: RequirementStatus
    ): List<ProcurementRequirement>

    // 使用原生SQL查询PostgreSQL特性
    @Query(value = """
        SELECT * FROM procurement_requirements
        WHERE specification @> :specFilter::jsonb
        AND tags && :tags::text[]
        """, nativeQuery = true)
    suspend fun findByJsonSpecificationAndTags(
        @Param("specFilter") specificationFilter: String,
        @Param("tags") tags: Array<String>
    ): List<ProcurementRequirement>

    // 自定义查询方法
    @Query("""
        SELECT r FROM ProcurementRequirement r
        LEFT JOIN FETCH r.items
        WHERE r.id = :id
        """)
    suspend fun findByIdWithItems(@Param("id") id: RequirementId): ProcurementRequirement?

    // 统计查询
    @Query("SELECT COUNT(r) FROM ProcurementRequirement r WHERE r.status = :status")
    suspend fun countByStatus(@Param("status") status: RequirementStatus): Long

    // 批量操作
    @Modifying
    @Query("UPDATE ProcurementRequirement r SET r.status = :newStatus WHERE r.status = :oldStatus")
    suspend fun updateStatusBatch(
        @Param("oldStatus") oldStatus: RequirementStatus,
        @Param("newStatus") newStatus: RequirementStatus
    ): Int
}

// 扩展函数提供便利方法
suspend fun ProcurementRequirementRepository.findByIdOrThrow(id: RequirementId): ProcurementRequirement =
    findById(id).orElseThrow { RequirementNotFoundException("需求不存在: $id") }

suspend fun ProcurementRequirementRepository.existsByTitle(title: String): Boolean =
    findByTitle(title) != null
```

#### 4.2.2 自定义Repository实现
```kotlin
// 当需要复杂查询时，可以自定义实现
interface ProcurementRequirementRepositoryCustom {
    suspend fun findByComplexCriteria(
        criteria: RequirementSearchCriteria,
        pageable: Pageable
    ): Page<ProcurementRequirement>

    suspend fun getStatisticsByCategory(): List<RequirementStatistics>
}

@Repository
class ProcurementRequirementRepositoryImpl(
    @PersistenceContext
    private val entityManager: EntityManager
) : ProcurementRequirementRepositoryCustom {

    override suspend fun findByComplexCriteria(
        criteria: RequirementSearchCriteria,
        pageable: Pageable
    ): Page<ProcurementRequirement> = withContext(Dispatchers.IO) {

        val cb = entityManager.criteriaBuilder
        val query = cb.createQuery(ProcurementRequirement::class.java)
        val root = query.from(ProcurementRequirement::class.java)

        val predicates = mutableListOf<Predicate>()

        // 动态构建查询条件 - 使用Kotlin的简洁语法
        criteria.status?.let { status ->
            predicates.add(cb.equal(root.get<RequirementStatus>("status"), status))
        }

        criteria.title?.let { title ->
            predicates.add(cb.like(root.get("title"), "%$title%"))
        }

        criteria.dateRange?.let { dateRange ->
            predicates.add(cb.between(
                root.get("createdAt"),
                dateRange.start,
                dateRange.end
            ))
        }

        query.where(*predicates.toTypedArray())

        // 执行查询
        val typedQuery = entityManager.createQuery(query).apply {
            firstResult = pageable.offset.toInt()
            maxResults = pageable.pageSize
        }

        val results = typedQuery.resultList

        // 计算总数
        val countQuery = cb.createQuery(Long::class.java).apply {
            select(cb.count(from(ProcurementRequirement::class.java)))
            where(*predicates.toTypedArray())
        }
        val total = entityManager.createQuery(countQuery).singleResult

        PageImpl(results, pageable, total)
    }

    override suspend fun getStatisticsByCategory(): List<RequirementStatistics> =
        withContext(Dispatchers.IO) {
            // 使用原生查询获取统计信息
            val query = entityManager.createNativeQuery("""
                SELECT
                    specification->>'category' as category,
                    COUNT(*) as total_count,
                    AVG(CAST(specification->>'estimatedValue' AS DECIMAL)) as avg_value
                FROM procurement_requirements
                WHERE status = 'APPROVED'
                GROUP BY specification->>'category'
                ORDER BY total_count DESC
            """)

            query.resultList.map { row ->
                val result = row as Array<*>
                RequirementStatistics(
                    category = result[0] as String,
                    totalCount = (result[1] as Number).toLong(),
                    averageValue = (result[2] as Number).toBigDecimal()
                )
            }
        }
}
```

## 5. 应用层规范

### 5.1 应用服务设计
```kotlin
@Service
@Transactional
class UserApplicationService(
    private val userRepository: UserRepository,
    private val eventPublisher: DomainEventPublisher
) {

    suspend fun createUser(command: CreateUserCommand): UserResponse {
        // 1. 验证命令
        validateCommand(command)

        // 2. 检查业务规则 - 使用扩展函数
        userRepository.findByEmail(command.email)?.let {
            throw UserAlreadyExistsException("用户已存在: ${command.email}")
        }

        // 3. 创建聚合根 - 使用数据类构造
        val user = User.create(
            email = Email(command.email),
            profile = UserProfile(command.username, command.phone)
        )

        // 4. 保存聚合根
        userRepository.save(user)

        // 5. 发布领域事件 - 使用协程
        eventPublisher.publishEvents(user.uncommittedEvents)

        // 6. 返回响应
        return UserResponse.from(user)
    }

    // 使用Result类型处理错误
    suspend fun updateUserProfile(command: UpdateUserProfileCommand): Result<UserResponse> =
        runCatching {
            val user = userRepository.findByIdOrThrow(command.userId)
            user.updateProfile(UserProfile(command.username, command.phone))
            userRepository.save(user)
            UserResponse.from(user)
        }

    private fun validateCommand(command: CreateUserCommand) {
        require(command.email.isNotBlank()) { "邮箱不能为空" }
        require(command.username.isNotBlank()) { "用户名不能为空" }
        // 更多验证逻辑...
    }
}
```

### 5.2 命令对象设计
```kotlin
// 使用数据类实现命令对象 - 更简洁
data class CreateUserCommand(
    @field:NotBlank(message = "邮箱不能为空")
    @field:Email(message = "邮箱格式无效")
    val email: String,

    @field:NotBlank(message = "用户名不能为空")
    @field:Size(min = 2, max = 50, message = "用户名长度必须在2-50个字符之间")
    val username: String,

    @field:Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "手机号格式无效")
    val phone: String
) {
    // 数据类自动提供：
    // - 构造函数
    // - equals()和hashCode()
    // - toString()
    // - copy()方法

    // 业务验证方法
    fun validate(): List<String> {
        val errors = mutableListOf<String>()

        if (email.isBlank()) errors.add("邮箱不能为空")
        if (username.isBlank()) errors.add("用户名不能为空")
        if (phone.isBlank()) errors.add("手机号不能为空")

        return errors
    }

    // 扩展属性
    val isValid: Boolean get() = validate().isEmpty()
}
```

### 5.3 查询对象设计
```kotlin
// 使用数据类实现查询对象
data class UserSearchQuery(
    val email: String? = null,
    val role: Role? = null,
    val status: UserStatus? = null,
    val company: String? = null,
    val pageable: Pageable = Pageable.unpaged()
) {
    // 使用扩展属性提供便利访问
    val hasFilters: Boolean get() =
        email != null || role != null || status != null || company != null

    val isEmailSearch: Boolean get() = email != null
    val isRoleSearch: Boolean get() = role != null

    // 使用作用域函数构建查询
    fun withEmail(email: String): UserSearchQuery = copy(email = email)
    fun withRole(role: Role): UserSearchQuery = copy(role = role)
    fun withStatus(status: UserStatus): UserSearchQuery = copy(status = status)

    companion object {
        // 工厂方法
        fun forEmail(email: String): UserSearchQuery = UserSearchQuery(email = email)
        fun forRole(role: Role): UserSearchQuery = UserSearchQuery(role = role)
        fun forCompany(company: String): UserSearchQuery = UserSearchQuery(company = company)
    }
}

// 使用DSL风格的查询构建器
fun userSearchQuery(init: UserSearchQueryBuilder.() -> Unit): UserSearchQuery =
    UserSearchQueryBuilder().apply(init).build()

class UserSearchQueryBuilder {
    private var email: String? = null
    private var role: Role? = null
    private var status: UserStatus? = null
    private var company: String? = null
    private var pageable: Pageable = Pageable.unpaged()

    fun email(email: String) = apply { this.email = email }
    fun role(role: Role) = apply { this.role = role }
    fun status(status: UserStatus) = apply { this.status = status }
    fun company(company: String) = apply { this.company = company }
    fun pageable(pageable: Pageable) = apply { this.pageable = pageable }

    fun build(): UserSearchQuery = UserSearchQuery(email, role, status, company, pageable)
}
```

## 6. 接口层规范

### 6.1 REST控制器设计
```kotlin
@RestController
@RequestMapping("/api/v1/users")
@Validated
class UserController(
    private val userApplicationService: UserApplicationService,
    private val userQueryService: UserQueryService
) {

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    suspend fun createUser(
        @RequestBody @Valid request: CreateUserRequest
    ): ResponseEntity<Result<UserResponse>> {

        val command = CreateUserCommand(
            email = request.email,
            username = request.username,
            phone = request.phone
        )

        return userApplicationService.createUser(command)
            .let { response ->
                ResponseEntity.status(HttpStatus.CREATED)
                    .body(Result.success(response))
            }
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @securityService.canAccessUser(#id)")
    suspend fun getUser(@PathVariable id: Long): ResponseEntity<Result<UserResponse>> =
        userQueryService.getUserById(id)
            .let { response -> ResponseEntity.ok(Result.success(response)) }

    // 使用Result类型处理错误
    @PutMapping("/{id}/profile")
    suspend fun updateUserProfile(
        @PathVariable id: Long,
        @RequestBody @Valid request: UpdateUserProfileRequest
    ): ResponseEntity<Result<UserResponse>> {

        val command = UpdateUserProfileCommand(
            userId = UserId(id),
            username = request.username,
            phone = request.phone
        )

        return userApplicationService.updateUserProfile(command)
            .fold(
                onSuccess = { response ->
                    ResponseEntity.ok(Result.success(response))
                },
                onFailure = { error ->
                    ResponseEntity.badRequest()
                        .body(Result.failure<UserResponse>(error.message ?: "更新失败"))
                }
            )
    }

    // 使用协程处理批量操作
    @PostMapping("/batch")
    suspend fun createUsersBatch(
        @RequestBody @Valid requests: List<CreateUserRequest>
    ): ResponseEntity<Result<List<UserResponse>>> = coroutineScope {

        val responses = requests.map { request ->
            async {
                val command = CreateUserCommand(
                    email = request.email,
                    username = request.username,
                    phone = request.phone
                )
                userApplicationService.createUser(command)
            }
        }.awaitAll()

        ResponseEntity.ok(Result.success(responses))
    }
}
```

### 6.2 DTO设计
```kotlin
// 请求DTO - 使用数据类
data class CreateUserRequest(
    @field:NotBlank(message = "邮箱不能为空")
    @field:Email(message = "邮箱格式无效")
    val email: String,

    @field:NotBlank(message = "用户名不能为空")
    val username: String,

    val phone: String
) {
    // 数据类自动提供构造函数、getter、equals、hashCode、toString
}

// 更新请求DTO
data class UpdateUserProfileRequest(
    @field:NotBlank(message = "用户名不能为空")
    val username: String,

    val phone: String
)

// 响应DTO - 使用数据类
data class UserResponse(
    val id: Long,
    val email: String,
    val username: String,
    val phone: String,
    val roles: Set<String>,
    val status: String,
    val createdAt: LocalDateTime
) {
    companion object {
        fun from(user: User): UserResponse = UserResponse(
            id = user.id.value,
            email = user.email.value,
            username = user.profile.username,
            phone = user.profile.phone,
            roles = user.roles.map { it.name }.toSet(),
            status = user.status.name,
            createdAt = user.createdAt
        )

        // 批量转换
        fun fromList(users: List<User>): List<UserResponse> =
            users.map { from(it) }
    }

    // 扩展属性
    val isActive: Boolean get() = status == "ACTIVE"
    val hasAdminRole: Boolean get() = "ADMIN" in roles
}

// 分页响应DTO
data class PagedUserResponse(
    val content: List<UserResponse>,
    val totalElements: Long,
    val totalPages: Int,
    val size: Int,
    val number: Int,
    val first: Boolean,
    val last: Boolean
) {
    companion object {
        fun from(page: Page<User>): PagedUserResponse = PagedUserResponse(
            content = UserResponse.fromList(page.content),
            totalElements = page.totalElements,
            totalPages = page.totalPages,
            size = page.size,
            number = page.number,
            first = page.isFirst,
            last = page.isLast
        )
    }
}
```

## 6. 测试规范

### 6.1 单元测试
```kotlin
@ExtendWith(MockitoExtension::class)
class UserApplicationServiceTest {

    @Mock
    private lateinit var userRepository: UserRepository

    @Mock
    private lateinit var eventPublisher: DomainEventPublisher

    @InjectMocks
    private lateinit var userApplicationService: UserApplicationService

    @Test
    @DisplayName("应该成功创建用户")
    fun `should create user successfully`() = runTest {
        // Given
        val command = CreateUserCommand(
            email = "<EMAIL>",
            username = "testuser",
            phone = "+1234567890"
        )

        whenever(userRepository.findByEmail(any<Email>()))
            .thenReturn(null)

        // When
        val response = userApplicationService.createUser(command)

        // Then
        assertThat(response).isNotNull()
        assertThat(response.email).isEqualTo("<EMAIL>")
        assertThat(response.username).isEqualTo("testuser")

        verify(userRepository).save(any<User>())
        verify(eventPublisher).publishEvents(any())
    }

    @Test
    @DisplayName("当用户已存在时应该抛出异常")
    fun `should throw exception when user already exists`() = runTest {
        // Given
        val command = CreateUserCommand(
            email = "<EMAIL>",
            username = "existinguser",
            phone = "+1234567890"
        )

        val existingUser = User.create(
            email = Email("<EMAIL>"),
            profile = UserProfile("existinguser", "+1234567890")
        )

        whenever(userRepository.findByEmail(any<Email>()))
            .thenReturn(existingUser)

        // When & Then
        assertThrows<UserAlreadyExistsException> {
            userApplicationService.createUser(command)
        }.also { exception ->
            assertThat(exception.message).contains("<EMAIL>")
        }
    }

    @Test
    @DisplayName("应该正确处理批量创建")
    fun `should handle batch creation correctly`() = runTest {
        // Given
        val commands = listOf(
            CreateUserCommand("<EMAIL>", "user1", "+1111111111"),
            CreateUserCommand("<EMAIL>", "user2", "+2222222222"),
            CreateUserCommand("<EMAIL>", "user3", "+3333333333")
        )

        whenever(userRepository.findByEmail(any<Email>()))
            .thenReturn(null)

        // When
        val results = userApplicationService.createUsersBatch(commands)

        // Then
        assertThat(results).hasSize(3)
        assertThat(results.all { it.isSuccess }).isTrue()
        verify(userRepository, times(3)).save(any<User>())
    }
}
```

### 6.2 集成测试
```kotlin
@SpringBootTest
@Testcontainers
@Transactional
class UserIntegrationTest {

    companion object {
        @Container
        @JvmStatic
        val postgresql = PostgreSQLContainer("postgres:16")
            .withDatabaseName("test")
            .withUsername("test")
            .withPassword("test")
    }

    @Autowired
    private lateinit var userApplicationService: UserApplicationService

    @Autowired
    private lateinit var userRepository: UserRepository

    @Test
    @DisplayName("应该完整地创建和查询用户")
    fun `should create and retrieve user completely`() = runTest {
        // Given
        val command = CreateUserCommand(
            email = "<EMAIL>",
            username = "integrationuser",
            phone = "+1234567890"
        )

        // When
        val createdUser = userApplicationService.createUser(command)
        val retrievedUser = userRepository.findById(UserId(createdUser.id))

        // Then
        assertThat(retrievedUser).isNotNull()
        assertThat(retrievedUser!!.email.value)
            .isEqualTo("<EMAIL>")
    }

    @Test
    @DisplayName("应该正确处理事务回滚")
    fun `should handle transaction rollback correctly`() = runTest {
        // Given
        val command = CreateUserCommand(
            email = "<EMAIL>",
            username = "rollbackuser",
            phone = "+1234567890"
        )

        // When & Then
        assertThrows<RuntimeException> {
            userApplicationService.createUserWithError(command)
        }

        // 验证事务已回滚
        val user = userRepository.findByEmail(Email("<EMAIL>"))
        assertThat(user).isNull()
    }
}
```

## 7. 异常处理规范

### 7.1 异常层次结构
```kotlin
// 基础异常类 - 使用密封类
sealed class DomainException(
    message: String,
    cause: Throwable? = null
) : RuntimeException(message, cause)

// 业务异常 - 使用数据类
data class UserAlreadyExistsException(
    val email: String
) : DomainException("用户已存在: $email")

data class RequirementNotFoundException(
    val requirementId: RequirementId
) : DomainException("需求不存在: $requirementId")

data class InvalidStatusTransitionException(
    val fromStatus: RequirementStatus,
    val toStatus: RequirementStatus
) : DomainException("无效的状态转换: $fromStatus -> $toStatus")

// 验证异常
data class InvalidEmailException(
    val email: String
) : DomainException("邮箱格式无效: $email")

data class ValidationException(
    val field: String,
    val value: Any?,
    val rule: String
) : DomainException("字段验证失败: $field = $value, 规则: $rule")

// 使用密封类表示结果类型
sealed class DomainResult<out T> {
    data class Success<T>(val value: T) : DomainResult<T>()
    data class Failure(val exception: DomainException) : DomainResult<Nothing>()

    inline fun <R> map(transform: (T) -> R): DomainResult<R> = when (this) {
        is Success -> Success(transform(value))
        is Failure -> this
    }

    inline fun <R> flatMap(transform: (T) -> DomainResult<R>): DomainResult<R> = when (this) {
        is Success -> transform(value)
        is Failure -> this
    }

    fun getOrThrow(): T = when (this) {
        is Success -> value
        is Failure -> throw exception
    }
}
```

### 7.2 全局异常处理
```kotlin
@RestControllerAdvice
class GlobalExceptionHandler {

    private val logger = LoggerFactory.getLogger(GlobalExceptionHandler::class.java)

    @ExceptionHandler(DomainException::class)
    fun handleDomainException(e: DomainException): ResponseEntity<Result<Nothing>> =
        when (e) {
            is UserAlreadyExistsException -> ResponseEntity.status(HttpStatus.CONFLICT)
                .body(Result.failure("用户已存在: ${e.email}"))

            is RequirementNotFoundException -> ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(Result.failure("需求不存在: ${e.requirementId}"))

            is InvalidStatusTransitionException -> ResponseEntity.badRequest()
                .body(Result.failure("状态转换无效: ${e.fromStatus} -> ${e.toStatus}"))

            is ValidationException -> ResponseEntity.badRequest()
                .body(Result.failure("验证失败: ${e.field} = ${e.value}, 规则: ${e.rule}"))

            else -> ResponseEntity.badRequest()
                .body(Result.failure(e.message ?: "业务异常"))
        }

    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleValidationException(e: MethodArgumentNotValidException): ResponseEntity<Result<Nothing>> {
        val errors = e.bindingResult.fieldErrors.map { error ->
            "${error.field}: ${error.defaultMessage}"
        }

        return ResponseEntity.badRequest()
            .body(Result.failure("参数验证失败: ${errors.joinToString(", ")}"))
    }

    @ExceptionHandler(ConstraintViolationException::class)
    fun handleConstraintViolationException(e: ConstraintViolationException): ResponseEntity<Result<Nothing>> {
        val errors = e.constraintViolations.map { violation ->
            "${violation.propertyPath}: ${violation.message}"
        }

        return ResponseEntity.badRequest()
            .body(Result.failure("约束验证失败: ${errors.joinToString(", ")}"))
    }

    @ExceptionHandler(Exception::class)
    fun handleGenericException(e: Exception): ResponseEntity<Result<Nothing>> {
        logger.error("意外错误发生", e)
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(Result.failure("服务器内部错误"))
    }

    // 协程异常处理
    @ExceptionHandler(CancellationException::class)
    fun handleCancellationException(e: CancellationException): ResponseEntity<Result<Nothing>> {
        logger.warn("协程被取消", e)
        return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
            .body(Result.failure("请求超时"))
    }
}

// 结果包装类
data class Result<T>(
    val success: Boolean,
    val data: T? = null,
    val message: String? = null,
    val timestamp: LocalDateTime = LocalDateTime.now()
) {
    companion object {
        fun <T> success(data: T): Result<T> = Result(
            success = true,
            data = data
        )

        fun <T> failure(message: String): Result<T> = Result(
            success = false,
            message = message
        )

        fun <T> failure(exception: Exception): Result<T> = Result(
            success = false,
            message = exception.message ?: "未知错误"
        )
    }
}
```

## 8. 配置规范

### 8.1 配置文件组织
```yaml
# application.yml - 基于Spring Boot 3.5.4的现代化配置
spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:development}

  # PostgreSQL 16+ 配置
  datasource:
    url: ${DATABASE_URL:************************************************}
    username: ${DATABASE_USERNAME:postgres}
    password: ${DATABASE_PASSWORD:password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA + Hibernate 6.6.x 配置
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: ${JPA_DDL_AUTO:validate}
    show-sql: ${JPA_SHOW_SQL:false}
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
        # Hibernate 6.6.x 新特性
        connection.provider_disables_autocommit: true
        # JSON类型支持
        type.preferred_uuid_jdbc_type: VARCHAR
        # 启用批处理优化
        jdbc.batch_size: 20
        order_inserts: true
        order_updates: true

  # Redis 7.x 配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
      # Spring Boot 3.5.4 新增配置
      read-from: ${REDIS_READ_FROM:UPSTREAM}

  # Kafka 3.9.x 配置
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 5
      enable-idempotence: true
    consumer:
      group-id: purchase-system
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: false
      properties:
        spring.json.trusted.packages: "com.purchase.*"
    # Spring Boot 3.5.4 新增配置
    listener:
      auth-exception-retry-interval: 10s

# Elasticsearch 8.17+ 配置
elasticsearch:
  uris: ${ELASTICSEARCH_URIS:http://localhost:9200}
  username: ${ELASTICSEARCH_USERNAME:}
  password: ${ELASTICSEARCH_PASSWORD:}

# Spring Boot 3.5.4 新增的WebClient配置
spring:
  webclient:
    connect-timeout: 5s
    read-timeout: 30s
    write-timeout: 30s
    max-in-memory-size: 1MB

# 应用特定配置
purchase:
  jwt:
    secret: ${JWT_SECRET:your-secret-key}
    expiration: ${JWT_EXPIRATION:86400}

  file:
    upload-path: ${FILE_UPLOAD_PATH:/tmp/uploads}
    max-size: ${FILE_MAX_SIZE:10MB}

# Spring Boot 3.5.4 任务执行配置
spring:
  task:
    execution:
      mode: ${TASK_EXECUTION_MODE:auto}
      pool:
        core-size: 8
        max-size: 16
        queue-capacity: 100
        keep-alive: 60s
    scheduling:
      pool:
        size: 4

# 结构化日志配置 (Spring Boot 3.5.4 新特性)
logging:
  structured:
    json:
      enabled: true
      stacktrace:
        max-length: 2048
        format: short
      customizer: com.purchase.config.LoggingCustomizer
```

### 8.2 配置类设计
```kotlin
@Configuration
@ConfigurationProperties(prefix = "purchase")
@ConstructorBinding
data class PurchaseSystemProperties(
    val jwt: Jwt = Jwt(),
    val file: File = File()
) {
    data class Jwt(
        val secret: String = "",
        val expiration: Long = 86400
    )

    data class File(
        val uploadPath: String = "/tmp/uploads",
        val maxSize: String = "10MB"
    ) {
        // 扩展属性提供便利访问
        val maxSizeBytes: Long get() = when {
            maxSize.endsWith("KB") -> maxSize.dropLast(2).toLong() * 1024
            maxSize.endsWith("MB") -> maxSize.dropLast(2).toLong() * 1024 * 1024
            maxSize.endsWith("GB") -> maxSize.dropLast(2).toLong() * 1024 * 1024 * 1024
            else -> maxSize.toLong()
        }
    }
}

// Kotlin协程配置
@Configuration
@EnableAsync
class CoroutineConfig {

    @Bean
    fun applicationCoroutineScope(): CoroutineScope =
        CoroutineScope(
            SupervisorJob() +
            Dispatchers.Default +
            CoroutineName("ApplicationScope")
        )

    @Bean
    fun ioCoroutineScope(): CoroutineScope =
        CoroutineScope(
            SupervisorJob() +
            Dispatchers.IO +
            CoroutineName("IOScope")
        )

    // Spring Boot 3.5.4 协程支持配置
    @Bean
    fun webMvcConfigurer(): WebMvcConfigurer = object : WebMvcConfigurer {
        override fun configureAsyncSupport(configurer: AsyncSupportConfigurer) {
            configurer.setDefaultTimeout(30000)
            configurer.setTaskExecutor(
                TaskExecutorAdapter(Dispatchers.Default.asExecutor())
            )
        }
    }
}

## 9. Kotlin协程在DDD中的最佳实践

### 9.1 协程在聚合根中的应用
```kotlin
// 聚合根中的异步操作
@Entity
@Table(name = "procurement_requirements")
class ProcurementRequirement(
    @EmbeddedId
    val id: RequirementId,

    var title: String,
    var status: RequirementStatus
) : AggregateRoot<RequirementId>() {

    // 使用协程处理异步业务逻辑
    suspend fun processApproval(approver: UserId): Result<Unit> = runCatching {
        require(status == RequirementStatus.SUBMITTED) { "只有已提交的需求才能审批" }

        // 异步验证业务规则
        withContext(Dispatchers.IO) {
            validateApprovalRules()
        }

        status = RequirementStatus.APPROVED
        addDomainEvent(RequirementApprovedEvent(id, approver))
    }

    // 协程中的并发处理
    suspend fun enrichWithExternalData(): ProcurementRequirement = coroutineScope {
        val categoryInfo = async { fetchCategoryInfo() }
        val supplierInfo = async { fetchSupplierInfo() }
        val marketData = async { fetchMarketData() }

        // 等待所有异步操作完成
        val (category, suppliers, market) = Triple(
            categoryInfo.await(),
            supplierInfo.await(),
            marketData.await()
        )

        // 更新聚合根状态
        updateWithExternalData(category, suppliers, market)
        this@ProcurementRequirement
    }

    private suspend fun validateApprovalRules() {
        // 模拟异步验证
        delay(100)
        // 验证逻辑...
    }
}
```

### 9.2 协程在应用服务中的应用
```kotlin
@Service
@Transactional
class RequirementApplicationService(
    private val requirementRepository: RequirementRepository,
    private val eventPublisher: DomainEventPublisher,
    private val externalService: ExternalDataService
) {

    // 使用协程处理复杂业务流程
    suspend fun createRequirementWithValidation(
        command: CreateRequirementCommand
    ): Result<RequirementResponse> = runCatching {

        // 并发执行多个验证
        coroutineScope {
            val titleValidation = async { validateTitle(command.title) }
            val categoryValidation = async { validateCategory(command.category) }
            val budgetValidation = async { validateBudget(command.budget) }

            // 等待所有验证完成
            awaitAll(titleValidation, categoryValidation, budgetValidation)
        }

        // 创建聚合根
        val requirement = ProcurementRequirement.create(command)

        // 异步丰富数据
        requirement.enrichWithExternalData()

        // 保存并发布事件
        requirementRepository.save(requirement)
        eventPublisher.publishEvents(requirement.uncommittedEvents)

        RequirementResponse.from(requirement)
    }

    // 批量处理使用协程
    suspend fun processBatchRequirements(
        commands: List<CreateRequirementCommand>
    ): List<Result<RequirementResponse>> = coroutineScope {

        commands.map { command ->
            async {
                createRequirementWithValidation(command)
            }
        }.awaitAll()
    }

    // 使用Flow处理流式数据
    suspend fun processRequirementStream(
        commands: Flow<CreateRequirementCommand>
    ): Flow<RequirementResponse> = commands
        .map { command ->
            createRequirementWithValidation(command)
                .getOrThrow()
        }
        .flowOn(Dispatchers.IO)
}

## 10. 总结

本开发规范文档全面采用Kotlin作为主要开发语言，结合Spring Boot 3.5.4、PostgreSQL 16+和DDD架构模式，为采购系统的重写提供了完整的技术指导。

### 10.1 核心技术优势

#### Kotlin语言优势
- **简洁语法**：减少样板代码，提高开发效率
- **空安全**：编译时防止空指针异常
- **协程支持**：优雅的异步编程模型
- **数据类**：完美支持DDD值对象
- **扩展函数**：增强现有类的功能
- **密封类**：类型安全的状态管理

#### Spring Boot 3.5.4新特性
- **结构化日志**：更好的日志管理和分析
- **WebClient配置**：统一的HTTP客户端配置
- **任务执行优化**：更灵活的异步任务处理
- **SSL支持增强**：更安全的服务连接
- **监控改进**：更全面的应用监控

#### DDD架构价值
- **领域驱动**：业务逻辑与技术实现分离
- **聚合模式**：确保数据一致性
- **事件驱动**：松耦合的系统集成
- **六边形架构**：可测试和可维护的代码结构

### 10.2 开发效率提升

通过采用Kotlin + Spring Boot 3.5.4 + DDD的技术栈，预期可以实现：

- **代码量减少30-40%**：Kotlin简洁语法和数据类
- **开发效率提升50%**：协程异步编程和扩展函数
- **Bug减少60%**：空安全和类型安全
- **维护成本降低40%**：清晰的领域边界和职责分离

### 10.3 最佳实践要点

1. **始终使用协程**：替代传统的线程池和回调
2. **充分利用数据类**：实现值对象和DTO
3. **合理使用扩展函数**：增强代码可读性
4. **遵循DDD原则**：保持领域模型的纯净
5. **编写全面测试**：确保代码质量和业务正确性

这套开发规范为团队提供了统一的技术标准，确保代码质量和项目的长期可维护性。
```
