# 采购系统DDD重写 - 开发标准规范

## 1. 长远代码组织原则

### 1.1 模块化架构
```
purchase-system/
├── purchase-system-shared/              # 共享内核
├── purchase-system-identity/            # 身份认证上下文
├── purchase-system-procurement/         # 采购需求上下文
├── purchase-system-procurement-bidding/ # 采购竞价上下文
├── purchase-system-order/              # 订单履约上下文
├── purchase-system-logistics/          # 物流运输上下文
├── purchase-system-logistics-bidding/  # 物流竞价上下文
├── purchase-system-settlement/         # 财务结算上下文
├── purchase-system-communication/      # 通信协作上下文
└── purchase-system-platform/          # 平台启动模块
```

### 1.2 设计原则
- **单一职责**：每个模块专注于一个业务领域
- **开闭原则**：对扩展开放，对修改封闭
- **依赖倒置**：依赖抽象而非具体实现
- **接口隔离**：客户端不应依赖它不需要的接口

### 1.2 模块内部结构
```
purchase-system-identity/
├── src/main/kotlin/com/purchase/identity/
│   ├── interfaces/                  # 接口层
│   │   ├── rest/                   # REST控制器 (协程支持)
│   │   ├── event/                  # 事件监听器 (协程处理)
│   │   └── dto/                    # 数据传输对象 (数据类)
│   ├── application/                # 应用层
│   │   ├── service/                # 应用服务 (suspend函数)
│   │   ├── command/                # 命令对象 (数据类)
│   │   └── query/                  # 查询对象 (数据类)
│   ├── domain/                     # 领域层
│   │   ├── model/                  # 领域模型 (数据类/密封类)
│   │   ├── service/                # 领域服务 (扩展函数)
│   │   ├── repository/             # 仓储接口 (协程支持)
│   │   └── event/                  # 领域事件 (密封类)
│   └── infrastructure/             # 基础设施层
│       ├── repository/             # 仓储实现 (协程优化)
│       ├── config/                 # 配置 (数据类)
│       └── external/               # 外部服务适配器 (协程客户端)
└── src/test/kotlin/                # 测试代码 (协程测试)
```

## 2. 命名规范

### 2.1 包命名
- **基础包名**：`com.purchase.{context}`
- **接口层**：`com.purchase.{context}.interfaces`
- **应用层**：`com.purchase.{context}.application`
- **领域层**：`com.purchase.{context}.domain`
- **基础设施层**：`com.purchase.{context}.infrastructure`

### 2.2 类命名
- **聚合根**：`{EntityName}` (如：`User`, `ProcurementRequirement`)
- **实体**：`{EntityName}` (如：`UserProfile`, `RequirementItem`)
- **值对象**：`{ValueName}` (如：`Email`, `Money`, `Address`)
- **领域服务**：`{ServiceName}Service` (如：`AuthenticationService`)
- **应用服务**：`{Context}ApplicationService` (如：`UserApplicationService`)
- **仓储接口**：`{EntityName}Repository` (如：`UserRepository`)
- **仓储实现**：`{EntityName}RepositoryImpl` (如：`UserRepositoryImpl`)

### 2.3 方法命名 (Kotlin风格)
- **命令方法**：动词开头 (如：`createUser`, `updateProfile`, `deleteOrder`)
- **查询方法**：`get`/`find`开头 (如：`getUserById`, `findUsersByRole`)
- **布尔方法**：`is`/`has`/`can`开头，使用属性语法 (如：`val isActive`, `val hasPermission`)
- **扩展函数**：动词开头，体现扩展的功能 (如：`User.activate()`, `String.toEmail()`)

## 3. DDD建模规范

### 3.1 聚合根设计 (Kotlin风格)
```kotlin
@Entity
@Table(name = "users")
class User(
    @EmbeddedId
    val id: UserId,

    @Embedded
    var email: Email,

    @Embedded
    var profile: UserProfile,

    @ElementCollection
    @Enumerated(EnumType.STRING)
    private val _roles: MutableSet<Role> = mutableSetOf()
) : AggregateRoot<UserId>() {

    // 只读属性暴露角色集合
    val roles: Set<Role> get() = _roles.toSet()

    // 使用init块进行初始化验证
    init {
        // 发布领域事件
        registerEvent(UserCreatedEvent(id, email))
    }

    // 业务方法 - 使用Kotlin的简洁语法
    fun updateProfile(newProfile: UserProfile) {
        val oldProfile = profile
        profile = newProfile

        registerEvent(UserProfileUpdatedEvent(id, oldProfile, newProfile))
    }

    // 使用扩展属性提供便利访问
    val isActive: Boolean get() = profile.status == UserStatus.ACTIVE
    val hasAdminRole: Boolean get() = Role.ADMIN in roles

    // 使用高阶函数处理角色操作
    fun addRole(role: Role): User = apply {
        if (role !in _roles) {
            _roles.add(role)
            registerEvent(UserRoleAddedEvent(id, role))
        }
    }

    fun removeRole(role: Role): User = apply {
        if (_roles.remove(role)) {
            registerEvent(UserRoleRemovedEvent(id, role))
        }
    }

    // JPA需要的无参构造函数
    protected constructor() : this(
        UserId.empty(),
        Email.empty(),
        UserProfile.empty()
    )
}
```

### 3.2 值对象设计 (Kotlin数据类)
```kotlin
@Embeddable
data class Email(
    @Column(name = "email")
    val value: String
) {
    // 使用init块进行验证
    init {
        require(isValidEmail(value)) {
            "Invalid email format: $value"
        }
    }

    // 使用扩展属性提供便利访问
    val domain: String get() = value.substringAfter("@")
    val localPart: String get() = value.substringBefore("@")

    // 使用伴生对象提供工厂方法
    companion object {
        fun empty(): Email = Email("")

        fun fromString(email: String): Email = Email(email)

        // 使用扩展函数提供DSL风格的构造
        fun String.toEmail(): Email = Email(this)

        private fun isValidEmail(email: String): Boolean {
            return email.matches(Regex("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"))
        }
    }

    // 数据类自动生成equals、hashCode、toString
    // 无需手动实现
}

// 使用值类优化性能的简单值对象
@JvmInline
value class UserId(val value: Long) {
    init {
        require(value > 0) { "UserId must be positive" }
    }

    companion object {
        fun empty(): UserId = UserId(0L)
        fun generate(): UserId = UserId(System.currentTimeMillis())
    }
}
```

### 3.3 领域服务设计 (Kotlin风格)
```kotlin
@Component
class AuthenticationService(
    private val passwordEncoder: PasswordEncoder,
    private val tokenProvider: JwtTokenProvider,
    private val userRepository: UserRepository
) {

    suspend fun authenticate(email: Email, password: String): Result<AuthenticationResult> =
        runCatching {
            // 使用扩展函数让查找更自然
            val user = userRepository.findByEmailOrThrow(email)

            // 使用require进行验证
            require(passwordEncoder.matches(password, user.passwordHash)) {
                "Invalid password"
            }

            require(user.isActive) {
                "User account is inactive"
            }

            // 生成令牌并返回结果
            val token = tokenProvider.generateToken(user)
            AuthenticationResult(user.id, token)
        }

    // 使用扩展函数让仓储操作更自然
    private suspend fun UserRepository.findByEmailOrThrow(email: Email): User =
        findByEmail(email) ?: throw UserNotFoundException("User not found: ${email.value}")

    // 使用密封类表示认证结果
    sealed class AuthenticationResult {
        data class Success(val userId: UserId, val token: String) : AuthenticationResult()
        data class Failure(val reason: String) : AuthenticationResult()
    }
}
```

### 3.4 仓储接口设计 (Kotlin协程支持)
```kotlin
interface UserRepository : Repository<User, UserId> {

    // 基本CRUD操作 - 使用协程
    suspend fun save(user: User)
    suspend fun findById(id: UserId): User?
    suspend fun delete(user: User)

    // 业务查询方法 - 返回可空类型而非Optional
    suspend fun findByEmail(email: Email): User?
    suspend fun findByRole(role: Role): List<User>
    suspend fun findByStatus(status: UserStatus, pageable: Pageable): Page<User>

    // 复杂查询方法
    suspend fun findActiveUsersByCompany(company: String): List<User>
    suspend fun countByRoleAndStatus(role: Role, status: UserStatus): Long

    // 使用扩展函数提供便利方法
    suspend fun findByEmailOrThrow(email: Email): User =
        findByEmail(email) ?: throw UserNotFoundException("User not found: ${email.value}")

    // 批量操作
    suspend fun saveAll(users: List<User>): List<User>
    suspend fun findAllById(ids: List<UserId>): List<User>
}

// 使用扩展函数为仓储添加便利方法
suspend fun UserRepository.existsByEmail(email: Email): Boolean =
    findByEmail(email) != null

suspend fun UserRepository.findActiveUsers(): List<User> =
    findByStatus(UserStatus.ACTIVE, Pageable.unpaged()).content
```

## 4. Spring Data JPA + Hibernate + Kotlin 开发规范

### 4.1 实体类设计规范

#### 4.1.1 聚合根实体 (Kotlin + Hibernate 6.6.x)
```kotlin
// 使用Kotlin + Hibernate 6.6.x的现代特性
@Entity
@Table(name = "procurement_requirements")
class ProcurementRequirement(
    // 使用嵌入式ID（强类型ID）
    @EmbeddedId
    val id: RequirementId,

    // 基础属性
    @Column(name = "title", nullable = false, length = 200)
    var title: String,

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    var status: RequirementStatus,

    // 利用PostgreSQL的JSON类型存储复杂值对象
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "specification", columnDefinition = "jsonb")
    var specification: RequirementSpecification,

    // 利用PostgreSQL数组类型
    @JdbcTypeCode(SqlTypes.ARRAY)
    @Column(name = "tags", columnDefinition = "text[]")
    var tags: MutableList<String> = mutableListOf(),

    // 聚合内实体的级联操作
    @OneToMany(cascade = [CascadeType.ALL], fetch = FetchType.LAZY,
               mappedBy = "requirement", orphanRemoval = true)
    private val _items: MutableList<RequirementItem> = mutableListOf(),

    // 审计字段
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime? = null,

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime? = null,

    @Version
    @Column(name = "version")
    var version: Long? = null

) : AggregateRoot<RequirementId>() {

    // 只读属性暴露items集合
    val items: List<RequirementItem> get() = _items.toList()

    // 使用扩展属性提供便利访问
    val isDraft: Boolean get() = status == RequirementStatus.DRAFT
    val isSubmitted: Boolean get() = status == RequirementStatus.SUBMITTED
    val totalEstimatedValue: Money get() = items.sumOf { it.estimatedPrice }

    // 业务方法 - 使用Kotlin的简洁语法
    fun addItem(item: RequirementItem): ProcurementRequirement = apply {
        _items.add(item)
        item.requirement = this
        addDomainEvent(RequirementItemAddedEvent(id, item.id))
    }

    fun submit(): Result<Unit> = runCatching {
        require(isDraft) { "只有草稿状态的需求才能提交" }

        status = RequirementStatus.SUBMITTED
        addDomainEvent(RequirementSubmittedEvent(id))
    }

    // JPA自动发布领域事件
    @DomainEvents
    fun domainEvents(): Collection<DomainEvent> = uncommittedEvents

    @AfterDomainEventPublication
    fun clearDomainEvents() {
        markEventsAsCommitted()
    }

    // JPA需要的无参构造函数
    protected constructor() : this(
        RequirementId.empty(),
        "",
        RequirementStatus.DRAFT,
        RequirementSpecification.empty()
    )
}
```

#### 4.1.2 值对象设计 (使用Kotlin数据类)
```kotlin
// 使用Kotlin数据类实现值对象 - 比Java记录类更强大
data class RequirementSpecification(
    @field:NotBlank
    val description: String,

    @field:NotNull
    val technicalSpecs: Map<String, Any>,

    @field:NotNull
    val quality: QualityRequirements,

    @field:NotNull
    val delivery: DeliveryRequirements
) {
    // 记录类自动提供：
    // - 不可变性
    // - equals()和hashCode()
    // - toString()

    // 构造函数验证
    public RequirementSpecification {
        if (description == null || description.isBlank()) {
            throw new IllegalArgumentException("描述不能为空");
        }
        if (technicalSpecs == null) {
            technicalSpecs = new HashMap<>();
        }
    }

    // 业务方法
    public RequirementSpecification updateDescription(String newDescription) {
        return new RequirementSpecification(newDescription, technicalSpecs, quality, delivery);
    }
}

// 嵌套值对象
public record Money(
    @NotNull @DecimalMin("0.00") BigDecimal amount,
    @NotNull Currency currency
) {
    public Money add(Money other) {
        if (!this.currency.equals(other.currency)) {
            throw new IllegalArgumentException("货币类型不匹配");
        }
        return new Money(this.amount.add(other.amount), this.currency);
    }

    public boolean isGreaterThan(Money other) {
        if (!this.currency.equals(other.currency)) {
            throw new IllegalArgumentException("货币类型不匹配");
        }
        return this.amount.compareTo(other.amount) > 0;
    }
}
```

#### 4.1.3 强类型ID设计
```java
// 使用记录类实现强类型ID
public record RequirementId(@NotNull String value) {

    public RequirementId {
        if (value == null || value.isBlank()) {
            throw new IllegalArgumentException("RequirementId不能为空");
        }
    }

    public static RequirementId generate() {
        return new RequirementId(UUID.randomUUID().toString());
    }

    public static RequirementId of(String value) {
        return new RequirementId(value);
    }
}

// 在实体中使用
@Embeddable
public class RequirementIdConverter {

    @Column(name = "id")
    private String value;

    // JPA需要的默认构造函数
    protected RequirementIdConverter() {}

    public RequirementIdConverter(RequirementId id) {
        this.value = id.value();
    }

    public RequirementId toRequirementId() {
        return RequirementId.of(value);
    }
}
```

### 4.2 Repository设计规范

#### 4.2.1 Repository接口定义
```java
// 继承JpaRepository获得基础CRUD操作
public interface ProcurementRequirementRepository
    extends JpaRepository<ProcurementRequirement, RequirementId> {

    // 方法名自动生成查询
    List<ProcurementRequirement> findByStatus(RequirementStatus status);

    List<ProcurementRequirement> findByStatusAndCreatedAtBetween(
        RequirementStatus status,
        LocalDateTime start,
        LocalDateTime end
    );

    Optional<ProcurementRequirement> findByIdAndStatus(
        RequirementId id,
        RequirementStatus status
    );

    // 使用@Query进行复杂查询
    @Query("""
        SELECT r FROM ProcurementRequirement r
        WHERE JSON_EXTRACT(r.specification, '$.category') = :category
        AND r.status = :status
        """)
    List<ProcurementRequirement> findBySpecificationCategoryAndStatus(
        @Param("category") String category,
        @Param("status") RequirementStatus status
    );

    // 使用原生SQL查询PostgreSQL特性
    @Query(value = """
        SELECT * FROM procurement_requirements
        WHERE specification @> :specFilter::jsonb
        AND tags && :tags::text[]
        """, nativeQuery = true)
    List<ProcurementRequirement> findByJsonSpecificationAndTags(
        @Param("specFilter") String specificationFilter,
        @Param("tags") String[] tags
    );

    // 自定义查询方法
    @Query("""
        SELECT r FROM ProcurementRequirement r
        LEFT JOIN FETCH r.items
        WHERE r.id = :id
        """)
    Optional<ProcurementRequirement> findByIdWithItems(@Param("id") RequirementId id);

    // 统计查询
    @Query("SELECT COUNT(r) FROM ProcurementRequirement r WHERE r.status = :status")
    long countByStatus(@Param("status") RequirementStatus status);

    // 批量操作
    @Modifying
    @Query("UPDATE ProcurementRequirement r SET r.status = :newStatus WHERE r.status = :oldStatus")
    int updateStatusBatch(@Param("oldStatus") RequirementStatus oldStatus,
                         @Param("newStatus") RequirementStatus newStatus);
}
```

#### 4.2.2 自定义Repository实现
```java
// 当需要复杂查询时，可以自定义实现
public interface ProcurementRequirementRepositoryCustom {
    Page<ProcurementRequirement> findByComplexCriteria(RequirementSearchCriteria criteria, Pageable pageable);
    List<RequirementStatistics> getStatisticsByCategory();
}

@Repository
public class ProcurementRequirementRepositoryImpl implements ProcurementRequirementRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<ProcurementRequirement> findByComplexCriteria(
            RequirementSearchCriteria criteria,
            Pageable pageable) {

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<ProcurementRequirement> query = cb.createQuery(ProcurementRequirement.class);
        Root<ProcurementRequirement> root = query.from(ProcurementRequirement.class);

        List<Predicate> predicates = new ArrayList<>();

        // 动态构建查询条件
        if (criteria.getStatus() != null) {
            predicates.add(cb.equal(root.get("status"), criteria.getStatus()));
        }

        if (criteria.getTitle() != null) {
            predicates.add(cb.like(root.get("title"), "%" + criteria.getTitle() + "%"));
        }

        if (criteria.getDateRange() != null) {
            predicates.add(cb.between(root.get("createdAt"),
                criteria.getDateRange().getStart(),
                criteria.getDateRange().getEnd()));
        }

        query.where(predicates.toArray(new Predicate[0]));

        // 执行查询
        TypedQuery<ProcurementRequirement> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<ProcurementRequirement> results = typedQuery.getResultList();

        // 计算总数
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        countQuery.select(cb.count(countQuery.from(ProcurementRequirement.class)));
        countQuery.where(predicates.toArray(new Predicate[0]));
        Long total = entityManager.createQuery(countQuery).getSingleResult();

        return new PageImpl<>(results, pageable, total);
    }
}
```

## 5. 应用层规范

### 4.1 应用服务设计
```java
@Service
@Transactional
public class UserApplicationService {
    
    private final UserRepository userRepository;
    private final DomainEventPublisher eventPublisher;
    
    public UserApplicationService(UserRepository userRepository,
                                DomainEventPublisher eventPublisher) {
        this.userRepository = userRepository;
        this.eventPublisher = eventPublisher;
    }
    
    public UserResponse createUser(CreateUserCommand command) {
        // 1. 验证命令
        validateCommand(command);
        
        // 2. 检查业务规则
        if (userRepository.findByEmail(command.getEmail()).isPresent()) {
            throw new UserAlreadyExistsException("User already exists: " + command.getEmail());
        }
        
        // 3. 创建聚合根
        User user = new User(
            UserIdGenerator.generate(),
            new Email(command.getEmail()),
            new UserProfile(command.getUsername(), command.getPhone())
        );
        
        // 4. 保存聚合根
        userRepository.save(user);
        
        // 5. 发布领域事件
        eventPublisher.publishEvents(user.getUncommittedEvents());
        
        // 6. 返回响应
        return UserResponse.from(user);
    }
    
    private void validateCommand(CreateUserCommand command) {
        if (command == null) {
            throw new IllegalArgumentException("Command cannot be null");
        }
        // 更多验证逻辑...
    }
}
```

### 4.2 命令对象设计
```java
public class CreateUserCommand {
    
    @NotBlank(message = "Email is required")
    @Email(message = "Invalid email format")
    private String email;
    
    @NotBlank(message = "Username is required")
    @Size(min = 2, max = 50, message = "Username must be between 2 and 50 characters")
    private String username;
    
    @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Invalid phone number")
    private String phone;
    
    // 构造函数、getter、setter
    public CreateUserCommand(String email, String username, String phone) {
        this.email = email;
        this.username = username;
        this.phone = phone;
    }
    
    // getter方法...
}
```

### 4.3 查询对象设计
```java
public class UserSearchQuery {
    
    private String email;
    private Role role;
    private UserStatus status;
    private String company;
    private Pageable pageable;
    
    // 构造函数和getter方法...
    
    public static class Builder {
        private UserSearchQuery query = new UserSearchQuery();
        
        public Builder email(String email) {
            query.email = email;
            return this;
        }
        
        public Builder role(Role role) {
            query.role = role;
            return this;
        }
        
        public UserSearchQuery build() {
            return query;
        }
    }
}
```

## 5. 接口层规范

### 5.1 REST控制器设计
```java
@RestController
@RequestMapping("/api/v1/users")
@Validated
public class UserController {
    
    private final UserApplicationService userApplicationService;
    private final UserQueryService userQueryService;
    
    public UserController(UserApplicationService userApplicationService,
                         UserQueryService userQueryService) {
        this.userApplicationService = userApplicationService;
        this.userQueryService = userQueryService;
    }
    
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Result<UserResponse>> createUser(
            @RequestBody @Valid CreateUserRequest request) {
        
        CreateUserCommand command = new CreateUserCommand(
            request.getEmail(),
            request.getUsername(),
            request.getPhone()
        );
        
        UserResponse response = userApplicationService.createUser(command);
        
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(Result.success(response));
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @securityService.canAccessUser(#id)")
    public ResponseEntity<Result<UserResponse>> getUser(@PathVariable Long id) {
        UserResponse response = userQueryService.getUserById(id);
        return ResponseEntity.ok(Result.success(response));
    }
}
```

### 5.2 DTO设计
```java
// 请求DTO
public class CreateUserRequest {
    
    @NotBlank(message = "Email is required")
    @Email(message = "Invalid email format")
    private String email;
    
    @NotBlank(message = "Username is required")
    private String username;
    
    private String phone;
    
    // 构造函数、getter、setter
}

// 响应DTO
public class UserResponse {
    
    private Long id;
    private String email;
    private String username;
    private String phone;
    private Set<String> roles;
    private String status;
    private LocalDateTime createdAt;
    
    public static UserResponse from(User user) {
        return new UserResponse(
            user.getId().getValue(),
            user.getEmail().getValue(),
            user.getProfile().getUsername(),
            user.getProfile().getPhone(),
            user.getRoles().stream().map(Role::name).collect(Collectors.toSet()),
            user.getStatus().name(),
            user.getCreatedAt()
        );
    }
    
    // 构造函数、getter、setter
}
```

## 6. 测试规范

### 6.1 单元测试
```java
@ExtendWith(MockitoExtension.class)
class UserApplicationServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private DomainEventPublisher eventPublisher;
    
    @InjectMocks
    private UserApplicationService userApplicationService;
    
    @Test
    @DisplayName("应该成功创建用户")
    void shouldCreateUserSuccessfully() {
        // Given
        CreateUserCommand command = new CreateUserCommand(
            "<EMAIL>",
            "testuser",
            "+1234567890"
        );
        
        when(userRepository.findByEmail(any(Email.class)))
            .thenReturn(Optional.empty());
        
        // When
        UserResponse response = userApplicationService.createUser(command);
        
        // Then
        assertThat(response).isNotNull();
        assertThat(response.getEmail()).isEqualTo("<EMAIL>");
        assertThat(response.getUsername()).isEqualTo("testuser");
        
        verify(userRepository).save(any(User.class));
        verify(eventPublisher).publishEvents(anyList());
    }
    
    @Test
    @DisplayName("当用户已存在时应该抛出异常")
    void shouldThrowExceptionWhenUserAlreadyExists() {
        // Given
        CreateUserCommand command = new CreateUserCommand(
            "<EMAIL>",
            "existinguser",
            "+1234567890"
        );
        
        User existingUser = new User(
            new UserId(1L),
            new Email("<EMAIL>"),
            new UserProfile("existinguser", "+1234567890")
        );
        
        when(userRepository.findByEmail(any(Email.class)))
            .thenReturn(Optional.of(existingUser));
        
        // When & Then
        assertThatThrownBy(() -> userApplicationService.createUser(command))
            .isInstanceOf(UserAlreadyExistsException.class)
            .hasMessage("User already exists: <EMAIL>");
    }
}
```

### 6.2 集成测试
```java
@SpringBootTest
@Testcontainers
@Transactional
class UserIntegrationTest {
    
    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>("mysql:8.0")
            .withDatabaseName("test")
            .withUsername("test")
            .withPassword("test");
    
    @Autowired
    private UserApplicationService userApplicationService;
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    @DisplayName("应该完整地创建和查询用户")
    void shouldCreateAndRetrieveUserCompletely() {
        // Given
        CreateUserCommand command = new CreateUserCommand(
            "<EMAIL>",
            "integrationuser",
            "+1234567890"
        );
        
        // When
        UserResponse createdUser = userApplicationService.createUser(command);
        Optional<User> retrievedUser = userRepository.findById(
            new UserId(createdUser.getId())
        );
        
        // Then
        assertThat(retrievedUser).isPresent();
        assertThat(retrievedUser.get().getEmail().getValue())
            .isEqualTo("<EMAIL>");
    }
}
```

## 7. 异常处理规范

### 7.1 异常层次结构
```java
// 基础异常类
public abstract class DomainException extends RuntimeException {
    protected DomainException(String message) {
        super(message);
    }
    
    protected DomainException(String message, Throwable cause) {
        super(message, cause);
    }
}

// 业务异常
public class UserAlreadyExistsException extends DomainException {
    public UserAlreadyExistsException(String message) {
        super(message);
    }
}

// 验证异常
public class InvalidEmailException extends DomainException {
    public InvalidEmailException(String message) {
        super(message);
    }
}
```

### 7.2 全局异常处理
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(DomainException.class)
    public ResponseEntity<Result<Void>> handleDomainException(DomainException e) {
        return ResponseEntity.badRequest()
            .body(Result.failure(e.getMessage()));
    }
    
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<Result<Void>> handleValidationException(ValidationException e) {
        return ResponseEntity.badRequest()
            .body(Result.failure("Validation failed: " + e.getMessage()));
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Result<Void>> handleGenericException(Exception e) {
        log.error("Unexpected error occurred", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(Result.failure("Internal server error"));
    }
}
```

## 8. 配置规范

### 8.1 配置文件组织
```yaml
# application.yml - 基于现代化技术栈的配置
spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:development}

  # PostgreSQL 16+ 配置
  datasource:
    url: ${DATABASE_URL:************************************************}
    username: ${DATABASE_USERNAME:postgres}
    password: ${DATABASE_PASSWORD:password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA + Hibernate 6.6.x 配置
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: ${JPA_DDL_AUTO:validate}
    show-sql: ${JPA_SHOW_SQL:false}
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
        # 启用虚拟线程支持
        connection.provider_disables_autocommit: true
        # JSON类型支持
        type.preferred_uuid_jdbc_type: VARCHAR

  # Redis 7.x 配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

  # Kafka 3.9.x 配置
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 5
      enable-idempotence: true
    consumer:
      group-id: purchase-system
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: false
      properties:
        spring.json.trusted.packages: "com.purchase.*"

# Elasticsearch 8.x 配置
elasticsearch:
  uris: ${ELASTICSEARCH_URIS:http://localhost:9200}
  username: ${ELASTICSEARCH_USERNAME:}
  password: ${ELASTICSEARCH_PASSWORD:}

# 应用特定配置
purchase:
  jwt:
    secret: ${JWT_SECRET:your-secret-key}
    expiration: ${JWT_EXPIRATION:86400}

  file:
    upload-path: ${FILE_UPLOAD_PATH:/tmp/uploads}
    max-size: ${FILE_MAX_SIZE:10MB}

  # 虚拟线程配置
  virtual-threads:
    enabled: ${VIRTUAL_THREADS_ENABLED:true}
```

### 8.2 配置类设计
```java
@Configuration
@ConfigurationProperties(prefix = "purchase")
@Data
public class PurchaseSystemProperties {
    
    private Jwt jwt = new Jwt();
    private File file = new File();
    
    @Data
    public static class Jwt {
        private String secret;
        private long expiration;
    }
    
    @Data
    public static class File {
        private String uploadPath;
        private String maxSize;
    }
}
```
