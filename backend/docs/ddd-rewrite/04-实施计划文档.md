# 采购系统DDD重写 - 实施路线图 (Kotlin技术栈)

## 1. 长远实施战略

### 1.1 战略目标
- 构建面向未来的企业级采购平台架构
- 建立可持续发展的技术和组织能力
- 实现业务敏捷性和技术先进性的平衡
- 为数字化转型和生态化发展奠定基础

### 1.2 实施原则
- **价值驱动**：优先实现高业务价值的功能模块
- **风险可控**：采用渐进式演进，降低实施风险
- **能力建设**：注重团队能力提升和知识积累
- **质量优先**：建立高标准的质量保证体系

### 1.3 成功标准
- **技术指标**：架构清晰、代码质量高、性能优异
- **业务指标**：功能完整、用户体验好、业务价值实现
- **团队指标**：技能提升、协作高效、知识沉淀
- **长远指标**：可扩展、可维护、可演进

## 2. 项目里程碑

### 2.1 整体时间线
```
阶段1: 基础设施搭建    ├─────────────┤ (3周)
阶段2: 核心领域建模    ├─────────────────────────┤ (6周)  
阶段3: 扩展领域实现    ├─────────────────┤ (4周)
阶段4: 基础设施服务    ├─────────────┤ (3周)
阶段5: 数据迁移测试    ├─────────────┤ (3周)
阶段6: 上线和监控      ├─────┤ (2周)
                      └─────────────────────────────────┘
                      0    5    10   15   20   25 (周)
```

### 2.2 关键里程碑
| 里程碑 | 时间 | 交付物 | 长远价值 |
|--------|------|--------|----------|
| M1: 技术基础设施 | 第4周 | DDD框架、CI/CD、监控 | 为长期发展奠定技术基础 |
| M2: 身份权限体系 | 第8周 | 统一身份认证平台 | 支持未来多租户和联邦认证 |
| M3: 采购领域核心 | 第12周 | 采购需求和竞价上下文 | 建立专业化采购能力 |
| M4: 订单履约体系 | 第16周 | 订单管理和履约体系 | 构建端到端履约能力 |
| M5: 物流服务平台 | 第20周 | 物流竞价和执行上下文 | 建立智能物流服务能力 |
| M6: 财务结算体系 | 第24周 | 多方结算和风控体系 | 构建可信的财务基础设施 |
| M7: 生态协作平台 | 第28周 | 完整业务生态 | 实现平台化运营能力 |

## 3. 详细实施计划

### 3.1 阶段1：基础设施搭建（第1-3周）

#### 第1周：项目初始化 (Kotlin技术栈)
**目标**：建立Kotlin项目基础结构
- [ ] 创建新的Git仓库和分支策略
- [ ] 搭建Maven多模块Kotlin项目结构
- [ ] 配置Spring Boot 3.5.4 + Kotlin 2.1.x基础框架
- [ ] 建立Kotlin代码规范和质量检查工具
- [ ] 配置Kotlin协程支持

**交付物**：
- Kotlin项目骨架代码
- Kotlin开发环境配置文档
- Kotlin代码规范文档
- 协程配置示例

#### 第2周：基础设施组件
**目标**：搭建核心基础设施
- [ ] 配置PostgreSQL 16+数据库连接和事务管理
- [ ] 集成Redis 7.x缓存和分布式锁
- [ ] 配置Apache Kafka 3.9.x消息队列
- [ ] 建立基于Kafka的事件驱动架构基础
- [ ] 配置Elasticsearch 8.x搜索引擎

**交付物**：
- Spring Boot 3.5.4 + Kotlin基础设施配置代码
- PostgreSQL数据库初始化脚本和JSON支持配置
- Kafka主题配置和Spring Kafka Kotlin集成
- Elasticsearch索引模板和Kotlin客户端配置
- Kotlin协程优化的数据库和消息队列配置

#### 第3周：开发工具链
**目标**：完善开发和部署工具
- [ ] 建立CI/CD流水线
- [ ] 配置测试框架（单元测试、集成测试）
- [ ] 集成代码质量检查工具
- [ ] 建立监控和日志系统

**交付物**：
- CI/CD配置文件
- 测试框架配置
- 监控配置文档

### 3.2 阶段2：核心领域建模（第4-9周）

#### 第4-5周：用户身份上下文
**目标**：实现用户管理和认证授权
- [ ] 设计User聚合根和相关实体
- [ ] 实现JWT认证机制
- [ ] 实现基于角色的权限控制
- [ ] 实现用户资料管理功能

**技术任务**：
```kotlin
// 使用Hibernate 6.6.x和Kotlin特性的领域模型
@Entity
@Table(name = "users")
class User(
    @EmbeddedId
    val id: UserId,

    @Embedded
    var email: Email,  // 使用数据类作为值对象

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "profile", columnDefinition = "jsonb")
    var profile: UserProfile,  // JSON存储复杂对象

    @ElementCollection(fetch = FetchType.LAZY)
    @Enumerated(EnumType.STRING)
    private val _roles: MutableSet<Role> = mutableSetOf()

) : AggregateRoot<UserId>() {

    // 只读属性暴露角色集合
    val roles: Set<Role> get() = _roles.toSet()

    // 业务方法 - 使用协程
    suspend fun authenticate(credentials: Credentials): Result<Unit> = runCatching {
        // 认证逻辑
    }

    fun updateProfile(newProfile: UserProfile): User = apply {
        profile = newProfile
        addDomainEvent(UserProfileUpdatedEvent(id))
    }

    // JPA自动发布领域事件
    @DomainEvents
    fun domainEvents(): Collection<DomainEvent> = uncommittedEvents

    // JPA需要的无参构造函数
    protected constructor() : this(
        UserId.empty(),
        Email.empty(),
        UserProfile.empty()
    )
}

// Spring Data JPA Repository - 协程支持
interface UserRepository : JpaRepository<User, UserId> {
    suspend fun findByEmail(email: Email): User?
    suspend fun findByRolesContaining(role: Role): List<User>

    // 扩展函数提供便利方法
    suspend fun findByEmailOrThrow(email: Email): User =
        findByEmail(email) ?: throw UserNotFoundException("User not found: ${email.value}")
}

// 应用服务 - 协程优化
@Service
@Transactional
class UserApplicationService(
    private val userRepository: UserRepository
) {

    suspend fun createUser(command: CreateUserCommand): UserResponse {
        var user = User.create(command);
        userRepository.save(user);  // 自动处理事件发布
        return UserResponse.from(user);
    }
}
```

#### 第6-7周：采购需求上下文
**目标**：实现需求发布和管理功能
- [ ] 设计ProcurementRequirement聚合根
- [ ] 实现需求发布流程
- [ ] 实现需求分类管理
- [ ] 实现需求搜索功能

**技术任务**：
```java
// 使用现代化技术栈的领域模型
@Entity
@Table(name = "procurement_requirements")
public class ProcurementRequirement extends AggregateRoot<RequirementId> {

    @EmbeddedId
    private RequirementId id;

    @Column(name = "buyer_id")
    private UserId buyerId;

    // 利用PostgreSQL的JSON类型存储复杂规格
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "specification", columnDefinition = "jsonb")
    private RequirementSpec specification;

    // 聚合内实体的级联管理
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "requirement", orphanRemoval = true)
    private List<RequirementItem> items = new ArrayList<>();

    @Enumerated(EnumType.STRING)
    private RequirementStatus status;

    // 业务方法
    public void publish() {
        if (this.status != RequirementStatus.DRAFT) {
            throw new IllegalStateException("只有草稿状态才能发布");
        }
        this.status = RequirementStatus.PUBLISHED;
        addDomainEvent(new RequirementPublishedEvent(this.id));
    }

    public void addItem(RequirementItem item) {
        this.items.add(item);
        item.setRequirement(this);
        addDomainEvent(new RequirementItemAddedEvent(this.id, item.getId()));
    }
}

// Repository with complex queries
public interface ProcurementRequirementRepository
    extends JpaRepository<ProcurementRequirement, RequirementId> {

    // 利用PostgreSQL的JSON查询能力
    @Query(value = """
        SELECT * FROM procurement_requirements
        WHERE specification @> :categoryFilter::jsonb
        AND status = :status
        """, nativeQuery = true)
    List<ProcurementRequirement> findBySpecificationCategoryAndStatus(
        @Param("categoryFilter") String categoryFilter,
        @Param("status") String status
    );
}
```

#### 第8-9周：竞价交易上下文
**目标**：实现竞价投标功能
- [ ] 设计BiddingProcess聚合根
- [ ] 实现竞价提交流程
- [ ] 实现竞价审核机制
- [ ] 实现中标选择逻辑

### 3.3 阶段3：扩展领域实现（第10-13周）

#### 第10-11周：订单履约上下文
**目标**：实现订单管理功能
- [ ] 设计Order聚合根
- [ ] 实现订单创建流程
- [ ] 实现支付管理功能
- [ ] 实现订单状态跟踪

#### 第12-13周：库存管理上下文
**目标**：实现基于信任关系的智能库存管理功能
- [ ] 设计Inventory聚合根和供应商信任关系模型
- [ ] 实现库存录入和原始采购信息关联
- [ ] 实现基于信任关系的智能补货算法
- [ ] 实现一键补货和供应商直接通知机制
- [ ] 实现定制化补货流程（PDF上传和订单完善）
- [ ] 实现供应商信任评分体系

**技术任务**：
```java
// 基于信任关系的库存聚合根设计
@Entity
@Table(name = "inventories")
public class Inventory extends AggregateRoot<InventoryId> {

    @EmbeddedId
    private InventoryId id;

    @Column(name = "buyer_id")
    private UserId buyerId;

    @Column(name = "warehouse_name")
    private String warehouseName;

    // 库存商品列表
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "inventory", orphanRemoval = true)
    private List<InventoryItem> items = new ArrayList<>();

    // 信任供应商关系
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "inventory", orphanRemoval = true)
    private List<TrustedSupplierRelationship> trustedSuppliers = new ArrayList<>();

    // 业务方法
    public void addItemWithOriginalPurchase(InventoryItem item, OriginalPurchaseInfo purchaseInfo) {
        this.items.add(item);
        item.setInventory(this);
        item.linkOriginalPurchase(purchaseInfo);

        // 建立或更新供应商信任关系
        establishTrustRelationship(purchaseInfo);

        addDomainEvent(new InventoryItemAddedEvent(this.id, item.getId()));
        addDomainEvent(new OriginalPurchaseLinkedEvent(this.id, item.getId(), purchaseInfo.supplierId()));
    }

    public void recordStockMovement(InventoryItemId itemId, StockMovement movement) {
        var item = findItemById(itemId);
        item.recordMovement(movement);

        // 检查是否需要预警
        if (item.isBelowSafetyStock()) {
            addDomainEvent(new InventoryAlertTriggeredEvent(this.id, itemId, item.getCurrentStock()));

            // 检查是否可以推荐信任供应商补货
            var originalPurchase = item.getOriginalPurchase();
            if (originalPurchase != null && isTrustedSupplier(originalPurchase.supplierId())) {
                addDomainEvent(new TrustedSupplierReplenishmentRecommendedEvent(
                    this.id, itemId, originalPurchase.supplierId()));
            }
        }
    }

    public QuickReplenishResult executeQuickReplenish(InventoryItemId itemId,
                                                      StockQuantity quantity,
                                                      Money maxPrice) {
        var item = findItemById(itemId);
        var originalPurchase = item.getOriginalPurchase();

        if (originalPurchase == null || !isTrustedSupplier(originalPurchase.supplierId())) {
            throw new QuickReplenishNotAvailableException("该商品不支持一键补货");
        }

        // 验证价格是否在可接受范围内
        var maxAllowedPrice = originalPurchase.lastPurchasePrice().multiply(1.2); // 最多涨价20%
        if (maxPrice.isGreaterThan(maxAllowedPrice)) {
            throw new PriceExceedsLimitException("补货价格超出可接受范围");
        }

        // 创建快速补货订单
        var order = createQuickReplenishOrder(item, quantity, maxPrice);

        addDomainEvent(new QuickReplenishOrderCreatedEvent(
            this.id, itemId, originalPurchase.supplierId(), order.getId()));

        return QuickReplenishResult.success(order.getId());
    }

    private void establishTrustRelationship(OriginalPurchaseInfo purchaseInfo) {
        var existingRelationship = findTrustedSupplier(purchaseInfo.supplierId());

        if (existingRelationship != null) {
            existingRelationship.updateFromNewPurchase(purchaseInfo);
        } else {
            var newRelationship = TrustedSupplierRelationship.create(
                this.id, purchaseInfo.supplierId(), purchaseInfo);
            this.trustedSuppliers.add(newRelationship);

            addDomainEvent(new SupplierTrustRelationshipEstablishedEvent(
                this.id, purchaseInfo.supplierId()));
        }
    }
}

// 库存商品实体 - 包含原始采购信息
@Entity
@Table(name = "inventory_items")
public class InventoryItem {

    @EmbeddedId
    private InventoryItemId id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inventory_id")
    private Inventory inventory;

    @Embedded
    private ProductInfo productInfo;

    @Embedded
    private StockLevel stockLevel;

    // 原始采购信息
    @Embedded
    private OriginalPurchaseInfo originalPurchase;

    // 补货偏好设置
    @Embedded
    private ReplenishmentPreference preference;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "inventoryItem")
    private List<StockMovement> movements = new ArrayList<>();

    // 业务方法
    public void linkOriginalPurchase(OriginalPurchaseInfo purchaseInfo) {
        this.originalPurchase = purchaseInfo;

        // 根据采购历史设置默认补货偏好
        this.preference = ReplenishmentPreference.createDefault(purchaseInfo);
    }

    public boolean isBelowSafetyStock() {
        return stockLevel.getCurrentQuantity().compareTo(stockLevel.getSafetyStock()) < 0;
    }

    public boolean canQuickReplenish() {
        return originalPurchase != null &&
               preference.preferOriginalSupplier() &&
               originalPurchase.qualityRating().isGood();
    }

    public void recordMovement(StockMovement movement) {
        this.movements.add(movement);
        movement.setInventoryItem(this);

        // 更新当前库存
        if (movement.getType() == MovementType.IN) {
            stockLevel = stockLevel.increase(movement.getQuantity());
        } else {
            stockLevel = stockLevel.decrease(movement.getQuantity());
        }
    }
}

// 信任供应商关系实体
@Entity
@Table(name = "trusted_supplier_relationships")
public class TrustedSupplierRelationship {

    @EmbeddedId
    private TrustedSupplierRelationshipId id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inventory_id")
    private Inventory inventory;

    @Column(name = "supplier_id")
    private SupplierId supplierId;

    @Embedded
    private TrustScore trustScore;

    @Column(name = "total_orders")
    private Integer totalOrders;

    @Column(name = "successful_orders")
    private Integer successfulOrders;

    @Column(name = "last_order_date")
    private LocalDateTime lastOrderDate;

    @Column(name = "average_delivery_time")
    private Integer averageDeliveryTime;

    // 业务方法
    public static TrustedSupplierRelationship create(InventoryId inventoryId,
                                                     SupplierId supplierId,
                                                     OriginalPurchaseInfo purchaseInfo) {
        var relationship = new TrustedSupplierRelationship();
        relationship.id = TrustedSupplierRelationshipId.generate();
        relationship.supplierId = supplierId;
        relationship.totalOrders = 1;
        relationship.successfulOrders = 1;
        relationship.lastOrderDate = purchaseInfo.lastPurchaseDate();
        relationship.trustScore = TrustScore.calculateInitial(purchaseInfo);

        return relationship;
    }

    public void updateFromNewPurchase(OriginalPurchaseInfo purchaseInfo) {
        this.totalOrders++;
        this.successfulOrders++;
        this.lastOrderDate = purchaseInfo.lastPurchaseDate();

        // 重新计算信任评分
        this.trustScore = this.trustScore.updateWith(purchaseInfo);
    }

    public boolean isEligibleForQuickReplenish() {
        return trustScore.getValue() >= 80 && // 信任评分80分以上
               successfulOrders >= 2 &&      // 至少2次成功订单
               isRecentlyActive();            // 最近有交易活动
    }

    private boolean isRecentlyActive() {
        return lastOrderDate.isAfter(LocalDateTime.now().minusMonths(6));
    }
}

// 值对象 - 使用Java 21记录类
public record OriginalPurchaseInfo(
    @NotNull SupplierId supplierId,
    @NotNull String supplierName,
    @NotNull OrderId originalOrderId,
    @NotNull Money lastPurchasePrice,
    @NotNull LocalDateTime lastPurchaseDate,
    @NotNull Integer lastPurchaseQuantity,
    @NotNull QualityRating qualityRating,
    @NotNull DeliveryPerformance deliveryPerformance
) {
    public OriginalPurchaseInfo {
        if (lastPurchaseQuantity <= 0) {
            throw new IllegalArgumentException("采购数量必须大于0");
        }
        if (lastPurchaseDate.isAfter(LocalDateTime.now())) {
            throw new IllegalArgumentException("采购日期不能是未来时间");
        }
    }
}

public record ReplenishmentPreference(
    boolean preferOriginalSupplier,    // 是否优先原供应商
    @NotNull Money maxPriceIncrease,   // 可接受的最大涨价幅度
    @NotNull Integer defaultReplenishQuantity,  // 默认补货数量
    boolean autoReplenish             // 是否自动补货
) {
    public static ReplenishmentPreference createDefault(OriginalPurchaseInfo purchaseInfo) {
        return new ReplenishmentPreference(
            true,  // 默认优先原供应商
            purchaseInfo.lastPurchasePrice().multiply(0.2), // 最多涨价20%
            purchaseInfo.lastPurchaseQuantity(), // 默认按上次数量补货
            false  // 默认不自动补货
        );
    }
}

public record TrustScore(
    @NotNull Integer value,           // 信任评分 0-100
    @NotNull LocalDateTime lastUpdated,
    @NotNull String calculationBasis  // 计算依据
) {
    public TrustScore {
        if (value < 0 || value > 100) {
            throw new IllegalArgumentException("信任评分必须在0-100之间");
        }
    }

    public static TrustScore calculateInitial(OriginalPurchaseInfo purchaseInfo) {
        int score = 60; // 基础分

        // 根据质量评分调整
        score += purchaseInfo.qualityRating().getScoreAdjustment();

        // 根据交期表现调整
        score += purchaseInfo.deliveryPerformance().getScoreAdjustment();

        return new TrustScore(
            Math.min(100, Math.max(0, score)),
            LocalDateTime.now(),
            "基于首次采购表现的初始评分"
        );
    }

    public TrustScore updateWith(OriginalPurchaseInfo newPurchaseInfo) {
        // 基于新的采购信息更新信任评分
        int newScore = this.value;

        // 质量表现影响
        newScore += newPurchaseInfo.qualityRating().getScoreAdjustment();

        // 交期表现影响
        newScore += newPurchaseInfo.deliveryPerformance().getScoreAdjustment();

        // 平滑处理，避免单次交易影响过大
        newScore = (this.value * 3 + newScore) / 4;

        return new TrustScore(
            Math.min(100, Math.max(0, newScore)),
            LocalDateTime.now(),
            "基于历史交易记录的综合评分"
        );
    }
}

public record StockLevel(
    @NotNull StockQuantity currentQuantity,
    @NotNull StockQuantity safetyStock,
    @NotNull StockQuantity reorderPoint
) {
    public StockLevel increase(StockQuantity quantity) {
        return new StockLevel(
            currentQuantity.add(quantity),
            safetyStock,
            reorderPoint
        );
    }

    public StockLevel decrease(StockQuantity quantity) {
        return new StockLevel(
            currentQuantity.subtract(quantity),
            safetyStock,
            reorderPoint
        );
    }
}

// 基于信任关系的智能补货服务
@Service
public class TrustBasedReplenishmentService {

    private final SupplierAvailabilityChecker availabilityChecker;
    private final TrustScoreCalculator trustScoreCalculator;

    public ReplenishmentRecommendation generateRecommendation(InventoryItem item) {
        var originalPurchase = item.getOriginalPurchase();
        var preference = item.getPreference();

        if (originalPurchase == null) {
            return createCompetitiveBiddingRecommendation(item, "无历史采购记录");
        }

        // 检查原供应商是否可用
        var supplierAvailability = availabilityChecker.checkAvailability(originalPurchase.supplierId());

        if (!supplierAvailability.isAvailable()) {
            return createCompetitiveBiddingRecommendation(item, "原供应商当前不可用");
        }

        // 检查信任评分
        var trustScore = trustScoreCalculator.calculateCurrentScore(originalPurchase);

        if (trustScore.value() >= 80 && preference.preferOriginalSupplier()) {
            return createTrustedSupplierRecommendation(item, originalPurchase, supplierAvailability, trustScore);
        } else {
            return createCompetitiveBiddingRecommendation(item, "信任评分不足或用户偏好竞价");
        }
    }

    private ReplenishmentRecommendation createTrustedSupplierRecommendation(
            InventoryItem item,
            OriginalPurchaseInfo originalPurchase,
            SupplierAvailability availability,
            TrustScore trustScore) {

        // 基于历史价格计算推荐价格
        var recommendedPrice = calculateRecommendedPrice(originalPurchase, availability);

        // 基于消耗速度计算推荐数量
        var recommendedQuantity = calculateRecommendedQuantity(item);

        return ReplenishmentRecommendation.builder()
            .type(ReplenishmentType.TRUSTED_SUPPLIER)
            .supplierId(originalPurchase.supplierId())
            .supplierName(originalPurchase.supplierName())
            .recommendedPrice(recommendedPrice)
            .recommendedQuantity(recommendedQuantity)
            .trustScore(trustScore)
            .estimatedDeliveryTime(availability.getEstimatedDeliveryTime())
            .maxPriceIncrease(item.getPreference().maxPriceIncrease())
            .reasoning("基于与该供应商的成功合作历史，推荐一键补货")
            .canQuickReplenish(true)
            .build();
    }

    private ReplenishmentRecommendation createCompetitiveBiddingRecommendation(
            InventoryItem item, String reason) {

        var recommendedQuantity = calculateRecommendedQuantity(item);

        return ReplenishmentRecommendation.builder()
            .type(ReplenishmentType.COMPETITIVE_BIDDING)
            .recommendedQuantity(recommendedQuantity)
            .reasoning(reason + "，建议发起竞价补货")
            .canQuickReplenish(false)
            .build();
    }

    private Money calculateRecommendedPrice(OriginalPurchaseInfo originalPurchase,
                                           SupplierAvailability availability) {
        var basePrice = originalPurchase.lastPurchasePrice();

        // 考虑市场波动和供应商当前状况
        var adjustmentFactor = availability.getPriceAdjustmentFactor();

        return basePrice.multiply(adjustmentFactor);
    }

    private StockQuantity calculateRecommendedQuantity(InventoryItem item) {
        // 基于历史消耗速度和安全库存计算
        var consumptionRate = analyzeConsumptionRate(item);
        var safetyStock = item.getStockLevel().safetyStock();
        var currentStock = item.getStockLevel().currentQuantity();

        // 推荐补货到安全库存的2倍
        var targetStock = safetyStock.multiply(2);
        var recommendedQuantity = targetStock.subtract(currentStock);

        return recommendedQuantity.isPositive() ? recommendedQuantity : item.getPreference().defaultReplenishQuantity();
    }
}

// 一键补货应用服务
@Service
@Transactional
public class QuickReplenishmentApplicationService {

    private final InventoryRepository inventoryRepository;
    private final OrderRepository orderRepository;
    private final SupplierNotificationService supplierNotificationService;

    public QuickReplenishResult executeQuickReplenish(QuickReplenishCommand command) {
        // 1. 获取库存商品
        var inventory = inventoryRepository.findById(command.getInventoryId())
            .orElseThrow(() -> new InventoryNotFoundException(command.getInventoryId()));

        // 2. 执行一键补货
        var result = inventory.executeQuickReplenish(
            command.getItemId(),
            command.getQuantity(),
            command.getMaxPrice()
        );

        // 3. 保存变更
        inventoryRepository.save(inventory);

        // 4. 异步通知供应商
        var item = inventory.findItemById(command.getItemId());
        var originalPurchase = item.getOriginalPurchase();

        CompletableFuture.runAsync(() -> {
            supplierNotificationService.notifyQuickReplenishOrder(
                originalPurchase.supplierId(),
                result.getOrderId(),
                command.getQuantity(),
                command.getMaxPrice()
            );
        });

        return result;
    }
}

// 定制化补货聚合根
@Entity
@Table(name = "customized_replenishment_orders")
public class CustomizedReplenishmentOrder extends AggregateRoot<CustomizedReplenishmentOrderId> {

    @EmbeddedId
    private CustomizedReplenishmentOrderId id;

    @Column(name = "buyer_id")
    private UserId buyerId;

    @Column(name = "supplier_id")
    private SupplierId supplierId;

    @Column(name = "inventory_item_id")
    private InventoryItemId inventoryItemId;

    // 产品需求文档
    @Embedded
    private ProductRequirementDocument requirementDocument;

    // 订单完善状态
    @Enumerated(EnumType.STRING)
    private OrderCompletionStatus completionStatus;

    // 定金信息
    @Embedded
    private DepositInfo depositInfo;

    // 供应商完善的订单详情
    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "supplier_completion_id")
    private SupplierOrderCompletion supplierCompletion;

    // 创建定制补货订单
    public static CustomizedReplenishmentOrder create(
            UserId buyerId,
            SupplierId supplierId,
            InventoryItemId inventoryItemId,
            ProductRequirementDocument document,
            Money depositAmount) {

        var order = new CustomizedReplenishmentOrder();
        order.id = CustomizedReplenishmentOrderId.generate();
        order.buyerId = buyerId;
        order.supplierId = supplierId;
        order.inventoryItemId = inventoryItemId;
        order.requirementDocument = document;
        order.completionStatus = OrderCompletionStatus.PENDING_DEPOSIT;
        order.depositInfo = DepositInfo.create(depositAmount);

        order.addDomainEvent(new CustomizedReplenishmentOrderCreatedEvent(
            order.id, buyerId, supplierId, document.getFileUrl()));

        return order;
    }

    // 支付定金
    public void payDeposit(PaymentInfo paymentInfo) {
        if (this.completionStatus != OrderCompletionStatus.PENDING_DEPOSIT) {
            throw new IllegalStateException("订单状态不允许支付定金");
        }

        this.depositInfo = this.depositInfo.markAsPaid(paymentInfo);
        this.completionStatus = OrderCompletionStatus.PENDING_SUPPLIER_COMPLETION;

        addDomainEvent(new DepositPaidEvent(this.id, paymentInfo.getAmount()));
        addDomainEvent(new SupplierOrderCompletionRequestedEvent(
            this.id, this.supplierId, this.requirementDocument.getFileUrl()));
    }

    // 供应商完善订单
    public void completeBySupplier(SupplierOrderCompletion completion) {
        if (this.completionStatus != OrderCompletionStatus.PENDING_SUPPLIER_COMPLETION) {
            throw new IllegalStateException("订单状态不允许供应商完善");
        }

        this.supplierCompletion = completion;
        this.completionStatus = OrderCompletionStatus.PENDING_BUYER_CONFIRMATION;

        addDomainEvent(new OrderDetailsCompletedBySupplierEvent(
            this.id, completion.getTotalPrice(), completion.getDeliveryDate()));
    }

    // 买家确认订单
    public void confirmByBuyer() {
        if (this.completionStatus != OrderCompletionStatus.PENDING_BUYER_CONFIRMATION) {
            throw new IllegalStateException("订单状态不允许买家确认");
        }

        this.completionStatus = OrderCompletionStatus.CONFIRMED;

        addDomainEvent(new CustomizedOrderConfirmedEvent(
            this.id, this.supplierCompletion.getTotalPrice()));
    }
}

// 产品需求文档值对象
public record ProductRequirementDocument(
    @NotNull String fileName,
    @NotNull String fileUrl,
    @NotNull String fileHash,
    @NotNull Long fileSize,
    @NotNull LocalDateTime uploadedAt,
    String description
) {
    public ProductRequirementDocument {
        if (fileName == null || fileName.isBlank()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        if (fileUrl == null || fileUrl.isBlank()) {
            throw new IllegalArgumentException("文件URL不能为空");
        }
        if (fileSize <= 0) {
            throw new IllegalArgumentException("文件大小必须大于0");
        }
    }

    public boolean isPdfFile() {
        return fileName.toLowerCase().endsWith(".pdf");
    }
}

// 供应商订单完善实体
@Entity
@Table(name = "supplier_order_completions")
public class SupplierOrderCompletion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "customized_order_id")
    private CustomizedReplenishmentOrderId customizedOrderId;

    // 产品详细规格
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "product_specifications", columnDefinition = "jsonb")
    private ProductSpecifications specifications;

    // 价格信息
    @Embedded
    private PricingDetails pricingDetails;

    // 交期信息
    @Column(name = "delivery_date")
    private LocalDate deliveryDate;

    @Column(name = "estimated_production_days")
    private Integer estimatedProductionDays;

    // 备注信息
    @Column(name = "supplier_notes", length = 1000)
    private String supplierNotes;

    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    // 业务方法
    public Money getTotalPrice() {
        return pricingDetails.getTotalPrice();
    }

    public void updateSpecifications(ProductSpecifications newSpecs) {
        this.specifications = newSpecs;
    }

    public void updatePricing(PricingDetails newPricing) {
        this.pricingDetails = newPricing;
    }
}

// 定制化补货应用服务
@Service
@Transactional
public class CustomizedReplenishmentApplicationService {

    private final CustomizedReplenishmentOrderRepository orderRepository;
    private final FileUploadService fileUploadService;
    private final PaymentService paymentService;

    public CustomizedReplenishmentResult createCustomizedReplenishment(
            CreateCustomizedReplenishmentCommand command) {

        // 1. 上传PDF文件
        var uploadResult = fileUploadService.uploadFile(
            command.getPdfFile(),
            FileType.PRODUCT_REQUIREMENT_DOCUMENT
        );

        // 2. 创建产品需求文档
        var document = new ProductRequirementDocument(
            command.getPdfFile().getOriginalFilename(),
            uploadResult.getFileUrl(),
            uploadResult.getFileHash(),
            command.getPdfFile().getSize(),
            LocalDateTime.now(),
            command.getDescription()
        );

        // 3. 创建定制补货订单
        var order = CustomizedReplenishmentOrder.create(
            command.getBuyerId(),
            command.getSupplierId(),
            command.getInventoryItemId(),
            document,
            command.getDepositAmount()
        );

        // 4. 保存订单
        orderRepository.save(order);

        return CustomizedReplenishmentResult.success(order.getId());
    }

    public PaymentResult payDeposit(PayDepositCommand command) {
        var order = orderRepository.findById(command.getOrderId())
            .orElseThrow(() -> new CustomizedOrderNotFoundException(command.getOrderId()));

        // 处理支付
        var paymentResult = paymentService.processPayment(
            command.getPaymentMethod(),
            command.getAmount()
        );

        if (paymentResult.isSuccess()) {
            order.payDeposit(paymentResult.getPaymentInfo());
            orderRepository.save(order);
        }

        return paymentResult;
    }

    public void completeOrderBySupplier(CompleteOrderBySupplierCommand command) {
        var order = orderRepository.findById(command.getOrderId())
            .orElseThrow(() -> new CustomizedOrderNotFoundException(command.getOrderId()));

        var completion = new SupplierOrderCompletion();
        completion.setSpecifications(command.getSpecifications());
        completion.setPricingDetails(command.getPricingDetails());
        completion.setDeliveryDate(command.getDeliveryDate());
        completion.setSupplierNotes(command.getNotes());
        completion.setCompletedAt(LocalDateTime.now());

        order.completeBySupplier(completion);
        orderRepository.save(order);
    }
}
```

#### 第14-15周：物流运输上下文
**目标**：实现货代管理功能
- [ ] 设计ShippingRequirement和ShippingOrder聚合根
- [ ] 实现货代需求自动生成
- [ ] 实现货代竞价功能
- [ ] 实现运输订单管理

### 3.4 阶段4：基础设施服务（第16-18周）

#### 第16周：财务结算上下文
**目标**：实现结算功能
- [ ] 设计Settlement聚合根
- [ ] 实现多方结算逻辑
- [ ] 实现佣金计算功能

#### 第15周：通信协作上下文
**目标**：实现消息通讯功能
- [ ] 设计ChatRoom聚合根
- [ ] 实现实时聊天功能
- [ ] 实现消息通知功能

#### 第16周：基础设施上下文
**目标**：实现文件管理等基础功能
- [ ] 实现文件上传下载功能
- [ ] 实现系统配置管理
- [ ] 实现数据分析功能

### 3.5 阶段5：数据迁移和测试（第17-19周）

#### 第17周：数据迁移
**目标**：完成数据迁移
- [ ] 开发数据迁移脚本
- [ ] 执行数据迁移测试
- [ ] 验证数据完整性

#### 第18周：集成测试
**目标**：完成系统集成测试
- [ ] 端到端业务流程测试
- [ ] 性能测试和优化
- [ ] 安全测试

#### 第19周：用户验收测试
**目标**：用户验收
- [ ] 用户培训和测试
- [ ] 问题修复和优化
- [ ] 上线准备

### 3.6 阶段6：上线和监控（第20-21周）

#### 第20周：生产部署
**目标**：部署到生产环境
- [ ] 生产环境配置
- [ ] 数据库迁移执行
- [ ] 系统部署和验证

#### 第21周：监控和优化
**目标**：系统稳定运行
- [ ] 监控系统配置
- [ ] 性能调优
- [ ] 问题修复

## 4. 资源分配

### 4.1 团队组织
```
项目经理 (1人)
├── 架构师 (1人)
├── 后端开发团队 (4人)
│   ├── 高级开发工程师 (2人)
│   └── 中级开发工程师 (2人)
├── 测试团队 (2人)
│   ├── 测试工程师 (1人)
│   └── 自动化测试工程师 (1人)
└── 运维工程师 (1人)
```

### 4.2 角色职责

#### 项目经理
- 项目整体规划和进度管理
- 资源协调和风险控制
- 与业务方沟通协调

#### 架构师
- 技术架构设计和评审
- 关键技术难点攻关
- 代码质量把控

#### 高级开发工程师
- 核心领域模型设计
- 复杂业务逻辑实现
- 技术方案制定

#### 中级开发工程师
- 具体功能开发实现
- 单元测试编写
- 文档编写

#### 测试工程师
- 测试用例设计
- 功能测试执行
- 缺陷跟踪管理

#### 自动化测试工程师
- 自动化测试框架搭建
- 集成测试脚本开发
- 性能测试执行

#### 运维工程师
- 环境搭建和维护
- 部署脚本开发
- 监控系统配置

## 5. 风险管理

### 5.1 技术风险

#### 风险1：DDD学习曲线陡峭
- **影响**：开发效率降低，代码质量不达标
- **概率**：中等
- **应对措施**：
  - 提前进行DDD培训
  - 安排有经验的架构师指导
  - 建立代码评审机制

#### 风险2：性能不达标
- **影响**：系统响应慢，用户体验差
- **概率**：中等
- **应对措施**：
  - 早期进行性能测试
  - 建立性能基准
  - 预留性能优化时间

#### 风险3：数据迁移失败
- **影响**：系统无法上线，数据丢失
- **概率**：低
- **应对措施**：
  - 充分的迁移测试
  - 数据备份策略
  - 回滚方案准备

### 5.2 项目风险

#### 风险1：进度延期
- **影响**：项目无法按时交付
- **概率**：中等
- **应对措施**：
  - 合理的时间估算
  - 定期进度检查
  - 关键路径管理

#### 风险2：需求变更
- **影响**：开发工作量增加
- **概率**：中等
- **应对措施**：
  - 需求冻结机制
  - 变更影响评估
  - 预留缓冲时间

#### 风险3：人员流失
- **影响**：项目进度受影响
- **概率**：低
- **应对措施**：
  - 知识文档化
  - 交叉培训
  - 备用人员计划

## 6. 质量保证

### 6.1 代码质量
- **代码覆盖率**：单元测试覆盖率 > 80%
- **代码规范**：通过SonarQube质量门禁
- **代码评审**：所有代码必须经过评审

### 6.2 测试策略
- **单元测试**：每个类都有对应的单元测试
- **集成测试**：关键业务流程的集成测试
- **端到端测试**：完整业务场景的自动化测试
- **性能测试**：关键接口的性能测试

### 6.3 文档要求
- **技术文档**：架构设计、API文档、部署文档
- **用户文档**：操作手册、FAQ
- **运维文档**：监控手册、故障处理手册

## 7. 沟通计划

### 7.1 定期会议
- **每日站会**：每天上午9:30，15分钟
- **周例会**：每周五下午，1小时
- **里程碑评审**：每个里程碑结束后，2小时
- **项目汇报**：每月向管理层汇报进展

### 7.2 沟通渠道
- **即时沟通**：企业微信群
- **文档协作**：Confluence
- **问题跟踪**：JIRA
- **代码协作**：GitLab

### 7.3 汇报机制
- **日报**：每日进展和问题
- **周报**：周进展总结和下周计划
- **月报**：月度总结和风险评估
- **里程碑报告**：里程碑完成情况和质量评估
