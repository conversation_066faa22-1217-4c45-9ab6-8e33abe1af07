# 采购系统DDD重写 - 实施路线图 (Kotlin技术栈)

## 1. 长远实施战略

### 1.1 战略目标
- 构建面向未来的企业级采购平台架构
- 建立可持续发展的技术和组织能力
- 实现业务敏捷性和技术先进性的平衡
- 为数字化转型和生态化发展奠定基础

### 1.2 实施原则
- **价值驱动**：优先实现高业务价值的功能模块
- **风险可控**：采用渐进式演进，降低实施风险
- **能力建设**：注重团队能力提升和知识积累
- **质量优先**：建立高标准的质量保证体系

### 1.3 成功标准
- **技术指标**：架构清晰、代码质量高、性能优异
- **业务指标**：功能完整、用户体验好、业务价值实现
- **团队指标**：技能提升、协作高效、知识沉淀
- **长远指标**：可扩展、可维护、可演进

## 2. 项目里程碑

### 2.1 整体时间线
```
阶段1: 基础设施搭建    ├─────────────┤ (3周)
阶段2: 核心领域建模    ├─────────────────────────┤ (6周)  
阶段3: 扩展领域实现    ├─────────────────┤ (4周)
阶段4: 基础设施服务    ├─────────────┤ (3周)
阶段5: 数据迁移测试    ├─────────────┤ (3周)
阶段6: 上线和监控      ├─────┤ (2周)
                      └─────────────────────────────────┘
                      0    5    10   15   20   25 (周)
```

### 2.2 关键里程碑
| 里程碑 | 时间 | 交付物 | 长远价值 |
|--------|------|--------|----------|
| M1: 技术基础设施 | 第4周 | DDD框架、CI/CD、监控 | 为长期发展奠定技术基础 |
| M2: 身份权限体系 | 第8周 | 统一身份认证平台 | 支持未来多租户和联邦认证 |
| M3: 采购领域核心 | 第12周 | 采购需求和竞价上下文 | 建立专业化采购能力 |
| M4: 订单履约体系 | 第16周 | 订单管理和履约体系 | 构建端到端履约能力 |
| M5: 物流服务平台 | 第20周 | 物流竞价和执行上下文 | 建立智能物流服务能力 |
| M6: 财务结算体系 | 第24周 | 多方结算和风控体系 | 构建可信的财务基础设施 |
| M7: 生态协作平台 | 第28周 | 完整业务生态 | 实现平台化运营能力 |

## 3. 详细实施计划

### 3.1 阶段1：基础设施搭建（第1-3周）

#### 第1周：项目初始化 (Kotlin技术栈)
**目标**：建立Kotlin项目基础结构
- [ ] 创建新的Git仓库和分支策略
- [ ] 搭建Maven多模块Kotlin项目结构
- [ ] 配置Spring Boot 3.5.4 + Kotlin 2.1.x基础框架
- [ ] 建立Kotlin代码规范和质量检查工具
- [ ] 配置Kotlin协程支持

**交付物**：
- Kotlin项目骨架代码
- Kotlin开发环境配置文档
- Kotlin代码规范文档
- 协程配置示例

#### 第2周：基础设施组件
**目标**：搭建核心基础设施
- [ ] 配置PostgreSQL 16+数据库连接和事务管理
- [ ] 集成Redis 7.x缓存和分布式锁
- [ ] 配置Apache Kafka 3.9.x消息队列
- [ ] 建立基于Kafka的事件驱动架构基础
- [ ] 配置Elasticsearch 8.x搜索引擎

**交付物**：
- Spring Boot 3.5.4 + Kotlin基础设施配置代码
- PostgreSQL数据库初始化脚本和JSON支持配置
- Kafka主题配置和Spring Kafka Kotlin集成
- Elasticsearch索引模板和Kotlin客户端配置
- Kotlin协程优化的数据库和消息队列配置

#### 第3周：开发工具链
**目标**：完善开发和部署工具
- [ ] 建立CI/CD流水线
- [ ] 配置测试框架（单元测试、集成测试）
- [ ] 集成代码质量检查工具
- [ ] 建立监控和日志系统

**交付物**：
- CI/CD配置文件
- 测试框架配置
- 监控配置文档

### 3.2 阶段2：核心领域建模（第4-9周）

#### 第4-5周：用户身份上下文
**目标**：实现用户管理和认证授权
- [ ] 设计User聚合根和相关实体
- [ ] 实现JWT认证机制
- [ ] 实现基于角色的权限控制
- [ ] 实现用户资料管理功能

**技术任务**：
```kotlin
// 使用Hibernate 6.6.x和Kotlin特性的领域模型
@Entity
@Table(name = "users")
class User(
    @EmbeddedId
    val id: UserId,

    @Embedded
    var email: Email,  // 使用数据类作为值对象

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "profile", columnDefinition = "jsonb")
    var profile: UserProfile,  // JSON存储复杂对象

    @ElementCollection(fetch = FetchType.LAZY)
    @Enumerated(EnumType.STRING)
    private val _roles: MutableSet<Role> = mutableSetOf()

) : AggregateRoot<UserId>() {

    // 只读属性暴露角色集合
    val roles: Set<Role> get() = _roles.toSet()

    // 业务方法 - 使用协程
    suspend fun authenticate(credentials: Credentials): Result<Unit> = runCatching {
        // 认证逻辑
    }

    fun updateProfile(newProfile: UserProfile): User = apply {
        profile = newProfile
        addDomainEvent(UserProfileUpdatedEvent(id))
    }

    // JPA自动发布领域事件
    @DomainEvents
    fun domainEvents(): Collection<DomainEvent> = uncommittedEvents

    // JPA需要的无参构造函数
    protected constructor() : this(
        UserId.empty(),
        Email.empty(),
        UserProfile.empty()
    )
}

// Spring Data JPA Repository - 协程支持
interface UserRepository : JpaRepository<User, UserId> {
    suspend fun findByEmail(email: Email): User?
    suspend fun findByRolesContaining(role: Role): List<User>

    // 扩展函数提供便利方法
    suspend fun findByEmailOrThrow(email: Email): User =
        findByEmail(email) ?: throw UserNotFoundException("User not found: ${email.value}")
}

// 应用服务 - 协程优化
@Service
@Transactional
class UserApplicationService(
    private val userRepository: UserRepository
) {

    suspend fun createUser(command: CreateUserCommand): UserResponse {
        var user = User.create(command);
        userRepository.save(user);  // 自动处理事件发布
        return UserResponse.from(user);
    }
}
```

#### 第6-7周：采购需求上下文
**目标**：实现需求发布和管理功能
- [ ] 设计ProcurementRequirement聚合根
- [ ] 实现需求发布流程
- [ ] 实现需求分类管理
- [ ] 实现需求搜索功能

**技术任务**：
```kotlin
// 使用Kotlin现代化技术栈的领域模型
@Entity
@Table(name = "procurement_requirements")
class ProcurementRequirement private constructor(
    @EmbeddedId
    val id: RequirementId,

    @Column(name = "buyer_id")
    val buyerId: UserId,

    // 利用PostgreSQL的JSON类型存储复杂规格
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "specification", columnDefinition = "jsonb")
    var specification: RequirementSpec,

    // 聚合内实体的级联管理
    @OneToMany(cascade = [CascadeType.ALL], mappedBy = "requirement", orphanRemoval = true)
    private val _items: MutableList<RequirementItem> = mutableListOf(),

    @Enumerated(EnumType.STRING)
    var status: RequirementStatus = RequirementStatus.DRAFT

) : AggregateRoot<RequirementId>() {

    // 只读属性暴露items集合
    val items: List<RequirementItem> get() = _items.toList()

    // JPA需要的无参构造函数
    protected constructor() : this(
        RequirementId.empty(),
        UserId.empty(),
        RequirementSpec.empty()
    )

    companion object {
        // 工厂方法创建需求
        fun create(
            buyerId: UserId,
            specification: RequirementSpec
        ): ProcurementRequirement {
            require(specification.isValid) { "需求规格无效" }

            return ProcurementRequirement(
                id = RequirementId.generate(),
                buyerId = buyerId,
                specification = specification
            ).apply {
                addDomainEvent(RequirementCreatedEvent(id, buyerId))
            }
        }
    }

    // 业务方法 - 使用Kotlin简洁语法
    fun publish(): ProcurementRequirement = apply {
        require(status == RequirementStatus.DRAFT) { "只有草稿状态才能发布" }
        status = RequirementStatus.PUBLISHED
        addDomainEvent(RequirementPublishedEvent(id))
    }

    fun addItem(item: RequirementItem): ProcurementRequirement = apply {
        require(status == RequirementStatus.DRAFT) { "只有草稿状态才能添加项目" }
        _items.add(item)
        item.setRequirement(this)
        addDomainEvent(RequirementItemAddedEvent(id, item.id))
    }

    // 使用协程的异步业务方法
    suspend fun validateAndPublish(
        validationService: RequirementValidationService
    ): Result<ProcurementRequirement> = runCatching {
        validationService.validate(this@ProcurementRequirement)
        publish()
    }

    // 扩展属性
    val isPublished: Boolean get() = status == RequirementStatus.PUBLISHED
    val itemCount: Int get() = _items.size
    val totalEstimatedValue: Money get() = _items.sumOf { it.estimatedValue }
}

// Repository with complex queries - 协程支持
interface ProcurementRequirementRepository : JpaRepository<ProcurementRequirement, RequirementId> {

    // 利用PostgreSQL的JSON查询能力
    @Query(value = """
        SELECT * FROM procurement_requirements
        WHERE specification @> :categoryFilter::jsonb
        AND status = :status
        """, nativeQuery = true)
    suspend fun findBySpecificationCategoryAndStatus(
        @Param("categoryFilter") categoryFilter: String,
        @Param("status") status: String
    ): List<ProcurementRequirement>

    // 使用Kotlin扩展函数的便利方法
    suspend fun findByBuyerIdAndStatus(buyerId: UserId, status: RequirementStatus): List<ProcurementRequirement>

    // 复杂的JSONB查询
    @Query(value = """
        SELECT * FROM procurement_requirements
        WHERE specification->'budget'->>'amount' BETWEEN :minAmount AND :maxAmount
        AND specification->>'category' = :category
        """, nativeQuery = true)
    suspend fun findByBudgetRangeAndCategory(
        @Param("minAmount") minAmount: String,
        @Param("maxAmount") maxAmount: String,
        @Param("category") category: String
    ): List<ProcurementRequirement>
}

// 扩展函数提供便利方法
suspend fun ProcurementRequirementRepository.findByIdOrThrow(id: RequirementId): ProcurementRequirement =
    findById(id).orElseThrow { RequirementNotFoundException("需求不存在: $id") }

suspend fun ProcurementRequirementRepository.findPublishedRequirements(): List<ProcurementRequirement> =
    findByStatus(RequirementStatus.PUBLISHED)
```

#### 第8-9周：竞价交易上下文
**目标**：实现竞价投标功能
- [ ] 设计BiddingProcess聚合根
- [ ] 实现竞价提交流程
- [ ] 实现竞价审核机制
- [ ] 实现中标选择逻辑

### 3.3 阶段3：扩展领域实现（第10-13周）

#### 第10-11周：订单履约上下文
**目标**：实现订单管理功能
- [ ] 设计Order聚合根
- [ ] 实现订单创建流程
- [ ] 实现支付管理功能
- [ ] 实现订单状态跟踪

#### 第12-13周：库存管理上下文
**目标**：实现基于信任关系的智能库存管理功能
- [ ] 设计Inventory聚合根和供应商信任关系模型
- [ ] 实现库存录入和原始采购信息关联
- [ ] 实现基于信任关系的智能补货算法
- [ ] 实现一键补货和供应商直接通知机制
- [ ] 实现定制化补货流程（PDF上传和订单完善）
- [ ] 实现供应商信任评分体系

**技术任务**：
```kotlin
// 基于信任关系的库存聚合根设计
@Entity
@Table(name = "inventories")
class Inventory private constructor(
    @EmbeddedId
    val id: InventoryId,

    @Column(name = "buyer_id")
    val buyerId: UserId,

    @Column(name = "warehouse_name")
    var warehouseName: String,

    // 库存商品列表
    @OneToMany(cascade = [CascadeType.ALL], mappedBy = "inventory", orphanRemoval = true)
    private val _items: MutableList<InventoryItem> = mutableListOf(),

    // 信任供应商关系
    @OneToMany(cascade = [CascadeType.ALL], mappedBy = "inventory", orphanRemoval = true)
    private val _trustedSuppliers: MutableList<TrustedSupplierRelationship> = mutableListOf()

) : AggregateRoot<InventoryId>() {

    // 只读属性暴露集合
    val items: List<InventoryItem> get() = _items.toList()
    val trustedSuppliers: List<TrustedSupplierRelationship> get() = _trustedSuppliers.toList()

    // JPA需要的无参构造函数
    protected constructor() : this(
        InventoryId.empty(),
        UserId.empty(),
        ""
    )

    companion object {
        // 工厂方法创建库存
        fun create(buyerId: UserId, warehouseName: String): Inventory {
            require(warehouseName.isNotBlank()) { "仓库名称不能为空" }

            return Inventory(
                id = InventoryId.generate(),
                buyerId = buyerId,
                warehouseName = warehouseName
            ).apply {
                addDomainEvent(InventoryCreatedEvent(id, buyerId))
            }
        }
    }

    // 业务方法 - 使用Kotlin简洁语法
    fun addItemWithOriginalPurchase(
        item: InventoryItem,
        purchaseInfo: OriginalPurchaseInfo
    ): Inventory = apply {
        _items.add(item)
        item.setInventory(this)
        item.linkOriginalPurchase(purchaseInfo)

        // 建立或更新供应商信任关系
        establishTrustRelationship(purchaseInfo)

        addDomainEvent(InventoryItemAddedEvent(id, item.id))
        addDomainEvent(OriginalPurchaseLinkedEvent(id, item.id, purchaseInfo.supplierId))
    }

    fun recordStockMovement(itemId: InventoryItemId, movement: StockMovement): Inventory = apply {
        val item = findItemById(itemId)
        item.recordMovement(movement)

        // 检查是否需要预警
        if (item.isBelowSafetyStock) {
            addDomainEvent(InventoryAlertTriggeredEvent(id, itemId, item.currentStock))

            // 检查是否可以推荐信任供应商补货
            item.originalPurchase?.let { originalPurchase ->
                if (isTrustedSupplier(originalPurchase.supplierId)) {
                    addDomainEvent(TrustedSupplierReplenishmentRecommendedEvent(
                        id, itemId, originalPurchase.supplierId
                    ))
                }
            }
        }
    }

    // 使用协程的异步补货方法
    suspend fun executeQuickReplenish(
        itemId: InventoryItemId,
        quantity: StockQuantity,
        maxPrice: Money
    ): Result<QuickReplenishResult> = runCatching {
        val item = findItemById(itemId)
        val originalPurchase = item.originalPurchase
            ?: throw QuickReplenishNotAvailableException("该商品不支持一键补货")

        require(isTrustedSupplier(originalPurchase.supplierId)) { "供应商不在信任列表中" }

        // 验证价格是否在可接受范围内
        val maxAllowedPrice = originalPurchase.lastPurchasePrice * 1.2.toBigDecimal() // 最多涨价20%
        require(maxPrice <= maxAllowedPrice) { "补货价格超出可接受范围" }

        // 创建快速补货订单
        val order = createQuickReplenishOrder(item, quantity, maxPrice)

        addDomainEvent(QuickReplenishOrderCreatedEvent(
            id, itemId, originalPurchase.supplierId, order.id
        ))

        QuickReplenishResult.success(order.id)
    }

    private fun establishTrustRelationship(purchaseInfo: OriginalPurchaseInfo) {
        val existingRelationship = findTrustedSupplier(purchaseInfo.supplierId)

        if (existingRelationship != null) {
            existingRelationship.updateFromNewPurchase(purchaseInfo)
        } else {
            val newRelationship = TrustedSupplierRelationship.create(
                id, purchaseInfo.supplierId, purchaseInfo
            )
            _trustedSuppliers.add(newRelationship)

            addDomainEvent(SupplierTrustRelationshipEstablishedEvent(
                id, purchaseInfo.supplierId
            ))
        }
    }

    // 扩展属性和便利方法
    val itemCount: Int get() = _items.size
    val trustedSupplierCount: Int get() = _trustedSuppliers.size
    val lowStockItems: List<InventoryItem> get() = _items.filter { it.isBelowSafetyStock }

    private fun findItemById(itemId: InventoryItemId): InventoryItem =
        _items.find { it.id == itemId }
            ?: throw InventoryItemNotFoundException("库存项目不存在: $itemId")

    private fun findTrustedSupplier(supplierId: SupplierId): TrustedSupplierRelationship? =
        _trustedSuppliers.find { it.supplierId == supplierId }

    private fun isTrustedSupplier(supplierId: SupplierId): Boolean =
        _trustedSuppliers.any { it.supplierId == supplierId && it.trustScore.isHigh }
}

// 库存商品实体 - 包含原始采购信息
@Entity
@Table(name = "inventory_items")
class InventoryItem private constructor(
    @EmbeddedId
    val id: InventoryItemId,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inventory_id")
    var inventory: Inventory?,

    @Embedded
    var productInfo: ProductInfo,

    @Embedded
    var stockLevel: StockLevel,

    // 原始采购信息
    @Embedded
    var originalPurchase: OriginalPurchaseInfo? = null,

    // 补货偏好设置
    @Embedded
    var preference: ReplenishmentPreference? = null,

    @OneToMany(cascade = [CascadeType.ALL], mappedBy = "inventoryItem")
    private val _movements: MutableList<StockMovement> = mutableListOf()

) {
    // 只读属性暴露移动记录
    val movements: List<StockMovement> get() = _movements.toList()

    // JPA需要的无参构造函数
    protected constructor() : this(
        InventoryItemId.empty(),
        null,
        ProductInfo.empty(),
        StockLevel.empty()
    )

    companion object {
        // 工厂方法创建库存项目
        fun create(
            productInfo: ProductInfo,
            initialStock: StockQuantity,
            safetyStock: StockQuantity
        ): InventoryItem {
            require(initialStock.isPositive) { "初始库存必须大于0" }
            require(safetyStock.isPositive) { "安全库存必须大于0" }

            return InventoryItem(
                id = InventoryItemId.generate(),
                inventory = null,
                productInfo = productInfo,
                stockLevel = StockLevel.create(initialStock, safetyStock)
            )
        }
    }

    // 业务方法 - 使用Kotlin简洁语法
    fun linkOriginalPurchase(purchaseInfo: OriginalPurchaseInfo): InventoryItem = apply {
        originalPurchase = purchaseInfo
        // 根据采购历史设置默认补货偏好
        preference = ReplenishmentPreference.createDefault(purchaseInfo)
    }

    fun recordMovement(movement: StockMovement): InventoryItem = apply {
        _movements.add(movement)
        movement.setInventoryItem(this)

        // 更新当前库存
        stockLevel = when (movement.type) {
            MovementType.IN -> stockLevel.increase(movement.quantity)
            MovementType.OUT -> stockLevel.decrease(movement.quantity)
        }
    }

    // 扩展属性 - 使用Kotlin的便利语法
    val isBelowSafetyStock: Boolean
        get() = stockLevel.currentQuantity < stockLevel.safetyStock

    val canQuickReplenish: Boolean
        get() = originalPurchase != null &&
                preference?.preferOriginalSupplier == true &&
                originalPurchase?.qualityRating?.isGood == true

    val currentStock: StockQuantity
        get() = stockLevel.currentQuantity

    val daysOfStock: Int
        get() = stockLevel.calculateDaysOfStock(movements)

    // 使用协程的异步方法
    suspend fun checkReplenishmentNeeded(): Boolean = withContext(Dispatchers.Default) {
        isBelowSafetyStock || daysOfStock < 7
    }

    fun setInventory(inventory: Inventory) {
        this.inventory = inventory
    }
}

// 信任供应商关系实体
@Entity
@Table(name = "trusted_supplier_relationships")
class TrustedSupplierRelationship private constructor(
    @EmbeddedId
    val id: TrustedSupplierRelationshipId,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inventory_id")
    var inventory: Inventory?,

    @Column(name = "supplier_id")
    val supplierId: SupplierId,

    @Embedded
    var trustScore: TrustScore,

    @Column(name = "total_orders")
    var totalOrders: Int = 0,

    @Column(name = "successful_orders")
    var successfulOrders: Int = 0,

    @Column(name = "last_order_date")
    var lastOrderDate: LocalDateTime,

    @Column(name = "average_delivery_time")
    var averageDeliveryTime: Int = 0

) {
    // JPA需要的无参构造函数
    protected constructor() : this(
        TrustedSupplierRelationshipId.empty(),
        null,
        SupplierId.empty(),
        TrustScore.empty(),
        lastOrderDate = LocalDateTime.now()
    )

    companion object {
        // 工厂方法创建信任关系
        fun create(
            inventoryId: InventoryId,
            supplierId: SupplierId,
            purchaseInfo: OriginalPurchaseInfo
        ): TrustedSupplierRelationship {
            return TrustedSupplierRelationship(
                id = TrustedSupplierRelationshipId.generate(),
                inventory = null,
                supplierId = supplierId,
                totalOrders = 1,
                successfulOrders = 1,
                lastOrderDate = purchaseInfo.lastPurchaseDate,
                trustScore = TrustScore.calculateInitial(purchaseInfo)
            )
        }
    }

    // 业务方法 - 使用Kotlin简洁语法
    fun updateFromNewPurchase(purchaseInfo: OriginalPurchaseInfo): TrustedSupplierRelationship = apply {
        totalOrders++
        successfulOrders++
        lastOrderDate = purchaseInfo.lastPurchaseDate

        // 重新计算信任评分
        trustScore = trustScore.updateWith(purchaseInfo)
    }

    // 扩展属性 - 使用Kotlin的便利语法
    val isEligibleForQuickReplenish: Boolean
        get() = trustScore.value >= 80 && // 信任评分80分以上
                successfulOrders >= 2 &&  // 至少2次成功订单
                isRecentlyActive          // 最近有交易活动

    val isRecentlyActive: Boolean
        get() = lastOrderDate.isAfter(LocalDateTime.now().minusMonths(6))

    val successRate: Double
        get() = if (totalOrders > 0) successfulOrders.toDouble() / totalOrders else 0.0

    val isHighTrust: Boolean
        get() = trustScore.value >= 90 && successRate >= 0.95
}

// 值对象 - 使用Kotlin数据类
data class OriginalPurchaseInfo(
    @field:NotNull val supplierId: SupplierId,
    @field:NotNull val supplierName: String,
    @field:NotNull val originalOrderId: OrderId,
    @field:NotNull val lastPurchasePrice: Money,
    @field:NotNull val lastPurchaseDate: LocalDateTime,
    @field:NotNull val lastPurchaseQuantity: Int,
    @field:NotNull val qualityRating: QualityRating,
    @field:NotNull val deliveryPerformance: DeliveryPerformance
) {
    // 构造函数验证
    init {
        require(lastPurchaseQuantity > 0) { "采购数量必须大于0" }
        require(lastPurchaseDate.isBefore(LocalDateTime.now())) { "采购日期不能是未来时间" }
        require(supplierName.isNotBlank()) { "供应商名称不能为空" }
    }

    // 扩展属性
    val isRecentPurchase: Boolean
        get() = lastPurchaseDate.isAfter(LocalDateTime.now().minusMonths(3))

    val isHighQuality: Boolean
        get() = qualityRating.isGood && deliveryPerformance.isExcellent

    companion object {
        fun empty(): OriginalPurchaseInfo = OriginalPurchaseInfo(
            supplierId = SupplierId.empty(),
            supplierName = "",
            originalOrderId = OrderId.empty(),
            lastPurchasePrice = Money.ZERO,
            lastPurchaseDate = LocalDateTime.now().minusYears(1),
            lastPurchaseQuantity = 1,
            qualityRating = QualityRating.UNKNOWN,
            deliveryPerformance = DeliveryPerformance.UNKNOWN
        )
    }
}

data class ReplenishmentPreference(
    val preferOriginalSupplier: Boolean,    // 是否优先原供应商
    @field:NotNull val maxPriceIncrease: Money,   // 可接受的最大涨价幅度
    @field:NotNull val defaultReplenishQuantity: Int,  // 默认补货数量
    val autoReplenish: Boolean             // 是否自动补货
) {
    companion object {
        fun createDefault(purchaseInfo: OriginalPurchaseInfo): ReplenishmentPreference =
            ReplenishmentPreference(
                preferOriginalSupplier = true,  // 默认优先原供应商
                maxPriceIncrease = purchaseInfo.lastPurchasePrice * 0.2.toBigDecimal(), // 最多涨价20%
                defaultReplenishQuantity = purchaseInfo.lastPurchaseQuantity, // 默认按上次数量补货
                autoReplenish = false  // 默认不自动补货
            )

        fun conservative(): ReplenishmentPreference = ReplenishmentPreference(
            preferOriginalSupplier = true,
            maxPriceIncrease = Money.ZERO,
            defaultReplenishQuantity = 1,
            autoReplenish = false
        )
    }

    // 扩展属性
    val isConservative: Boolean
        get() = !autoReplenish && maxPriceIncrease.isZero

    val allowsPriceIncrease: Boolean
        get() = maxPriceIncrease.isPositive
}

data class TrustScore(
    @field:NotNull val value: Int,           // 信任评分 0-100
    @field:NotNull val lastUpdated: LocalDateTime,
    @field:NotNull val calculationBasis: String  // 计算依据
) {
    // 构造函数验证
    init {
        require(value in 0..100) { "信任评分必须在0-100之间" }
        require(calculationBasis.isNotBlank()) { "计算依据不能为空" }
    }

    companion object {
        fun calculateInitial(purchaseInfo: OriginalPurchaseInfo): TrustScore {
            var score = 60 // 基础分

            // 根据质量评分调整
            score += purchaseInfo.qualityRating.scoreAdjustment

            // 根据交期表现调整
            score += purchaseInfo.deliveryPerformance.scoreAdjustment

            return TrustScore(
                value = score.coerceIn(0, 100),
                lastUpdated = LocalDateTime.now(),
                calculationBasis = "基于首次采购表现的初始评分"
            )
        }

        fun empty(): TrustScore = TrustScore(
            value = 0,
            lastUpdated = LocalDateTime.now(),
            calculationBasis = "空评分"
        )
    }

    fun updateWith(newPurchaseInfo: OriginalPurchaseInfo): TrustScore {
        // 基于新的采购信息更新信任评分
        var newScore = value

        // 质量表现影响
        newScore += newPurchaseInfo.qualityRating.scoreAdjustment

        // 交期表现影响
        newScore += newPurchaseInfo.deliveryPerformance.scoreAdjustment

        // 平滑处理，避免单次交易影响过大
        newScore = (value * 3 + newScore) / 4

        return TrustScore(
            value = newScore.coerceIn(0, 100),
            lastUpdated = LocalDateTime.now(),
            calculationBasis = "基于历史交易记录的综合评分"
        )
    }

    // 扩展属性
    val isHigh: Boolean get() = value >= 80
    val isMedium: Boolean get() = value in 50..79
    val isLow: Boolean get() = value < 50
    val grade: String get() = when {
        value >= 90 -> "优秀"
        value >= 80 -> "良好"
        value >= 60 -> "一般"
        value >= 40 -> "较差"
        else -> "很差"
    }
}

public record StockLevel(
    @NotNull StockQuantity currentQuantity,
    @NotNull StockQuantity safetyStock,
    @NotNull StockQuantity reorderPoint
) {
    public StockLevel increase(StockQuantity quantity) {
        return new StockLevel(
            currentQuantity.add(quantity),
            safetyStock,
            reorderPoint
        );
    }

    public StockLevel decrease(StockQuantity quantity) {
        return new StockLevel(
            currentQuantity.subtract(quantity),
            safetyStock,
            reorderPoint
        );
    }
}

// 基于信任关系的智能补货服务
@Service
public class TrustBasedReplenishmentService {

    private final SupplierAvailabilityChecker availabilityChecker;
    private final TrustScoreCalculator trustScoreCalculator;

    public ReplenishmentRecommendation generateRecommendation(InventoryItem item) {
        var originalPurchase = item.getOriginalPurchase();
        var preference = item.getPreference();

        if (originalPurchase == null) {
            return createCompetitiveBiddingRecommendation(item, "无历史采购记录");
        }

        // 检查原供应商是否可用
        var supplierAvailability = availabilityChecker.checkAvailability(originalPurchase.supplierId());

        if (!supplierAvailability.isAvailable()) {
            return createCompetitiveBiddingRecommendation(item, "原供应商当前不可用");
        }

        // 检查信任评分
        var trustScore = trustScoreCalculator.calculateCurrentScore(originalPurchase);

        if (trustScore.value() >= 80 && preference.preferOriginalSupplier()) {
            return createTrustedSupplierRecommendation(item, originalPurchase, supplierAvailability, trustScore);
        } else {
            return createCompetitiveBiddingRecommendation(item, "信任评分不足或用户偏好竞价");
        }
    }

    private ReplenishmentRecommendation createTrustedSupplierRecommendation(
            InventoryItem item,
            OriginalPurchaseInfo originalPurchase,
            SupplierAvailability availability,
            TrustScore trustScore) {

        // 基于历史价格计算推荐价格
        var recommendedPrice = calculateRecommendedPrice(originalPurchase, availability);

        // 基于消耗速度计算推荐数量
        var recommendedQuantity = calculateRecommendedQuantity(item);

        return ReplenishmentRecommendation.builder()
            .type(ReplenishmentType.TRUSTED_SUPPLIER)
            .supplierId(originalPurchase.supplierId())
            .supplierName(originalPurchase.supplierName())
            .recommendedPrice(recommendedPrice)
            .recommendedQuantity(recommendedQuantity)
            .trustScore(trustScore)
            .estimatedDeliveryTime(availability.getEstimatedDeliveryTime())
            .maxPriceIncrease(item.getPreference().maxPriceIncrease())
            .reasoning("基于与该供应商的成功合作历史，推荐一键补货")
            .canQuickReplenish(true)
            .build();
    }

    private ReplenishmentRecommendation createCompetitiveBiddingRecommendation(
            InventoryItem item, String reason) {

        var recommendedQuantity = calculateRecommendedQuantity(item);

        return ReplenishmentRecommendation.builder()
            .type(ReplenishmentType.COMPETITIVE_BIDDING)
            .recommendedQuantity(recommendedQuantity)
            .reasoning(reason + "，建议发起竞价补货")
            .canQuickReplenish(false)
            .build();
    }

    private Money calculateRecommendedPrice(OriginalPurchaseInfo originalPurchase,
                                           SupplierAvailability availability) {
        var basePrice = originalPurchase.lastPurchasePrice();

        // 考虑市场波动和供应商当前状况
        var adjustmentFactor = availability.getPriceAdjustmentFactor();

        return basePrice.multiply(adjustmentFactor);
    }

    private StockQuantity calculateRecommendedQuantity(InventoryItem item) {
        // 基于历史消耗速度和安全库存计算
        var consumptionRate = analyzeConsumptionRate(item);
        var safetyStock = item.getStockLevel().safetyStock();
        var currentStock = item.getStockLevel().currentQuantity();

        // 推荐补货到安全库存的2倍
        var targetStock = safetyStock.multiply(2);
        var recommendedQuantity = targetStock.subtract(currentStock);

        return recommendedQuantity.isPositive() ? recommendedQuantity : item.getPreference().defaultReplenishQuantity();
    }
}

// 一键补货应用服务
@Service
@Transactional
public class QuickReplenishmentApplicationService {

    private final InventoryRepository inventoryRepository;
    private final OrderRepository orderRepository;
    private final SupplierNotificationService supplierNotificationService;

    public QuickReplenishResult executeQuickReplenish(QuickReplenishCommand command) {
        // 1. 获取库存商品
        var inventory = inventoryRepository.findById(command.getInventoryId())
            .orElseThrow(() -> new InventoryNotFoundException(command.getInventoryId()));

        // 2. 执行一键补货
        var result = inventory.executeQuickReplenish(
            command.getItemId(),
            command.getQuantity(),
            command.getMaxPrice()
        );

        // 3. 保存变更
        inventoryRepository.save(inventory);

        // 4. 异步通知供应商
        var item = inventory.findItemById(command.getItemId());
        var originalPurchase = item.getOriginalPurchase();

        CompletableFuture.runAsync(() -> {
            supplierNotificationService.notifyQuickReplenishOrder(
                originalPurchase.supplierId(),
                result.getOrderId(),
                command.getQuantity(),
                command.getMaxPrice()
            );
        });

        return result;
    }
}

// 定制化补货聚合根
@Entity
@Table(name = "customized_replenishment_orders")
public class CustomizedReplenishmentOrder extends AggregateRoot<CustomizedReplenishmentOrderId> {

    @EmbeddedId
    private CustomizedReplenishmentOrderId id;

    @Column(name = "buyer_id")
    private UserId buyerId;

    @Column(name = "supplier_id")
    private SupplierId supplierId;

    @Column(name = "inventory_item_id")
    private InventoryItemId inventoryItemId;

    // 产品需求文档
    @Embedded
    private ProductRequirementDocument requirementDocument;

    // 订单完善状态
    @Enumerated(EnumType.STRING)
    private OrderCompletionStatus completionStatus;

    // 定金信息
    @Embedded
    private DepositInfo depositInfo;

    // 供应商完善的订单详情
    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "supplier_completion_id")
    private SupplierOrderCompletion supplierCompletion;

    // 创建定制补货订单
    public static CustomizedReplenishmentOrder create(
            UserId buyerId,
            SupplierId supplierId,
            InventoryItemId inventoryItemId,
            ProductRequirementDocument document,
            Money depositAmount) {

        var order = new CustomizedReplenishmentOrder();
        order.id = CustomizedReplenishmentOrderId.generate();
        order.buyerId = buyerId;
        order.supplierId = supplierId;
        order.inventoryItemId = inventoryItemId;
        order.requirementDocument = document;
        order.completionStatus = OrderCompletionStatus.PENDING_DEPOSIT;
        order.depositInfo = DepositInfo.create(depositAmount);

        order.addDomainEvent(new CustomizedReplenishmentOrderCreatedEvent(
            order.id, buyerId, supplierId, document.getFileUrl()));

        return order;
    }

    // 支付定金
    public void payDeposit(PaymentInfo paymentInfo) {
        if (this.completionStatus != OrderCompletionStatus.PENDING_DEPOSIT) {
            throw new IllegalStateException("订单状态不允许支付定金");
        }

        this.depositInfo = this.depositInfo.markAsPaid(paymentInfo);
        this.completionStatus = OrderCompletionStatus.PENDING_SUPPLIER_COMPLETION;

        addDomainEvent(new DepositPaidEvent(this.id, paymentInfo.getAmount()));
        addDomainEvent(new SupplierOrderCompletionRequestedEvent(
            this.id, this.supplierId, this.requirementDocument.getFileUrl()));
    }

    // 供应商完善订单
    public void completeBySupplier(SupplierOrderCompletion completion) {
        if (this.completionStatus != OrderCompletionStatus.PENDING_SUPPLIER_COMPLETION) {
            throw new IllegalStateException("订单状态不允许供应商完善");
        }

        this.supplierCompletion = completion;
        this.completionStatus = OrderCompletionStatus.PENDING_BUYER_CONFIRMATION;

        addDomainEvent(new OrderDetailsCompletedBySupplierEvent(
            this.id, completion.getTotalPrice(), completion.getDeliveryDate()));
    }

    // 买家确认订单
    public void confirmByBuyer() {
        if (this.completionStatus != OrderCompletionStatus.PENDING_BUYER_CONFIRMATION) {
            throw new IllegalStateException("订单状态不允许买家确认");
        }

        this.completionStatus = OrderCompletionStatus.CONFIRMED;

        addDomainEvent(new CustomizedOrderConfirmedEvent(
            this.id, this.supplierCompletion.getTotalPrice()));
    }
}

// 产品需求文档值对象
public record ProductRequirementDocument(
    @NotNull String fileName,
    @NotNull String fileUrl,
    @NotNull String fileHash,
    @NotNull Long fileSize,
    @NotNull LocalDateTime uploadedAt,
    String description
) {
    public ProductRequirementDocument {
        if (fileName == null || fileName.isBlank()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        if (fileUrl == null || fileUrl.isBlank()) {
            throw new IllegalArgumentException("文件URL不能为空");
        }
        if (fileSize <= 0) {
            throw new IllegalArgumentException("文件大小必须大于0");
        }
    }

    public boolean isPdfFile() {
        return fileName.toLowerCase().endsWith(".pdf");
    }
}

// 供应商订单完善实体
@Entity
@Table(name = "supplier_order_completions")
public class SupplierOrderCompletion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "customized_order_id")
    private CustomizedReplenishmentOrderId customizedOrderId;

    // 产品详细规格
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "product_specifications", columnDefinition = "jsonb")
    private ProductSpecifications specifications;

    // 价格信息
    @Embedded
    private PricingDetails pricingDetails;

    // 交期信息
    @Column(name = "delivery_date")
    private LocalDate deliveryDate;

    @Column(name = "estimated_production_days")
    private Integer estimatedProductionDays;

    // 备注信息
    @Column(name = "supplier_notes", length = 1000)
    private String supplierNotes;

    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    // 业务方法
    public Money getTotalPrice() {
        return pricingDetails.getTotalPrice();
    }

    public void updateSpecifications(ProductSpecifications newSpecs) {
        this.specifications = newSpecs;
    }

    public void updatePricing(PricingDetails newPricing) {
        this.pricingDetails = newPricing;
    }
}

// 定制化补货应用服务
@Service
@Transactional
public class CustomizedReplenishmentApplicationService {

    private final CustomizedReplenishmentOrderRepository orderRepository;
    private final FileUploadService fileUploadService;
    private final PaymentService paymentService;

    public CustomizedReplenishmentResult createCustomizedReplenishment(
            CreateCustomizedReplenishmentCommand command) {

        // 1. 上传PDF文件
        var uploadResult = fileUploadService.uploadFile(
            command.getPdfFile(),
            FileType.PRODUCT_REQUIREMENT_DOCUMENT
        );

        // 2. 创建产品需求文档
        var document = new ProductRequirementDocument(
            command.getPdfFile().getOriginalFilename(),
            uploadResult.getFileUrl(),
            uploadResult.getFileHash(),
            command.getPdfFile().getSize(),
            LocalDateTime.now(),
            command.getDescription()
        );

        // 3. 创建定制补货订单
        var order = CustomizedReplenishmentOrder.create(
            command.getBuyerId(),
            command.getSupplierId(),
            command.getInventoryItemId(),
            document,
            command.getDepositAmount()
        );

        // 4. 保存订单
        orderRepository.save(order);

        return CustomizedReplenishmentResult.success(order.getId());
    }

    public PaymentResult payDeposit(PayDepositCommand command) {
        var order = orderRepository.findById(command.getOrderId())
            .orElseThrow(() -> new CustomizedOrderNotFoundException(command.getOrderId()));

        // 处理支付
        var paymentResult = paymentService.processPayment(
            command.getPaymentMethod(),
            command.getAmount()
        );

        if (paymentResult.isSuccess()) {
            order.payDeposit(paymentResult.getPaymentInfo());
            orderRepository.save(order);
        }

        return paymentResult;
    }

    public void completeOrderBySupplier(CompleteOrderBySupplierCommand command) {
        var order = orderRepository.findById(command.getOrderId())
            .orElseThrow(() -> new CustomizedOrderNotFoundException(command.getOrderId()));

        var completion = new SupplierOrderCompletion();
        completion.setSpecifications(command.getSpecifications());
        completion.setPricingDetails(command.getPricingDetails());
        completion.setDeliveryDate(command.getDeliveryDate());
        completion.setSupplierNotes(command.getNotes());
        completion.setCompletedAt(LocalDateTime.now());

        order.completeBySupplier(completion);
        orderRepository.save(order);
    }
}
```

#### 第14-15周：物流运输上下文
**目标**：实现货代管理功能
- [ ] 设计ShippingRequirement和ShippingOrder聚合根
- [ ] 实现货代需求自动生成
- [ ] 实现货代竞价功能
- [ ] 实现运输订单管理

### 3.4 阶段4：基础设施服务（第16-18周）

#### 第16周：财务结算上下文
**目标**：实现结算功能
- [ ] 设计Settlement聚合根
- [ ] 实现多方结算逻辑
- [ ] 实现佣金计算功能

#### 第15周：通信协作上下文
**目标**：实现消息通讯功能
- [ ] 设计ChatRoom聚合根
- [ ] 实现实时聊天功能
- [ ] 实现消息通知功能

#### 第16周：基础设施上下文
**目标**：实现文件管理等基础功能
- [ ] 实现文件上传下载功能
- [ ] 实现系统配置管理
- [ ] 实现数据分析功能

### 3.5 阶段5：数据迁移和测试（第17-19周）

#### 第17周：数据迁移
**目标**：完成数据迁移
- [ ] 开发数据迁移脚本
- [ ] 执行数据迁移测试
- [ ] 验证数据完整性

#### 第18周：集成测试
**目标**：完成系统集成测试
- [ ] 端到端业务流程测试
- [ ] 性能测试和优化
- [ ] 安全测试

#### 第19周：用户验收测试
**目标**：用户验收
- [ ] 用户培训和测试
- [ ] 问题修复和优化
- [ ] 上线准备

### 3.6 阶段6：上线和监控（第20-21周）

#### 第20周：生产部署
**目标**：部署到生产环境
- [ ] 生产环境配置
- [ ] 数据库迁移执行
- [ ] 系统部署和验证

#### 第21周：监控和优化
**目标**：系统稳定运行
- [ ] 监控系统配置
- [ ] 性能调优
- [ ] 问题修复

## 4. 资源分配

### 4.1 团队组织
```
项目经理 (1人)
├── 架构师 (1人)
├── 后端开发团队 (4人)
│   ├── 高级开发工程师 (2人)
│   └── 中级开发工程师 (2人)
├── 测试团队 (2人)
│   ├── 测试工程师 (1人)
│   └── 自动化测试工程师 (1人)
└── 运维工程师 (1人)
```

### 4.2 角色职责

#### 项目经理
- 项目整体规划和进度管理
- 资源协调和风险控制
- 与业务方沟通协调

#### 架构师
- 技术架构设计和评审
- 关键技术难点攻关
- 代码质量把控

#### 高级开发工程师
- 核心领域模型设计
- 复杂业务逻辑实现
- 技术方案制定

#### 中级开发工程师
- 具体功能开发实现
- 单元测试编写
- 文档编写

#### 测试工程师
- 测试用例设计
- 功能测试执行
- 缺陷跟踪管理

#### 自动化测试工程师
- 自动化测试框架搭建
- 集成测试脚本开发
- 性能测试执行

#### 运维工程师
- 环境搭建和维护
- 部署脚本开发
- 监控系统配置

## 5. 风险管理

### 5.1 技术风险

#### 风险1：DDD学习曲线陡峭
- **影响**：开发效率降低，代码质量不达标
- **概率**：中等
- **应对措施**：
  - 提前进行DDD培训
  - 安排有经验的架构师指导
  - 建立代码评审机制

#### 风险2：性能不达标
- **影响**：系统响应慢，用户体验差
- **概率**：中等
- **应对措施**：
  - 早期进行性能测试
  - 建立性能基准
  - 预留性能优化时间

#### 风险3：数据迁移失败
- **影响**：系统无法上线，数据丢失
- **概率**：低
- **应对措施**：
  - 充分的迁移测试
  - 数据备份策略
  - 回滚方案准备

### 5.2 项目风险

#### 风险1：进度延期
- **影响**：项目无法按时交付
- **概率**：中等
- **应对措施**：
  - 合理的时间估算
  - 定期进度检查
  - 关键路径管理

#### 风险2：需求变更
- **影响**：开发工作量增加
- **概率**：中等
- **应对措施**：
  - 需求冻结机制
  - 变更影响评估
  - 预留缓冲时间

#### 风险3：人员流失
- **影响**：项目进度受影响
- **概率**：低
- **应对措施**：
  - 知识文档化
  - 交叉培训
  - 备用人员计划

## 6. 质量保证

### 6.1 代码质量
- **代码覆盖率**：单元测试覆盖率 > 80%
- **代码规范**：通过SonarQube质量门禁
- **代码评审**：所有代码必须经过评审

### 6.2 测试策略
- **单元测试**：每个类都有对应的单元测试
- **集成测试**：关键业务流程的集成测试
- **端到端测试**：完整业务场景的自动化测试
- **性能测试**：关键接口的性能测试

### 6.3 文档要求
- **技术文档**：架构设计、API文档、部署文档
- **用户文档**：操作手册、FAQ
- **运维文档**：监控手册、故障处理手册

## 7. 沟通计划

### 7.1 定期会议
- **每日站会**：每天上午9:30，15分钟
- **周例会**：每周五下午，1小时
- **里程碑评审**：每个里程碑结束后，2小时
- **项目汇报**：每月向管理层汇报进展

### 7.2 沟通渠道
- **即时沟通**：企业微信群
- **文档协作**：Confluence
- **问题跟踪**：JIRA
- **代码协作**：GitLab

### 7.3 汇报机制
- **日报**：每日进展和问题
- **周报**：周进展总结和下周计划
- **月报**：月度总结和风险评估
- **里程碑报告**：里程碑完成情况和质量评估
