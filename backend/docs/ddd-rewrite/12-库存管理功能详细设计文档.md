# 采购系统DDD重写 - 库存管理功能详细设计

## 📋 功能概述

库存管理功能是采购系统的重要创新，通过建立基于信任关系的智能补货机制，实现从传统的"每次竞价"模式升级为"信任关系优先"模式，大幅简化补货流程，提升用户体验。

## 🎯 核心价值主张

### 买家价值
- **极简操作**：3步完成补货（预警→设置数量→确认下单）
- **降低风险**：基于成功合作历史，供应商可靠性更高
- **价格透明**：基于历史价格，避免价格欺诈
- **快速响应**：紧急补货时可以快速下单

### 供应商价值
- **客户粘性**：成功合作后获得优先补货机会
- **稳定收入**：老客户重复订单提供稳定收入来源
- **简化流程**：无需重新竞价，直接接收补货订单
- **信任溢价**：基于信任关系可以获得合理的价格溢价

### 平台价值
- **差异化服务**：提供独特的信任基础补货服务
- **提升粘性**：买家和供应商都更依赖平台的信任体系
- **降低成本**：减少竞价流程的运营和客服成本
- **数据价值**：积累更精准的供应商-买家匹配数据

## 🏗️ 功能架构设计

### 核心功能模块

#### 1. 库存档案管理
- **库存录入**：买家录入现有库存信息
- **原始采购关联**：关联库存商品的原始采购记录
- **产品信息管理**：维护详细的产品规格和属性
- **仓库位置管理**：支持多仓库库存管理

#### 2. 信任关系建立
- **采购历史分析**：基于历史采购订单建立供应商档案
- **信任评分计算**：综合质量、交期、服务等维度评分
- **信任关系维护**：持续更新和优化信任评分
- **信任门槛设置**：设定享受一键补货的信任门槛

#### 3. 智能库存监控
- **实时库存跟踪**：监控库存数量变化
- **安全库存预警**：低于安全库存时自动预警
- **消耗模式分析**：分析历史消耗数据识别模式
- **需求预测**：基于历史数据预测未来需求

#### 4. 一键补货机制
- **信任供应商推荐**：优先推荐信任度高的原供应商
- **智能价格建议**：基于历史价格提供合理价格建议
- **快速下单流程**：用户确认后直接生成订单
- **供应商直接通知**：订单生成后直接通知供应商

#### 5. 定制化补货机制
- **PDF需求文档上传**：支持用户上传产品需求文档（PM PDF）
- **先付后完善模式**：基于信任关系支持用户先支付定金
- **供应商订单完善**：供应商根据PDF文档填写详细规格和价格
- **协商确认流程**：买家确认供应商完善的订单详情
- **文档智能解析**：AI辅助解析PDF内容，提取关键信息

#### 6. 风险控制机制
- **价格涨幅限制**：限制补货价格涨幅在合理范围内
- **供应商可用性检查**：验证供应商当前状态和能力
- **订单金额限制**：设定单次补货的最大金额限制
- **异常情况处理**：价格异常或供应商不可用时转竞价流程

## 📊 详细功能规格

### 1. 库存录入功能

#### 1.1 功能描述
买家可以录入现有库存信息，系统自动关联原始采购记录，建立库存商品档案。

#### 1.2 输入信息
- **基础信息**：商品名称、规格型号、品牌、单位
- **库存信息**：当前数量、安全库存、预警库存
- **仓库信息**：仓库位置、存储条件、管理员
- **采购信息**：原始订单号、供应商信息、采购价格、采购日期

#### 1.3 业务规则
- 库存数量必须为非负数
- 安全库存应小于当前库存
- 必须关联有效的原始采购记录
- 同一商品在同一仓库不能重复录入

#### 1.4 输出结果
- 生成唯一的库存商品ID
- 建立库存商品档案
- 关联原始采购信息
- 触发信任关系建立流程

### 2. 信任关系建立功能

#### 2.1 功能描述
基于历史采购记录自动建立买家与供应商的信任关系，计算信任评分。

#### 2.2 信任评分算法
```
信任评分 = 基础分(60) + 质量评分调整 + 交期评分调整 + 服务评分调整

质量评分调整：
- 优秀(95-100分)：+20分
- 良好(85-94分)：+10分
- 一般(70-84分)：+0分
- 较差(60-69分)：-10分
- 很差(60分以下)：-20分

交期评分调整：
- 提前交付：+10分
- 准时交付：+5分
- 延期1-3天：+0分
- 延期4-7天：-5分
- 延期超过7天：-15分

服务评分调整：
- 响应及时，服务优秀：+10分
- 响应正常，服务良好：+5分
- 响应一般，服务普通：+0分
- 响应较慢，服务较差：-5分
- 响应很慢，服务很差：-10分
```

#### 2.3 信任等级划分
- **高度信任(90-100分)**：享受一键补货，价格涨幅限制10%
- **中度信任(80-89分)**：享受一键补货，价格涨幅限制20%
- **基础信任(70-79分)**：可推荐补货，但需要竞价确认
- **信任不足(70分以下)**：只能通过竞价补货

### 3. 一键补货功能

#### 3.1 功能描述
对于信任度高的供应商，买家可以跳过竞价流程，直接下单补货。

#### 3.2 触发条件
- 库存低于安全库存线
- 原供应商信任评分≥80分
- 原供应商当前状态可用
- 买家设置优先原供应商

#### 3.3 补货流程
1. **系统预警**：库存低于安全线时自动预警
2. **推荐供应商**：系统推荐信任度高的原供应商
3. **价格建议**：基于历史价格提供补货价格建议
4. **用户确认**：买家设置补货数量和最高接受价格
5. **订单生成**：系统直接生成补货订单
6. **供应商通知**：直接通知原供应商确认订单

#### 3.4 价格保护机制
- **涨幅限制**：根据信任等级限制价格涨幅
- **市场价格参考**：参考同类商品的市场价格
- **异常价格处理**：价格异常时提示用户选择竞价
- **价格历史记录**：记录所有补货价格变化

### 4. 定制化补货功能

#### 4.1 功能描述
对于有特殊需求或规格变更的补货场景，买家可以上传产品需求文档（PM PDF），基于信任关系先支付定金，供应商根据文档完善订单详情。

#### 4.2 适用场景
- **产品规格变更**：在原有产品基础上有新的规格要求
- **定制化需求**：需要特殊定制的产品功能或外观
- **技术升级**：产品需要技术升级或改进
- **批量定制**：大批量采购时的特殊要求

#### 4.3 业务流程
1. **选择供应商**：从信任供应商列表中选择合作伙伴
2. **上传需求文档**：上传详细的产品需求PDF文档
3. **系统生成订单**：创建包含PDF URL的初始订单
4. **支付定金**：基于信任关系先行支付约定比例的定金
5. **供应商完善**：供应商查看PDF并填写详细规格、价格、交期
6. **买家确认**：买家审核供应商完善的订单详情
7. **协商调整**：如有异议可以协商调整方案
8. **正式执行**：双方确认后订单正式执行

#### 4.4 业务规则
- **信任门槛**：只有信任评分≥80分的供应商才能享受此服务
- **定金比例**：定金比例根据信任等级确定（10%-30%）
- **文档格式**：只支持PDF格式的需求文档
- **文件大小**：单个文件不超过50MB
- **完善时限**：供应商需在48小时内完善订单详情
- **确认时限**：买家需在24小时内确认或提出修改意见

#### 4.5 风险控制
- **定金保护**：如供应商无法满足需求，定金可退还
- **价格上限**：设定价格上限，超出需重新协商
- **交期保证**：供应商需承诺明确的交期
- **质量保证**：按照PDF需求文档执行质量标准

## 🔧 技术实现要点

### 1. 数据模型设计
- **库存聚合根**：管理库存商品和信任关系
- **定制补货订单聚合根**：管理定制化补货流程
- **信任关系实体**：记录买家-供应商信任信息
- **供应商订单完善实体**：记录供应商完善的订单详情
- **补货偏好值对象**：用户的补货偏好设置
- **产品需求文档值对象**：PDF文档的元数据信息
- **信任评分值对象**：信任评分的计算和更新
- **订单完善状态值对象**：定制订单的状态管理

### 2. 事件驱动架构
- **库存预警事件**：触发补货推荐流程
- **信任关系建立事件**：建立新的信任关系
- **一键补货事件**：执行快速补货操作
- **定制补货订单创建事件**：创建定制化补货订单
- **产品需求文档上传事件**：PDF文档上传完成
- **定金支付事件**：用户支付定金
- **供应商订单完善请求事件**：通知供应商完善订单
- **订单详情完善事件**：供应商完善订单详情
- **定制订单确认事件**：买家确认定制订单
- **信任评分更新事件**：更新供应商信任评分

### 3. 集成接口设计
- **与采购需求上下文集成**：库存预警触发采购需求
- **与订单履约上下文集成**：订单完成后自动入库，定制订单执行
- **与智能运营上下文集成**：提供库存数据支持推荐
- **与财务结算上下文集成**：库存成本核算，定金和尾款结算
- **与文件存储服务集成**：PDF文档的上传、存储和访问
- **与支付服务集成**：定金支付和订单结算
- **与通知服务集成**：供应商通知和订单状态更新

### 4. 文件处理和AI解析
- **文件上传服务**：支持PDF文件的安全上传和存储
- **文档预览服务**：在线预览PDF需求文档
- **AI文档解析**：智能提取PDF中的关键产品规格信息
- **文档版本管理**：支持需求文档的版本控制和历史记录

## 📈 预期效果

### 业务指标提升
- **标准补货效率**：从平均30分钟减少到5分钟，提升83%
- **定制补货效率**：从传统2-3天减少到1天内完成订单确认，提升67%
- **用户满意度**：简化流程和定制化服务，预期用户满意度提升50%
- **供应商粘性**：信任机制和定制化合作预期提升供应商留存率40%
- **平台交易量**：便捷补货和定制化服务预期带动交易量增长35%
- **客单价提升**：定制化产品通常价值更高，预期客单价提升20%

### 运营成本降低
- **客服成本**：减少补货相关咨询，客服成本降低30%
- **运营成本**：简化竞价流程，运营成本降低25%
- **系统负载**：减少竞价系统调用，系统负载降低20%
- **沟通成本**：PDF文档明确需求，减少反复沟通，沟通成本降低40%

### 新增收入来源
- **定制化服务费**：可对定制化补货服务收取额外服务费
- **文档解析服务**：AI文档解析可作为增值服务
- **优先处理费**：紧急定制需求可收取优先处理费
- **供应商会员费**：高级信任供应商可推出会员服务

这个库存管理功能将成为采购系统的重要差异化优势，通过信任关系的建立和维护，为买家和供应商创造更大价值，同时提升平台的竞争力和用户粘性。
