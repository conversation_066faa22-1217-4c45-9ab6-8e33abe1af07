# 采购系统DDD重写 - 技术架构方案

## 1. 长远技术战略

### 1.1 技术选型原则
- **成熟稳定**：选择经过大规模生产验证的技术栈
- **生态丰富**：拥有活跃社区和完善生态的技术
- **前瞻性**：支持未来5-10年技术发展趋势
- **团队匹配**：与团队技能和组织能力相匹配

### 1.2 核心技术栈

#### 应用框架层
- **Spring Boot 3.3.4**：当前稳定版企业级应用框架，完美支持Kotlin
- **Spring Framework 6.1.12**：核心IoC和AOP框架，原生支持Kotlin特性
- **Spring Security 6.3.3**：企业级安全框架，OAuth2和JWT支持
- **Spring Data JPA + Hibernate 6.4.10**：ORM框架，与Kotlin数据类完美集成
- **Kotlin 2.0.20**：现代编程语言，简洁语法、空安全、协程支持
- **Kotlin Coroutines**：异步编程框架，比虚拟线程更优雅的解决方案

#### 数据存储层

##### ORM技术栈详解
**Spring Data JPA + Hibernate 6.4.x 组合架构**：

```
┌─────────────────────────────────────┐
│        业务代码层                    │
│   (Repository, Service, Entity)     │
└─────────────────┬───────────────────┘
                  │ 调用
┌─────────────────▼───────────────────┐
│         Spring Data JPA             │  ← 提供Repository接口和查询方法
│    (简化数据访问的抽象层)              │    自动生成90%的数据访问代码
└─────────────────┬───────────────────┘
                  │ 底层实现
┌─────────────────▼───────────────────┐
│             JPA 规范                │  ← Java官方ORM标准
│        (定义ORM的标准接口)            │    提供统一的API规范
└─────────────────┬───────────────────┘
                  │ 具体实现
┌─────────────────▼───────────────────┐
│         Hibernate 6.4.x            │  ← JPA规范的具体实现
│      (真正的ORM引擎)                 │    负责对象-数据库映射
└─────────────────┬───────────────────┘
                  │ 操作
┌─────────────────▼───────────────────┐
│         PostgreSQL 16+              │  ← 数据存储
└─────────────────────────────────────┘
```

**Kotlin + Spring Data JPA相比Java + MyBatis Plus的DDD优势**：

| 方面 | Java + MyBatis Plus | Kotlin + Spring Data JPA | DDD价值 |
|------|---------------------|---------------------------|---------|
| **开发理念** | SQL优先，数据驱动 | 对象优先，领域驱动 | 更符合DDD理念 |
| **聚合根支持** | 手动管理关联关系 | 自动管理聚合边界 | 完美支持聚合模式 |
| **值对象支持** | 需要手动转换器 | Kotlin数据类天然支持 | 零样板代码的值对象 |
| **事件发布** | 手动实现 | @DomainEvents自动发布 | 简化事件驱动架构 |
| **代码量** | 大量样板代码 | Kotlin简洁语法，极少代码 | 专注业务逻辑 |
| **类型安全** | SQL字符串，运行时错误 | 编译时类型检查+空安全 | 更安全的代码 |
| **异步处理** | CompletableFuture复杂 | Kotlin协程简洁优雅 | 更好的异步体验 |
| **函数式编程** | Stream API冗长 | Kotlin集合操作简洁 | 更优雅的数据处理 |

**数据存储组件**：
- **PostgreSQL 16+**：企业级关系数据库，强大的JSON支持和全文搜索能力
- **Redis 7.x**：高性能缓存、发布订阅和分布式锁
- **Elasticsearch 8.x**：分布式搜索引擎和复杂数据分析
- **InfluxDB 2.x**：时序数据存储，用于用户行为数据和业务指标
- **阿里云OSS/AWS S3**：对象存储，文件和图片存储

#### 消息和事件系统
- **Apache Kafka 3.9.x**：高吞吐量事件流处理平台
- **Spring Kafka 3.3.x**：Spring Boot完美集成，声明式消息处理
- **Spring Cloud Stream**：流处理框架，简化消息驱动开发
- **PostgreSQL事件存储**：基于PostgreSQL的自建事件存储，利用JSON能力

#### 可观测性和监控
- **Micrometer 1.15.x**：指标收集和业务监控
- **Spring Cloud Sleuth + Zipkin**：分布式链路追踪
- **Prometheus + Grafana**：监控数据存储、告警和可视化
- **ELK Stack (Elasticsearch + Logstash + Kibana)**：日志聚合和分析
- **Spring Boot Actuator**：应用健康检查和运行时监控

#### 容器化和编排
- **Docker**：容器化技术
- **Kubernetes**：容器编排平台
- **Helm**：Kubernetes应用包管理
- **Istio**：服务网格（未来考虑）

## 2. 架构模式

### 2.1 CQRS + 事件驱动模式
分离命令（写操作）和查询（读操作），通过事件实现最终一致性：

```kotlin
// 命令处理器 - 充分利用Kotlin特性
@Component
class ProcurementCommandHandler(
    private val repository: ProcurementRepository,
    private val kafkaTemplate: KafkaTemplate<String, Any>
) {

    @CommandHandler
    @Transactional
    suspend fun handle(command: CreateRequirementCommand): Result<RequirementId> = runCatching {
        // 使用作用域函数让代码更流畅
        ProcurementRequirement.create(command).also { requirement ->
            repository.save(requirement)
            requirement.uncommittedEvents.publishToKafka()
        }.id
    }

    // 扩展函数让业务逻辑更自然
    private suspend fun List<DomainEvent>.publishToKafka() {
        // 使用协程并发发布，并处理异常
        supervisorScope {
            map { event ->
                async {
                    kafkaTemplate.send("requirement-events", event)
                        .whenComplete { _, throwable ->
                            throwable?.let {
                                logger.error("Failed to publish event: ${event.eventType}", it)
                            }
                        }
                }
            }.awaitAll()
        }
    }

    // 使用内联函数优化性能
    private inline fun <T> withEventPublishing(
        crossinline block: () -> T
    ): T = block().also { result ->
        // 事件发布逻辑
    }
}

// 查询处理器 - 充分利用Kotlin特性
@Component
class RequirementQueryHandler(
    private val readModelRepo: RequirementReadModelRepository,
    private val elasticsearchTemplate: ElasticsearchTemplate
) {

    @QueryHandler
    @Cacheable("requirements")
    suspend fun handle(query: GetRequirementsQuery): Page<RequirementView> =
        readModelRepo.findByFilters(query.filters, query.pageable)
            .map(RequirementView::from) // 方法引用更简洁

    @QueryHandler
    suspend fun searchRequirements(query: SearchRequirementsQuery): List<RequirementView> =
        query.toSearchQuery()
            .let { searchQuery ->
                elasticsearchTemplate.search(searchQuery, RequirementDocument::class.java)
            }
            .map(RequirementView::from)

    // 使用扩展函数增强查询能力
    suspend fun GetRequirementsQuery.executeWithFallback(): Page<RequirementView> =
        runCatching { handle(this) }
            .getOrElse {
                logger.warn("Primary query failed, using fallback", it)
                readModelRepo.findAll(pageable).map(RequirementView::from)
            }

    // 使用委托属性实现懒加载
    private val searchQueryBuilder by lazy {
        SearchQueryBuilder().apply {
            withDefaultAnalyzer("ik_max_word")
            withHighlight("title", "description")
        }
    }
}

// 事件处理器 - 充分利用Kotlin特性
@Component
class RequirementProjectionHandler(
    private val readModelRepository: RequirementReadModelRepository,
    private val elasticsearchTemplate: ElasticsearchTemplate,
    private val cacheManager: CacheManager
) {

    @KafkaListener(topics = ["requirement-events"])
    @Transactional
    suspend fun on(event: RequirementCreatedEvent) {
        // 使用作用域函数和并发处理
        event.processProjections {
            listOf(
                async { updateReadModel(event) },
                async { updateSearchIndex(event) },
                async { evictCache(event.requirementId) }
            ).awaitAll()
        }
    }

    // 扩展函数让事件处理更自然
    private suspend inline fun <T> T.processProjections(
        crossinline block: suspend CoroutineScope.() -> Unit
    ) = supervisorScope { block() }

    // 使用扩展函数分离关注点
    private suspend fun updateReadModel(event: RequirementCreatedEvent) =
        RequirementReadModel.from(event)
            .let { readModelRepository.save(it) }

    private suspend fun updateSearchIndex(event: RequirementCreatedEvent) =
        RequirementDocument.from(event)
            .let { elasticsearchTemplate.save(it) }

    private suspend fun evictCache(requirementId: RequirementId) =
        cacheManager.getCache("requirements")
            ?.takeIf { it.get(requirementId) != null }
            ?.evict(requirementId)

    // 使用密封类处理不同事件类型
    suspend fun handleEvent(event: DomainEvent) = when (event) {
        is RequirementCreatedEvent -> on(event)
        is RequirementUpdatedEvent -> updateProjections(event)
        is RequirementDeletedEvent -> removeProjections(event)
        else -> logger.debug("Unhandled event type: ${event::class.simpleName}")
    }
}
```

### 2.2 基于PostgreSQL的事件存储
利用PostgreSQL的JSON能力构建高性能事件存储：

```kotlin
// 事件存储实体 - 利用PostgreSQL的JSON类型
@Entity
@Table(name = "event_store")
data class EventStoreEntity(
    @Id
    val eventId: String,

    @Column(name = "aggregate_id")
    val aggregateId: String,

    @Column(name = "aggregate_type")
    val aggregateType: String,

    @Column(name = "event_type")
    val eventType: String,

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "event_data", columnDefinition = "jsonb")
    val eventData: String,

    @Column(name = "event_metadata", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    val eventMetadata: String,

    @Column(name = "occurred_at")
    val occurredAt: LocalDateTime,

    @Column(name = "version")
    val version: Long,

    // 索引优化查询
    @Column(name = "stream_id")
    val streamId: String // aggregate_type:aggregate_id
)

// 事件存储服务
@Service
@Transactional
class PostgreSQLEventStore(
    private val eventStoreRepository: EventStoreRepository,
    private val objectMapper: ObjectMapper,
    private val kafkaTemplate: KafkaTemplate<String, Any>
) {

    suspend fun saveEvents(
        aggregateId: String,
        aggregateType: String,
        events: List<DomainEvent>,
        expectedVersion: Long
    ) {
        // 1. 乐观锁检查
        validateVersion(aggregateId, aggregateType, expectedVersion)

        // 2. 保存事件到PostgreSQL
        val eventEntities = events.map { event ->
            createEventEntity(aggregateId, aggregateType, event)
        }

        eventStoreRepository.saveAll(eventEntities)

        // 3. 异步发布到Kafka (使用协程)
        publishEventsAsync(events)
    }

    suspend fun getEvents(aggregateId: String, aggregateType: String): List<DomainEvent> {
        val streamId = "$aggregateType:$aggregateId"

        return eventStoreRepository.findByStreamIdOrderByVersionAsc(streamId)
            .map { deserializeEvent(it) }
    }

    // 支持事件回放和投影重建
    suspend fun getAllEventsAfter(timestamp: LocalDateTime): Flow<DomainEvent> {
        return eventStoreRepository.findByOccurredAtAfterOrderByOccurredAtAsc(timestamp)
            .asFlow()
            .map { deserializeEvent(it) }
    }
}

// 聚合根重建服务
@Service
class AggregateRehydrationService(
    private val eventStore: PostgreSQLEventStore
) {

    suspend inline fun <reified T : AggregateRoot> rehydrate(
        aggregateId: String,
        aggregateType: String
    ): T {
        val events = eventStore.getEvents(aggregateId, aggregateType)

        return try {
            val aggregate = T::class.createInstance()
            events.forEach { aggregate.apply(it) }
            aggregate.markEventsAsCommitted()
            aggregate
        } catch (e: Exception) {
            throw AggregateRehydrationException(
                "Failed to rehydrate aggregate: $aggregateId", e
            )
        }
    }
}
```

### 2.3 六边形架构
将业务逻辑与外部依赖隔离：

```
                    ┌─────────────────────────────────┐
                    │         Application Core        │
                    │                                 │
    ┌───────────────┤         Domain Layer            ├───────────────┐
    │               │                                 │               │
    │               └─────────────────────────────────┘               │
    │                                                                 │
┌───▼───┐                                                         ┌───▼───┐
│ REST  │                                                         │Database│
│ API   │                                                         │Repository│
└───────┘                                                         └───────┘
    │                                                                 │
┌───▼───┐                                                         ┌───▼───┐
│WebSocket│                                                       │Message│
│Handler │                                                         │Queue  │
└───────┘                                                         └───────┘
```

### 2.4 Kotlin现代特性应用

#### 2.4.1 协程支持 - 比虚拟线程更优雅
```kotlin
// 协程配置
@Configuration
class CoroutineConfig {

    @Bean
    fun coroutineScope(): CoroutineScope {
        return CoroutineScope(
            SupervisorJob() +
            Dispatchers.Default +
            CoroutineName("ApplicationScope")
        )
    }

    @Bean
    fun webMvcConfigurer(): WebMvcConfigurer {
        return object : WebMvcConfigurer {
            override fun configureAsyncSupport(configurer: AsyncSupportConfigurer) {
                configurer.setDefaultTimeout(30000)
                configurer.setTaskExecutor(TaskExecutorAdapter(Dispatchers.Default.asExecutor()))
            }
        }
    }
}

// 异步处理使用协程 - 比虚拟线程更轻量
@Service
class AsyncRequirementProcessor(
    private val coroutineScope: CoroutineScope
) {

    suspend fun processRequirementAsync(id: RequirementId) {
        // 协程处理，比虚拟线程更轻量，内存占用更少
        withContext(Dispatchers.IO) {
            // 长时间运行的业务逻辑
            processRequirement(id)
        }
    }

    // 并发处理多个需求
    suspend fun processMultipleRequirements(ids: List<RequirementId>) {
        ids.map { id ->
            async { processRequirement(id) }
        }.awaitAll()
    }
}
```

#### 2.4.2 密封类和数据类 - 更简洁的语法
```kotlin
// 密封类定义领域事件 - 比Java更简洁
sealed interface DomainEvent {
    val eventId: String
    val occurredAt: LocalDateTime

    // when表达式处理事件 - 比switch更强大
    val eventType: String
        get() = when (this) {
            is RequirementCreatedEvent -> "RequirementCreated"
            is BidSubmittedEvent -> "BidSubmitted"
            is OrderCompletedEvent -> "OrderCompleted"
        }
}

// Money值对象 - 充分利用Kotlin特性
@JvmInline
value class Money(private val value: Pair<BigDecimal, Currency>) {

    constructor(amount: BigDecimal, currency: Currency) : this(
        amount.also { require(it >= BigDecimal.ZERO) { "金额不能为负数" } } to currency
    )

    val amount: BigDecimal get() = value.first
    val currency: Currency get() = value.second

    // 使用内联函数优化性能
    private inline fun requireSameCurrency(other: Money) {
        require(currency == other.currency) { "货币类型不匹配: $currency vs ${other.currency}" }
    }

    // 运算符重载 - 更自然的语法
    operator fun plus(other: Money): Money {
        requireSameCurrency(other)
        return Money(amount + other.amount, currency)
    }

    operator fun minus(other: Money): Money {
        requireSameCurrency(other)
        return Money(amount - other.amount, currency)
    }

    operator fun times(multiplier: BigDecimal): Money =
        Money(amount * multiplier, currency)

    operator fun times(multiplier: Double): Money =
        Money(amount * multiplier.toBigDecimal(), currency)

    operator fun div(divisor: BigDecimal): Money =
        Money(amount / divisor, currency)

    // 比较运算符
    operator fun compareTo(other: Money): Int {
        requireSameCurrency(other)
        return amount.compareTo(other.amount)
    }

    // 使用扩展属性增强可读性
    val isZero: Boolean get() = amount == BigDecimal.ZERO
    val isPositive: Boolean get() = amount > BigDecimal.ZERO
    val isNegative: Boolean get() = amount < BigDecimal.ZERO

    // 使用作用域函数提供便利方法
    fun format(): String = NumberFormat.getCurrencyInstance().apply {
        this.currency = <EMAIL>
    }.format(amount)

    companion object {
        val ZERO = Money(BigDecimal.ZERO, Currency.getInstance("CNY"))

        // 使用扩展函数提供DSL风格的构造
        fun BigDecimal.cny() = Money(this, Currency.getInstance("CNY"))
        fun BigDecimal.usd() = Money(this, Currency.getInstance("USD"))
        fun Int.cny() = Money(this.toBigDecimal(), Currency.getInstance("CNY"))
        fun Double.cny() = Money(this.toBigDecimal(), Currency.getInstance("CNY"))
    }
}

// when表达式在业务逻辑中的应用 - 更强大的模式匹配
@Service
class RequirementStatusService {

    fun getStatusDescription(status: RequirementStatus): String = when (status) {
        RequirementStatus.DRAFT -> "草稿状态，可以编辑"
        RequirementStatus.SUBMITTED -> "已提交，等待审核"
        RequirementStatus.APPROVED -> "已审核通过，可以竞价"
        RequirementStatus.REJECTED -> "审核未通过，需要修改"
        RequirementStatus.COMPLETED -> "需求已完成"
    }

    // 智能类型转换
    fun processEvent(event: DomainEvent) = when (event) {
        is RequirementCreatedEvent -> {
            // 自动类型转换为RequirementCreatedEvent
            println("需求创建: ${event.requirementId}")
        }
        is BidSubmittedEvent -> {
            // 自动类型转换为BidSubmittedEvent
            println("竞价提交: ${event.bidAmount}")
        }
        is OrderCompletedEvent -> {
            // 自动类型转换为OrderCompletedEvent
            println("订单完成: ${event.orderId}")
        }
    }
}
```

#### 2.4.3 配置属性绑定 - 数据类的天然优势
```kotlin
// 使用数据类简化配置 - 比Java记录类更简洁
@ConfigurationProperties("procurement")
data class ProcurementProperties(
    val timeout: Duration,
    val maxRetries: Int = 10, // 默认值更简洁
    val allowedCategories: List<String>,
    val database: DatabaseConfig,
    val kafka: KafkaConfig
) {
    data class DatabaseConfig(
        val url: String,
        val maxConnections: Int,
        val connectionTimeout: Duration
    )

    data class KafkaConfig(
        val bootstrapServers: String,
        val groupId: String,
        val producerProps: Map<String, String>
    )
}

// 在服务中使用配置 - 构造函数注入更简洁
@Service
class RequirementService(
    private val properties: ProcurementProperties
) {

    suspend fun processWithTimeout(id: RequirementId) {
        // 使用协程的超时处理 - 比CompletableFuture更优雅
        withTimeout(properties.timeout.toMillis()) {
            processRequirement(id)
        }
    }

    // 扩展函数让配置使用更自然
    private fun ProcurementProperties.isValidCategory(category: String): Boolean {
        return allowedCategories.contains(category)
    }
}
```

## 3. 数据架构

### 3.1 数据分层策略

#### 3.1.1 命令端数据模型 - Kotlin + PostgreSQL + Hibernate 6.6
```kotlin
// 聚合根实体 - 强一致性，利用PostgreSQL JSON支持
@Entity
@Table(name = "procurement_requirements")
class ProcurementRequirement(
    @EmbeddedId
    val id: RequirementId,

    @Embedded
    var specification: RequirementSpec,

    // 利用PostgreSQL的JSON类型存储复杂对象
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "requirement_details", columnDefinition = "jsonb")
    var details: RequirementDetails,

    // 利用PostgreSQL数组类型
    @JdbcTypeCode(SqlTypes.ARRAY)
    @Column(name = "tags", columnDefinition = "text[]")
    var tags: List<String>,

    @OneToMany(cascade = [CascadeType.ALL], fetch = FetchType.LAZY, mappedBy = "requirement")
    val items: MutableList<RequirementItem> = mutableListOf(),

    @Enumerated(EnumType.STRING)
    var status: RequirementStatus,

    @Version
    var version: Long? = null,

    // 审计字段
    @CreationTimestamp
    val createdAt: LocalDateTime? = null,

    @UpdateTimestamp
    var updatedAt: LocalDateTime? = null

) : AggregateRoot<RequirementId>() {

    // 使用委托属性实现计算属性
    val totalBudget: Money by lazy {
        items.map { it.estimatedPrice }
            .takeIf { it.isNotEmpty() }
            ?.reduce(Money::plus)
            ?: Money.ZERO
    }

    // 使用扩展属性增强可读性
    val isSubmittable: Boolean get() = status == RequirementStatus.DRAFT
    val canBid: Boolean get() = status == RequirementStatus.APPROVED
    val isCompleted: Boolean get() = status == RequirementStatus.COMPLETED

    // 业务方法 - 使用作用域函数和契约
    fun submit(): Result<Unit> = runCatching {
        require(isSubmittable) { "只有草稿状态的需求才能提交" }

        status = RequirementStatus.SUBMITTED
        addDomainEvent(RequirementSubmittedEvent(id))
    }

    // 使用作用域函数让代码更流畅
    fun addItem(item: RequirementItem): ProcurementRequirement = apply {
        items += item.also { it.requirement = this }
        addDomainEvent(RequirementItemAddedEvent(id, item.id))
    }

    // 使用高阶函数处理业务逻辑
    inline fun <T> withValidStatus(
        vararg validStatuses: RequirementStatus,
        action: () -> T
    ): T {
        require(status in validStatuses) {
            "当前状态 $status 不允许此操作，有效状态: ${validStatuses.joinToString()}"
        }
        return action()
    }

    // 使用扩展函数增强业务能力
    fun approve(approver: UserId): Result<Unit> =
        withValidStatus(RequirementStatus.SUBMITTED) {
            runCatching {
                status = RequirementStatus.APPROVED
                addDomainEvent(RequirementApprovedEvent(id, approver))
            }
        }
}

// 值对象 - 充分利用Kotlin特性
data class RequirementDetails(
    val description: String,
    val specifications: Map<String, Any>,
    val attachments: List<String>,
    val delivery: DeliveryRequirement
) {
    // 使用委托属性实现计算属性
    val hasAttachments: Boolean by lazy { attachments.isNotEmpty() }
    val specificationCount: Int by lazy { specifications.size }

    // 使用扩展函数增强功能
    fun withAdditionalSpec(key: String, value: Any): RequirementDetails =
        copy(specifications = specifications + (key to value))

    // 使用操作符重载
    operator fun plus(other: RequirementDetails): RequirementDetails =
        copy(
            description = "$description\n${other.description}",
            specifications = specifications + other.specifications,
            attachments = attachments + other.attachments
        )
}

data class DeliveryRequirement(
    val address: String,
    val expectedDate: LocalDate,
    val specialInstructions: String? = null
) {
    // 使用扩展属性
    val isUrgent: Boolean get() = expectedDate.isBefore(LocalDate.now().plusDays(7))
    val daysUntilDelivery: Long get() = ChronoUnit.DAYS.between(LocalDate.now(), expectedDate)

    // 使用作用域函数
    fun withInstructions(instructions: String): DeliveryRequirement =
        copy(specialInstructions = specialInstructions?.let { "$it\n$instructions" } ?: instructions)

    companion object {
        // 使用伴生对象提供工厂方法
        fun urgent(address: String): DeliveryRequirement =
            DeliveryRequirement(address, LocalDate.now().plusDays(3))

        fun standard(address: String): DeliveryRequirement =
            DeliveryRequirement(address, LocalDate.now().plusWeeks(2))
    }
}
```

#### 3.1.2 查询端数据模型 - 读优化
```kotlin
// 查询视图 - 读优化，使用物化视图
@Entity
@Table(name = "requirement_list_view")
@Immutable // Hibernate优化，标记为不可变
data class RequirementListView(
    @Id
    val id: Long,

    val title: String,
    val categoryName: String,
    val buyerName: String,
    val estimatedValue: BigDecimal,
    val publishedAt: LocalDateTime,
    val status: String,
    val bidCount: Int,

    // PostgreSQL全文搜索支持
    @Column(name = "search_vector", columnDefinition = "tsvector")
    val searchVector: String,

    // JSON聚合字段，包含更多详细信息
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "summary_data", columnDefinition = "jsonb")
    val summaryData: RequirementSummary
) {
    // 扩展函数提供便利方法
    fun isActive(): Boolean = status in listOf("SUBMITTED", "APPROVED")

    fun hasActiveBids(): Boolean = bidCount > 0
}

// Elasticsearch文档模型
@Document(indexName = "requirements")
data class RequirementDocument(
    @Id
    val id: String,

    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    val title: String,

    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    val description: String,

    @Field(type = FieldType.Keyword)
    val category: String,

    @Field(type = FieldType.Keyword)
    val status: String,

    @Field(type = FieldType.Double)
    val estimatedValue: BigDecimal,

    @Field(type = FieldType.Date)
    val publishedAt: LocalDateTime,

    @Field(type = FieldType.Nested)
    val items: List<RequirementItemDocument>,

    // 地理位置搜索支持
    @GeoPointField
    val deliveryLocation: GeoPoint
) {
    companion object {
        fun from(event: RequirementCreatedEvent): RequirementDocument {
            return RequirementDocument(
                id = event.requirementId.value,
                title = event.title,
                description = event.description,
                category = event.category,
                status = event.status.name,
                estimatedValue = event.estimatedValue.amount,
                publishedAt = event.occurredAt,
                items = event.items.map { RequirementItemDocument.from(it) },
                deliveryLocation = GeoPoint(event.deliveryLat, event.deliveryLon)
            )
        }
    }
}
```

### 3.2 基于Kafka的数据同步策略
```kotlin
// Kafka事件监听器 - 更新读模型
@Component
class RequirementProjectionHandler(
    private val readModelRepository: RequirementReadModelRepository,
    private val elasticsearchTemplate: ElasticsearchTemplate,
    private val redisTemplate: RedisTemplate<String, Any>
) {

    @KafkaListener(topics = ["requirement-events"], groupId = "requirement-projection")
    @Transactional
    suspend fun handle(event: RequirementCreatedEvent) {
        // 使用协程并发处理多个更新操作
        coroutineScope {
            // 1. 更新PostgreSQL读模型
            async {
                val readModel = RequirementListView.from(event)
                readModelRepository.save(readModel)
            }

            // 2. 更新Elasticsearch索引
            async {
                val document = RequirementDocument.from(event)
                elasticsearchTemplate.save(document)
            }

            // 3. 清除相关缓存
            async {
                evictRelatedCaches(event.requirementId)
            }

            // 4. 更新实时统计
            async {
                updateRealTimeStats(event)
            }
        }
    }

    @KafkaListener(topics = ["requirement-events"], groupId = "requirement-projection")
    @Transactional
    suspend fun handle(event: RequirementStatusChangedEvent) {
        coroutineScope {
            // 批量更新，提高性能
            async {
                readModelRepository.updateStatus(event.requirementId, event.newStatus)
            }

            // 更新Elasticsearch
            async {
                val updateQuery = UpdateQuery.builder(event.requirementId.value)
                    .withDocument(Document.create().append("status", event.newStatus.name))
                    .build()
                elasticsearchTemplate.update(updateQuery, RequirementDocument::class.java)
            }
        }
    }

    // 错误处理和重试机制
    @KafkaListener(topics = ["requirement-events.DLT"], groupId = "requirement-projection-dlq")
    fun handleFailedEvents(record: ConsumerRecord<String, Any>) {
        logger.error("Failed to process event: ${record.value()}")
        // 发送到监控系统或人工处理队列
    }
}

// 实时统计更新
@Component
class RealTimeStatsUpdater(
    private val redisTemplate: RedisTemplate<String, Any>
) {

    @KafkaListener(topics = ["requirement-events"], groupId = "stats-updater")
    suspend fun updateStats(event: DomainEvent) {
        when (event) {
            is RequirementCreatedEvent -> incrementRequirementCount(event.buyerId)
            is BidSubmittedEvent -> incrementBidCount(event.requirementId)
            is OrderCompletedEvent -> updateCompletionStats(event)
        }
    }

    private suspend fun incrementRequirementCount(buyerId: String) {
        withContext(Dispatchers.IO) {
            redisTemplate.opsForValue().increment("stats:buyer:$buyerId:requirements")
        }
    }

    private suspend fun incrementBidCount(requirementId: RequirementId) {
        withContext(Dispatchers.IO) {
            redisTemplate.opsForValue().increment("stats:requirement:${requirementId.value}:bids")
        }
    }

    private suspend fun updateCompletionStats(event: OrderCompletedEvent) {
        withContext(Dispatchers.IO) {
            redisTemplate.opsForValue().increment("stats:global:completed_orders")
            redisTemplate.opsForValue().increment("stats:buyer:${event.buyerId}:completed_orders")
        }
    }
}
```

### 3.3 多层缓存策略
```kotlin
// 分层缓存服务 - 充分利用Kotlin特性
@Service
class RequirementCacheService(
    private val redisTemplate: RedisTemplate<String, Any>,
    private val cacheManager: CacheManager,
    private val requirementViewRepository: RequirementViewRepository
) {

    // 使用委托属性实现缓存键生成
    private val cacheKeyGenerator by lazy { CacheKeyGenerator("requirement") }

    // L1缓存：本地缓存（Caffeine）
    @Cacheable(value = ["requirements-local"], key = "#id")
    suspend fun getRequirementDetail(id: Long): RequirementDetailView? =
        getFromRedisOrDatabase(id)

    // 使用扩展函数和作用域函数优化缓存逻辑
    private suspend fun getFromRedisOrDatabase(id: Long): RequirementDetailView? {
        val cacheKey = cacheKeyGenerator.detailKey(id)

        return redisTemplate.getFromCache<RequirementDetailView>(cacheKey)
            ?: requirementViewRepository.findDetailById(id)
                ?.also { view -> redisTemplate.cacheWithExpiry(cacheKey, view, 1.hours) }
    }

    // 使用扩展函数让Redis操作更自然
    private suspend inline fun <reified T> RedisTemplate<String, Any>.getFromCache(key: String): T? =
        withContext(Dispatchers.IO) {
            opsForValue().get(key) as? T
        }

    private suspend fun RedisTemplate<String, Any>.cacheWithExpiry(
        key: String,
        value: Any,
        duration: Duration
    ) = withContext(Dispatchers.IO) {
        opsForValue().set(key, value, duration)
    }

    // 使用扩展属性提供便利的时间单位
    private val Int.hours: Duration get() = Duration.ofHours(this.toLong())
    private val Int.minutes: Duration get() = Duration.ofMinutes(this.toLong())
}

    // 缓存预热
    @EventListener
    fun preloadCache(event: RequirementCreatedEvent) {
        // 使用协程异步预热缓存
        GlobalScope.launch {
            getRequirementDetail(event.requirementId.value.toLong())
        }
    }

    // 智能缓存失效
    @CacheEvict(value = ["requirements-local", "requirement-stats"], key = "#id")
    suspend fun evictRequirementCache(id: Long) {
        // 同时清除Redis缓存
        withContext(Dispatchers.IO) {
            redisTemplate.delete("requirement:detail:$id")
            redisTemplate.delete("requirement:summary:$id")
        }
    }

    // 缓存穿透保护
    @Cacheable(value = ["requirement-exists"], key = "#id", unless = "#result == false")
    suspend fun requirementExists(id: Long): Boolean {
        return withContext(Dispatchers.IO) {
            requirementRepository.existsById(id)
        }
    }
}

// 缓存配置
@Configuration
@EnableCaching
class CacheConfig {

    @Bean
    fun cacheManager(): CacheManager {
        val cacheManager = CaffeineCacheManager()
        cacheManager.setCaffeine(
            Caffeine.newBuilder()
                .maximumSize(10000)
                .expireAfterWrite(Duration.ofMinutes(30))
                .recordStats()
        )
        return cacheManager
    }

    @Bean
    fun redisTemplate(factory: RedisConnectionFactory): RedisTemplate<String, Any> {
        return RedisTemplate<String, Any>().apply {
            connectionFactory = factory
            defaultSerializer = GenericJackson2JsonRedisSerializer()
        }
    }
}
```

## 4. 基于Kafka的消息架构

### 4.1 事件发布订阅模式
```kotlin
// Kafka事件发布器 - 充分利用Kotlin特性
@Component
class KafkaDomainEventPublisher(
    private val kafkaTemplate: KafkaTemplate<String, Any>,
    private val objectMapper: ObjectMapper
) {

    suspend fun publish(event: DomainEvent): Result<Unit> = runCatching {
        val topic = event.getTopicName()
        val key = event.aggregateId

        // 使用数据类构建事件包装
        val eventWithMetadata = EventEnvelope(
            eventId = event.eventId,
            eventType = event::class.simpleName!!,
            aggregateId = event.aggregateId,
            aggregateType = event.aggregateType,
            version = event.version,
            occurredAt = event.occurredAt,
            payload = event,
            metadata = createMetadata()
        )

        // 使用协程发布事件
        withContext(Dispatchers.IO) {
            kafkaTemplate.send(topic, key, eventWithMetadata).get()
        }
    }.onFailure { throwable ->
        logger.error("Failed to publish event: ${event.eventType}", throwable)
    }

    // 使用扩展函数让主题名称获取更自然
    private fun DomainEvent.getTopicName(): String = when (aggregateType) {
        "ProcurementRequirement" -> "requirement-events"
        "Bid" -> "bid-events"
        "Order" -> "order-events"
        "Settlement" -> "settlement-events"
        else -> "domain-events"
    }

    // 使用数据类替代Builder模式
    data class EventEnvelope(
        val eventId: String,
        val eventType: String,
        val aggregateId: String,
        val aggregateType: String,
        val version: Long,
        val occurredAt: LocalDateTime,
        val payload: DomainEvent,
        val metadata: Map<String, Any>
    )
}

// Kafka事件监听器 - 充分利用Kotlin特性
@Component
class OrderCreationEventHandler(
    private val orderApplicationService: OrderApplicationService,
    private val processedEventRepository: ProcessedEventRepository
) {

    @KafkaListener(topics = ["bid-events"], groupId = "order-creation")
    @Transactional
    suspend fun handle(event: BidAcceptedEvent) {
        runCatching {
            // 使用数据类构建命令，更简洁
            val command = CreateOrderCommand(
                requirementId = event.requirementId,
                supplierId = event.supplierId,
                bidAmount = event.bidAmount,
                deliveryDate = event.deliveryDate
            )

            orderApplicationService.createOrder(command)
        }.onFailure { exception ->
            logger.error("Failed to handle BidAcceptedEvent: ${event.eventId}", exception)
            // 重新抛出异常，让Kafka重试机制处理
            throw exception
        }
    }

    // 幂等性处理 - 使用扩展函数
    @KafkaListener(topics = ["requirement-events"], groupId = "order-creation")
    suspend fun handle(event: RequirementCompletedEvent) {
        // 使用扩展函数检查幂等性
        if (event.isAlreadyProcessed()) {
            logger.info("Event already processed: ${event.eventId}")
            return
        }

        // 处理业务逻辑
        handleRequirementCompletion(event)

        // 记录已处理的事件
        event.markAsProcessed()
    }

    // 扩展函数让幂等性检查更自然
    private suspend fun RequirementCompletedEvent.isAlreadyProcessed(): Boolean =
        withContext(Dispatchers.IO) {
            processedEventRepository.existsByEventId(eventId)
        }

    private suspend fun RequirementCompletedEvent.markAsProcessed() {
        withContext(Dispatchers.IO) {
            processedEventRepository.save(ProcessedEvent(eventId))
        }
    }
}
```

### 4.2 Kafka配置和主题管理
```kotlin
@Configuration
@EnableKafka
class KafkaConfig(
    @Value("\${spring.kafka.bootstrap-servers}")
    private val bootstrapServers: String
) {

    // 生产者配置 - 使用mapOf更简洁
    @Bean
    fun producerFactory(): ProducerFactory<String, Any> {
        val props = mapOf(
            ProducerConfig.BOOTSTRAP_SERVERS_CONFIG to bootstrapServers,
            ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG to StringSerializer::class.java,
            ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG to JsonSerializer::class.java,

            // 性能和可靠性配置
            ProducerConfig.ACKS_CONFIG to "all", // 等待所有副本确认
            ProducerConfig.RETRIES_CONFIG to 3,
            ProducerConfig.BATCH_SIZE_CONFIG to 16384,
            ProducerConfig.LINGER_MS_CONFIG to 5,
            ProducerConfig.BUFFER_MEMORY_CONFIG to 33554432,

            // 幂等性保证
            ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG to true
        )

        return DefaultKafkaProducerFactory(props)
    }

    @Bean
    fun kafkaTemplate(): KafkaTemplate<String, Any> {
        return KafkaTemplate(producerFactory()).apply {
            // 设置默认主题
            defaultTopic = "domain-events"

            // 设置事务支持
            transactionIdPrefix = "tx-"
        }
    }

    // 消费者配置 - 使用mapOf更简洁
    @Bean
    fun consumerFactory(): ConsumerFactory<String, Any> {
        val props = mapOf(
            ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to bootstrapServers,
            ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG to StringDeserializer::class.java,
            ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG to JsonDeserializer::class.java,

            // 消费者组配置
            ConsumerConfig.GROUP_ID_CONFIG to "purchase-system",
            ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
            ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to false,

            // JSON反序列化配置
            JsonDeserializer.TRUSTED_PACKAGES to "com.purchase.*",
            JsonDeserializer.VALUE_DEFAULT_TYPE to "com.purchase.shared.domain.event.DomainEvent"
        )

        return DefaultKafkaConsumerFactory(props)
    }

    @Bean
    fun kafkaListenerContainerFactory(): ConcurrentKafkaListenerContainerFactory<String, Any> {
        return ConcurrentKafkaListenerContainerFactory<String, Any>().apply {
            consumerFactory = consumerFactory()

            // 并发配置
            setConcurrency(3)

            // 手动提交配置
            containerProperties.ackMode = ContainerProperties.AckMode.MANUAL_IMMEDIATE

            // 错误处理 - 重试3次，间隔1秒
            setCommonErrorHandler(DefaultErrorHandler(FixedBackOff(1000L, 3L)))
        }
    }

    // 主题管理
    @Bean
    fun kafkaAdmin(): KafkaAdmin {
        val configs = mapOf(
            AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG to bootstrapServers
        )
        return KafkaAdmin(configs)
    }

    @Bean
    fun requirementEventsTopic(): NewTopic =
        TopicBuilder.name("requirement-events")
            .partitions(3)
            .replicas(2)
            .config(TopicConfig.RETENTION_MS_CONFIG, "604800000") // 7天保留
            .build()

    @Bean
    fun bidEventsTopic(): NewTopic =
        TopicBuilder.name("bid-events")
            .partitions(3)
            .replicas(2)
            .build()

    @Bean
    fun orderEventsTopic(): NewTopic =
        TopicBuilder.name("order-events")
            .partitions(3)
            .replicas(2)
            .build()
}

// 死信队列配置
@Configuration
class KafkaDeadLetterConfig {

    @Bean
    fun deadLetterTopic(): NewTopic =
        TopicBuilder.name("dead-letter-queue")
            .partitions(1)
            .replicas(2)
            .config(TopicConfig.RETENTION_MS_CONFIG, "**********") // 30天保留
            .build()

    @Bean
    fun deadLetterPublishingRecoverer(
        kafkaTemplate: KafkaTemplate<String, Any>
    ): DeadLetterPublishingRecoverer =
        DeadLetterPublishingRecoverer(kafkaTemplate)
}
```

## 5. 安全架构

### 5.1 JWT认证架构
```kotlin
@Component
class JwtAuthenticationProvider(
    private val secretKey: String
) {

    fun authenticate(token: String): Authentication = runCatching {
        val claims = Jwts.parser()
            .setSigningKey(secretKey)
            .parseClaimsJws(token)
            .body

        val userId = claims.subject
        val roles = claims["roles"] as? List<String> ?: emptyList()

        JwtAuthenticationToken(userId, roles)
    }.getOrElse { exception ->
        when (exception) {
            is JwtException -> throw AuthenticationException("Invalid JWT token")
            else -> throw exception
        }
    }
}

@Component
class SecurityContextService {

    fun getCurrentContext(): SecurityContext {
        val auth = SecurityContextHolder.getContext().authentication
        return SecurityContext(
            userId = auth.name.toLong(),
            roles = auth.authorities
                .map { it.authority }
                .toSet()
        )
    }
}
```

### 5.2 权限控制架构
```kotlin
@PreAuthorize("hasRole('BUYER') and @securityService.canAccessRequirement(#requirementId)")
@GetMapping("/requirements/{requirementId}")
suspend fun getRequirement(@PathVariable requirementId: Long): Result<RequirementDetailResponse> {
    // 方法实现 - 使用协程
    return runCatching {
        requirementService.getRequirementDetail(requirementId)
    }
}

@Component
class SecurityService(
    private val securityContextService: SecurityContextService,
    private val requirementRepository: RequirementRepository
) {

    suspend fun canAccessRequirement(requirementId: Long): Boolean {
        val context = securityContextService.getCurrentContext()
        val requirement = withContext(Dispatchers.IO) {
            requirementRepository.findById(requirementId)
        }

        // 使用扩展函数让权限检查更自然
        return requirement?.let { req ->
            req.buyerId == context.userId || context.hasRole("ADMIN")
        } ?: false
    }

    // 扩展函数让权限检查更自然
    private fun SecurityContext.hasRole(role: String): Boolean =
        roles.contains("ROLE_$role")
}
```

## 6. 性能优化

### 6.1 数据库优化
```sql
-- 索引优化
CREATE INDEX idx_requirements_buyer_status ON procurement_requirements(buyer_id, status);
CREATE INDEX idx_bids_requirement_status ON bidding_bids(requirement_id, status);
CREATE INDEX idx_orders_buyer_created ON orders(buyer_id, created_at);

-- 分区表（大数据量场景）
CREATE TABLE event_store (
    event_id VARCHAR(36) NOT NULL,
    aggregate_id VARCHAR(36) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSON NOT NULL,
    occurred_at TIMESTAMP NOT NULL,
    version BIGINT NOT NULL,
    PRIMARY KEY (event_id, occurred_at)
) PARTITION BY RANGE (YEAR(occurred_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 6.2 缓存优化
```kotlin
@Configuration
@EnableCaching
class CacheConfig(
    private val redisConnectionFactory: RedisConnectionFactory
) {

    @Bean
    fun cacheManager(): CacheManager =
        RedisCacheManager.RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory)
            .cacheDefaults(cacheConfiguration())
            .build()

    private fun cacheConfiguration(): RedisCacheConfiguration =
        RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(
                RedisSerializationContext.SerializationPair
                    .fromSerializer(StringRedisSerializer())
            )
            .serializeValuesWith(
                RedisSerializationContext.SerializationPair
                    .fromSerializer(GenericJackson2JsonRedisSerializer())
            )
}
```

### 6.3 异步处理 - 使用协程替代线程池
```kotlin
@Configuration
class CoroutineAsyncConfig {

    @Bean
    fun applicationCoroutineScope(): CoroutineScope =
        CoroutineScope(
            SupervisorJob() +
            Dispatchers.Default +
            CoroutineName("ApplicationScope")
        )

    @Bean
    fun notificationCoroutineScope(): CoroutineScope =
        CoroutineScope(
            SupervisorJob() +
            Dispatchers.IO +
            CoroutineName("NotificationScope")
        )
}

@Service
class NotificationService(
    private val notificationScope: CoroutineScope,
    private val emailService: EmailService,
    private val smsService: SmsService
) {

    // 使用协程替代@Async - 更轻量、更高效
    suspend fun sendNotification(event: NotificationEvent) {
        notificationScope.launch {
            // 并发发送邮件和短信
            coroutineScope {
                launch { emailService.sendEmail(event.recipient, event.content) }
                launch { smsService.sendSms(event.recipient, event.content) }
            }
        }
    }

    // 批量通知 - 展示协程的优势
    suspend fun sendBatchNotifications(events: List<NotificationEvent>) {
        events.map { event ->
            async { sendNotification(event) }
        }.awaitAll()
    }
}
```

## 7. 部署架构

### 7.1 容器化配置
```dockerfile
# Dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY target/purchase-system.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 7.2 Kubernetes部署
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: purchase-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: purchase-system
  template:
    metadata:
      labels:
        app: purchase-system
    spec:
      containers:
      - name: purchase-system
        image: purchase-system:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
```

### 7.3 监控配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'purchase-system'
    static_configs:
      - targets: ['purchase-system:8080']
    metrics_path: '/actuator/prometheus'
```

## 8. 开发工具链

### 8.1 代码质量
- **SonarQube**：代码质量检查
- **SpotBugs**：静态代码分析
- **Checkstyle**：代码风格检查

### 8.2 测试框架
- **JUnit 5**：单元测试
- **Testcontainers**：集成测试
- **WireMock**：外部服务模拟

### 8.3 构建部署
- **Maven**：项目构建管理
- **Jenkins/GitLab CI**：持续集成
- **Docker**：容器化
- **Helm**：Kubernetes应用管理

## 9. Kotlin高级特性在DDD中的应用

### 9.1 DSL构建器模式
```kotlin
// 使用DSL构建复杂的查询条件
class RequirementQueryBuilder {
    private val filters = mutableMapOf<String, Any>()
    private var sortBy: String? = null
    private var sortDirection: Sort.Direction = Sort.Direction.ASC

    fun category(category: String) = apply { filters["category"] = category }
    fun status(status: RequirementStatus) = apply { filters["status"] = status }
    fun priceRange(min: Money, max: Money) = apply {
        filters["minPrice"] = min
        filters["maxPrice"] = max
    }

    fun sortBy(field: String, direction: Sort.Direction = Sort.Direction.ASC) = apply {
        sortBy = field
        sortDirection = direction
    }

    fun build(): RequirementQuery = RequirementQuery(filters, sortBy, sortDirection)
}

// DSL函数
fun requirementQuery(init: RequirementQueryBuilder.() -> Unit): RequirementQuery =
    RequirementQueryBuilder().apply(init).build()

// 使用DSL - 更自然的语法
val query = requirementQuery {
    category("电子产品")
    status(RequirementStatus.APPROVED)
    priceRange(1000.cny(), 10000.cny())
    sortBy("createdAt", Sort.Direction.DESC)
}
```

### 9.2 类型安全的构建器
```kotlin
// 使用@DslMarker防止DSL嵌套错误
@DslMarker
annotation class RequirementDsl

@RequirementDsl
class RequirementBuilder {
    private var title: String = ""
    private var description: String = ""
    private val items = mutableListOf<RequirementItem>()

    fun title(title: String) { this.title = title }
    fun description(description: String) { this.description = description }

    @RequirementDsl
    fun item(init: RequirementItemBuilder.() -> Unit) {
        items.add(RequirementItemBuilder().apply(init).build())
    }

    fun build(): ProcurementRequirement = ProcurementRequirement.create(
        title = title,
        description = description,
        items = items
    )
}

// 使用类型安全的DSL
val requirement = requirement {
    title("采购笔记本电脑")
    description("为开发团队采购高性能笔记本电脑")

    item {
        name("MacBook Pro")
        quantity(10)
        specification("16GB RAM, 512GB SSD")
    }

    item {
        name("ThinkPad X1")
        quantity(5)
        specification("32GB RAM, 1TB SSD")
    }
}
```

### 9.3 契约式编程
```kotlin
// 使用契约增强类型安全
import kotlin.contracts.*

inline fun <T> T?.requireNotNull(message: () -> String): T {
    contract {
        returns() implies (this@requireNotNull != null)
    }
    return this ?: throw IllegalArgumentException(message())
}

// 在业务逻辑中使用契约
class RequirementService {
    fun processRequirement(requirement: ProcurementRequirement?) {
        val validRequirement = requirement.requireNotNull {
            "需求不能为空"
        }
        // 编译器知道validRequirement不为null
        validRequirement.submit()
    }
}
```

### 9.4 上下文接收者
```kotlin
// 使用上下文接收者简化API
context(CoroutineScope)
suspend fun ProcurementRequirement.publishAsync() {
    launch {
        eventPublisher.publish(RequirementCreatedEvent(this@publishAsync))
    }
}

context(TransactionTemplate)
fun ProcurementRequirement.saveWithTransaction(): ProcurementRequirement =
    execute { repository.save(this@saveWithTransaction) }!!

// 使用上下文接收者
class RequirementCommandHandler(
    private val coroutineScope: CoroutineScope,
    private val transactionTemplate: TransactionTemplate
) {
    suspend fun handle(command: CreateRequirementCommand) {
        val requirement = ProcurementRequirement.create(command)

        with(transactionTemplate) {
            requirement.saveWithTransaction()
        }

        with(coroutineScope) {
            requirement.publishAsync()
        }
    }
}
```

### 9.5 多平台代码共享
```kotlin
// 共享业务逻辑
expect class PlatformSpecificValidator {
    fun validateBusinessLicense(license: String): Boolean
}

// 通用业务逻辑
class RequirementValidator {
    private val platformValidator = PlatformSpecificValidator()

    fun validate(requirement: ProcurementRequirement): ValidationResult {
        val errors = mutableListOf<String>()

        // 通用验证逻辑
        if (requirement.title.isBlank()) {
            errors.add("标题不能为空")
        }

        // 平台特定验证
        if (!platformValidator.validateBusinessLicense(requirement.buyerLicense)) {
            errors.add("营业执照无效")
        }

        return if (errors.isEmpty()) {
            ValidationResult.Success
        } else {
            ValidationResult.Failure(errors)
        }
    }
}
```

这套基于Kotlin的技术架构充分利用了现代语言特性和技术栈的优势，为采购生态平台提供了更加优雅、高效、类型安全的技术基础。通过CQRS + 事件驱动架构结合Kotlin协程，实现了高性能、高可用、可扩展的企业级系统。
