-- MySQL dump 10.13  Distrib 8.0.39, for Win64 (x86_64)
--
-- Host: localhost    Database: purchase_system
-- ------------------------------------------------------
-- Server version	8.0.39

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `bidding_record`
--

DROP TABLE IF EXISTS `bidding_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bidding_record` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '竞价ID',
  `requirement_id` bigint unsigned NOT NULL COMMENT '需求ID',
  `seller_id` bigint unsigned NOT NULL COMMENT '卖家ID',
  `bidding_price` decimal(10,2) NOT NULL COMMENT '竞价金额',
  `images` varchar(2048) DEFAULT NULL COMMENT '产品图片URL列表，多个URL用分隔符分隔',
  `videos` varchar(2048) DEFAULT NULL COMMENT '产品视频URL列表，多个URL用分隔符分隔',
  `description` text NOT NULL COMMENT '竞价描述',
  `delivery_time` date NOT NULL COMMENT '预计交付时间',
  `status` enum('pending','accepted','rejected','cancelled','sample_accepted') NOT NULL DEFAULT 'pending' COMMENT '状态：pending-待处理，accepted-已中标，rejected-已拒绝，cancelled-已取消，sample_accepted-样品已接受',
  `is_winner` tinyint NOT NULL DEFAULT '0' COMMENT '是否为中标者：0-否，1-是',
  `deleted` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `attributes_json` json DEFAULT NULL COMMENT '需求属性值JSON，包含该需求的所有属性及其值',
  `unit` varchar(50) DEFAULT NULL COMMENT '销售单位，如个、件、套等',
  `product_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
  `company` varchar(100) DEFAULT NULL COMMENT '公司名称',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮箱',
  `company_environment_images` varchar(1024) DEFAULT NULL COMMENT '公司/工厂环境照片，多张图片用逗号分隔',
  `company_address` varchar(600) DEFAULT NULL COMMENT '公司详细地址',
  `hs_code` varchar(20) DEFAULT NULL COMMENT '海关商品编码(HS Code)',
  `audit_status` varchar(20) DEFAULT 'pending_audit' COMMENT '审核状态：pending_audit-待审核，approved-审核通过，rejected-审核拒绝',
  `audit_remark` text COMMENT '审核备注',
  `bidding_type` enum('purchase','sample') NOT NULL DEFAULT 'purchase' COMMENT '竞价类型：purchase-普通竞价，sample-样品竞价',
  `sample_price` decimal(10,2) DEFAULT NULL COMMENT '样品价格（仅样品竞价时使用）',
  `sample_quantity` int DEFAULT '1' COMMENT '样品数量（仅样品竞价时使用）',
  `sample_specification` text COMMENT '样品规格说明（仅样品竞价时使用）',
  `winner` tinyint(1) DEFAULT '0' COMMENT '是否中标：0-未中标，1-中标',
  `data_type` varchar(20) DEFAULT 'real' COMMENT '数据类型：real-真实竞价，mock-虚拟测试竞价',
  PRIMARY KEY (`id`),
  KEY `idx_requirement` (`requirement_id`),
  KEY `idx_seller` (`seller_id`),
  KEY `idx_status` (`status`),
  KEY `idx_winner` (`is_winner`),
  KEY `idx_bidding_type` (`bidding_type`),
  KEY `idx_requirement_type_status` (`requirement_id`,`bidding_type`,`status`),
  CONSTRAINT `fk_bidding_requirement` FOREIGN KEY (`requirement_id`) REFERENCES `purchase_requirement` (`id`),
  CONSTRAINT `fk_bidding_seller` FOREIGN KEY (`seller_id`) REFERENCES `user` (`id`),
  CONSTRAINT `bidding_record_chk_1` CHECK ((`is_winner` in (0,1)))
) ENGINE=InnoDB AUTO_INCREMENT=16284 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='竞价记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `chat_message`
--

DROP TABLE IF EXISTS `chat_message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `chat_message` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `room_id` bigint unsigned NOT NULL COMMENT '聊天室ID',
  `sender_id` bigint unsigned NOT NULL COMMENT '发送者ID',
  `message_type` enum('text','image','file','system','contract','purchase-order') NOT NULL,
  `text_content` text COMMENT '文本消息内容',
  `image_url` varchar(255) DEFAULT NULL COMMENT '图片URL',
  `file_url` varchar(255) DEFAULT NULL COMMENT '文件URL',
  `system_content` text COMMENT '系统通知内容',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `sender_role` enum('buyer','seller','admin','forwarder') NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_room` (`room_id`),
  KEY `idx_sender` (`sender_id`),
  KEY `idx_created` (`created_at`),
  CONSTRAINT `fk_message_room` FOREIGN KEY (`room_id`) REFERENCES `chat_room` (`id`),
  CONSTRAINT `fk_message_sender` FOREIGN KEY (`sender_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='聊天消息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `chat_room`
--

DROP TABLE IF EXISTS `chat_room`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `chat_room` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '聊天室ID',
  `buyer_id` bigint unsigned DEFAULT NULL,
  `seller_id` bigint unsigned DEFAULT NULL,
  `forwarder_id` bigint unsigned DEFAULT NULL,
  `admin_id` bigint unsigned NOT NULL COMMENT '管理员ID',
  `last_message_time` timestamp NULL DEFAULT NULL COMMENT '最后消息时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-关闭，1-开启',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int DEFAULT '1' COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_buyer_seller_forwarder_admin` (`buyer_id`,`seller_id`,`forwarder_id`,`admin_id`),
  KEY `idx_buyer` (`buyer_id`),
  KEY `idx_seller` (`seller_id`),
  KEY `idx_admin` (`admin_id`),
  KEY `idx_last_message` (`last_message_time`),
  KEY `idx_forwarder` (`forwarder_id`),
  CONSTRAINT `fk_chat_admin` FOREIGN KEY (`admin_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_chat_buyer` FOREIGN KEY (`buyer_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_chat_seller` FOREIGN KEY (`seller_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='聊天室表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `chat_room_unread`
--

DROP TABLE IF EXISTS `chat_room_unread`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `chat_room_unread` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `room_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `unread_count` int NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_user` (`room_id`,`user_id`),
  KEY `fk_unread_user` (`user_id`),
  CONSTRAINT `fk_unread_room` FOREIGN KEY (`room_id`) REFERENCES `chat_room` (`id`),
  CONSTRAINT `fk_unread_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='??????δ????Ϣ???';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `commission_config`
--

DROP TABLE IF EXISTS `commission_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `commission_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_type` varchar(50) NOT NULL COMMENT '配置类型：commission_rate/monthly_bonus',
  `min_amount` decimal(19,2) NOT NULL COMMENT '最小金额',
  `max_amount` decimal(19,2) DEFAULT NULL COMMENT '最大金额（NULL表示无上限）',
  `rate_value` decimal(5,4) DEFAULT NULL COMMENT '比例值（用于commission_rate类型）',
  `bonus_amount` decimal(10,2) DEFAULT NULL COMMENT '奖金金额（用于monthly_bonus类型）',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_amount_range` (`min_amount`,`max_amount`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='佣金配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `commission_processing_config`
--

DROP TABLE IF EXISTS `commission_processing_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `commission_processing_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` varchar(500) NOT NULL COMMENT '配置值',
  `config_type` varchar(50) NOT NULL DEFAULT 'STRING' COMMENT '配置类型: STRING/INT/BOOLEAN/JSON',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='佣金处理配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `commission_processing_log`
--

DROP TABLE IF EXISTS `commission_processing_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `commission_processing_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '处理日志ID',
  `month_key` varchar(7) NOT NULL COMMENT '处理月份 (YYYY-MM)',
  `processing_date` date NOT NULL COMMENT '处理日期',
  `total_records` int NOT NULL DEFAULT '0' COMMENT '总记录数',
  `success_count` int NOT NULL DEFAULT '0' COMMENT '成功处理数',
  `failure_count` int NOT NULL DEFAULT '0' COMMENT '失败处理数',
  `status` varchar(20) NOT NULL DEFAULT 'PROCESSING' COMMENT '处理状态: PROCESSING/SUCCESS/FAILED',
  `error_message` text COMMENT '错误信息',
  `started_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_month_key` (`month_key`),
  KEY `idx_processing_date` (`processing_date`),
  KEY `idx_status` (`status`),
  KEY `idx_started_at` (`started_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='佣金处理日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `commission_record`
--

DROP TABLE IF EXISTS `commission_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `commission_record` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '佣金记录ID',
  `inviter_id` bigint unsigned NOT NULL COMMENT '邀请者ID',
  `invitee_id` bigint unsigned NOT NULL COMMENT '被邀请者ID（订单买家）',
  `order_id` bigint unsigned NOT NULL COMMENT '订单ID',
  `order_type` varchar(50) NOT NULL COMMENT '订单类型：purchase/logistics',
  `order_amount` decimal(19,2) NOT NULL COMMENT '订单金额',
  `commission_rate` decimal(5,4) NOT NULL COMMENT '佣金比例',
  `commission_amount` decimal(19,2) NOT NULL COMMENT '佣金金额',
  `status` varchar(50) DEFAULT 'PENDING' COMMENT '状态：PENDING/CONFIRMED/PAID/CANCELLED',
  `month_key` varchar(7) NOT NULL COMMENT '月份标识：YYYY-MM',
  `buyer_role_verified` tinyint(1) DEFAULT '1' COMMENT '被邀请者买家角色验证',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `confirmed_at` datetime DEFAULT NULL COMMENT '确认时间',
  `processed_at` datetime DEFAULT NULL COMMENT '处理时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：false-未删除，true-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_inviter_month` (`inviter_id`,`month_key`),
  KEY `idx_order` (`order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_invitee_buyer` (`invitee_id`,`buyer_role_verified`),
  KEY `idx_month_key` (`month_key`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_confirmed_at` (`confirmed_at`),
  KEY `idx_processed_at` (`processed_at`),
  KEY `idx_month_key_status` (`month_key`,`status`),
  CONSTRAINT `fk_commission_invitee` FOREIGN KEY (`invitee_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_commission_inviter` FOREIGN KEY (`inviter_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='佣金记录表V2';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `commission_settlement`
--

DROP TABLE IF EXISTS `commission_settlement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `commission_settlement` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '结算记录ID',
  `settlement_number` varchar(64) NOT NULL COMMENT '结算编号',
  `inviter_id` bigint unsigned NOT NULL COMMENT '邀请者ID',
  `month_key` varchar(7) NOT NULL COMMENT '结算月份：YYYY-MM',
  `total_commission` decimal(19,2) NOT NULL COMMENT '总佣金金额',
  `monthly_bonus` decimal(10,2) DEFAULT '0.00' COMMENT '月度奖金',
  `total_amount` decimal(19,2) NOT NULL COMMENT '结算总金额',
  `settlement_method` varchar(50) DEFAULT 'bank_transfer' COMMENT '结算方式：bank_transfer/alipay/wechat',
  `account_info` varchar(500) DEFAULT NULL COMMENT '收款账户信息',
  `settlement_status` varchar(50) DEFAULT 'COMPLETED' COMMENT '结算状态：COMPLETED-已完成',
  `settlement_time` datetime DEFAULT NULL COMMENT '结算时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：false-未删除，true-已删除',
  `receipt_files` json NOT NULL COMMENT '转账回执文件列表(JSON格式存储)',
  `payment_reference` varchar(100) NOT NULL COMMENT '支付参考号/流水号',
  `notes` text NOT NULL COMMENT '结算备注',
  `created_by` varchar(50) NOT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `completed_by` varchar(50) DEFAULT NULL COMMENT '完成确认人',
  `completed_at` datetime DEFAULT NULL COMMENT '完成确认时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_settlement_number` (`settlement_number`),
  KEY `idx_inviter_month` (`inviter_id`,`month_key`),
  KEY `idx_settlement_status` (`settlement_status`),
  KEY `idx_settlement_time` (`settlement_time`),
  KEY `idx_settlement_created_by` (`created_by`),
  KEY `idx_settlement_completed_by` (`completed_by`),
  KEY `idx_settlement_completed_at` (`completed_at`),
  CONSTRAINT `fk_settlement_inviter` FOREIGN KEY (`inviter_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='佣金结算表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `daily_visit_summary`
--

DROP TABLE IF EXISTS `daily_visit_summary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `daily_visit_summary` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '汇总记录ID',
  `summary_date` date NOT NULL COMMENT '汇总日期',
  `total_visits` int unsigned NOT NULL DEFAULT '0' COMMENT '总访问量',
  `unique_visitors` int unsigned NOT NULL DEFAULT '0' COMMENT '独立访客数',
  `registered_user_visits` int unsigned NOT NULL DEFAULT '0' COMMENT '注册用户访问量',
  `anonymous_visits` int unsigned NOT NULL DEFAULT '0' COMMENT '匿名用户访问量',
  `peak_hour` tinyint unsigned DEFAULT NULL COMMENT '访问高峰小时（0-23）',
  `peak_hour_visits` int unsigned DEFAULT NULL COMMENT '高峰小时访问量',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_summary_date` (`summary_date`),
  KEY `idx_summary_date` (`summary_date`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='日访问汇总表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `email_verification_code`
--

DROP TABLE IF EXISTS `email_verification_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_verification_code` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `email` varchar(100) NOT NULL COMMENT '邮箱地址',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `type` varchar(20) NOT NULL COMMENT '验证码类型：REGISTER-注册，LOGIN-登录，RESET_PASSWORD-重置密码',
  `used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已使用：0-未使用，1-已使用',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` varchar(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除字段：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_email_type` (`email`,`type`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='邮件验证码表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `forwarder_order`
--

DROP TABLE IF EXISTS `forwarder_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `forwarder_order` (
  `id` bigint NOT NULL COMMENT '主键ID，使用雪花算法生成',
  `order_number` varchar(64) NOT NULL COMMENT '订单编号，格式: FWD+日期+序列号',
  `requirement_id` bigint NOT NULL COMMENT '货代需求ID',
  `bidding_id` bigint NOT NULL COMMENT '竞价ID',
  `purchase_order_id` bigint unsigned DEFAULT NULL COMMENT '采购订单ID - 关联的采购订单',
  `forwarder_id` bigint NOT NULL COMMENT '货代ID',
  `forwarder_name` varchar(100) NOT NULL COMMENT '货代用户名',
  `forwarder_company_name` varchar(200) NOT NULL COMMENT '货代公司名称',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `creator_name` varchar(100) NOT NULL COMMENT '创建者名称',
  `creator_role` varchar(20) NOT NULL COMMENT '创建者角色',
  `title` varchar(200) NOT NULL COMMENT '需求标题',
  `product_name` varchar(200) DEFAULT NULL COMMENT '产品名称',
  `hs_code` varchar(20) DEFAULT NULL COMMENT 'HS编码',
  `destination_country` varchar(100) DEFAULT NULL COMMENT '目的国家',
  `destination_address` varchar(255) DEFAULT NULL COMMENT '目的地址',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `expected_delivery_date` date DEFAULT NULL COMMENT '预期交付日期',
  `description` text COMMENT '需求描述',
  `cargo_type` varchar(100) DEFAULT NULL COMMENT '货物类型',
  `cargo_weight` decimal(10,2) DEFAULT NULL COMMENT '货物重量(kg)',
  `cargo_volume` decimal(10,3) DEFAULT NULL COMMENT '货物体积(m³)',
  `package_count` int DEFAULT '1' COMMENT '包装数量',
  `special_requirements` text COMMENT '特殊要求',
  `documents` text COMMENT '文档(JSON)',
  `product_images` text COMMENT '产品图片(JSON)',
  `product_videos` text COMMENT '产品视频(JSON)',
  `port_of_loading_name` varchar(100) DEFAULT NULL COMMENT '起运港名称',
  `port_of_loading_code` varchar(10) DEFAULT NULL COMMENT '起运港代码',
  `port_of_destination_name` varchar(100) DEFAULT NULL COMMENT '目的港名称',
  `port_of_destination_code` varchar(10) DEFAULT NULL COMMENT '目的港代码',
  `need_certification` char(1) DEFAULT '0' COMMENT '需要认证(1是0否)',
  `need_fumigation` char(1) DEFAULT '0' COMMENT '需要熏蒸(1是0否)',
  `shipment_type` varchar(10) DEFAULT NULL COMMENT '装运类型(FCL整柜/LCL拼箱)',
  `container_size` varchar(20) DEFAULT NULL COMMENT '集装箱尺寸',
  `container_qty` int DEFAULT '1' COMMENT '集装箱数量',
  `delivery_terms` varchar(20) DEFAULT NULL COMMENT '交付条款(FOB/CIF/EXW等)',
  `vessel_name` varchar(100) DEFAULT NULL COMMENT '船名/航班号 - 运输工具识别关键',
  `voyage_number` varchar(50) DEFAULT NULL COMMENT '航次号 - 追踪具体航次必备',
  `etd` datetime DEFAULT NULL COMMENT '预计离港时间(ETD) - 客户最关心的时效信息',
  `eta` datetime DEFAULT NULL COMMENT '预计到港时间(ETA) - 供应链计划关键数据',
  `bill_of_lading_number` varchar(50) DEFAULT NULL COMMENT '提单号 - 单证追踪核心标识',
  `bill_of_lading_type` varchar(20) DEFAULT NULL COMMENT '提单类型(电放/正本) - 影响放货流程',
  `bidding_price` decimal(10,2) NOT NULL COMMENT '报价金额',
  `currency` varchar(3) DEFAULT 'USD' COMMENT '货币单位',
  `shipping_method` varchar(20) DEFAULT NULL COMMENT '运输方式(SEA/AIR/RAIL/ROAD)',
  `shipping_route` varchar(255) DEFAULT NULL COMMENT '运输路线',
  `estimated_days` int DEFAULT NULL COMMENT '预计运输天数',
  `bidding_description` text COMMENT '竞价描述',
  `insurance_included` tinyint(1) DEFAULT '0' COMMENT '是否包含保险',
  `insurance_amount` decimal(10,2) DEFAULT NULL COMMENT '保险金额',
  `customs_service` tinyint(1) DEFAULT '0' COMMENT '是否提供清关服务',
  `tracking_number` varchar(100) DEFAULT NULL COMMENT '物流跟踪号',
  `tracking_url` varchar(255) DEFAULT NULL COMMENT '物流跟踪URL',
  `estimated_pickup_date` datetime DEFAULT NULL COMMENT '预计提货日期',
  `estimated_delivery_date` datetime DEFAULT NULL COMMENT '预计送达日期',
  `actual_pickup_date` datetime DEFAULT NULL COMMENT '实际提货日期',
  `actual_delivery_date` datetime DEFAULT NULL COMMENT '实际送达日期',
  `order_status` varchar(20) DEFAULT 'pending' COMMENT '订单状态(pending/processing/completed/cancelled)',
  `payment_status` varchar(20) DEFAULT 'unpaid' COMMENT '支付状态',
  `total_price` decimal(10,2) DEFAULT NULL COMMENT '订单总金额',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `completion_time` datetime DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remarks` text COMMENT '备注',
  `cost_export_packing` decimal(12,2) DEFAULT NULL COMMENT '出口包装费 (卖方承担)',
  `cost_origin_inland_haulage` decimal(12,2) DEFAULT NULL COMMENT '起运地内陆运输费 (卖方承担)',
  `cost_export_customs_docs` decimal(12,2) DEFAULT NULL COMMENT '出口报关及单证费 (卖方承担)',
  `cost_origin_handling` decimal(12,2) DEFAULT NULL COMMENT '起运地操作费 (卖方承担)',
  `cost_main_freight` decimal(12,2) DEFAULT NULL COMMENT '主运费 (卖方承担部分)',
  `cost_freight_surcharges` decimal(12,2) DEFAULT NULL COMMENT '运费附加费 (卖方承担部分)',
  `cost_cargo_insurance` decimal(12,2) DEFAULT NULL COMMENT '货物运输保险费 (卖方支付的保费)',
  `cost_destination_handling_by_seller` decimal(12,2) DEFAULT NULL COMMENT '目的地操作费 (卖方承担)',
  `cost_destination_unloading_by_seller` decimal(12,2) DEFAULT NULL COMMENT '目的地卸货费 (卖方承担)',
  `cost_destination_inland_haulage_by_seller` decimal(12,2) DEFAULT NULL COMMENT '目的地内陆运输费 (卖方承担)',
  `cost_import_customs_docs_by_seller` decimal(12,2) DEFAULT NULL COMMENT '进口报关及单证费 (卖方承担-DDP)',
  `cost_import_duties_taxes_by_seller` decimal(12,2) DEFAULT NULL COMMENT '进口关税及税费 (卖方承担-DDP)',
  `cost_forwarder_service_fee` decimal(12,2) DEFAULT NULL COMMENT '货代服务费/操作费 (卖方承担)',
  `customed_costs` json DEFAULT NULL COMMENT '自定义费用项目 JSON数组，格式: [{"name":"费用名称","amount":金额,"description":"费用说明","currency":"货币"}]',
  `deleted` enum('0','1') DEFAULT '0' COMMENT '逻辑删除标志 0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_order_number` (`order_number`),
  KEY `idx_requirement_id` (`requirement_id`),
  KEY `idx_bidding_id` (`bidding_id`),
  KEY `idx_forwarder_id` (`forwarder_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_bl_number` (`bill_of_lading_number`),
  KEY `idx_forwarder_order_bidding_price` (`bidding_price`),
  KEY `idx_forwarder_order_total_price` (`total_price`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='货代订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `forwarding_bidding`
--

DROP TABLE IF EXISTS `forwarding_bidding`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `forwarding_bidding` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '货代竞价ID',
  `forwarding_requirement_id` bigint unsigned NOT NULL COMMENT '货代需求ID',
  `purchase_order_id` bigint unsigned DEFAULT NULL,
  `forwarder_id` bigint unsigned NOT NULL COMMENT '货代用户ID',
  `forwarder_name` varchar(100) DEFAULT NULL,
  `forwarder_company_name` varchar(100) DEFAULT NULL,
  `bidding_price` decimal(10,2) NOT NULL COMMENT '竞价金额',
  `currency` varchar(10) NOT NULL DEFAULT 'USD',
  `shipping_method` varchar(100) NOT NULL COMMENT '运输方式',
  `shipping_route` varchar(255) DEFAULT NULL COMMENT '运输路线',
  `estimated_days` int NOT NULL COMMENT '预计运输天数',
  `description` text COMMENT '服务描述',
  `documents` varchar(2048) DEFAULT NULL COMMENT '相关文件URL',
  `insurance_included` tinyint NOT NULL DEFAULT '0' COMMENT '是否包含保险：0-否，1-是',
  `insurance_amount` decimal(10,2) DEFAULT NULL COMMENT '保险金额',
  `customs_service` tinyint NOT NULL DEFAULT '0' COMMENT '是否提供清关服务：0-否，1-是',
  `need_certification` varchar(1) DEFAULT '0' COMMENT '是否需要产品认证：0-不需要，1-需要',
  `need_fumigation` varchar(1) DEFAULT '0' COMMENT '是否需要货物熏蒸：0-不需要，1-需要',
  `cost_export_packing` decimal(12,2) DEFAULT NULL COMMENT '出口包装费 (卖方承担)',
  `cost_origin_inland_haulage` decimal(12,2) DEFAULT NULL COMMENT '起运地内陆运输费 (卖方承担)',
  `cost_export_customs_docs` decimal(12,2) DEFAULT NULL COMMENT '出口报关及单证费 (卖方承担)',
  `cost_origin_handling` decimal(12,2) DEFAULT NULL COMMENT '起运地操作费 (卖方承担)',
  `cost_main_freight` decimal(12,2) DEFAULT NULL COMMENT '主运费 (卖方承担部分)',
  `cost_freight_surcharges` decimal(12,2) DEFAULT NULL COMMENT '运费附加费 (卖方承担部分)',
  `cost_cargo_insurance` decimal(12,2) DEFAULT NULL COMMENT '货物运输保险费 (卖方支付的保费)',
  `cost_destination_handling_by_seller` decimal(12,2) DEFAULT NULL COMMENT '目的地操作费 (卖方承担)',
  `cost_destination_unloading_by_seller` decimal(12,2) DEFAULT NULL COMMENT '目的地卸货费 (卖方承担)',
  `cost_destination_inland_haulage_by_seller` decimal(12,2) DEFAULT NULL COMMENT '目的地内陆运输费 (卖方承担)',
  `cost_import_customs_docs_by_seller` decimal(12,2) DEFAULT NULL COMMENT '进口报关及单证费 (卖方承担-DDP)',
  `cost_import_duties_taxes_by_seller` decimal(12,2) DEFAULT NULL COMMENT '进口关税及税费 (卖方承担-DDP)',
  `cost_forwarder_service_fee` decimal(12,2) DEFAULT NULL COMMENT '货代服务费/操作费 (卖方承担)',
  `status` enum('pending','accepted','rejected','cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态：待处理、已中标、已拒绝、已取消',
  `is_winner` tinyint NOT NULL DEFAULT '0' COMMENT '是否为中标者：0-否，1-是',
  `tracking_number` varchar(100) DEFAULT NULL COMMENT '物流跟踪号',
  `tracking_url` varchar(255) DEFAULT NULL COMMENT '物流跟踪URL',
  `deleted` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `estimated_pickup_date` datetime DEFAULT NULL COMMENT '预计提货日期',
  `estimated_delivery_date` datetime DEFAULT NULL COMMENT '预计送达日期',
  `audit_status` varchar(20) DEFAULT 'pending_audit' COMMENT '审核状态：pending_audit-待审核, approved-审核通过, rejected-审核拒绝',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `delivery_terms` varchar(50) DEFAULT NULL COMMENT '交货条款/国际贸易术语 (例如: FOB, CIF)',
  `customed_costs` json DEFAULT NULL COMMENT '自定义费用项目 JSON数组，格式: [{"name":"费用名称","amount":金额,"description":"费用说明"}]',
  `data_type` varchar(20) DEFAULT 'real' COMMENT '数据类型：real-真实货代竞价，mock-虚拟测试竞价',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_forwarding_requirement_forwarder` (`forwarding_requirement_id`,`forwarder_id`,`deleted`),
  KEY `idx_forwarding_requirement` (`forwarding_requirement_id`),
  KEY `idx_forwarder` (`forwarder_id`),
  KEY `idx_status` (`status`),
  KEY `idx_winner` (`is_winner`),
  CONSTRAINT `fk_bidding_forwarder` FOREIGN KEY (`forwarder_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_bidding_forwarding_requirement` FOREIGN KEY (`forwarding_requirement_id`) REFERENCES `forwarding_requirement` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='货代竞价表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `forwarding_requirement`
--

DROP TABLE IF EXISTS `forwarding_requirement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `forwarding_requirement` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '货代需求ID',
  `order_id` bigint unsigned DEFAULT NULL COMMENT '关联订单ID',
  `created_by` bigint unsigned NOT NULL COMMENT '创建用户ID（卖家）',
  `creator_role` varchar(20) NOT NULL DEFAULT 'buyer' COMMENT '需求创建者角色(buyer-买家, seller-卖家, forwarder-货代, admin-管理员)',
  `title` varchar(200) NOT NULL COMMENT '需求标题',
  `product_name` varchar(200) DEFAULT NULL,
  `hs_code` varchar(20) DEFAULT NULL COMMENT '海关商品编码(HS Code)',
  `destination_country` varchar(100) NOT NULL DEFAULT '',
  `destination_address` varchar(255) NOT NULL COMMENT '详细地址',
  `origin_country` varchar(100) DEFAULT NULL COMMENT '起始国家',
  `origin_address` varchar(255) DEFAULT NULL COMMENT '起始地址',
  `contact_person` varchar(50) NOT NULL COMMENT '联系人',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `expected_delivery_date` date DEFAULT NULL COMMENT '期望交付日期',
  `description` text,
  `cargo_type` varchar(100) NOT NULL COMMENT '货物类型',
  `product_category` varchar(100) DEFAULT NULL,
  `cargo_weight` decimal(10,2) NOT NULL COMMENT '货物重量(kg)',
  `cargo_volume` decimal(10,2) DEFAULT NULL COMMENT '货物体积(m³)',
  `package_count` int DEFAULT NULL,
  `special_requirements` text COMMENT '特殊要求',
  `documents` varchar(2048) DEFAULT NULL COMMENT '相关文件URL',
  `product_images` varchar(2048) DEFAULT NULL,
  `product_videos` varchar(2048) DEFAULT NULL,
  `budget` decimal(10,2) DEFAULT NULL COMMENT '预算金额',
  `status` enum('open','in_progress','completed','cancelled') NOT NULL DEFAULT 'open' COMMENT '状态：开放、进行中、已完成、已取消',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `port_of_loading_name` varchar(100) DEFAULT NULL COMMENT '起始港口名称 (例如: Shanghai)',
  `port_of_loading_code` varchar(10) DEFAULT NULL COMMENT '起始港口 UN/LOCODE (例如: CNSHA)',
  `port_of_destination_name` varchar(100) DEFAULT NULL COMMENT '目的港口名称 (例如: Rotterdam)',
  `port_of_destination_code` varchar(10) DEFAULT NULL COMMENT '目的港口 UN/LOCODE (例如: NLRTM)',
  `need_certification` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否需要认证',
  `need_fumigation` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否需要熏蒸',
  `shipment_type` enum('LCL','FCL') NOT NULL DEFAULT 'LCL' COMMENT '运输类型：LCL（拼柜）、FCL（整柜）',
  `container_size` varchar(10) DEFAULT NULL COMMENT '货柜尺寸（如20GP、40HQ等），仅当shipment_type=FCL时有效',
  `container_qty` int DEFAULT '0',
  `delivery_terms` text COMMENT '交付条款',
  `customs_service` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否包含报关服务：0-不包含，1-包含',
  `insurance_included` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否包含保险：0-不包含，1-包含',
  `data_type` varchar(20) DEFAULT 'real' COMMENT '数据类型：real-真实货代需求，mock-虚拟测试需求',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_requirement_creator` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=128 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单货代需求表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hourly_visit_summary`
--

DROP TABLE IF EXISTS `hourly_visit_summary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `hourly_visit_summary` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '汇总记录ID',
  `summary_date` date NOT NULL COMMENT '汇总日期',
  `summary_hour` tinyint unsigned NOT NULL COMMENT '汇总小时（0-23）',
  `total_visits` int unsigned NOT NULL DEFAULT '0' COMMENT '总访问量',
  `unique_visitors` int unsigned NOT NULL DEFAULT '0' COMMENT '独立访客数',
  `registered_user_visits` int unsigned NOT NULL DEFAULT '0' COMMENT '注册用户访问量',
  `anonymous_visits` int unsigned NOT NULL DEFAULT '0' COMMENT '匿名用户访问量',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date_hour` (`summary_date`,`summary_hour`),
  KEY `idx_summary_date_hour` (`summary_date`,`summary_hour`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小时访问汇总表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inviter_coupon`
--

DROP TABLE IF EXISTS `inviter_coupon`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inviter_coupon` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '优惠券ID',
  `inviter_id` bigint unsigned NOT NULL COMMENT '邀请人ID（买家）',
  `invitee_id` bigint unsigned NOT NULL COMMENT '被邀请者ID（订单创建者）',
  `trigger_order_id` bigint unsigned NOT NULL COMMENT '触发订单ID',
  `coupon_code` varchar(100) NOT NULL COMMENT '优惠券代码',
  `coupon_type` varchar(50) NOT NULL COMMENT '优惠券类型：FIXED_AMOUNT/PERCENTAGE',
  `discount_value` decimal(19,2) NOT NULL COMMENT '优惠金额或折扣比例',
  `minimum_amount` decimal(19,2) DEFAULT NULL COMMENT '最低使用金额',
  `maximum_discount` decimal(19,2) DEFAULT NULL COMMENT '最大优惠金额（用于百分比折扣）',
  `status` varchar(50) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE/USED/EXPIRED/CANCELLED',
  `month_key` varchar(7) NOT NULL COMMENT '月份键（格式：YYYY-MM）',
  `valid_from` datetime NOT NULL COMMENT '有效期开始时间',
  `valid_until` datetime NOT NULL COMMENT '有效期结束时间',
  `used_at` datetime DEFAULT NULL COMMENT '使用时间',
  `used_order_id` bigint unsigned DEFAULT NULL COMMENT '使用订单ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_coupon_code` (`coupon_code`),
  UNIQUE KEY `uk_trigger_order` (`trigger_order_id`),
  KEY `idx_inviter_month` (`inviter_id`,`month_key`),
  KEY `idx_inviter_status` (`inviter_id`,`status`),
  KEY `idx_status_valid` (`status`,`valid_until`),
  KEY `idx_month_key` (`month_key`),
  KEY `idx_created_at` (`created_at`),
  KEY `fk_coupon_invitee` (`invitee_id`),
  CONSTRAINT `fk_coupon_invitee` FOREIGN KEY (`invitee_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_coupon_inviter` FOREIGN KEY (`inviter_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='邀请人优惠券表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monthly_commission_summary`
--

DROP TABLE IF EXISTS `monthly_commission_summary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monthly_commission_summary` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '汇总记录ID',
  `inviter_id` bigint unsigned NOT NULL COMMENT '邀请者ID',
  `month_key` varchar(7) NOT NULL COMMENT '月份：YYYY-MM',
  `order_count` int NOT NULL DEFAULT '0' COMMENT '订单数量',
  `total_order_amount` decimal(19,2) NOT NULL DEFAULT '0.00' COMMENT '月度订单总金额',
  `commission_rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '月度适用佣金比例（如0.0120表示1.2%）',
  `commission_amount` decimal(19,2) NOT NULL DEFAULT '0.00' COMMENT '月度佣金总额',
  `bonus_amount` decimal(19,2) DEFAULT '0.00' COMMENT '月度奖金',
  `total_amount` decimal(19,2) NOT NULL DEFAULT '0.00' COMMENT '月度总收益（佣金+奖金）',
  `status` varchar(50) DEFAULT 'PENDING' COMMENT '状态：PENDING/PROCESSING/COMPLETED/CANCELLED',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_calculated_at` datetime DEFAULT NULL COMMENT '最后计算时间',
  `calculation_version` int NOT NULL DEFAULT '1' COMMENT '计算版本号',
  `settled_at` datetime DEFAULT NULL COMMENT '结算时间',
  `deleted` char(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_inviter_month` (`inviter_id`,`month_key`),
  KEY `idx_month_key` (`month_key`),
  KEY `idx_status` (`status`),
  KEY `idx_total_order_amount` (`total_order_amount`),
  KEY `idx_settled_at` (`settled_at`),
  KEY `idx_commission_rate` (`commission_rate`),
  KEY `idx_last_calculated` (`last_calculated_at`),
  KEY `idx_calculation_version` (`calculation_version`),
  KEY `idx_deleted` (`deleted`),
  CONSTRAINT `fk_monthly_summary_inviter` FOREIGN KEY (`inviter_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='月度佣金汇总表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `notification`
--

DROP TABLE IF EXISTS `notification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notification` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `type` varchar(64) NOT NULL COMMENT '通知类型',
  `title` varchar(255) NOT NULL COMMENT '通知标题',
  `content` text COMMENT '通知内容',
  `status` int DEFAULT '0' COMMENT '状态（0-未读，1-已读，2-处理）',
  `receiver_id` bigint NOT NULL COMMENT '接收者用户ID',
  `receiver_role` varchar(32) NOT NULL COMMENT '接收者角色',
  `related_id` bigint DEFAULT NULL COMMENT '关联业务ID（如竞价ID）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `read_at` datetime DEFAULT NULL COMMENT '阅读时间',
  PRIMARY KEY (`id`),
  KEY `idx_receiver` (`receiver_id`,`receiver_role`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `order_item`
--

DROP TABLE IF EXISTS `order_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `order_item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `order_id` bigint unsigned NOT NULL COMMENT '订单ID',
  `name` varchar(200) NOT NULL COMMENT '产品名称',
  `description` text COMMENT '产品描述',
  `hs_code` varchar(20) DEFAULT NULL COMMENT '海关商品编码(HS Code)',
  `specification` text COMMENT '规格参数',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `quantity` decimal(10,2) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) NOT NULL COMMENT '单价',
  `total_price` decimal(10,2) NOT NULL COMMENT '总价',
  `tax_rate` decimal(5,2) DEFAULT NULL COMMENT '税率(%)',
  `tax_amount` decimal(10,2) DEFAULT NULL COMMENT '税额',
  `discount_rate` decimal(5,2) DEFAULT NULL COMMENT '折扣率(%)',
  `discount_amount` decimal(10,2) DEFAULT NULL COMMENT '折扣金额',
  `final_price` decimal(10,2) NOT NULL COMMENT '最终价格（含税、折扣后）',
  `weight` decimal(10,3) DEFAULT NULL COMMENT '单件重量(例如 kg)',
  `volume` decimal(10,4) DEFAULT NULL COMMENT '单件体积(例如 立方米)',
  `attributes_json` json DEFAULT NULL COMMENT '产品属性JSON',
  `images` varchar(1000) DEFAULT NULL COMMENT '产品图片URL列表，多个URL用逗号分隔',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `remarks` text COMMENT '项目备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `category_id` bigint unsigned DEFAULT NULL COMMENT '商品分类ID',
  PRIMARY KEY (`id`),
  KEY `idx_order` (`order_id`),
  CONSTRAINT `fk_order_item_order` FOREIGN KEY (`order_id`) REFERENCES `unified_order` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单项目表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `order_progress_activities`
--

DROP TABLE IF EXISTS `order_progress_activities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `order_progress_activities` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint unsigned NOT NULL,
  `stage_value` int NOT NULL COMMENT '对应的进度节点值(0,20,40,60,80,100)',
  `title` varchar(100) NOT NULL COMMENT '活动标题',
  `description` text COMMENT '活动详细描述',
  `image_urls` json DEFAULT NULL COMMENT '活动相关图片URL数组，JSON格式',
  `video_urls` json DEFAULT NULL COMMENT '活动相关视频URL数组，JSON格式',
  `activity_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '活动发生时间',
  `created_by` bigint unsigned NOT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` enum('0','1') NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_stage_value` (`stage_value`),
  CONSTRAINT `fk_order_progress_activities_order_id` FOREIGN KEY (`order_id`) REFERENCES `unified_order` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payment_proof`
--

DROP TABLE IF EXISTS `payment_proof`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payment_proof` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '支付凭证ID',
  `order_id` bigint unsigned DEFAULT NULL COMMENT '关联的订单ID（普通订单时使用）',
  `payment_type` enum('DEPOSIT','FINAL','FULL') NOT NULL COMMENT '支付类型：DEPOSIT-定金，FINAL-尾款，FULL-全款',
  `proof_url` varchar(255) NOT NULL COMMENT '支付凭证文件URL',
  `uploaded_by` bigint unsigned NOT NULL COMMENT '上传用户ID (买家)',
  `status` enum('PENDING','CONFIRMED','REJECTED') NOT NULL DEFAULT 'PENDING',
  `remarks` text COMMENT '审核备注（例如拒绝原因）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（即上传时间）',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `uploaded_at` datetime DEFAULT NULL COMMENT '上传时间',
  `confirmed_by` bigint unsigned DEFAULT NULL COMMENT '确认人ID',
  `confirmed_at` datetime DEFAULT NULL COMMENT '确认时间',
  `order_type` enum('UNIFIED','SAMPLE','SETTLEMENT') NOT NULL DEFAULT 'UNIFIED' COMMENT '订单类型：UNIFIED-普通订单直接支付，SAMPLE-样品订单，SETTLEMENT-结算单支付',
  `sample_order_id` bigint DEFAULT NULL COMMENT '样品订单ID（当order_type为SAMPLE时使用）',
  `settlement_id` bigint DEFAULT NULL COMMENT '结算单ID（当order_type为SETTLEMENT时使用）',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '支付金额',
  `admin_confirmed_amount` decimal(10,2) DEFAULT NULL COMMENT '管理员确认金额',
  `buyer_payment_time` datetime DEFAULT NULL COMMENT '买家付款时间',
  `buyer_remark` varchar(255) DEFAULT NULL COMMENT '买家备注',
  `admin_confirm_time` datetime DEFAULT NULL COMMENT '管理员确认时间',
  `admin_proof_url` varchar(255) DEFAULT NULL COMMENT '管理员收款凭证URL',
  `admin_remark` varchar(255) DEFAULT NULL COMMENT '管理员备注',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_payment_proof_sample_order_id` (`sample_order_id`),
  KEY `idx_payment_proof_order_type` (`order_type`),
  KEY `idx_settlement_id` (`settlement_id`),
  KEY `idx_order_type_status` (`order_type`,`status`),
  KEY `idx_amount` (`amount`),
  KEY `idx_buyer_payment_time` (`buyer_payment_time`),
  CONSTRAINT `fk_payment_proof_order` FOREIGN KEY (`order_id`) REFERENCES `unified_order` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_payment_proof_uploader` FOREIGN KEY (`uploaded_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='支付凭证表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ports`
--

DROP TABLE IF EXISTS `ports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ports` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '港口ID',
  `port_code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '港口代码',
  `port_name_cn` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '港口中文名',
  `port_name_en` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '港口英文名',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_port_code` (`port_code`),
  KEY `idx_port_name_en` (`port_name_en`)
) ENGINE=InnoDB AUTO_INCREMENT=4917 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='港口信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product`
--

DROP TABLE IF EXISTS `product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '产品ID',
  `seller_id` bigint unsigned NOT NULL COMMENT '卖家ID',
  `category_id` bigint unsigned DEFAULT NULL COMMENT '分类ID',
  `name` varchar(200) NOT NULL COMMENT '产品名称',
  `name_en` varchar(200) DEFAULT NULL COMMENT '产品英文名称',
  `description` text NOT NULL COMMENT '产品描述',
  `specification` text NOT NULL COMMENT '产品规格',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-下架，1-上架',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `images` varchar(2048) DEFAULT NULL COMMENT '产品图片URL列表，多个URL用分隔符分隔',
  `videos` varchar(2048) DEFAULT NULL COMMENT '产品视频URL列表，多个URL用分隔符分隔',
  `stock` int NOT NULL DEFAULT '0' COMMENT '库存数量',
  `attributes_json` json DEFAULT NULL COMMENT '产品属性值JSON，包含该产品的所有属性及其值',
  `attributes_json_en` json DEFAULT NULL COMMENT '产品英文属性值JSON，包含该产品的所有英文属性及其值',
  PRIMARY KEY (`id`),
  KEY `idx_seller` (`seller_id`),
  KEY `idx_status` (`status`),
  KEY `idx_category_id` (`category_id`),
  CONSTRAINT `fk_product_category` FOREIGN KEY (`category_id`) REFERENCES `product_category` (`id`),
  CONSTRAINT `fk_product_seller` FOREIGN KEY (`seller_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='产品表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_category`
--

DROP TABLE IF EXISTS `product_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_category` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `name_en` varchar(100) DEFAULT NULL COMMENT '分类英文名称',
  `description` varchar(500) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(200) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int DEFAULT '0' COMMENT '排序顺序',
  `parent_id` bigint unsigned DEFAULT NULL COMMENT '父分类ID',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `attributes_json` json DEFAULT NULL COMMENT '分类属性模板JSON，包含该分类下产品应具备的属性',
  `attributes_json_en` json DEFAULT NULL COMMENT '分类英文属性模板JSON，包含该分类下产品应具备的英文属性',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_name_en` (`name_en`),
  CONSTRAINT `fk_product_category_parent` FOREIGN KEY (`parent_id`) REFERENCES `product_category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15637 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `purchase_forwarding_settlement`
--

DROP TABLE IF EXISTS `purchase_forwarding_settlement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `purchase_forwarding_settlement` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `settlement_number` varchar(64) NOT NULL COMMENT '结算编号',
  `unified_order_id` bigint NOT NULL COMMENT '采购订单ID',
  `forwarder_order_id` bigint NOT NULL COMMENT '货代订单ID',
  `settlement_amount` decimal(12,2) DEFAULT NULL COMMENT '结算总金额',
  `purchase_amount` decimal(12,2) DEFAULT NULL COMMENT '采购金额',
  `freight_amount` decimal(12,2) DEFAULT NULL COMMENT '货运金额',
  `deposit_amount` decimal(12,2) DEFAULT NULL COMMENT '定金金额',
  `final_payment_amount` decimal(12,2) DEFAULT NULL COMMENT '尾款金额',
  `currency` varchar(16) DEFAULT 'USD' COMMENT '货币单位，默认USD',
  `payment_terms` varchar(128) DEFAULT NULL COMMENT '支付条款/支付比例',
  `delivery_terms` varchar(32) DEFAULT NULL COMMENT '交付条款(FOB/CIF/EXW等)',
  `port_of_loading_name` varchar(100) DEFAULT NULL COMMENT '起运港名称',
  `port_of_loading_code` varchar(10) DEFAULT NULL COMMENT '起运港代码',
  `port_of_destination_name` varchar(100) DEFAULT NULL COMMENT '目的港名称',
  `port_of_destination_code` varchar(10) DEFAULT NULL COMMENT '目的港代码',
  `shipping_method` varchar(20) DEFAULT NULL COMMENT '运输方式(SEA/AIR/RAIL/ROAD)',
  `need_certification` char(1) DEFAULT '0' COMMENT '需要认证(1是0否)',
  `need_fumigation` char(1) DEFAULT '0' COMMENT '需要熏蒸(1是0否)',
  `shipment_type` varchar(10) DEFAULT NULL COMMENT '装运类型(FCL整柜/LCL拼箱)',
  `container_size` varchar(20) DEFAULT NULL COMMENT '集装箱尺寸',
  `container_qty` int DEFAULT '1' COMMENT '集装箱数量',
  `etd` datetime DEFAULT NULL COMMENT '预计离港时间(ETD)',
  `eta` datetime DEFAULT NULL COMMENT '预计到港时间(ETA)',
  `insurance_included` tinyint(1) DEFAULT '0' COMMENT '是否包含保险',
  `insurance_amount` decimal(10,2) DEFAULT NULL COMMENT '保险金额',
  `customs_service` tinyint(1) DEFAULT '0' COMMENT '是否提供清关服务',
  `settlement_status` varchar(32) DEFAULT 'pending' COMMENT '结算状态：pending(待结算)、processing(结算中)、completed(已完成)、cancelled(已取消)',
  `payment_status` varchar(32) DEFAULT 'unpaid' COMMENT '支付状态：unpaid(未支付)、deposit_paid(定金已支付)、deposit_refunded(定金已退款)、final_paid(尾款已支付)、final_refunded(尾款已退款)',
  `deposit_payment_time` datetime DEFAULT NULL COMMENT '定金支付时间',
  `final_payment_time` datetime DEFAULT NULL COMMENT '尾款支付时间',
  `buyer_id` bigint DEFAULT NULL COMMENT '买家ID',
  `buyer_company` varchar(128) DEFAULT NULL COMMENT '买家公司名称',
  `buyer_contact` varchar(50) DEFAULT NULL COMMENT '买家联系人',
  `buyer_phone` varchar(20) DEFAULT NULL COMMENT '买家联系电话',
  `buyer_email` varchar(100) DEFAULT NULL COMMENT '买家邮箱',
  `buyer_address` varchar(255) DEFAULT NULL COMMENT '买家地址',
  `seller_id` bigint DEFAULT NULL COMMENT '卖家ID',
  `seller_company` varchar(128) DEFAULT NULL COMMENT '卖家公司名称',
  `seller_contact` varchar(50) DEFAULT NULL COMMENT '卖家联系人',
  `seller_phone` varchar(20) DEFAULT NULL COMMENT '卖家联系电话',
  `seller_email` varchar(100) DEFAULT NULL COMMENT '卖家邮箱',
  `seller_address` varchar(255) DEFAULT NULL COMMENT '卖家地址',
  `forwarder_id` bigint DEFAULT NULL COMMENT '货代ID',
  `forwarder_company` varchar(128) DEFAULT NULL COMMENT '货代公司名称',
  `forwarder_contact` varchar(50) DEFAULT NULL COMMENT '货代联系人',
  `forwarder_phone` varchar(20) DEFAULT NULL COMMENT '货代联系电话',
  `forwarder_email` varchar(100) DEFAULT NULL COMMENT '货代邮箱',
  `forwarder_address` varchar(255) DEFAULT NULL COMMENT '货代地址',
  `receiver_name` varchar(50) DEFAULT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收货人联系电话',
  `receiver_email` varchar(100) DEFAULT NULL COMMENT '收货人邮箱',
  `receiver_address` varchar(255) DEFAULT NULL COMMENT '收货地址',
  `remarks` varchar(512) DEFAULT NULL COMMENT '备注',
  `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
  `creator_name` varchar(64) DEFAULT NULL COMMENT '创建者名称',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` char(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `order_items` json DEFAULT NULL COMMENT '订单项JSON数据',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_settlement_number` (`settlement_number`),
  KEY `idx_unified_order_id` (`unified_order_id`),
  KEY `idx_forwarder_order_id` (`forwarder_order_id`),
  KEY `idx_settlement_status` (`settlement_status`),
  KEY `idx_payment_status` (`payment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='采购货运结算表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `purchase_requirement`
--

DROP TABLE IF EXISTS `purchase_requirement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `purchase_requirement` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '需求ID',
  `buyer_id` bigint unsigned NOT NULL COMMENT '买家ID',
  `title` varchar(200) NOT NULL COMMENT '需求标题',
  `title_en` varchar(200) DEFAULT NULL COMMENT '需求英文标题',
  `description` text,
  `specification` text,
  `min_price` decimal(10,2) DEFAULT NULL COMMENT '最低期望价格',
  `max_price` decimal(10,2) DEFAULT NULL COMMENT '最高期望价格',
  `expected_delivery_time` date DEFAULT NULL,
  `images` varchar(2048) DEFAULT NULL COMMENT '图片URL列表，多个URL用分隔符分隔',
  `videos` varchar(2048) DEFAULT NULL COMMENT '视频URL列表，多个URL用分隔符分隔',
  `pdf_url` varchar(512) DEFAULT NULL COMMENT 'PDF文档URL，可存储需求相关的PDF文档链接',
  `status` enum('in_progress','completed','cancelled') NOT NULL DEFAULT 'in_progress',
  `attributes_json` json DEFAULT NULL COMMENT '需求属性值JSON，包含该需求的所有属性及其值',
  `attributes_json_en` json DEFAULT NULL COMMENT '需求英文属性值JSON，包含该需求的所有英文属性及其值',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `category_id` bigint unsigned DEFAULT NULL COMMENT '需求类别ID',
  `service_fee_rate` decimal(5,2) NOT NULL COMMENT '服务费比例（百分比）',
  `service_level` enum('basic','standard','premium') NOT NULL DEFAULT 'basic' COMMENT '服务级别：基础、标准、高级',
  `contact_info` json DEFAULT NULL COMMENT '买家联系方式(JSON格式)',
  `quantity` decimal(12,3) DEFAULT NULL,
  `unit` varchar(20) DEFAULT NULL,
  `hs_code` varchar(20) DEFAULT NULL COMMENT '海关商品编码(HS Code)',
  `requirement_type` enum('purchase','sample') NOT NULL DEFAULT 'purchase' COMMENT '需求类型：purchase-普通采购，sample-样品需求',
  `delivery_terms` text,
  `data_type` varchar(20) DEFAULT 'real' COMMENT '数据类型：real-真实需求，mock-虚拟测试需求',
  PRIMARY KEY (`id`),
  KEY `idx_buyer` (`buyer_id`),
  KEY `idx_status` (`status`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_requirement_type` (`requirement_type`),
  KEY `idx_buyer_type_status` (`buyer_id`,`requirement_type`,`status`),
  CONSTRAINT `fk_requirement_buyer` FOREIGN KEY (`buyer_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_requirement_category` FOREIGN KEY (`category_id`) REFERENCES `requirement_category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=186 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='采购需求表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `purchase_requirement_backup_contact_info`
--

DROP TABLE IF EXISTS `purchase_requirement_backup_contact_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `purchase_requirement_backup_contact_info` (
  `id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '需求ID',
  `contact_info` varchar(255) DEFAULT NULL COMMENT '买家联系方式'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `requirement_category`
--

DROP TABLE IF EXISTS `requirement_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `requirement_category` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '类别名称',
  `name_en` varchar(100) DEFAULT NULL COMMENT '类别英文名称',
  `description` varchar(500) DEFAULT NULL COMMENT '类别描述',
  `icon` varchar(200) DEFAULT NULL COMMENT '类别图标',
  `sort_order` int DEFAULT '0' COMMENT '排序顺序',
  `parent_id` bigint unsigned DEFAULT NULL COMMENT '父类别ID',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `attributes_json` json DEFAULT NULL COMMENT '需求属性值JSON，包含该需求的所有属性及其值',
  `attributes_json_en` json DEFAULT NULL COMMENT '需求英文属性值JSON，包含该需求的所有英文属性及其值',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_name_en` (`name_en`),
  FULLTEXT KEY `ft_name_en` (`name_en`),
  CONSTRAINT `fk_requirement_category_parent` FOREIGN KEY (`parent_id`) REFERENCES `requirement_category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15637 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='需求分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sample_order`
--

DROP TABLE IF EXISTS `sample_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sample_order` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '样品订单ID',
  `order_number` varchar(50) NOT NULL COMMENT '样品订单编号，格式: SP+日期+序列号',
  `requirement_id` bigint unsigned NOT NULL COMMENT '关联的样品需求ID',
  `buyer_id` bigint unsigned NOT NULL COMMENT '买家ID',
  `buyer_company` varchar(100) DEFAULT NULL COMMENT '买家公司名称',
  `buyer_contact` varchar(50) NOT NULL COMMENT '买家联系人',
  `buyer_phone` varchar(20) NOT NULL COMMENT '买家联系电话',
  `buyer_email` varchar(100) DEFAULT NULL COMMENT '买家邮箱',
  `buyer_address` varchar(255) NOT NULL COMMENT '买家收货地址',
  `order_status` enum('pending','setted_address','setted_shipping_fee','paid','shipped','completed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `total_sample_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '样品总价',
  `shipping_fee` decimal(10,2) DEFAULT NULL COMMENT '运费（管理员填写）',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额（样品价格+运费）',
  `payment_status` enum('UNPAID','PENDING','PAID','REJECTED') DEFAULT 'UNPAID' COMMENT '支付状态：UNPAID-未支付，PENDING-待审核，PAID-已支付，REJECTED-支付被拒绝',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式：BANK_TRANSFER-银行转账，ALIPAY-支付宝，WECHAT-微信等',
  `admin_id` bigint unsigned DEFAULT NULL COMMENT '处理管理员ID',
  `admin_remark` text COMMENT '管理员备注',
  `admin_processed_at` timestamp NULL DEFAULT NULL COMMENT '管理员处理时间',
  `shipping_method` varchar(50) DEFAULT NULL COMMENT '邮寄方式（如：顺丰、EMS等）',
  `tracking_number` varchar(100) DEFAULT NULL COMMENT '快递单号',
  `shipped_at` timestamp NULL DEFAULT NULL COMMENT '发货时间',
  `estimated_delivery_date` date DEFAULT NULL COMMENT '预计送达日期',
  `actual_delivery_date` date DEFAULT NULL COMMENT '实际送达日期',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `deleted` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_number` (`order_number`),
  KEY `idx_requirement` (`requirement_id`),
  KEY `idx_buyer` (`buyer_id`),
  KEY `idx_admin` (`admin_id`),
  KEY `idx_status` (`order_status`),
  KEY `idx_created` (`created_at`),
  KEY `idx_sample_order_payment_status` (`payment_status`),
  KEY `idx_sample_order_payment_time` (`payment_time`),
  KEY `idx_sample_order_status_payment` (`order_status`,`payment_status`),
  CONSTRAINT `fk_sample_order_admin` FOREIGN KEY (`admin_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_sample_order_buyer` FOREIGN KEY (`buyer_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_sample_order_requirement` FOREIGN KEY (`requirement_id`) REFERENCES `purchase_requirement` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='样品订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sample_order_item`
--

DROP TABLE IF EXISTS `sample_order_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sample_order_item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `sample_order_id` bigint unsigned NOT NULL COMMENT '样品订单ID',
  `bidding_id` bigint unsigned NOT NULL COMMENT '关联的样品竞价ID',
  `seller_id` bigint unsigned NOT NULL COMMENT '卖家ID',
  `seller_company` varchar(100) DEFAULT NULL COMMENT '卖家公司名称',
  `seller_contact` varchar(50) DEFAULT NULL COMMENT '卖家联系人',
  `seller_phone` varchar(20) DEFAULT NULL COMMENT '卖家联系电话',
  `product_name` varchar(255) NOT NULL COMMENT '样品产品名称',
  `sample_specification` text COMMENT '样品规格说明',
  `sample_quantity` int NOT NULL DEFAULT '1' COMMENT '样品数量',
  `sample_price` decimal(10,2) NOT NULL COMMENT '单个样品价格',
  `total_price` decimal(10,2) NOT NULL COMMENT '该项总价',
  `attributes_json` json DEFAULT NULL COMMENT '样品属性JSON',
  `images` varchar(2048) DEFAULT NULL COMMENT '样品图片URL列表',
  `videos` varchar(2048) DEFAULT NULL COMMENT '样品视频URL列表',
  `item_status` enum('pending','confirmed','shipped','delivered') NOT NULL DEFAULT 'pending' COMMENT '明细状态：pending-待处理，confirmed-已确认，shipped-已发货，delivered-已送达',
  `remark` text COMMENT '备注信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sample_order` (`sample_order_id`),
  KEY `idx_bidding` (`bidding_id`),
  KEY `idx_seller` (`seller_id`),
  KEY `idx_status` (`item_status`),
  CONSTRAINT `fk_sample_item_bidding` FOREIGN KEY (`bidding_id`) REFERENCES `bidding_record` (`id`),
  CONSTRAINT `fk_sample_item_order` FOREIGN KEY (`sample_order_id`) REFERENCES `sample_order` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sample_item_seller` FOREIGN KEY (`seller_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='样品订单明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `service_level_config`
--

DROP TABLE IF EXISTS `service_level_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_level_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `level_code` enum('basic','standard','premium') NOT NULL COMMENT '服务级别代码',
  `name` varchar(50) NOT NULL COMMENT '服务级别名称',
  `name_en` varchar(50) DEFAULT NULL COMMENT '服务级别英文名称',
  `description` text NOT NULL COMMENT '服务描述',
  `description_en` text COMMENT '服务英文描述',
  `fee_rate` decimal(5,2) NOT NULL COMMENT '服务费比例（百分比）',
  `features_json` json NOT NULL COMMENT '服务特性JSON',
  `features_json_en` json DEFAULT NULL COMMENT '服务特性英文JSON',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level_code` (`level_code`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务级别配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shipping_status`
--

DROP TABLE IF EXISTS `shipping_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shipping_status` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '状态记录ID',
  `forwarding_bidding_id` bigint unsigned NOT NULL COMMENT '关联的货代竞价ID',
  `order_id` bigint unsigned NOT NULL COMMENT '冗余-订单ID',
  `forwarding_requirement_id` bigint unsigned NOT NULL COMMENT '冗余-需求ID',
  `status_code` varchar(50) NOT NULL COMMENT '状态代码',
  `status_name` varchar(100) NOT NULL COMMENT '状态名称',
  `location` varchar(255) DEFAULT NULL COMMENT '当前位置',
  `description` text COMMENT '状态详细描述',
  `occur_time` timestamp NOT NULL COMMENT '状态发生时间',
  `estimated_arrival` timestamp NULL DEFAULT NULL COMMENT '预计到达时间',
  `operator_id` bigint unsigned DEFAULT NULL COMMENT '操作人ID',
  `proof_images` varchar(2048) DEFAULT NULL COMMENT '状态证明图片',
  `exception_flag` tinyint DEFAULT '0' COMMENT '异常标记：0-正常，1-异常',
  `exception_reason` varchar(500) DEFAULT NULL COMMENT '异常原因',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_forwarding_bidding` (`forwarding_bidding_id`),
  KEY `idx_order` (`order_id`),
  KEY `idx_forwarding_requirement` (`forwarding_requirement_id`),
  KEY `idx_status` (`status_code`),
  KEY `idx_occur_time` (`occur_time`),
  KEY `idx_exception` (`exception_flag`),
  CONSTRAINT `fk_status_forwarding_bidding` FOREIGN KEY (`forwarding_bidding_id`) REFERENCES `forwarding_bidding` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='货代运输状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `unified_order`
--

DROP TABLE IF EXISTS `unified_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `unified_order` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_number` varchar(50) NOT NULL COMMENT '订单编号（业务编号）',
  `room_id` bigint unsigned NOT NULL COMMENT '聊天室ID',
  `buyer_id` bigint unsigned NOT NULL COMMENT '买家ID',
  `buyer_company` varchar(100) DEFAULT NULL COMMENT '买家公司名称',
  `buyer_contact` varchar(50) NOT NULL COMMENT '买家联系人',
  `buyer_phone` varchar(20) NOT NULL COMMENT '买家联系电话',
  `buyer_email` varchar(100) DEFAULT NULL COMMENT '买家邮箱',
  `buyer_address` varchar(255) DEFAULT NULL COMMENT '买家地址',
  `buyer_sign_time` timestamp NULL DEFAULT NULL COMMENT '买家签署时间',
  `buyer_signature_url` varchar(255) DEFAULT NULL COMMENT '买家电子签名图片URL',
  `seller_id` bigint unsigned NOT NULL COMMENT '卖家ID',
  `seller_company` varchar(100) DEFAULT NULL COMMENT '卖家公司名称',
  `seller_contact` varchar(50) NOT NULL COMMENT '卖家联系人',
  `seller_phone` varchar(20) NOT NULL COMMENT '卖家联系电话',
  `seller_email` varchar(100) DEFAULT NULL COMMENT '卖家邮箱',
  `seller_address` varchar(255) DEFAULT NULL COMMENT '卖家地址',
  `seller_sign_time` timestamp NULL DEFAULT NULL COMMENT '卖家签署时间',
  `seller_signature_url` varchar(255) DEFAULT NULL COMMENT '卖家电子签名图片URL',
  `total_price` decimal(10,2) NOT NULL COMMENT '订单总价',
  `total_weight` decimal(10,3) DEFAULT NULL COMMENT '订单总重量(kg)',
  `total_volume` decimal(10,4) DEFAULT NULL COMMENT '订单总体积(m³)',
  `cargo_type` varchar(100) DEFAULT NULL COMMENT '货物类型',
  `payment_terms` text COMMENT '支付条款',
  `deposit_amount` decimal(10,2) DEFAULT NULL COMMENT '定金金额',
  `final_payment_amount` decimal(10,2) DEFAULT NULL,
  `payment_status` enum('unpaid','deposit_paid','deposit_refunded','final_paid','final_refunded') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态：未支付、定金已支付、定金已退款、尾款已支付、尾款已退款',
  `payment_time` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `deposit_payment_time` timestamp NULL DEFAULT NULL COMMENT '定金支付时间',
  `delivery_terms` text COMMENT '交付条款',
  `expected_delivery_time` date NOT NULL COMMENT '预计交付时间',
  `receiver_name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收货人联系电话',
  `receiver_email` varchar(100) DEFAULT NULL COMMENT '收货人邮箱',
  `receiver_address` varchar(255) NOT NULL COMMENT '收货地址',
  `progress_info` text COMMENT '进度信息',
  `progress_percentage` int DEFAULT '0' COMMENT '进度百分比，0-100',
  `remarks` text COMMENT '订单备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '订单完成时间',
  `deleted` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `order_status` enum('draft','processing','completed','cancelled') NOT NULL DEFAULT 'draft',
  `country_of_origin` varchar(100) DEFAULT NULL COMMENT '生产国别',
  `manufacturer_name` varchar(255) DEFAULT NULL COMMENT '制造厂商名称',
  `port_of_loading_name` varchar(100) DEFAULT NULL COMMENT '起始港口名称 (例如: Shanghai)',
  `port_of_loading_code` varchar(10) DEFAULT NULL COMMENT '起始港口 UN/LOCODE (例如: CNSHA)',
  `port_of_destination_name` varchar(100) DEFAULT NULL COMMENT '目的港口名称 (例如: Rotterdam)',
  `port_of_destination_code` varchar(10) DEFAULT NULL COMMENT '目的港口 UN/LOCODE (例如: NLRTM)',
  `need_certification` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否需要认证：0-不需要，1-需要',
  `need_fumigation` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否需要熏蒸：0-不需要，1-需要',
  `shipment_type` enum('LCL','FCL') DEFAULT NULL COMMENT '运输类型：LCL（拼柜）、FCL（整柜）',
  `container_size` varchar(10) DEFAULT NULL COMMENT '货柜尺寸（如20GP、40HQ等），仅当shipmentType=FCL时有效',
  `container_qty` int DEFAULT '1' COMMENT '货柜数量（默认1），仅当shipmentType=FCL时有效',
  `shipping_address` varchar(255) NOT NULL COMMENT '发货地址',
  `shipping_contact` varchar(50) NOT NULL COMMENT '发货人姓名',
  `shipping_phone` varchar(20) NOT NULL COMMENT '发货人电话',
  `shipping_email` varchar(100) DEFAULT NULL COMMENT '发货人邮箱',
  `customs_service` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否包含报关服务：0-不包含，1-包含',
  `insurance_included` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否包含保险：0-不包含，1-包含',
  `final_document_url` varchar(255) DEFAULT NULL COMMENT '最终文档URL',
  `document_hash` varchar(64) DEFAULT NULL COMMENT '文档哈希值',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_number` (`order_number`),
  KEY `idx_buyer` (`buyer_id`),
  KEY `idx_seller` (`seller_id`),
  KEY `idx_room` (`room_id`),
  CONSTRAINT `fk_unified_order_buyer` FOREIGN KEY (`buyer_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_unified_order_room` FOREIGN KEY (`room_id`) REFERENCES `chat_room` (`id`),
  CONSTRAINT `fk_unified_order_seller` FOREIGN KEY (`seller_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码（加密存储）',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `phone_country_code` varchar(10) DEFAULT NULL COMMENT '手机区号（如：+86、+1等）',
  `avatar` varchar(255) DEFAULT NULL COMMENT '用户头像URL',
  `role` enum('buyer','seller','admin','forwarder') NOT NULL COMMENT '用户角色：买家、卖家、管理员、货代',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `invite_code` varchar(20) NOT NULL COMMENT '用户专属邀请码',
  `inviter_id` bigint unsigned DEFAULT NULL COMMENT '父节点ID(直接邀请人)',
  `grandparent_id` bigint unsigned DEFAULT NULL COMMENT '祖父节点ID(间接邀请人)',
  `invite_time` timestamp NULL DEFAULT NULL COMMENT '被邀请时间',
  `child_count` int NOT NULL DEFAULT '0' COMMENT '直接邀请用户数',
  `grandchild_count` int NOT NULL DEFAULT '0' COMMENT '间接邀请用户数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `company` varchar(100) DEFAULT NULL COMMENT '公司名称',
  `industry` varchar(100) DEFAULT NULL COMMENT '用户所属行业',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人姓名',
  `country` varchar(100) DEFAULT NULL COMMENT '国家',
  `province` varchar(100) DEFAULT NULL COMMENT '省份/州',
  `city` varchar(100) DEFAULT NULL COMMENT '城市',
  `postal_code` varchar(20) DEFAULT NULL COMMENT '邮政编码',
  `detailed_address` varchar(512) DEFAULT NULL COMMENT '详细地址',
  `company_license` varchar(255) DEFAULT NULL COMMENT '营业执照号',
  `tax_id` varchar(100) DEFAULT NULL COMMENT '税号',
  `social_credit_code` varchar(100) DEFAULT NULL COMMENT '统一社会信用代码',
  `business_license_image` varchar(1024) DEFAULT NULL COMMENT '营业执照照片路径',
  `qualification_images` varchar(1024) DEFAULT NULL COMMENT '资质证书照片路径',
  `company_environment_images` varchar(1024) DEFAULT NULL COMMENT '公司/工厂环境照片路径，多张图片用逗号分隔',
  `data_type` varchar(20) DEFAULT 'real' COMMENT '数据类型：real-真实用户，mock-虚拟测试用户',
  PRIMARY KEY (`id`),
  UNIQUE KEY `invite_code` (`invite_code`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=93 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_payment_account`
--

DROP TABLE IF EXISTS `user_payment_account`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_payment_account` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `account_type` enum('bank_transfer','alipay','wechat') NOT NULL COMMENT '账户类型',
  `account_name` varchar(100) NOT NULL COMMENT '账户名称/备注',
  `account_holder` varchar(50) NOT NULL COMMENT '账户持有人姓名',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '银行名称',
  `account_number` varchar(200) DEFAULT NULL COMMENT '银行账号（加密存储）',
  `branch_name` varchar(200) DEFAULT NULL COMMENT '开户行支行',
  `swift_code` varchar(20) DEFAULT NULL COMMENT 'SWIFT代码（国际转账）',
  `alipay_account` varchar(200) DEFAULT NULL COMMENT '支付宝账号（加密存储）',
  `wechat_account` varchar(200) DEFAULT NULL COMMENT '微信账号（加密存储）',
  `is_default` tinyint NOT NULL DEFAULT '0' COMMENT '是否为默认账户：0-否，1-是',
  `is_verified` tinyint NOT NULL DEFAULT '0' COMMENT '是否已验证：0-未验证，1-已验证',
  `status` enum('active','inactive','pending','rejected') NOT NULL DEFAULT 'pending' COMMENT '账户状态',
  `verification_notes` text COMMENT '验证备注',
  `verified_at` timestamp NULL DEFAULT NULL COMMENT '验证时间',
  `verified_by` varchar(50) DEFAULT NULL COMMENT '验证人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `extra_info` json DEFAULT NULL COMMENT '扩展信息（JSON格式）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_type` (`user_id`,`account_type`),
  KEY `idx_user_default` (`user_id`,`is_default`),
  KEY `idx_status` (`status`),
  KEY `idx_verified` (`is_verified`),
  KEY `idx_deleted` (`deleted`),
  CONSTRAINT `fk_payment_account_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户收款账户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_visit_summary`
--

DROP TABLE IF EXISTS `user_visit_summary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_visit_summary` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '汇总记录ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `summary_date` date NOT NULL COMMENT '汇总日期',
  `visit_count` int unsigned NOT NULL DEFAULT '0' COMMENT '访问次数',
  `first_visit_time` time DEFAULT NULL COMMENT '首次访问时间',
  `last_visit_time` time DEFAULT NULL COMMENT '最后访问时间',
  `total_duration_minutes` int unsigned DEFAULT NULL COMMENT '总访问时长（分钟）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`,`summary_date`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_summary_date` (`summary_date`),
  CONSTRAINT `fk_user_visit_summary_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户访问汇总表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Temporary view structure for view `v_active_bidding_data_summary`
--

DROP TABLE IF EXISTS `v_active_bidding_data_summary`;
/*!50001 DROP VIEW IF EXISTS `v_active_bidding_data_summary`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_active_bidding_data_summary` AS SELECT 
 1 AS `table_name`,
 1 AS `total_count`,
 1 AS `mock_count`,
 1 AS `real_count`,
 1 AS `mock_percentage`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_bidding_data_summary`
--

DROP TABLE IF EXISTS `v_bidding_data_summary`;
/*!50001 DROP VIEW IF EXISTS `v_bidding_data_summary`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_bidding_data_summary` AS SELECT 
 1 AS `table_name`,
 1 AS `total_count`,
 1 AS `mock_count`,
 1 AS `real_count`,
 1 AS `mock_percentage`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_bidding_status_summary`
--

DROP TABLE IF EXISTS `v_bidding_status_summary`;
/*!50001 DROP VIEW IF EXISTS `v_bidding_status_summary`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_bidding_status_summary` AS SELECT 
 1 AS `status`,
 1 AS `total_count`,
 1 AS `mock_count`,
 1 AS `real_count`,
 1 AS `mock_percentage`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_bidding_type_summary`
--

DROP TABLE IF EXISTS `v_bidding_type_summary`;
/*!50001 DROP VIEW IF EXISTS `v_bidding_type_summary`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_bidding_type_summary` AS SELECT 
 1 AS `bidding_type`,
 1 AS `total_count`,
 1 AS `mock_count`,
 1 AS `real_count`,
 1 AS `mock_percentage`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_commission_processing_summary`
--

DROP TABLE IF EXISTS `v_commission_processing_summary`;
/*!50001 DROP VIEW IF EXISTS `v_commission_processing_summary`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_commission_processing_summary` AS SELECT 
 1 AS `month_key`,
 1 AS `processing_date`,
 1 AS `status`,
 1 AS `total_records`,
 1 AS `success_count`,
 1 AS `failure_count`,
 1 AS `started_at`,
 1 AS `completed_at`,
 1 AS `actual_confirmed_records`,
 1 AS `total_confirmed_commission`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_forwarding_requirement_data_summary`
--

DROP TABLE IF EXISTS `v_forwarding_requirement_data_summary`;
/*!50001 DROP VIEW IF EXISTS `v_forwarding_requirement_data_summary`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_forwarding_requirement_data_summary` AS SELECT 
 1 AS `table_name`,
 1 AS `total_count`,
 1 AS `mock_count`,
 1 AS `real_count`,
 1 AS `mock_percentage`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_forwarding_requirement_status_summary`
--

DROP TABLE IF EXISTS `v_forwarding_requirement_status_summary`;
/*!50001 DROP VIEW IF EXISTS `v_forwarding_requirement_status_summary`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_forwarding_requirement_status_summary` AS SELECT 
 1 AS `status`,
 1 AS `total_count`,
 1 AS `mock_count`,
 1 AS `real_count`,
 1 AS `mock_percentage`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_monthly_processing_status`
--

DROP TABLE IF EXISTS `v_monthly_processing_status`;
/*!50001 DROP VIEW IF EXISTS `v_monthly_processing_status`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_monthly_processing_status` AS SELECT 
 1 AS `month_key`,
 1 AS `pending_count`,
 1 AS `confirmed_count`,
 1 AS `paid_count`,
 1 AS `cancelled_count`,
 1 AS `total_records`,
 1 AS `confirmed_commission`,
 1 AS `paid_commission`,
 1 AS `unique_inviters`,
 1 AS `latest_record_time`,
 1 AS `processing_status`,
 1 AS `processing_started_at`,
 1 AS `processing_completed_at`,
 1 AS `processing_success_count`,
 1 AS `processing_failure_count`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_requirement_data_summary`
--

DROP TABLE IF EXISTS `v_requirement_data_summary`;
/*!50001 DROP VIEW IF EXISTS `v_requirement_data_summary`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_requirement_data_summary` AS SELECT 
 1 AS `table_name`,
 1 AS `total_count`,
 1 AS `mock_count`,
 1 AS `real_count`,
 1 AS `mock_percentage`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_requirement_status_summary`
--

DROP TABLE IF EXISTS `v_requirement_status_summary`;
/*!50001 DROP VIEW IF EXISTS `v_requirement_status_summary`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_requirement_status_summary` AS SELECT 
 1 AS `status`,
 1 AS `total_count`,
 1 AS `mock_count`,
 1 AS `real_count`,
 1 AS `mock_percentage`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_user_data_summary`
--

DROP TABLE IF EXISTS `v_user_data_summary`;
/*!50001 DROP VIEW IF EXISTS `v_user_data_summary`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_user_data_summary` AS SELECT 
 1 AS `table_name`,
 1 AS `total_count`,
 1 AS `mock_count`,
 1 AS `real_count`,
 1 AS `mock_percentage`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_user_role_summary`
--

DROP TABLE IF EXISTS `v_user_role_summary`;
/*!50001 DROP VIEW IF EXISTS `v_user_role_summary`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_user_role_summary` AS SELECT 
 1 AS `role`,
 1 AS `total_count`,
 1 AS `mock_count`,
 1 AS `real_count`,
 1 AS `mock_percentage`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `visit_deduplication_log`
--

DROP TABLE IF EXISTS `visit_deduplication_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `visit_deduplication_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '去重记录ID',
  `visitor_key` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '访问者唯一标识（用户ID或IP+UA的hash）',
  `session_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID',
  `last_visit_time` datetime NOT NULL COMMENT '最后访问时间',
  `visit_count` int unsigned NOT NULL DEFAULT '1' COMMENT '访问次数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_visitor_session` (`visitor_key`,`session_id`),
  KEY `idx_visitor_key_time` (`visitor_key`,`last_visit_time`),
  KEY `idx_last_visit_time` (`last_visit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访问去重记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `website_visit_log`
--

DROP TABLE IF EXISTS `website_visit_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `website_visit_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '访问记录ID',
  `user_id` bigint unsigned DEFAULT NULL COMMENT '用户ID（已登录用户，关联user表）',
  `session_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '前端会话ID',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址（支持IPv6）',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理字符串',
  `referer` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源页面URL',
  `page_url` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '/' COMMENT '访问页面URL',
  `visit_time` datetime NOT NULL COMMENT '访问时间',
  `is_unique_visit` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否唯一访问（去重后）',
  `visitor_type` enum('REGISTERED','ANONYMOUS') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '访问者类型',
  `client_info` json DEFAULT NULL COMMENT '客户端信息JSON（屏幕分辨率、时区等）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_visit_time` (`user_id`,`visit_time`),
  KEY `idx_ip_visit_time` (`ip_address`,`visit_time`),
  KEY `idx_visit_time` (`visit_time`),
  KEY `idx_session_time` (`session_id`,`visit_time`),
  KEY `idx_visitor_type_time` (`visitor_type`,`visit_time`),
  KEY `idx_unique_visit_time` (`is_unique_visit`,`visit_time`),
  KEY `idx_time_type_unique` (`visit_time`,`visitor_type`,`is_unique_visit`),
  KEY `idx_user_time_unique` (`user_id`,`visit_time`,`is_unique_visit`)
) ENGINE=InnoDB AUTO_INCREMENT=339 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站访问日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `withdrawal_record`
--

DROP TABLE IF EXISTS `withdrawal_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `withdrawal_record` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '提现记录ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `status` enum('pending','processing','completed','rejected') NOT NULL DEFAULT 'pending' COMMENT '状态：待处理、处理中、已完成、已拒绝',
  `account_info` varchar(255) NOT NULL COMMENT '收款账户信息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` enum('0','1') NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_withdrawal_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='提现记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Final view structure for view `v_active_bidding_data_summary`
--

/*!50001 DROP VIEW IF EXISTS `v_active_bidding_data_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_active_bidding_data_summary` AS select 'active_bidding_record' AS `table_name`,count(0) AS `total_count`,sum((case when (`bidding_record`.`data_type` = 'mock') then 1 else 0 end)) AS `mock_count`,sum((case when ((`bidding_record`.`data_type` = 'real') or (`bidding_record`.`data_type` is null)) then 1 else 0 end)) AS `real_count`,round(((sum((case when (`bidding_record`.`data_type` = 'mock') then 1 else 0 end)) * 100.0) / count(0)),1) AS `mock_percentage` from `bidding_record` where (`bidding_record`.`deleted` = '0') */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_bidding_data_summary`
--

/*!50001 DROP VIEW IF EXISTS `v_bidding_data_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_bidding_data_summary` AS select 'bidding_record' AS `table_name`,count(0) AS `total_count`,sum((case when (`bidding_record`.`data_type` = 'mock') then 1 else 0 end)) AS `mock_count`,sum((case when ((`bidding_record`.`data_type` = 'real') or (`bidding_record`.`data_type` is null)) then 1 else 0 end)) AS `real_count`,round(((sum((case when (`bidding_record`.`data_type` = 'mock') then 1 else 0 end)) * 100.0) / count(0)),1) AS `mock_percentage` from `bidding_record` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_bidding_status_summary`
--

/*!50001 DROP VIEW IF EXISTS `v_bidding_status_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_bidding_status_summary` AS select `bidding_record`.`status` AS `status`,count(0) AS `total_count`,sum((case when (`bidding_record`.`data_type` = 'mock') then 1 else 0 end)) AS `mock_count`,sum((case when ((`bidding_record`.`data_type` = 'real') or (`bidding_record`.`data_type` is null)) then 1 else 0 end)) AS `real_count`,round(((sum((case when (`bidding_record`.`data_type` = 'mock') then 1 else 0 end)) * 100.0) / count(0)),1) AS `mock_percentage` from `bidding_record` where (`bidding_record`.`deleted` = '0') group by `bidding_record`.`status` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_bidding_type_summary`
--

/*!50001 DROP VIEW IF EXISTS `v_bidding_type_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_bidding_type_summary` AS select `bidding_record`.`bidding_type` AS `bidding_type`,count(0) AS `total_count`,sum((case when (`bidding_record`.`data_type` = 'mock') then 1 else 0 end)) AS `mock_count`,sum((case when ((`bidding_record`.`data_type` = 'real') or (`bidding_record`.`data_type` is null)) then 1 else 0 end)) AS `real_count`,round(((sum((case when (`bidding_record`.`data_type` = 'mock') then 1 else 0 end)) * 100.0) / count(0)),1) AS `mock_percentage` from `bidding_record` where (`bidding_record`.`deleted` = '0') group by `bidding_record`.`bidding_type` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_commission_processing_summary`
--

/*!50001 DROP VIEW IF EXISTS `v_commission_processing_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_commission_processing_summary` AS select `cpl`.`month_key` AS `month_key`,`cpl`.`processing_date` AS `processing_date`,`cpl`.`status` AS `status`,`cpl`.`total_records` AS `total_records`,`cpl`.`success_count` AS `success_count`,`cpl`.`failure_count` AS `failure_count`,`cpl`.`started_at` AS `started_at`,`cpl`.`completed_at` AS `completed_at`,count(`cr`.`id`) AS `actual_confirmed_records`,sum((case when (`cr`.`status` = 'CONFIRMED') then `cr`.`commission_amount` else 0 end)) AS `total_confirmed_commission` from (`commission_processing_log` `cpl` left join `commission_record` `cr` on(((`cpl`.`month_key` = `cr`.`month_key`) and (`cr`.`status` = 'CONFIRMED') and (`cr`.`deleted` = '0')))) group by `cpl`.`id`,`cpl`.`month_key`,`cpl`.`processing_date`,`cpl`.`status`,`cpl`.`total_records`,`cpl`.`success_count`,`cpl`.`failure_count`,`cpl`.`started_at`,`cpl`.`completed_at` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_forwarding_requirement_data_summary`
--

/*!50001 DROP VIEW IF EXISTS `v_forwarding_requirement_data_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_forwarding_requirement_data_summary` AS select 'forwarding_requirement' AS `table_name`,count(0) AS `total_count`,sum((case when (`forwarding_requirement`.`data_type` = 'mock') then 1 else 0 end)) AS `mock_count`,sum((case when ((`forwarding_requirement`.`data_type` = 'real') or (`forwarding_requirement`.`data_type` is null)) then 1 else 0 end)) AS `real_count`,round(((sum((case when (`forwarding_requirement`.`data_type` = 'mock') then 1 else 0 end)) * 100.0) / count(0)),1) AS `mock_percentage` from `forwarding_requirement` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_forwarding_requirement_status_summary`
--

/*!50001 DROP VIEW IF EXISTS `v_forwarding_requirement_status_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_forwarding_requirement_status_summary` AS select `forwarding_requirement`.`status` AS `status`,count(0) AS `total_count`,sum((case when (`forwarding_requirement`.`data_type` = 'mock') then 1 else 0 end)) AS `mock_count`,sum((case when ((`forwarding_requirement`.`data_type` = 'real') or (`forwarding_requirement`.`data_type` is null)) then 1 else 0 end)) AS `real_count`,round(((sum((case when (`forwarding_requirement`.`data_type` = 'mock') then 1 else 0 end)) * 100.0) / count(0)),1) AS `mock_percentage` from `forwarding_requirement` group by `forwarding_requirement`.`status` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_monthly_processing_status`
--

/*!50001 DROP VIEW IF EXISTS `v_monthly_processing_status`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_monthly_processing_status` AS select `cr`.`month_key` AS `month_key`,count((case when (`cr`.`status` = 'PENDING') then 1 end)) AS `pending_count`,count((case when (`cr`.`status` = 'CONFIRMED') then 1 end)) AS `confirmed_count`,count((case when (`cr`.`status` = 'PAID') then 1 end)) AS `paid_count`,count((case when (`cr`.`status` = 'CANCELLED') then 1 end)) AS `cancelled_count`,count(0) AS `total_records`,sum((case when (`cr`.`status` = 'CONFIRMED') then `cr`.`commission_amount` else 0 end)) AS `confirmed_commission`,sum((case when (`cr`.`status` = 'PAID') then `cr`.`commission_amount` else 0 end)) AS `paid_commission`,count(distinct `cr`.`inviter_id`) AS `unique_inviters`,max(`cr`.`created_at`) AS `latest_record_time`,`cpl`.`status` AS `processing_status`,`cpl`.`started_at` AS `processing_started_at`,`cpl`.`completed_at` AS `processing_completed_at`,`cpl`.`success_count` AS `processing_success_count`,`cpl`.`failure_count` AS `processing_failure_count` from (`commission_record` `cr` left join `commission_processing_log` `cpl` on((`cr`.`month_key` = `cpl`.`month_key`))) where (`cr`.`deleted` = 0) group by `cr`.`month_key`,`cpl`.`status`,`cpl`.`started_at`,`cpl`.`completed_at`,`cpl`.`success_count`,`cpl`.`failure_count` order by `cr`.`month_key` desc */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_requirement_data_summary`
--

/*!50001 DROP VIEW IF EXISTS `v_requirement_data_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_requirement_data_summary` AS select 'purchase_requirement' AS `table_name`,count(0) AS `total_count`,sum((case when (`purchase_requirement`.`data_type` = 'mock') then 1 else 0 end)) AS `mock_count`,sum((case when ((`purchase_requirement`.`data_type` = 'real') or (`purchase_requirement`.`data_type` is null)) then 1 else 0 end)) AS `real_count`,round(((sum((case when (`purchase_requirement`.`data_type` = 'mock') then 1 else 0 end)) * 100.0) / count(0)),1) AS `mock_percentage` from `purchase_requirement` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_requirement_status_summary`
--

/*!50001 DROP VIEW IF EXISTS `v_requirement_status_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_requirement_status_summary` AS select `purchase_requirement`.`status` AS `status`,count(0) AS `total_count`,sum((case when (`purchase_requirement`.`data_type` = 'mock') then 1 else 0 end)) AS `mock_count`,sum((case when ((`purchase_requirement`.`data_type` = 'real') or (`purchase_requirement`.`data_type` is null)) then 1 else 0 end)) AS `real_count`,round(((sum((case when (`purchase_requirement`.`data_type` = 'mock') then 1 else 0 end)) * 100.0) / count(0)),1) AS `mock_percentage` from `purchase_requirement` group by `purchase_requirement`.`status` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_user_data_summary`
--

/*!50001 DROP VIEW IF EXISTS `v_user_data_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_user_data_summary` AS select 'user' AS `table_name`,count(0) AS `total_count`,sum((case when (`user`.`data_type` = 'mock') then 1 else 0 end)) AS `mock_count`,sum((case when ((`user`.`data_type` = 'real') or (`user`.`data_type` is null)) then 1 else 0 end)) AS `real_count`,round(((sum((case when (`user`.`data_type` = 'mock') then 1 else 0 end)) * 100.0) / count(0)),1) AS `mock_percentage` from `user` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_user_role_summary`
--

/*!50001 DROP VIEW IF EXISTS `v_user_role_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_user_role_summary` AS select `user`.`role` AS `role`,count(0) AS `total_count`,sum((case when (`user`.`data_type` = 'mock') then 1 else 0 end)) AS `mock_count`,sum((case when ((`user`.`data_type` = 'real') or (`user`.`data_type` is null)) then 1 else 0 end)) AS `real_count`,round(((sum((case when (`user`.`data_type` = 'mock') then 1 else 0 end)) * 100.0) / count(0)),1) AS `mock_percentage` from `user` group by `user`.`role` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-29 17:23:54
