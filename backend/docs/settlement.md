## settlement 模块文档

### 1. 模块概述
财务结算模块，负责处理交易款项的结算、对账和资金划转功能，支持多级结算流程和自动化对账

### 2. 核心类说明
#### Controller类
- **SettlementController**:
  - 功能: 提供结算管理的REST API接口
  - 主要接口:
    - `POST /settlements`: 创建结算单
    - `GET /settlements/{id}`: 查询结算单详情
    - `PUT /settlements/{id}/status`: 更新结算状态
    - `GET /settlements/report`: 生成结算报表

#### Service类
- **SettlementService**:
  - 核心方法:
    - `createSettlement`: 生成结算单
    - `processPayment`: 执行资金划转
    - `generateSettlementReport`: 生成结算报表
    - `reconcileAccounts`: 执行对账操作

#### Repository类
- **SettlementRepository**:
  - 核心查询方法:
    - `findByPeriod`: 按结算周期查询
    - `findByStatus`: 按状态查询
    - `sumAmountByStatus`: 按状态汇总金额

#### Entity类
- **Settlement**:
  - 主要字段说明:
    - `id`: 结算单ID
    - `orderIds`: 关联订单ID列表
    - `totalAmount`: 结算总金额
    - `status`: 结算状态(PENDING/COMPLETED/FAILED)
    - `settlementDate`: 结算日期
    - `paymentProof`: 付款凭证

### 3. 使用示例
```java
// 创建结算单示例
SettlementDTO dto = new SettlementDTO();
dto.setOrderIds(Arrays.asList(1001L, 1002L));
dto.setSettlementDate(LocalDate.now());
Settlement settlement = settlementService.createSettlement(dto);

// 执行结算示例
settlementService.processPayment(settlement.getId());

// 生成报表示例
SettlementReport report = settlementService.generateSettlementReport(
    LocalDate.now().minusMonths(1),
    LocalDate.now()
);
```

### 4. 业务规则
- 结算周期配置:
  ```yaml
  settlement:
    cycle: DAILY # 支持DAILY/WEEKLY/MONTHLY
    cutoff-time: "18:00" # 当日结算截止时间
  ```
- 支持多级结算流程(商户→平台→供应商)
- 自动对账异常处理机制

### 5. 注意事项
- 结算单生成后需财务审核
- 支持手动冲正操作
- 提供完整的审计日志
- 与支付系统对接需配置白名单
- 生产环境需启用双重验证