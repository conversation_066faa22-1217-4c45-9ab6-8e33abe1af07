# 佣金系统技术规格说明书

## 1. 系统架构

### 1.1 分层架构设计
```
┌─────────────────────────────────────────┐
│              接口层 (Interfaces)          │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  Web API    │  │  Event Listener │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              应用层 (Application)         │
│  ┌─────────────────────────────────────┐ │
│  │  CommissionApplicationService      │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│               领域层 (Domain)             │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │   Entity    │  │     Service     │   │
│  │   VO        │  │   Repository    │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│            基础设施层 (Infrastructure)     │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  Database   │  │     Config      │   │
│  │   Mapper    │  │    Message      │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

### 1.2 核心组件

#### 1.2.1 领域实体 (Domain Entities)
- **CommissionRecord**: 佣金记录聚合根
- **InviterCoupon**: 邀请人优惠券聚合根
- **MonthlyCommissionSummary**: 月度佣金汇总

#### 1.2.2 值对象 (Value Objects)
- **Money**: 金额值对象，确保精度和货币单位
- **CommissionRate**: 佣金比例值对象
- **MonthKey**: 月份键值对象

#### 1.2.3 领域服务 (Domain Services)
- **CommissionCalculationService**: 佣金计算核心逻辑
- **InviterCouponService**: 优惠券生成和管理逻辑

## 2. API接口规格

### 2.1 事件接口

#### 2.1.1 OrderCompletedEvent
```java
public class CommissionCalculationEvent {
    private Long orderId;           // 订单ID
    private Long buyerId;           // 买家ID
    private Money orderAmount;      // 订单金额
    private String orderType;       // 订单类型
    private LocalDateTime completedDate; // 完成时间
}
```

**事件触发条件**:
- 买家确认收货
- 订单状态变更为"completed"
- 系统自动发布事件

**事件处理流程**:
1. 验证订单佣金资格
2. 查询买家邀请关系
3. 根据邀请人角色执行激励策略
4. 创建佣金记录或生成优惠券

### 2.2 REST API接口

#### 2.2.1 佣金记录管理API

**查询佣金记录**
```http
GET /api/commission/records
Parameters:
- inviterId: Long (邀请人ID)
- monthKey: String (月份键，格式：YYYY-MM)
- status: String (记录状态)
- page: Integer (页码，默认0)
- size: Integer (页大小，默认20)

Response:
{
  "content": [
    {
      "id": 1,
      "inviterId": 123,
      "inviteeId": 456,
      "orderId": 789,
      "orderType": "PURCHASE",
      "orderAmount": "1000.00",
      "commissionRate": "0.012",
      "commissionAmount": "12.00",
      "status": "CONFIRMED",
      "monthKey": "2024-03",
      "createdDate": "2024-03-15T10:30:00"
    }
  ],
  "totalElements": 50,
  "totalPages": 3,
  "size": 20,
  "number": 0
}
```

**确认佣金记录**
```http
POST /api/commission/records/{id}/confirm
Response:
{
  "success": true,
  "message": "佣金记录确认成功",
  "data": {
    "id": 1,
    "status": "CONFIRMED",
    "confirmedDate": "2024-03-16T09:15:00"
  }
}
```

**取消佣金记录**
```http
POST /api/commission/records/{id}/cancel
Request Body:
{
  "reason": "订单退款"
}

Response:
{
  "success": true,
  "message": "佣金记录取消成功"
}
```

#### 2.2.2 月度汇总API

**获取月度汇总**
```http
GET /api/commission/monthly-summary
Parameters:
- inviterId: Long (邀请人ID)
- monthKey: String (月份键)

Response:
{
  "inviterId": 123,
  "monthKey": "2024-03",
  "totalOrderAmount": "15000.00",
  "totalCommissionAmount": "180.00",
  "monthlyBonus": "100.00",
  "totalEarnings": "280.00",
  "recordCount": 15,
  "status": "CALCULATED"
}
```

#### 2.2.3 优惠券管理API

**查询优惠券列表**
```http
GET /api/coupons
Parameters:
- inviterId: Long (邀请人ID)
- status: String (优惠券状态)
- page: Integer
- size: Integer

Response:
{
  "content": [
    {
      "id": 1,
      "couponCode": "INV-123-1640995200000-A1B2C3D4",
      "inviterId": 123,
      "discountValue": "50.00",
      "minimumAmount": "500.00",
      "couponType": "FIXED_AMOUNT",
      "status": "ACTIVE",
      "validFrom": "2024-03-15T00:00:00",
      "validUntil": "2024-06-15T23:59:59"
    }
  ]
}
```

**验证优惠券**
```http
POST /api/coupons/validate
Request Body:
{
  "couponCode": "INV-123-1640995200000-A1B2C3D4",
  "orderAmount": "600.00",
  "userId": 123
}

Response:
{
  "valid": true,
  "discountAmount": "50.00",
  "message": "优惠券验证成功"
}
```

## 3. 数据模型规格

### 3.1 数据库表结构

#### 3.1.1 佣金记录表 (commission_records)
```sql
CREATE TABLE commission_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    inviter_id BIGINT NOT NULL COMMENT '邀请人ID',
    invitee_id BIGINT NOT NULL COMMENT '被邀请者ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    order_type VARCHAR(20) NOT NULL COMMENT '订单类型',
    order_amount DECIMAL(15,2) NOT NULL COMMENT '订单金额',
    commission_rate DECIMAL(6,4) NOT NULL COMMENT '佣金比例',
    commission_amount DECIMAL(15,2) NOT NULL COMMENT '佣金金额',
    status VARCHAR(20) NOT NULL COMMENT '状态',
    month_key VARCHAR(7) NOT NULL COMMENT '月份键',
    created_date DATETIME NOT NULL COMMENT '创建时间',
    confirmed_date DATETIME COMMENT '确认时间',
    paid_date DATETIME COMMENT '支付时间',
    
    UNIQUE KEY uk_order_inviter (order_id, inviter_id),
    INDEX idx_inviter_month (inviter_id, month_key),
    INDEX idx_status (status),
    INDEX idx_created_date (created_date)
);
```

#### 3.1.2 优惠券表 (inviter_coupons)
```sql
CREATE TABLE inviter_coupons (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    coupon_code VARCHAR(50) NOT NULL UNIQUE COMMENT '优惠券代码',
    inviter_id BIGINT NOT NULL COMMENT '邀请人ID',
    invitee_id BIGINT NOT NULL COMMENT '被邀请者ID',
    trigger_order_id BIGINT NOT NULL COMMENT '触发订单ID',
    discount_value DECIMAL(10,2) NOT NULL COMMENT '优惠金额',
    minimum_amount DECIMAL(10,2) COMMENT '最低使用金额',
    maximum_discount DECIMAL(10,2) COMMENT '最大优惠金额',
    coupon_type VARCHAR(20) NOT NULL COMMENT '优惠券类型',
    status VARCHAR(20) NOT NULL COMMENT '状态',
    month_key VARCHAR(7) NOT NULL COMMENT '月份键',
    valid_from DATETIME NOT NULL COMMENT '生效时间',
    valid_until DATETIME NOT NULL COMMENT '失效时间',
    used_date DATETIME COMMENT '使用时间',
    used_order_id BIGINT COMMENT '使用订单ID',
    created_date DATETIME NOT NULL COMMENT '创建时间',
    
    INDEX idx_inviter_id (inviter_id),
    INDEX idx_status (status),
    INDEX idx_valid_period (valid_from, valid_until),
    INDEX idx_month_key (month_key)
);
```

#### 3.1.3 月度汇总表 (monthly_commission_summaries)
```sql
CREATE TABLE monthly_commission_summaries (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    inviter_id BIGINT NOT NULL COMMENT '邀请人ID',
    month_key VARCHAR(7) NOT NULL COMMENT '月份键',
    total_order_amount DECIMAL(15,2) NOT NULL COMMENT '总订单金额',
    total_commission_amount DECIMAL(15,2) NOT NULL COMMENT '总佣金金额',
    monthly_bonus DECIMAL(10,2) NOT NULL COMMENT '月度奖金',
    total_earnings DECIMAL(15,2) NOT NULL COMMENT '总收益',
    record_count INT NOT NULL COMMENT '记录数量',
    status VARCHAR(20) NOT NULL COMMENT '状态',
    calculated_date DATETIME NOT NULL COMMENT '计算时间',
    settled_date DATETIME COMMENT '结算时间',
    
    UNIQUE KEY uk_inviter_month (inviter_id, month_key),
    INDEX idx_month_key (month_key),
    INDEX idx_status (status)
);
```

### 3.2 领域对象映射

#### 3.2.1 CommissionRecord实体
```java
@Entity
@Table(name = "commission_records")
public class CommissionRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "inviter_id", nullable = false)
    private Long inviterId;
    
    @Column(name = "invitee_id", nullable = false)
    private Long inviteeId;
    
    @Column(name = "order_id", nullable = false)
    private Long orderId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "order_type", nullable = false)
    private OrderType orderType;
    
    @Embedded
    @AttributeOverride(name = "amount", column = @Column(name = "order_amount"))
    private Money orderAmount;
    
    @Embedded
    @AttributeOverride(name = "rate", column = @Column(name = "commission_rate"))
    private CommissionRate commissionRate;
    
    @Embedded
    @AttributeOverride(name = "amount", column = @Column(name = "commission_amount"))
    private Money commissionAmount;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status;
    
    @Embedded
    @AttributeOverride(name = "value", column = @Column(name = "month_key"))
    private MonthKey monthKey;
    
    // 业务方法
    public void confirm() { /* 确认佣金 */ }
    public void cancel() { /* 取消佣金 */ }
    public void markAsPaid() { /* 标记已支付 */ }
}
```

## 4. 业务规则引擎

### 4.1 佣金计算规则配置
```java
@Component
public class CommissionRuleEngine {
    
    // 佣金阶梯配置
    private static final List<CommissionTier> COMMISSION_TIERS = Arrays.asList(
        new CommissionTier(Money.of("0"), Money.of("7000"), CommissionRate.of("0.009")),
        new CommissionTier(Money.of("7000.01"), Money.of("15000"), CommissionRate.of("0.010")),
        // ... 其他阶梯
    );
    
    // 奖金阶梯配置
    private static final List<BonusTier> BONUS_TIERS = Arrays.asList(
        new BonusTier(Money.of("7000.01"), Money.of("15000"), Money.of("50")),
        new BonusTier(Money.of("15000.01"), Money.of("30000"), Money.of("100")),
        // ... 其他阶梯
    );
    
    public CommissionRate calculateRate(Money monthlyAmount) {
        return COMMISSION_TIERS.stream()
            .filter(tier -> tier.matches(monthlyAmount))
            .findFirst()
            .map(CommissionTier::getRate)
            .orElse(CommissionRate.of("0.009"));
    }
    
    public Money calculateBonus(Money monthlyAmount) {
        return BONUS_TIERS.stream()
            .filter(tier -> tier.qualifies(monthlyAmount))
            .findFirst()
            .map(BonusTier::getBonusAmount)
            .orElse(Money.zero());
    }
}
```

### 4.2 优惠券生成规则
```java
@Component
public class CouponGenerationRule {
    
    private static final Map<String, Double> BASE_RATES = Map.of(
        "purchase", 0.005,
        "logistics", 0.003,
        "default", 0.002
    );
    
    private static final Map<Money, Double> AMOUNT_MULTIPLIERS = Map.of(
        Money.of("10000"), 1.1,
        Money.of("20000"), 1.2,
        Money.of("50000"), 1.3,
        Money.of("100000"), 1.5
    );
    
    public Money calculateCouponValue(Money orderAmount, String orderType) {
        double baseRate = BASE_RATES.getOrDefault(orderType.toLowerCase(), 
                                                 BASE_RATES.get("default"));
        double multiplier = getAmountMultiplier(orderAmount);
        
        Money calculatedValue = orderAmount.multiply(baseRate * multiplier);
        
        // 应用最小最大值限制
        Money minValue = Money.of("10.00");
        Money maxValue = Money.of("500.00");
        
        if (calculatedValue.lessThan(minValue)) return minValue;
        if (calculatedValue.greaterThan(maxValue)) return maxValue;
        
        return calculatedValue;
    }
}
```

## 5. 异常处理规格

### 5.1 业务异常定义
```java
public class CommissionBusinessException extends RuntimeException {
    private final String errorCode;
    private final String errorMessage;
    
    public static final String INVALID_ORDER = "COMM_001";
    public static final String DUPLICATE_PROCESSING = "COMM_002";
    public static final String INSUFFICIENT_AMOUNT = "COMM_003";
    public static final String EXPIRED_COUPON = "COMM_004";
}
```

### 5.2 异常处理策略
- **业务异常**: 记录日志，返回错误信息给用户
- **系统异常**: 记录详细日志，触发告警，返回通用错误信息
- **数据异常**: 事务回滚，记录异常数据，人工介入处理

---

**文档版本**: 1.0  
**创建日期**: 2025-07-06  
**最后更新**: 2025-07-06  
**文档状态**: 正式版
