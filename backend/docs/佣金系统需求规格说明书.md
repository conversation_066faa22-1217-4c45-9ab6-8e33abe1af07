# 佣金系统需求规格说明书

## 1. 项目概述

### 1.1 项目背景
采购系统佣金模块是基于事件驱动架构和DDD（领域驱动设计）的核心业务模块，负责处理邀请人佣金计算、优惠券生成和月度结算等功能。

### 1.2 项目目标
- 实现精确的阶梯式佣金计算机制
- 支持角色差异化激励策略
- 提供完整的佣金管理和结算功能
- 确保系统的高可用性和数据一致性

### 1.3 适用范围
本文档适用于采购系统中所有涉及佣金计算、优惠券管理和激励分发的业务场景。

## 2. 业务需求

### 2.1 核心业务流程

#### 2.1.1 事件驱动佣金计算
**触发条件**: 买家确认收货完成订单
**处理流程**:
1. 系统发布`OrderCompletedEvent`事件
2. 佣金计算服务异步监听事件
3. 验证订单佣金资格
4. 根据邀请人角色执行不同激励策略
5. 创建佣金记录或生成优惠券

#### 2.1.2 角色差异化激励策略
**卖家/货代邀请人**: 获得现金佣金
- 计算月度累计金额
- 应用阶梯式佣金比例
- 生成佣金记录
- 计算月度奖金

**买家邀请人**: 获得优惠券
- 根据订单金额计算优惠券价值
- 生成唯一优惠券代码
- 设置使用条件和有效期

### 2.2 佣金计算规则

#### 2.2.1 阶梯式佣金比例
| 月度累计金额范围 | 佣金比例 |
|------------------|----------|
| $0 - $7,000 | 0.90% |
| $7,000.01 - $15,000 | 1.00% |
| $15,000.01 - $20,000 | 1.10% |
| $20,000.01 - $40,000 | 1.20% |
| $40,000.01 - $70,000 | 1.30% |
| $70,000.01以上 | 1.40% |

#### 2.2.2 月度奖金机制
| 月度累计金额范围 | 奖金金额 |
|------------------|----------|
| $0 - $7,000 | $0 |
| $7,000.01 - $15,000 | $50 |
| $15,000.01 - $30,000 | $100 |
| $30,000.01 - $50,000 | $200 |
| $50,000.01 - $80,000 | $350 |
| $80,000.01 - $120,000 | $550 |
| $120,000.01 - $150,000 | $750 |
| $150,000.01以上 | $900 |

### 2.3 优惠券生成规则

#### 2.3.1 优惠券价值计算
**基础比例**:
- 采购订单: 0.5%
- 物流订单: 0.3%
- 其他订单: 0.2%

**金额阶梯调整**:
- $10,000以下: 基础比例 × 1.0
- $10,000以上: 基础比例 × 1.1
- $20,000以上: 基础比例 × 1.2
- $50,000以上: 基础比例 × 1.3
- $100,000以上: 基础比例 × 1.5

**价值限制**:
- 最小优惠券价值: $10
- 最大优惠券价值: $500
- 最低使用金额: 优惠券价值 × 10

#### 2.3.2 优惠券属性
- **代码格式**: INV-{邀请人ID}-{时间戳}-{随机码}
- **有效期**: 生成后3个月
- **使用条件**: 订单金额需达到最低使用金额
- **使用限制**: 每张优惠券仅可使用一次

## 3. 功能需求

### 3.1 佣金计算服务

#### 3.1.1 订单验证功能
**功能描述**: 验证订单是否符合佣金计算条件
**验证规则**:
- 订单状态必须为"completed"
- 买家必须是被邀请用户
- 订单类型必须为"purchase"或"logistics"
- 排除样品订单（订单类型包含"sample"）

#### 3.1.2 佣金比例计算
**功能描述**: 根据月度累计金额计算适用的佣金比例
**输入参数**: 月度累计金额
**输出结果**: 佣金比例对象
**计算逻辑**: 按阶梯表查找对应比例

#### 3.1.3 月度奖金计算
**功能描述**: 根据月度累计金额计算奖金
**输入参数**: 月度累计金额
**输出结果**: 奖金金额
**计算逻辑**: 按奖金阶梯表查找对应金额

### 3.2 优惠券管理服务

#### 3.2.1 优惠券生成功能
**功能描述**: 为买家邀请人生成优惠券
**输入参数**:
- 邀请人ID
- 被邀请者ID
- 触发订单ID
- 订单金额
- 订单类型
- 月份键

**输出结果**: 优惠券实体对象

#### 3.2.2 优惠券验证功能
**功能描述**: 验证优惠券使用条件
**验证规则**:
- 优惠券状态为有效
- 当前时间在有效期内
- 使用者为优惠券所有者
- 订单金额满足最低使用要求

### 3.3 佣金记录管理

#### 3.3.1 佣金记录创建
**功能描述**: 创建新的佣金记录
**必填字段**:
- 邀请人ID
- 被邀请者ID
- 订单ID
- 订单类型
- 订单金额
- 佣金比例
- 月份键

#### 3.3.2 佣金记录状态管理
**状态流转**:
- PENDING（待确认）→ CONFIRMED（已确认）
- CONFIRMED（已确认）→ PAID（已支付）
- PENDING/CONFIRMED → CANCELLED（已取消）

**业务规则**:
- 只有待确认状态的记录可以确认
- 只有已确认状态的记录可以标记为已支付
- 已支付的记录不能取消或重新计算

## 4. 非功能需求

### 4.1 性能需求
- 佣金计算响应时间 < 100ms
- 优惠券生成响应时间 < 50ms
- 支持并发处理1000个订单事件

### 4.2 可靠性需求
- 系统可用性 ≥ 99.9%
- 数据一致性保证
- 异常情况下的事务回滚

### 4.3 安全性需求
- 佣金数据加密存储
- 操作日志完整记录
- 权限控制和访问审计

### 4.4 可维护性需求
- 代码测试覆盖率 ≥ 90%
- 完整的API文档
- 详细的错误日志

## 5. 接口需求

### 5.1 事件接口
- `OrderCompletedEvent`: 订单完成事件
- `CommissionCalculationEvent`: 佣金计算事件

### 5.2 REST API接口
- 佣金记录查询接口
- 佣金统计接口
- 优惠券管理接口
- 月度结算接口

### 5.3 数据库接口
- 佣金记录表操作
- 优惠券表操作
- 佣金配置表操作
- 月度汇总表操作

## 6. 数据需求

### 6.1 核心实体
- **CommissionRecord**: 佣金记录
- **InviterCoupon**: 邀请人优惠券
- **MonthlyCommissionSummary**: 月度佣金汇总
- **CommissionConfig**: 佣金配置

### 6.2 值对象
- **Money**: 金额对象
- **CommissionRate**: 佣金比例
- **MonthKey**: 月份键

### 6.3 数据完整性
- 外键约束保证数据关联性
- 唯一性约束防止重复处理
- 检查约束保证数据有效性

## 7. 约束条件

### 7.1 业务约束
- 每个订单只能产生一次佣金
- 优惠券代码全局唯一
- 月度结算按自然月进行

### 7.2 技术约束
- 使用Java 17+
- 基于Spring Boot框架
- 使用MySQL数据库
- 遵循DDD架构模式

### 7.3 合规约束
- 符合财务审计要求
- 满足数据保护法规
- 遵循行业最佳实践

## 8. 测试需求

### 8.1 单元测试要求

#### 8.1.1 领域服务测试
**CommissionCalculationService测试覆盖**:
- 佣金比例计算的所有阶梯边界值
- 月度奖金计算的完整场景
- 订单验证逻辑的各种情况
- 异常参数处理和错误消息验证

**InviterCouponService测试覆盖**:
- 优惠券生成的完整流程
- 价值计算的各种订单类型和金额
- 代码生成的格式和唯一性
- 验证逻辑的边界条件

#### 8.1.2 实体测试
**CommissionRecord测试覆盖**:
- 实体创建和属性验证
- 状态流转的业务规则
- 不变性约束检查

**InviterCoupon测试覆盖**:
- 优惠券创建和配置
- 使用条件验证
- 状态管理和过期检查

### 8.2 集成测试要求

#### 8.2.1 事件驱动测试
- 订单完成事件的端到端处理
- 异步事件处理的时序验证
- 事件失败时的重试机制

#### 8.2.2 数据库集成测试
- 事务一致性验证
- 并发操作的数据完整性
- 外键约束和级联操作

### 8.3 性能测试要求
- 大批量订单处理的性能基准
- 并发佣金计算的吞吐量测试
- 数据库查询优化验证

## 9. 部署需求

### 9.1 环境要求
- **开发环境**: 本地开发和单元测试
- **测试环境**: 集成测试和性能测试
- **生产环境**: 正式业务运行

### 9.2 配置管理
- 佣金比例配置的动态调整
- 奖金阶梯的灵活配置
- 优惠券规则的参数化设置

### 9.3 监控告警
- 佣金计算异常监控
- 系统性能指标监控
- 业务数据异常告警

## 10. 风险评估

### 10.1 技术风险
- **数据一致性风险**: 通过事务管理和幂等性设计降低
- **性能瓶颈风险**: 通过缓存和异步处理优化
- **系统可用性风险**: 通过容错设计和监控告警保障

### 10.2 业务风险
- **计算错误风险**: 通过完整的测试覆盖和验证机制
- **重复处理风险**: 通过唯一性约束和幂等性检查
- **数据泄露风险**: 通过权限控制和数据加密

## 11. 验收标准

### 11.1 功能验收
- [ ] 所有佣金计算规则正确实现
- [ ] 优惠券生成和管理功能完整
- [ ] 事件驱动架构正常运行
- [ ] 角色差异化激励策略生效

### 11.2 质量验收
- [ ] 单元测试覆盖率 ≥ 90%
- [ ] 集成测试全部通过
- [ ] 性能测试达到预期指标
- [ ] 安全测试无重大漏洞

### 11.3 文档验收
- [ ] API文档完整准确
- [ ] 用户手册清晰易懂
- [ ] 运维文档详细可操作
- [ ] 测试报告完整规范

## 12. 附录

### 12.1 术语定义
- **邀请人**: 推荐其他用户注册的用户
- **被邀请者**: 通过邀请链接注册的用户
- **佣金**: 邀请人因被邀请者订单获得的现金奖励
- **优惠券**: 买家邀请人获得的消费抵扣券
- **月度结算**: 按自然月统计和发放佣金的过程

### 12.2 参考文档
- [Commission事件监听与佣金计算机制详解.md](./Commission事件监听与佣金计算机制详解.md)
- [佣金模块测试总结.md](../佣金模块测试总结.md)
- [Commission_PostmanTests_README.md](../Commission_PostmanTests_README.md)

### 12.3 变更记录
| 版本 | 日期 | 变更内容 | 变更人 |
|------|------|----------|--------|
| 1.0 | 2025-07-06 | 初始版本创建 | 系统分析师 |

---

**文档版本**: 1.0
**创建日期**: 2025-07-06
**最后更新**: 2025-07-06
**文档状态**: 正式版
