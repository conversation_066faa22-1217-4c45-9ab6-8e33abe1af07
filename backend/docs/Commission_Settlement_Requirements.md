# Commission Settlement 结算单据功能需求文档

  

## 📋 项目概述

  

### 项目背景

当前佣金系统通过 `MonthlyCommissionSummary` 实现了基础的结算状态管理，但缺乏正式的结算单据功能。为了满足财务合规要求、提供完整的审计轨迹，以及支持管理员手动结算流程，需要实现基于 `commission_settlement` 表的正式结算单据功能。

  

**当前痛点**：

- 缺乏正式的结算凭证和记录

- 管理员无法有效跟踪结算进度

- 缺少完整的结算审计轨迹

- 无法支持多种结算方式的管理

  

### 项目目标

- 🎯 建立正式的结算单据体系，支持管理员手动结算流程

- 📝 提供完整的结算凭证和审计轨迹

- 💰 支持多种结算方式的记录和管理（银行转账、支付宝、微信等）

- 🔍 实现完整的财务对账功能

- 📊 为管理员提供详细的结算管理界面和报表

- 👨‍💼 支持管理员手动确认和处理每笔结算

  

## 🎯 功能需求

  

### 1. 核心业务需求

  

#### 1.1 结算单据生成

**需求描述**：基于月度汇总数据生成正式的结算单据，供管理员手动处理

  

**功能要点**：

- 管理员可以选择 `MonthlyCommissionSummary` 状态为 `PENDING` 的记录生成结算单据

- 自动生成唯一的结算编号（格式：SETTLE-YYYYMM-序号）

- 包含完整的结算金额明细（佣金+奖金）

- 支持批量生成和单个生成两种模式

- 生成前提供结算预览功能，管理员确认后生成

- **强制要求**：生成结算单据时必须上传转账回执，否则不能生成

  

**业务规则**：

```

触发条件：

- 管理员手动选择待结算记录

- MonthlyCommissionSummary.status = PENDING

- MonthlyCommissionSummary.totalAmount > 0

- 月度数据已确认无误

- 必须上传至少一个转账回执文件

  

生成逻辑：

- 结算编号：SETTLE-202403-001（年月+序号）

- 结算金额：佣金总额 + 月度奖金

- 默认结算方式：bank_transfer（可修改）

- 初始状态：COMPLETED（因为已经有转账回执）

- 创建人：当前管理员

- 转账回执：必须字段，不能为空

```

  

#### 1.2 结算方式管理

**需求描述**：支持多种结算方式的记录和管理，便于管理员选择合适的支付方式

  

**支持的结算方式**：

- `bank_transfer` - 银行转账（默认，推荐）

- `alipay` - 支付宝转账

- `wechat` - 微信转账

- `paypal` - PayPal转账（国际用户）

- `manual` - 线下手动支付（现金、支票等）

  

**账户信息管理**：

- 银行转账：银行名称、账户名、账号、开户行

- 支付宝：支付宝账号、真实姓名

- 微信：微信号、真实姓名

- PayPal：PayPal邮箱

- 线下支付：收款人姓名、联系方式、备注信息

  

**管理员操作**：

- 可以在生成结算单据时选择结算方式

- 可以修改待处理结算单据的结算方式

- 可以添加结算备注和说明

  

#### 1.3 结算状态流转

**需求描述**：完整的结算状态管理，确保结算流程可控

  

**状态定义**：

```java

public enum SettlementStatus {

    COMPLETED("已完成");    // 结算单据生成时即为已完成（因为已有转账回执）

    // 注：由于生成时必须有转账回执，所以直接为完成状态

    // 如需要其他状态可后续扩展

}

```

  

**状态流转规则**：

```

生成结算单据 → COMPLETED：生成时已有转账回执，直接完成

  

简化流程说明：

- 管理员先线下完成转账操作

- 获得转账回执文件

- 在系统中生成结算单据时上传回执

- 结算单据生成后即为完成状态

- 无需复杂的状态流转

  

管理员操作权限：

- 只有管理员可以生成结算单据

- 生成时需要记录操作人和操作时间

- 生成时需要填写备注说明

```

  

### 2. 数据管理需求

  

#### 2.1 结算记录查询

**需求描述**：提供灵活的结算记录查询功能

  

**查询维度**：

- 按邀请人ID查询

- 按月份查询

- 按结算状态查询

- 按结算方式查询

- 按时间范围查询

- 按金额范围查询

- 按处理人查询

- 按是否有回执文件查询

  

**排序和分页**：

- 支持按创建时间、结算时间、金额排序

- 支持分页查询，默认每页20条

  

#### 2.2 转账回执文件管理

**需求描述**：管理结算单据中的转账回执文件

  

**文件上传功能**：

- **文件上传组件集成**：

  - 调用现有的文件上传组件

  - 文件上传到OSS后获得URL字符串

  - 支持上传多个回执文件（每个结算单据最多5个）

- **文件验证**：

  - 支持的文件格式：PDF、JPG、PNG、JPEG

  - 文件大小限制：单个文件最大10MB

  - 文件上传成功验证

- **回执信息管理**：

  - 每个文件必须有描述信息（如"银行转账回执"、"支付宝截图"）

  - 记录文件上传时间和上传人

  - 支持预览已上传的文件

  - 支持删除和重新上传文件（仅在生成单据过程中）

  

**数据存储格式**：

- 使用JSON格式存储在数据库中

- 包含文件URL、文件名、描述、上传时间、上传人等信息

  

#### 2.2 结算统计分析

**需求描述**：提供结算数据的统计分析功能

  

**统计指标**：

- 月度结算总金额

- 各结算方式占比

- 结算成功率

- 平均结算时长

- 待处理结算数量

  

### 3. 业务流程需求

  

#### 3.1 管理员结算流程

**需求描述**：基于现有的月度处理机制，为管理员提供结算管理界面

  

**流程设计**：

```

月度处理定时任务（保持现有逻辑）

    ↓

生成 MonthlyCommissionSummary（状态：PENDING）

    ↓

管理员登录结算管理界面

    ↓

查看待结算列表

    ↓

选择需要结算的记录

    ↓

管理员线下执行支付操作

    ↓

管理员获得转账回执文件

    ↓

管理员在系统中生成结算单据：

    - 选择结算方式和填写账户信息

    - 调用文件上传组件上传转账回执

    - 填写结算备注

    - 确认生成（状态直接为COMPLETED）

    ↓

系统更新相关状态并发送通知

```

  

#### 3.2 结算管理界面需求

**需求描述**：为管理员提供完整的结算管理界面

  

**界面功能要求**：

  

**3.2.1 待结算列表页面**

- 显示所有 `PENDING` 状态的月度汇总

- 支持按邀请人、月份、金额范围筛选

- 显示邀请人信息、月份、佣金金额、奖金、总金额

- 支持批量选择和单个选择

- 提供"生成结算单据"按钮

  

**3.2.2 结算单据生成页面**

- 显示选中的月度汇总详细信息

- 提供结算方式选择和账户信息填写

- **转账回执上传功能**（必填）：

  - 调用现有文件上传组件

  - 支持上传PDF、JPG、PNG格式文件

  - 每个文件需要填写描述信息

  - 支持上传多个回执文件（最多5个）

  - 必须至少上传一个文件才能继续

- 填写结算备注（必填）

- 填写支付参考号（必填）

- 确认生成结算单据

  

**3.2.3 结算单据管理页面**

- 显示所有已生成的结算单据列表

- 支持按结算方式、时间范围、邀请人筛选

- 显示结算编号、邀请人、金额、创建时间、处理人

- 支持查看详情、预览回执文件

- 显示结算单据详细信息和回执文件列表

  

**操作流程**：

```

管理员查看待结算列表

    ↓

选择需要结算的记录

    ↓

线下执行支付操作（银行转账、支付宝等）

    ↓

获得转账回执文件

    ↓

点击"生成结算单据"

    ↓

填写结算方式和账户信息

    ↓

上传转账回执文件（必填，调用文件上传组件）

    ↓

填写结算备注和支付参考号

    ↓

确认生成结算单据（状态直接为COMPLETED）

    ↓

在结算管理页面查看生成的单据

    ↓

系统自动更新相关状态

```

  

### 4. 集成需求

  

#### 4.1 与现有系统集成

**需求描述**：与当前的佣金系统无缝集成

  

**集成点**：

- `MonthlyCommissionProcessor` - 月度处理器集成

- `CommissionApplicationService` - 应用服务扩展

- `CommissionNotificationAdapter` - 通知服务集成

  

**数据同步**：

- 结算完成后同步更新 `MonthlyCommissionSummary` 状态

- 同步更新相关 `CommissionRecord` 的支付状态

- 保持数据一致性

  

#### 4.2 用户信息集成

**需求描述**：集成用户系统获取收款账户信息

  

**集成内容**：

- 用户基本信息（姓名、联系方式）

- 用户收款账户信息

- 用户身份验证状态

- 用户等级和权限信息

  

**账户信息管理**：

- 支持用户在个人中心维护收款账户

- 管理员可以查看和验证用户账户信息

- 支持多种收款方式的账户信息

- 提供账户信息变更历史记录

  

**数据安全要求**：

- 敏感账户信息加密存储

- 管理员查看账户信息需要权限验证

- 账户信息变更需要审计日志

  

## �‍💼 管理员操作需求

  

### 1. 管理员角色和权限

  

#### 1.1 角色定义

- **结算管理员**：可以查看、生成、处理结算单据

- **财务主管**：可以审批大额结算（超过设定阈值）

- **系统管理员**：可以进行所有操作，包括取消已完成的结算

  

#### 1.2 权限控制

- 结算操作需要管理员登录验证

- 重要操作（如标记完成、取消）需要二次确认

- 大额结算（如超过10000元）需要财务主管审批

- 所有操作记录操作人、操作时间、操作备注

  

### 2. 管理员操作界面需求

  

#### 2.1 结算工作台

**功能描述**：为管理员提供结算工作的总览界面

  

**界面要素**：

- 待处理结算数量统计

- 本月已完成结算金额统计

- 处理中结算列表（快速访问）

- 异常结算提醒（失败、超时等）

- 快速操作按钮（批量生成、批量处理）

  

#### 2.2 操作确认机制

**功能描述**：确保管理员操作的准确性和安全性

  

**确认要求**：

- **生成结算单据前的必要条件**：

  - 必须上传至少一个转账回执文件（调用文件上传组件）

  - 必须填写支付参考号（银行流水号、支付宝订单号等）

  - 必须填写结算备注

  - 所有回执文件必须有描述信息

  - 显示预览信息，需要二次确认操作

- 批量操作前：显示影响范围，需要确认

- 文件上传验证：

  - 文件格式必须为PDF、JPG、PNG

  - 文件大小不超过10MB

  - 文件上传成功后才能继续操作

  

#### 2.3 操作日志查看

**功能描述**：提供完整的操作历史记录查看

  

**日志内容**：

- 操作时间、操作人、操作类型

- 操作前后的状态变化

- 操作备注和说明

- 相关的业务数据变化

  

### 3. 手动支付流程规范

  

#### 3.1 支付前准备

1. 管理员在系统中查看结算单据详情

2. 确认收款人信息和收款账户

3. 确认结算金额和结算方式

4. 在系统中点击"开始处理"（状态变为PROCESSING）

  

#### 3.2 线下支付执行

1. 根据结算方式执行实际支付操作：

   - 银行转账：通过网银或柜台转账

   - 支付宝转账：通过支付宝企业账户转账

   - 微信转账：通过微信企业账户转账

   - 现金支付：线下现金交付

2. 保留支付凭证（转账截图、回执单等）

  

#### 3.3 结算单据生成确认

1. 管理员线下完成转账操作

2. 获得转账回执文件

3. 在系统中点击"生成结算单据"

4. **上传转账回执**（必填步骤）：

   - 调用文件上传组件上传回执文件

   - 支持上传多个回执文件（如银行回执单、转账截图等）

   - 每个文件需要添加文件描述（如"银行转账回执"、"支付宝转账截图"）

   - 文件上传成功后获得OSS URL

5. 填写结算备注和支付参考号（必填）

6. 确认生成结算单据（状态直接为COMPLETED）

  

### 4. 异常处理流程

  

#### 4.1 支付失败处理

- 管理员可以将状态标记为"失败"

- 需要填写失败原因

- 系统保留重试机制（重置为PENDING状态）

  

#### 4.2 信息错误处理

- 发现收款信息错误时，可以取消结算

- 修正信息后重新生成结算单据

- 保留完整的修改记录

  

#### 4.3 争议处理

- 提供结算争议标记功能

- 争议结算需要特殊处理流程

- 保留争议处理的完整记录

  

## �🔧 技术需求

  

### 1. 数据库设计

  

#### 1.1 表结构优化

基于现有的 `commission_settlement` 表结构，进行以下优化：

  

**1.1.1 commission_settlement表字段扩展**

```sql

-- 添加转账回执相关字段

ALTER TABLE commission_settlement

ADD COLUMN payment_reference VARCHAR(100) COMMENT '支付参考号/流水号',

ADD COLUMN receipt_files JSON COMMENT '转账回执文件列表(JSON格式存储)',

ADD COLUMN completion_notes TEXT COMMENT '完成备注',

ADD COLUMN processed_by VARCHAR(50) COMMENT '处理人',

ADD COLUMN completed_by VARCHAR(50) COMMENT '完成确认人',

ADD COLUMN completed_at DATETIME COMMENT '完成确认时间';

  

-- 添加索引优化

CREATE INDEX idx_settlement_inviter_month ON commission_settlement(inviter_id, month_key);

CREATE INDEX idx_settlement_status ON commission_settlement(settlement_status);

CREATE INDEX idx_settlement_method ON commission_settlement(settlement_method);

CREATE INDEX idx_settlement_time ON commission_settlement(settlement_time);

CREATE INDEX idx_settlement_processed_by ON commission_settlement(processed_by);

  

-- 添加外键约束

ALTER TABLE commission_settlement

ADD CONSTRAINT fk_settlement_summary

FOREIGN KEY (inviter_id, month_key)

REFERENCES monthly_commission_summary(inviter_id, month_key);

```

  

**1.1.2 转账回执文件JSON格式定义**

```json

{

  "receipt_files": [

    {

      "file_url": "https://oss.example.com/receipts/20240301/receipt_001.pdf",

      "file_name": "银行转账回执.pdf",

      "file_type": "pdf",

      "file_size": 1024000,

      "description": "中国银行转账回执单",

      "uploaded_at": "2024-03-01T10:30:00",

      "uploaded_by": "admin001"

    },

    {

      "file_url": "https://oss.example.com/receipts/20240301/screenshot_001.jpg",

      "file_name": "支付宝转账截图.jpg",

      "file_type": "jpg",

      "file_size": 512000,

      "description": "支付宝转账成功截图",

      "uploaded_at": "2024-03-01T10:35:00",

      "uploaded_by": "admin001"

    }

  ]

}

```

  

#### 1.2 数据完整性

- 添加必要的约束条件

- 实现软删除机制

- 添加审计字段（创建人、修改人等）

- 转账回执文件JSON字段验证

- 文件URL有效性验证

  

#### 1.3 URL存储技术要求

**URL处理要求**：

- 后端只处理文件URL的保存和验证

- URL格式验证和有效性检查

- 支持URL访问权限验证

  

**数据存储要求**：

- URL以JSON格式存储在数据库中

- 包含URL、描述、时间戳等元数据

- 支持URL的增删改查操作

  

### 2. 架构设计

  

#### 2.1 DDD分层架构

```

interfaces/web/

├── CommissionSettlementController.java     # 结算API控制器

└── dto/

    ├── SettlementRequestDTO.java           # 结算请求DTO

    ├── SettlementResponseDTO.java          # 结算响应DTO

    └── SettlementSummaryDTO.java           # 结算汇总DTO

  

application/service/

├── CommissionSettlementService.java       # 结算应用服务

└── SettlementNotificationService.java     # 结算通知服务

  

domain/

├── entity/

│   └── CommissionSettlement.java          # 结算实体

├── service/

│   ├── SettlementDomainService.java       # 结算领域服务

│   └── PaymentGatewayService.java         # 支付网关服务

└── repository/

    └── CommissionSettlementRepository.java # 结算仓储接口

  

infrastructure/

├── repository/

│   └── CommissionSettlementRepositoryImpl.java # 结算仓储实现

├── mapper/

│   └── CommissionSettlementMapper.java    # MyBatis映射器

├── po/

│   └── CommissionSettlementPO.java        # 持久化对象

└── gateway/

    ├── AlipayGateway.java                  # 支付宝支付网关

    ├── WechatGateway.java                  # 微信支付网关

    └── BankTransferGateway.java            # 银行转账网关

```

  

#### 2.2 设计模式应用

- **策略模式**：不同结算方式的处理策略

- **工厂模式**：支付网关工厂

- **观察者模式**：结算状态变更通知

- **模板方法模式**：结算流程模板

  

### 3. 性能要求

  

#### 3.1 响应时间

- 结算单据生成：< 2秒

- 结算记录查询：< 1秒

- 结算列表页面加载：< 3秒

- 状态更新操作：< 1秒

  

#### 3.2 并发处理

- 支持多个管理员同时操作

- 防止重复结算（乐观锁）

- 状态变更冲突检测和提示

  

#### 3.3 数据量支持

- 单月结算记录：10万条

- 历史数据保留：3年

- 查询性能优化：索引覆盖

- 分页查询：每页最多100条记录

  

## 📊 非功能性需求

  

### 1. 安全性需求

- 结算操作需要管理员权限验证

- 敏感账户信息加密存储

- 所有操作完整记录审计日志

- 防止SQL注入和XSS攻击

- 管理员操作需要二次确认

- 重要操作需要操作备注

- 支持操作权限分级（查看、编辑、审批）

  

### 2. 可靠性需求

- 结算数据不能丢失

- 支持事务回滚

- 异常情况自动恢复

- 99.9%的系统可用性

  

### 3. 可维护性需求

- 代码结构清晰

- 完整的单元测试

- 详细的技术文档

- 标准化的错误处理

  

### 4. 可扩展性需求

- 支持新增结算方式

- 支持自定义结算规则

- 支持多币种结算

- 支持国际化

  

## 🎯 验收标准

  

### 1. 功能验收

- ✅ 结算单据生成功能正常

- ✅ 支持所有定义的结算方式

- ✅ 结算状态流转正确

- ✅ 查询和统计功能完整

- ✅ 与现有系统集成无误

- ✅ **转账回执上传功能验收**：

  - 支持PDF、JPG、PNG文件上传

  - 文件大小和格式验证正确

  - 文件上传进度显示正常

  - 文件预览功能正常

  - 文件URL正确保存到数据库

  - 文件访问权限控制有效

  - 文件删除和重新上传功能正常

- ✅ **结算完成验证**：

  - 没有上传回执文件不能标记完成

  - 必须填写支付参考号才能完成

  - 完成操作的二次确认机制正常

  

### 2. 性能验收

- ✅ 响应时间满足要求

- ✅ 并发处理能力达标

- ✅ 大数据量处理正常

  

### 3. 安全验收

- ✅ 权限控制有效

- ✅ 数据加密正确

- ✅ 审计日志完整

  

### 4. 测试验收

- ✅ 单元测试覆盖率 > 90%

- ✅ 集成测试通过

- ✅ 性能测试达标

- ✅ 安全测试通过

  

## 📅 项目计划

  

### 阶段1：基础功能开发（2-3周）

- 实体类和数据库表结构完善

- 基础的CRUD功能实现

- 结算单据生成逻辑

- 基础的管理员API接口

  

### 阶段2：管理界面开发（2-3周）

- 待结算列表页面开发

- 结算单据管理页面开发

- 结算处理页面开发

- 状态流转和权限控制实现

  

### 阶段3：集成和完善（1-2周）

- 与现有佣金系统集成测试

- 用户信息和账户信息集成

- 通知功能完善

- 安全性加固和权限验证

  

### 阶段4：测试和上线（1周）

- 完整功能测试和用户验收

- 管理员操作培训

- 生产环境部署

- 监控和维护机制建立

  

---

  

**文档版本**: v2.1

**创建日期**: 2025-07-06

**修订日期**: 2025-07-06

**需求分析基础**: commission模块测试代码分析

**核心变更**:

- 调整为管理员手动结算模式

- 生成结算单据时强制上传转账回执

- 调用现有文件上传组件获取OSS URL

- 简化状态流转，生成即完成

**项目负责人**: 后端开发团队