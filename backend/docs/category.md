## category 模块文档

### 1. 模块概述
商品分类管理模块，负责处理商品分类的创建、查询和管理功能

### 2. 核心类说明
#### Controller类
- **CategoryController**:
  - 功能: 提供分类管理的REST API接口
  - 主要接口:
    - `GET /categories`: 获取所有分类列表
    - `POST /categories`: 创建新分类

#### Service类
- **CategoryService**:
  - 核心方法:
    - `createCategory`: 创建分类
    - `getAllCategories`: 获取全部分类

#### Entity类
- **Category**:
  - 主要字段说明:
    - `id`: 分类ID
    - `name`: 分类名称
    - `parentId`: 父分类ID

### 3. 使用示例
```java
// 创建分类示例
CategoryDTO dto = new CategoryDTO();
dto.setName("电子产品");
Category category = categoryService.createCategory(dto);

// 查询分类示例
List<Category> categories = categoryService.getAllCategories();
```

### 4. 注意事项
- 分类支持多级嵌套结构
- 删除分类时需要先确保没有商品关联