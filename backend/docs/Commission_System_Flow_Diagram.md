# 佣金系统业务流程图与数据流向分析

## 📋 概述

本文档基于commission模块测试代码分析，详细描述了佣金系统的完整业务流程、数据流向和数据库表关系。通过Mermaid流程图和详细说明，展现了从订单完成到佣金结算的全过程。

## 🎯 核心业务流程

### 1. 主流程：订单完成到佣金计算

```mermaid
graph TD
    A[订单完成事件] --> B{订单有效性验证}
    B -->|无效| C[结束处理]
    B -->|有效| D[查找邀请关系]
    D -->|无邀请关系| C
    D -->|有邀请关系| E[检查邀请人角色]
    E -->|买家| F[生成优惠券<br/>暂时注释]
    E -->|卖家/货代| G[重复处理检查]
    G -->|已处理| C
    G -->|未处理| H[创建佣金记录]
    H --> I[更新月度汇总]
    I --> J[保存到数据库]
    
    style A fill:#e1f5fe
    style F fill:#fff3e0
    style H fill:#e8f5e8
    style I fill:#f3e5f5
    style J fill:#e0f2f1
```

### 2. 佣金计算详细流程

```mermaid
graph TD
    A[开始佣金计算] --> B[获取月度累计金额]
    B --> C[计算佣金比例]
    C --> D{阶梯判断}
    D -->|$0-$7K| E[0.9%比例]
    D -->|$7K-$20K| F[1.2%比例]
    D -->|$20K+| G[1.5%比例]
    E --> H[计算单笔佣金]
    F --> H
    G --> H
    H --> I[计算月度奖金]
    I --> J{奖金阶梯}
    J -->|<$7K| K[无奖金]
    J -->|$7K-$15K| L[$50奖金]
    J -->|$15K-$30K| M[$100奖金]
    J -->|$30K+| N[$200奖金]
    K --> O[创建佣金记录]
    L --> O
    M --> O
    N --> O
    O --> P[更新月度汇总]
    
    style D fill:#fff3e0
    style J fill:#e8f5e8
    style O fill:#e1f5fe
    style P fill:#f3e5f5
```

### 3. 月度结算流程

```mermaid
graph TD
    A[月度结算开始] --> B[查找月度汇总]
    B -->|不存在| C[结束处理]
    B -->|存在| D{汇总状态检查}
    D -->|非PENDING| C
    D -->|PENDING| E[开始结算<br/>状态→PROCESSING]
    E --> F[查找可结算记录]
    F --> G[执行结算逻辑]
    G --> H[标记佣金为已支付]
    H --> I[完成结算<br/>状态→COMPLETED]
    I --> J[记录结算时间]
    J --> K[保存更新]
    
    style E fill:#fff3e0
    style G fill:#e8f5e8
    style I fill:#e1f5fe
    style K fill:#e0f2f1
```

## 🗄️ 数据库表关系与数据流向

### 核心数据表结构

```mermaid
erDiagram
    commission_record_v2 {
        bigint id PK
        bigint inviter_id FK
        bigint invitee_id FK
        bigint order_id
        string order_type
        decimal order_amount
        decimal commission_rate
        decimal commission_amount
        string status
        string month_key
        boolean buyer_role_verified
        datetime created_at
        datetime updated_at
        boolean deleted
    }
    
    monthly_commission_summary {
        bigint id PK
        bigint inviter_id FK
        string month_key
        decimal total_order_amount
        decimal commission_rate
        decimal commission_amount
        decimal monthly_bonus
        decimal total_amount
        int order_count
        string status
        datetime settled_at
        datetime created_at
        datetime updated_at
    }
    
    commission_config {
        bigint id PK
        string config_type
        string config_key
        string config_value
        string description
        boolean enabled
        datetime created_at
        datetime updated_at
    }
    
    commission_settlement {
        bigint id PK
        string settlement_number
        bigint inviter_id FK
        string month_key
        decimal total_commission
        decimal monthly_bonus
        decimal total_amount
        string settlement_method
        string account_info
        string settlement_status
        datetime settlement_time
        datetime created_at
        datetime updated_at
    }
    
    inviter_coupon {
        bigint id PK
        bigint inviter_id FK
        bigint invitee_id FK
        bigint trigger_order_id FK
        string coupon_code
        string coupon_type
        decimal discount_value
        decimal minimum_amount
        string status
        string month_key
        datetime valid_from
        datetime valid_until
        datetime used_at
        bigint used_order_id
        datetime created_at
        datetime updated_at
    }
    
    commission_record_v2 ||--o{ monthly_commission_summary : "聚合汇总"
    monthly_commission_summary ||--o| commission_settlement : "结算记录"
    commission_record_v2 ||--o{ inviter_coupon : "触发生成"
    commission_config ||--o{ commission_record_v2 : "配置规则"
```

### 数据流向详解

#### 1. 订单完成 → 佣金记录创建
```
OrderCompletedEvent
    ↓ (事件监听)
OrderCompletedEventListener
    ↓ (调用应用服务)
CommissionApplicationService.handleOrderCompleted()
    ↓ (验证和计算)
CommissionCalculationService
    ↓ (创建记录)
commission_record_v2 表
```

#### 2. 佣金记录 → 月度汇总更新
```
commission_record_v2 (新增/更新)
    ↓ (触发汇总更新)
CommissionApplicationService.updateMonthlySummary()
    ↓ (聚合计算)
monthly_commission_summary 表
    ↓ (状态管理)
PENDING → PROCESSING → COMPLETED
```

#### 3. 月度汇总 → 结算处理
```
monthly_commission_summary (PENDING状态)
    ↓ (开始结算)
startMonthlySettlement()
    ↓ (状态变更)
monthly_commission_summary.status = PROCESSING
    ↓ (完成结算)
completeMonthlySettlement()
    ↓ (更新相关记录)
commission_record_v2.status = PAID
monthly_commission_summary.status = COMPLETED
```

## 📊 关键业务规则验证

### 1. 订单有效性验证
```java
// 测试验证点
@Test
void isValidCommissionOrder_ValidPurchaseOrder_ReturnsTrue() {
    // 验证：采购订单 + 已完成状态 + 买家角色验证 = 有效
    assertTrue(calculationService.isValidCommissionOrder("purchase", "completed", true));
}
```

### 2. 重复处理防护
```java
// 测试验证点
@Test
void handleOrderCompleted_DuplicateOrder_SkipsProcessing() {
    // 验证：同一订单+邀请人组合只处理一次
    when(commissionRecordRepository.existsByOrderIdAndInviterId(ORDER_ID, INVITER_ID))
        .thenReturn(true);
    // 应跳过处理
}
```

### 3. 角色差异化处理
```java
// 测试验证点
@Test
void handleOrderCompleted_BuyerInviter_NoActionTaken() {
    // 验证：买家邀请人暂不生成优惠券（功能未开放）
    when(userMapper.selectRoleById(INVITER_ID)).thenReturn("buyer");
    // 应跳过佣金计算
}
```

## 🔄 状态流转图

### 佣金记录状态流转
```mermaid
stateDiagram-v2
    [*] --> PENDING: 创建佣金记录
    PENDING --> CONFIRMED: 确认佣金
    PENDING --> CANCELLED: 取消佣金
    CONFIRMED --> PAID: 标记已支付
    CANCELLED --> [*]: 结束
    PAID --> [*]: 结束
    
    note right of PENDING: 初始状态，等待确认
    note right of CONFIRMED: 已确认，等待支付
    note right of PAID: 已支付，流程结束
```

### 月度汇总状态流转
```mermaid
stateDiagram-v2
    [*] --> PENDING: 创建月度汇总
    PENDING --> PROCESSING: 开始结算
    PROCESSING --> COMPLETED: 完成结算
    PROCESSING --> CANCELLED: 取消结算
    CANCELLED --> PENDING: 重置状态
    COMPLETED --> [*]: 结束
    
    note right of PENDING: 待结算状态
    note right of PROCESSING: 结算处理中
    note right of COMPLETED: 结算完成
```

## 📈 性能优化设计

### 1. 数据聚合策略
- **实时更新**：每次佣金记录变更时更新月度汇总
- **避免重复计算**：月度汇总表缓存计算结果
- **分页查询**：大数据量查询支持分页

### 2. 索引设计
```sql
-- 佣金记录表关键索引
CREATE INDEX idx_commission_inviter_month ON commission_record_v2(inviter_id, month_key);
CREATE INDEX idx_commission_order_inviter ON commission_record_v2(order_id, inviter_id);
CREATE INDEX idx_commission_status ON commission_record_v2(status);

-- 月度汇总表关键索引
CREATE INDEX idx_summary_inviter_month ON monthly_commission_summary(inviter_id, month_key);
CREATE INDEX idx_summary_status ON monthly_commission_summary(status);
```

## 🔍 测试覆盖范围

### 1. 单元测试覆盖
- ✅ **佣金计算服务**：47个测试用例，覆盖所有计算逻辑
- ✅ **应用服务**：22个测试用例，覆盖完整业务流程
- ✅ **事件监听器**：15个测试用例，覆盖事件处理
- ✅ **实体对象**：35个测试用例，覆盖领域逻辑

### 2. 集成测试覆盖
- ✅ **端到端流程**：订单完成到佣金创建
- ✅ **数据一致性**：明细记录与汇总数据同步
- ✅ **异常处理**：各种边界条件和错误场景

## 💡 总结

佣金系统通过事件驱动架构实现了：

1. **松耦合设计**：订单系统与佣金系统通过事件解耦
2. **数据一致性**：通过事务和状态管理保证数据一致性
3. **性能优化**：通过汇总表避免频繁聚合计算
4. **业务扩展性**：支持多种订单类型和角色差异化处理
5. **可靠性保证**：完善的重复处理防护和异常处理机制

整个系统的核心是 `MonthlyCommissionSummary` 表，它既是数据汇总中心，也是结算状态管理中心，确保了业务流程的完整性和数据的一致性。

## 🛠️ 技术实现细节

### 1. 事件驱动架构实现

#### 事件发布机制
```java
// 订单完成时发布事件
@Service
public class OrderService {
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    public void completeOrder(Long orderId) {
        // 业务逻辑...

        // 发布事件
        CommissionCalculationEvent event = new CommissionCalculationEvent(
            orderId, buyerId, orderAmount, orderType, LocalDateTime.now()
        );
        eventPublisher.publishEvent(event);
    }
}
```

#### 异步事件监听
```java
// 异步处理避免影响主业务流程
@Component
public class OrderCompletedEventListener {

    @Async("commissionTaskExecutor")
    @EventListener
    public void handleOrderCompleted(CommissionCalculationEvent event) {
        try {
            commissionApplicationService.handleOrderCompleted(
                event.getOrderId(),
                event.getBuyerId(),
                event.getOrderAmount(),
                event.getOrderType(),
                event.getCompletedAt().toLocalDate()
            );
        } catch (Exception e) {
            log.error("佣金计算失败", e);
            // 异常不影响主流程
        }
    }
}
```

### 2. 数据一致性保证机制

#### 事务管理
```java
@Service
@Transactional
public class CommissionApplicationService {

    @Transactional(rollbackFor = Exception.class)
    public void handleOrderCompleted(Long orderId, Long buyerId,
                                   Money orderAmount, String orderType,
                                   LocalDate completedAt) {
        // 1. 创建佣金记录
        CommissionRecord record = createCommissionRecord(...);
        commissionRecordRepository.save(record);

        // 2. 更新月度汇总（同一事务）
        updateMonthlySummary(record.getInviterId(), record.getMonthKey());

        // 任何异常都会回滚整个事务
    }
}
```

#### 重复处理防护
```java
// 基于数据库唯一约束防止重复处理
@Entity
@Table(uniqueConstraints = {
    @UniqueConstraint(columnNames = {"order_id", "inviter_id"})
})
public class CommissionRecord {
    // 确保同一订单+邀请人组合只能创建一条记录
}

// 业务层双重检查
public void handleOrderCompleted(...) {
    if (commissionRecordRepository.existsByOrderIdAndInviterId(orderId, inviterId)) {
        log.warn("佣金记录已存在，跳过处理: orderId={}, inviterId={}", orderId, inviterId);
        return;
    }
    // 继续处理...
}
```

### 3. 计算引擎实现

#### 阶梯式佣金计算
```java
@Service
public class CommissionCalculationService {

    public CommissionRate calculateCommissionRate(Money monthlyAmount) {
        BigDecimal amount = monthlyAmount.getAmount();

        // 阶梯配置（可配置化）
        if (amount.compareTo(new BigDecimal("20000")) >= 0) {
            return CommissionRate.ofPercentage(new BigDecimal("1.5")); // 1.5%
        } else if (amount.compareTo(new BigDecimal("7000")) >= 0) {
            return CommissionRate.ofPercentage(new BigDecimal("1.2")); // 1.2%
        } else {
            return CommissionRate.ofPercentage(new BigDecimal("0.9")); // 0.9%
        }
    }

    public Money calculateMonthlyBonus(Money monthlyAmount) {
        BigDecimal amount = monthlyAmount.getAmount();

        // 奖金阶梯（可配置化）
        if (amount.compareTo(new BigDecimal("30000")) >= 0) {
            return Money.of("200");
        } else if (amount.compareTo(new BigDecimal("15000")) >= 0) {
            return Money.of("100");
        } else if (amount.compareTo(new BigDecimal("7000")) >= 0) {
            return Money.of("50");
        } else {
            return Money.zero();
        }
    }
}
```

### 4. 月度汇总更新机制

#### 实时汇总更新
```java
public void updateMonthlySummary(Long inviterId, MonthKey monthKey) {
    // 1. 获取或创建月度汇总
    MonthlyCommissionSummary summary = monthlySummaryRepository
        .findByInviterIdAndMonthKey(inviterId, monthKey)
        .orElse(createNewMonthlySummary(inviterId, monthKey));

    // 2. 重新计算该月所有有效记录
    List<CommissionRecord> validRecords = commissionRecordRepository
        .findByInviterIdAndMonthKey(inviterId, monthKey)
        .stream()
        .filter(CommissionRecord::isValid)
        .collect(toList());

    // 3. 聚合计算
    Money totalOrderAmount = validRecords.stream()
        .map(CommissionRecord::getOrderAmount)
        .reduce(Money.zero(), Money::add);

    Money totalCommission = validRecords.stream()
        .map(CommissionRecord::getCommissionAmount)
        .reduce(Money.zero(), Money::add);

    // 4. 重新计算佣金比例和奖金（基于新的累计金额）
    CommissionRate newRate = calculationService.calculateCommissionRate(totalOrderAmount);
    Money monthlyBonus = calculationService.calculateMonthlyBonus(totalOrderAmount);

    // 5. 更新汇总记录
    summary.updateSummary(totalOrderAmount, newRate, totalCommission,
                         monthlyBonus, validRecords.size());

    monthlySummaryRepository.save(summary);
}
```

## 📋 API接口设计

### 1. 查询接口
```java
@RestController
@RequestMapping("/api/v1/commission")
public class CommissionController {

    // 查询佣金记录（分页）
    @GetMapping("/records/inviter/{inviterId}")
    public Result<Page<CommissionRecordDTO>> getCommissionRecords(
            @PathVariable Long inviterId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        // 实现分页查询
    }

    // 查询月度汇总
    @GetMapping("/summary/{inviterId}/{monthKey}")
    public Result<MonthlyCommissionSummaryDTO> getMonthlySummary(
            @PathVariable Long inviterId,
            @PathVariable String monthKey) {
        // 实现月度汇总查询
    }

    // 查询佣金趋势
    @GetMapping("/trend/{inviterId}")
    public Result<List<CommissionTrendDTO>> getCommissionTrend(
            @PathVariable Long inviterId,
            @RequestParam(defaultValue = "12") Integer months) {
        // 实现趋势数据查询
    }
}
```

### 2. 管理接口
```java
@RestController
@RequestMapping("/api/v1/commission/admin")
@PreAuthorize("hasRole('ADMIN')")
public class CommissionAdminController {

    // 手动触发月度处理
    @PostMapping("/monthly/process/{monthKey}")
    public Result<String> processMonthlyCommission(@PathVariable String monthKey) {
        monthlyCommissionProcessor.processMonth(MonthKey.of(monthKey));
        return Result.success("月度佣金处理已启动");
    }

    // 开始结算
    @PostMapping("/settlement/start")
    public Result<String> startSettlement(@RequestBody SettlementRequest request) {
        commissionApplicationService.startMonthlySettlement(
            request.getInviterId(), MonthKey.of(request.getMonthKey()));
        return Result.success("结算已开始");
    }

    // 完成结算
    @PostMapping("/settlement/complete")
    public Result<String> completeSettlement(@RequestBody SettlementRequest request) {
        commissionApplicationService.completeMonthlySettlement(
            request.getInviterId(),
            MonthKey.of(request.getMonthKey()),
            LocalDateTime.now());
        return Result.success("结算已完成");
    }
}
```

## 🔧 配置管理

### 1. 佣金配置表设计
```sql
-- 佣金阶梯配置
INSERT INTO commission_config (config_type, config_key, config_value, description) VALUES
('COMMISSION_RATE', 'TIER_1_THRESHOLD', '7000', '第一阶梯门槛'),
('COMMISSION_RATE', 'TIER_1_RATE', '0.009', '第一阶梯比例(0.9%)'),
('COMMISSION_RATE', 'TIER_2_THRESHOLD', '20000', '第二阶梯门槛'),
('COMMISSION_RATE', 'TIER_2_RATE', '0.012', '第二阶梯比例(1.2%)'),
('COMMISSION_RATE', 'TIER_3_RATE', '0.015', '第三阶梯比例(1.5%)');

-- 奖金配置
INSERT INTO commission_config (config_type, config_key, config_value, description) VALUES
('MONTHLY_BONUS', 'BONUS_1_THRESHOLD', '7000', '第一奖金阶梯门槛'),
('MONTHLY_BONUS', 'BONUS_1_AMOUNT', '50', '第一奖金阶梯金额'),
('MONTHLY_BONUS', 'BONUS_2_THRESHOLD', '15000', '第二奖金阶梯门槛'),
('MONTHLY_BONUS', 'BONUS_2_AMOUNT', '100', '第二奖金阶梯金额'),
('MONTHLY_BONUS', 'BONUS_3_THRESHOLD', '30000', '第三奖金阶梯门槛'),
('MONTHLY_BONUS', 'BONUS_3_AMOUNT', '200', '第三奖金阶梯金额');
```

### 2. 动态配置加载
```java
@Service
public class CommissionConfigService {

    @Cacheable("commissionConfig")
    public List<CommissionTierConfig> getCommissionTiers() {
        return commissionConfigRepository
            .findByConfigTypeAndEnabled("COMMISSION_RATE", true)
            .stream()
            .map(this::convertToTierConfig)
            .sorted(Comparator.comparing(CommissionTierConfig::getThreshold))
            .collect(toList());
    }

    @CacheEvict(value = "commissionConfig", allEntries = true)
    public void refreshConfig() {
        // 刷新配置缓存
    }
}
```

---

**文档版本**：v1.0
**创建日期**：2025-07-06
**基于测试代码版本**：commission模块完整测试套件
**维护人员**：后端开发团队
