# PageResult.java

## 文件概述 (File Overview)
`PageResult.java` 是一个泛型数据传输对象 (DTO)，旨在封装和返回分页数据结果。它提供了数据列表、总记录数、当前页码、每页大小、总页数以及指示是否有下一页或上一页的标志字段。该类利用 Lombok 注解减少样板代码，并使用 Swagger 注解进行 API 文档说明。

## 核心功能 (Core Functionality)
*   **数据封装:** 包含一个泛型类型 `T` 的数据列表 (`records`)、`total` 记录总数、`current` 当前页码、`size` 每页记录数、`pages` 总页数，以及 `hasNext` 和 `hasPrevious` 标志。
*   **`of` 静态方法:** 提供一个便捷的静态工厂方法来构建 `PageResult` 实例，根据提供的数据自动计算 `pages`、`hasNext` 和 `hasPrevious`。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `records`: `List<T>` - 当前页的数据项列表。
*   `total`: `Long` - 所有页面的记录总数。
*   `current`: `Integer` - 当前页码（从 1 开始）。
*   `size`: `Integer` - 每页的记录数。
*   `pages`: `Integer` - 总页数。
*   `hasNext`: `Boolean` - 如果有下一页，则为 true，否则为 false。
*   `hasPrevious`: `Boolean` - 如果有上一页，则为 true，否则为 false。

### `of(List<T> records, Long total, Integer current, Integer size)` 静态方法
*   **参数:**
    *   `records`: `List<T>` - 当前页的数据项列表。
    *   `total`: `Long` - 记录总数。
    *   `current`: `Integer` - 当前页码。
    *   `size`: `Integer` - 每页的记录数。
*   **返回值:** `PageResult<T>` - 一个新的 `PageResult` 实例，其中包含提供的数据和计算出的分页元数据。
*   **业务逻辑:**
    1.  通过将 `total` 除以 `size` 并向上取整来计算 `pages`。
    2.  通过检查 `current` 是否小于 `pages` 来计算 `hasNext`。
    3.  通过检查 `current` 是否大于 1 来计算 `hasPrevious`。
    4.  使用 Lombok 的 `@Builder` 构建并返回 `PageResult` 对象。

## 使用示例 (Usage Examples)

```java
// In a Service Layer method
public PageResult<UserDto> getUsers(int pageNum, int pageSize) {
    // Assume userMapper.selectPage returns an IPage object from Mybatis-Plus
    IPage<User> userPage = userMapper.selectPage(new Page<>(pageNum, pageSize), null);

    List<UserDto> userDtos = userPage.getRecords().stream()
                                     .map(user -> convertToDto(user)) // Your conversion logic
                                     .collect(Collectors.toList());

    return PageResult.of(userDtos, userPage.getTotal(), (int) userPage.getCurrent(), (int) userPage.getSize());
}

// In a Controller Layer method
@GetMapping("/users")
public ResponseResult<PageResult<UserDto>> listUsers(@RequestParam int pageNum, @RequestParam int pageSize) {
    PageResult<UserDto> pageResult = userService.getUsers(pageNum, pageSize);
    return ResponseResult.success(pageResult);
}
```

## 注意事项 (Notes)
*   **泛型:** 泛型 (`<T>`) 的使用使得此 DTO 在需要分页的不同实体或数据类型之间具有高度可重用性。
*   **Lombok:** 依赖 Lombok 注解 (`@Data`, `@Builder`, `@NoArgsConstructor`, `@AllArgsConstructor`) 自动生成 getter、setter、构造函数和构建器模式，减少样板代码。
*   **Swagger:** `@Schema` 注解为 API 消费者提供了清晰的文档，增强了生成的 OpenAPI 规范。
*   **1-based Indexing:** `current` 页码通常是基于 1 的，这是分页的常见约定。
*   **自动计算:** `of` 方法通过自动派生 `pages`、`hasNext` 和 `hasPrevious` 来简化 `PageResult` 实例的创建。
