# `com.purchase.common` 包概述

`com.purchase.common` 包包含了整个采购系统后端通用的基础设施、工具类、异常处理和API响应封装等核心组件。它旨在提供可复用的基础功能，确保系统的一致性、健壮性和可维护性。

## 主要职责
- **统一API响应**: 定义了标准化的API响应格式，便于前后端数据交互。
- **全局异常处理**: 集中处理应用程序中的各类异常，提供友好的错误信息。
- **日志管理**: 提供了日志分类、审计日志切面和日志上下文管理，增强了日志的可追溯性和可分析性。
- **工具类**: 包含常用的工具方法，如日期时间处理、Cookie操作、JWT令牌生成与解析、雪花算法ID生成等。
- **通用配置**: 提供了Web MVC相关的通用配置，如CORS和Jackson序列化配置。

## 包内文件概览

### `config` 目录
- [`MvcConfiguration.md`](./config/MvcConfiguration.md): Spring MVC的通用配置，包括CORS设置和Jackson消息转换器配置，解决Long型ID精度丢失问题。

### `dto` 目录
- [`PageResult.md`](./dto/PageResult.md): 通用的分页结果数据传输对象，用于封装分页查询的返回数据。

### `exception` 目录
- [`BusinessException.md`](./exception/BusinessException.md): 自定义业务异常类，用于封装业务逻辑错误。
- [`GlobalExceptionHandler.md`](./exception/GlobalExceptionHandler.md): 全局异常处理器，统一处理应用程序中的各种异常。
- [`ResourceNotFoundException.md`](./exception/ResourceNotFoundException.md): 资源未找到异常类，表示请求的资源不存在。

### `log` 目录
- [`AuditLog.md`](./log/AuditLog.md): 审计日志注解，用于标记需要记录审计日志的方法。
- [`AuditLogAspect.md`](./log/AuditLogAspect.md): 审计日志切面，实现对 `@AuditLog` 注解方法的拦截和日志记录。
- [`LogContext.md`](./log/LogContext.md): 日志上下文工具类，利用MDC管理请求链路追踪和用户信息。
- [`LoggerFactory.md`](./log/LoggerFactory.md): 日志工厂类，提供不同类型的日志记录器（业务、审计、性能、普通）。
- [`LogInterceptor.md`](./log/LogInterceptor.md): HTTP请求日志拦截器，记录请求和响应信息，并进行敏感数据脱敏。

### `response` 目录
- [`ApiResponse.md`](./response/ApiResponse.md): 统一API响应格式，包含成功/失败状态、消息、数据和状态码。
- [`Result.md`](./response/Result.md): 另一个统一API响应格式，包含状态码、消息和数据。

### `util` 目录
- [`CookieUtil.md`](./util/CookieUtil.md): Cookie操作工具类。
- [`DateTimeUtil.md`](./util/DateTimeUtil.md): 日期时间处理工具类。
- [`JwtUtil.md`](./util/JwtUtil.md): JWT（JSON Web Token）生成与解析工具类。
- [`SecurityContextUtil.md`](./util/SecurityContextUtil.md): Spring Security上下文工具类，用于获取当前认证用户信息。
- [`SnowflakeIdGenerator.md`](./util/SnowflakeIdGenerator.md): 基于雪花算法的分布式ID生成器。