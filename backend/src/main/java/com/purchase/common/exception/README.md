# com.purchase.common.exception 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.common.exception` 包为采购系统后端提供了健壮且集中的异常处理机制。它定义了用于业务特定错误和资源未找到场景的自定义异常类，以及一个全局异常处理器，以确保 API 消费者获得一致的错误响应。

## 目录结构概览 (Directory Structure Overview)
*   `BusinessException.java`: 用于业务逻辑错误的自定义运行时异常。
*   `GlobalExceptionHandler.java`: 用于各种应用程序异常的集中式处理器，确保一致的 API 错误响应。
*   `ResourceNotFoundException.java`: 当请求的资源未找到时的自定义运行时异常。
*   `BusinessException.md`: `BusinessException.java` 的文档。
*   `GlobalExceptionHandler.md`: `GlobalExceptionHandler.java` 的文档。
*   `ResourceNotFoundException.md`: `ResourceNotFoundException.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
该包协同工作以提供全面的错误管理：
1.  **异常定义:** `BusinessException` 和 `ResourceNotFoundException` 定义了在业务操作或资源查找期间可能发生的特定类型的运行时错误。这些自定义异常允许开发人员抛出具有语义意义的错误，而不是通用的 `RuntimeException`。
2.  **异常抛出:** 在应用程序的服务层和控制器层中，开发人员可以抛出 `BusinessException`（例如，用于验证失败、无效状态）或 `ResourceNotFoundException`（例如，当数据库查询未返回给定 ID 的结果时）的实例。
3.  **全局处理:** `GlobalExceptionHandler` 作为这些异常和其他 Spring 相关异常（如验证错误、安全异常、HTTP 消息解析问题）的中心拦截器。
    *   它使用 `@ExceptionHandler` 注解将特定异常类型映射到专用的处理方法。
    *   每个处理方法记录异常（在适当的级别，如业务错误的 `WARN` 级别和系统错误的 `ERROR` 级别），并构建一个标准化的 `Result` 对象（或通过反射为佣金模块构建特定的 `ApiResponse`），作为 HTTP 响应体返回。
    *   `@ResponseStatus` 注解确保在 HTTP 响应中设置正确的 HTTP 状态码（例如，400 表示错误请求，403 表示禁止，404 表示未找到，500 表示内部服务器错误）。
这种协作确保了内部应用程序错误得到优雅处理，转换为一致的 API 响应，并记录下来以供操作洞察，从而改善开发人员体验和 API 消费者体验。