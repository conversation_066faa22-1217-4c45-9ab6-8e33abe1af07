# ResourceNotFoundException.java

## 文件概述 (File Overview)
`ResourceNotFoundException.java` 是一个自定义的运行时异常类，专门用于指示请求的资源（例如，用户、产品或订单）未找到。它继承自 `RuntimeException`，并提供错误码和描述性消息字段，为在应用程序中发出“未找到”场景信号提供了一种清晰且标准化的方式。

## 核心功能 (Core Functionality)
*   **特定异常类型:** 为资源未找到场景提供专用异常，使错误处理更具语义性，并且更容易与其他业务或系统错误区分开来。
*   **错误码:** 允许将特定代码（默认为“404”）与“未找到”错误关联。
*   **描述性消息:** 携带一条消息，解释未找到的资源或原因。
*   **构造函数:** 提供构造函数，以创建带有自定义代码和消息的异常，或仅带有消息的异常（使用默认的“404”代码）。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `code`: `String` - 与异常关联的错误码（例如，“404”）。
*   `message`: `String` - 异常的描述性消息。

### 构造函数 (Constructors)
*   `ResourceNotFoundException(String code, String message)`: 使用指定的代码和消息创建异常。
*   `ResourceNotFoundException(String message)`: 使用消息创建异常，代码默认为“404”。

### Getter 和 Setter (Getters and Setters)
*   `getCode()`: 获取错误码。
*   `setCode(String code)`: 设置错误码。
*   `getMessage()`: 获取异常消息。
*   `setMessage(String message)`: 设置异常消息。

## 使用示例 (Usage Examples)

```java
// Example 1: Throwing a ResourceNotFoundException when a user is not found
public User getUserById(Long userId) {
    return userRepository.findById(userId)
                         .orElseThrow(() -> new ResourceNotFoundException("用户ID为 " + userId + " 的用户未找到"));
}

// Example 2: Throwing a ResourceNotFoundException with a custom code
public Product getProductBySku(String sku) {
    Product product = productRepository.findBySku(sku);
    if (product == null) {
        throw new ResourceNotFoundException("PRODUCT_NOT_FOUND", "SKU为 " + sku + " 的产品未找到");
    }S
    return product;
}
```

## 注意事项 (Notes)
*   **运行时异常:** 作为 `RuntimeException`，它不需要显式声明 `throws`，从而简化了方法签名。
*   **与 GlobalExceptionHandler 集成:** 此异常通常由全局异常处理器（如 `GlobalExceptionHandler`）捕获，然后将其转换为 RESTful API 的适当 HTTP 404 Not Found 响应。
*   **语义清晰度:** 使用特定的 `ResourceNotFoundException` 通过清晰地指示错误的性质来提高代码库的清晰度和可维护性。
*   **默认代码“404”:** 默认代码“404”与“未找到”错误的标准 HTTP 状态码一致，这对于 RESTful 服务来说是良好的实践。
