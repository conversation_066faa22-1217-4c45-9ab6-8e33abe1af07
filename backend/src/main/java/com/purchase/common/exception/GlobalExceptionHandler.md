# GlobalExceptionHandler.java

## 文件概述 (File Overview)
`GlobalExceptionHandler.java` 是一个 Spring Boot 应用程序的集中式异常处理器，使用 `@RestControllerAdvice` 注解。其主要作用是拦截应用程序 API 层抛出的各种异常，并返回一致、用户友好的 `Result`（或针对佣金模块的 `ApiResponse`）对象，从而确保统一的错误响应格式。它还记录异常以进行调试和监控。

## 核心功能 (Core Functionality)
*   **集中式异常处理:** 全局捕获所有 `@Controller` 和 `@RestController` 组件中的异常。
*   **业务异常处理:** 特别处理 `BusinessException` 并返回带有自定义代码和消息的 `Result`。
*   **验证异常处理:** 捕获并处理与请求参数验证相关的异常（`MethodArgumentNotValidException`、`BindException`、`ConstraintViolationException`），提取错误消息并返回合并的错误响应。
*   **安全异常处理:** 处理 `AccessDeniedException` 以应对未经授权的访问尝试。
*   **常见运行时异常处理:** 提供 `IllegalArgumentException`、`NullPointerException` 和通用 `RuntimeException` 的处理器。
*   **HTTP 相关异常处理:** 解决诸如 JSON 格式错误（`HttpMessageNotReadableException`）、不支持的媒体类型（`HttpMediaTypeNotSupportedException`）和缺少请求参数（`MissingServletRequestParameterException`）等问题。
*   **未知异常回退:** 一个通用的 `Exception.class` 处理器捕获任何未处理的异常，提供通用错误消息并记录完整的堆栈跟踪。
*   **佣金模块特定响应:** 包含逻辑（`createCommissionApiResponse`、`isCommissionRequest`）以专门针对 `/api/v1/commission` 端点相关的请求返回不同的 `ApiResponse` 格式，使用反射来避免直接的模块依赖。

## 接口说明 (Interface Description)

### `createCommissionApiResponse(boolean success, String message, Object data)`
*   **参数:** `success` (boolean), `message` (String), `data` (Object)。
*   **返回值:** `Object`（佣金模块的 `ApiResponse` 或 `Result`）。
*   **业务逻辑:** 尝试使用反射从 `com.purchase.commission.settlement.controller.dto` 包创建 `ApiResponse` 对象。如果成功，则设置 `success`、`message`、`data` 和 `timestamp`。如果反射失败（例如，找不到类），则回退到返回标准 `Result` 对象。

### `isCommissionRequest(WebRequest request)`
*   **参数:** `request` (WebRequest)。
*   **返回值:** `boolean`。
*   **业务逻辑:** 检查请求描述是否包含 `/api/v1/commission` 以确定是否是佣金模块的请求。

### `handleBusinessException(BusinessException e)`
*   **参数:** `e` (BusinessException)。
*   **返回值:** `Result<Object>`。
*   **业务逻辑:** 记录业务异常，并返回带有异常代码、消息和数据（如果存在）的 `Result` 对象。HTTP 状态码: 400 (Bad Request)。

### `handleMethodArgumentNotValidException(MethodArgumentNotValidException e)`
*   **参数:** `e` (MethodArgumentNotValidException)。
*   **返回值:** `Result<Object>`。
*   **业务逻辑:** 从绑定结果中提取所有字段错误消息，将其连接起来，记录日志，并返回指示参数验证失败的 `Result`。HTTP 状态码: 400 (Bad Request)。

### `handleBindException(BindException e)`
*   **参数:** `e` (BindException)。
*   **返回值:** `Result<Object>`。
*   **业务逻辑:** 类似于 `handleMethodArgumentNotValidException`，处理参数绑定错误。HTTP 状态码: 400 (Bad Request)。

### `handleConstraintViolationException(ConstraintViolationException e)`
*   **参数:** `e` (ConstraintViolationException)。
*   **返回值:** `Result<Object>`。
*   **业务逻辑:** 提取约束违反消息，将其连接起来，记录日志，并返回指示约束验证失败的 `Result`。HTTP 状态码: 400 (Bad Request)。

### `handleAccessDeniedException(AccessDeniedException e)`
*   **参数:** `e` (AccessDeniedException)。
*   **返回值:** `Result<Object>`。
*   **业务逻辑:** 记录访问拒绝异常，并返回指示权限不足的 `Result`。HTTP 状态码: 403 (Forbidden)。

### `handleIllegalArgumentException(IllegalArgumentException e)`
*   **参数:** `e` (IllegalArgumentException)。
*   **返回值:** `Result<Object>`。
*   **业务逻辑:** 记录非法参数异常，并返回带有特定错误消息的 `Result`。HTTP 状态码: 400 (Bad Request)。

### `handleNullPointerException(NullPointerException e)`
*   **参数:** `e` (NullPointerException)。
*   **返回值:** `Result<Object>`。
*   **业务逻辑:** 以 ERROR 级别记录空指针异常，并返回通用的内部服务器错误消息。HTTP 状态码: 500 (Internal Server Error)。

### `handleRuntimeException(RuntimeException e)`
*   **参数:** `e` (RuntimeException)。
*   **返回值:** `Result<Object>`。
*   **业务逻辑:** 捕获通用运行时异常，以 ERROR 级别记录日志，并返回通用的运行时错误消息。HTTP 状态码: 500 (Internal Server Error)。

### `handleHttpMessageNotReadable(HttpMessageNotReadableException e, WebRequest request)`
*   **参数:** `e` (HttpMessageNotReadableException), `request` (WebRequest)。
*   **返回值:** `Object`（`ApiResponse` 或 `Result`）。
*   **业务逻辑:** 处理 JSON 解析错误。如果是佣金请求，则返回佣金特定的 `ApiResponse`；否则，返回标准 `Result`。HTTP 状态码: 400 (Bad Request)。

### `handleHttpMediaTypeNotSupported(HttpMediaTypeNotSupportedException e, WebRequest request)`
*   **参数:** `e` (HttpMediaTypeNotSupportedException), `request` (WebRequest)。
*   **返回值:** `Object`（`ApiResponse` 或 `Result`）。
*   **业务逻辑:** 处理不支持的媒体类型错误。如果是佣金请求，则返回佣金特定的 `ApiResponse`；否则，返回标准 `Result`。HTTP 状态码: 415 (Unsupported Media Type)。

### `handleMissingServletRequestParameter(MissingServletRequestParameterException e, WebRequest request)`
*   **参数:** `e` (MissingServletRequestParameterException), `request` (WebRequest)。
*   **返回值:** `Object`（`ApiResponse` 或 `Result`）。
*   **业务逻辑:** 处理缺少必需的请求参数。如果是佣金请求，则返回佣金特定的 `ApiResponse`；否则，返回标准 `Result`。HTTP 状态码: 400 (Bad Request)。

### `handleException(Exception e)`
*   **参数:** `e` (Exception)。
*   **返回值:** `Result<Object>`。
*   **业务逻辑:** 捕获任何其他未处理异常的通用处理器。以 ERROR 级别记录异常，并返回通用的系统错误消息。HTTP 状态码: 500 (Internal Server Error)。

## 使用示例 (Usage Examples)
此类别由 Spring 的异常处理机制自动调用。开发人员通常不会通过调用其方法来“使用”它。相反，他们会抛出此处理器配置为捕获的异常。

```java
// Example of throwing an exception that GlobalExceptionHandler will catch
// In a service method:
public User createUser(UserCreationRequest request) {
    if (userRepository.existsByUsername(request.getUsername())) {
        throw new BusinessException(400, "用户名已存在"); // Handled by handleBusinessException
    }
    // ...
    return userRepository.save(newUser);
}

// In a controller method with validation:
@PostMapping("/products")
public ResponseEntity<Result<Product>> createProduct(@Valid @RequestBody ProductDto productDto) { // MethodArgumentNotValidException if validation fails
    // ...
    return ResponseEntity.ok(Result.success(productService.createProduct(productDto)));
}
```

## 注意事项 (Notes)
*   **`@RestControllerAdvice`:** 此注解结合了 `@ControllerAdvice` 和 `@ResponseBody`，使其适用于处理 RESTful API 中的异常并返回 JSON 响应。
*   **`@ExceptionHandler`:** 每个用 `@ExceptionHandler` 注解的方法都指定了它将处理的异常类型。
*   **`@ResponseStatus`:** 设置捕获到特定异常时响应的 HTTP 状态码。
*   **日志记录:** 使用 `Slf4j` 进行日志记录，这对于生产环境中的错误监控和调试至关重要。业务/验证错误使用 `warn` 级别，意外的系统错误使用 `error` 级别。
*   **佣金模块的反射:** 使用反射为佣金模块创建 `ApiResponse` 是一种设计选择，旨在解耦 `common` 模块与 `commission` 模块。这避免了直接的编译时依赖，但引入了运行时复杂性以及如果 `ApiResponse` 类或其方法发生更改时可能出现的 `ClassNotFoundException` 或 `NoSuchMethodException`。
*   **错误一致性:** 确保所有 API 错误响应都遵循一致的结构，从而提高客户端的 API 可用性。
