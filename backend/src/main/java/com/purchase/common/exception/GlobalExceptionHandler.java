package com.purchase.common.exception;

import com.purchase.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.time.LocalDateTime;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理API异常和业务异常
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 创建commission模块的ApiResponse
     */
    private Object createCommissionApiResponse(boolean success, String message, Object data) {
        try {
            // 使用反射创建ApiResponse对象，避免直接依赖commission模块
            Class<?> apiResponseClass = Class.forName("com.purchase.commission.settlement.controller.dto.ApiResponse");
            Object apiResponse = apiResponseClass.getDeclaredConstructor().newInstance();

            // 设置字段值
            apiResponseClass.getMethod("setSuccess", boolean.class).invoke(apiResponse, success);
            apiResponseClass.getMethod("setMessage", String.class).invoke(apiResponse, message);
            apiResponseClass.getMethod("setData", Object.class).invoke(apiResponse, data);
            apiResponseClass.getMethod("setTimestamp", LocalDateTime.class).invoke(apiResponse, LocalDateTime.now());

            return apiResponse;
        } catch (Exception e) {
            log.warn("Failed to create commission ApiResponse, falling back to Result", e);
            return success ? Result.success(message, data) : Result.error(message);
        }
    }

    /**
     * 检查是否是commission模块的请求
     */
    private boolean isCommissionRequest(WebRequest request) {
        String description = request.getDescription(false);
        return description != null && description.contains("/api/v1/commission");
    }
    
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        if (e.getData() != null) {
            return new Result<>(e.getCode(), e.getMessage(), e.getData());
        }
        return Result.error(e.getCode(), e.getMessage());
    }
    
    /**
     * 处理参数验证异常 - @Valid注解验证失败
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        log.warn("参数验证异常: {}", message);
        return Result.error("参数验证失败: " + message);
    }
    
    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleBindException(BindException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        log.warn("参数绑定异常: {}", message);
        return Result.error("参数绑定失败: " + message);
    }
    
    /**
     * 处理约束验证异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleConstraintViolationException(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String message = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        
        log.warn("约束验证异常: {}", message);
        return Result.error("约束验证失败: " + message);
    }
    
    /**
     * 处理权限拒绝异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<Object> handleAccessDeniedException(AccessDeniedException e) {
        log.warn("权限拒绝异常: {}", e.getMessage());
        return Result.error("权限不足，拒绝访问");
    }
    
    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常: {}", e.getMessage());
        return Result.error("参数错误: " + e.getMessage());
    }
    
    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Object> handleNullPointerException(NullPointerException e) {
        log.error("空指针异常", e);
        return Result.error("系统内部错误，请联系管理员");
    }
    
    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Object> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常", e);
        return Result.error("系统运行异常: " + e.getMessage());
    }
    
    /**
     * 处理JSON格式错误
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Object handleHttpMessageNotReadable(HttpMessageNotReadableException e, WebRequest request) {
        log.warn("JSON格式错误: {}", e.getMessage());

        if (isCommissionRequest(request)) {
            return createCommissionApiResponse(false, "请求数据格式错误", null);
        }
        return Result.error("请求数据格式错误");
    }

    /**
     * 处理不支持的媒体类型
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public Object handleHttpMediaTypeNotSupported(HttpMediaTypeNotSupportedException e, WebRequest request) {
        log.warn("不支持的媒体类型: {}", e.getMessage());

        if (isCommissionRequest(request)) {
            return createCommissionApiResponse(false, "不支持的媒体类型", null);
        }
        return Result.error("不支持的媒体类型");
    }

    /**
     * 处理缺少请求参数
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Object handleMissingServletRequestParameter(MissingServletRequestParameterException e, WebRequest request) {
        log.warn("缺少请求参数: {}", e.getMessage());

        if (isCommissionRequest(request)) {
            return createCommissionApiResponse(false, "缺少必需的请求参数: " + e.getParameterName(), null);
        }
        return Result.error("缺少必需的请求参数: " + e.getParameterName());
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Object> handleException(Exception e) {
        log.error("未知异常", e);
        return Result.error("系统异常，请联系管理员");
    }
}