package com.purchase.common.exception;

/**
 * 资源未找到异常类
 */
public class ResourceNotFoundException extends RuntimeException {
    private String code;
    private String message;

    public ResourceNotFoundException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public ResourceNotFoundException(String message) {
        super(message);
        this.code = "404";
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
} 