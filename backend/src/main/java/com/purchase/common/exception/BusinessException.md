# BusinessException.java

## 文件概述 (File Overview)
`BusinessException.java` 是一个自定义的运行时异常类，旨在表示应用程序中的业务特定错误。它继承自 `RuntimeException`，并包含错误码、描述性消息和可选的附加数据字段，提供了一种结构化的方式来处理和传达业务逻辑失败。

## 核心功能 (Core Functionality)
*   **自定义异常:** 提供一种标准化方式来抛出和捕获与业务规则或逻辑相关的异常。
*   **错误码:** 允许为每个业务错误关联一个特定的数字代码，以便于识别和处理。
*   **描述性消息:** 携带一条人类可读的消息，解释业务错误的性质。
*   **可选数据:** 可以包含与异常相关的附加数据，这对于调试或向客户端提供更多上下文可能很有用。
*   **构造函数:** 提供多个构造函数，以创建具有不同详细程度（代码、消息、数据）的异常。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `code`: `Integer` - 与业务异常关联的错误码。
*   `message`: `String` - 异常的描述性消息。
*   `data`: `Object` - 与异常相关的可选附加数据。

### 构造函数 (Constructors)
*   `BusinessException(Integer code, String message)`: 使用指定的代码和消息创建异常。
*   `BusinessException(String message)`: 使用消息创建异常，代码默认为 500。
*   `BusinessException(Integer code, String message, Object data)`: 使用指定的代码、消息和附加数据创建异常。
*   `BusinessException(String message, Object data)`: 使用消息和附加数据创建异常，代码默认为 500。

### Getter 和 Setter (Getters and Setters)
*   `getCode()`: 获取错误码。
*   `setCode(Integer code)`: 设置错误码。
*   `getMessage()`: 获取异常消息。
*   `setMessage(String message)`: 设置异常消息。
*   `getData()`: 获取附加数据。
*   `setData(Object data)`: 设置附加数据。

## 使用示例 (Usage Examples)

```java
// Example 1: Throwing a BusinessException with a specific code and message
public void processOrder(Order order) {
    if (order.getTotalAmount() <= 0) {
        throw new BusinessException(4001, "订单金额必须大于零");
    }
    // ... further processing
}

// Example 2: Throwing a BusinessException with only a message (code defaults to 500)
public void validateUser(User user) {
    if (userRepository.findByUsername(user.getUsername()) != null) {
        throw new BusinessException("用户名已存在");
    }
    // ... further validation
}

// Example 3: Throwing a BusinessException with additional data
public void updateProductStock(String productId, int quantity) {
    Product product = productRepository.findById(productId)
                                       .orElseThrow(() -> new ResourceNotFoundException("产品未找到: " + productId));
    if (product.getStock() < quantity) {
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("requestedQuantity", quantity);
        errorData.put("availableStock", product.getStock());
        throw new BusinessException(4002, "库存不足", errorData);
    }
    product.setStock(product.getStock() - quantity);
    productRepository.save(product);
}
```

## 注意事项 (Notes)
*   **运行时异常:** 作为 `RuntimeException`，它不需要在方法签名中显式声明，这简化了代码，但需要通过全局异常处理器进行仔细处理。
*   **标准化错误处理:** 此类促进了在整个应用程序中报告业务错误的一致方法。
*   **与 GlobalExceptionHandler 集成:** 通常与 `GlobalExceptionHandler`（或类似机制）结合使用，以捕获这些异常并向客户端返回适当的 HTTP 响应。
*   **默认代码:** 对于未指定代码的构造函数，默认错误码 500 应谨慎使用；通常最好为不同的业务场景定义特定的代码。
