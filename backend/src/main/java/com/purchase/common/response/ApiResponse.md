# ApiResponse.java

## 文件概述 (File Overview)
`ApiResponse.java` 是一个泛型类，用于定义应用程序中统一的 API 响应格式。它封装了操作是否成功 (`success`)、响应消息 (`message`)、返回的数据 (`data`) 和状态码 (`code`)。该类提供了静态工厂方法来方便地创建成功或失败的响应，确保了前后端交互时响应结构的一致性。

## 核心功能 (Core Functionality)
*   **统一响应结构:** 为所有 API 响应提供一个标准化的格式，包含成功状态、消息、数据和代码。
*   **泛型支持:** `T` 泛型参数允许 `data` 字段承载任何类型的数据，提高了类的复用性。
*   **便捷的工厂方法:** 提供了 `success()` 和 `error()` 静态方法，简化了在控制器或服务层构建响应对象的过程。
*   **Lombok 支持:** 使用 `@Data` 注解自动生成 getter、setter、`equals`、`hashCode` 和 `toString` 方法，减少样板代码。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `success`: `Boolean` - 指示 API 操作是否成功。`true` 表示成功，`false` 表示失败。
*   `message`: `String` - 响应消息，通常用于向客户端提供操作结果的描述或错误信息。
*   `data`: `T` - 泛型数据字段，承载 API 返回的实际业务数据。如果操作没有返回数据，则为 `null`。
*   `code`: `Integer` - 响应的状态码。通常 200 表示成功，500 或其他自定义代码表示错误。

### 构造函数 (Constructors)
*   `ApiResponse()`: 无参构造函数。
*   `ApiResponse(Boolean success, String message, T data, Integer code)`: 全参构造函数，用于手动构建 `ApiResponse` 实例。

### 静态工厂方法 (Static Factory Methods)
*   `static <T> ApiResponse<T> success(T data)`:
    *   **参数:** `data` (T) - 成功响应时返回的数据。
    *   **返回值:** `ApiResponse<T>` - 一个表示操作成功的 `ApiResponse` 实例，`success` 为 `true`，`message` 为“操作成功”，`code` 为 200。

*   `static <T> ApiResponse<T> success(T data, String message)`:
    *   **参数:** `data` (T) - 成功响应时返回的数据；`message` (String) - 自定义的成功消息。
    *   **返回值:** `ApiResponse<T>` - 一个表示操作成功的 `ApiResponse` 实例，`success` 为 `true`，`code` 为 200，并包含自定义消息。

*   `static <T> ApiResponse<T> error(String message)`:
    *   **参数:** `message` (String) - 失败响应时的错误消息。
    *   **返回值:** `ApiResponse<T>` - 一个表示操作失败的 `ApiResponse` 实例，`success` 为 `false`，`code` 为 500，并包含提供的错误消息。

*   `static <T> ApiResponse<T> error(Integer code, String message)`:
    *   **参数:** `code` (Integer) - 失败响应时的自定义错误码；`message` (String) - 失败响应时的错误消息。
    *   **返回值:** `ApiResponse<T>` - 一个表示操作失败的 `ApiResponse` 实例，`success` 为 `false`，并包含自定义错误码和错误消息。

## 使用示例 (Usage Examples)

```java
// In a Spring Controller
@RestController
@RequestMapping("/api/v1/users")
public class UserController {

    @Autowired
    private UserService userService;

    @GetMapping("/{id}")
    public ApiResponse<UserDto> getUserById(@PathVariable Long id) {
        UserDto user = userService.findById(id);
        if (user != null) {
            return ApiResponse.success(user, "用户查询成功");
        } else {
            return ApiResponse.error(404, "用户未找到");
        }
    }

    @PostMapping
    public ApiResponse<Void> createUser(@RequestBody UserCreationDto userDto) {
        try {
            userService.createUser(userDto);
            return ApiResponse.success(null, "用户创建成功");
        } catch (BusinessException e) {
            return ApiResponse.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("系统内部错误");
        }
    }
}
```

## 注意事项 (Notes)
*   **与 `Result` 类的关系:** 在 `com.purchase.common.response` 包中，可能存在另一个名为 `Result` 的类，它也提供类似的统一响应结构。需要明确这两个类的使用场景和区别。通常，`ApiResponse` 可能用于更通用的 API 响应，而 `Result` 可能用于更具体的业务操作结果。
*   **错误码约定:** 建议维护一个清晰的错误码约定文档，以便客户端能够理解不同错误码的含义。
*   **Lombok 依赖:** 该类依赖 Lombok 库来自动生成样板代码。确保项目中已正确配置 Lombok 插件和依赖。
*   **不可变性:** 当前设计中，字段是可变的（因为有 setter 方法）。如果需要更严格的不可变性，可以移除 setter 方法，并仅通过构造函数或构建器模式来设置字段。
