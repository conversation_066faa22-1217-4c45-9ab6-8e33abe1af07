# com.purchase.common.response 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.common.response` 包定义了采购系统后端应用程序中用于统一 API 响应和业务操作结果的通用数据结构。它提供了 `ApiResponse` 和 `Result` 两个核心类，旨在确保前后端交互时响应格式的一致性、可预测性和易用性，从而简化客户端的错误处理和数据解析。

## 目录结构概览 (Directory Structure Overview)
*   `ApiResponse.java`: 统一 API 响应格式的泛型类，包含成功状态、消息、数据和状态码。
*   `Result.java`: 业务操作结果的通用封装类，包含状态码、消息和可选数据。
*   `ApiResponse.md`: `ApiResponse.java` 的文档。
*   `Result.md`: `Result.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`ApiResponse` 和 `Result` 类在功能上非常相似，都旨在提供统一的响应格式。它们之间的主要区别可能在于其预期的使用场景或历史演进：

1.  **`ApiResponse`:** 通常用于更广泛的 API 响应场景，其 `success` 字段明确指示操作的成功或失败，这对于 RESTful API 的消费者来说非常直观。它还包含一个 `code` 字段，可以与 HTTP 状态码或自定义业务错误码结合使用。

2.  **`Result`:** 可能更侧重于业务逻辑层面的操作结果封装。它直接使用 `code` 字段来表示操作的状态（例如，200 表示成功，400/500 表示失败），并且也包含 `message` 和 `data`。在某些设计中，`Result` 可能被服务层用于返回业务操作的内部结果，然后由控制器层将其转换为 `ApiResponse` 或直接作为响应返回。

**协作流程总结:**

*   **服务层:** 业务逻辑通常在服务层执行。服务方法在完成操作后，会根据操作结果（成功或失败）返回一个 `Result` 对象。例如，如果用户注册成功，服务可能返回 `Result.success(userDto)`；如果用户名已存在，则返回 `Result.error(400, "用户名已存在")`。

*   **控制器层:** 控制器层接收服务层返回的 `Result` 对象，并将其进一步封装为 `ApiResponse` 对象（如果需要，或者如果 `ApiResponse` 是对外暴露的统一 API 响应格式）。控制器负责将业务结果转换为 HTTP 响应，包括设置正确的 HTTP 状态码和响应体。

*   **全局异常处理器 (`GlobalExceptionHandler`):** 在 `com.purchase.common.exception` 包中的 `GlobalExceptionHandler` 会捕获应用程序中抛出的各种异常（包括业务异常），并将其转换为统一的 `Result` 或 `ApiResponse` 格式返回给客户端。这确保了即使在发生异常时，客户端也能收到一致的错误响应结构。

通过这种方式，`ApiResponse` 和 `Result` 共同确保了应用程序的响应具有清晰、一致的结构，无论操作是成功、失败还是由异常引起，都能够提供标准化的信息，从而简化了客户端的集成和错误处理逻辑。