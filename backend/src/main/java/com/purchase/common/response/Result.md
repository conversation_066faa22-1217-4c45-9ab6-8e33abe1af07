# Result.java

## 文件概述 (File Overview)
`Result.java` 是一个泛型类，用于封装应用程序中操作结果的通用响应。它包含一个状态码 (`code`)、一条消息 (`message`) 和可选的返回数据 (`data`)。该类提供了多种静态工厂方法，用于方便地创建成功或失败的结果对象，旨在为业务操作提供一致且可预测的响应格式。

## 核心功能 (Core Functionality)
*   **通用结果封装:** 提供一个标准化的结构来表示任何业务操作的结果，包括其状态、描述和数据。
*   **泛型支持:** `T` 泛型参数允许 `data` 字段承载任何类型的数据，提高了类的复用性。
*   **便捷的工厂方法:** 提供了多种 `success()` 和 `error()` 静态方法，简化了在服务层或控制器层构建结果对象的过程。
*   **Lombok 支持:** 使用 `@Data` 注解自动生成 getter、setter、`equals`、`hashCode` 和 `toString` 方法，减少样板代码。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `code`: `Integer` - 操作的状态码。通常 200 表示成功，500 或其他自定义代码表示错误。
*   `message`: `String` - 响应消息，通常用于向客户端提供操作结果的描述或错误信息。
*   `data`: `T` - 泛型数据字段，承载操作返回的实际业务数据。如果操作没有返回数据，则为 `null`。

### 构造函数 (Constructors)
*   `Result()`: 无参构造函数。
*   `Result(Integer code, String message)`: 使用指定的代码和消息创建结果实例。
*   `Result(Integer code, String message, T data)`: 使用指定的代码、消息和数据创建结果实例。

### 静态工厂方法 (Static Factory Methods)
*   `static <T> Result<T> success()`:
    *   **参数:** 无。
    *   **返回值:** `Result<T>` - 一个表示操作成功的 `Result` 实例，`code` 为 200，`message` 为“操作成功”，`data` 为 `null`。

*   `static <T> Result<T> success(T data)`:
    *   **参数:** `data` (T) - 成功响应时返回的数据。
    *   **返回值:** `Result<T>` - 一个表示操作成功的 `Result` 实例，`code` 为 200，`message` 为“操作成功”，并包含提供的数据。

*   `static <T> Result<T> success(String message, T data)`:
    *   **参数:** `message` (String) - 自定义的成功消息；`data` (T) - 成功响应时返回的数据。
    *   **返回值:** `Result<T>` - 一个表示操作成功的 `Result` 实例，`code` 为 200，并包含自定义消息和数据。

*   `static <T> Result<T> error(String message)`:
    *   **参数:** `message` (String) - 失败响应时的错误消息。
    *   **返回值:** `Result<T>` - 一个表示操作失败的 `Result` 实例，`code` 为 500，并包含提供的错误消息。

*   `static <T> Result<T> error(Integer code, String message)`:
    *   **参数:** `code` (Integer) - 失败响应时的自定义错误码；`message` (String) - 失败响应时的错误消息。
    *   **返回值:** `Result<T>` - 一个表示操作失败的 `Result` 实例，并包含自定义错误码和错误消息。

*   `static <T> Result<T> error(String code, String message)`:
    *   **参数:** `code` (String) - 失败响应时的字符串类型错误码；`message` (String) - 失败响应时的错误消息。
    *   **返回值:** `Result<T>` - 一个表示操作失败的 `Result` 实例，将字符串错误码转换为 `Integer`，并包含提供的错误消息。

## 使用示例 (Usage Examples)

```java
// In a Service Layer method
public Result<UserDto> registerUser(UserRegistrationDto registrationDto) {
    if (userRepository.findByUsername(registrationDto.getUsername()) != null) {
        return Result.error(400, "用户名已存在");
    }
    User newUser = new User();
    newUser.setUsername(registrationDto.getUsername());
    // ... save user
    UserDto userDto = convertToDto(newUser);
    return Result.success("用户注册成功", userDto);
}

// In a Controller Layer method
@PostMapping("/login")
public Result<String> login(@RequestBody LoginRequest request) {
    try {
        String token = authService.authenticate(request.getUsername(), request.getPassword());
        return Result.success(token);
    } catch (BusinessException e) {
        return Result.error(e.getCode(), e.getMessage());
    } catch (Exception e) {
        return Result.error("登录失败，请稍后再试");
    }
}
```

## 注意事项 (Notes)
*   **与 `ApiResponse` 类的关系:** 在 `com.purchase.common.response` 包中，可能存在另一个名为 `ApiResponse` 的类，它也提供类似的统一响应结构。需要明确这两个类的使用场景和区别。通常，`Result` 可能更侧重于业务操作的结果，而 `ApiResponse` 可能用于更通用的 API 响应。
*   **错误码约定:** 建议维护一个清晰的错误码约定文档，以便客户端能够理解不同错误码的含义。
*   **Lombok 依赖:** 该类依赖 Lombok 库来自动生成样板代码。确保项目中已正确配置 Lombok 插件和依赖。
*   **不可变性:** 当前设计中，字段是可变的（因为有 setter 方法）。如果需要更严格的不可变性，可以移除 setter 方法，并仅通过构造函数或构建器模式来设置字段。
