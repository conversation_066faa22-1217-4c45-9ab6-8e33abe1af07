# MvcConfiguration.java

## 文件概述 (File Overview)
`MvcConfiguration.java` 是一个 Spring `@Configuration` 类，实现了 `WebMvcConfigurer` 接口。它为应用程序提供了统一的 MVC 配置，主要负责处理跨域资源共享 (CORS) 和配置 Jackson 消息转换器，以解决前端 JavaScript 中 `Long` 和 `BigInteger` 类型数据精度丢失的问题。

## 核心功能 (Core Functionality)
*   `addCorsMappings(CorsRegistry registry)`: 配置全局 CORS 设置，允许来自任何源的请求，并指定允许的 HTTP 方法、请求头和凭证。
*   `configureMessageConverters(List<HttpMessageConverter<?>> converters)`: 注册自定义消息转换器，主要是添加 `mappingJackson2HttpMessageConverter`。
*   `mappingJackson2HttpMessageConverter()`: 一个 `@Bean` 方法，用于创建和配置 `ObjectMapper`。它注册了一个 `SimpleModule`，将 `Long` 和 `BigInteger` 类型序列化为字符串，以防止在前端 JavaScript 中出现精度丢失。同时，它还注册了 `JavaTimeModule` 以正确处理 Java 8 日期时间类型，并禁用将日期序列化为时间戳。

## 接口说明 (Interface Description)

### `addCorsMappings(CorsRegistry registry)`
*   **参数:** `registry` (CorsRegistry) - 用于配置 CORS 映射的注册表。
*   **返回值:** `void`
*   **业务逻辑:** 配置 CORS，允许所有来源 (`*`)、常用的 HTTP 方法 (GET, POST, PUT, DELETE, OPTIONS)、所有请求头，并支持凭证，预检请求的最大缓存时间为 3600 秒。

### `configureMessageConverters(List<HttpMessageConverter<?>> converters)`
*   **参数:** `converters` (List<HttpMessageConverter<?>>) - 要配置的消息转换器列表。
*   **返回值:** `void`
*   **业务逻辑:** 将自定义的 `MappingJackson2HttpMessageConverter` 添加到 Spring MVC 使用的消息转换器列表中。

### `mappingJackson2HttpMessageConverter()`
*   **参数:** 无。
*   **返回值:** `MappingJackson2HttpMessageConverter` - 一个已配置的 Jackson 消息转换器。
*   **业务逻辑:**
    1.  创建一个 `MappingJackson2HttpMessageConverter` 实例。
    2.  创建一个 `ObjectMapper`。
    3.  注册一个 `SimpleModule`，用于将 `Long`、`Long.TYPE` 和 `BigInteger` 类型序列化为 `String`。这对于防止将大数字发送到 JavaScript 客户端时的数据丢失至关重要。
    4.  注册 `JavaTimeModule` 以正确处理 `java.time` 包中的类型（例如 `LocalDateTime`, `Instant`）。
    5.  禁用 `SerializationFeature.WRITE_DATES_AS_TIMESTAMPS`，以便将日期序列化为 ISO 8601 字符串而不是时间戳。
    6.  将配置好的 `ObjectMapper` 设置给转换器。

## 使用示例 (Usage Examples)
这是一个配置类，通常由 Spring Boot 自动加载和应用。开发者通常不需要直接调用其方法。其效果体现在应用程序如何处理 CORS 和 JSON 序列化/反序列化。

## 注意事项 (Notes)
*   **精度丢失:** 该配置专门解决了 `Long` 和 `BigInteger` 值在传输到 JavaScript 客户端时常见的精度丢失问题，通过将其序列化为字符串来避免。
*   **日期处理:** 确保 Java 8 日期/时间对象能够正确地进行 JSON 序列化。
*   **CORS:** 当前的 CORS 配置非常宽松 (`allowedOriginPatterns("*")`)。在生产环境中，出于安全考虑，应将其限制为已知的来源。
*   **`@Primary` 注解:** 当存在多个 `WebMvcConfigurer` 实现时，此注解指示应优先使用此 `MvcConfiguration` bean。
