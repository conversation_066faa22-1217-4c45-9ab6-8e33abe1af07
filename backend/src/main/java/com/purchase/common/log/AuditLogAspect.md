# AuditLogAspect.java

## 文件概述 (File Overview)
`AuditLogAspect.java` 是一个 Spring AOP 切面，用于实现系统操作的审计日志功能。它通过拦截带有 `@AuditLog` 注解的方法，自动记录操作的详细信息，包括操作类型、描述、操作人、请求参数、响应结果、执行时间以及可能发生的异常。该切面利用 `ProceedingJoinPoint` 实现环绕通知，确保在方法执行前后都能捕获所需信息，并使用 `ObjectMapper` 将审计信息序列化为 JSON 格式输出到专门的审计日志中。

## 核心功能 (Core Functionality)
*   **切点定义:** 定义了一个切点，匹配所有被 `@AuditLog` 注解标记的方法。
*   **环绕通知:** 在目标方法执行前后执行逻辑，捕获方法执行的各个阶段（开始、成功、失败）。
*   **审计信息构建:** 收集丰富的审计信息，包括：
    *   操作时间、类型、描述和业务模块。
    *   被调用方法的类名和方法名。
    *   当前认证用户的用户名和角色（如果存在）。
    *   HTTP 请求信息（URI、方法、客户端 IP、User-Agent）。
    *   方法参数（可选择性记录，并进行敏感信息清理）。
    *   方法执行结果（可选择性记录，并进行敏感信息清理）。
    *   方法执行时间。
    *   异常信息（如果发生）。
*   **敏感信息清理:** 提供了 `sanitizeParameters` 和 `sanitizeResult` 方法，用于过滤或截断日志中可能包含的敏感数据（如密码、token）或过长的内容，以保护隐私和控制日志大小。
*   **客户端 IP 获取:** `getClientIpAddress` 方法用于从 HTTP 请求头中获取客户端的真实 IP 地址，支持多种代理转发头。
*   **日志记录:** 使用 `LoggerFactory` 获取专门的审计日志记录器 (`auditLogger`) 和普通日志记录器 (`logger`)，将审计信息以 JSON 格式记录到日志中。
*   **业务上下文设置:** 在 `finally` 块中，通过 `LogContext` 设置业务操作类型和业务模块，可能用于后续的日志追踪或上下文传递。

## 接口说明 (Interface Description)

### `auditLogPointcut()`
*   **类型:** `@Pointcut` 方法。
*   **业务逻辑:** 定义了一个切点表达式 `@annotation(com.purchase.common.log.AuditLog)`，表示所有被 `com.purchase.common.log.AuditLog` 注解标记的方法都将成为这个切面的目标。

### `around(ProceedingJoinPoint joinPoint)`
*   **类型:** `@Around` 通知方法。
*   **参数:** `joinPoint` (ProceedingJoinPoint) - 表示被拦截的方法执行点。
*   **返回值:** `Object` - 目标方法的执行结果。
*   **抛出:** `Throwable` - 目标方法可能抛出的任何异常。
*   **业务逻辑:**
    1.  记录方法开始时间。
    2.  获取被拦截方法的 `AuditLog` 注解信息。
    3.  调用 `buildAuditInfo` 方法构建审计日志的基础信息。
    4.  执行 `joinPoint.proceed()` 调用目标方法。
    5.  如果目标方法成功执行，根据 `auditLog.logResult()` 配置，记录并清理返回结果。
    6.  如果目标方法抛出异常，捕获异常，并根据 `auditLog.logException()` 配置记录异常信息，同时将 `success` 标志设置为 `false`。
    7.  在 `finally` 块中，计算方法执行时间，设置结束时间，并根据执行结果（成功或失败）将完整的审计信息序列化为 JSON 字符串，然后使用 `auditLogger` 记录。
    8.  通过 `LogContext.setBusinessInfo` 设置业务上下文信息。

### `buildAuditInfo(ProceedingJoinPoint joinPoint, AuditLog auditLog, long startTime)`
*   **类型:** 私有辅助方法。
*   **参数:** `joinPoint` (ProceedingJoinPoint), `auditLog` (AuditLog), `startTime` (long)。
*   **返回值:** `Map<String, Object>` - 包含审计日志所有详细信息的 Map。
*   **业务逻辑:** 负责收集和组装审计日志的各个字段，包括基本信息、用户信息、请求信息和方法参数（如果 `logParams` 为 true）。

### `sanitizeParameters(Object[] args)`
*   **类型:** 私有辅助方法。
*   **参数:** `args` (Object[]) - 方法的参数数组。
*   **返回值:** `Object` - 清理后的参数 Map 或字符串。
*   **业务逻辑:** 遍历方法参数，对敏感对象（如包含“password”、“token”等字符串的对象，或 `HttpServletRequest`/`HttpServletResponse`）进行脱敏处理（替换为“***”）。对于非敏感参数，将其序列化为 JSON 字符串，并限制长度以避免日志过大。

### `sanitizeResult(Object result)`
*   **类型:** 私有辅助方法。
*   **参数:** `result` (Object) - 方法的返回结果。
*   **返回值:** `Object` - 清理后的结果字符串。
*   **业务逻辑:** 检查返回结果是否为敏感对象。如果是，则脱敏为“***”；否则，将其序列化为 JSON 字符串，并限制长度。

### `isSensitiveObject(Object obj)`
*   **类型:** 私有辅助方法。
*   **参数:** `obj` (Object) - 要检查的对象。
*   **返回值:** `boolean` - 如果对象被认为是敏感的，则为 `true`。
*   **业务逻辑:** 判断一个对象是否包含敏感信息（通过其 `toString()` 方法的字符串表示是否包含“password”、“token”、“secret”、“key”等关键字），或是否是 `HttpServletRequest`/`HttpServletResponse` 实例。

### `getClientIpAddress(HttpServletRequest request)`
*   **类型:** 私有辅助方法。
*   **参数:** `request` (HttpServletRequest) - HTTP 请求对象。
*   **返回值:** `String` - 客户端的 IP 地址。
*   **业务逻辑:** 尝试从一系列常见的 HTTP 请求头（如 `X-Forwarded-For`, `X-Real-IP`）中获取客户端的真实 IP 地址。如果这些头不存在或为空，则回退到 `request.getRemoteAddr()`。

## 使用示例 (Usage Examples)
`AuditLogAspect` 是一个 Spring AOP 组件，它通过 `@Aspect` 和 `@Component` 注解被 Spring 容器自动发现和管理。开发人员不需要直接调用其方法。它的功能通过在需要审计的方法上使用 `@AuditLog` 注解来激活。

```java
// Example: A service method that will be audited by AuditLogAspect
@Service
public class UserService {

    @AuditLog(type = OperationType.ADD, description = "创建新用户", bizType = BizType.USER_MANAGEMENT)
    public User createUser(UserCreationDto userDto) {
        // ... business logic to create user
        User newUser = new User();
        newUser.setUsername(userDto.getUsername());
        // ... set other properties
        return newUser;
    }

    @AuditLog(type = OperationType.DELETE, description = "删除用户", bizType = BizType.USER_MANAGEMENT)
    public void deleteUser(Long userId) {
        // ... business logic to delete user
    }
}
```

## 注意事项 (Notes)
*   **AOP 配置:** 确保 Spring Boot 应用程序已启用 AOP（通常默认启用）。
*   **日志配置:** 审计日志的输出路径和格式需要通过 `logback-spring.xml` 或 `application.yml` 等日志配置文件进行适当配置，以确保审计日志能够独立存储和管理。
*   **性能影响:** 尽管切面设计考虑了性能（例如，参数和结果的长度限制），但在高并发或日志量巨大的场景下，仍需监控其对应用程序性能的影响。
*   **敏感信息处理:** 敏感信息清理逻辑依赖于字符串匹配。对于更严格的安全要求，可能需要更复杂的脱敏策略，例如使用加密或更精细的字段级控制。
*   **`LogContext`:** `LogContext` 的使用表明可能存在一个线程局部变量，用于在整个请求生命周期中传递日志上下文信息，这对于分布式追踪和日志关联非常有用。
*   **反射使用:** `createCommissionApiResponse` 方法中使用了反射，这在某些情况下可能导致运行时错误，如果目标类或方法签名发生变化，需要谨慎维护。
