package com.purchase.common.log;

import java.lang.annotation.*;

/**
 * 审计日志注解
 * 用于标记需要记录审计日志的方法，支持自动记录用户操作、数据变更等信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AuditLog {
    
    /**
     * 操作描述
     * 
     * @return 操作描述
     */
    String value() default "";
    
    /**
     * 操作类型
     * 
     * @return 操作类型
     */
    OperationType operationType() default OperationType.OTHER;
    
    /**
     * 业务模块
     * 
     * @return 业务模块
     */
    BusinessModule businessModule() default BusinessModule.COMMON;
    
    /**
     * 是否记录请求参数
     * 
     * @return 是否记录请求参数
     */
    boolean logParams() default true;
    
    /**
     * 是否记录返回结果
     * 
     * @return 是否记录返回结果
     */
    boolean logResult() default false;
    
    /**
     * 是否记录异常信息
     * 
     * @return 是否记录异常信息
     */
    boolean logException() default true;
    
    /**
     * 操作类型枚举
     */
    enum OperationType {
        /**
         * 创建操作
         */
        CREATE("创建"),
        
        /**
         * 更新操作
         */
        UPDATE("更新"),
        
        /**
         * 删除操作
         */
        DELETE("删除"),
        
        /**
         * 查询操作
         */
        QUERY("查询"),
        
        /**
         * 登录操作
         */
        LOGIN("登录"),
        
        /**
         * 登出操作
         */
        LOGOUT("登出"),
        
        /**
         * 审核操作
         */
        AUDIT("审核"),
        
        /**
         * 导入操作
         */
        IMPORT("导入"),
        
        /**
         * 导出操作
         */
        EXPORT("导出"),
        
        /**
         * 上传操作
         */
        UPLOAD("上传"),
        
        /**
         * 下载操作
         */
        DOWNLOAD("下载"),
        
        /**
         * 其他操作
         */
        OTHER("其他");
        
        private final String description;
        
        OperationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 业务模块枚举
     */
    enum BusinessModule {
        /**
         * 通用模块
         */
        COMMON("通用"),
        
        /**
         * 用户管理
         */
        USER("用户管理"),
        
        /**
         * 采购需求
         */
        REQUIREMENT("采购需求"),
        
        /**
         * 竞价管理
         */
        BIDDING("竞价管理"),
        
        /**
         * 订单管理
         */
        ORDER("订单管理"),
        
        /**
         * 产品管理
         */
        PRODUCT("产品管理"),
        
        /**
         * 货代服务
         */
        FORWARDING("货代服务"),
        
        /**
         * 结算管理
         */
        SETTLEMENT("结算管理"),
        
        /**
         * 消息管理
         */
        MESSAGE("消息管理"),
        
        /**
         * 文件管理
         */
        FILE("文件管理"),
        
        /**
         * 系统管理
         */
        SYSTEM("系统管理");
        
        private final String description;
        
        BusinessModule(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
} 