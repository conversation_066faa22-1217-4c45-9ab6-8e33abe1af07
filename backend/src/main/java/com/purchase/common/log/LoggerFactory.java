package com.purchase.common.log;

import org.slf4j.Logger;

/**
 * 日志工厂类
 * 提供不同类型的日志记录器，用于区分业务日志、审计日志、性能日志等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class LoggerFactory {
    
    /**
     * 获取业务日志记录器
     * 用于记录业务操作相关的日志信息
     * 
     * @param clazz 调用类
     * @return 业务日志记录器
     */
    public static Logger getBusinessLogger(Class<?> clazz) {
        return org.slf4j.LoggerFactory.getLogger("com.purchase.business." + clazz.getSimpleName());
    }
    
    /**
     * 获取审计日志记录器
     * 用于记录用户操作、权限变更、数据修改等审计信息
     * 
     * @param clazz 调用类
     * @return 审计日志记录器
     */
    public static Logger getAuditLogger(Class<?> clazz) {
        return org.slf4j.LoggerFactory.getLogger("com.purchase.audit." + clazz.getSimpleName());
    }
    
    /**
     * 获取性能监控日志记录器
     * 用于记录方法执行时间、数据库查询性能等信息
     * 
     * @param clazz 调用类
     * @return 性能监控日志记录器
     */
    public static Logger getPerformanceLogger(Class<?> clazz) {
        return org.slf4j.LoggerFactory.getLogger("com.purchase.performance." + clazz.getSimpleName());
    }
    
    /**
     * 获取普通日志记录器
     * 用于记录一般的应用程序日志
     * 
     * @param clazz 调用类
     * @return 普通日志记录器
     */
    public static Logger getLogger(Class<?> clazz) {
        return org.slf4j.LoggerFactory.getLogger(clazz);
    }
} 