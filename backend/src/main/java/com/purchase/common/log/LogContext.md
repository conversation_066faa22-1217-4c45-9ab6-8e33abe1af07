# LogContext.java

## 文件概述 (File Overview)
`LogContext.java` 是一个日志上下文工具类，它利用 SLF4J 的 Mapped Diagnostic Context (MDC) 来管理和传递与当前请求或操作相关的上下文信息。通过将诸如追踪 ID、用户 ID、用户名、请求 URI、操作类型等信息放入 MDC，可以实现在整个请求处理链路中（包括跨线程异步操作）日志的关联和追踪，极大地提高了日志的可读性和问题排查效率。

## 核心功能 (Core Functionality)
*   **MDC 管理:** 提供了一系列静态方法，用于向 MDC 中设置、获取和清除键值对信息。
*   **请求链路追踪:** 通过 `setTraceId` 方法设置唯一的追踪 ID，即使在分布式或异步环境中也能关联同一请求的所有日志。
*   **用户信息传递:** 允许将当前操作的用户 ID、用户名和角色信息放入 MDC，方便在日志中记录操作者。
*   **请求信息传递:** 能够记录请求的 URI、方法和客户端 IP，为日志提供请求上下文。
*   **业务信息传递:** 支持设置操作类型和业务模块，进一步细化日志的业务含义。
*   **自动生成追踪 ID:** 如果未提供追踪 ID，`setTraceId` 方法会自动生成一个 UUID 作为追踪 ID。
*   **清除上下文:** `clear()` 方法用于清除当前线程 MDC 中的所有信息，`remove(String key)` 用于清除指定键的信息，这对于防止内存泄漏和确保线程隔离至关重要。

## 接口说明 (Interface Description)

### 常量 (Constants)
*   `TRACE_ID`: `String` - 追踪 ID 的键名。
*   `USER_ID`: `String` - 用户 ID 的键名。
*   `USERNAME`: `String` - 用户名的键名。
*   `USER_ROLE`: `String` - 用户角色的键名。
*   `REQUEST_URI`: `String` - 请求 URI 的键名。
*   `REQUEST_METHOD`: `String` - 请求方法的键名。
*   `CLIENT_IP`: `String` - 客户端 IP 的键名。
*   `OPERATION_TYPE`: `String` - 操作类型的键名。
*   `BUSINESS_MODULE`: `String` - 业务模块的键名。

### 静态方法 (Static Methods)
*   `setTraceId(String traceId)`:
    *   **参数:** `traceId` (String) - 要设置的追踪 ID。如果为 `null` 或空，则自动生成一个 UUID。
    *   **返回值:** `void`
    *   **业务逻辑:** 将追踪 ID 放入 MDC。如果传入的 `traceId` 无效，则生成一个新的 UUID。

*   `getTraceId()`:
    *   **参数:** 无。
    *   **返回值:** `String` - 当前线程的追踪 ID。
    *   **业务逻辑:** 从 MDC 中获取追踪 ID。

*   `setUserInfo(String userId, String username, String userRole)`:
    *   **参数:** `userId` (String), `username` (String), `userRole` (String)。
    *   **返回值:** `void`
    *   **业务逻辑:** 将用户 ID、用户名和用户角色（如果非空）放入 MDC。

*   `setRequestInfo(String requestUri, String requestMethod, String clientIp)`:
    *   **参数:** `requestUri` (String), `requestMethod` (String), `clientIp` (String)。
    *   **返回值:** `void`
    *   **业务逻辑:** 将请求 URI、请求方法和客户端 IP（如果非空）放入 MDC。

*   `setBusinessInfo(String operationType, String businessModule)`:
    *   **参数:** `operationType` (String) - 操作类型，`businessModule` (String) - 业务模块。
    *   **返回值:** `void`
    *   **业务逻辑:** 将操作类型和业务模块（如果非空）放入 MDC。

*   `clear()`:
    *   **参数:** 无。
    *   **返回值:** `void`
    *   **业务逻辑:** 清除当前线程 MDC 中的所有键值对。在请求处理结束时调用此方法至关重要。

*   `remove(String key)`:
    *   **参数:** `key` (String) - 要移除的键名。
    *   **返回值:** `void`
    *   **业务逻辑:** 从当前线程 MDC 中移除指定键的映射。

*   `getUserId()`:
    *   **参数:** 无。
    *   **返回值:** `String` - 当前线程的用户 ID。
    *   **业务逻辑:** 从 MDC 中获取用户 ID。

*   `getUsername()`:
    *   **参数:** 无。
    *   **返回值:** `String` - 当前线程的用户名。
    *   **业务逻辑:** 从 MDC 中获取用户名。

*   `getUserRole()`:
    *   **参数:** 无。
    *   **返回值:** `String` - 当前线程的用户角色。
    *   **业务逻辑:** 从 MDC 中获取用户角色。

## 使用示例 (Usage Examples)
`LogContext` 通常与 Spring 的拦截器 (`HandlerInterceptor`) 或过滤器 (`Filter`) 结合使用，以在请求开始时设置上下文信息，并在请求结束时清除。

```java
// Example in a Spring Interceptor (preHandle method)
@Override
public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
    String traceId = request.getHeader("X-Trace-ID"); // Attempt to get trace ID from header
    LogContext.setTraceId(traceId);
    
    // Assume user details are available from Spring Security context
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null && authentication.isAuthenticated() && !"anonymousUser".equals(authentication.getName())) {
        // In a real app, you might get userId and userRole from custom UserDetails
        String userId = "someUserId"; 
        String username = authentication.getName();
        String userRole = authentication.getAuthorities().stream()
                                    .map(GrantedAuthority::getAuthority)
                                    .collect(Collectors.joining(","));
        LogContext.setUserInfo(userId, username, userRole);
    }
    
    LogContext.setRequestInfo(request.getRequestURI(), request.getMethod(), request.getRemoteAddr());
    
    return true;
}

// Example in a Spring Interceptor (afterCompletion method)
@Override
public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
    LogContext.clear(); // Crucial to clear MDC to prevent memory leaks in thread pools
}

// Example of using LogContext in a service method for business-specific logging
@Service
public class OrderService {
    private static final Logger logger = LoggerFactory.getLogger(OrderService.class);

    public Order createOrder(OrderDto orderDto) {
        LogContext.setBusinessInfo("CREATE", "ORDER_PROCESSING");
        logger.info("Creating order for user: {}", LogContext.getUsername());
        // ... business logic
        return new Order();
    }
}
```

## 注意事项 (Notes)
*   **MDC 的线程局部性:** MDC 是基于 `ThreadLocal` 实现的，这意味着每个线程都有自己独立的 MDC 副本。这对于在多线程环境中（如 Web 服务器处理并发请求）隔离日志上下文至关重要。
*   **清除 MDC:** 在请求处理结束时（例如，在 Spring 拦截器的 `afterCompletion` 方法中或 Servlet 过滤器的 `finally` 块中），务必调用 `LogContext.clear()`。否则，在线程池中重用线程时，旧的 MDC 信息可能会泄漏到新的请求中，导致日志混乱和潜在的内存泄漏。
*   **日志配置:** 为了使 MDC 中的信息能够打印到日志中，需要配置日志框架（如 Logback 或 Log4j2）的布局模式。例如，在 `logback-spring.xml` 中，可以使用 `%X{traceId}`、`%X{userId}` 等模式来引用 MDC 中的值。
*   **异步操作:** 对于跨线程的异步操作（如使用 `CompletableFuture` 或 `@Async`），需要确保 MDC 上下文能够正确地从父线程传递到子线程。Spring 提供了 `RequestContextHolder` 和 `InheritableThreadLocal` 等机制来辅助实现这一点，或者需要手动传递 MDC 上下文。
