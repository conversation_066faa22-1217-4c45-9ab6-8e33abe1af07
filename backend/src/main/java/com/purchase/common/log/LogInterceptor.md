# LogInterceptor.java

## 文件概述 (File Overview)
`LogInterceptor.java` 是一个 Spring MVC 拦截器，实现了 `HandlerInterceptor` 接口。它旨在对进入应用程序的每个 HTTP 请求进行集中式日志记录。该拦截器在请求处理的 `preHandle` 阶段记录请求的详细信息（如方法、URI、参数、头信息），并在 `afterCompletion` 阶段记录响应状态、执行时间以及任何发生的异常。它还利用 `LogContext` 来设置和清除请求链路追踪信息，并对敏感数据进行脱敏处理。

## 核心功能 (Core Functionality)
*   **请求前处理 (`preHandle`):**
    *   记录请求开始时间。
    *   从请求头获取或生成追踪 ID，并设置到 `LogContext`。
    *   从 Spring Security 上下文获取用户信息（用户名、角色），并设置到 `LogContext`。
    *   获取客户端 IP 地址，并设置到 `LogContext`。
    *   记录详细的 HTTP 请求日志，包括方法、URI、查询字符串、客户端 IP、User-Agent、内容类型、内容长度、请求头（脱敏）和请求参数（脱敏）。
*   **请求后处理 (`afterCompletion`):**
    *   计算请求的执行时间。
    *   记录详细的 HTTP 响应日志，包括方法、URI、状态码、执行时间、内容类型和异常信息。
    *   根据响应状态码（5xx, 4xx, 2xx/3xx）记录不同级别的日志（ERROR, WARN, INFO）。
    *   检测并记录慢请求（执行时间超过阈值）。
    *   **关键:** 清除 `LogContext` 中的所有 MDC 信息，以防止线程池中的数据泄漏。
*   **敏感信息处理:** 提供了 `isSensitiveHeader` 和 `isSensitiveParameter` 方法，用于识别并脱敏日志中的敏感请求头（如 `Authorization`, `Cookie`, `Token`）和敏感请求参数（如 `password`, `token`, `secret`, `key`）。
*   **客户端 IP 获取:** `getClientIpAddress` 方法用于从 HTTP 请求头中获取客户端的真实 IP 地址，支持多种代理转发头。
*   **日志分类:** 使用 `LoggerFactory` 获取 `businessLogger` 和 `logger`，将 HTTP 请求/响应日志记录到业务日志中，慢请求记录到性能日志中。

## 接口说明 (Interface Description)

### `preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)`
*   **类型:** `HandlerInterceptor` 接口方法。
*   **参数:** `request` (HttpServletRequest), `response` (HttpServletResponse), `handler` (Object)。
*   **返回值:** `boolean` - 始终返回 `true`，表示继续处理请求。
*   **业务逻辑:** 在请求到达控制器之前执行。主要负责初始化日志上下文（追踪 ID、用户信息、请求信息）和记录请求开始日志。

### `afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)`
*   **类型:** `HandlerInterceptor` 接口方法。
*   **参数:** `request` (HttpServletRequest), `response` (HttpServletResponse), `handler` (Object), `ex` (Exception)。
*   **返回值:** `void`
*   **业务逻辑:** 在视图渲染之后（或请求处理完成，无论是否发生异常）执行。主要负责计算请求执行时间，记录响应日志（包括异常信息和慢请求检测），并在 `finally` 块中清除 `LogContext`。

### `logRequest(HttpServletRequest request)`
*   **类型:** 私有辅助方法。
*   **参数:** `request` (HttpServletRequest)。
*   **返回值:** `void`
*   **业务逻辑:** 收集并记录 HTTP 请求的详细信息，包括方法、URI、查询字符串、客户端 IP、User-Agent、内容类型、内容长度、脱敏后的请求头和请求参数。使用 `businessLogger` 以 JSON 格式记录。

### `logResponse(HttpServletRequest request, HttpServletResponse response, long executionTime, Exception exception)`
*   **类型:** 私有辅助方法。
*   **参数:** `request` (HttpServletRequest), `response` (HttpServletResponse), `executionTime` (long), `exception` (Exception)。
*   **返回值:** `void`
*   **业务逻辑:** 收集并记录 HTTP 响应的详细信息，包括方法、URI、状态码、执行时间、内容类型和异常信息。根据响应状态码使用不同级别的 `businessLogger` 记录。如果执行时间超过 3 秒，则使用 `performanceLogger` 记录为慢请求。

### `getClientIpAddress(HttpServletRequest request)`
*   **类型:** 私有辅助方法。
*   **参数:** `request` (HttpServletRequest)。
*   **返回值:** `String` - 客户端的 IP 地址。
*   **业务逻辑:** 尝试从一系列常见的 HTTP 请求头（如 `X-Forwarded-For`, `X-Real-IP`）中获取客户端的真实 IP 地址。如果这些头不存在或为空，则回退到 `request.getRemoteAddr()`。

### `isSensitiveHeader(String headerName)`
*   **类型:** 私有辅助方法。
*   **参数:** `headerName` (String)。
*   **返回值:** `boolean` - 如果请求头名称包含敏感关键字（如 `authorization`, `cookie`, `token`），则为 `true`。
*   **业务逻辑:** 判断给定的请求头名称是否为敏感信息，用于日志脱敏。

### `isSensitiveParameter(String paramName)`
*   **类型:** 私有辅助方法。
*   **参数:** `paramName` (String)。
*   **返回值:** `boolean` - 如果参数名称包含敏感关键字（如 `password`, `token`, `secret`, `key`），则为 `true`。
*   **业务逻辑:** 判断给定的参数名称是否为敏感信息，用于日志脱敏。

## 使用示例 (Usage Examples)
`LogInterceptor` 是一个 Spring 组件，需要通过 `WebMvcConfigurer` 进行注册才能生效。

```java
// In a Spring configuration class (e.g., WebConfig.java)
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private final LogInterceptor logInterceptor;

    public WebConfig(LogInterceptor logInterceptor) {
        this.logInterceptor = logInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(logInterceptor).addPathPatterns("/**"); // Apply to all paths
    }
}

// No direct usage in controllers or services; it intercepts automatically.
```

## 注意事项 (Notes)
*   **拦截器注册:** 必须在 Spring 配置中注册 `LogInterceptor` 才能使其生效。
*   **性能影响:** 拦截器会处理每个 HTTP 请求，因此在生产环境中需要监控其对性能的影响。日志记录的详细程度应根据实际需求进行权衡。
*   **敏感数据处理:** 尽管拦截器尝试脱敏敏感信息，但仍需确保所有潜在的敏感数据都得到适当处理，以符合安全和隐私要求。
*   **MDC 清理:** `afterCompletion` 方法中的 `LogContext.clear()` 调用至关重要，它确保了线程池中线程的 MDC 状态在请求结束后被重置，防止数据交叉污染。
*   **日志级别:** 根据响应状态码动态调整日志级别（INFO, WARN, ERROR）有助于快速识别和分类问题。
*   **慢请求阈值:** 慢请求的阈值（当前为 3000 毫秒）应根据应用程序的性能基线和业务需求进行调整。