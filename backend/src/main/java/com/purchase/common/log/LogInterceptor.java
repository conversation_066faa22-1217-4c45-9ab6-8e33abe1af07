package com.purchase.common.log;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 日志拦截器
 * 用于自动记录HTTP请求的日志信息，包括请求参数、响应状态、执行时间等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class LogInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(LogInterceptor.class);
    private static final Logger businessLogger = LoggerFactory.getBusinessLogger(LogInterceptor.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 请求开始时间的属性名
     */
    private static final String START_TIME_ATTRIBUTE = "startTime";
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 记录请求开始时间
        request.setAttribute(START_TIME_ATTRIBUTE, System.currentTimeMillis());
        
        // 设置追踪ID
        String traceId = request.getHeader("X-Trace-Id");
        LogContext.setTraceId(traceId);
        
        // 获取用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && !"anonymousUser".equals(authentication.getName())) {
            String username = authentication.getName();
            String userRole = authentication.getAuthorities().toString();
            LogContext.setUserInfo(null, username, userRole);
        }
        
        // 设置请求信息
        String clientIp = getClientIpAddress(request);
        LogContext.setRequestInfo(request.getRequestURI(), request.getMethod(), clientIp);
        
        // 记录请求日志
        logRequest(request);
        
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        try {
            // 计算执行时间
            Long startTime = (Long) request.getAttribute(START_TIME_ATTRIBUTE);
            long executionTime = startTime != null ? System.currentTimeMillis() - startTime : 0;
            
            // 记录响应日志
            logResponse(request, response, executionTime, ex);
            
        } finally {
            // 清除MDC信息
            LogContext.clear();
        }
    }
    
    /**
     * 记录请求日志
     * 
     * @param request HTTP请求
     */
    private void logRequest(HttpServletRequest request) {
        try {
            Map<String, Object> requestInfo = new HashMap<>();
            requestInfo.put("method", request.getMethod());
            requestInfo.put("uri", request.getRequestURI());
            requestInfo.put("queryString", request.getQueryString());
            requestInfo.put("clientIp", LogContext.CLIENT_IP);
            requestInfo.put("userAgent", request.getHeader("User-Agent"));
            requestInfo.put("contentType", request.getContentType());
            requestInfo.put("contentLength", request.getContentLength());
            
            // 记录请求头（排除敏感信息）
            Map<String, String> headers = new HashMap<>();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                if (!isSensitiveHeader(headerName)) {
                    headers.put(headerName, request.getHeader(headerName));
                }
            }
            requestInfo.put("headers", headers);
            
            // 记录请求参数
            Map<String, String[]> parameters = request.getParameterMap();
            if (!parameters.isEmpty()) {
                Map<String, Object> params = new HashMap<>();
                for (Map.Entry<String, String[]> entry : parameters.entrySet()) {
                    String key = entry.getKey();
                    String[] values = entry.getValue();
                    if (!isSensitiveParameter(key)) {
                        params.put(key, values.length == 1 ? values[0] : values);
                    } else {
                        params.put(key, "***");
                    }
                }
                requestInfo.put("parameters", params);
            }
            
            businessLogger.info("HTTP请求开始: {}", objectMapper.writeValueAsString(requestInfo));
            
        } catch (Exception e) {
            logger.warn("记录请求日志失败", e);
        }
    }
    
    /**
     * 记录响应日志
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @param executionTime 执行时间（毫秒）
     * @param exception 异常信息
     */
    private void logResponse(HttpServletRequest request, HttpServletResponse response, long executionTime, Exception exception) {
        try {
            Map<String, Object> responseInfo = new HashMap<>();
            responseInfo.put("method", request.getMethod());
            responseInfo.put("uri", request.getRequestURI());
            responseInfo.put("status", response.getStatus());
            responseInfo.put("executionTime", executionTime + "ms");
            responseInfo.put("contentType", response.getContentType());
            
            if (exception != null) {
                responseInfo.put("exception", exception.getClass().getSimpleName());
                responseInfo.put("exceptionMessage", exception.getMessage());
                businessLogger.error("HTTP请求异常: {}", objectMapper.writeValueAsString(responseInfo), exception);
            } else {
                // 根据响应状态记录不同级别的日志
                if (response.getStatus() >= 500) {
                    businessLogger.error("HTTP请求完成（服务器错误）: {}", objectMapper.writeValueAsString(responseInfo));
                } else if (response.getStatus() >= 400) {
                    businessLogger.warn("HTTP请求完成（客户端错误）: {}", objectMapper.writeValueAsString(responseInfo));
                } else {
                    businessLogger.info("HTTP请求完成: {}", objectMapper.writeValueAsString(responseInfo));
                }
            }
            
            // 记录慢请求
            if (executionTime > 3000) { // 超过3秒的请求
                Logger performanceLogger = LoggerFactory.getPerformanceLogger(LogInterceptor.class);
                performanceLogger.warn("慢请求检测: {} {} 执行时间: {}ms", request.getMethod(), request.getRequestURI(), executionTime);
            }
            
        } catch (Exception e) {
            logger.warn("记录响应日志失败", e);
        }
    }
    
    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headers = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String header : headers) {
            String ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // 多级代理的情况，取第一个IP
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 判断是否为敏感请求头
     * 
     * @param headerName 请求头名称
     * @return 是否为敏感请求头
     */
    private boolean isSensitiveHeader(String headerName) {
        String lowerCaseName = headerName.toLowerCase();
        return lowerCaseName.contains("authorization") || 
               lowerCaseName.contains("cookie") || 
               lowerCaseName.contains("token");
    }
    
    /**
     * 判断是否为敏感参数
     * 
     * @param paramName 参数名称
     * @return 是否为敏感参数
     */
    private boolean isSensitiveParameter(String paramName) {
        String lowerCaseName = paramName.toLowerCase();
        return lowerCaseName.contains("password") || 
               lowerCaseName.contains("token") || 
               lowerCaseName.contains("secret") ||
               lowerCaseName.contains("key");
    }
} 