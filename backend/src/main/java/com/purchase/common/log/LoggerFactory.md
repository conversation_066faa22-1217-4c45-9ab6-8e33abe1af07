# LoggerFactory.java

## 文件概述 (File Overview)
`LoggerFactory.java` 是一个自定义的日志工厂类，它封装了 SLF4J 的 `org.slf4j.LoggerFactory`，并提供了便捷的方法来获取不同类别的日志记录器。通过这种方式，应用程序可以根据日志的用途（如业务日志、审计日志、性能日志或通用日志）将日志输出到不同的文件或目的地，从而实现日志的精细化管理和分类。

## 核心功能 (Core Functionality)
*   **日志分类:** 提供四种不同类型的日志记录器，每种类型都有一个特定的前缀，便于在日志配置中进行区分和路由：
    *   **业务日志 (Business Logger):** 用于记录与应用程序核心业务逻辑相关的事件和信息。
    *   **审计日志 (Audit Logger):** 专门用于记录用户操作、权限变更、数据修改等安全和合规性相关的审计信息。
    *   **性能监控日志 (Performance Logger):** 用于记录方法执行时间、数据库查询性能等性能指标，便于性能分析和优化。
    *   **普通日志 (General Logger):** 用于记录应用程序的一般性信息、调试信息、错误等。
*   **简化日志获取:** 开发者无需手动构建带有特定前缀的日志名称，只需传入当前类即可获取相应类别的日志记录器。

## 接口说明 (Interface Description)

### 静态方法 (Static Methods)
*   `getBusinessLogger(Class<?> clazz)`:
    *   **参数:** `clazz` (Class<?>) - 调用此日志记录器的类。
    *   **返回值:** `Logger` - 一个用于业务日志的 SLF4J `Logger` 实例。
    *   **业务逻辑:** 返回一个名为 `com.purchase.business.<ClassName>` 的日志记录器。例如，如果从 `UserService` 调用，则日志记录器名称为 `com.purchase.business.UserService`。

*   `getAuditLogger(Class<?> clazz)`:
    *   **参数:** `clazz` (Class<?>) - 调用此日志记录器的类。
    *   **返回值:** `Logger` - 一个用于审计日志的 SLF4J `Logger` 实例。
    *   **业务逻辑:** 返回一个名为 `com.purchase.audit.<ClassName>` 的日志记录器。例如，如果从 `AuditLogAspect` 调用，则日志记录器名称为 `com.purchase.audit.AuditLogAspect`。

*   `getPerformanceLogger(Class<?> clazz)`:
    *   **参数:** `clazz` (Class<?>) - 调用此日志记录器的类。
    *   **返回值:** `Logger` - 一个用于性能监控日志的 SLF4J `Logger` 实例。
    *   **业务逻辑:** 返回一个名为 `com.purchase.performance.<ClassName>` 的日志记录器。例如，如果从 `PerformanceMonitorAspect` 调用，则日志记录器名称为 `com.purchase.performance.PerformanceMonitorAspect`。

*   `getLogger(Class<?> clazz)`:
    *   **参数:** `clazz` (Class<?>) - 调用此日志记录器的类。
    *   **返回值:** `Logger` - 一个用于普通应用程序日志的 SLF4J `Logger` 实例。
    *   **业务逻辑:** 返回一个名为 `clazz.getName()` 的日志记录器，这是 SLF4J 获取日志记录器的标准方式。例如，如果从 `MyController` 调用，则日志记录器名称为 `com.purchase.controller.MyController`。

## 使用示例 (Usage Examples)

```java
// In a service class for business logic logging
package com.purchase.user.service;
import com.purchase.common.log.LoggerFactory;
import org.slf4j.Logger;

public class UserService {
    private static final Logger businessLogger = LoggerFactory.getBusinessLogger(UserService.class);
    private static final Logger logger = LoggerFactory.getLogger(UserService.class); // For general logs

    public void createUser(String username) {
        businessLogger.info("User creation initiated for username: {}", username);
        // ... business logic
        logger.debug("User object created in database.");
    }
}

// In an aspect for audit logging
package com.purchase.common.log;
import org.slf4j.Logger;

@Aspect
@Component
public class AuditLogAspect {
    private static final Logger auditLogger = LoggerFactory.getAuditLogger(AuditLogAspect.class);

    @Around("auditLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // ... audit logic
        auditLogger.info("Audit log for operation: {}", operationDetails);
        return joinPoint.proceed();
    }
}

// In a performance monitoring component
package com.purchase.common.performance;
import com.purchase.common.log.LoggerFactory;
import org.slf4j.Logger;

public class PerformanceMonitor {
    private static final Logger performanceLogger = LoggerFactory.getPerformanceLogger(PerformanceMonitor.class);

    public void monitorMethodExecution(String methodName, long duration) {
        performanceLogger.info("Method {} executed in {}ms", methodName, duration);
    }
}
```

## 注意事项 (Notes)
*   **Logback/Log4j2 配置:** 要使这些分类日志记录器生效，需要在 `logback-spring.xml` 或 `log4j2.xml` 等日志配置文件中为 `com.purchase.business`、`com.purchase.audit` 和 `com.purchase.performance` 配置单独的 appender 和 logger，以便将它们路由到不同的文件或处理逻辑。
*   **命名约定:** 日志记录器名称的约定（例如 `com.purchase.business.`）是实现日志分类的关键。更改这些前缀将需要更新日志配置文件。
*   **简化与灵活性:** 这个工厂类在简化日志记录器获取的同时，提供了足够的灵活性来根据日志类型进行不同的处理，这对于大型应用程序的日志管理非常有用。
*   **静态方法:** 所有方法都是静态的，可以直接通过类名调用，无需实例化 `LoggerFactory`。
