package com.purchase.common.log;

import org.slf4j.MDC;

import java.util.UUID;

/**
 * 日志上下文工具类
 * 用于管理MDC（Mapped Diagnostic Context）信息，实现请求链路追踪
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class LogContext {
    
    /**
     * 追踪ID键名
     */
    public static final String TRACE_ID = "traceId";
    
    /**
     * 用户ID键名
     */
    public static final String USER_ID = "userId";
    
    /**
     * 用户名键名
     */
    public static final String USERNAME = "username";
    
    /**
     * 用户角色键名
     */
    public static final String USER_ROLE = "userRole";
    
    /**
     * 请求URI键名
     */
    public static final String REQUEST_URI = "requestUri";
    
    /**
     * 请求方法键名
     */
    public static final String REQUEST_METHOD = "requestMethod";
    
    /**
     * 客户端IP键名
     */
    public static final String CLIENT_IP = "clientIp";
    
    /**
     * 操作类型键名
     */
    public static final String OPERATION_TYPE = "operationType";
    
    /**
     * 业务模块键名
     */
    public static final String BUSINESS_MODULE = "businessModule";
    
    /**
     * 设置追踪ID
     * 
     * @param traceId 追踪ID，如果为null则自动生成
     */
    public static void setTraceId(String traceId) {
        if (traceId == null || traceId.trim().isEmpty()) {
            traceId = generateTraceId();
        }
        MDC.put(TRACE_ID, traceId);
    }
    
    /**
     * 获取追踪ID
     * 
     * @return 追踪ID
     */
    public static String getTraceId() {
        return MDC.get(TRACE_ID);
    }
    
    /**
     * 设置用户信息
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param userRole 用户角色
     */
    public static void setUserInfo(String userId, String username, String userRole) {
        if (userId != null) {
            MDC.put(USER_ID, userId);
        }
        if (username != null) {
            MDC.put(USERNAME, username);
        }
        if (userRole != null) {
            MDC.put(USER_ROLE, userRole);
        }
    }
    
    /**
     * 设置请求信息
     * 
     * @param requestUri 请求URI
     * @param requestMethod 请求方法
     * @param clientIp 客户端IP
     */
    public static void setRequestInfo(String requestUri, String requestMethod, String clientIp) {
        if (requestUri != null) {
            MDC.put(REQUEST_URI, requestUri);
        }
        if (requestMethod != null) {
            MDC.put(REQUEST_METHOD, requestMethod);
        }
        if (clientIp != null) {
            MDC.put(CLIENT_IP, clientIp);
        }
    }
    
    /**
     * 设置业务信息
     * 
     * @param operationType 操作类型（如：CREATE、UPDATE、DELETE、QUERY）
     * @param businessModule 业务模块（如：REQUIREMENT、BIDDING、ORDER）
     */
    public static void setBusinessInfo(String operationType, String businessModule) {
        if (operationType != null) {
            MDC.put(OPERATION_TYPE, operationType);
        }
        if (businessModule != null) {
            MDC.put(BUSINESS_MODULE, businessModule);
        }
    }
    
    /**
     * 清除所有MDC信息
     */
    public static void clear() {
        MDC.clear();
    }
    
    /**
     * 清除指定键的MDC信息
     * 
     * @param key 键名
     */
    public static void remove(String key) {
        MDC.remove(key);
    }
    
    /**
     * 生成追踪ID
     * 
     * @return 追踪ID
     */
    private static String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 获取用户ID
     * 
     * @return 用户ID
     */
    public static String getUserId() {
        return MDC.get(USER_ID);
    }
    
    /**
     * 获取用户名
     * 
     * @return 用户名
     */
    public static String getUsername() {
        return MDC.get(USERNAME);
    }
    
    /**
     * 获取用户角色
     * 
     * @return 用户角色
     */
    public static String getUserRole() {
        return MDC.get(USER_ROLE);
    }
} 