# com.purchase.common.log 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.common.log` 包提供了采购系统后端应用程序的全面日志和审计功能。它通过自定义注解、AOP 切面、MDC（Mapped Diagnostic Context）工具类和 Spring MVC 拦截器，实现了日志的分类、请求链路追踪、操作审计以及敏感数据脱敏，旨在提高日志的可读性、可追溯性和安全性，从而简化问题排查和系统监控。

## 目录结构概览 (Directory Structure Overview)
*   `AuditLog.java`: 自定义注解，用于标记需要进行操作审计的方法。
*   `AuditLogAspect.java`: Spring AOP 切面，拦截 `@AuditLog` 注解的方法，记录审计日志。
*   `LogContext.java`: 日志上下文工具类，利用 SLF4J MDC 管理和传递请求链路追踪信息。
*   `LoggerFactory.java`: 自定义日志工厂类，提供不同类别的日志记录器（业务、审计、性能、通用）。
*   `LogInterceptor.java`: Spring MVC 拦截器，用于记录 HTTP 请求和响应的详细日志，并处理日志上下文。
*   `AuditLog.md`: `AuditLog.java` 的文档。
*   `AuditLogAspect.md`: `AuditLogAspect.java` 的文档。
*   `LogContext.md`: `LogContext.java` 的文档。
*   `LoggerFactory.md`: `LoggerFactory.java` 的文档。
*   `LogInterceptor.md`: `LogInterceptor.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
该包中的组件协同工作，构建了一个强大的日志和审计框架：

1.  **`LogContext` 作为核心:** `LogContext` 是整个日志体系的基础。它利用 MDC 为每个请求维护一个独立的上下文，包括 `traceId`（用于请求链路追踪）、`userId`、`username`、`requestUri` 等信息。这些信息在请求的整个生命周期中都可用，并最终被日志记录器捕获。

2.  **`LogInterceptor` 处理 HTTP 请求:** `LogInterceptor` 作为 Spring MVC 的入口点，在每个 HTTP 请求的 `preHandle` 阶段：
    *   初始化 `LogContext`，设置 `traceId`、用户信息和请求信息。
    *   记录请求的详细信息（包括脱敏的参数和头信息）到业务日志。
    在 `afterCompletion` 阶段：
    *   计算请求执行时间。
    *   记录响应状态和任何异常到业务日志。
    *   检测慢请求并记录到性能日志。
    *   **最关键的是，它在 `finally` 块中调用 `LogContext.clear()`，确保 MDC 信息在请求结束后被清除，防止线程池中的数据泄漏。**

3.  **`AuditLog` 和 `AuditLogAspect` 实现操作审计:**
    *   开发人员通过在需要审计的业务方法上添加 `@AuditLog` 注解来标记这些方法。
    *   `AuditLogAspect` 是一个 AOP 切面，它拦截所有带有 `@AuditLog` 注解的方法。
    *   在方法执行的环绕通知中，`AuditLogAspect` 从 `AuditLog` 注解中获取操作类型和描述，从 `LogContext` 获取请求上下文信息，并捕获方法的参数、返回结果和异常。
    *   它还负责对敏感参数和结果进行脱敏处理。
    *   最终，它将结构化的审计信息（通常是 JSON 格式）记录到专门的审计日志中，这对于安全审计和合规性非常重要。

4.  **`LoggerFactory` 提供日志分类:** `LoggerFactory` 提供了不同类型的 `Logger` 实例（业务、审计、性能、通用），这些实例的名称带有特定的前缀。这使得日志框架（如 Logback）能够根据这些名称将不同类别的日志路由到不同的文件或目的地，从而实现日志的精细化管理和分析。

**协作流程总结:**
当一个 HTTP 请求进入应用程序时，`LogInterceptor` 首先捕获它，初始化 `LogContext` 并记录请求开始日志。请求处理过程中，如果调用了带有 `@AuditLog` 注解的业务方法，`AuditLogAspect` 会介入，从 `LogContext` 获取上下文信息，并记录详细的操作审计日志。所有日志记录都通过 `LoggerFactory` 获取的分类 `Logger` 进行。最后，当请求完成时，`LogInterceptor` 记录响应日志并清除 `LogContext`，确保资源的正确释放和上下文的隔离。

这种设计模式确保了日志的全面性、可追溯性和可管理性，为应用程序的监控、调试和安全审计提供了坚实的基础。
