# AuditLog.java

## 文件概述 (File Overview)
`AuditLog.java` 是一个自定义注解，用于标记需要进行操作审计的方法。当方法被此注解标记时，`AuditLogAspect` 将会拦截该方法的执行，并记录相关的操作日志，包括操作类型、描述、操作人、操作时间、请求参数和响应结果等信息。这个注解是实现系统操作审计功能的核心组成部分。

## 核心功能 (Core Functionality)
*   **操作审计标记:** 作为标记，指示哪些方法需要被审计。
*   **操作类型定义:** 允许指定操作的类型（例如，`ADD`, `UPDATE`, `DELETE`, `QUERY` 等）。
*   **操作描述:** 提供一个可读的描述，说明被审计操作的具体内容。
*   **业务类型定义:** 允许指定操作所属的业务模块（例如，`USER_MANAGEMENT`, `ORDER_PROCESSING` 等）。

## 接口说明 (Interface Description)

### 注解属性 (Annotation Attributes)
*   `type()`: `OperationType` - 操作的类型。这是一个枚举，定义了常见的操作类型（如 `ADD`, `UPDATE`, `DELETE`, `QUERY`, `LOGIN`, `LOGOUT` 等）。默认为 `OTHER`。
*   `description()`: `String` - 操作的详细描述。默认为空字符串。
*   `bizType()`: `BizType` - 操作所属的业务类型。这是一个枚举，定义了不同的业务模块（如 `USER_MANAGEMENT`, `PRODUCT_MANAGEMENT`, `ORDER_PROCESSING`, `COMMISSION_SETTLEMENT` 等）。默认为 `OTHER`。

## 使用示例 (Usage Examples)

```java
// Example 1: Annotating a method for adding a user
@AuditLog(type = OperationType.ADD, description = "添加新用户", bizType = BizType.USER_MANAGEMENT)
public User addUser(User user) {
    // ... logic to add user
    return user;
}

// Example 2: Annotating a method for updating an order status
@AuditLog(type = OperationType.UPDATE, description = "更新订单状态", bizType = BizType.ORDER_PROCESSING)
public Order updateOrderStatus(Long orderId, OrderStatus status) {
    // ... logic to update order status
    return order;
}

// Example 3: Annotating a method for querying product information
@AuditLog(type = OperationType.QUERY, description = "查询产品列表", bizType = BizType.PRODUCT_MANAGEMENT)
public List<Product> getProducts(ProductQuery query) {
    // ... logic to query products
    return products;
}
```

## 注意事项 (Notes)
*   **AOP 集成:** 此注解需要与 Spring AOP（具体是 `AuditLogAspect`）结合使用才能发挥作用。单独使用此注解不会产生任何审计行为。
*   **枚举依赖:** `type` 和 `bizType` 属性依赖于 `OperationType` 和 `BizType` 枚举的定义，这些枚举通常位于同一个或相关的包中。
*   **描述清晰:** `description` 属性应提供足够的信息，以便审计日志能够清晰地反映操作的业务含义。
*   **性能考虑:** 虽然审计日志对于安全和合规性很重要，但过度使用或在性能敏感路径上使用可能会引入开销。应谨慎选择需要审计的方法。
