package com.purchase.common.log;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 审计日志切面
 * 用于自动处理带有@AuditLog注解的方法，记录用户操作、参数、结果等信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Aspect
@Component
public class AuditLogAspect {
    
    private static final Logger auditLogger = LoggerFactory.getAuditLogger(AuditLogAspect.class);
    private static final Logger logger = LoggerFactory.getLogger(AuditLogAspect.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 定义切点：所有带有@AuditLog注解的方法
     */
    @Pointcut("@annotation(com.purchase.common.log.AuditLog)")
    public void auditLogPointcut() {
    }
    
    /**
     * 环绕通知：记录审计日志
     * 
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 异常
     */
    @Around("auditLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取方法和注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        AuditLog auditLog = method.getAnnotation(AuditLog.class);
        
        // 构建审计日志信息
        Map<String, Object> auditInfo = buildAuditInfo(joinPoint, auditLog, startTime);
        
        Object result = null;
        Exception exception = null;
        
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            
            // 记录返回结果
            if (auditLog.logResult() && result != null) {
                auditInfo.put("result", sanitizeResult(result));
            }
            
            return result;
            
        } catch (Exception e) {
            exception = e;
            throw e;
            
        } finally {
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            auditInfo.put("executionTime", executionTime + "ms");
            auditInfo.put("endTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            // 记录异常信息
            if (exception != null && auditLog.logException()) {
                auditInfo.put("exception", exception.getClass().getSimpleName());
                auditInfo.put("exceptionMessage", exception.getMessage());
                auditInfo.put("success", false);
            } else {
                auditInfo.put("success", true);
            }
            
            // 设置业务上下文
            LogContext.setBusinessInfo(
                auditLog.operationType().name(),
                auditLog.businessModule().name()
            );
            
            try {
                // 记录审计日志
                if (exception != null) {
                    auditLogger.error("审计日志 - 操作失败: {}", objectMapper.writeValueAsString(auditInfo));
                } else {
                    auditLogger.info("审计日志 - 操作成功: {}", objectMapper.writeValueAsString(auditInfo));
                }
            } catch (Exception e) {
                logger.warn("记录审计日志失败", e);
            }
        }
    }
    
    /**
     * 构建审计日志信息
     * 
     * @param joinPoint 连接点
     * @param auditLog 审计日志注解
     * @param startTime 开始时间
     * @return 审计日志信息
     */
    private Map<String, Object> buildAuditInfo(ProceedingJoinPoint joinPoint, AuditLog auditLog, long startTime) {
        Map<String, Object> auditInfo = new HashMap<>();
        
        // 基本信息
        auditInfo.put("startTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        auditInfo.put("operation", auditLog.value());
        auditInfo.put("operationType", auditLog.operationType().getDescription());
        auditInfo.put("businessModule", auditLog.businessModule().getDescription());
        auditInfo.put("className", joinPoint.getTarget().getClass().getSimpleName());
        auditInfo.put("methodName", joinPoint.getSignature().getName());
        
        // 用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && !"anonymousUser".equals(authentication.getName())) {
            auditInfo.put("username", authentication.getName());
            auditInfo.put("userRole", authentication.getAuthorities().toString());
        }
        
        // 请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            auditInfo.put("requestUri", request.getRequestURI());
            auditInfo.put("requestMethod", request.getMethod());
            auditInfo.put("clientIp", getClientIpAddress(request));
            auditInfo.put("userAgent", request.getHeader("User-Agent"));
        }
        
        // 方法参数
        if (auditLog.logParams()) {
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                auditInfo.put("parameters", sanitizeParameters(args));
            }
        }
        
        return auditInfo;
    }
    
    /**
     * 清理敏感参数信息
     * 
     * @param args 方法参数
     * @return 清理后的参数
     */
    private Object sanitizeParameters(Object[] args) {
        try {
            Map<String, Object> params = new HashMap<>();
            for (int i = 0; i < args.length; i++) {
                Object arg = args[i];
                if (arg != null) {
                    String paramName = "param" + i;
                    
                    // 检查是否为敏感信息
                    if (isSensitiveObject(arg)) {
                        params.put(paramName, "***");
                    } else {
                        // 限制参数长度，避免日志过大
                        String paramValue = objectMapper.writeValueAsString(arg);
                        if (paramValue.length() > 1000) {
                            paramValue = paramValue.substring(0, 1000) + "...";
                        }
                        params.put(paramName, paramValue);
                    }
                }
            }
            return params;
        } catch (Exception e) {
            logger.warn("清理参数信息失败", e);
            return "参数序列化失败";
        }
    }
    
    /**
     * 清理返回结果信息
     * 
     * @param result 返回结果
     * @return 清理后的结果
     */
    private Object sanitizeResult(Object result) {
        try {
            if (isSensitiveObject(result)) {
                return "***";
            }
            
            String resultValue = objectMapper.writeValueAsString(result);
            if (resultValue.length() > 2000) {
                resultValue = resultValue.substring(0, 2000) + "...";
            }
            return resultValue;
        } catch (Exception e) {
            logger.warn("清理返回结果失败", e);
            return "结果序列化失败";
        }
    }
    
    /**
     * 判断是否为敏感对象
     * 
     * @param obj 对象
     * @return 是否为敏感对象
     */
    private boolean isSensitiveObject(Object obj) {
        if (obj == null) {
            return false;
        }
        
        String objString = obj.toString().toLowerCase();
        return objString.contains("password") || 
               objString.contains("token") || 
               objString.contains("secret") ||
               objString.contains("key") ||
               obj instanceof HttpServletRequest ||
               obj instanceof javax.servlet.http.HttpServletResponse;
    }
    
    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headers = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String header : headers) {
            String ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        return request.getRemoteAddr();
    }
} 