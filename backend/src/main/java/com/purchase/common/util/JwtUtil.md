# JwtUtil.java

## 文件概述 (File Overview)
`JwtUtil.java` 是一个 Spring `@Component` 工具类，专门用于 JSON Web Token (JWT) 的生成、解析和验证。它封装了使用 JJWT 库进行 JWT 操作的复杂性，并从应用程序配置中获取 JWT 的密钥和过期时间。该工具类提供了生成访问令牌和刷新令牌、从令牌中提取声明（如用户名、用户 ID、角色）以及验证令牌有效性的功能，是实现基于 JWT 认证和授权机制的核心组件。

## 核心功能 (Core Functionality)
*   **JWT 生成:** 提供 `generateToken` 和 `generateRefreshToken` 方法，用于创建带有用户身份信息（用户名、用户 ID、角色）的 JWT。这些令牌的过期时间分别由 `jwt.expiration` 和 `jwt.refresh-expiration` 配置决定。
*   **JWT 解析:** `parseToken` 方法用于解析 JWT 字符串，并返回包含所有声明的 `Claims` 对象。
*   **声明提取:** 提供便捷方法（`getRoleFromToken`、`getUsernameFromToken`、`getUserIdFromToken`）从解析后的 `Claims` 中提取特定的用户信息。
*   **JWT 验证:** `validateToken` 方法用于验证 JWT 的签名和过期时间，确保令牌的完整性和有效性。
*   **配置化:** JWT 的密钥和过期时间通过 Spring 的 `@Value` 注解从配置文件中动态加载，增强了灵活性和安全性。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `secret`: `String` - JWT 签名和验证的密钥，从 `jwt.secret` 配置中读取。
*   `expiration`: `Long` - 访问令牌的过期时间（毫秒），从 `jwt.expiration` 配置中读取。
*   `refreshExpiration`: `Long` - 刷新令牌的过期时间（毫秒），从 `jwt.refresh-expiration` 配置中读取。

### 公共方法 (Public Methods)
*   `generateToken(String username, String userId, String role)`:
    *   **参数:** `username` (String) - 用户名；`userId` (String) - 用户 ID；`role` (String) - 用户角色。
    *   **返回值:** `String` - 生成的 JWT 访问令牌。
    *   **业务逻辑:** 使用 HS512 算法和配置的密钥，生成一个包含 `username` 作为主题、`userId` 和 `role` 作为自定义声明的 JWT。令牌的签发时间为当前时间，过期时间由 `expiration` 字段决定。

*   `generateRefreshToken(String username, String userId, String role)`:
    *   **参数:** `username` (String) - 用户名；`userId` (String) - 用户 ID；`role` (String) - 用户角色。
    *   **返回值:** `String` - 生成的 JWT 刷新令牌。
    *   **业务逻辑:** 与 `generateToken` 类似，但使用 `refreshExpiration` 字段作为过期时间。

*   `parseToken(String token)`:
    *   **参数:** `token` (String) - 要解析的 JWT 字符串。
    *   **返回值:** `Claims` - 包含 JWT 所有声明的 `Claims` 对象。
    *   **抛出:** `JwtException` (例如 `SignatureException`, `ExpiredJwtException`, `MalformedJwtException`) - 如果令牌无效或解析失败。
    *   **业务逻辑:** 使用配置的密钥解析 JWT 字符串，并返回其主体（claims）。

*   `getRoleFromToken(String token)`:
    *   **参数:** `token` (String) - JWT 字符串。
    *   **返回值:** `String` - 从令牌中提取的用户角色。
    *   **业务逻辑:** 调用 `parseToken` 解析令牌，然后从 `Claims` 中获取 `role` 声明。

*   `getUsernameFromToken(String token)`:
    *   **参数:** `token` (String) - JWT 字符串。
    *   **返回值:** `String` - 从令牌中提取的用户名（主题）。
    *   **业务逻辑:** 调用 `parseToken` 解析令牌，然后从 `Claims` 中获取主题 (`subject`)。

*   `getUserIdFromToken(String token)`:
    *   **参数:** `token` (String) - JWT 字符串。
    *   **返回值:** `Long` - 从令牌中提取的用户 ID。如果 `userId` 声明不存在或无法解析为 `Long`，则返回 `null`。
    *   **业务逻辑:** 调用 `parseToken` 解析令牌，然后从 `Claims` 中获取 `userId` 声明并尝试将其转换为 `Long`。

*   `validateToken(String token)`:
    *   **参数:** `token` (String) - 要验证的 JWT 字符串。
    *   **返回值:** `boolean` - 如果令牌有效（签名正确且未过期），则为 `true`；否则为 `false`。
    *   **业务逻辑:** 尝试解析令牌。如果解析过程中没有抛出异常，则认为令牌有效。

*   `getExpirationFromToken(String token)`:
    *   **参数:** `token` (String) - JWT 字符串。
    *   **返回值:** `Long` - 令牌的过期时间戳（毫秒）。如果令牌无效或无法获取过期时间，则返回 `null`。
    *   **业务逻辑:** 调用 `parseToken` 解析令牌，然后获取其过期时间并转换为毫秒时间戳。

## 使用示例 (Usage Examples)

```java
// In an authentication service
@Service
public class AuthService {
    @Autowired
    private JwtUtil jwtUtil;

    public String loginAndGenerateTokens(String username, String password) {
        // ... authenticate user
        String userId = "123"; // Assume user ID from database
        String role = "USER"; // Assume user role from database

        String accessToken = jwtUtil.generateToken(username, userId, role);
        String refreshToken = jwtUtil.generateRefreshToken(username, userId, role);

        // Store refresh token securely (e.g., in database or Redis)
        // Return access token to client, refresh token might be set in HttpOnly cookie
        return accessToken;
    }
}

// In a JWT authentication filter
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    @Autowired
    private JwtUtil jwtUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            if (jwtUtil.validateToken(token)) {
                String username = jwtUtil.getUsernameFromToken(token);
                String role = jwtUtil.getRoleFromToken(token);
                Long userId = jwtUtil.getUserIdFromToken(token);

                // Set authentication in SecurityContext
                // ...
            }
        }
        filterChain.doFilter(request, response);
    }
}
```

## 注意事项 (Notes)
*   **密钥安全:** `jwt.secret` 必须是一个强密钥，并且绝不能硬编码在代码中或暴露在版本控制中。它应该通过安全的方式（如环境变量、密钥管理服务）进行配置。
*   **过期时间:** `expiration` 和 `refreshExpiration` 的值应根据安全性和用户体验的需求进行合理设置。访问令牌通常较短，刷新令牌较长。
*   **异常处理:** `parseToken` 方法会抛出 `JwtException` 及其子类（如 `ExpiredJwtException`、`SignatureException`），调用方应捕获这些异常以处理令牌过期、签名无效等情况。
*   **依赖:** 该类依赖于 `io.jsonwebtoken` 库。确保 `pom.xml` 或 `build.gradle` 中包含正确的依赖。
*   **用户 ID 类型:** `getUserIdFromToken` 方法将 `userId` 声明解析为 `String` 后再转换为 `Long`。确保在生成令牌时 `userId` 声明是以字符串形式存储的，以避免潜在的类型转换问题。
*   **刷新令牌策略:** 刷新令牌的实现通常涉及将其存储在安全的持久化存储中，并在访问令牌过期时使用它来获取新的访问令牌和刷新令牌，同时确保旧的刷新令牌失效，以防止重放攻击。