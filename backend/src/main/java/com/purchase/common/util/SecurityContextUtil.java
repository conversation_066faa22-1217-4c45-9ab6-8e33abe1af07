package com.purchase.common.util;

import com.purchase.common.exception.BusinessException;
import io.jsonwebtoken.Claims;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * Spring Security Context 工具类
 * 专门负责从 Spring Security Context 中获取用户信息
 * 
 * 设计原则：
 * 1. 单一职责：只负责从SecurityContext获取用户信息
 * 2. 边界清晰：不涉及JWT token的生成、解析等操作
 * 3. 统一异常处理：提供一致的异常处理机制
 */
@Component
public class SecurityContextUtil {

    /**
     * 获取当前登录用户ID
     * 
     * @return 用户ID
     * @throws BusinessException 当无法获取用户ID时抛出异常
     */
    public static Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new BusinessException(401, "用户未认证");
        }

        // 从authentication.getName()获取用户ID（在JwtAuthenticationFilter中设置）
        String userIdStr = authentication.getName();
        if (userIdStr == null || userIdStr.trim().isEmpty()) {
            throw new BusinessException(401, "无法获取用户ID");
        }

        try {
            return Long.parseLong(userIdStr);
        } catch (NumberFormatException e) {
            throw new BusinessException(401, "用户ID格式无效");
        }
    }

    /**
     * 获取当前登录用户ID（安全版本，不抛出异常）
     * 
     * @return 用户ID，如果获取失败返回null
     */
    public static Long getCurrentUserIdSafely() {
        try {
            return getCurrentUserId();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前登录用户角色
     * 
     * @return 用户角色
     * @throws BusinessException 当无法获取用户角色时抛出异常
     */
    public static String getCurrentUserRole() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new BusinessException(401, "用户未认证");
        }

        Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
        if (authorities == null || authorities.isEmpty()) {
            throw new BusinessException(401, "无法获取用户角色");
        }

        return authorities.iterator().next().getAuthority();
    }

    /**
     * 获取当前登录用户角色（安全版本，不抛出异常）
     * 
     * @return 用户角色，如果获取失败返回null
     */
    public static String getCurrentUserRoleSafely() {
        try {
            return getCurrentUserRole();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前登录用户名
     * 
     * @return 用户名
     * @throws BusinessException 当无法获取用户名时抛出异常
     */
    public static String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new BusinessException(401, "用户未认证");
        }

        Object credentials = authentication.getCredentials();
        if (credentials instanceof Claims) {
            Claims claims = (Claims) credentials;
            String username = claims.getSubject();
            if (username == null || username.trim().isEmpty()) {
                throw new BusinessException(401, "无法获取用户名");
            }
            return username;
        }

        throw new BusinessException(401, "无效的用户凭证类型");
    }

    /**
     * 获取当前登录用户名（安全版本，不抛出异常）
     * 
     * @return 用户名，如果获取失败返回null
     */
    public static String getCurrentUsernameSafely() {
        try {
            return getCurrentUsername();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 检查当前用户是否拥有指定角色
     * 
     * @param role 角色名称
     * @return 是否拥有该角色
     */
    public static boolean hasRole(String role) {
        try {
            String currentRole = getCurrentUserRole();
            return role != null && role.equals(currentRole);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查当前用户是否拥有任意一个指定角色
     * 
     * @param roles 角色名称数组
     * @return 是否拥有任意一个角色
     */
    public static boolean hasAnyRole(String... roles) {
        if (roles == null || roles.length == 0) {
            return false;
        }

        try {
            String currentRole = getCurrentUserRole();
            for (String role : roles) {
                if (role != null && role.equals(currentRole)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查当前用户是否已认证
     * 
     * @return 是否已认证
     */
    public static boolean isAuthenticated() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null && authentication.isAuthenticated();
    }

    /**
     * 获取当前认证对象
     * 
     * @return Authentication对象，如果未认证返回null
     */
    public static Authentication getCurrentAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }
}
