package com.purchase.common.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 日期时间工具类
 */
public class DateTimeUtil {
    
    private static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    
    /**
     * 解析日期时间字符串为LocalDateTime对象
     * 支持多种常见格式
     * 
     * @param dateTimeStr 日期时间字符串
     * @return 解析后的LocalDateTime对象
     * @throws DateTimeParseException 如果无法解析
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        
        // 尝试使用默认格式解析
        try {
            return LocalDateTime.parse(dateTimeStr, DEFAULT_FORMATTER);
        } catch (DateTimeParseException e) {
            // 尝试其他常见格式
            try {
                // 只有日期，添加默认时间
                return LocalDateTime.parse(dateTimeStr + " 00:00:00", DEFAULT_FORMATTER);
            } catch (DateTimeParseException e2) {
                try {
                    // ISO格式 (yyyy-MM-ddTHH:mm:ss)
                    return LocalDateTime.parse(dateTimeStr);
                } catch (DateTimeParseException e3) {
                    // 其他格式处理...
                    throw new DateTimeParseException("无法解析日期时间: " + dateTimeStr, dateTimeStr, 0);
                }
            }
        }
    }
    
    /**
     * 格式化LocalDateTime为字符串
     * 
     * @param dateTime LocalDateTime对象
     * @return 格式化后的字符串，使用默认格式
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DEFAULT_FORMATTER);
    }
    
    /**
     * 格式化LocalDateTime为日期字符串
     * 
     * @param dateTime LocalDateTime对象
     * @return 格式化后的日期字符串 (yyyy-MM-dd)
     */
    public static String formatDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DATE_FORMATTER);
    }
    
    /**
     * 格式化LocalDateTime为时间字符串
     * 
     * @param dateTime LocalDateTime对象
     * @return 格式化后的时间字符串 (HH:mm:ss)
     */
    public static String formatTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(TIME_FORMATTER);
    }
} 