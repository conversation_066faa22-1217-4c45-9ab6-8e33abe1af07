package com.purchase.common.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Cookie工具类
 * 统一管理JWT token的Cookie设置
 *
 * 设计原则：
 * 1. 单一职责：只负责Cookie的设置、获取和清除
 * 2. 安全最佳实践：HttpOnly、Secure、SameSite等安全属性
 * 3. 配置化：所有关键参数都可通过配置文件设置
 * 4. 输入验证：对输入参数进行必要的验证
 */
@Component
public class CookieUtil {

    @Value("${app.cookie.secure:false}")
    private boolean cookieSecure;

    @Value("${app.cookie.domain:}")
    private String cookieDomain;

    @Value("${app.cookie.same-site:Lax}")
    private String cookieSameSite;

    @Value("${jwt.expiration:7200}")
    private int tokenMaxAge; // 从JWT配置读取，默认2小时

    @Value("${jwt.refresh-expiration:604800}")
    private int refreshTokenMaxAge; // 从JWT配置读取，默认7天

    private static final String TOKEN_COOKIE_NAME = "token";
    private static final String REFRESH_TOKEN_COOKIE_NAME = "refreshToken";

    /**
     * 设置token cookie
     *
     * @param response HTTP响应
     * @param token JWT token
     * @throws IllegalArgumentException 当参数无效时抛出异常
     */
    public void setTokenCookie(HttpServletResponse response, String token) {
        validateInputs(response, token);

        Cookie cookie = createSecureCookie(TOKEN_COOKIE_NAME, token, tokenMaxAge);
        response.addCookie(cookie);
    }

    /**
     * 设置refresh token cookie
     *
     * @param response HTTP响应
     * @param refreshToken 刷新token
     * @throws IllegalArgumentException 当参数无效时抛出异常
     */
    public void setRefreshTokenCookie(HttpServletResponse response, String refreshToken) {
        validateInputs(response, refreshToken);

        Cookie cookie = createSecureCookie(REFRESH_TOKEN_COOKIE_NAME, refreshToken, refreshTokenMaxAge);
        response.addCookie(cookie);
    }

    /**
     * 清除token cookie
     *
     * @param response HTTP响应
     * @throws IllegalArgumentException 当response为null时抛出异常
     */
    public void clearTokenCookie(HttpServletResponse response) {
        validateResponse(response);

        Cookie cookie = createExpiredCookie(TOKEN_COOKIE_NAME);
        response.addCookie(cookie);
    }

    /**
     * 清除refresh token cookie
     *
     * @param response HTTP响应
     * @throws IllegalArgumentException 当response为null时抛出异常
     */
    public void clearRefreshTokenCookie(HttpServletResponse response) {
        validateResponse(response);

        Cookie cookie = createExpiredCookie(REFRESH_TOKEN_COOKIE_NAME);
        response.addCookie(cookie);
    }

    /**
     * 清除所有认证相关的cookie
     * 
     * @param response HTTP响应
     */
    public void clearAllAuthCookies(HttpServletResponse response) {
        clearTokenCookie(response);
        clearRefreshTokenCookie(response);
    }

    /**
     * 从请求中获取token
     * 
     * @param request HTTP请求
     * @return token值，如果不存在则返回null
     */
    public String getTokenFromRequest(HttpServletRequest request) {
        return getCookieValue(request, TOKEN_COOKIE_NAME);
    }

    /**
     * 从请求中获取refresh token
     * 
     * @param request HTTP请求
     * @return refresh token值，如果不存在则返回null
     */
    public String getRefreshTokenFromRequest(HttpServletRequest request) {
        return getCookieValue(request, REFRESH_TOKEN_COOKIE_NAME);
    }

    /**
     * 从请求中获取指定名称的cookie值
     *
     * @param request HTTP请求
     * @param cookieName cookie名称
     * @return cookie值，如果不存在则返回null
     */
    private String getCookieValue(HttpServletRequest request, String cookieName) {
        if (request == null || request.getCookies() == null || cookieName == null) {
            return null;
        }

        for (Cookie cookie : request.getCookies()) {
            if (cookieName.equals(cookie.getName())) {
                return cookie.getValue();
            }
        }
        return null;
    }

    /**
     * 创建安全的Cookie
     *
     * @param name Cookie名称
     * @param value Cookie值
     * @param maxAge 过期时间（秒）
     * @return 配置好的Cookie对象
     */
    private Cookie createSecureCookie(String name, String value, int maxAge) {
        Cookie cookie = new Cookie(name, value);
        cookie.setHttpOnly(true);
        cookie.setSecure(cookieSecure);
        cookie.setPath("/");
        cookie.setMaxAge(maxAge);

        // 设置SameSite属性（通过注释说明，因为Servlet API不直接支持）
        // 实际项目中可能需要通过响应头手动设置

        if (cookieDomain != null && !cookieDomain.isEmpty()) {
            cookie.setDomain(cookieDomain);
        }

        return cookie;
    }

    /**
     * 创建过期的Cookie（用于清除）
     *
     * @param name Cookie名称
     * @return 配置为立即过期的Cookie对象
     */
    private Cookie createExpiredCookie(String name) {
        return createSecureCookie(name, "", 0);
    }

    /**
     * 验证输入参数
     *
     * @param response HTTP响应对象
     * @param value Cookie值
     * @throws IllegalArgumentException 当参数无效时抛出异常
     */
    private void validateInputs(HttpServletResponse response, String value) {
        validateResponse(response);
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Cookie值不能为空");
        }
    }

    /**
     * 验证响应对象
     *
     * @param response HTTP响应对象
     * @throws IllegalArgumentException 当response为null时抛出异常
     */
    private void validateResponse(HttpServletResponse response) {
        if (response == null) {
            throw new IllegalArgumentException("HttpServletResponse不能为null");
        }
    }
} 