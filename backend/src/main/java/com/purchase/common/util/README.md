# com.purchase.common.util 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.common.util` 包包含了采购系统后端应用程序中常用的各种通用工具类。这些工具类旨在提供跨模块的、可重用的功能，涵盖了日期时间处理、JWT（JSON Web Token）操作、HTTP Cookie 管理、Spring Security 上下文访问以及分布式唯一 ID 生成等。通过将这些通用功能封装在独立的工具类中，该包提高了代码的模块化、可维护性和开发效率。

## 目录结构概览 (Directory Structure Overview)
*   `CookieUtil.java`: 用于管理 HTTP Cookie，特别是 JWT token 相关的 Cookie。
*   `DateTimeUtil.java`: 提供 `LocalDateTime` 对象与字符串之间转换的日期时间工具类。
*   `JwtUtil.java`: 用于 JWT 的生成、解析和验证的工具类。
*   `SecurityContextUtil.java`: 从 Spring Security Context 中获取当前认证用户信息的工具类。
*   `SnowflakeIdGenerator.java`: 基于 Twitter Snowflake 算法的分布式唯一 ID 生成器。
*   `CookieUtil.md`: `CookieUtil.java` 的文档。
*   `DateTimeUtil.md`: `DateTimeUtil.java` 的文档。
*   `JwtUtil.md`: `JwtUtil.java` 的文档。
*   `SecurityContextUtil.md`: `SecurityContextUtil.java` 的文档。
*   `SnowflakeIdGenerator.md`: `SnowflakeIdGenerator.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.common.util` 包中的各个工具类独立提供特定功能，但它们共同为应用程序的多个方面提供了基础支持：

1.  **认证与授权辅助:**
    *   `JwtUtil` 负责 JWT 的核心生命周期管理（生成、解析、验证）。
    *   `CookieUtil` 与 `JwtUtil` 紧密协作，处理 JWT 在 HTTP Cookie 中的存储和检索，确保 token 的安全传输和管理。
    *   `SecurityContextUtil` 则在 `JwtAuthenticationFilter`（通常在 `com.purchase.config` 包中）将解析后的 JWT 信息设置到 Spring Security Context 后，提供便捷的接口供业务逻辑层访问当前认证用户的身份和权限信息。

2.  **通用数据处理:**
    *   `DateTimeUtil` 简化了应用程序中日期时间数据的格式化和解析，确保了时间数据处理的一致性。
    *   `SnowflakeIdGenerator` 提供了在分布式环境中生成唯一 ID 的能力，这些 ID 可以用于数据库主键、订单号等需要全局唯一标识的场景，避免了传统自增 ID 在分布式系统中的局限性。

**协作流程总结:**

例如，在一个典型的用户登录流程中：
*   用户通过认证后，认证服务会使用 `JwtUtil` 生成访问令牌和刷新令牌。
*   `CookieUtil` 随后将这些令牌安全地设置到 HTTP 响应的 Cookie 中。
*   在后续的请求中，`JwtAuthenticationFilter`（或其他安全过滤器）会从请求的 Cookie 中提取令牌，并使用 `JwtUtil` 进行验证和解析。
*   解析出的用户信息（如用户 ID、角色）会被设置到 Spring Security 的 `SecurityContext` 中。
*   此时，应用程序的任何业务逻辑层都可以通过 `SecurityContextUtil` 方便地获取当前操作用户的身份信息，而无需直接接触 JWT 或 Cookie 的细节。
*   同时，如果业务操作需要生成新的唯一标识（如创建订单），则可以调用 `SnowflakeIdGenerator.nextId()` 来获取一个全局唯一的 ID。
*   在处理日期时间相关的业务逻辑时，`DateTimeUtil` 则提供了统一的格式化和解析能力。

这个工具包通过提供这些基础且通用的功能，极大地提高了开发效率，并确保了应用程序在安全性、可伸缩性和数据一致性方面的最佳实践。