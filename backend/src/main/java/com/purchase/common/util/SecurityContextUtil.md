# SecurityContextUtil.java

## 文件概述 (File Overview)
`SecurityContextUtil.java` 是一个 Spring `@Component` 工具类，专门用于从 Spring Security 的 `SecurityContextHolder` 中获取当前认证用户的详细信息。它提供了一系列静态方法来安全地检索用户 ID、用户名、角色以及检查用户的认证状态和角色权限。该工具类旨在简化在应用程序的任何层级访问安全上下文信息的过程，同时通过抛出 `BusinessException` 或返回 `null` 来处理未认证或信息缺失的情况。

## 核心功能 (Core Functionality)
*   **获取当前用户 ID:** 提供 `getCurrentUserId()` 和 `getCurrentUserIdSafely()` 方法，从认证信息中提取用户 ID。
*   **获取当前用户角色:** 提供 `getCurrentUserRole()` 和 `getCurrentUserRoleSafely()` 方法，从认证信息中提取用户角色。
*   **获取当前用户名:** 提供 `getCurrentUsername()` 和 `getCurrentUsernameSafely()` 方法，从认证信息中提取用户名。
*   **角色权限检查:** 提供 `hasRole()` 和 `hasAnyRole()` 方法，用于检查当前用户是否拥有特定角色或任意一个指定角色。
*   **认证状态检查:** `isAuthenticated()` 方法用于判断当前用户是否已认证。
*   **获取认证对象:** `getCurrentAuthentication()` 方法直接返回当前的 `Authentication` 对象。
*   **安全获取版本:** 对于每个获取方法，都提供了一个“安全版本”（以 `Safely` 结尾），这些版本在获取失败时不会抛出异常，而是返回 `null`，适用于不需要强制中断流程的场景。
*   **统一异常处理:** 在非安全版本的方法中，当无法获取所需信息时，会抛出带有特定错误码的 `BusinessException`，与应用程序的全局异常处理机制集成。

## 接口说明 (Interface Description)

### 静态方法 (Static Methods)
*   `static Long getCurrentUserId()`:
    *   **参数:** 无。
    *   **返回值:** `Long` - 当前登录用户的 ID。
    *   **抛出:** `BusinessException` - 如果用户未认证、无法获取用户 ID 或用户 ID 格式无效。
    *   **业务逻辑:** 从 `SecurityContextHolder` 获取 `Authentication` 对象，并尝试将其 `name`（通常在 `JwtAuthenticationFilter` 中设置为用户 ID）解析为 `Long`。

*   `static Long getCurrentUserIdSafely()`:
    *   **参数:** 无。
    *   **返回值:** `Long` - 当前登录用户的 ID，如果获取失败则返回 `null`。
    *   **业务逻辑:** 调用 `getCurrentUserId()` 并捕获所有异常，返回 `null`。

*   `static String getCurrentUserRole()`:
    *   **参数:** 无。
    *   **返回值:** `String` - 当前登录用户的角色。
    *   **抛出:** `BusinessException` - 如果用户未认证或无法获取用户角色。
    *   **业务逻辑:** 从 `Authentication` 对象中获取 `GrantedAuthority` 集合，并返回第一个权限的字符串表示。

*   `static String getCurrentUserRoleSafely()`:
    *   **参数:** 无。
    *   **返回值:** `String` - 当前登录用户的角色，如果获取失败则返回 `null`。
    *   **业务逻辑:** 调用 `getCurrentUserRole()` 并捕获所有异常，返回 `null`。

*   `static String getCurrentUsername()`:
    *   **参数:** 无。
    *   **返回值:** `String` - 当前登录用户的用户名。
    *   **抛出:** `BusinessException` - 如果用户未认证、凭证类型无效或无法获取用户名。
    *   **业务逻辑:** 从 `Authentication` 对象的 `credentials` 中获取 `Claims` 对象（假设凭证是 JWT Claims），然后提取 `subject` 作为用户名。

*   `static String getCurrentUsernameSafely()`:
    *   **参数:** 无。
    *   **返回值:** `String` - 当前登录用户的用户名，如果获取失败则返回 `null`。
    *   **业务逻辑:** 调用 `getCurrentUsername()` 并捕获所有异常，返回 `null`。

*   `static boolean hasRole(String role)`:
    *   **参数:** `role` (String) - 要检查的角色名称。
    *   **返回值:** `boolean` - 如果当前用户拥有指定角色，则为 `true`。
    *   **业务逻辑:** 安全地获取当前用户角色，并与传入的角色进行比较。

*   `static boolean hasAnyRole(String... roles)`:
    *   **参数:** `roles` (String...) - 要检查的角色名称数组。
    *   **返回值:** `boolean` - 如果当前用户拥有数组中的任意一个角色，则为 `true`。
    *   **业务逻辑:** 遍历角色数组，安全地获取当前用户角色，并进行比较。

*   `static boolean isAuthenticated()`:
    *   **参数:** 无。
    *   **返回值:** `boolean` - 如果当前用户已认证，则为 `true`。
    *   **业务逻辑:** 检查 `SecurityContextHolder` 中的 `Authentication` 对象是否非空且已认证。

*   `static Authentication getCurrentAuthentication()`:
    *   **参数:** 无。
    *   **返回值:** `Authentication` - 当前的 `Authentication` 对象，如果未认证则返回 `null`。
    *   **业务逻辑:** 直接从 `SecurityContextHolder` 获取 `Authentication` 对象。

## 使用示例 (Usage Examples)

```java
// In a service method requiring user ID
@Service
public class OrderService {
    public Order createOrder(OrderRequest request) {
        Long userId = SecurityContextUtil.getCurrentUserId(); // Throws BusinessException if not authenticated
        // ... create order for userId
        return new Order();
    }

    public Order getOrderDetails(Long orderId) {
        Long userId = SecurityContextUtil.getCurrentUserIdSafely(); // Returns null if not authenticated
        if (userId == null) {
            // Handle unauthenticated case gracefully
            return null;
        }
        // ... fetch order details
        return orderRepository.findByIdAndUserId(orderId, userId);
    }
}

// In a controller method requiring specific role
@RestController
@RequestMapping("/admin")
public class AdminController {
    @GetMapping("/users")
    public List<User> getAllUsers() {
        if (!SecurityContextUtil.hasRole("ADMIN")) {
            throw new BusinessException(403, "无权限访问");
        }
        // ... fetch all users
        return userService.findAllUsers();
    }

    @PostMapping("/products")
    public ResponseEntity<String> createProduct(@RequestBody ProductDto productDto) {
        if (!SecurityContextUtil.hasAnyRole("ADMIN", "PRODUCT_MANAGER")) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body("Forbidden");
        }
        // ... create product
        return ResponseEntity.ok("Product created");
    }
}
```

## 注意事项 (Notes)
*   **Spring Security 上下文:** 此工具类高度依赖于 Spring Security 的 `SecurityContextHolder`。确保 Spring Security 已正确配置，并且认证信息已成功设置到上下文中（例如，通过 JWT 过滤器）。
*   **线程局部性:** `SecurityContextHolder` 默认使用 `ThreadLocal` 来存储安全上下文。这意味着在异步操作中，如果线程发生切换，需要手动传递安全上下文，否则 `SecurityContextUtil` 将无法获取到用户信息。
*   **异常处理策略:** 提供了抛出 `BusinessException` 和返回 `null` 两种获取信息失败时的处理方式。选择哪种取决于具体的业务需求和错误处理策略。
*   **角色字符串:** `getCurrentUserRole()` 方法返回的是 `GrantedAuthority` 的字符串表示。确保在进行角色比较时，使用的字符串与 Spring Security 中配置的角色名称一致。
*   **凭证类型:** `getCurrentUsername()` 方法假设 `Authentication` 对象的 `credentials` 是 `Claims` 类型（通常在 JWT 认证流程中是这样）。如果认证机制不同，可能需要调整此方法以适应实际的凭证类型。