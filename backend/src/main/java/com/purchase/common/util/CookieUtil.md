# CookieUtil.java

## 文件概述 (File Overview)
`CookieUtil.java` 是一个 Spring `@Component` 工具类，专门用于管理 HTTP Cookie，特别是与 JWT token 相关的 Cookie。它封装了设置、获取和清除 token 和 refresh token Cookie 的逻辑，并遵循安全最佳实践（如 HttpOnly、Secure、SameSite 属性）。该类通过 `@Value` 注解从配置文件中读取关键的 Cookie 安全和过期时间配置，确保了灵活性和可配置性。

## 核心功能 (Core Functionality)
*   **设置 Token Cookie:** 提供方法将 JWT token 设置为 HttpOnly、Secure 的 Cookie，并配置其过期时间。
*   **设置 Refresh Token Cookie:** 提供方法将 Refresh token 设置为 HttpOnly、Secure 的 Cookie，并配置其过期时间。
*   **清除 Token Cookie:** 提供方法通过设置过期时间为 0 来清除指定的 token Cookie。
*   **获取 Token 值:** 提供方法从 `HttpServletRequest` 中获取指定名称的 Cookie 值。
*   **安全属性配置:** 支持通过配置文件配置 Cookie 的 `Secure` 属性、`Domain` 属性和 `SameSite` 属性（通过注释说明，实际设置可能需要额外处理）。
*   **输入验证:** 对传入的 `HttpServletResponse` 和 Cookie 值进行非空验证，确保操作的健壮性。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `cookieSecure`: `boolean` - 控制 Cookie 的 `Secure` 属性。如果为 `true`，则 Cookie 仅通过 HTTPS 发送。
*   `cookieDomain`: `String` - 控制 Cookie 的 `Domain` 属性。指定 Cookie 有效的域。
*   `cookieSameSite`: `String` - 控制 Cookie 的 `SameSite` 属性。用于防止 CSRF 攻击（例如 `Lax`, `Strict`, `None`）。
*   `tokenMaxAge`: `int` - JWT token Cookie 的最大存活时间（秒）。
*   `refreshTokenMaxAge`: `int` - Refresh token Cookie 的最大存活时间（秒）。
*   `TOKEN_COOKIE_NAME`: `String` - JWT token 在 Cookie 中的名称（常量：“token”）。
*   `REFRESH_TOKEN_COOKIE_NAME`: `String` - Refresh token 在 Cookie 中的名称（常量：“refreshToken”）。

### 公共方法 (Public Methods)
*   `setTokenCookie(HttpServletResponse response, String token)`:
    *   **参数:** `response` (HttpServletResponse) - HTTP 响应对象；`token` (String) - 要设置的 JWT token 字符串。
    *   **返回值:** `void`
    *   **抛出:** `IllegalArgumentException` - 如果 `response` 为 `null` 或 `token` 为 `null`/空。
    *   **业务逻辑:** 创建一个名为 `token` 的 HttpOnly、Secure Cookie，其值为传入的 `token`，过期时间为 `tokenMaxAge`，并将其添加到响应中。

*   `setRefreshTokenCookie(HttpServletResponse response, String refreshToken)`:
    *   **参数:** `response` (HttpServletResponse) - HTTP 响应对象；`refreshToken` (String) - 要设置的刷新 token 字符串。
    *   **返回值:** `void`
    *   **抛出:** `IllegalArgumentException` - 如果 `response` 为 `null` 或 `refreshToken` 为 `null`/空。
    *   **业务逻辑:** 创建一个名为 `refreshToken` 的 HttpOnly、Secure Cookie，其值为传入的 `refreshToken`，过期时间为 `refreshTokenMaxAge`，并将其添加到响应中。

*   `clearTokenCookie(HttpServletResponse response)`:
    *   **参数:** `response` (HttpServletResponse) - HTTP 响应对象。
    *   **返回值:** `void`
    *   **抛出:** `IllegalArgumentException` - 如果 `response` 为 `null`。
    *   **业务逻辑:** 创建一个名为 `token` 且 `maxAge` 为 0 的 Cookie，并将其添加到响应中，从而指示浏览器立即删除该 Cookie。

*   `clearRefreshTokenCookie(HttpServletResponse response)`:
    *   **参数:** `response` (HttpServletResponse) - HTTP 响应对象。
    *   **返回值:** `void`
    *   **抛出:** `IllegalArgumentException` - 如果 `response` 为 `null`。
    *   **业务逻辑:** 创建一个名为 `refreshToken` 且 `maxAge` 为 0 的 Cookie，并将其添加到响应中，从而指示浏览器立即删除该 Cookie。

*   `clearAllAuthCookies(HttpServletResponse response)`:
    *   **参数:** `response` (HttpServletResponse) - HTTP 响应对象。
    *   **返回值:** `void`
    *   **业务逻辑:** 调用 `clearTokenCookie` 和 `clearRefreshTokenCookie` 方法，清除所有认证相关的 Cookie。

*   `getTokenFromRequest(HttpServletRequest request)`:
    *   **参数:** `request` (HttpServletRequest) - HTTP 请求对象。
    *   **返回值:** `String` - `token` Cookie 的值，如果不存在则返回 `null`。
    *   **业务逻辑:** 遍历请求中的所有 Cookie，查找名为 `token` 的 Cookie 并返回其值。

*   `getRefreshTokenFromRequest(HttpServletRequest request)`:
    *   **参数:** `request` (HttpServletRequest) - HTTP 请求对象。
    *   **返回值:** `String` - `refreshToken` Cookie 的值，如果不存在则返回 `null`。
    *   **业务逻辑:** 遍历请求中的所有 Cookie，查找名为 `refreshToken` 的 Cookie 并返回其值。

### 私有辅助方法 (Private Helper Methods)
*   `getCookieValue(HttpServletRequest request, String cookieName)`: 从请求中获取指定名称的 Cookie 值。
*   `createSecureCookie(String name, String value, int maxAge)`: 创建一个配置了 HttpOnly、Secure、Path 和 MaxAge 的 Cookie 对象。如果 `cookieDomain` 非空，则设置 `Domain` 属性。
*   `createExpiredCookie(String name)`: 创建一个立即过期的 Cookie，用于清除。
*   `validateInputs(HttpServletResponse response, String value)`: 验证 `HttpServletResponse` 和 Cookie 值是否有效。
*   `validateResponse(HttpServletResponse response)`: 验证 `HttpServletResponse` 是否为 `null`。

## 使用示例 (Usage Examples)

```java
// In an authentication service or controller after successful login
@Service
public class AuthService {
    @Autowired
    private CookieUtil cookieUtil;

    public String login(String username, String password, HttpServletResponse response) {
        // ... authentication logic
        String jwtToken = "generated_jwt_token";
        String refreshToken = "generated_refresh_token";

        cookieUtil.setTokenCookie(response, jwtToken);
        cookieUtil.setRefreshTokenCookie(response, refreshToken);

        return jwtToken;
    }

    public void logout(HttpServletResponse response) {
        cookieUtil.clearAllAuthCookies(response);
    }
}

// In a JWT filter to extract token from cookie
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    @Autowired
    private CookieUtil cookieUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String token = cookieUtil.getTokenFromRequest(request);
        if (token != null) {
            // ... validate token and set security context
        }
        filterChain.doFilter(request, response);
    }
}
```

## 注意事项 (Notes)
*   **HttpOnly:** `HttpOnly` 属性可以有效防止客户端脚本访问 Cookie，从而降低 XSS 攻击的风险。
*   **Secure:** `Secure` 属性确保 Cookie 仅通过加密的 HTTPS 连接发送。在生产环境中，应始终启用此功能。
*   **SameSite:** `SameSite` 属性有助于缓解 CSRF 攻击。当前代码通过注释说明了其意图，但在 Servlet API 中直接设置 `SameSite` 需要 Servlet 4.0+ 或通过手动添加 `Set-Cookie` 响应头来实现。
*   **配置化:** Cookie 的安全属性和过期时间都是可配置的，这使得在不同环境（开发、测试、生产）中调整行为变得容易。
*   **异常处理:** 该类在输入验证失败时抛出 `IllegalArgumentException`，调用方应捕获并处理这些异常。
*   **依赖注入:** `CookieUtil` 是一个 Spring `@Component`，因此可以通过 Spring 的依赖注入机制在其他组件中使用。