# DateTimeUtil.java

## 文件概述 (File Overview)
`DateTimeUtil.java` 是一个静态工具类，提供了一系列便捷的方法来处理 Java 8 `java.time.LocalDateTime` 对象与字符串之间的转换。它支持解析多种常见格式的日期时间字符串，并将 `LocalDateTime` 对象格式化为默认的日期时间、日期或时间字符串。该工具类旨在简化应用程序中日期时间数据的处理，减少重复代码并提高一致性。

## 核心功能 (Core Functionality)
*   **日期时间字符串解析:** `parseDateTime` 方法能够将多种常见格式的日期时间字符串解析为 `LocalDateTime` 对象，包括默认格式（`yyyy-MM-dd HH:mm:ss`）和 ISO 格式（`yyyy-MM-ddTHH:mm:ss`），并能处理仅包含日期的字符串（自动添加默认时间）。
*   **日期时间格式化:** 提供了 `formatDateTime`、`formatDate` 和 `formatTime` 方法，分别用于将 `LocalDateTime` 对象格式化为完整的日期时间字符串、仅日期字符串或仅时间字符串。
*   **常量格式化器:** 内部定义了三个 `DateTimeFormatter` 常量，用于默认的日期时间、日期和时间格式，确保格式化的一致性。

## 接口说明 (Interface Description)

### 静态方法 (Static Methods)
*   `static LocalDateTime parseDateTime(String dateTimeStr)`:
    *   **参数:** `dateTimeStr` (String) - 要解析的日期时间字符串。
    *   **返回值:** `LocalDateTime` - 解析后的 `LocalDateTime` 对象。如果输入为 `null` 或空字符串，则返回 `null`。
    *   **抛出:** `DateTimeParseException` - 如果字符串无法被任何支持的格式解析。
    *   **业务逻辑:** 尝试按以下顺序解析字符串：
        1.  使用 `DEFAULT_FORMATTER` (`yyyy-MM-dd HH:mm:ss`)。
        2.  如果失败，尝试将字符串视为仅日期（`yyyy-MM-dd`），并追加 `00:00:00` 后再用 `DEFAULT_FORMATTER` 解析。
        3.  如果再失败，尝试使用 `LocalDateTime.parse(dateTimeStr)` 解析 ISO 格式。
        4.  如果所有尝试都失败，则抛出 `DateTimeParseException`。

*   `static String formatDateTime(LocalDateTime dateTime)`:
    *   **参数:** `dateTime` (LocalDateTime) - 要格式化的 `LocalDateTime` 对象。
    *   **返回值:** `String` - 格式化后的日期时间字符串（`yyyy-MM-dd HH:mm:ss`）。如果输入为 `null`，则返回 `null`。
    *   **业务逻辑:** 使用 `DEFAULT_FORMATTER` 将 `LocalDateTime` 对象格式化为字符串。

*   `static String formatDate(LocalDateTime dateTime)`:
    *   **参数:** `dateTime` (LocalDateTime) - 要格式化的 `LocalDateTime` 对象。
    *   **返回值:** `String` - 格式化后的日期字符串（`yyyy-MM-dd`）。如果输入为 `null`，则返回 `null`。
    *   **业务逻辑:** 使用 `DATE_FORMATTER` 将 `LocalDateTime` 对象格式化为日期字符串。

*   `static String formatTime(LocalDateTime dateTime)`:
    *   **参数:** `dateTime` (LocalDateTime) - 要格式化的 `LocalDateTime` 对象。
    *   **返回值:** `String` - 格式化后的时间字符串（`HH:mm:ss`）。如果输入为 `null`，则返回 `null`。
    *   **业务逻辑:** 使用 `TIME_FORMATTER` 将 `LocalDateTime` 对象格式化为时间字符串。

## 使用示例 (Usage Examples)

```java
import java.time.LocalDateTime;
import static com.purchase.common.util.DateTimeUtil.*;

public class Example {
    public static void main(String[] args) {
        // Parsing examples
        LocalDateTime dt1 = parseDateTime("2023-07-27 10:30:00");
        System.out.println("Parsed dt1: " + dt1); // 2023-07-27T10:30

        LocalDateTime dt2 = parseDateTime("2023-07-27");
        System.out.println("Parsed dt2 (date only): " + dt2); // 2023-07-27T00:00

        LocalDateTime dt3 = parseDateTime("2023-07-27T15:45:30");
        System.out.println("Parsed dt3 (ISO): " + dt3); // 2023-07-27T15:45:30

        // Formatting examples
        LocalDateTime now = LocalDateTime.now();
        System.out.println("Formatted full: " + formatDateTime(now));
        System.out.println("Formatted date: " + formatDate(now));
        System.out.println("Formatted time: " + formatTime(now));

        // Handling null inputs
        System.out.println("Null date time format: " + formatDateTime(null)); // null
        System.out.println("Null date time parse: " + parseDateTime(null)); // null
    }
}
```

## 注意事项 (Notes)
*   **Java 8 Date/Time API:** 该工具类基于 `java.time` 包（JSR-310），这是 Java 处理日期和时间的现代 API，推荐使用。
*   **线程安全:** `DateTimeFormatter` 是线程安全的，因此将其定义为 `static final` 是安全的做法。
*   **异常处理:** `parseDateTime` 方法在无法解析字符串时会抛出 `DateTimeParseException`，调用方应捕获并处理此异常。
*   **格式支持:** 目前支持的解析格式有限。如果应用程序需要处理更多样化的日期时间格式，可能需要扩展 `parseDateTime` 方法以包含更多 `DateTimeFormatter` 实例或更复杂的解析逻辑。
*   **时区:** 此工具类主要处理 `LocalDateTime`，它不包含时区信息。如果应用程序需要处理带有时区或偏移量的日期时间，应使用 `ZonedDateTime` 或 `OffsetDateTime`，并考虑使用 `ZoneId` 或 `ZoneOffset`。