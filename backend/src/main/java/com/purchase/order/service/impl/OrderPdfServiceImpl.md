# OrderPdfServiceImpl.java

## 文件概述 (File Overview)
`OrderPdfServiceImpl.java` 是订单PDF处理服务的具体实现类，实现了 `OrderPdfService` 接口。该类负责将订单数据转换为专业的PDF文档，支持中文字体渲染、图片嵌入、复杂表格布局等功能。它使用Apache PDFBox库进行PDF生成，提供了完整的订单合同文档生成能力，包括订单基本信息、订单项目明细、签名信息、支付条款等内容的格式化输出。

## 核心功能 (Core Functionality)
*   **PDF文档生成:** 将订单数据转换为格式化的PDF文档，支持专业的合同样式。
*   **中文字体支持:** 支持多种中文字体，包括思源宋体、标准宋体等，确保中文内容正确显示。
*   **图片处理:** 支持在PDF中嵌入图片，包括公司Logo、产品图片、签名图片等。
*   **表格布局:** 生成复杂的表格布局，展示订单项目明细、价格计算等信息。
*   **文档安全:** 生成文档哈希值，确保文档完整性和防篡改。
*   **文件上传:** 将生成的PDF文档上传到文件服务，并返回访问URL。
*   **异常处理:** 完善的异常处理机制，确保PDF生成过程的稳定性。
*   **模板化设计:** 采用模板化设计，支持不同类型订单的PDF生成需求。

## 接口说明 (Interface Description)

### 主要方法 (Main Methods)

#### 1. generateOrderPdf(Long orderId)
*   **参数:** `orderId` - 订单ID
*   **返回值:** `PdfGenerationResult`
*   **功能:** 生成指定订单的PDF文档
*   **业务逻辑:**
    1. 根据订单ID查询订单详情和订单项目
    2. 创建PDF文档并设置页面属性
    3. 加载中文字体资源
    4. 渲染订单基本信息
    5. 渲染订单项目明细表格
    6. 添加签名信息和支付条款
    7. 计算文档哈希值
    8. 上传文档到文件服务
    9. 返回生成结果

### 辅助方法 (Helper Methods)

#### 1. loadChineseFont(PDDocument document)
*   **功能:** 加载中文字体，按优先级尝试不同字体
*   **支持字体:** 思源宋体、标准宋体、系统默认字体
*   **异常处理:** 字体加载失败时使用备用字体

#### 2. addOrderBasicInfo(PDPageContentStream contentStream, UnifiedOrder order, PDFont font, float yPosition)
*   **功能:** 添加订单基本信息到PDF
*   **包含内容:** 订单号、创建时间、买家卖家信息、总金额等

#### 3. addOrderItemsTable(PDPageContentStream contentStream, List<OrderItem> orderItems, PDFont font, float yPosition)
*   **功能:** 添加订单项目明细表格
*   **表格内容:** 产品名称、规格、数量、单价、总价等

#### 4. addSignatureInfo(PDPageContentStream contentStream, UnifiedOrder order, PDFont font, float yPosition)
*   **功能:** 添加签名信息
*   **包含内容:** 买家签名、卖家签名、签名时间等

#### 5. calculateDocumentHash(byte[] pdfBytes)
*   **功能:** 计算PDF文档的SHA-256哈希值
*   **用途:** 确保文档完整性和防篡改

#### 6. drawText(PDPageContentStream contentStream, String text, PDFont font, float fontSize, float x, float y)
*   **功能:** 在指定位置绘制文本
*   **支持:** 中文文本渲染、字体大小控制、位置精确定位

#### 7. drawTable(PDPageContentStream contentStream, String[][] tableData, float[] columnWidths, float x, float y, PDFont font)
*   **功能:** 绘制表格
*   **支持:** 自定义列宽、表格边框、单元格对齐

## 使用示例 (Usage Examples)

```java
// 在Controller中使用OrderPdfServiceImpl
@RestController
@RequestMapping("/api/v1/orders")
public class OrderController {
    @Autowired
    private OrderPdfService orderPdfService;

    @PostMapping("/{orderId}/generate-pdf")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")
    public Result<PdfGenerationResult> generateOrderPdf(@PathVariable Long orderId) {
        try {
            PdfGenerationResult result = orderPdfService.generateOrderPdf(orderId);
            return Result.success(result);
        } catch (BusinessException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("生成订单PDF失败", e);
            return Result.error("生成PDF文档失败");
        }
    }
}

// 在Service中使用
@Service
public class OrderManagementService {
    @Autowired
    private OrderPdfService orderPdfService;

    public void processOrderCompletion(Long orderId) {
        try {
            // 订单完成后自动生成PDF合同
            PdfGenerationResult pdfResult = orderPdfService.generateOrderPdf(orderId);

            // 更新订单的PDF文档信息
            UnifiedOrder order = orderService.getById(orderId);
            order.setContractPdfUrl(pdfResult.getPdfUrl());
            order.setContractPdfHash(pdfResult.getDocumentHash());
            orderService.updateById(order);

            // 发送PDF文档给买卖双方
            notificationService.sendOrderPdfNotification(orderId, pdfResult.getPdfUrl());

            log.info("订单{}的PDF合同生成成功: {}", orderId, pdfResult.getPdfUrl());
        } catch (Exception e) {
            log.error("处理订单完成时生成PDF失败: orderId={}", orderId, e);
            // 不影响主流程，记录错误日志
        }
    }
}
```

## 注意事项 (Notes)
*   **字体资源:** 需要在classpath中提供中文字体文件，建议使用思源宋体以获得最佳显示效果。
*   **内存管理:** PDF生成过程中会占用较多内存，特别是处理大量订单项目或图片时，需要注意内存使用。
*   **文件上传:** 依赖 `FileService` 进行PDF文件的上传和存储，需要确保文件服务的可用性。
*   **异常处理:** 字体加载失败、图片处理错误、文件上传失败等异常都有相应的处理机制。
*   **文档安全:** 生成的PDF文档包含哈希值，可用于验证文档完整性和防篡改。
*   **性能考虑:** 对于大批量PDF生成，建议使用异步处理避免阻塞主线程。
*   **模板扩展:** 当前实现支持标准订单模板，如需支持其他类型订单，可扩展模板系统。
*   **图片处理:** 支持从URL加载图片并嵌入PDF，需要确保图片URL的可访问性。
*   **中文支持:** 使用UTF-8编码处理中文内容，确保中文字符正确显示。
