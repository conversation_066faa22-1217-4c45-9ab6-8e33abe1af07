# OrderProgressActivityService.java

## 文件概述 (File Overview)
`OrderProgressActivityService.java` 是订单进度活动的业务服务接口，位于 `com.purchase.order.service` 包中。该接口定义了订单进度活动管理的核心业务方法，包括活动的创建、查询、更新和删除操作。通过提供多种查询方式（按订单、按阶段、分页查询、分组查询），支持订单全生命周期的进度跟踪和管理。接口设计遵循单一职责原则，专注于订单进度活动的业务逻辑处理。

## 核心功能 (Core Functionality)
*   **活动管理**: 提供订单进度活动的完整CRUD操作
*   **多维查询**: 支持按订单、按阶段、分页等多种查询方式
*   **分组查询**: 按订单阶段对活动进行分组，便于前端展示
*   **业务封装**: 封装复杂的业务逻辑，为控制器层提供简洁的接口
*   **数据转换**: 处理实体对象与响应DTO之间的转换
*   **权限支持**: 为权限控制提供必要的数据查询支持

## 接口说明 (Interface Description)

### 主要方法定义

#### createActivity - 创建订单进度活动
*   **方法签名**: `OrderProgressActivity createActivity(Long orderId, CreateProgressActivityRequest request, Long userId)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID，指定活动所属的订单
    *   `request` (CreateProgressActivityRequest) - 创建请求对象，包含活动详情
    *   `userId` (Long) - 创建用户ID，用于记录操作人
*   **返回值**: `OrderProgressActivity` - 创建成功的活动实体对象
*   **业务逻辑**: 
    *   验证订单是否存在
    *   验证用户是否有权限在指定阶段创建活动
    *   创建活动记录并设置创建时间和创建人
    *   返回完整的活动实体信息

#### getActivitiesByOrderId - 获取订单的所有进度活动
*   **方法签名**: `List<ProgressActivityResponse> getActivitiesByOrderId(Long orderId)`
*   **参数**: `orderId` (Long) - 订单ID
*   **返回值**: `List<ProgressActivityResponse>` - 按时间降序排列的活动列表
*   **业务逻辑**: 
    *   查询指定订单的所有进度活动
    *   按创建时间降序排列，最新的活动在前
    *   转换为响应DTO对象返回

#### getActivitiesByOrderIdGroupedByStage - 按节点分组获取订单进度活动
*   **方法签名**: `Map<Integer, List<ProgressActivityResponse>> getActivitiesByOrderIdGroupedByStage(Long orderId)`
*   **参数**: `orderId` (Long) - 订单ID
*   **返回值**: `Map<Integer, List<ProgressActivityResponse>>` - 按节点值分组的活动Map
*   **业务逻辑**: 
    *   查询订单的所有进度活动
    *   按阶段值（stageValue）进行分组
    *   每个阶段内的活动按时间排序
    *   便于前端按阶段展示进度信息

#### getActivitiesByOrderIdAndStage - 获取特定节点的所有活动
*   **方法签名**: `List<ProgressActivityResponse> getActivitiesByOrderIdAndStage(Long orderId, Integer stageValue)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID
    *   `stageValue` (Integer) - 节点值（0-订单款项, 20-营业执照, 40-生产过程, 60-检验包装, 80-物流信息, 100-验收）
*   **返回值**: `List<ProgressActivityResponse>` - 特定阶段的活动列表
*   **业务逻辑**: 
    *   查询指定订单和阶段的所有活动
    *   按时间排序返回
    *   用于查看特定阶段的详细进度

#### getActivityById - 获取活动详情
*   **方法签名**: `ProgressActivityResponse getActivityById(Long activityId)`
*   **参数**: `activityId` (Long) - 活动ID
*   **返回值**: `ProgressActivityResponse` - 活动详情响应对象
*   **业务逻辑**: 
    *   根据活动ID查询活动详情
    *   转换为响应DTO对象
    *   包含活动的完整信息

#### updateActivity - 更新活动
*   **方法签名**: `OrderProgressActivity updateActivity(Long activityId, CreateProgressActivityRequest request)`
*   **参数**: 
    *   `activityId` (Long) - 活动ID
    *   `request` (CreateProgressActivityRequest) - 更新请求对象
*   **返回值**: `OrderProgressActivity` - 更新后的活动实体
*   **业务逻辑**: 
    *   验证活动是否存在
    *   验证用户是否有权限更新该活动
    *   更新活动信息并设置更新时间
    *   返回更新后的活动实体

#### deleteActivity - 删除活动
*   **方法签名**: `void deleteActivity(Long activityId)`
*   **参数**: `activityId` (Long) - 活动ID
*   **返回值**: `void`
*   **业务逻辑**: 
    *   验证活动是否存在
    *   验证用户是否有权限删除该活动
    *   执行软删除或硬删除操作
    *   记录删除日志

#### getActivityPage - 分页获取订单活动
*   **方法签名**: `IPage<ProgressActivityResponse> getActivityPage(Long orderId, Integer page, Integer size)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID
    *   `page` (Integer) - 页码，从1开始
    *   `size` (Integer) - 每页大小
*   **返回值**: `IPage<ProgressActivityResponse>` - 分页结果对象
*   **业务逻辑**: 
    *   分页查询指定订单的进度活动
    *   按时间降序排列
    *   返回分页结果，包含总数、当前页等信息

## 使用示例 (Usage Examples)

```java
// 1. 服务实现类示例
@Service
@Transactional
public class OrderProgressActivityServiceImpl implements OrderProgressActivityService {
    
    @Autowired
    private OrderProgressActivityMapper activityMapper;
    
    @Autowired
    private UnifiedOrderService orderService;
    
    @Override
    public OrderProgressActivity createActivity(Long orderId, CreateProgressActivityRequest request, Long userId) {
        // 验证订单是否存在
        UnifiedOrder order = orderService.getOrderById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 验证用户权限（根据阶段和用户角色）
        validateStagePermission(request.getStageValue(), userId);
        
        // 创建活动实体
        OrderProgressActivity activity = new OrderProgressActivity();
        activity.setOrderId(orderId);
        activity.setStageValue(request.getStageValue());
        activity.setTitle(request.getTitle());
        activity.setDescription(request.getDescription());
        activity.setAttachments(request.getAttachments());
        activity.setCreatedBy(userId);
        activity.setCreatedAt(LocalDateTime.now());
        
        // 保存到数据库
        activityMapper.insert(activity);
        
        // 记录日志
        log.info("创建订单进度活动成功，订单ID: {}, 阶段: {}, 创建人: {}", 
                orderId, request.getStageValue(), userId);
        
        return activity;
    }
    
    @Override
    public List<ProgressActivityResponse> getActivitiesByOrderId(Long orderId) {
        List<OrderProgressActivity> activities = activityMapper.selectByOrderId(orderId);
        return activities.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    public Map<Integer, List<ProgressActivityResponse>> getActivitiesByOrderIdGroupedByStage(Long orderId) {
        List<ProgressActivityResponse> activities = getActivitiesByOrderId(orderId);
        return activities.stream()
                .collect(Collectors.groupingBy(ProgressActivityResponse::getStageValue));
    }
    
    private ProgressActivityResponse convertToResponse(OrderProgressActivity activity) {
        ProgressActivityResponse response = new ProgressActivityResponse();
        BeanUtils.copyProperties(activity, response);
        
        // 设置创建人信息
        User creator = userService.getUserById(activity.getCreatedBy());
        if (creator != null) {
            response.setCreatorName(creator.getName());
            response.setCreatorRole(creator.getRole());
        }
        
        return response;
    }
}

// 2. 控制器层调用示例
@RestController
public class OrderProgressController {
    
    @Autowired
    private OrderProgressActivityService activityService;
    
    // 创建进度活动
    @PostMapping("/orders/{orderId}/progress/activities")
    public Result<OrderProgressActivity> createActivity(
            @PathVariable Long orderId,
            @RequestBody CreateProgressActivityRequest request) {
        
        Long currentUserId = getCurrentUserId();
        OrderProgressActivity activity = activityService.createActivity(orderId, request, currentUserId);
        return Result.success(activity);
    }
    
    // 获取订单所有活动
    @GetMapping("/orders/{orderId}/progress/activities")
    public Result<List<ProgressActivityResponse>> getActivities(@PathVariable Long orderId) {
        List<ProgressActivityResponse> activities = activityService.getActivitiesByOrderId(orderId);
        return Result.success(activities);
    }
    
    // 按阶段分组获取活动
    @GetMapping("/orders/{orderId}/progress/activities/grouped")
    public Result<Map<Integer, List<ProgressActivityResponse>>> getActivitiesGrouped(@PathVariable Long orderId) {
        Map<Integer, List<ProgressActivityResponse>> groupedActivities = 
            activityService.getActivitiesByOrderIdGroupedByStage(orderId);
        return Result.success(groupedActivities);
    }
}

// 3. 业务场景使用示例
@Service
public class OrderWorkflowService {
    
    @Autowired
    private OrderProgressActivityService activityService;
    
    // 自动创建阶段完成活动
    public void completeOrderStage(Long orderId, Integer stageValue, String description, Long operatorId) {
        CreateProgressActivityRequest request = new CreateProgressActivityRequest();
        request.setStageValue(stageValue);
        request.setTitle(getStageTitle(stageValue) + "已完成");
        request.setDescription(description);
        request.setActivityType("STAGE_COMPLETE");
        
        activityService.createActivity(orderId, request, operatorId);
        
        // 检查是否可以进入下一阶段
        checkAndAdvanceToNextStage(orderId, stageValue);
    }
    
    // 获取订单当前阶段
    public Integer getCurrentStage(Long orderId) {
        Map<Integer, List<ProgressActivityResponse>> groupedActivities = 
            activityService.getActivitiesByOrderIdGroupedByStage(orderId);
        
        return groupedActivities.keySet().stream()
                .max(Integer::compareTo)
                .orElse(0);
    }
    
    // 检查阶段是否完成
    public boolean isStageCompleted(Long orderId, Integer stageValue) {
        List<ProgressActivityResponse> stageActivities = 
            activityService.getActivitiesByOrderIdAndStage(orderId, stageValue);
        
        return stageActivities.stream()
                .anyMatch(activity -> "STAGE_COMPLETE".equals(activity.getActivityType()));
    }
}
```

## 注意事项 (Notes)
*   **事务管理**: 所有修改操作（创建、更新、删除）都应该在事务环境中执行，确保数据一致性
*   **权限验证**: 实现类需要根据用户角色和订单阶段进行权限验证，确保用户只能操作有权限的阶段
*   **数据转换**: 需要在实体对象和响应DTO之间进行适当的数据转换，避免敏感信息泄露
*   **异常处理**: 需要定义和处理各种业务异常，如订单不存在、权限不足等
*   **日志记录**: 重要操作都应该有详细的日志记录，便于问题排查和审计
*   **性能优化**: 查询操作需要考虑性能优化，特别是分页查询和分组查询
*   **缓存策略**: 对于频繁查询的数据，可以考虑使用缓存提高性能
*   **并发控制**: 多用户同时操作同一订单时，需要考虑并发控制
*   **数据校验**: 输入参数需要进行有效性校验，确保数据完整性
*   **接口设计**: 接口方法应该职责单一，参数和返回值类型明确
*   **版本兼容**: 接口变更时需要考虑向后兼容性
*   **文档维护**: 接口变更时需要及时更新文档和注释
