# PaymentProofServiceImpl.java

## 文件概述 (File Overview)
`PaymentProofServiceImpl.java` 是支付凭证业务服务的实现类，位于 `com.purchase.order.service.impl` 包中。该类继承了MyBatis-Plus的 `ServiceImpl<PaymentProofMapper, PaymentProof>` 并实现了 `PaymentProofService` 接口，提供了支付凭证的完整业务逻辑处理。支持样品订单、统一订单、结算订单等多种订单类型的支付凭证管理，包括上传、审核、确认、拒绝、批量处理等功能。通过策略模式实现不同订单类型的差异化处理，并集成事件发布机制实现业务解耦。

## 核心功能 (Core Functionality)
*   **支付凭证管理**: 提供支付凭证的完整CRUD操作和生命周期管理
*   **多订单类型支持**: 支持样品订单、统一订单、结算订单的支付凭证处理
*   **审核流程**: 完整的支付凭证审核流程，包括确认和拒绝操作
*   **批量处理**: 支持批量确认和批量拒绝支付凭证
*   **策略模式**: 通过策略工厂实现不同订单类型的差异化处理逻辑
*   **事件驱动**: 集成Spring事件发布机制，实现业务解耦
*   **统计查询**: 提供支付凭证的统计和分析功能
*   **权限控制**: 基于用户角色的业务权限验证

## 接口说明 (Interface Description)

### 基础查询方法

#### getPaymentProofById - 根据ID查询支付凭证
*   **方法签名**: `PaymentProof getPaymentProofById(Long proofId)`
*   **参数**: `proofId` (Long) - 支付凭证ID
*   **返回值**: `PaymentProof` - 支付凭证实体对象
*   **业务逻辑**: 根据ID直接查询支付凭证详情

#### getPaymentProofsBySampleOrderId - 获取样品订单的支付凭证
*   **方法签名**: `List<PaymentProof> getPaymentProofsBySampleOrderId(Long sampleOrderId)`
*   **参数**: `sampleOrderId` (Long) - 样品订单ID
*   **返回值**: `List<PaymentProof>` - 该订单的所有支付凭证列表
*   **业务逻辑**: 查询指定样品订单的所有支付凭证记录

#### getPaymentProofsByOrderId - 获取统一订单的支付凭证
*   **方法签名**: `List<PaymentProof> getPaymentProofsByOrderId(Long orderId)`
*   **参数**: `orderId` (Long) - 统一订单ID
*   **返回值**: `List<PaymentProof>` - 该订单的所有支付凭证列表
*   **业务逻辑**: 查询指定统一订单的所有支付凭证记录

#### getPaymentProofsBySettlementId - 获取结算订单的支付凭证
*   **方法签名**: `List<PaymentProof> getPaymentProofsBySettlementId(Long settlementId)`
*   **参数**: `settlementId` (Long) - 结算订单ID
*   **返回值**: `List<PaymentProof>` - 该结算订单的所有支付凭证列表
*   **业务逻辑**: 查询指定结算订单的所有支付凭证记录

### 状态查询方法

#### getPendingPaymentProofs - 获取待审核的支付凭证
*   **方法签名**: `List<PaymentProof> getPendingPaymentProofs()`
*   **返回值**: `List<PaymentProof>` - 所有待审核的支付凭证列表
*   **业务逻辑**: 
    *   查询状态为PENDING的所有支付凭证
    *   按上传时间倒序排列
    *   用于管理员审核页面展示

#### getProcessedPaymentProofs - 获取已处理的支付凭证
*   **方法签名**: `List<PaymentProof> getProcessedPaymentProofs()`
*   **返回值**: `List<PaymentProof>` - 所有已处理的支付凭证列表
*   **业务逻辑**: 
    *   查询状态为CONFIRMED或REJECTED的支付凭证
    *   按确认时间倒序排列
    *   用于历史记录查询

#### getPendingPaymentProofsByOrderType - 按订单类型获取待审核凭证
*   **方法签名**: `List<PaymentProof> getPendingPaymentProofsByOrderType(String orderType)`
*   **参数**: `orderType` (String) - 订单类型（SAMPLE、UNIFIED、SETTLEMENT）
*   **返回值**: `List<PaymentProof>` - 指定类型的待审核支付凭证列表
*   **业务逻辑**: 按订单类型筛选待审核的支付凭证

### 核心业务方法

#### saveSampleOrderPaymentProof - 保存样品订单支付凭证
*   **方法签名**: `PaymentProof saveSampleOrderPaymentProof(Long sampleOrderId, String proofUrl, Long uploadedBy, String remarks)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `sampleOrderId` (Long) - 样品订单ID
    *   `proofUrl` (String) - 支付凭证文件URL
    *   `uploadedBy` (Long) - 上传用户ID
    *   `remarks` (String) - 备注信息
*   **返回值**: `PaymentProof` - 保存的支付凭证对象
*   **业务逻辑**: 
    *   验证样品订单存在性和状态
    *   创建支付凭证记录
    *   设置初始状态为PENDING
    *   记录上传时间和上传人

#### confirmPaymentProofUnified - 统一确认支付凭证
*   **方法签名**: `PaymentProof confirmPaymentProofUnified(Long proofId, Long adminUserId, String adminRemark)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `proofId` (Long) - 支付凭证ID
    *   `adminUserId` (Long) - 管理员用户ID
    *   `adminRemark` (String) - 管理员备注
*   **返回值**: `PaymentProof` - 确认后的支付凭证对象
*   **业务逻辑**: 
    *   验证支付凭证存在性和状态
    *   通过策略工厂获取对应的审核策略
    *   执行确认操作并更新状态
    *   发布支付确认事件
    *   记录确认时间和确认人

#### rejectPaymentProofUnified - 统一拒绝支付凭证
*   **方法签名**: `PaymentProof rejectPaymentProofUnified(Long proofId, Long adminUserId, String adminRemark)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `proofId` (Long) - 支付凭证ID
    *   `adminUserId` (Long) - 管理员用户ID
    *   `adminRemark` (String) - 拒绝原因
*   **返回值**: `PaymentProof` - 拒绝后的支付凭证对象
*   **业务逻辑**: 
    *   验证支付凭证存在性和状态
    *   通过策略工厂获取对应的审核策略
    *   执行拒绝操作并更新状态
    *   发布支付拒绝事件
    *   记录拒绝时间和拒绝原因

### 批量处理方法

#### batchConfirmPaymentProofs - 批量确认支付凭证
*   **方法签名**: `int batchConfirmPaymentProofs(List<Long> proofIds, Long adminUserId, String adminRemark)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `proofIds` (List<Long>) - 支付凭证ID列表
    *   `adminUserId` (Long) - 管理员用户ID
    *   `adminRemark` (String) - 批量确认备注
*   **返回值**: `int` - 成功确认的数量
*   **业务逻辑**: 
    *   遍历支付凭证ID列表
    *   逐个调用确认方法
    *   统计成功和失败的数量
    *   记录批量操作日志

#### batchRejectPaymentProofs - 批量拒绝支付凭证
*   **方法签名**: `int batchRejectPaymentProofs(List<Long> proofIds, Long adminUserId, String adminRemark)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `proofIds` (List<Long>) - 支付凭证ID列表
    *   `adminUserId` (Long) - 管理员用户ID
    *   `adminRemark` (String) - 批量拒绝原因
*   **返回值**: `int` - 成功拒绝的数量
*   **业务逻辑**: 
    *   遍历支付凭证ID列表
    *   逐个调用拒绝方法
    *   统计成功和失败的数量
    *   记录批量操作日志

### 统计分析方法

#### getPaymentProofStatistics - 获取支付凭证统计
*   **方法签名**: `Map<String, Long> getPaymentProofStatistics()`
*   **返回值**: `Map<String, Long>` - 按订单类型统计的支付凭证数量
*   **业务逻辑**: 
    *   统计各种订单类型的支付凭证总数
    *   返回Map格式的统计结果
    *   用于管理后台的数据展示

#### getPendingPaymentProofStatistics - 获取待审核支付凭证统计
*   **方法签名**: `Map<String, Long> getPendingPaymentProofStatistics()`
*   **返回值**: `Map<String, Long>` - 按订单类型统计的待审核支付凭证数量
*   **业务逻辑**: 
    *   统计各种订单类型的待审核支付凭证数量
    *   用于管理员工作台的待办事项提醒

### 辅助方法

#### validatePaymentProof - 验证支付凭证
*   **方法签名**: `void validatePaymentProof(PaymentProof paymentProof)`
*   **参数**: `paymentProof` (PaymentProof) - 待验证的支付凭证
*   **业务逻辑**: 
    *   验证支付凭证的基本信息完整性
    *   验证文件URL的有效性
    *   验证订单关联关系

#### getPaymentProofBusinessContext - 获取支付凭证业务上下文
*   **方法签名**: `Object getPaymentProofBusinessContext(PaymentProof paymentProof)`
*   **参数**: `paymentProof` (PaymentProof) - 支付凭证对象
*   **返回值**: `Object` - 业务上下文对象（订单详情等）
*   **业务逻辑**:
    *   根据订单类型获取对应的业务上下文
    *   返回订单详情、买家信息等相关数据
    *   用于审核页面的详情展示

## 使用示例 (Usage Examples)

```java
// 1. 控制器层调用示例
@RestController
public class PaymentProofController {

    @Autowired
    private PaymentProofServiceImpl paymentProofService;

    // 上传样品订单支付凭证
    @PostMapping("/sample-orders/{orderId}/payment-proof")
    public Result<PaymentProof> uploadSampleOrderPaymentProof(
            @PathVariable Long orderId,
            @RequestParam String proofUrl,
            @RequestParam(required = false) String remarks) {

        Long uploadedBy = getCurrentUserId();
        PaymentProof proof = paymentProofService.saveSampleOrderPaymentProof(
            orderId, proofUrl, uploadedBy, remarks);
        return Result.success("支付凭证上传成功", proof);
    }

    // 管理员确认支付凭证
    @PostMapping("/admin/payment-proofs/{proofId}/confirm")
    public Result<PaymentProof> confirmPaymentProof(
            @PathVariable Long proofId,
            @RequestParam(required = false) String adminRemark) {

        Long adminUserId = getCurrentUserId();
        PaymentProof proof = paymentProofService.confirmPaymentProofUnified(
            proofId, adminUserId, adminRemark);
        return Result.success("支付凭证确认成功", proof);
    }

    // 批量处理支付凭证
    @PostMapping("/admin/payment-proofs/batch-confirm")
    public Result<Integer> batchConfirmPaymentProofs(
            @RequestBody List<Long> proofIds,
            @RequestParam(required = false) String adminRemark) {

        Long adminUserId = getCurrentUserId();
        int successCount = paymentProofService.batchConfirmPaymentProofs(
            proofIds, adminUserId, adminRemark);
        return Result.success("批量确认完成，成功数量: " + successCount, successCount);
    }

    // 获取待审核支付凭证列表
    @GetMapping("/admin/payment-proofs/pending")
    public Result<List<PaymentProof>> getPendingPaymentProofs() {
        List<PaymentProof> proofs = paymentProofService.getPendingPaymentProofs();
        return Result.success(proofs);
    }
}

// 2. 事件监听器示例
@Component
public class PaymentProofEventListener {

    @Autowired
    private OrderService orderService;

    @Autowired
    private NotificationService notificationService;

    // 监听支付确认事件
    @EventListener
    @Async
    public void handlePaymentConfirmed(PaymentConfirmedEvent event) {
        log.info("处理支付确认事件，支付凭证ID: {}", event.getProofId());

        try {
            // 更新订单状态
            if ("SAMPLE".equals(event.getOrderType())) {
                orderService.updateSampleOrderPaymentStatus(
                    event.getOrderId(), "PAID");
            } else if ("UNIFIED".equals(event.getOrderType())) {
                orderService.updateUnifiedOrderPaymentStatus(
                    event.getOrderId(), "PAID");
            }

            // 发送通知给买家
            notificationService.sendPaymentConfirmedNotification(
                event.getBuyerId(), event.getOrderId(), event.getOrderType());

        } catch (Exception e) {
            log.error("处理支付确认事件失败，支付凭证ID: {}", event.getProofId(), e);
        }
    }

    // 监听支付拒绝事件
    @EventListener
    @Async
    public void handlePaymentRejected(PaymentRejectedEvent event) {
        log.info("处理支付拒绝事件，支付凭证ID: {}", event.getProofId());

        try {
            // 发送通知给买家
            notificationService.sendPaymentRejectedNotification(
                event.getBuyerId(), event.getOrderId(),
                event.getOrderType(), event.getRejectReason());

        } catch (Exception e) {
            log.error("处理支付拒绝事件失败，支付凭证ID: {}", event.getProofId(), e);
        }
    }
}

// 3. 策略模式使用示例
@Component
public class PaymentProofAuditStrategyFactory {

    @Autowired
    private SampleOrderPaymentAuditStrategy sampleOrderStrategy;

    @Autowired
    private UnifiedOrderPaymentAuditStrategy unifiedOrderStrategy;

    @Autowired
    private SettlementPaymentAuditStrategy settlementStrategy;

    public PaymentProofAuditStrategy getStrategy(String orderType) {
        switch (orderType) {
            case "SAMPLE":
                return sampleOrderStrategy;
            case "UNIFIED":
                return unifiedOrderStrategy;
            case "SETTLEMENT":
                return settlementStrategy;
            default:
                throw new IllegalArgumentException("不支持的订单类型: " + orderType);
        }
    }
}

// 样品订单支付审核策略
@Component
public class SampleOrderPaymentAuditStrategy implements PaymentProofAuditStrategy {

    @Autowired
    private SampleOrderService sampleOrderService;

    @Override
    public void confirmPayment(PaymentProof paymentProof, Long adminUserId, String adminRemark) {
        // 更新样品订单支付状态
        sampleOrderService.updatePaymentStatus(
            paymentProof.getSampleOrderId(), "PAID");

        // 自动确认订单（如果配置了自动确认）
        if (isAutoConfirmEnabled()) {
            sampleOrderService.autoConfirmOrder(paymentProof.getSampleOrderId());
        }

        log.info("样品订单支付确认完成，订单ID: {}", paymentProof.getSampleOrderId());
    }

    @Override
    public void rejectPayment(PaymentProof paymentProof, Long adminUserId, String adminRemark) {
        // 记录拒绝日志
        log.info("样品订单支付被拒绝，订单ID: {}, 拒绝原因: {}",
                paymentProof.getSampleOrderId(), adminRemark);
    }
}

// 4. 定时任务示例
@Component
public class PaymentProofScheduledTasks {

    @Autowired
    private PaymentProofServiceImpl paymentProofService;

    // 自动处理超时的支付凭证
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void processExpiredPaymentProofs() {
        log.info("开始处理超时的支付凭证");

        List<PaymentProof> expiredProofs = getExpiredPendingProofs();

        for (PaymentProof proof : expiredProofs) {
            try {
                // 超时自动拒绝
                paymentProofService.rejectPaymentProofUnified(
                    proof.getId(), getSystemAdminId(), "超时自动拒绝");
                log.info("超时支付凭证自动拒绝，凭证ID: {}", proof.getId());
            } catch (Exception e) {
                log.error("处理超时支付凭证失败，凭证ID: {}", proof.getId(), e);
            }
        }
    }

    // 生成支付凭证统计报告
    @Scheduled(cron = "0 0 8 * * MON") // 每周一早上8点执行
    public void generateWeeklyReport() {
        Map<String, Long> statistics = paymentProofService.getPaymentProofStatistics();
        Map<String, Long> pendingStatistics = paymentProofService.getPendingPaymentProofStatistics();

        // 生成并发送统计报告
        generateAndSendReport(statistics, pendingStatistics);
    }
}

// 5. 业务集成示例
@Service
public class OrderPaymentService {

    @Autowired
    private PaymentProofServiceImpl paymentProofService;

    // 检查订单支付状态
    public boolean isOrderPaid(String orderType, Long orderId) {
        List<PaymentProof> proofs;

        switch (orderType) {
            case "SAMPLE":
                proofs = paymentProofService.getPaymentProofsBySampleOrderId(orderId);
                break;
            case "UNIFIED":
                proofs = paymentProofService.getPaymentProofsByOrderId(orderId);
                break;
            case "SETTLEMENT":
                proofs = paymentProofService.getPaymentProofsBySettlementId(orderId);
                break;
            default:
                return false;
        }

        return proofs.stream()
                .anyMatch(proof -> PaymentProof.ProofStatus.CONFIRMED.equals(proof.getStatus()));
    }

    // 获取订单支付金额
    public BigDecimal getOrderPaidAmount(String orderType, Long orderId) {
        List<PaymentProof> confirmedProofs = getConfirmedPaymentProofs(orderType, orderId);

        return confirmedProofs.stream()
                .map(PaymentProof::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    // 检查是否可以上传支付凭证
    public boolean canUploadPaymentProof(String orderType, Long orderId, Long userId) {
        // 检查订单状态
        if (!isOrderAllowUploadProof(orderType, orderId)) {
            return false;
        }

        // 检查用户权限
        if (!hasUploadPermission(orderType, orderId, userId)) {
            return false;
        }

        // 检查是否已经支付
        if (isOrderPaid(orderType, orderId)) {
            return false;
        }

        return true;
    }
}
```

## 注意事项 (Notes)
*   **事务管理**: 所有修改操作都使用@Transactional注解，确保数据一致性，特别是批量操作需要考虑部分失败的情况
*   **策略模式**: 通过PaymentProofAuditStrategyFactory实现不同订单类型的差异化处理，新增订单类型时需要实现对应的策略
*   **事件驱动**: 使用ApplicationEventPublisher发布业务事件，实现模块间的解耦，事件处理需要考虑异步和异常情况
*   **状态管理**: 支付凭证状态变更遵循严格的业务流程（PENDING -> CONFIRMED/REJECTED），不能随意跳转状态
*   **权限控制**: 上传支付凭证需要验证用户权限，确认和拒绝操作只能由管理员执行
*   **文件安全**: 支付凭证文件URL需要进行安全验证，防止恶意文件上传和访问
*   **并发控制**: 支付凭证审核时需要考虑并发情况，避免重复处理同一个凭证
*   **日志记录**: 所有重要操作都有详细的日志记录，包括操作人、操作时间、操作结果等
*   **异常处理**: 批量操作时需要捕获单个操作的异常，避免影响其他操作的执行
*   **数据验证**: 上传支付凭证前需要验证订单状态、用户权限、文件格式等
*   **业务规则**: 不同订单类型的支付凭证处理规则可能不同，需要通过策略模式灵活处理
*   **性能优化**: 批量操作时需要考虑性能问题，避免一次处理过多数据导致超时
*   **监控告警**: 支付凭证审核失败、批量操作异常等情况需要有相应的监控告警
*   **数据一致性**: 支付凭证状态变更需要与订单状态保持一致，避免数据不一致问题
*   **审计追踪**: 支付凭证的所有操作都需要有完整的审计记录，便于问题排查和合规要求
*   **缓存策略**: 支付凭证统计数据可以考虑使用缓存提高查询性能
*   **接口幂等**: 支付凭证确认和拒绝操作需要考虑接口幂等性，避免重复操作
*   **业务解耦**: 通过事件机制实现与其他业务模块的解耦，但需要确保事件处理的可靠性
