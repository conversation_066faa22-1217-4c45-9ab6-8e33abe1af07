# OrderService.java

## 文件概述 (File Overview)
`OrderService.java` 是订单业务服务接口，位于 `com.purchase.order.service` 包中。该接口定义了订单管理的核心业务规范，提供了订单的完整生命周期管理功能，包括创建、更新、支付、状态管理、查询、统计等。作为订单模块的业务服务层接口，它抽象了复杂的订单业务逻辑，为控制器层提供了统一的业务操作接口。该接口支持多种订单类型，实现了完善的状态机管理，并提供了丰富的查询和统计功能。

## 核心功能 (Core Functionality)
*   **订单生命周期管理**: 提供订单从创建到完成的完整生命周期管理功能
*   **状态机管理**: 实现复杂的订单状态流转和业务规则控制
*   **支付处理**: 集成支付流程，支持多种支付方式和支付状态管理
*   **库存管理**: 与库存系统集成，实现库存预留、扣减和释放
*   **订单编号生成**: 提供唯一的订单编号生成策略和规则
*   **金额计算**: 支持复杂的订单金额计算，包括商品价格、运费、优惠等
*   **多维度查询**: 支持按状态、时间、用户等多种维度查询订单
*   **统计分析**: 提供订单统计和分析功能，支持业务决策
*   **事件驱动**: 集成事件发布机制，支持订单状态变更的异步处理
*   **权限控制**: 实现基于用户角色的订单访问权限控制
*   **审计日志**: 记录订单操作的详细审计日志，满足合规要求
*   **异常处理**: 完善的异常处理机制，确保订单操作的可靠性

## 接口说明 (Interface Description)

### 订单创建方法

#### createOrder - 创建订单
*   **方法签名**: `String createOrder(CreateOrderRequest request)`
*   **参数**: `request` (CreateOrderRequest) - 订单创建请求对象，包含买家信息、商品信息、收货地址等
*   **返回值**: `String` - 生成的订单ID
*   **业务逻辑**:
    *   验证请求参数的完整性和有效性
    *   检查商品库存是否充足
    *   生成唯一的订单编号（格式：ORD-YYYYMMDD-XXXX）
    *   计算订单总金额，包括商品价格、运费、优惠券等
    *   初始化订单状态为"待支付"
    *   预留商品库存
    *   保存订单记录和订单项
    *   发布订单创建事件
    *   返回订单ID

#### createOrderFromRequirement - 基于需求创建订单
*   **方法签名**: `String createOrderFromRequirement(Long requirementId, CreateOrderRequest request)`
*   **参数**:
    *   `requirementId` (Long) - 采购需求ID
    *   `request` (CreateOrderRequest) - 订单创建请求
*   **返回值**: `String` - 生成的订单ID
*   **业务逻辑**:
    *   验证采购需求的有效性和状态
    *   关联订单与采购需求
    *   执行标准订单创建流程
    *   更新采购需求状态

### 订单更新方法

#### updateOrder - 更新订单信息
*   **方法签名**: `void updateOrder(String orderId, UpdateOrderRequest request)`
*   **参数**:
    *   `orderId` (String) - 订单ID
    *   `request` (UpdateOrderRequest) - 更新请求对象
*   **业务逻辑**:
    *   验证订单存在性和用户权限
    *   检查订单状态是否允许更新
    *   验证更新数据的有效性
    *   记录变更历史到审计日志
    *   更新订单信息
    *   重新计算相关费用（如有必要）
    *   触发订单更新事件

#### updateOrderStatus - 更新订单状态
*   **方法签名**: `void updateOrderStatus(String orderId, OrderStatus newStatus, String reason)`
*   **参数**:
    *   `orderId` (String) - 订单ID
    *   `newStatus` (OrderStatus) - 新状态
    *   `reason` (String) - 状态变更原因
*   **业务逻辑**:
    *   验证状态流转的合法性
    *   执行状态变更前的业务检查
    *   更新订单状态和时间戳
    *   记录状态变更日志
    *   触发状态变更事件

### 支付处理方法

#### processPayment - 处理订单支付
*   **方法签名**: `void processPayment(String orderId, PaymentInfo paymentInfo)`
*   **参数**:
    *   `orderId` (String) - 订单ID
    *   `paymentInfo` (PaymentInfo) - 支付信息对象
*   **业务逻辑**:
    *   验证订单状态为"待支付"
    *   验证支付金额与订单金额匹配
    *   调用支付网关处理支付
    *   更新订单支付状态和支付时间
    *   确认库存扣减
    *   发布支付成功事件
    *   发送支付确认通知

### 查询方法

#### getOrderById - 根据ID获取订单详情
*   **方法签名**: `OrderDetailResponse getOrderById(String orderId)`
*   **参数**: `orderId` (String) - 订单ID
*   **返回值**: `OrderDetailResponse` - 订单详情响应对象
*   **业务逻辑**:
    *   验证订单存在性
    *   检查用户访问权限
    *   查询订单基本信息
    *   查询订单项列表
    *   查询订单状态历史
    *   组装完整的订单详情

#### getOrdersByUser - 获取用户订单列表
*   **方法签名**: `PageResult<OrderSimpleResponse> getOrdersByUser(Long userId, OrderQueryRequest request)`
*   **参数**:
    *   `userId` (Long) - 用户ID
    *   `request` (OrderQueryRequest) - 查询请求对象
*   **返回值**: `PageResult<OrderSimpleResponse>` - 分页的订单简要信息列表
*   **业务逻辑**:
    *   验证用户权限
    *   构建查询条件
    *   执行分页查询
    *   转换为响应格式

#### getOrdersByStatus - 按状态查询订单
*   **方法签名**: `List<OrderSimpleResponse> getOrdersByStatus(OrderStatus status, Integer limit)`
*   **参数**:
    *   `status` (OrderStatus) - 订单状态
    *   `limit` (Integer) - 返回数量限制
*   **返回值**: `List<OrderSimpleResponse>` - 订单简要信息列表
*   **业务逻辑**:
    *   验证状态参数有效性
    *   执行状态查询
    *   限制返回数量

### 统计方法

#### getOrderStatistics - 获取订单统计信息
*   **方法签名**: `OrderStatisticsResponse getOrderStatistics(StatisticsRequest request)`
*   **参数**: `request` (StatisticsRequest) - 统计请求对象
*   **返回值**: `OrderStatisticsResponse` - 订单统计响应对象
*   **业务逻辑**:
    *   验证统计时间范围
    *   执行多维度统计查询
    *   计算统计指标
    *   组装统计结果

#### getTotalOrderCount - 获取订单总数
*   **方法签名**: `Long getTotalOrderCount()`
*   **返回值**: `Long` - 订单总数量
*   **业务逻辑**:
    *   查询所有有效订单数量
    *   排除已删除的订单

### 业务操作方法

#### cancelOrder - 取消订单
*   **方法签名**: `void cancelOrder(String orderId, String reason)`
*   **参数**:
    *   `orderId` (String) - 订单ID
    *   `reason` (String) - 取消原因
*   **业务逻辑**:
    *   验证订单状态允许取消
    *   释放预留库存
    *   处理退款（如已支付）
    *   更新订单状态为已取消
    *   发送取消通知

#### confirmOrder - 确认订单
*   **方法签名**: `void confirmOrder(String orderId)`
*   **参数**: `orderId` (String) - 订单ID
*   **业务逻辑**:
    *   验证订单状态为已支付
    *   确认库存扣减
    *   更新订单状态为处理中
    *   发送确认通知

## 状态管理 (State Management)
```mermaid
stateDiagram-v2
    [*] --> DRAFT: 创建草稿
    DRAFT --> PENDING_PAYMENT: 提交订单
    PENDING_PAYMENT --> PAID: 支付成功
    PENDING_PAYMENT --> CANCELLED: 支付超时/取消
    PAID --> PROCESSING: 确认订单
    PROCESSING --> SHIPPED: 发货
    SHIPPED --> COMPLETED: 确认收货
    PAID --> REFUNDED: 申请退款
    PROCESSING --> REFUNDED: 申请退款
    SHIPPED --> REFUNDED: 申请退款
    CANCELLED --> [*]: 结束
    COMPLETED --> [*]: 结束
    REFUNDED --> [*]: 结束
```

## 业务规则 (Business Rules)
*   **订单编号**: 全局唯一，格式为ORD-YYYYMMDD-XXXX，创建后不可修改
*   **支付超时**: 订单创建后30分钟内未支付自动取消，释放库存
*   **状态流转**: 严格按照状态机规则流转，不允许跳跃或回退
*   **权限控制**: 买家只能操作自己的订单，卖家可查看相关订单，管理员拥有全部权限
*   **库存管理**: 订单创建时预留库存，支付成功后确认扣减，取消时释放
*   **金额计算**: 订单金额 = 商品总价 + 运费 - 优惠金额，支持多种优惠策略
*   **审计要求**: 所有订单操作必须记录操作人、操作时间和操作原因
*   **并发控制**: 使用乐观锁防止订单状态的并发修改冲突

## 使用示例 (Usage Examples)

```java
// 1. 服务实现类示例
@Service
@Transactional
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderItemMapper orderItemMapper;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private PaymentService paymentService;

    @Override
    public String createOrder(CreateOrderRequest request) {
        // 1. 参数验证
        validateCreateOrderRequest(request);

        // 2. 检查库存
        for (OrderItemRequest item : request.getItems()) {
            if (!inventoryService.checkStock(item.getSkuId(), item.getQuantity())) {
                throw new BusinessException("商品库存不足: " + item.getSkuId());
            }
        }

        // 3. 生成订单编号
        String orderNumber = generateOrderNumber();

        // 4. 计算订单金额
        BigDecimal totalAmount = calculateOrderAmount(request);

        // 5. 创建订单实体
        Order order = new Order();
        order.setOrderNumber(orderNumber);
        order.setBuyerId(request.getBuyerId());
        order.setSellerId(request.getSellerId());
        order.setTotalAmount(totalAmount);
        order.setStatus(OrderStatus.PENDING_PAYMENT);
        order.setCreatedAt(LocalDateTime.now());

        // 6. 保存订单
        orderMapper.insert(order);

        // 7. 保存订单项
        for (OrderItemRequest itemRequest : request.getItems()) {
            OrderItem item = new OrderItem();
            item.setOrderId(order.getId());
            item.setSkuId(itemRequest.getSkuId());
            item.setQuantity(itemRequest.getQuantity());
            item.setUnitPrice(itemRequest.getUnitPrice());
            item.setTotalPrice(itemRequest.getTotalPrice());
            orderItemMapper.insert(item);
        }

        // 8. 预留库存
        for (OrderItemRequest item : request.getItems()) {
            inventoryService.reserveStock(item.getSkuId(), item.getQuantity(), order.getId());
        }

        // 9. 发布订单创建事件
        applicationEventPublisher.publishEvent(new OrderCreatedEvent(order));

        // 10. 记录审计日志
        auditLogService.logOrderCreation(order.getId(), request.getBuyerId());

        return order.getId();
    }

    @Override
    public void processPayment(String orderId, PaymentInfo paymentInfo) {
        // 1. 获取订单
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在: " + orderId);
        }

        // 2. 验证订单状态
        if (order.getStatus() != OrderStatus.PENDING_PAYMENT) {
            throw new BusinessException("订单状态不允许支付: " + order.getStatus());
        }

        // 3. 验证支付金额
        if (paymentInfo.getAmount().compareTo(order.getTotalAmount()) != 0) {
            throw new BusinessException("支付金额与订单金额不匹配");
        }

        // 4. 调用支付服务
        PaymentResult paymentResult = paymentService.processPayment(paymentInfo);

        if (paymentResult.isSuccess()) {
            // 5. 更新订单状态
            order.setStatus(OrderStatus.PAID);
            order.setPaymentTime(LocalDateTime.now());
            order.setPaymentMethod(paymentInfo.getPaymentMethod());
            order.setTransactionId(paymentResult.getTransactionId());
            orderMapper.updateById(order);

            // 6. 确认库存扣减
            List<OrderItem> items = orderItemMapper.selectByOrderId(orderId);
            for (OrderItem item : items) {
                inventoryService.confirmStockReduction(item.getSkuId(), item.getQuantity(), orderId);
            }

            // 7. 发布支付成功事件
            applicationEventPublisher.publishEvent(new PaymentSuccessEvent(order, paymentResult));

            // 8. 发送支付确认通知
            notificationService.sendPaymentConfirmation(order);

        } else {
            throw new BusinessException("支付失败: " + paymentResult.getErrorMessage());
        }
    }

    @Override
    public void cancelOrder(String orderId, String reason) {
        // 1. 获取订单
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在: " + orderId);
        }

        // 2. 验证是否可以取消
        if (!canCancelOrder(order.getStatus())) {
            throw new BusinessException("当前状态不允许取消订单: " + order.getStatus());
        }

        // 3. 释放库存
        List<OrderItem> items = orderItemMapper.selectByOrderId(orderId);
        for (OrderItem item : items) {
            inventoryService.releaseStock(item.getSkuId(), item.getQuantity(), orderId);
        }

        // 4. 处理退款（如果已支付）
        if (order.getStatus() == OrderStatus.PAID) {
            RefundRequest refundRequest = new RefundRequest();
            refundRequest.setOrderId(orderId);
            refundRequest.setAmount(order.getTotalAmount());
            refundRequest.setReason(reason);
            paymentService.processRefund(refundRequest);
        }

        // 5. 更新订单状态
        order.setStatus(OrderStatus.CANCELLED);
        order.setCancelReason(reason);
        order.setCancelTime(LocalDateTime.now());
        orderMapper.updateById(order);

        // 6. 发布订单取消事件
        applicationEventPublisher.publishEvent(new OrderCancelledEvent(order, reason));

        // 7. 发送取消通知
        notificationService.sendOrderCancellationNotification(order);
    }

    // 私有辅助方法
    private String generateOrderNumber() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String sequence = String.format("%04d", getNextSequence());
        return "ORD-" + dateStr + "-" + sequence;
    }

    private BigDecimal calculateOrderAmount(CreateOrderRequest request) {
        BigDecimal itemsTotal = request.getItems().stream()
            .map(item -> item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal shippingFee = calculateShippingFee(request);
        BigDecimal discountAmount = calculateDiscountAmount(request);

        return itemsTotal.add(shippingFee).subtract(discountAmount);
    }

    private boolean canCancelOrder(OrderStatus status) {
        return status == OrderStatus.PENDING_PAYMENT ||
               status == OrderStatus.PAID ||
               status == OrderStatus.PROCESSING;
    }
}

// 2. 控制器层调用示例
@RestController
@RequestMapping("/api/v1/orders")
public class OrderController {

    @Autowired
    private OrderService orderService;

    // 创建订单
    @PostMapping
    public Result<String> createOrder(@Valid @RequestBody CreateOrderRequest request) {
        try {
            String orderId = orderService.createOrder(request);
            return Result.success(orderId);
        } catch (BusinessException e) {
            return Result.error(e.getMessage());
        }
    }

    // 支付订单
    @PostMapping("/{orderId}/payment")
    public Result<String> processPayment(
            @PathVariable String orderId,
            @Valid @RequestBody PaymentRequest request) {

        try {
            PaymentInfo paymentInfo = new PaymentInfo();
            paymentInfo.setAmount(request.getAmount());
            paymentInfo.setPaymentMethod(request.getPaymentMethod());
            paymentInfo.setPaymentAccount(request.getPaymentAccount());

            orderService.processPayment(orderId, paymentInfo);
            return Result.success("支付成功");
        } catch (BusinessException e) {
            return Result.error(e.getMessage());
        }
    }

    // 取消订单
    @PostMapping("/{orderId}/cancel")
    public Result<String> cancelOrder(
            @PathVariable String orderId,
            @RequestBody CancelOrderRequest request) {

        try {
            orderService.cancelOrder(orderId, request.getReason());
            return Result.success("订单已取消");
        } catch (BusinessException e) {
            return Result.error(e.getMessage());
        }
    }
}

// 3. 事件监听器示例
@Component
public class OrderEventListener {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private CommissionService commissionService;

    // 监听订单创建事件
    @EventListener
    @Async
    public void handleOrderCreated(OrderCreatedEvent event) {
        Order order = event.getOrder();

        // 发送订单创建通知
        notificationService.sendOrderCreatedNotification(order);

        // 记录业务日志
        log.info("订单创建成功: orderId={}, buyerId={}, amount={}",
            order.getId(), order.getBuyerId(), order.getTotalAmount());
    }

    // 监听支付成功事件
    @EventListener
    @Async
    public void handlePaymentSuccess(PaymentSuccessEvent event) {
        Order order = event.getOrder();

        // 计算佣金
        commissionService.calculateCommission(order);

        // 发送支付成功通知
        notificationService.sendPaymentSuccessNotification(order);

        // 启动发货流程
        fulfillmentService.startFulfillment(order.getId());
    }

    // 监听订单取消事件
    @EventListener
    @Async
    public void handleOrderCancelled(OrderCancelledEvent event) {
        Order order = event.getOrder();
        String reason = event.getReason();

        // 发送取消通知
        notificationService.sendOrderCancelledNotification(order, reason);

        // 记录取消统计
        statisticsService.recordOrderCancellation(order, reason);
    }
}

// 4. 定时任务示例
@Component
public class OrderScheduledTasks {

    @Autowired
    private OrderService orderService;

    // 检查超时未支付订单
    @Scheduled(cron = "0 */5 * * * ?") // 每5分钟执行一次
    public void checkTimeoutOrders() {
        log.info("开始检查超时未支付订单");

        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(30);
            List<Order> timeoutOrders = orderMapper.selectTimeoutOrders(cutoffTime);

            for (Order order : timeoutOrders) {
                try {
                    orderService.cancelOrder(order.getId(), "支付超时自动取消");
                    log.info("自动取消超时订单: orderId={}", order.getId());
                } catch (Exception e) {
                    log.error("自动取消订单失败: orderId={}", order.getId(), e);
                }
            }

            log.info("超时订单检查完成，处理订单数: {}", timeoutOrders.size());

        } catch (Exception e) {
            log.error("检查超时订单失败", e);
        }
    }

    // 生成订单统计报告
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点
    public void generateDailyOrderReport() {
        log.info("开始生成日度订单报告");

        try {
            LocalDate yesterday = LocalDate.now().minusDays(1);

            DailyOrderReport report = new DailyOrderReport();
            report.setReportDate(yesterday);
            report.setTotalOrders(orderService.getOrderCountByDate(yesterday));
            report.setPaidOrders(orderService.getPaidOrderCountByDate(yesterday));
            report.setCancelledOrders(orderService.getCancelledOrderCountByDate(yesterday));
            report.setTotalAmount(orderService.getTotalAmountByDate(yesterday));

            // 发送报告给管理员
            notificationService.sendDailyOrderReport(report);

            log.info("日度订单报告生成完成: date={}", yesterday);

        } catch (Exception e) {
            log.error("生成日度订单报告失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
@Transactional
class OrderServiceTest {

    @Autowired
    private OrderService orderService;

    @MockBean
    private InventoryService inventoryService;

    @MockBean
    private PaymentService paymentService;

    @Test
    void testCreateOrder() {
        // 准备测试数据
        CreateOrderRequest request = new CreateOrderRequest();
        request.setBuyerId(1L);
        request.setSellerId(2L);
        request.setDeliveryAddress("测试地址");

        List<OrderItemRequest> items = new ArrayList<>();
        OrderItemRequest item = new OrderItemRequest();
        item.setSkuId("SKU001");
        item.setQuantity(2);
        item.setUnitPrice(new BigDecimal("100.00"));
        items.add(item);
        request.setItems(items);

        // Mock库存检查
        when(inventoryService.checkStock("SKU001", 2)).thenReturn(true);

        // 执行测试
        String orderId = orderService.createOrder(request);

        // 验证结果
        assertThat(orderId).isNotNull();

        // 验证订单已创建
        Order order = orderMapper.selectById(orderId);
        assertThat(order).isNotNull();
        assertThat(order.getStatus()).isEqualTo(OrderStatus.PENDING_PAYMENT);
        assertThat(order.getTotalAmount()).isEqualTo(new BigDecimal("200.00"));

        // 验证库存预留
        verify(inventoryService, times(1)).reserveStock("SKU001", 2, orderId);
    }

    @Test
    void testProcessPayment() {
        // 准备测试数据
        String orderId = "test-order-id";
        Order order = new Order();
        order.setId(orderId);
        order.setStatus(OrderStatus.PENDING_PAYMENT);
        order.setTotalAmount(new BigDecimal("200.00"));

        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setAmount(new BigDecimal("200.00"));
        paymentInfo.setPaymentMethod(PaymentMethod.ALIPAY);

        PaymentResult paymentResult = new PaymentResult();
        paymentResult.setSuccess(true);
        paymentResult.setTransactionId("txn123");

        // Mock数据
        when(orderMapper.selectById(orderId)).thenReturn(order);
        when(paymentService.processPayment(paymentInfo)).thenReturn(paymentResult);

        // 执行测试
        orderService.processPayment(orderId, paymentInfo);

        // 验证结果
        verify(paymentService, times(1)).processPayment(paymentInfo);
        verify(orderMapper, times(1)).updateById(any(Order.class));
        verify(inventoryService, times(1)).confirmStockReduction(anyString(), anyInt(), eq(orderId));
    }
}
```

## 注意事项 (Notes)
*   **事务管理**: 订单操作涉及多个数据表，必须使用@Transactional注解确保数据一致性
*   **并发控制**: 使用乐观锁或分布式锁防止订单状态的并发修改冲突
*   **库存管理**: 订单创建时预留库存，支付成功后确认扣减，取消时及时释放
*   **状态机**: 严格按照状态机规则进行状态流转，不允许跳跃或非法回退
*   **异常处理**: 完善的异常处理机制，确保业务异常能够正确回滚和恢复
*   **审计日志**: 所有重要操作必须记录审计日志，包括操作人、时间、原因等
*   **性能优化**: 大量订单查询时使用分页和索引优化，避免全表扫描
*   **缓存策略**: 订单详情等频繁查询的数据可以考虑使用缓存提高性能
*   **消息队列**: 订单状态变更等事件可以通过消息队列异步处理，提高系统响应性
*   **幂等性**: 支付等关键操作需要保证幂等性，防止重复处理
*   **超时处理**: 设置合理的支付超时时间，并通过定时任务自动处理超时订单
*   **数据一致性**: 订单金额计算需要考虑精度问题，使用BigDecimal进行金额运算
*   **权限验证**: 确保用户只能操作自己有权限的订单，防止越权访问
*   **监控告警**: 对订单创建失败、支付异常等关键指标进行监控和告警
*   **国际化**: 订单状态和错误消息需要支持多语言，便于国际化部署
*   **接口版本**: 订单API的版本管理需要考虑向后兼容性，避免影响现有功能