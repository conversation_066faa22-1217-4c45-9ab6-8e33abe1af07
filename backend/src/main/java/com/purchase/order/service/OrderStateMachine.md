## 订单状态机规范

### 状态定义
```mermaid
stateDiagram-v2
    [*] --> DRAFT
    DRAFT --> PENDING_PAYMENT: 提交订单
    PENDING_PAYMENT --> PAID: 支付成功
    PENDING_PAYMENT --> CANCELLED: 用户取消
    PAID --> PROCESSING: 开始处理
    PROCESSING --> SHIPPED: 发货完成
    PROCESSING --> CANCELLED: 库存不足取消
    SHIPPED --> COMPLETED: 确认收货
    SHIPPED --> REFUNDED: 申请退款
    any --> FAILED: 系统异常
```

### 事件触发
| 事件 | 触发条件 | 源状态 | 目标状态 |
|-----|---------|-------|---------|
| ORDER_SUBMITTED | 用户提交订单 | DRAFT | PENDING_PAYMENT |
| PAYMENT_RECEIVED | 支付网关回调成功 | PENDING_PAYMENT | PAID |
| PAYMENT_TIMEOUT | 30分钟未支付 | PENDING_PAYMENT | CANCELLED |
| INVENTORY_RESERVED | 库存预留完成 | PAID | PROCESSING |
| SHIPPING_CONFIRMED | 物流单号生成 | PROCESSING | SHIPPED |
| DELIVERY_CONFIRMED | 用户确认收货 | SHIPPED | COMPLETED |
| REFUND_REQUESTED | 用户发起退款 | SHIPPED | REFUNDED |

### 业务规则
1. 状态转换需记录操作人
2. 重要变更需触发事件通知
3. 逆向流程需审批：
   - 取消已支付订单
   - 退款处理
4. 终态不可逆：
   - COMPLETED
   - CANCELLED
   - REFUNDED

### 异常处理
```mermaid
flowchart TD
    A[状态变更请求] --> B{校验当前状态}
    B -->|有效| C[执行转换]
    B -->|无效| D[抛出StateTransitionException]
    C --> E[持久化新状态]
    E --> F[触发后续操作]
```

## 使用示例
```java
// 状态转换代码示例
public void transitionState(Order order, OrderEvent event) {
    StateMachine<OrderStatus, OrderEvent> stateMachine = stateMachineFactory.getStateMachine();
    stateMachine.sendEvent(
        MessageBuilder.withPayload(event)
            .setHeader("orderId", order.getId())
            .build()
    );
}

// 监听状态变更示例
@OnStateChange(source = "PROCESSING", target = "SHIPPED")
public void handleOrderShipped(OrderShippedEvent event) {
    notificationService.sendShippingNotice(event.getOrderId());
}
```

## 注意事项
- 生产环境需保证状态转换的原子性
- 分布式场景需处理状态一致性
- 重要操作需添加审计日志