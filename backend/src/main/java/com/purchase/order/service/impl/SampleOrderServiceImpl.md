# SampleOrderServiceImpl.java

## 文件概述 (File Overview)
`SampleOrderServiceImpl.java` 是样品订单业务服务的实现类，位于 `com.purchase.order.service.impl` 包中。该类实现了 `SampleOrderService` 接口，提供了样品订单的完整业务逻辑处理，包括订单创建、查询、状态管理、发货、确认收货等核心功能。通过集成竞价、需求、用户等多个模块，实现了样品订单的全生命周期管理。该服务类包含19个主要方法，总计980行代码，是样品订单模块的核心业务处理组件。

## 核心功能 (Core Functionality)
*   **订单创建**: 支持基于需求和竞价创建样品订单，包含完整的业务验证
*   **订单查询**: 提供管理员和买家的分页查询、详情查询、完整详情查询
*   **订单管理**: 管理员处理订单、发货操作、状态更新
*   **买家操作**: 确认收货、取消订单、更新收货地址
*   **状态管理**: 订单状态和支付状态的完整生命周期管理
*   **权限控制**: 基于角色的业务权限验证和数据访问控制
*   **事务处理**: 关键操作的事务管理，确保数据一致性
*   **跨模块集成**: 与竞价、需求、用户模块的深度集成

## 接口说明 (Interface Description)

### 订单创建相关方法

#### createSampleOrder - 创建样品订单
*   **方法签名**: `SampleOrderResponse createSampleOrder(SampleOrderCreateRequest request, Long buyerId)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `request` (SampleOrderCreateRequest) - 订单创建请求，包含需求ID、竞价列表、联系信息等
    *   `buyerId` (Long) - 买家用户ID
*   **返回值**: `SampleOrderResponse` - 创建成功的订单响应对象
*   **业务逻辑**: 
    *   验证需求存在性和类型（必须是样品需求）
    *   验证买家权限（只能操作自己的需求）
    *   检查是否已存在样品订单（避免重复创建）
    *   验证竞价有效性和状态
    *   创建订单主记录和订单明细
    *   生成订单编号和初始状态设置

#### createSampleOrderByBiddings - 基于竞价创建样品订单
*   **方法签名**: `SampleOrderResponse createSampleOrderByBiddings(Long requirementId, List<Long> acceptedBiddingIds, Long buyerId)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `requirementId` (Long) - 需求ID
    *   `acceptedBiddingIds` (List<Long>) - 接受的竞价ID列表
    *   `buyerId` (Long) - 买家用户ID
*   **返回值**: `SampleOrderResponse` - 创建的订单响应
*   **业务逻辑**: 
    *   自动填充买家联系信息
    *   调用完整的订单创建方法
    *   简化的订单创建接口

### 管理员功能方法

#### getAdminSampleOrders - 管理员分页查询样品订单
*   **方法签名**: `IPage<SampleOrderResponse> getAdminSampleOrders(Integer page, Integer size, String status, Long buyerId, String orderNumber)`
*   **参数**: 
    *   `page` (Integer) - 页码
    *   `size` (Integer) - 每页大小
    *   `status` (String) - 订单状态筛选，可选
    *   `buyerId` (Long) - 买家ID筛选，可选
    *   `orderNumber` (String) - 订单编号筛选，可选
*   **返回值**: `IPage<SampleOrderResponse>` - 分页查询结果
*   **业务逻辑**: 
    *   支持多条件组合查询
    *   按创建时间倒序排列
    *   包含订单基本信息和统计数据

#### adminProcessSampleOrder - 管理员处理样品订单
*   **方法签名**: `SampleOrderResponse adminProcessSampleOrder(Long orderId, SampleOrderAdminProcessRequest request, Long adminId)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID
    *   `request` (SampleOrderAdminProcessRequest) - 处理请求，包含处理动作和备注
    *   `adminId` (Long) - 管理员ID
*   **返回值**: `SampleOrderResponse` - 处理后的订单信息
*   **业务逻辑**: 
    *   验证订单存在性和状态
    *   根据处理动作更新订单状态
    *   记录处理日志和操作人信息
    *   支持审批、拒绝等多种处理动作

#### shipSampleOrder - 管理员发货
*   **方法签名**: `SampleOrderResponse shipSampleOrder(Long orderId, SampleOrderShipRequest request, Long adminId)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID
    *   `request` (SampleOrderShipRequest) - 发货请求，包含物流信息
    *   `adminId` (Long) - 管理员ID
*   **返回值**: `SampleOrderResponse` - 发货后的订单信息
*   **业务逻辑**: 
    *   验证订单状态（只能发货已确认的订单）
    *   更新物流信息和发货时间
    *   变更订单状态为已发货
    *   记录发货操作日志

### 买家功能方法

#### getBuyerSampleOrders - 买家分页查询自己的样品订单
*   **方法签名**: `IPage<SampleOrderResponse> getBuyerSampleOrders(Long buyerId, Integer page, Integer size, String status)`
*   **参数**: 
    *   `buyerId` (Long) - 买家ID
    *   `page` (Integer) - 页码
    *   `size` (Integer) - 每页大小
    *   `status` (String) - 订单状态筛选，可选
*   **返回值**: `IPage<SampleOrderResponse>` - 买家的订单分页结果
*   **业务逻辑**: 
    *   只查询指定买家的订单
    *   支持按状态筛选
    *   按创建时间倒序排列

#### confirmReceipt - 买家确认收货
*   **方法签名**: `SampleOrderResponse confirmReceipt(Long orderId, Long buyerId)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID
    *   `buyerId` (Long) - 买家ID
*   **返回值**: `SampleOrderResponse` - 确认收货后的订单信息
*   **业务逻辑**: 
    *   验证订单所有权（买家只能确认自己的订单）
    *   验证订单状态（只能确认已发货的订单）
    *   更新订单状态为已完成
    *   记录确认收货时间

#### cancelSampleOrder - 取消样品订单
*   **方法签名**: `SampleOrderResponse cancelSampleOrder(Long orderId, Long buyerId, String reason)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID
    *   `buyerId` (Long) - 买家ID
    *   `reason` (String) - 取消原因
*   **返回值**: `SampleOrderResponse` - 取消后的订单信息
*   **业务逻辑**: 
    *   验证订单所有权和状态
    *   只能取消特定状态的订单（如待处理、已确认）
    *   更新订单状态为已取消
    *   记录取消原因和时间

#### updateShippingAddress - 更新收货地址
*   **方法签名**: `SampleOrderResponse updateShippingAddress(Long orderId, Long buyerId, String buyerAddress)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID
    *   `buyerId` (Long) - 买家ID
    *   `buyerAddress` (String) - 新的收货地址
*   **返回值**: `SampleOrderResponse` - 更新后的订单信息
*   **业务逻辑**: 
    *   验证订单所有权
    *   验证订单状态（只能在发货前更新地址）
    *   更新收货地址信息
    *   记录地址变更日志

### 查询和状态管理方法

#### getSampleOrderDetail - 获取样品订单详情
*   **方法签名**: `SampleOrderResponse getSampleOrderDetail(Long orderId, Long buyerId)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID
    *   `buyerId` (Long) - 买家ID
*   **返回值**: `SampleOrderResponse` - 订单详情响应
*   **业务逻辑**: 
    *   验证订单所有权
    *   返回订单基本信息和订单项详情
    *   包含状态、金额、物流等信息

#### getSampleOrderFullDetail - 获取样品订单完整详情
*   **方法签名**: `SampleOrderDetailVO getSampleOrderFullDetail(Long orderId)`
*   **参数**: `orderId` (Long) - 订单ID
*   **返回值**: `SampleOrderDetailVO` - 包含买家、卖家、需求信息的完整详情
*   **业务逻辑**: 
    *   查询订单基本信息
    *   关联查询买家、卖家、需求信息
    *   组装完整的订单详情对象
    *   管理员专用，包含所有相关信息

#### updatePaymentStatus - 更新支付状态
*   **方法签名**: `void updatePaymentStatus(Long sampleOrderId, String paymentStatus)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `sampleOrderId` (Long) - 样品订单ID
    *   `paymentStatus` (String) - 支付状态
*   **业务逻辑**: 
    *   验证订单存在性
    *   更新支付状态和支付时间
    *   触发相关业务流程（如自动确认订单）

#### canUploadPaymentProof - 检查是否可以上传支付凭证
*   **方法签名**: `boolean canUploadPaymentProof(Long sampleOrderId, Long buyerId)`
*   **参数**: 
    *   `sampleOrderId` (Long) - 样品订单ID
    *   `buyerId` (Long) - 买家ID
*   **返回值**: `boolean` - 是否可以上传支付凭证
*   **业务逻辑**:
    *   验证订单所有权
    *   检查订单状态是否允许上传支付凭证
    *   检查是否已经上传过支付凭证

## 使用示例 (Usage Examples)

```java
// 1. 控制器层调用示例
@RestController
public class SampleOrderController {

    @Autowired
    private SampleOrderServiceImpl sampleOrderService;

    // 创建样品订单
    @PostMapping("/sample-orders")
    public Result<SampleOrderResponse> createSampleOrder(
            @RequestBody SampleOrderCreateRequest request) {

        Long buyerId = getCurrentUserId();
        SampleOrderResponse response = sampleOrderService.createSampleOrder(request, buyerId);
        return Result.success("样品订单创建成功", response);
    }

    // 管理员处理订单
    @PostMapping("/admin/sample-orders/{orderId}/process")
    public Result<SampleOrderResponse> processOrder(
            @PathVariable Long orderId,
            @RequestBody SampleOrderAdminProcessRequest request) {

        Long adminId = getCurrentUserId();
        SampleOrderResponse response = sampleOrderService.adminProcessSampleOrder(orderId, request, adminId);
        return Result.success("订单处理成功", response);
    }

    // 买家确认收货
    @PostMapping("/sample-orders/{orderId}/confirm-receipt")
    public Result<SampleOrderResponse> confirmReceipt(@PathVariable Long orderId) {
        Long buyerId = getCurrentUserId();
        SampleOrderResponse response = sampleOrderService.confirmReceipt(orderId, buyerId);
        return Result.success("确认收货成功", response);
    }
}

// 2. 业务流程集成示例
@Service
public class OrderWorkflowService {

    @Autowired
    private SampleOrderServiceImpl sampleOrderService;

    @Autowired
    private PaymentService paymentService;

    // 支付成功后的业务处理
    @EventListener
    public void handlePaymentSuccess(PaymentSuccessEvent event) {
        if ("SAMPLE_ORDER".equals(event.getOrderType())) {
            // 更新样品订单支付状态
            sampleOrderService.updatePaymentStatus(event.getOrderId(), "PAID");

            // 自动确认订单（如果配置了自动确认）
            if (isAutoConfirmEnabled()) {
                SampleOrderAdminProcessRequest request = new SampleOrderAdminProcessRequest();
                request.setAction("CONFIRM");
                request.setRemark("支付成功自动确认");

                sampleOrderService.adminProcessSampleOrder(
                    event.getOrderId(), request, getSystemAdminId());
            }
        }
    }

    // 批量处理订单
    public void batchProcessOrders(List<Long> orderIds, String action, Long adminId) {
        for (Long orderId : orderIds) {
            try {
                SampleOrderAdminProcessRequest request = new SampleOrderAdminProcessRequest();
                request.setAction(action);
                request.setRemark("批量" + action);

                sampleOrderService.adminProcessSampleOrder(orderId, request, adminId);
                log.info("批量处理订单成功，订单ID: {}, 动作: {}", orderId, action);
            } catch (Exception e) {
                log.error("批量处理订单失败，订单ID: {}, 动作: {}, 错误: {}",
                         orderId, action, e.getMessage());
            }
        }
    }
}

// 3. 定时任务示例
@Component
public class SampleOrderScheduledTasks {

    @Autowired
    private SampleOrderServiceImpl sampleOrderService;

    // 自动确认收货（超时自动确认）
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void autoConfirmReceipt() {
        log.info("开始执行自动确认收货任务");

        // 查询已发货超过7天未确认收货的订单
        List<SampleOrder> expiredOrders = getExpiredShippedOrders();

        for (SampleOrder order : expiredOrders) {
            try {
                sampleOrderService.confirmSampleOrderReceived(
                    order.getId(), order.getBuyerId(), "系统自动确认收货");
                log.info("自动确认收货成功，订单ID: {}", order.getId());
            } catch (Exception e) {
                log.error("自动确认收货失败，订单ID: {}, 错误: {}",
                         order.getId(), e.getMessage());
            }
        }
    }

    // 订单状态同步
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void syncOrderStatus() {
        // 同步物流状态
        syncShippingStatus();

        // 同步支付状态
        syncPaymentStatus();
    }
}

// 4. 异常处理示例
@Service
public class SampleOrderServiceWrapper {

    @Autowired
    private SampleOrderServiceImpl sampleOrderService;

    public SampleOrderResponse createOrderSafely(SampleOrderCreateRequest request, Long buyerId) {
        try {
            return sampleOrderService.createSampleOrder(request, buyerId);
        } catch (BusinessException e) {
            log.error("创建样品订单业务异常，买家ID: {}, 错误: {}", buyerId, e.getMessage());
            throw e; // 重新抛出业务异常
        } catch (Exception e) {
            log.error("创建样品订单系统异常，买家ID: {}", buyerId, e);
            throw new BusinessException("系统繁忙，请稍后重试");
        }
    }

    public boolean canUploadPaymentProofSafely(Long orderId, Long buyerId) {
        try {
            return sampleOrderService.canUploadPaymentProof(orderId, buyerId);
        } catch (Exception e) {
            log.error("检查上传支付凭证权限异常，订单ID: {}, 买家ID: {}",
                     orderId, buyerId, e);
            return false; // 异常时返回false，保证安全性
        }
    }
}

// 5. 数据转换示例
@Component
public class SampleOrderConverter {

    // 实体转响应DTO
    public SampleOrderResponse convertToResponse(SampleOrder order, List<SampleOrderItem> items) {
        SampleOrderResponse response = new SampleOrderResponse();
        BeanUtils.copyProperties(order, response);

        // 转换订单项
        List<SampleOrderItemResponse> itemResponses = items.stream()
            .map(this::convertItemToResponse)
            .collect(Collectors.toList());
        response.setOrderItems(itemResponses);

        // 计算总金额
        BigDecimal totalAmount = items.stream()
            .map(SampleOrderItem::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        response.setTotalAmount(totalAmount);

        return response;
    }

    // 构建订单实体
    public SampleOrder buildSampleOrder(SampleOrderCreateRequest request,
                                       PurchaseRequirement requirement,
                                       Long buyerId,
                                       List<BiddingRecord> biddings) {
        SampleOrder order = new SampleOrder();
        order.setOrderNumber(generateOrderNumber());
        order.setRequirementId(request.getRequirementId());
        order.setBuyerId(buyerId);
        order.setBuyerContact(request.getBuyerContact());
        order.setBuyerPhone(request.getBuyerPhone());
        order.setBuyerEmail(request.getBuyerEmail());
        order.setBuyerAddress(request.getBuyerAddress());
        order.setRemark(request.getRemark());
        order.setOrderStatus("PENDING");
        order.setPaymentStatus("UNPAID");
        order.setCreatedAt(LocalDateTime.now());

        // 计算总金额
        BigDecimal totalAmount = biddings.stream()
            .map(BiddingRecord::getSamplePrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        order.setTotalAmount(totalAmount);

        return order;
    }
}
```

## 注意事项 (Notes)
*   **事务管理**: 所有修改操作都使用@Transactional注解，确保数据一致性，rollbackFor = Exception.class确保所有异常都回滚
*   **权限验证**: 严格的业务权限控制，买家只能操作自己的订单，管理员拥有全部权限
*   **状态管理**: 订单状态变更遵循严格的业务流程，不能随意跳转状态（如只能确认已发货的订单）
*   **数据验证**: 创建订单前进行完整的数据验证，包括需求类型、竞价有效性、重复创建检查等
*   **跨模块依赖**: 依赖竞价、需求、用户等多个模块，需要确保这些模块的数据一致性和服务可用性
*   **异常处理**: 使用BusinessException处理业务异常，提供明确的错误信息给前端
*   **日志记录**: 所有重要操作都有详细的日志记录，包括操作人、操作时间、操作内容等
*   **并发控制**: 订单状态变更时需要考虑并发情况，避免状态不一致问题
*   **性能优化**: 分页查询使用MyBatis-Plus的Page对象，大数据量查询时建议添加索引
*   **数据转换**: 实体对象和DTO之间的转换需要注意字段映射和数据脱敏
*   **业务规则**: 订单创建、状态变更等操作都有复杂的业务规则，需要严格按照业务流程执行
*   **订单编号**: 订单编号生成需要保证唯一性，建议使用分布式ID生成策略
*   **金额计算**: 涉及金额计算时使用BigDecimal，避免精度丢失问题
*   **软删除**: 订单删除使用软删除策略，保留历史数据便于审计和统计
*   **缓存策略**: 频繁查询的订单详情可以考虑使用Redis缓存提高性能
*   **监控告警**: 重要操作失败时应该有相应的监控告警机制
*   **数据一致性**: 订单创建涉及多表操作，需要确保事务的原子性
*   **接口幂等**: 订单创建等操作需要考虑接口幂等性，避免重复操作
