# OrderProgressActivityServiceImpl.java

## 文件概述 (File Overview)
`OrderProgressActivityServiceImpl.java` 是订单进度活动业务服务的实现类，位于 `com.purchase.order.service.impl` 包中。该类继承了MyBatis-Plus的 `ServiceImpl<OrderProgressActivityMapper, OrderProgressActivity>` 并实现了 `OrderProgressActivityService` 接口，提供了订单进度活动的完整业务逻辑处理。通过集成统一订单服务和用户服务，实现了订单进度的全生命周期跟踪管理。该服务类包含8个主要方法，总计398行代码，支持6个标准进度阶段的活动管理。

## 核心功能 (Core Functionality)
*   **进度活动管理**: 提供订单进度活动的完整CRUD操作
*   **阶段标准化**: 定义了6个标准进度阶段（0-订单创建、20-订单确认、40-生产中、60-生产完成、80-已发货、100-已送达）
*   **多维查询**: 支持按订单、按阶段、分页等多种查询方式
*   **分组展示**: 按进度阶段对活动进行分组，便于前端时间线展示
*   **数据转换**: 实体对象与响应DTO之间的完整转换
*   **用户信息集成**: 自动关联创建人的用户信息
*   **JSON处理**: 支持附件列表的JSON序列化和反序列化
*   **事务管理**: 关键操作的事务控制，确保数据一致性

## 接口说明 (Interface Description)

### 核心业务方法

#### createActivity - 创建订单进度活动
*   **方法签名**: `OrderProgressActivity createActivity(Long orderId, CreateProgressActivityRequest request, Long userId)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID，指定活动所属的订单
    *   `request` (CreateProgressActivityRequest) - 创建请求对象，包含活动详情
    *   `userId` (Long) - 创建用户ID，用于记录操作人
*   **返回值**: `OrderProgressActivity` - 创建成功的活动实体对象
*   **业务逻辑**: 
    *   验证订单是否存在（通过UnifiedOrderService）
    *   创建活动实体并设置基本信息
    *   处理附件列表的JSON序列化
    *   设置创建时间和创建人
    *   保存到数据库并返回完整实体

#### getActivitiesByOrderId - 获取订单的所有进度活动
*   **方法签名**: `List<ProgressActivityResponse> getActivitiesByOrderId(Long orderId)`
*   **参数**: `orderId` (Long) - 订单ID
*   **返回值**: `List<ProgressActivityResponse>` - 按时间降序排列的活动列表
*   **业务逻辑**: 
    *   查询指定订单的所有进度活动
    *   按创建时间降序排列，最新的活动在前
    *   转换为响应DTO对象，包含用户信息
    *   处理附件列表的JSON反序列化

#### getActivitiesByOrderIdGroupedByStage - 按节点分组获取订单进度活动
*   **方法签名**: `Map<Integer, List<ProgressActivityResponse>> getActivitiesByOrderIdGroupedByStage(Long orderId)`
*   **参数**: `orderId` (Long) - 订单ID
*   **返回值**: `Map<Integer, List<ProgressActivityResponse>>` - 按节点值分组的活动Map
*   **业务逻辑**: 
    *   获取订单的所有进度活动
    *   使用Stream API按stageValue进行分组
    *   每个阶段内的活动保持时间排序
    *   便于前端按阶段展示进度时间线

#### getActivitiesByOrderIdAndStage - 获取特定节点的所有活动
*   **方法签名**: `List<ProgressActivityResponse> getActivitiesByOrderIdAndStage(Long orderId, Integer stageValue)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID
    *   `stageValue` (Integer) - 节点值（0、20、40、60、80、100）
*   **返回值**: `List<ProgressActivityResponse>` - 特定阶段的活动列表
*   **业务逻辑**: 
    *   查询指定订单和阶段的所有活动
    *   按创建时间降序排列
    *   转换为响应DTO对象
    *   用于查看特定阶段的详细进度

#### getActivityById - 获取活动详情
*   **方法签名**: `ProgressActivityResponse getActivityById(Long activityId)`
*   **参数**: `activityId` (Long) - 活动ID
*   **返回值**: `ProgressActivityResponse` - 活动详情响应对象
*   **业务逻辑**: 
    *   根据活动ID查询活动详情
    *   验证活动是否存在
    *   转换为响应DTO对象，包含完整信息
    *   关联查询创建人信息

#### updateActivity - 更新活动
*   **方法签名**: `OrderProgressActivity updateActivity(Long activityId, CreateProgressActivityRequest request)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: 
    *   `activityId` (Long) - 活动ID
    *   `request` (CreateProgressActivityRequest) - 更新请求对象
*   **返回值**: `OrderProgressActivity` - 更新后的活动实体
*   **业务逻辑**: 
    *   验证活动是否存在
    *   更新活动的标题、描述、附件等信息
    *   处理附件列表的JSON序列化
    *   设置更新时间
    *   保存更新并返回实体

#### deleteActivity - 删除活动
*   **方法签名**: `void deleteActivity(Long activityId)`
*   **事务**: `@Transactional(rollbackFor = Exception.class)`
*   **参数**: `activityId` (Long) - 活动ID
*   **返回值**: `void`
*   **业务逻辑**: 
    *   验证活动是否存在
    *   执行物理删除操作
    *   记录删除日志

#### getActivityPage - 分页获取订单活动
*   **方法签名**: `IPage<ProgressActivityResponse> getActivityPage(Long orderId, Integer page, Integer size)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID
    *   `page` (Integer) - 页码，从1开始
    *   `size` (Integer) - 每页大小
*   **返回值**: `IPage<ProgressActivityResponse>` - 分页结果对象
*   **业务逻辑**: 
    *   分页查询指定订单的进度活动
    *   按创建时间降序排列
    *   转换为响应DTO对象
    *   返回分页结果，包含总数、当前页等信息

### 辅助方法

#### convertToResponse - 实体转响应DTO
*   **方法签名**: `ProgressActivityResponse convertToResponse(OrderProgressActivity activity)`
*   **参数**: `activity` (OrderProgressActivity) - 活动实体对象
*   **返回值**: `ProgressActivityResponse` - 响应DTO对象
*   **业务逻辑**: 
    *   复制基本属性到响应对象
    *   关联查询创建人信息
    *   设置阶段标签（根据STAGE_LABELS映射）
    *   处理附件列表的JSON反序列化
    *   处理JSON解析异常

### 常量定义

#### STAGE_LABELS - 进度阶段标签映射
*   **类型**: `Map<Integer, String>`
*   **内容**: 
    *   `0` -> "订单创建"
    *   `20` -> "订单确认"
    *   `40` -> "生产中"
    *   `60` -> "生产完成"
    *   `80` -> "已发货"
    *   `100` -> "已送达"
*   **用途**: 为前端提供阶段标签显示，保持前后端一致性

## 使用示例 (Usage Examples)

```java
// 1. 控制器层调用示例
@RestController
public class OrderProgressController {
    
    @Autowired
    private OrderProgressActivityServiceImpl activityService;
    
    // 创建进度活动
    @PostMapping("/orders/{orderId}/progress/activities")
    public Result<OrderProgressActivity> createActivity(
            @PathVariable Long orderId,
            @RequestBody CreateProgressActivityRequest request) {
        
        Long currentUserId = getCurrentUserId();
        OrderProgressActivity activity = activityService.createActivity(orderId, request, currentUserId);
        return Result.success("进度活动创建成功", activity);
    }
    
    // 获取订单进度时间线
    @GetMapping("/orders/{orderId}/progress/timeline")
    public Result<Map<Integer, List<ProgressActivityResponse>>> getProgressTimeline(@PathVariable Long orderId) {
        Map<Integer, List<ProgressActivityResponse>> timeline = 
            activityService.getActivitiesByOrderIdGroupedByStage(orderId);
        return Result.success(timeline);
    }
    
    // 分页获取订单活动
    @GetMapping("/orders/{orderId}/progress/activities")
    public Result<IPage<ProgressActivityResponse>> getActivityPage(
            @PathVariable Long orderId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        IPage<ProgressActivityResponse> result = activityService.getActivityPage(orderId, page, size);
        return Result.success(result);
    }
}

// 2. 业务流程集成示例
@Service
public class OrderWorkflowService {
    
    @Autowired
    private OrderProgressActivityServiceImpl activityService;
    
    // 自动创建阶段完成活动
    public void completeOrderStage(Long orderId, Integer stageValue, String description, Long operatorId) {
        CreateProgressActivityRequest request = new CreateProgressActivityRequest();
        request.setStageValue(stageValue);
        request.setTitle(getStageTitle(stageValue) + "已完成");
        request.setDescription(description);
        request.setActivityType("STAGE_COMPLETE");
        
        activityService.createActivity(orderId, request, operatorId);
        
        log.info("订单阶段完成活动已创建，订单ID: {}, 阶段: {}", orderId, stageValue);
    }
    
    // 批量创建进度活动
    public void batchCreateActivities(Long orderId, List<CreateProgressActivityRequest> requests, Long operatorId) {
        for (CreateProgressActivityRequest request : requests) {
            try {
                activityService.createActivity(orderId, request, operatorId);
            } catch (Exception e) {
                log.error("批量创建进度活动失败，订单ID: {}, 阶段: {}", 
                         orderId, request.getStageValue(), e);
            }
        }
    }
    
    // 获取订单当前阶段
    public Integer getCurrentStage(Long orderId) {
        Map<Integer, List<ProgressActivityResponse>> groupedActivities = 
            activityService.getActivitiesByOrderIdGroupedByStage(orderId);
        
        return groupedActivities.keySet().stream()
                .max(Integer::compareTo)
                .orElse(0);
    }
    
    // 检查阶段是否完成
    public boolean isStageCompleted(Long orderId, Integer stageValue) {
        List<ProgressActivityResponse> stageActivities = 
            activityService.getActivitiesByOrderIdAndStage(orderId, stageValue);
        
        return stageActivities.stream()
                .anyMatch(activity -> "STAGE_COMPLETE".equals(activity.getActivityType()));
    }
}

// 3. 前端集成示例
// JavaScript调用示例
const OrderProgressAPI = {
    // 创建进度活动
    async createActivity(orderId, activityData) {
        const response = await fetch(`/api/orders/${orderId}/progress/activities`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                stageValue: activityData.stageValue,
                title: activityData.title,
                description: activityData.description,
                attachments: activityData.attachments || [],
                activityType: activityData.activityType || 'MANUAL'
            })
        });
        return await response.json();
    },

    // 获取进度时间线
    async getProgressTimeline(orderId) {
        const response = await fetch(`/api/orders/${orderId}/progress/timeline`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });
        const result = await response.json();
        return result.data;
    },

    // 渲染进度时间线
    renderTimeline(timelineData) {
        const stages = [0, 20, 40, 60, 80, 100];
        const stageLabels = {
            0: '订单创建',
            20: '订单确认',
            40: '生产中',
            60: '生产完成',
            80: '已发货',
            100: '已送达'
        };

        stages.forEach(stage => {
            const activities = timelineData[stage] || [];
            const stageElement = document.getElementById(`stage-${stage}`);

            if (activities.length > 0) {
                stageElement.classList.add('completed');
                const latestActivity = activities[0]; // 最新的活动
                stageElement.querySelector('.stage-time').textContent =
                    new Date(latestActivity.createdAt).toLocaleString();
                stageElement.querySelector('.stage-description').textContent =
                    latestActivity.description;
            }
        });
    }
};

// 4. 定时任务示例
@Component
public class OrderProgressScheduledTasks {

    @Autowired
    private OrderProgressActivityServiceImpl activityService;

    @Autowired
    private OrderService orderService;

    // 自动创建阶段超时提醒
    @Scheduled(cron = "0 0 9 * * ?") // 每天早上9点执行
    public void checkStageTimeout() {
        log.info("开始检查订单阶段超时");

        List<Order> activeOrders = orderService.getActiveOrders();

        for (Order order : activeOrders) {
            try {
                Integer currentStage = getCurrentStage(order.getId());
                if (isStageTimeout(order.getId(), currentStage)) {
                    createTimeoutReminder(order.getId(), currentStage);
                }
            } catch (Exception e) {
                log.error("检查订单阶段超时失败，订单ID: {}", order.getId(), e);
            }
        }
    }

    private void createTimeoutReminder(Long orderId, Integer stageValue) {
        CreateProgressActivityRequest request = new CreateProgressActivityRequest();
        request.setStageValue(stageValue);
        request.setTitle("阶段超时提醒");
        request.setDescription("当前阶段已超过预期时间，请及时处理");
        request.setActivityType("TIMEOUT_REMINDER");

        activityService.createActivity(orderId, request, getSystemUserId());

        // 发送通知给相关人员
        sendTimeoutNotification(orderId, stageValue);
    }
}

// 5. 数据统计示例
@Service
public class OrderProgressStatisticsService {

    @Autowired
    private OrderProgressActivityServiceImpl activityService;

    // 统计各阶段平均耗时
    public Map<Integer, Double> getAverageStageTime(List<Long> orderIds) {
        Map<Integer, List<Long>> stageTimes = new HashMap<>();

        for (Long orderId : orderIds) {
            Map<Integer, List<ProgressActivityResponse>> timeline =
                activityService.getActivitiesByOrderIdGroupedByStage(orderId);

            // 计算各阶段耗时
            for (int i = 0; i < 100; i += 20) {
                Long stageTime = calculateStageTime(timeline, i, i + 20);
                if (stageTime != null) {
                    stageTimes.computeIfAbsent(i, k -> new ArrayList<>()).add(stageTime);
                }
            }
        }

        // 计算平均值
        return stageTimes.entrySet().stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().stream()
                            .mapToLong(Long::longValue)
                            .average()
                            .orElse(0.0)
                ));
    }

    // 生成进度报告
    public OrderProgressReport generateProgressReport(Long orderId) {
        Map<Integer, List<ProgressActivityResponse>> timeline =
            activityService.getActivitiesByOrderIdGroupedByStage(orderId);

        OrderProgressReport report = new OrderProgressReport();
        report.setOrderId(orderId);
        report.setCurrentStage(getCurrentStage(timeline));
        report.setCompletedStages(getCompletedStages(timeline));
        report.setTotalActivities(getTotalActivities(timeline));
        report.setLastUpdateTime(getLastUpdateTime(timeline));

        return report;
    }
}

// 6. 异常处理示例
@Service
public class OrderProgressActivityServiceWrapper {

    @Autowired
    private OrderProgressActivityServiceImpl activityService;

    public OrderProgressActivity createActivitySafely(Long orderId, CreateProgressActivityRequest request, Long userId) {
        try {
            return activityService.createActivity(orderId, request, userId);
        } catch (BusinessException e) {
            log.error("创建进度活动业务异常，订单ID: {}, 阶段: {}, 错误: {}",
                     orderId, request.getStageValue(), e.getMessage());
            throw e; // 重新抛出业务异常
        } catch (JsonProcessingException e) {
            log.error("创建进度活动JSON处理异常，订单ID: {}", orderId, e);
            throw new BusinessException("附件信息格式错误");
        } catch (Exception e) {
            log.error("创建进度活动系统异常，订单ID: {}", orderId, e);
            throw new BusinessException("系统繁忙，请稍后重试");
        }
    }

    public List<ProgressActivityResponse> getActivitiesSafely(Long orderId) {
        try {
            return activityService.getActivitiesByOrderId(orderId);
        } catch (Exception e) {
            log.error("获取订单进度活动异常，订单ID: {}", orderId, e);
            return Collections.emptyList(); // 异常时返回空列表
        }
    }
}
```

## 注意事项 (Notes)
*   **事务管理**: 创建、更新、删除操作都使用@Transactional注解，确保数据一致性
*   **JSON处理**: 附件列表使用JSON格式存储，需要处理序列化和反序列化异常
*   **阶段标准化**: STAGE_LABELS定义了标准的6个进度阶段，需要与前端保持一致
*   **用户信息关联**: 自动关联创建人的用户信息，需要确保UserMapper的可用性
*   **订单验证**: 创建活动前验证订单存在性，依赖UnifiedOrderService的正确实现
*   **数据转换**: 实体对象和DTO之间的转换需要处理所有字段映射
*   **异常处理**: 需要区分业务异常和系统异常，提供明确的错误信息
*   **性能优化**: 分页查询和分组查询需要考虑性能，建议在相关字段上建立索引
*   **并发控制**: 多用户同时操作同一订单时，需要考虑并发控制
*   **日志记录**: 重要操作都应该有详细的日志记录，便于问题排查
*   **数据校验**: 输入参数需要进行有效性校验，特别是stageValue的范围
*   **缓存策略**: 频繁查询的进度数据可以考虑使用缓存提高性能
*   **时间处理**: 创建时间和更新时间使用LocalDateTime，需要注意时区问题
*   **附件安全**: 附件URL需要进行安全验证，防止恶意文件访问
*   **业务规则**: 不同阶段的活动创建可能有不同的业务规则和权限要求
*   **监控告警**: 重要操作失败时应该有相应的监控告警机制
