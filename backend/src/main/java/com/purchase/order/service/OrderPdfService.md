# OrderPdfService.md

## 1. 文件概述

`OrderPdfService` 是一个专门用于处理订单PDF文档的服务接口。它的核心职责是生成最终的、具有法律效力的订单PDF文件，并提供验证该文件完整性的功能。此服务通常在订单流程的最终阶段被调用，以生成一个不可变的、包含所有订单详情和签名的官方记录。

- **类型**: 服务接口 (Service Interface)
- **核心职责**: 生成和验证订单的PDF文档。
- **在模块中的位置**: 作为订单模块的一部分，提供PDF处理的专业功能，通常被其他订单服务调用。

## 2. 核心功能

`OrderPdfService` 定义了两个核心功能：

- **生成最终PDF**: 从订单数据生成一个完整的PDF文档，该文档可能包含数字签名，并计算其哈希值以确保不可篡改性，最后上传到云存储（如OSS）。
- **验证PDF完整性**: 验证一个已下载的PDF文件是否与系统中存储的原始版本一致，通过比较其内容的哈希值来实现。

## 3. 接口说明

### 3.1 生成最终PDF文档
- **方法**: `generateFinalPdf(Long orderId) throws Exception`
- **描述**: 为指定的订单ID生成最终的PDF文档。这个过程包括：获取订单数据，渲染成PDF格式，添加签名，计算文件哈希值，然后将文件上传到对象存储服务（OSS）。
- **参数**:
  - `orderId` (Long): 需要生成PDF的订单ID。
- **返回值**: `PdfGenerationResult` - 一个数据传输对象（DTO），包含两个关键信息：
  - `pdfUrl` (String): 上传到OSS后PDF文件的公共访问URL。
  - `documentHash` (String): 生成的PDF文件的哈希值（例如SHA-256），用于后续的完整性验证。
- **异常**: `Exception` - 在PDF生成、计算哈希或上传过程中可能抛出异常。

### 3.2 验证下载的PDF文档完整性
- **方法**: `verifyPdfIntegrity(Long orderId, byte[] pdfContent)`
- **描述**: 这是一个可选的实现功能，用于验证客户端下载的PDF文件是否被篡改。它会重新计算下载文件内容的哈希值，并与生成时存储在数据库中的哈希值进行比较。
- **参数**:
  - `orderId` (Long): 相关订单的ID。
  - `pdfContent` (byte[]): 下载的PDF文件的二进制内容。
- **返回值**: `boolean` - 如果哈希值匹配，返回 `true`，表示文件完整；否则返回 `false`。

## 4. 使用示例

### 示例1: 在订单完成时生成最终PDF
```java
// 在 UnifiedOrderService 的一个方法中调用
@Autowired
private OrderPdfService orderPdfService;
@Autowired
private UnifiedOrderRepository orderRepository;

public void finalizeOrder(Long orderId) {
    try {
        // ... 其他订单完成逻辑 ...

        // 生成并上传PDF
        PdfGenerationResult result = orderPdfService.generateFinalPdf(orderId);

        // 将返回的URL和哈希值保存到订单实体中
        UnifiedOrder order = orderRepository.findById(orderId).orElseThrow();
        order.setFinalPdfUrl(result.getPdfUrl());
        order.setDocumentHash(result.getDocumentHash());
        orderRepository.save(order);

        // ... 通知用户等 ...
    } catch (Exception e) {
        log.error("Failed to generate final PDF for order: {}", orderId, e);
        // 处理异常，可能需要重试或人工介入
    }
}
```

### 示例2: 提供一个API端点来验证下载的PDF
```java
// 在某个Controller中
@Autowired
private OrderPdfService orderPdfService;

@PostMapping("/orders/{orderId}/verify-pdf")
public ResponseEntity<String> verifyOrderPdf(@PathVariable Long orderId, @RequestParam("file") MultipartFile file) {
    try {
        byte[] pdfContent = file.getBytes();
        boolean isIntact = orderPdfService.verifyPdfIntegrity(orderId, pdfContent);
        if (isIntact) {
            return ResponseEntity.ok("PDF verification successful. The document is intact.");
        } else {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("PDF verification failed. The document may have been altered.");
        }
    } catch (IOException e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing file.");
    }
}
```

## 5. 注意事项

1.  **不可变性**: 生成的PDF应被视为订单的最终快照，是不可变的。一旦生成，就不应再修改。
2.  **哈希算法**: 实现时应选择一个强大的哈希算法（如SHA-256）来确保文档的完整性和防篡改能力。
3.  **依赖库**: PDF的生成和处理通常需要依赖第三方库，如 `iText`, `Apache PDFBox` 或 `OpenPDF`。
4.  **存储**: 生成的PDF文件应存储在可靠的对象存储服务（OSS）上，如AWS S3或阿里云OSS，而不是直接存储在应用服务器或数据库中。
5.  **错误处理**: `generateFinalPdf` 方法的实现需要有健壮的错误处理机制，因为它涉及到文件I/O和网络上传，这些都是容易出错的操作。
6.  **事务性**: 整个生成、哈希、上传和更新数据库记录的过程应该是原子性的。如果上传失败，就不应该更新订单记录，可能需要回滚操作或实现补偿事务。
7.  **异步处理**: 如果PDF生成过程非常耗时，应考虑将其设计为异步任务，以避免阻塞主线程，并及时向用户返回响应。
8.  **安全性**: 存储在OSS上的PDF链接应该是安全的。可以考虑使用预签名URL（pre-signed URL）来提供有时效性的、安全的下载链接。
9.  **数据一致性**: 生成PDF时所依赖的订单数据必须是一致和准确的，应从一个确定的订单状态（如“已完成”）来获取数据。
10. **模板化**: PDF的布局和样式应该模板化，便于维护和修改。可以使用HTML模板（如Thymeleaf）渲染成HTML，再将HTML转换为PDF。