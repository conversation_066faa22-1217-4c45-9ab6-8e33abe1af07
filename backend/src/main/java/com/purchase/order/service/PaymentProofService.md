# PaymentProofService.java

## 文件概述 (File Overview)
`PaymentProofService.java` 是支付凭证管理的核心业务服务接口，位于 `com.purchase.order.service` 包中。该接口定义了支付凭证的完整业务规范，统一管理所有订单类型（统一订单、样品订单、结算单）的支付凭证处理流程。通过策略模式实现不同订单类型的差异化处理，提供了凭证上传、审核、确认、拒绝、查询、统计等完整的业务功能。该接口支持多种支付类型（定金、尾款、全款），实现了基于角色的权限控制，并提供了批量处理和业务验证等高级功能。

## 核心功能 (Core Functionality)
*   **统一凭证管理**: 提供统一的支付凭证保存、确认、拒绝方法，支持多种订单类型
*   **多订单类型支持**: 支持统一订单、样品订单、结算单的支付凭证处理
*   **策略模式实现**: 通过策略模式实现不同订单类型的差异化处理逻辑
*   **多维度查询**: 支持按ID、订单类型、处理状态等多种条件查询支付凭证
*   **批量操作处理**: 提供批量确认和批量拒绝支付凭证的高效处理功能
*   **统计分析功能**: 支持统计不同订单类型下待审核和已处理的支付凭证数量
*   **业务规则验证**: 定义完整的业务规则验证和数据完整性检查
*   **权限控制机制**: 基于用户角色的业务权限验证和数据访问控制
*   **支付类型管理**: 支持定金、尾款、全款等多种支付类型的凭证处理
*   **审核流程控制**: 完整的支付凭证审核流程，包括状态管理和时间记录
*   **业务上下文获取**: 提供获取支付凭证关联业务对象的标准接口
*   **异常处理规范**: 定义标准的异常处理和错误信息返回机制

## 接口说明 (Interface Description)

### 统一支付凭证管理方法

#### saveSettlementPaymentProof - 保存结算单支付凭证
*   **方法签名**: `PaymentProof saveSettlementPaymentProof(Long settlementId, String proofUrl, PaymentType paymentType, BigDecimal amount, Long uploadedBy, String buyerRemark)`
*   **功能**: 保存结算单的支付凭证信息
*   **参数**:
    *   `settlementId` (Long) - 结算单ID
    *   `proofUrl` (String) - 支付凭证文件URL
    *   `paymentType` (PaymentType) - 支付类型（定金/尾款/全款）
    *   `amount` (BigDecimal) - 支付金额
    *   `uploadedBy` (Long) - 上传用户ID（买家）
    *   `buyerRemark` (String) - 买家备注信息
*   **返回值**: `PaymentProof` - 保存的支付凭证对象
*   **业务逻辑**:
    *   验证结算单的存在性和状态
    *   检查支付类型和金额的合法性
    *   创建支付凭证记录并设置初始状态
    *   更新结算单的支付状态
    *   记录上传时间和买家信息

#### saveSampleOrderPaymentProof - 保存样品订单支付凭证
*   **方法签名**: `PaymentProof saveSampleOrderPaymentProof(Long sampleOrderId, String proofUrl, Long uploadedBy, String remarks)`
*   **功能**: 保存样品订单的支付凭证信息
*   **参数**:
    *   `sampleOrderId` (Long) - 样品订单ID
    *   `proofUrl` (String) - 支付凭证文件URL
    *   `uploadedBy` (Long) - 上传用户ID（买家）
    *   `remarks` (String) - 备注信息
*   **返回值**: `PaymentProof` - 保存的支付凭证对象
*   **业务逻辑**:
    *   验证样品订单的存在性和状态
    *   检查订单是否允许上传支付凭证
    *   创建支付凭证记录（样品订单只支持全款支付）
    *   更新样品订单的支付状态为待审核
    *   记录买家付款时间和备注信息

#### confirmPaymentProofUnified - 统一确认支付凭证
*   **方法签名**: `PaymentProof confirmPaymentProofUnified(Long proofId, Long adminUserId, String adminRemark)`
*   **功能**: 使用策略模式统一确认不同类型的支付凭证
*   **参数**:
    *   `proofId` (Long) - 支付凭证ID
    *   `adminUserId` (Long) - 管理员用户ID
    *   `adminRemark` (String) - 管理员备注
*   **返回值**: `PaymentProof` - 更新后的支付凭证对象
*   **业务逻辑**:
    *   根据凭证的订单类型选择相应的处理策略
    *   验证凭证状态是否允许确认操作
    *   更新凭证状态为已确认
    *   记录管理员确认信息和时间
    *   触发相应的业务流程（如更新订单状态）

#### rejectPaymentProofUnified - 统一拒绝支付凭证
*   **方法签名**: `PaymentProof rejectPaymentProofUnified(Long proofId, Long adminUserId, String adminRemark)`
*   **功能**: 使用策略模式统一拒绝不同类型的支付凭证
*   **参数**:
    *   `proofId` (Long) - 支付凭证ID
    *   `adminUserId` (Long) - 管理员用户ID
    *   `adminRemark` (String) - 拒绝原因
*   **返回值**: `PaymentProof` - 更新后的支付凭证对象
*   **业务逻辑**:
    *   根据凭证的订单类型选择相应的处理策略
    *   验证凭证状态是否允许拒绝操作
    *   更新凭证状态为已拒绝
    *   记录管理员拒绝信息和时间
    *   触发相应的业务流程（如通知买家重新上传）

### 查询方法

#### getPaymentProofById - 根据ID查询支付凭证
*   **方法签名**: `PaymentProof getPaymentProofById(Long proofId)`
*   **功能**: 根据凭证ID查询支付凭证详细信息
*   **参数**: `proofId` (Long) - 支付凭证ID
*   **返回值**: `PaymentProof` - 支付凭证对象
*   **业务逻辑**:
    *   验证凭证ID的有效性
    *   查询并返回完整的凭证信息
    *   包含关联的订单信息

#### getPaymentProofsBySettlementId - 根据结算单ID查询凭证
*   **方法签名**: `List<PaymentProof> getPaymentProofsBySettlementId(Long settlementId)`
*   **功能**: 查询指定结算单的所有支付凭证
*   **参数**: `settlementId` (Long) - 结算单ID
*   **返回值**: `List<PaymentProof>` - 支付凭证列表
*   **业务逻辑**:
    *   查询结算单关联的所有支付凭证
    *   按上传时间倒序排列
    *   包含凭证的完整状态信息

#### getPendingPaymentProofs - 获取所有待审核凭证
*   **方法签名**: `List<PaymentProof> getPendingPaymentProofs()`
*   **功能**: 查询所有订单类型的待审核支付凭证
*   **返回值**: `List<PaymentProof>` - 待审核支付凭证列表
*   **业务逻辑**:
    *   查询状态为PENDING的所有支付凭证
    *   包含统一订单、样品订单、结算单的凭证
    *   按上传时间排序，便于管理员处理

#### getPendingPaymentProofsByOrderType - 按订单类型查询待审核凭证
*   **方法签名**: `List<PaymentProof> getPendingPaymentProofsByOrderType(String orderType)`
*   **功能**: 根据订单类型查询待审核支付凭证
*   **参数**: `orderType` (String) - 订单类型（UNIFIED/SAMPLE/SETTLEMENT）
*   **返回值**: `List<PaymentProof>` - 待审核支付凭证列表
*   **业务逻辑**:
    *   根据指定的订单类型过滤待审核凭证
    *   支持统一订单、样品订单、结算单的分类查询
    *   按上传时间排序返回结果

### 统计和批量操作方法

#### getPaymentProofStatistics - 获取支付凭证统计信息
*   **方法签名**: `Map<String, Long> getPaymentProofStatistics()`
*   **功能**: 获取各订单类型的支付凭证统计信息
*   **返回值**: `Map<String, Long>` - 统计信息Map，key为订单类型，value为数量
*   **业务逻辑**:
    *   统计各订单类型的支付凭证总数
    *   包含所有状态的凭证统计
    *   用于管理后台的数据展示

#### getPendingPaymentProofStatistics - 获取待审核凭证统计
*   **方法签名**: `Map<String, Long> getPendingPaymentProofStatistics()`
*   **功能**: 获取各订单类型的待审核支付凭证统计
*   **返回值**: `Map<String, Long>` - 统计信息Map，key为订单类型，value为待审核数量
*   **业务逻辑**:
    *   统计各订单类型的待审核凭证数量
    *   用于管理后台的工作量展示
    *   支持实时数据更新

#### batchConfirmPaymentProofs - 批量确认支付凭证
*   **方法签名**: `int batchConfirmPaymentProofs(List<Long> proofIds, Long adminUserId, String adminRemark)`
*   **功能**: 批量确认多个支付凭证
*   **参数**:
    *   `proofIds` (List<Long>) - 凭证ID列表
    *   `adminUserId` (Long) - 管理员用户ID
    *   `adminRemark` (String) - 管理员备注
*   **返回值**: `int` - 成功处理的数量
*   **业务逻辑**:
    *   逐个验证凭证的状态和权限
    *   批量更新凭证状态为已确认
    *   记录管理员操作信息
    *   返回成功处理的凭证数量

#### batchRejectPaymentProofs - 批量拒绝支付凭证
*   **方法签名**: `int batchRejectPaymentProofs(List<Long> proofIds, Long adminUserId, String adminRemark)`
*   **功能**: 批量拒绝多个支付凭证
*   **参数**:
    *   `proofIds` (List<Long>) - 凭证ID列表
    *   `adminUserId` (Long) - 管理员用户ID
    *   `adminRemark` (String) - 拒绝原因
*   **返回值**: `int` - 成功处理的数量
*   **业务逻辑**:
    *   逐个验证凭证的状态和权限
    *   批量更新凭证状态为已拒绝
    *   记录管理员操作信息
    *   返回成功处理的凭证数量

### 业务验证方法

#### validatePaymentProof - 验证支付凭证业务规则
*   **方法签名**: `void validatePaymentProof(PaymentProof paymentProof)`
*   **功能**: 验证支付凭证的业务规则和数据完整性
*   **参数**: `paymentProof` (PaymentProof) - 支付凭证对象
*   **异常**: 验证失败时抛出 `IllegalArgumentException`
*   **业务逻辑**:
    *   验证凭证的基本字段完整性
    *   检查订单类型和支付类型的匹配性
    *   验证金额的合理性
    *   使用策略模式进行类型特定的验证

#### getPaymentProofBusinessContext - 获取业务上下文
*   **方法签名**: `Object getPaymentProofBusinessContext(PaymentProof paymentProof)`
*   **功能**: 获取支付凭证关联的业务对象
*   **参数**: `paymentProof` (PaymentProof) - 支付凭证对象
*   **返回值**: `Object` - 业务上下文对象（订单、样品订单或结算单）
*   **业务逻辑**:
    *   根据凭证的订单类型获取对应的业务对象
    *   使用策略模式处理不同类型的业务上下文
    *   返回完整的业务对象信息

#### getPaymentProofBusinessContextWithPermission - 获取业务上下文（带权限验证）
*   **方法签名**: `Object getPaymentProofBusinessContextWithPermission(PaymentProof paymentProof, Long currentUserId, String currentUserRole)`
*   **功能**: 获取支付凭证关联的业务对象并验证用户权限
*   **参数**:
    *   `paymentProof` (PaymentProof) - 支付凭证对象
    *   `currentUserId` (Long) - 当前用户ID
    *   `currentUserRole` (String) - 当前用户角色
*   **返回值**: `Object` - 业务上下文对象
*   **异常**: 权限验证失败时抛出 `BusinessException`
*   **业务逻辑**:
    *   验证用户是否有权限访问该凭证
    *   根据用户角色进行权限检查
    *   获取并返回业务上下文对象
    *   确保数据安全和访问控制

## 业务规则 (Business Rules)
*   **订单状态验证**: 只有特定状态的订单才能上传支付凭证，如样品订单需要已设置运费状态
*   **支付类型限制**: 不同订单类型支持的支付类型不同，样品订单只支持全款支付
*   **凭证状态管理**: 支付凭证状态包括待审核、已确认、已拒绝，状态转换需要严格控制
*   **权限分级控制**: 只有管理员可以确认或拒绝支付凭证，买家只能上传凭证
*   **金额验证规则**: 支付金额必须为正数，且不能超过订单总金额
*   **文件格式限制**: 支付凭证文件必须为图片格式，且大小在合理范围内
*   **重复上传控制**: 同一订单的同一支付类型不能重复上传未处理的凭证
*   **审核时效要求**: 支付凭证需要在规定时间内完成审核，超时需要特殊处理

## 使用示例 (Usage Examples)

```java
// 1. 服务实现类示例
@Service
@Transactional
public class PaymentProofServiceImpl extends ServiceImpl<PaymentProofMapper, PaymentProof>
    implements PaymentProofService {

    @Autowired
    private PaymentProofAuditStrategyFactory strategyFactory;

    @Autowired
    private SampleOrderMapper sampleOrderMapper;

    @Autowired
    private SettlementMapper settlementMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentProof saveSettlementPaymentProof(Long settlementId, String proofUrl,
            PaymentType paymentType, BigDecimal amount, Long uploadedBy, String buyerRemark) {

        log.info("保存结算单支付凭证 - settlementId: {}, paymentType: {}, amount: {}",
            settlementId, paymentType, amount);

        // 验证结算单存在性
        Settlement settlement = settlementMapper.selectById(settlementId);
        if (settlement == null) {
            throw new BusinessException("结算单不存在");
        }

        // 验证支付金额
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("支付金额必须大于0");
        }

        // 创建支付凭证
        PaymentProof proof = new PaymentProof();
        proof.setOrderType(PaymentProof.OrderType.SETTLEMENT);
        proof.setSettlementId(settlementId);
        proof.setProofUrl(proofUrl);
        proof.setPaymentType(paymentType);
        proof.setAmount(amount);
        proof.setUploadedBy(uploadedBy);
        proof.setUploadedAt(LocalDateTime.now());
        proof.setBuyerPaymentTime(LocalDateTime.now());
        proof.setBuyerRemark(buyerRemark);
        proof.setStatus(PaymentProof.ProofStatus.PENDING);

        // 验证业务规则
        validatePaymentProof(proof);

        // 保存凭证
        baseMapper.insert(proof);

        log.info("结算单支付凭证保存成功 - proofId: {}", proof.getId());
        return proof;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentProof confirmPaymentProofUnified(Long proofId, Long adminUserId, String adminRemark) {
        log.info("确认支付凭证 - proofId: {}, adminUserId: {}", proofId, adminUserId);

        // 查询支付凭证
        PaymentProof proof = baseMapper.selectById(proofId);
        if (proof == null) {
            throw new BusinessException("支付凭证不存在");
        }

        // 验证凭证状态
        if (!PaymentProof.ProofStatus.PENDING.equals(proof.getStatus())) {
            throw new BusinessException("只能确认待审核状态的支付凭证");
        }

        // 使用策略模式处理不同订单类型
        PaymentProofAuditStrategy strategy = strategyFactory.getStrategy(proof.getOrderType());
        PaymentProof confirmedProof = strategy.confirmPaymentProof(proof, adminUserId, adminRemark);

        log.info("支付凭证确认成功 - proofId: {}", proofId);
        return confirmedProof;
    }

    @Override
    public List<PaymentProof> getPendingPaymentProofsByOrderType(String orderType) {
        QueryWrapper<PaymentProof> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_type", orderType)
                   .eq("status", PaymentProof.ProofStatus.PENDING)
                   .orderByDesc("uploaded_at");

        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public Map<String, Long> getPendingPaymentProofStatistics() {
        Map<String, Long> statistics = new HashMap<>();

        // 统计各订单类型的待审核凭证数量
        statistics.put("UNIFIED", countPendingByOrderType("UNIFIED"));
        statistics.put("SAMPLE", countPendingByOrderType("SAMPLE"));
        statistics.put("SETTLEMENT", countPendingByOrderType("SETTLEMENT"));

        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchConfirmPaymentProofs(List<Long> proofIds, Long adminUserId, String adminRemark) {
        if (proofIds == null || proofIds.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (Long proofId : proofIds) {
            try {
                confirmPaymentProofUnified(proofId, adminUserId, adminRemark);
                successCount++;
            } catch (Exception e) {
                log.error("批量确认支付凭证失败 - proofId: {}", proofId, e);
            }
        }

        log.info("批量确认支付凭证完成 - 总数: {}, 成功: {}", proofIds.size(), successCount);
        return successCount;
    }
}

// 2. Java客户端调用示例
@Service
public class PaymentProofClientService {

    @Autowired
    private PaymentProofService paymentProofService;

    @Autowired
    private NotificationService notificationService;

    // 买家上传支付凭证
    public PaymentProof uploadPaymentProof(Long orderId, String orderType,
            String proofUrl, PaymentType paymentType, BigDecimal amount,
            Long buyerId, String remark) {

        try {
            PaymentProof proof = null;

            switch (orderType) {
                case "SETTLEMENT":
                    proof = paymentProofService.saveSettlementPaymentProof(
                        orderId, proofUrl, paymentType, amount, buyerId, remark);
                    break;
                case "SAMPLE":
                    proof = paymentProofService.saveSampleOrderPaymentProof(
                        orderId, proofUrl, buyerId, remark);
                    break;
                default:
                    throw new BusinessException("不支持的订单类型: " + orderType);
            }

            // 发送通知给管理员
            notificationService.notifyAdminNewPaymentProof(proof);

            return proof;

        } catch (Exception e) {
            log.error("上传支付凭证失败 - orderId: {}, orderType: {}", orderId, orderType, e);
            throw new SystemException("上传支付凭证失败", e);
        }
    }

    // 管理员审核支付凭证
    public PaymentProof auditPaymentProof(Long proofId, boolean approved,
            Long adminUserId, String remark) {

        try {
            PaymentProof result;

            if (approved) {
                result = paymentProofService.confirmPaymentProofUnified(proofId, adminUserId, remark);
                log.info("支付凭证审核通过 - proofId: {}", proofId);
            } else {
                result = paymentProofService.rejectPaymentProofUnified(proofId, adminUserId, remark);
                log.info("支付凭证审核拒绝 - proofId: {}", proofId);
            }

            // 发送通知给买家
            notificationService.notifyBuyerPaymentProofResult(result);

            return result;

        } catch (Exception e) {
            log.error("审核支付凭证失败 - proofId: {}", proofId, e);
            throw new SystemException("审核支付凭证失败", e);
        }
    }
}

// 3. 业务服务集成示例
@Service
public class PaymentProofWorkflowService {

    @Autowired
    private PaymentProofService paymentProofService;

    // 订单支付流程处理
    @EventListener
    @Async
    public void handleOrderPaymentProofUploaded(PaymentProofUploadedEvent event) {
        try {
            PaymentProof proof = event.getPaymentProof();

            // 自动验证支付凭证
            paymentProofService.validatePaymentProof(proof);

            // 获取业务上下文
            Object businessContext = paymentProofService.getPaymentProofBusinessContext(proof);

            // 根据订单类型执行不同的业务逻辑
            if (businessContext instanceof Settlement) {
                handleSettlementPaymentProof(proof, (Settlement) businessContext);
            } else if (businessContext instanceof SampleOrder) {
                handleSampleOrderPaymentProof(proof, (SampleOrder) businessContext);
            }

        } catch (Exception e) {
            log.error("处理支付凭证上传事件失败: proofId={}", event.getPaymentProof().getId(), e);
        }
    }
}

// 4. 定时任务示例
@Component
public class PaymentProofScheduledTasks {

    @Autowired
    private PaymentProofService paymentProofService;

    // 清理过期的支付凭证
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点
    public void cleanupExpiredPaymentProofs() {
        log.info("开始清理过期支付凭证");

        try {
            // 获取超过30天未处理的凭证
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30);
            List<PaymentProof> expiredProofs = paymentProofService.getExpiredPendingProofs(cutoffDate);

            for (PaymentProof proof : expiredProofs) {
                // 自动拒绝过期凭证
                paymentProofService.rejectPaymentProofUnified(
                    proof.getId(), 0L, "凭证已过期，请重新上传");
            }

            log.info("过期支付凭证清理完成，处理数量: {}", expiredProofs.size());

        } catch (Exception e) {
            log.error("清理过期支付凭证失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
class PaymentProofServiceTest {

    @Autowired
    private PaymentProofService paymentProofService;

    @Test
    void testSaveSettlementPaymentProof() {
        // 准备测试数据
        Long settlementId = 123L;
        String proofUrl = "http://example.com/proof.jpg";
        PaymentType paymentType = PaymentType.DEPOSIT;
        BigDecimal amount = new BigDecimal("1000.00");
        Long uploadedBy = 456L;
        String buyerRemark = "测试支付凭证";

        // 执行测试
        PaymentProof result = paymentProofService.saveSettlementPaymentProof(
            settlementId, proofUrl, paymentType, amount, uploadedBy, buyerRemark);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getSettlementId()).isEqualTo(settlementId);
        assertThat(result.getProofUrl()).isEqualTo(proofUrl);
        assertThat(result.getPaymentType()).isEqualTo(paymentType);
        assertThat(result.getAmount()).isEqualTo(amount);
        assertThat(result.getStatus()).isEqualTo(PaymentProof.ProofStatus.PENDING);
    }

    @Test
    void testConfirmPaymentProofUnified() {
        // 准备测试数据
        Long proofId = 789L;
        Long adminUserId = 1L;
        String adminRemark = "支付已核实";

        // 执行测试
        PaymentProof result = paymentProofService.confirmPaymentProofUnified(
            proofId, adminUserId, adminRemark);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(PaymentProof.ProofStatus.CONFIRMED);
        assertThat(result.getAdminUserId()).isEqualTo(adminUserId);
        assertThat(result.getAdminRemark()).isEqualTo(adminRemark);
    }
}
```

## 注意事项 (Notes)
*   **统一管理**: 接口的核心思想是统一管理，实现类需要正确处理不同订单类型的逻辑差异
*   **策略模式**: confirmPaymentProofUnified和rejectPaymentProofUnified应采用策略模式，为每种订单类型定义具体的处理策略
*   **事务一致性**: 所有写操作（保存、确认、拒绝）都必须是事务性的，确保数据的一致性
*   **权限验证**: getPaymentProofBusinessContextWithPermission方法强调权限的重要性，必须验证用户身份和角色
*   **参数校验**: 实现类需要对所有传入参数进行严格校验，如ID不能为空，金额必须为正数
*   **枚举使用**: PaymentType使用枚举，增强了代码的类型安全和可读性
*   **异常处理**: 接口方法在验证失败或权限不足时会抛出特定异常，调用方需要妥善处理
*   **命名清晰**: 接口方法的命名清晰地反映了其功能，如getPending...vs getProcessed...
*   **业务上下文**: getPaymentProofBusinessContext方法提供了获取凭证关联业务对象的方式
*   **可扩展性**: 接口设计良好，如果未来新增订单类型，可以通过增加新的策略实现来轻松扩展
*   **批量操作性能**: 批量操作方法的实现需要考虑性能，避免在循环中进行数据库查询
*   **空值处理**: 实现类需要妥善处理查询结果为空的情况，返回空列表而不是null
*   **日志记录**: 关键操作（如确认、拒绝）的实现应包含详细的日志记录，便于审计和问题排查
*   **接口隔离**: 接口遵循了接口隔离原则，将查询、操作、统计等功能清晰地分开定义
*   **并发控制**: 在高并发场景下，需要考虑支付凭证状态更新的并发控制，避免数据不一致
*   **状态管理**: 支付凭证的状态变更需要严格控制，确保状态转换的合法性和一致性
*   **文档完整性**: 接口的JavaDoc注释非常完整，为实现和调用提供了清晰的指导