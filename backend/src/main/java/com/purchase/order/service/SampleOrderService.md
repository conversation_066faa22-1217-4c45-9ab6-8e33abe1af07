# SampleOrderService 样品订单服务接口文档

## 文件概述

`SampleOrderService` 是样品订单服务接口，定义了样品订单管理的核心业务方法。该接口涵盖了样品订单的完整生命周期管理，包括订单创建、状态管理、支付处理、发货跟踪、权限控制等功能，为买家、卖家和管理员提供不同的服务接口。

## 核心功能

### 主要职责
- **订单创建**: 支持基于需求和竞价创建样品订单
- **状态管理**: 管理订单和支付状态的流转
- **权限控制**: 为不同角色提供相应的访问权限
- **流程管理**: 管理从创建到完成的完整业务流程
- **查询服务**: 提供多维度的订单查询功能

### 服务特点
- 多角色支持（买家、管理员）
- 完整的订单生命周期管理
- 灵活的查询和分页功能
- 严格的权限验证机制
- 丰富的业务操作接口

## 接口说明

### 订单创建接口

#### createSampleOrder
```java
SampleOrderResponse createSampleOrder(SampleOrderCreateRequest request, Long buyerId)
```
- **功能**: 创建样品订单（系统自动创建）
- **参数**:
  - `request`: 创建请求对象
  - `buyerId`: 买家用户ID
- **返回值**: 样品订单响应对象
- **权限**: 买家权限
- **业务逻辑**: 基于请求参数创建新的样品订单

#### createSampleOrderByBiddings
```java
SampleOrderResponse createSampleOrderByBiddings(Long requirementId, List<Long> acceptedBiddingIds, Long buyerId)
```
- **功能**: 根据需求ID和已接受的竞价ID列表创建样品订单
- **参数**:
  - `requirementId`: 样品需求ID
  - `acceptedBiddingIds`: 已接受的竞价ID列表
  - `buyerId`: 买家用户ID
- **返回值**: 样品订单响应对象
- **业务逻辑**: 基于竞价结果自动创建订单和订单明细

### 管理员操作接口

#### getAdminSampleOrders
```java
IPage<SampleOrderResponse> getAdminSampleOrders(Integer page, Integer size, String status, Long buyerId, String orderNumber)
```
- **功能**: 管理员获取待处理样品订单列表
- **参数**:
  - `page`: 页码
  - `size`: 每页大小
  - `status`: 订单状态（可选）
  - `buyerId`: 买家ID（可选）
  - `orderNumber`: 订单编号（可选）
- **返回值**: 分页的样品订单列表
- **权限**: 管理员权限

#### adminProcessSampleOrder
```java
SampleOrderResponse adminProcessSampleOrder(Long orderId, SampleOrderAdminProcessRequest request, Long adminId)
```
- **功能**: 管理员处理样品订单（编辑运费等）
- **参数**:
  - `orderId`: 订单ID
  - `request`: 处理请求对象
  - `adminId`: 管理员ID
- **返回值**: 处理后的订单响应
- **业务逻辑**: 设置运费、邮寄方式等信息

#### shipSampleOrder
```java
SampleOrderResponse shipSampleOrder(Long orderId, SampleOrderShipRequest request, Long adminId)
```
- **功能**: 管理员发货
- **参数**:
  - `orderId`: 订单ID
  - `request`: 发货请求对象
  - `adminId`: 管理员ID
- **返回值**: 发货后的订单响应
- **业务逻辑**: 设置快递单号、发货时间等信息

### 买家操作接口

#### getBuyerSampleOrders
```java
IPage<SampleOrderResponse> getBuyerSampleOrders(Long buyerId, Integer page, Integer size, String status)
```
- **功能**: 买家获取自己的样品订单列表
- **参数**:
  - `buyerId`: 买家ID
  - `page`: 页码
  - `size`: 每页大小
  - `status`: 订单状态（可选）
- **返回值**: 分页的样品订单列表
- **权限**: 买家只能查看自己的订单

#### updateShippingAddress
```java
SampleOrderResponse updateShippingAddress(Long orderId, Long buyerId, String buyerAddress)
```
- **功能**: 买家更新收货地址
- **参数**:
  - `orderId`: 订单ID
  - `buyerId`: 买家ID（权限验证）
  - `buyerAddress`: 收货地址
- **返回值**: 更新后的订单响应
- **业务逻辑**: 只能在特定状态下更新地址

#### confirmReceipt
```java
SampleOrderResponse confirmReceipt(Long orderId, Long buyerId)
```
- **功能**: 确认收货（买家操作）
- **参数**:
  - `orderId`: 订单ID
  - `buyerId`: 买家ID
- **返回值**: 确认后的订单响应
- **业务逻辑**: 将订单状态更新为已完成

### 查询接口

#### getSampleOrderDetail
```java
SampleOrderResponse getSampleOrderDetail(Long orderId, Long buyerId)
```
- **功能**: 买家查看样品订单详情
- **参数**:
  - `orderId`: 订单ID
  - `buyerId`: 买家ID（权限验证）
- **返回值**: 订单详情响应
- **权限**: 买家只能查看自己的订单

#### getSampleOrderFullDetail
```java
SampleOrderDetailVO getSampleOrderFullDetail(Long orderId)
```
- **功能**: 获取样品订单完整详情（包含买家、卖家、需求信息）
- **参数**: `orderId`: 订单ID
- **返回值**: 样品订单完整详情VO
- **权限**: 管理员使用，无权限限制

#### getSampleOrderByRequirementId
```java
SampleOrderResponse getSampleOrderByRequirementId(Long requirementId)
```
- **功能**: 根据需求ID查询样品订单
- **参数**: `requirementId`: 需求ID
- **返回值**: 样品订单响应，如果不存在返回null
- **用途**: 检查需求是否已有对应订单

### 状态管理接口

#### updatePaymentStatus
```java
void updatePaymentStatus(Long sampleOrderId, String paymentStatus)
```
- **功能**: 更新样品订单支付状态
- **参数**:
  - `sampleOrderId`: 样品订单ID
  - `paymentStatus`: 支付状态
- **用途**: 支付流程中的状态更新

#### updateOrderStatus
```java
void updateOrderStatus(Long sampleOrderId, String orderStatus)
```
- **功能**: 更新样品订单状态
- **参数**:
  - `sampleOrderId`: 样品订单ID
  - `orderStatus`: 订单状态
- **用途**: 订单流程中的状态更新

### 业务验证接口

#### canUploadPaymentProof
```java
boolean canUploadPaymentProof(Long sampleOrderId, Long buyerId)
```
- **功能**: 检查样品订单是否可以上传支付凭证
- **参数**:
  - `sampleOrderId`: 样品订单ID
  - `buyerId`: 买家ID（权限验证）
- **返回值**: 是否可以上传支付凭证
- **业务逻辑**: 检查订单状态和权限

### 取消操作接口

#### cancelSampleOrder
```java
SampleOrderResponse cancelSampleOrder(Long orderId, Long userId, String userRole, String reason)
```
- **功能**: 取消样品订单（通用版本）
- **参数**:
  - `orderId`: 订单ID
  - `userId`: 操作用户ID
  - `userRole`: 用户角色
  - `reason`: 取消原因
- **返回值**: 取消后的订单响应

## 使用示例

### 创建样品订单
```java
@Service
public class SampleOrderBusinessService {
    
    @Autowired
    private SampleOrderService sampleOrderService;
    
    public SampleOrderResponse createOrderFromBiddings(Long requirementId, 
                                                      List<Long> acceptedBiddingIds, 
                                                      Long buyerId) {
        // 验证买家权限
        validateBuyerPermission(buyerId, requirementId);
        
        // 创建样品订单
        SampleOrderResponse order = sampleOrderService.createSampleOrderByBiddings(
            requirementId, acceptedBiddingIds, buyerId
        );
        
        // 发送创建通知
        notificationService.notifyOrderCreated(order);
        
        return order;
    }
}
```

### 管理员处理订单
```java
@RestController
@RequestMapping("/api/admin/sample-orders")
public class AdminSampleOrderController {
    
    @Autowired
    private SampleOrderService sampleOrderService;
    
    @PostMapping("/{orderId}/process")
    public ApiResponse<SampleOrderResponse> processOrder(
            @PathVariable Long orderId,
            @RequestBody SampleOrderAdminProcessRequest request,
            @RequestHeader("Admin-Id") Long adminId) {
        
        SampleOrderResponse response = sampleOrderService.adminProcessSampleOrder(
            orderId, request, adminId
        );
        
        return ApiResponse.success(response);
    }
    
    @PostMapping("/{orderId}/ship")
    public ApiResponse<SampleOrderResponse> shipOrder(
            @PathVariable Long orderId,
            @RequestBody SampleOrderShipRequest request,
            @RequestHeader("Admin-Id") Long adminId) {
        
        SampleOrderResponse response = sampleOrderService.shipSampleOrder(
            orderId, request, adminId
        );
        
        return ApiResponse.success(response);
    }
}
```

### 买家操作订单
```java
@RestController
@RequestMapping("/api/buyer/sample-orders")
public class BuyerSampleOrderController {
    
    @Autowired
    private SampleOrderService sampleOrderService;
    
    @GetMapping
    public ApiResponse<IPage<SampleOrderResponse>> getMyOrders(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String status,
            @RequestHeader("User-Id") Long buyerId) {
        
        IPage<SampleOrderResponse> orders = sampleOrderService.getBuyerSampleOrders(
            buyerId, page, size, status
        );
        
        return ApiResponse.success(orders);
    }
    
    @PutMapping("/{orderId}/address")
    public ApiResponse<SampleOrderResponse> updateAddress(
            @PathVariable Long orderId,
            @RequestBody UpdateAddressRequest request,
            @RequestHeader("User-Id") Long buyerId) {
        
        SampleOrderResponse response = sampleOrderService.updateShippingAddress(
            orderId, buyerId, request.getAddress()
        );
        
        return ApiResponse.success(response);
    }
    
    @PostMapping("/{orderId}/confirm-receipt")
    public ApiResponse<SampleOrderResponse> confirmReceipt(
            @PathVariable Long orderId,
            @RequestHeader("User-Id") Long buyerId) {
        
        SampleOrderResponse response = sampleOrderService.confirmReceipt(orderId, buyerId);
        
        return ApiResponse.success(response);
    }
}
```

## 注意事项

### 权限控制
1. **买家权限**: 买家只能操作自己的订单
2. **管理员权限**: 管理员可以操作所有订单
3. **状态验证**: 每个操作都需要验证订单状态
4. **角色验证**: 严格验证用户角色和权限

### 业务规则
1. **状态流转**: 订单状态必须按照规定流程变更
2. **数据一致性**: 确保订单数据的完整性和一致性
3. **并发控制**: 防止并发操作导致的数据不一致
4. **事务管理**: 关键操作使用事务保护

### 异常处理
1. **参数验证**: 严格验证所有输入参数
2. **业务异常**: 抛出明确的业务异常信息
3. **系统异常**: 妥善处理系统级异常
4. **回滚机制**: 失败操作的数据回滚

### 性能考虑
1. **分页查询**: 所有列表查询都支持分页
2. **索引优化**: 确保查询字段有合适的索引
3. **缓存策略**: 频繁查询的数据考虑缓存
4. **批量操作**: 大量数据操作使用批量处理

### 扩展性
1. **接口设计**: 接口设计考虑未来扩展需求
2. **参数灵活性**: 查询接口支持多种过滤条件
3. **状态扩展**: 支持新增订单状态
4. **角色扩展**: 支持新增用户角色
