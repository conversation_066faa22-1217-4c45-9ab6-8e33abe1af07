# UnifiedOrderService 统一订单服务接口文档

## 文件概述

`UnifiedOrderService` 是统一订单服务的核心接口，继承自MyBatis-Plus的`IService<UnifiedOrder>`，提供了完整的订单生命周期管理功能。该接口涵盖了订单创建、更新、签署、支付、进度跟踪等所有业务操作。

## 核心功能

### 主要业务领域
- **订单管理**: 创建、更新、查询订单
- **订单项目管理**: 管理订单中的具体商品项目
- **签署流程**: 买卖双方电子签名
- **支付管理**: 支付状态和时间跟踪
- **进度跟踪**: 订单执行进度管理
- **权限验证**: 用户权限和关联关系验证
- **统计分析**: 订单状态统计和数据分析

### 服务特点
- 完整的订单生命周期管理
- 支持复杂的业务流程
- 严格的权限控制
- 灵活的查询和统计功能

## 接口说明

### 订单基础操作

#### 创建订单
```java
UnifiedOrder createOrder(CreateOrderRequest request);
```
- **功能**: 创建新的统一订单
- **参数**: `CreateOrderRequest` - 创建订单请求
- **返回**: 创建的订单实体
- **业务**: 初始化订单基础信息，设置为草稿状态

#### 获取订单详情
```java
UnifiedOrder getOrderById(Long orderId);
```
- **功能**: 根据ID获取订单完整信息
- **参数**: `orderId` - 订单ID
- **返回**: 订单实体（包含订单项目）
- **权限**: 需要验证用户访问权限

#### 更新订单
```java
UnifiedOrder updateOrder(Long orderId, UpdateOrderRequest request);
```
- **功能**: 更新订单基础信息
- **参数**: 
  - `orderId` - 订单ID
  - `request` - 更新请求
- **返回**: 更新后的订单
- **限制**: 只能更新草稿状态的订单

### 订单项目管理

#### 获取订单项目列表
```java
List<OrderItem> getOrderItems(Long orderId);
```
- **功能**: 获取订单的所有项目
- **参数**: `orderId` - 订单ID
- **返回**: 订单项目列表
- **排序**: 按sortOrder排序

#### 添加订单项目
```java
OrderItem addOrderItem(Long orderId, OrderItemRequest request);
```
- **功能**: 向订单添加新的项目
- **参数**: 
  - `orderId` - 订单ID
  - `request` - 项目请求
- **返回**: 添加的订单项目
- **业务**: 自动计算价格和更新订单总价

#### 更新订单项目
```java
OrderItem updateOrderItem(Long orderId, Long itemId, OrderItemRequest request);
```
- **功能**: 更新订单中的特定项目
- **参数**: 
  - `orderId` - 订单ID
  - `itemId` - 项目ID
  - `request` - 更新请求
- **返回**: 更新后的项目

#### 删除订单项目
```java
void deleteOrderItem(Long orderId, Long itemId);
```
- **功能**: 删除订单中的项目
- **参数**: 
  - `orderId` - 订单ID
  - `itemId` - 项目ID
- **业务**: 删除后重新计算订单总价

### 签署流程

#### 基础签署
```java
UnifiedOrder signOrder(Long orderId, Long userId, String role);
```
- **功能**: 用户签署订单
- **参数**: 
  - `orderId` - 订单ID
  - `userId` - 用户ID
  - `role` - 角色（"buyer"或"seller"）
- **返回**: 签署后的订单

#### 电子签名签署
```java
UnifiedOrder signOrderWithSignature(Long orderId, Long userId, String role, String signatureUrl);
```
- **功能**: 带电子签名的订单签署
- **参数**: 
  - `orderId` - 订单ID
  - `userId` - 用户ID
  - `role` - 角色
  - `signatureUrl` - 电子签名图片URL
- **返回**: 签署后的订单
- **业务**: 双方签署完成后订单状态变为处理中

### 状态和进度管理

#### 更新订单状态
```java
UnifiedOrder updateOrderStatus(Long orderId, String status);
```
- **功能**: 更新订单状态
- **参数**: 
  - `orderId` - 订单ID
  - `status` - 新状态
- **返回**: 更新后的订单
- **状态**: draft, processing, completed, cancelled

#### 更新订单进度
```java
UnifiedOrder updateOrderProgress(Long orderId, UpdateOrderProgressRequest request);
```
- **功能**: 更新订单执行进度
- **参数**: 
  - `orderId` - 订单ID
  - `request` - 进度更新请求
- **返回**: 更新后的订单
- **内容**: 进度信息和百分比

### 支付管理

#### 更新支付状态
```java
void updateOrderPaymentStatus(Long orderId, PaymentType paymentType, LocalDateTime paymentTime);
```
- **功能**: 更新订单支付状态
- **参数**: 
  - `orderId` - 订单ID
  - `paymentType` - 支付类型（定金/尾款）
  - `paymentTime` - 支付时间
- **业务**: 根据支付类型更新相应的支付状态

#### 确认收货
```java
UnifiedOrder confirmReceipt(Long orderId, Long buyerId, String remark);
```
- **功能**: 买家确认收货
- **参数**: 
  - `orderId` - 订单ID
  - `buyerId` - 买家ID
  - `remark` - 确认备注
- **返回**: 更新后的订单
- **业务**: 确认收货后订单状态变为完成

### 查询和统计

#### 分页查询订单
```java
IPage<UnifiedOrder> getOrderPage(Integer page, Integer size, Long userId, String role, String status);
```
- **功能**: 分页查询订单列表
- **参数**: 
  - `page` - 页码
  - `size` - 页大小
  - `userId` - 用户ID（可选）
  - `role` - 角色（可选）
  - `status` - 状态（可选）
- **返回**: 订单分页结果

#### 获取简要订单列表
```java
IPage<OrderSimpleResponse> getSimpleOrderPage(Integer page, Integer size, Long userId, String role);
```
- **功能**: 获取简化的订单列表
- **返回**: 只包含关键字段的订单列表
- **用途**: 列表页面显示，提高查询性能

#### 订单状态统计
```java
Map<String, Integer> getOrderStatusStats(Long userId, String role);
```
- **功能**: 获取用户订单状态统计
- **参数**: 
  - `userId` - 用户ID
  - `role` - 角色
- **返回**: 各状态订单数量统计

### 权限验证

#### 检查用户关联关系
```java
boolean isUserRelatedToOrder(Long orderId, Long userId, List<String> roles);
```
- **功能**: 检查用户是否与订单有关联
- **参数**: 
  - `orderId` - 订单ID
  - `userId` - 用户ID
  - `roles` - 角色列表
- **返回**: 是否有关联关系

#### 买家/卖家验证
```java
boolean isBuyerOfOrder(Long userId, Long orderId);
boolean isSellerOfOrder(Long userId, Long orderId);
```
- **功能**: 验证用户是否为订单的买家或卖家
- **用途**: 权限控制和业务逻辑验证

### 计算和工具方法

#### 重新计算订单总价
```java
UnifiedOrder recalculateOrderTotal(Long orderId);
```
- **功能**: 重新计算订单总价
- **业务**: 基于所有订单项目重新计算总价、总重量、总体积

#### 计算物理属性
```java
BigDecimal calculateOrderTotalWeight(Long orderId);
BigDecimal calculateOrderTotalVolume(Long orderId);
```
- **功能**: 计算订单总重量和总体积
- **返回**: 基于所有订单项目计算的总值

## 使用示例

### 创建完整订单流程
```java
@Service
public class OrderBusinessService {
    
    @Autowired
    private UnifiedOrderService orderService;
    
    public UnifiedOrder createCompleteOrder(CreateOrderRequest request) {
        // 1. 创建订单
        UnifiedOrder order = orderService.createOrder(request);
        
        // 2. 添加订单项目
        for (OrderItemRequest itemRequest : request.getItems()) {
            orderService.addOrderItem(order.getId(), itemRequest);
        }
        
        // 3. 重新计算总价
        order = orderService.recalculateOrderTotal(order.getId());
        
        return order;
    }
}
```

### 订单签署流程
```java
public void processOrderSigning(Long orderId, Long userId, String role, String signatureUrl) {
    // 验证权限
    if ("buyer".equals(role)) {
        if (!orderService.isBuyerOfOrder(userId, orderId)) {
            throw new BusinessException("无权限签署此订单");
        }
    } else if ("seller".equals(role)) {
        if (!orderService.isSellerOfOrder(userId, orderId)) {
            throw new BusinessException("无权限签署此订单");
        }
    }
    
    // 执行签署
    UnifiedOrder order = orderService.signOrderWithSignature(orderId, userId, role, signatureUrl);
    
    // 检查是否双方都已签署
    if (order.getBuyerSignTime() != null && order.getSellerSignTime() != null) {
        // 双方签署完成，更新状态为处理中
        orderService.updateOrderStatus(orderId, "processing");
    }
}
```

## 注意事项

### 业务规则
1. **状态流转**: 订单状态必须按照规定流程变更
2. **权限控制**: 所有操作都需要验证用户权限
3. **数据一致性**: 订单项目变更后需要重新计算总价
4. **签署顺序**: 双方签署完成后才能进入处理状态

### 事务管理
1. **原子操作**: 关键业务操作需要事务保护
2. **数据一致性**: 确保订单和订单项目数据一致
3. **并发控制**: 防止并发修改导致的数据问题
4. **回滚机制**: 操作失败时能够正确回滚

### 性能优化
1. **分页查询**: 所有列表查询都支持分页
2. **索引优化**: 为查询字段建立合适索引
3. **缓存策略**: 频繁查询的数据可以缓存
4. **批量操作**: 大量数据操作使用批量处理

### 安全考虑
1. **权限验证**: 每个操作都进行权限检查
2. **数据验证**: 严格验证输入数据
3. **操作日志**: 记录重要操作
4. **敏感信息**: 保护用户敏感信息
