# UnifiedOrder 统一订单实体文档

## 文件概述

`UnifiedOrder` 是统一订单实体类，合并了原合同和订单概念，是订单管理系统的核心数据模型。该实体包含了订单的完整生命周期信息，从创建、签署、支付到交付的全过程管理。

## 核心功能

### 主要职责
- **订单管理**: 统一管理订单和合同信息
- **双方信息**: 完整记录买家和卖家信息
- **支付管理**: 支持定金和尾款的分期支付
- **物流信息**: 详细的发货和收货信息
- **进度跟踪**: 订单执行进度管理
- **电子签名**: 支持买卖双方电子签名

### 业务特点
- 合并合同和订单概念，简化业务流程
- 支持复杂的支付状态管理
- 完整的物流和港口信息
- 灵活的进度跟踪机制

## 接口说明

### 常量定义

#### 订单状态 (OrderStatus)
- `DRAFT`: 草稿状态，订单创建但未完成
- `PROCESSING`: 处理中，订单正在执行
- `COMPLETED`: 已完成，订单执行完毕
- `CANCELLED`: 已取消，订单被取消

#### 支付状态 (PaymentStatus)
- `UNPAID`: 未支付，初始状态
- `DEPOSIT_PAID`: 定金已支付
- `DEPOSIT_REFUNDED`: 定金已退款
- `FINAL_PAID`: 尾款已支付，支付完成
- `FINAL_REFUNDED`: 尾款已退款

### 核心字段分类

#### 基础信息
- `id`: 订单主键ID
- `orderNumber`: 订单编号（业务编号）
- `roomId`: 关联的聊天室ID
- `orderStatus`: 订单状态
- `totalPrice`: 订单总价
- `totalWeight`: 订单总重量
- `totalVolume`: 订单总体积

#### 买家信息
- `buyerId`: 买家用户ID
- `buyerCompany`: 买家公司名称
- `buyerContact`: 买家联系人
- `buyerPhone`: 买家联系电话
- `buyerEmail`: 买家邮箱
- `buyerAddress`: 买家地址
- `buyerSignTime`: 买家签署时间
- `buyerSignatureUrl`: 买家电子签名图片URL

#### 卖家信息
- `sellerId`: 卖家用户ID
- `sellerCompany`: 卖家公司名称
- `sellerContact`: 卖家联系人
- `sellerPhone`: 卖家联系电话
- `sellerEmail`: 卖家邮箱
- `sellerAddress`: 卖家地址
- `sellerSignTime`: 卖家签署时间
- `sellerSignatureUrl`: 卖家电子签名图片URL

#### 支付信息
- `paymentTerms`: 支付条款
- `depositAmount`: 定金金额
- `finalPaymentAmount`: 尾款金额
- `paymentStatus`: 支付状态
- `paymentTime`: 支付时间
- `depositPaymentTime`: 定金支付时间

#### 物流信息
- `deliveryTerms`: 交付条款
- `expectedDeliveryTime`: 预计交付时间
- `receiverName`: 收货人姓名
- `receiverPhone`: 收货人联系电话
- `receiverEmail`: 收货人邮箱
- `receiverAddress`: 收货地址
- `shippingAddress`: 发货地址
- `shippingContact`: 发货人姓名
- `shippingPhone`: 发货人电话
- `shippingEmail`: 发货人邮箱

#### 港口信息
- `portOfLoadingName`: 起始港口名称
- `portOfLoadingCode`: 起始港口UN/LOCODE
- `portOfDestinationName`: 目的港口名称
- `portOfDestinationCode`: 目的港口UN/LOCODE

#### 货物信息
- `cargoType`: 货物类型
- `countryOfOrigin`: 生产国别
- `manufacturerName`: 制造厂商名称
- `shipmentType`: 运输类型（LCL拼柜/FCL整柜）
- `containerSize`: 货柜尺寸（如20GP、40HQ）
- `containerQty`: 货柜数量
- `needCertification`: 是否需要认证
- `needFumigation`: 是否需要熏蒸
- `customsService`: 是否包含报关服务
- `insuranceIncluded`: 是否包含保险

#### 进度和文档
- `progressInfo`: 进度信息
- `progressPercentage`: 进度百分比（0-100）
- `remarks`: 订单备注
- `finalDocumentUrl`: 最终合同文档URL
- `documentHash`: 文档哈希值

## 使用示例

### 创建订单
```java
UnifiedOrder order = new UnifiedOrder();

// 基础信息
order.setOrderNumber("ORD-2024-001");
order.setRoomId(1001L);
order.setOrderStatus(UnifiedOrder.OrderStatus.DRAFT);

// 买家信息
order.setBuyerId(2001L);
order.setBuyerCompany("ABC贸易有限公司");
order.setBuyerContact("张三");
order.setBuyerPhone("+86 13800138000");
order.setBuyerEmail("<EMAIL>");
order.setBuyerAddress("上海市浦东新区");

// 卖家信息
order.setSellerId(3001L);
order.setSellerCompany("XYZ制造有限公司");
order.setSellerContact("李四");
order.setSellerPhone("+86 13900139000");
order.setSellerEmail("<EMAIL>");
order.setSellerAddress("广东省深圳市");

// 金额信息
order.setTotalPrice(new BigDecimal("50000.00"));
order.setDepositAmount(new BigDecimal("15000.00"));
order.setFinalPaymentAmount(new BigDecimal("35000.00"));
order.setPaymentStatus(UnifiedOrder.PaymentStatus.UNPAID);

// 物流信息
order.setExpectedDeliveryTime(new Date());
order.setReceiverName("王五");
order.setReceiverPhone("+86 13700137000");
order.setReceiverAddress("北京市朝阳区");
order.setShippingAddress("深圳市南山区工厂");
```

### 订单签署
```java
// 买家签署
order.setBuyerSignTime(LocalDateTime.now());
order.setBuyerSignatureUrl("/signatures/buyer_signature_001.png");

// 卖家签署
order.setSellerSignTime(LocalDateTime.now());
order.setSellerSignatureUrl("/signatures/seller_signature_001.png");

// 更新订单状态
order.setOrderStatus(UnifiedOrder.OrderStatus.PROCESSING);
```

### 支付管理
```java
// 定金支付
order.setPaymentStatus(UnifiedOrder.PaymentStatus.DEPOSIT_PAID);
order.setDepositPaymentTime(LocalDateTime.now());

// 尾款支付
order.setPaymentStatus(UnifiedOrder.PaymentStatus.FINAL_PAID);
order.setPaymentTime(LocalDateTime.now());
```

### 进度更新
```java
// 更新进度
order.setProgressInfo("货物已发出，正在运输中");
order.setProgressPercentage(60);

// 订单完成
order.setOrderStatus(UnifiedOrder.OrderStatus.COMPLETED);
order.setCompletedAt(LocalDateTime.now());
order.setProgressPercentage(100);
```

### 港口和物流设置
```java
// 设置港口信息
order.setPortOfLoadingName("Shanghai");
order.setPortOfLoadingCode("CNSHA");
order.setPortOfDestinationName("Rotterdam");
order.setPortOfDestinationCode("NLRTM");

// 设置运输信息
order.setShipmentType("FCL"); // 整柜
order.setContainerSize("40HQ");
order.setContainerQty(2);
order.setCustomsService(1); // 包含报关服务
order.setInsuranceIncluded(1); // 包含保险
```

## 注意事项

### 数据完整性
1. **必填字段**: 买卖双方基础信息、订单金额等核心字段必须填写
2. **状态一致性**: 订单状态和支付状态应保持逻辑一致
3. **金额计算**: 定金+尾款应等于订单总价
4. **时间顺序**: 各个时间字段应符合业务逻辑顺序

### 状态流转
1. **订单状态**: `DRAFT` → `PROCESSING` → `COMPLETED/CANCELLED`
2. **支付状态**: `UNPAID` → `DEPOSIT_PAID` → `FINAL_PAID`
3. **签署流程**: 买卖双方都签署后才能进入处理状态
4. **完成条件**: 支付完成且货物交付后才能标记为完成

### 业务规则
1. **电子签名**: 买卖双方都必须进行电子签名
2. **分期支付**: 支持定金和尾款的分期支付模式
3. **进度跟踪**: 进度百分比应在0-100之间
4. **文档管理**: 重要文档应计算哈希值确保完整性

### 性能考虑
1. **索引优化**: 为查询字段建立合适的数据库索引
2. **关联查询**: 订单项通过关联查询获取，避免N+1问题
3. **大字段**: 备注等大字段考虑分离存储
4. **缓存策略**: 频繁查询的订单信息可以缓存

### 安全性
1. **敏感信息**: 联系方式等敏感信息需要权限控制
2. **签名验证**: 电子签名需要验证真实性
3. **文档安全**: 合同文档需要安全存储和传输
4. **操作日志**: 重要操作需要记录审计日志
