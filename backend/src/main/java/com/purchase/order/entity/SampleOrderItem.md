# SampleOrderItem 样品订单明细实体文档

## 文件概述

`SampleOrderItem` 是样品订单明细实体类，用于存储样品订单中每个样品项目的详细信息。该实体包含样品的基本信息、价格、卖家信息、媒体资源以及处理状态，是样品订单管理的核心数据模型。

## 核心功能

### 主要职责
- **样品信息管理**: 存储样品的详细规格和属性信息
- **价格计算**: 管理单价和总价的计算关系
- **卖家信息**: 关联样品提供方的详细信息
- **媒体资源**: 管理样品的图片和视频资源
- **状态跟踪**: 跟踪样品从确认到交付的完整流程

### 业务特点
- 支持复杂的样品属性配置（JSON格式）
- 完整的样品生命周期状态管理
- 多媒体资源支持（图片、视频）
- 与竞价系统的紧密集成

## 接口说明

### 常量定义

#### ItemStatus 明细状态
- `PENDING`: 待处理 - 样品订单项创建后的初始状态
- `CONFIRMED`: 已确认 - 卖家确认可以提供样品
- `SHIPPED`: 已发货 - 样品已发出
- `DELIVERED`: 已送达 - 样品已送达买家

### 核心字段

#### 关联信息
- `id`: 明细项唯一标识
- `sampleOrderId`: 所属样品订单ID
- `biddingId`: 关联的样品竞价ID
- `sellerId`: 卖家用户ID

#### 卖家信息
- `sellerCompany`: 卖家公司名称
- `sellerContact`: 卖家联系人
- `sellerPhone`: 卖家联系电话

#### 样品信息
- `productName`: 样品产品名称
- `sampleSpecification`: 样品规格说明
- `sampleQuantity`: 样品数量
- `attributesJson`: 样品属性JSON（颜色、尺寸等）

#### 价格信息
- `samplePrice`: 单个样品价格
- `totalPrice`: 该项总价（samplePrice × sampleQuantity）

#### 媒体资源
- `images`: 样品图片URL列表（JSON数组格式）
- `videos`: 样品视频URL列表（JSON数组格式）

#### 状态和备注
- `itemStatus`: 明细状态
- `remark`: 备注信息

## 使用示例

### 创建样品订单明细
```java
SampleOrderItem item = new SampleOrderItem();

// 基础关联信息
item.setSampleOrderId(1001L);
item.setBiddingId(2001L);
item.setSellerId(3001L);

// 卖家信息
item.setSellerCompany("优质制造有限公司");
item.setSellerContact("张经理");
item.setSellerPhone("+86 13800138000");

// 样品信息
item.setProductName("高端电子产品样品");
item.setSampleSpecification("型号：EP-2024，尺寸：10x5x2cm，重量：200g");
item.setSampleQuantity(3);
item.setSamplePrice(new BigDecimal("50.00"));
item.setTotalPrice(new BigDecimal("150.00"));

// 样品属性（JSON格式）
String attributes = "[{\"name\":\"颜色\",\"value\":\"黑色\"},{\"name\":\"材质\",\"value\":\"铝合金\"}]";
item.setAttributesJson(attributes);

// 媒体资源
item.setImages("[\"https://example.com/sample1.jpg\",\"https://example.com/sample2.jpg\"]");
item.setVideos("[\"https://example.com/sample_demo.mp4\"]");

// 初始状态
item.setItemStatus(SampleOrderItem.ItemStatus.PENDING);
item.setRemark("请确保样品质量符合要求");
```

### 样品订单明细状态管理
```java
public class SampleOrderItemService {
    
    public void confirmSampleItem(Long itemId, String sellerRemark) {
        SampleOrderItem item = sampleOrderItemMapper.selectById(itemId);
        
        if (!SampleOrderItem.ItemStatus.PENDING.equals(item.getItemStatus())) {
            throw new BusinessException("只能确认待处理状态的样品项");
        }
        
        item.setItemStatus(SampleOrderItem.ItemStatus.CONFIRMED);
        item.setRemark(sellerRemark);
        
        sampleOrderItemMapper.updateById(item);
        
        // 发送确认通知
        notificationService.notifySampleConfirmed(item);
    }
    
    public void shipSampleItem(Long itemId, String trackingNumber) {
        SampleOrderItem item = sampleOrderItemMapper.selectById(itemId);
        
        if (!SampleOrderItem.ItemStatus.CONFIRMED.equals(item.getItemStatus())) {
            throw new BusinessException("只能发货已确认的样品项");
        }
        
        item.setItemStatus(SampleOrderItem.ItemStatus.SHIPPED);
        item.setRemark("快递单号：" + trackingNumber);
        
        sampleOrderItemMapper.updateById(item);
        
        // 发送发货通知
        notificationService.notifySampleShipped(item, trackingNumber);
    }
    
    public void deliverSampleItem(Long itemId) {
        SampleOrderItem item = sampleOrderItemMapper.selectById(itemId);
        
        if (!SampleOrderItem.ItemStatus.SHIPPED.equals(item.getItemStatus())) {
            throw new BusinessException("只能确认已发货样品的送达");
        }
        
        item.setItemStatus(SampleOrderItem.ItemStatus.DELIVERED);
        
        sampleOrderItemMapper.updateById(item);
        
        // 发送送达通知
        notificationService.notifySampleDelivered(item);
    }
}
```

### 样品属性处理
```java
public class SampleAttributeService {
    
    public List<SampleAttribute> parseAttributes(String attributesJson) {
        if (attributesJson == null || attributesJson.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(attributesJson, 
                new TypeReference<List<SampleAttribute>>() {});
        } catch (Exception e) {
            log.error("解析样品属性失败: {}", attributesJson, e);
            return Collections.emptyList();
        }
    }
    
    public String formatAttributesForDisplay(String attributesJson) {
        List<SampleAttribute> attributes = parseAttributes(attributesJson);
        return attributes.stream()
            .map(attr -> attr.getName() + ": " + attr.getValue())
            .collect(Collectors.joining(", "));
    }
    
    public void updateSampleAttributes(Long itemId, List<SampleAttribute> attributes) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            String attributesJson = mapper.writeValueAsString(attributes);
            
            SampleOrderItem item = new SampleOrderItem();
            item.setId(itemId);
            item.setAttributesJson(attributesJson);
            
            sampleOrderItemMapper.updateById(item);
        } catch (Exception e) {
            throw new BusinessException("更新样品属性失败", e);
        }
    }
}
```

### 媒体资源管理
```java
public class SampleMediaService {
    
    public List<String> getImageUrls(SampleOrderItem item) {
        return parseMediaUrls(item.getImages());
    }
    
    public List<String> getVideoUrls(SampleOrderItem item) {
        return parseMediaUrls(item.getVideos());
    }
    
    private List<String> parseMediaUrls(String mediaJson) {
        if (mediaJson == null || mediaJson.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(mediaJson, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            log.error("解析媒体URL失败: {}", mediaJson, e);
            return Collections.emptyList();
        }
    }
    
    public void addSampleImage(Long itemId, String imageUrl) {
        SampleOrderItem item = sampleOrderItemMapper.selectById(itemId);
        List<String> images = getImageUrls(item);
        images.add(imageUrl);
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            String imagesJson = mapper.writeValueAsString(images);
            
            item.setImages(imagesJson);
            sampleOrderItemMapper.updateById(item);
        } catch (Exception e) {
            throw new BusinessException("添加样品图片失败", e);
        }
    }
}
```

### 价格计算和验证
```java
public class SamplePriceService {
    
    public void calculateTotalPrice(SampleOrderItem item) {
        if (item.getSamplePrice() != null && item.getSampleQuantity() != null) {
            BigDecimal totalPrice = item.getSamplePrice()
                .multiply(new BigDecimal(item.getSampleQuantity()));
            item.setTotalPrice(totalPrice);
        }
    }
    
    public void validatePricing(SampleOrderItem item) {
        if (item.getSamplePrice() == null || item.getSamplePrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ValidationException("样品单价必须大于0");
        }
        
        if (item.getSampleQuantity() == null || item.getSampleQuantity() <= 0) {
            throw new ValidationException("样品数量必须大于0");
        }
        
        BigDecimal expectedTotal = item.getSamplePrice()
            .multiply(new BigDecimal(item.getSampleQuantity()));
        
        if (item.getTotalPrice() != null && 
            item.getTotalPrice().compareTo(expectedTotal) != 0) {
            throw new ValidationException("总价计算不正确");
        }
    }
}
```

## 注意事项

### 数据完整性
1. **关联关系**: sampleOrderId和biddingId应关联到有效记录
2. **价格一致性**: totalPrice应等于samplePrice × sampleQuantity
3. **JSON格式**: attributesJson、images、videos应为有效JSON格式
4. **状态流转**: 状态变更应按照业务流程进行

### 业务规则
1. **状态流转**: 按照PENDING → CONFIRMED → SHIPPED → DELIVERED顺序
2. **数量限制**: 样品数量应在合理范围内
3. **媒体资源**: 图片和视频URL应指向有效资源
4. **属性验证**: 样品属性应符合产品规格要求

### 性能优化
1. **索引优化**: 为sampleOrderId、biddingId、sellerId建立索引
2. **JSON处理**: 大量属性数据考虑使用专门的JSON字段类型
3. **媒体加载**: 图片和视频采用懒加载策略
4. **分页查询**: 大量明细数据使用分页

### 安全考虑
1. **权限控制**: 确保用户只能操作相关的样品订单项
2. **文件安全**: 媒体文件需要进行安全验证
3. **数据验证**: 严格验证价格和数量数据
4. **操作日志**: 记录重要的状态变更操作
