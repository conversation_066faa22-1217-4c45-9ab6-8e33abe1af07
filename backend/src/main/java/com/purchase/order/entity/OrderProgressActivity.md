# OrderProgressActivity 订单进度活动实体文档

## 文件概述

`OrderProgressActivity` 是订单进度活动实体类，用于记录订单执行过程中的关键节点和活动信息。该实体支持多媒体内容记录，为买家提供订单执行的可视化跟踪，增强订单透明度和客户体验。

## 核心功能

### 主要职责
- **进度跟踪**: 记录订单执行过程中的关键进度节点
- **活动记录**: 详细记录每个进度节点的具体活动内容
- **多媒体支持**: 支持图片和视频等多媒体内容展示
- **时间轴管理**: 提供订单执行的完整时间轴视图
- **透明化服务**: 为买家提供订单执行的透明化信息

### 业务特点
- 标准化的进度节点值（0, 20, 40, 60, 80, 100）
- 丰富的活动描述和多媒体内容
- 完整的时间记录和创建人追踪
- 软删除支持，保留历史记录

## 接口说明

### 核心字段

#### 关联信息
- `id`: 活动记录唯一标识
- `orderId`: 关联的订单ID
- `createdBy`: 创建人ID（通常是卖家或管理员）

#### 进度信息
- `stageValue`: 进度节点值，标准值为0, 20, 40, 60, 80, 100
  - 0: 订单确认
  - 20: 生产准备
  - 40: 生产中
  - 60: 生产完成
  - 80: 发货
  - 100: 完成

#### 活动内容
- `title`: 活动标题（简短描述）
- `description`: 活动详细描述
- `activityTime`: 活动发生的具体时间

#### 多媒体资源
- `imageUrls`: 活动相关图片URL数组（JSON格式）
- `videoUrls`: 活动相关视频URL数组（JSON格式）

#### 系统字段
- `createdAt`: 记录创建时间
- `updatedAt`: 记录更新时间
- `deleted`: 软删除标记

## 使用示例

### 创建订单进度活动
```java
// 创建生产开始活动
OrderProgressActivity activity = OrderProgressActivity.builder()
    .orderId(1001L)
    .stageValue(40)
    .title("生产开始")
    .description("订单已进入生产阶段，预计生产周期15天，我们将严格按照您的要求进行生产")
    .activityTime(LocalDateTime.now())
    .createdBy(2001L) // 卖家ID
    .imageUrls("[\"https://example.com/production1.jpg\",\"https://example.com/production2.jpg\"]")
    .videoUrls("[\"https://example.com/production_video.mp4\"]")
    .build();

orderProgressActivityMapper.insert(activity);
```

### 订单进度管理服务
```java
public class OrderProgressService {
    
    public void addProgressActivity(Long orderId, Integer stageValue, 
                                  String title, String description, 
                                  List<String> imageUrls, List<String> videoUrls,
                                  Long createdBy) {
        // 验证进度值
        if (!isValidStageValue(stageValue)) {
            throw new BusinessException("无效的进度节点值");
        }
        
        // 检查进度是否倒退
        validateProgressSequence(orderId, stageValue);
        
        // 创建活动记录
        OrderProgressActivity activity = OrderProgressActivity.builder()
            .orderId(orderId)
            .stageValue(stageValue)
            .title(title)
            .description(description)
            .activityTime(LocalDateTime.now())
            .createdBy(createdBy)
            .imageUrls(convertToJson(imageUrls))
            .videoUrls(convertToJson(videoUrls))
            .build();
        
        orderProgressActivityMapper.insert(activity);
        
        // 更新订单状态
        updateOrderProgress(orderId, stageValue);
        
        // 发送进度通知
        notificationService.notifyOrderProgress(activity);
    }
    
    private boolean isValidStageValue(Integer stageValue) {
        return Arrays.asList(0, 20, 40, 60, 80, 100).contains(stageValue);
    }
    
    private void validateProgressSequence(Long orderId, Integer newStageValue) {
        // 获取最新进度
        OrderProgressActivity latestActivity = orderProgressActivityMapper.selectOne(
            new QueryWrapper<OrderProgressActivity>()
                .eq("order_id", orderId)
                .eq("deleted", "0")
                .orderByDesc("stage_value")
                .last("LIMIT 1")
        );
        
        if (latestActivity != null && newStageValue < latestActivity.getStageValue()) {
            throw new BusinessException("订单进度不能倒退");
        }
    }
}
```

### 获取订单进度时间轴
```java
public class OrderTimelineService {
    
    public List<OrderProgressActivity> getOrderTimeline(Long orderId) {
        return orderProgressActivityMapper.selectList(
            new QueryWrapper<OrderProgressActivity>()
                .eq("order_id", orderId)
                .eq("deleted", "0")
                .orderByAsc("stage_value")
                .orderByAsc("activity_time")
        );
    }
    
    public OrderProgressSummary getProgressSummary(Long orderId) {
        List<OrderProgressActivity> activities = getOrderTimeline(orderId);
        
        if (activities.isEmpty()) {
            return OrderProgressSummary.builder()
                .currentStage(0)
                .progressPercentage(0)
                .totalActivities(0)
                .build();
        }
        
        // 获取最新进度
        OrderProgressActivity latestActivity = activities.get(activities.size() - 1);
        
        return OrderProgressSummary.builder()
            .currentStage(latestActivity.getStageValue())
            .progressPercentage(latestActivity.getStageValue())
            .totalActivities(activities.size())
            .latestActivityTime(latestActivity.getActivityTime())
            .latestActivityTitle(latestActivity.getTitle())
            .build();
    }
}
```

### 多媒体资源处理
```java
public class ProgressMediaService {
    
    public List<String> getImageUrls(OrderProgressActivity activity) {
        return parseMediaUrls(activity.getImageUrls());
    }
    
    public List<String> getVideoUrls(OrderProgressActivity activity) {
        return parseMediaUrls(activity.getVideoUrls());
    }
    
    private List<String> parseMediaUrls(String mediaJson) {
        if (mediaJson == null || mediaJson.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(mediaJson, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            log.error("解析媒体URL失败: {}", mediaJson, e);
            return Collections.emptyList();
        }
    }
    
    public void addProgressImage(Long activityId, String imageUrl) {
        OrderProgressActivity activity = orderProgressActivityMapper.selectById(activityId);
        List<String> images = getImageUrls(activity);
        images.add(imageUrl);
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            String imagesJson = mapper.writeValueAsString(images);
            
            activity.setImageUrls(imagesJson);
            orderProgressActivityMapper.updateById(activity);
        } catch (Exception e) {
            throw new BusinessException("添加进度图片失败", e);
        }
    }
}
```

### 进度通知服务
```java
public class ProgressNotificationService {
    
    public void notifyOrderProgress(OrderProgressActivity activity) {
        // 获取订单信息
        Order order = orderService.getById(activity.getOrderId());
        
        // 构建通知内容
        String notificationTitle = String.format("订单进度更新 - %s", activity.getTitle());
        String notificationContent = String.format(
            "您的订单 #%s 有新的进度更新：%s。当前进度：%d%%",
            order.getOrderNumber(),
            activity.getDescription(),
            activity.getStageValue()
        );
        
        // 发送给买家
        notificationService.sendToUser(
            order.getBuyerId(),
            notificationTitle,
            notificationContent,
            NotificationType.ORDER_PROGRESS
        );
        
        // 如果有图片或视频，发送多媒体通知
        if (hasMediaContent(activity)) {
            sendMediaNotification(activity, order);
        }
    }
    
    private boolean hasMediaContent(OrderProgressActivity activity) {
        List<String> images = parseMediaUrls(activity.getImageUrls());
        List<String> videos = parseMediaUrls(activity.getVideoUrls());
        return !images.isEmpty() || !videos.isEmpty();
    }
}
```

### 进度统计分析
```java
public class ProgressAnalyticsService {
    
    public Map<String, Object> getProgressStatistics(Long orderId) {
        List<OrderProgressActivity> activities = getOrderTimeline(orderId);
        
        Map<String, Object> stats = new HashMap<>();
        
        // 基础统计
        stats.put("totalActivities", activities.size());
        stats.put("currentProgress", getCurrentProgress(activities));
        stats.put("averageStageTime", calculateAverageStageTime(activities));
        
        // 阶段分布
        Map<Integer, Long> stageDistribution = activities.stream()
            .collect(Collectors.groupingBy(
                OrderProgressActivity::getStageValue,
                Collectors.counting()
            ));
        stats.put("stageDistribution", stageDistribution);
        
        // 媒体内容统计
        long totalImages = activities.stream()
            .mapToLong(a -> parseMediaUrls(a.getImageUrls()).size())
            .sum();
        long totalVideos = activities.stream()
            .mapToLong(a -> parseMediaUrls(a.getVideoUrls()).size())
            .sum();
        
        stats.put("totalImages", totalImages);
        stats.put("totalVideos", totalVideos);
        
        return stats;
    }
    
    private Integer getCurrentProgress(List<OrderProgressActivity> activities) {
        return activities.stream()
            .mapToInt(OrderProgressActivity::getStageValue)
            .max()
            .orElse(0);
    }
}
```

## 注意事项

### 业务规则
1. **进度顺序**: 进度值应该递增，不允许倒退
2. **标准节点**: 使用标准的进度节点值（0, 20, 40, 60, 80, 100）
3. **时间逻辑**: 活动时间应该合理，不能早于订单创建时间
4. **权限控制**: 只有相关的卖家或管理员可以创建进度活动

### 数据完整性
1. **关联关系**: orderId应关联到有效的订单记录
2. **JSON格式**: imageUrls和videoUrls应为有效的JSON数组格式
3. **媒体资源**: 图片和视频URL应指向有效的资源
4. **软删除**: 使用软删除保留历史记录

### 性能优化
1. **索引优化**: 为orderId、stageValue、activityTime建立索引
2. **分页查询**: 大量活动记录使用分页查询
3. **媒体加载**: 图片和视频采用懒加载策略
4. **缓存策略**: 频繁查询的进度信息可以缓存

### 安全考虑
1. **权限验证**: 确保只有授权用户可以创建和查看进度
2. **媒体安全**: 上传的媒体文件需要安全验证
3. **数据验证**: 严格验证进度值和时间数据
4. **操作日志**: 记录重要的进度更新操作
