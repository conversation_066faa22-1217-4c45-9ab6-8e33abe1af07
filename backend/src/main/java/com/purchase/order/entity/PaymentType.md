# PaymentType 支付类型枚举文档

## 文件概述

`PaymentType` 是支付类型枚举类，用于标识支付凭证的类型。该枚举定义了系统中支持的三种支付方式：定金、尾款和全款，并提供了相应的业务逻辑方法来判断支付类型的适用性。

## 核心功能

### 主要职责
- **支付类型定义**: 定义系统支持的支付类型
- **类型描述**: 提供支付类型的中文描述
- **适用性检查**: 检查支付类型是否适用于特定订单类型
- **业务规则**: 封装支付类型相关的业务规则

### 枚举值说明
- `DEPOSIT`: 定金支付，适用于普通订单和结算单
- `FINAL`: 尾款支付，适用于普通订单和结算单
- `FULL`: 全款支付，适用于所有订单类型

## 接口说明

### 枚举常量

#### DEPOSIT (定金)
- **适用场景**: 普通订单(UNIFIED)和结算单(SETTLEMENT)
- **业务含义**: 订单总金额的部分预付款
- **中文描述**: "定金"

#### FINAL (尾款)
- **适用场景**: 普通订单(UNIFIED)和结算单(SETTLEMENT)
- **业务含义**: 订单总金额减去定金后的剩余款项
- **中文描述**: "尾款"

#### FULL (全款)
- **适用场景**: 所有订单类型
- **业务含义**: 订单的全部金额一次性支付
- **中文描述**: "全款"

### 核心方法

#### getDescription()
```java
public String getDescription()
```
- **功能**: 获取支付类型的中文描述
- **返回值**: String - 支付类型的中文名称
- **用途**: 用于前端显示和报表生成

#### isApplicableFor(String orderType)
```java
public boolean isApplicableFor(String orderType)
```
- **功能**: 检查支付类型是否适用于指定的订单类型
- **参数**: 
  - `orderType`: 订单类型（"UNIFIED", "SETTLEMENT", "SAMPLE"等）
- **返回值**: boolean - true表示适用，false表示不适用
- **业务规则**:
  - DEPOSIT和FINAL只适用于UNIFIED和SETTLEMENT订单
  - FULL适用于所有订单类型

## 使用示例

### 基本使用
```java
// 获取支付类型描述
PaymentType depositType = PaymentType.DEPOSIT;
String description = depositType.getDescription(); // "定金"

// 检查支付类型适用性
boolean applicable = PaymentType.DEPOSIT.isApplicableFor("UNIFIED"); // true
boolean notApplicable = PaymentType.DEPOSIT.isApplicableFor("SAMPLE"); // false
```

### 在支付处理中的使用
```java
public class PaymentService {
    
    public void processPayment(String orderType, PaymentType paymentType, BigDecimal amount) {
        // 验证支付类型是否适用于订单类型
        if (!paymentType.isApplicableFor(orderType)) {
            throw new BusinessException(
                String.format("支付类型 %s 不适用于订单类型 %s", 
                    paymentType.getDescription(), orderType)
            );
        }
        
        // 处理支付逻辑
        switch (paymentType) {
            case DEPOSIT:
                processDepositPayment(amount);
                break;
            case FINAL:
                processFinalPayment(amount);
                break;
            case FULL:
                processFullPayment(amount);
                break;
        }
    }
}
```

### 在前端选择器中的使用
```java
// 获取适用于特定订单类型的支付类型列表
public List<PaymentTypeOption> getAvailablePaymentTypes(String orderType) {
    return Arrays.stream(PaymentType.values())
        .filter(type -> type.isApplicableFor(orderType))
        .map(type -> new PaymentTypeOption(type.name(), type.getDescription()))
        .collect(Collectors.toList());
}
```

### 在支付凭证验证中的使用
```java
public class PaymentProofValidator {
    
    public void validatePaymentProof(PaymentProof proof) {
        PaymentType paymentType = proof.getPaymentType();
        String orderType = proof.getOrderType();
        
        // 验证支付类型适用性
        if (!paymentType.isApplicableFor(orderType)) {
            throw new ValidationException(
                String.format("订单类型 %s 不支持 %s 支付", 
                    orderType, paymentType.getDescription())
            );
        }
        
        // 其他验证逻辑...
    }
}
```

## 注意事项

### 业务规则
1. **支付类型限制**: 定金和尾款只适用于普通订单和结算单
2. **全款通用性**: 全款支付适用于所有订单类型
3. **类型一致性**: 同一订单的多次支付应保持类型一致性
4. **金额验证**: 定金+尾款应等于订单总金额

### 扩展性考虑
1. **新增支付类型**: 添加新的枚举值时需要更新相关方法
2. **订单类型扩展**: 新增订单类型时需要更新适用性规则
3. **国际化支持**: getDescription()方法可以扩展支持多语言
4. **业务规则配置**: 适用性规则可以考虑配置化管理

### 使用建议
1. **类型检查**: 在处理支付前始终检查类型适用性
2. **异常处理**: 对不适用的支付类型抛出明确的异常
3. **日志记录**: 记录支付类型的使用情况便于审计
4. **前端集成**: 前端应根据订单类型动态显示可用支付类型

### 相关类
- `PaymentProof`: 支付凭证实体，使用PaymentType标识支付类型
- `Order`: 订单实体，定义订单类型
- `PaymentService`: 支付服务，处理不同类型的支付
- `PaymentValidator`: 支付验证器，验证支付类型的合法性
