# OrderItem 订单项目实体文档

## 文件概述

`OrderItem` 是订单项目实体类，用于存储订单中的具体采购清单项目。每个订单可以包含多个订单项目，每个项目代表一种具体的商品或服务，包含详细的产品信息、价格计算和物理属性。

## 核心功能

### 主要职责
- **产品信息管理**: 存储产品的基本信息和规格
- **价格计算**: 支持复杂的价格计算逻辑
- **税费处理**: 处理税率和税额计算
- **折扣管理**: 支持折扣率和折扣金额
- **物理属性**: 记录重量、体积等物理特性
- **分类关联**: 与产品分类系统集成

### 业务特点
- 支持灵活的价格计算公式
- 完整的税费和折扣处理
- 丰富的产品属性扩展
- 与订单主表的强关联关系

## 接口说明

### 核心字段分类

#### 基础信息
- `id`: 项目主键ID
- `orderId`: 关联的订单ID
- `name`: 产品名称
- `description`: 产品描述
- `hsCode`: 海关商品编码
- `specification`: 规格参数
- `categoryId`: 产品分类ID

#### 数量和单位
- `unit`: 计量单位（如：个、吨、米等）
- `quantity`: 采购数量
- `sortOrder`: 在订单中的排序顺序

#### 价格计算
- `unitPrice`: 单价
- `totalPrice`: 总价（单价 × 数量）
- `taxRate`: 税率（百分比）
- `taxAmount`: 税额（总价 × 税率%）
- `discountRate`: 折扣率（百分比）
- `discountAmount`: 折扣金额（总价 × 折扣率%）
- `finalPrice`: 最终价格（总价 + 税额 - 折扣金额）

#### 物理属性
- `weight`: 重量（千克）
- `volume`: 体积（立方米）

#### 扩展信息
- `attributesJson`: 产品属性JSON字符串
- `images`: 产品图片URL列表（逗号分隔）
- `remarks`: 项目备注

## 使用示例

### 创建订单项目
```java
OrderItem item = new OrderItem();

// 基础信息
item.setOrderId(1001L);
item.setName("工业传感器");
item.setDescription("高精度压力传感器，适用于工业自动化");
item.setHsCode("9026209090");
item.setSpecification("量程：0-100bar，精度：±0.1%，输出：4-20mA");
item.setUnit("个");
item.setCategoryId(2001L);

// 数量和价格
item.setQuantity(new BigDecimal("100"));
item.setUnitPrice(new BigDecimal("250.00"));
item.setTotalPrice(new BigDecimal("25000.00")); // 100 × 250

// 税费计算
item.setTaxRate(new BigDecimal("13.00")); // 13%增值税
item.setTaxAmount(new BigDecimal("3250.00")); // 25000 × 13%

// 折扣处理
item.setDiscountRate(new BigDecimal("5.00")); // 5%折扣
item.setDiscountAmount(new BigDecimal("1250.00")); // 25000 × 5%

// 最终价格
item.setFinalPrice(new BigDecimal("27000.00")); // 25000 + 3250 - 1250

// 物理属性
item.setWeight(new BigDecimal("50.0")); // 50kg
item.setVolume(new BigDecimal("0.5")); // 0.5m³

// 扩展信息
item.setAttributesJson("{\"material\":\"不锈钢\",\"certification\":\"CE,ISO9001\"}");
item.setImages("sensor1.jpg,sensor2.jpg,sensor_spec.jpg");
item.setRemarks("需要提供质量检测报告");
item.setSortOrder(1);
```

### 价格计算工具方法
```java
public class OrderItemCalculator {
    
    /**
     * 计算订单项目的所有价格
     */
    public static void calculatePrices(OrderItem item) {
        BigDecimal quantity = item.getQuantity();
        BigDecimal unitPrice = item.getUnitPrice();
        
        // 计算总价
        BigDecimal totalPrice = unitPrice.multiply(quantity);
        item.setTotalPrice(totalPrice);
        
        // 计算税额
        if (item.getTaxRate() != null) {
            BigDecimal taxAmount = totalPrice.multiply(item.getTaxRate())
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            item.setTaxAmount(taxAmount);
        }
        
        // 计算折扣金额
        if (item.getDiscountRate() != null) {
            BigDecimal discountAmount = totalPrice.multiply(item.getDiscountRate())
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            item.setDiscountAmount(discountAmount);
        }
        
        // 计算最终价格
        BigDecimal finalPrice = totalPrice;
        if (item.getTaxAmount() != null) {
            finalPrice = finalPrice.add(item.getTaxAmount());
        }
        if (item.getDiscountAmount() != null) {
            finalPrice = finalPrice.subtract(item.getDiscountAmount());
        }
        item.setFinalPrice(finalPrice);
    }
}
```

### 批量创建订单项目
```java
public List<OrderItem> createOrderItems(Long orderId, List<ProductInfo> products) {
    List<OrderItem> items = new ArrayList<>();
    
    for (int i = 0; i < products.size(); i++) {
        ProductInfo product = products.get(i);
        OrderItem item = new OrderItem();
        
        // 设置基础信息
        item.setOrderId(orderId);
        item.setName(product.getName());
        item.setDescription(product.getDescription());
        item.setHsCode(product.getHsCode());
        item.setSpecification(product.getSpecification());
        item.setUnit(product.getUnit());
        item.setCategoryId(product.getCategoryId());
        
        // 设置数量和价格
        item.setQuantity(product.getQuantity());
        item.setUnitPrice(product.getUnitPrice());
        
        // 设置排序
        item.setSortOrder(i + 1);
        
        // 计算价格
        OrderItemCalculator.calculatePrices(item);
        
        items.add(item);
    }
    
    return items;
}
```

### 订单项目查询
```java
// 根据订单ID查询所有项目
List<OrderItem> items = orderItemMapper.selectList(
    new QueryWrapper<OrderItem>()
        .eq("order_id", orderId)
        .orderByAsc("sort_order")
);

// 计算订单总金额
BigDecimal orderTotal = items.stream()
    .map(OrderItem::getFinalPrice)
    .reduce(BigDecimal.ZERO, BigDecimal::add);

// 计算订单总重量
BigDecimal totalWeight = items.stream()
    .map(item -> item.getWeight() != null ? 
        item.getWeight().multiply(item.getQuantity()) : BigDecimal.ZERO)
    .reduce(BigDecimal.ZERO, BigDecimal::add);
```

### 属性JSON处理
```java
// 设置产品属性
Map<String, Object> attributes = new HashMap<>();
attributes.put("material", "不锈钢");
attributes.put("certification", Arrays.asList("CE", "ISO9001"));
attributes.put("warranty", "2年");

String attributesJson = JSON.toJSONString(attributes);
item.setAttributesJson(attributesJson);

// 读取产品属性
String json = item.getAttributesJson();
if (json != null) {
    Map<String, Object> attrs = JSON.parseObject(json, Map.class);
    String material = (String) attrs.get("material");
    List<String> certifications = (List<String>) attrs.get("certification");
}
```

## 注意事项

### 数据完整性
1. **必填字段**: `orderId`、`name`、`quantity`、`unitPrice` 等核心字段必须填写
2. **价格一致性**: 各个价格字段之间应保持计算逻辑一致
3. **数量验证**: 数量必须大于0
4. **单价验证**: 单价必须大于等于0

### 计算规则
1. **总价计算**: 总价 = 单价 × 数量
2. **税额计算**: 税额 = 总价 × 税率%
3. **折扣计算**: 折扣金额 = 总价 × 折扣率%
4. **最终价格**: 最终价格 = 总价 + 税额 - 折扣金额
5. **精度控制**: 金额计算保留2位小数，使用四舍五入

### 业务规则
1. **排序管理**: sortOrder用于控制项目在订单中的显示顺序
2. **分类关联**: categoryId应关联到有效的产品分类
3. **图片管理**: 多个图片URL使用逗号分隔存储
4. **属性扩展**: attributesJson用于存储动态的产品属性

### 性能优化
1. **批量操作**: 大量订单项目应使用批量插入
2. **索引优化**: 为orderId建立索引提高查询性能
3. **关联查询**: 避免N+1查询问题
4. **缓存策略**: 频繁查询的分类信息可以缓存

### 数据安全
1. **输入验证**: 严格验证数量、价格等数值字段
2. **JSON安全**: attributesJson内容需要验证防止注入
3. **文件安全**: 图片URL需要验证有效性
4. **权限控制**: 确保只有授权用户可以修改订单项目
