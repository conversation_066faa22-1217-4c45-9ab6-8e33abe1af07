# PaymentProof 支付凭证实体文档

## 文件概述

`PaymentProof` 是支付凭证实体类，用于存储买家上传的支付凭证信息和审核状态。该实体支持多种订单类型（普通订单、样品订单、结算单）的支付凭证管理，包含完整的审核流程和状态管理。

## 核心功能

### 主要职责
- **支付凭证存储**: 存储买家上传的支付凭证文件和相关信息
- **审核流程管理**: 管理支付凭证的审核状态和流程
- **多订单类型支持**: 支持普通订单、样品订单、结算单的支付凭证
- **双向凭证管理**: 支持买家支付凭证和管理员收款凭证
- **金额确认**: 支持买家申报金额和管理员确认金额

### 业务特点
- 支持定金、尾款、全款等多种支付类型
- 完整的审核工作流（待审核→已确认/已拒绝）
- 双重凭证机制（买家上传+管理员确认）
- 多订单类型的统一管理

## 接口说明

### 常量定义

#### ProofStatus 凭证状态
- `PENDING`: 待审核 - 买家上传凭证后的初始状态
- `CONFIRMED`: 已确认 - 管理员审核通过
- `REJECTED`: 已拒绝 - 管理员审核拒绝

#### OrderType 订单类型
- `UNIFIED`: 普通订单直接支付
- `SAMPLE`: 样品订单支付
- `SETTLEMENT`: 结算单支付

### 核心字段

#### 基础信息
- `id`: 凭证唯一标识
- `orderId`: 关联的普通订单ID
- `sampleOrderId`: 关联的样品订单ID
- `settlementId`: 关联的结算单ID
- `orderType`: 订单类型（UNIFIED/SAMPLE/SETTLEMENT）
- `paymentType`: 支付类型（DEPOSIT/FINAL/FULL）

#### 买家信息
- `uploadedBy`: 上传用户ID（买家）
- `uploadedAt`: 上传时间
- `proofUrl`: 支付凭证文件URL
- `amount`: 支付金额
- `buyerPaymentTime`: 买家付款时间
- `buyerRemark`: 买家备注

#### 管理员审核信息
- `status`: 凭证状态
- `confirmedBy`: 确认人ID（管理员）
- `confirmedAt`: 确认时间
- `adminConfirmedAmount`: 管理员确认金额
- `adminProofUrl`: 管理员收款凭证URL
- `adminRemark`: 管理员备注
- `adminConfirmTime`: 管理员确认时间
- `remarks`: 管理员备注（兼容字段）

### 业务方法

#### getBusinessId()
```java
public Long getBusinessId()
```
- **功能**: 获取关联的业务ID
- **返回值**: 根据订单类型返回对应的业务ID
- **逻辑**: 
  - UNIFIED类型返回orderId
  - SAMPLE类型返回sampleOrderId
  - SETTLEMENT类型返回settlementId

#### getBusinessTypeDescription()
```java
public String getBusinessTypeDescription()
```
- **功能**: 获取业务类型的中文描述
- **返回值**: 订单类型的中文名称
- **用途**: 用于前端显示和报表生成

## 使用示例

### 创建支付凭证
```java
// 创建普通订单支付凭证
PaymentProof proof = new PaymentProof();
proof.setOrderId(1001L);
proof.setOrderType(PaymentProof.OrderType.UNIFIED);
proof.setPaymentType(PaymentType.DEPOSIT);
proof.setProofUrl("https://example.com/proof.jpg");
proof.setUploadedBy(2001L);
proof.setUploadedAt(LocalDateTime.now());
proof.setAmount(new BigDecimal("5000.00"));
proof.setBuyerPaymentTime(LocalDateTime.now().minusHours(1));
proof.setBuyerRemark("定金支付凭证");
proof.setStatus(PaymentProof.ProofStatus.PENDING);
```

### 管理员审核支付凭证
```java
public class PaymentProofService {
    
    public void confirmPaymentProof(Long proofId, Long adminId, 
                                  BigDecimal confirmedAmount, String adminRemark) {
        PaymentProof proof = paymentProofMapper.selectById(proofId);
        
        // 验证状态
        if (!PaymentProof.ProofStatus.PENDING.equals(proof.getStatus())) {
            throw new BusinessException("只能审核待审核状态的支付凭证");
        }
        
        // 更新审核信息
        proof.setStatus(PaymentProof.ProofStatus.CONFIRMED);
        proof.setConfirmedBy(adminId);
        proof.setConfirmedAt(LocalDateTime.now());
        proof.setAdminConfirmedAmount(confirmedAmount);
        proof.setAdminRemark(adminRemark);
        
        paymentProofMapper.updateById(proof);
        
        // 发送确认通知
        notificationService.notifyPaymentConfirmed(proof);
    }
    
    public void rejectPaymentProof(Long proofId, Long adminId, String reason) {
        PaymentProof proof = paymentProofMapper.selectById(proofId);
        
        proof.setStatus(PaymentProof.ProofStatus.REJECTED);
        proof.setConfirmedBy(adminId);
        proof.setConfirmedAt(LocalDateTime.now());
        proof.setAdminRemark(reason);
        
        paymentProofMapper.updateById(proof);
        
        // 发送拒绝通知
        notificationService.notifyPaymentRejected(proof);
    }
}
```

### 查询支付凭证
```java
// 按订单类型和业务ID查询
public List<PaymentProof> getPaymentProofsByBusiness(String orderType, Long businessId) {
    QueryWrapper<PaymentProof> wrapper = new QueryWrapper<>();
    wrapper.eq("order_type", orderType);
    
    switch (orderType) {
        case PaymentProof.OrderType.UNIFIED:
            wrapper.eq("order_id", businessId);
            break;
        case PaymentProof.OrderType.SAMPLE:
            wrapper.eq("sample_order_id", businessId);
            break;
        case PaymentProof.OrderType.SETTLEMENT:
            wrapper.eq("settlement_id", businessId);
            break;
    }
    
    return paymentProofMapper.selectList(wrapper);
}

// 获取业务关联信息
public String getBusinessInfo(PaymentProof proof) {
    Long businessId = proof.getBusinessId();
    String businessType = proof.getBusinessTypeDescription();
    return String.format("%s (ID: %d)", businessType, businessId);
}
```

### 支付凭证统计
```java
public class PaymentProofStatisticsService {
    
    public Map<String, Object> getProofStatistics(String orderType) {
        List<PaymentProof> proofs = paymentProofMapper.selectList(
            new QueryWrapper<PaymentProof>().eq("order_type", orderType)
        );
        
        Map<String, Object> stats = new HashMap<>();
        
        // 按状态统计
        Map<String, Long> statusCount = proofs.stream()
            .collect(Collectors.groupingBy(
                PaymentProof::getStatus, 
                Collectors.counting()
            ));
        
        // 按支付类型统计
        Map<PaymentType, Long> typeCount = proofs.stream()
            .collect(Collectors.groupingBy(
                PaymentProof::getPaymentType, 
                Collectors.counting()
            ));
        
        // 总金额统计
        BigDecimal totalAmount = proofs.stream()
            .filter(p -> PaymentProof.ProofStatus.CONFIRMED.equals(p.getStatus()))
            .map(PaymentProof::getAdminConfirmedAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        stats.put("statusCount", statusCount);
        stats.put("typeCount", typeCount);
        stats.put("totalAmount", totalAmount);
        stats.put("totalCount", proofs.size());
        
        return stats;
    }
}
```

## 注意事项

### 数据完整性
1. **关联关系**: 根据orderType确保对应的业务ID字段不为空
2. **状态一致性**: 审核状态变更需要同时更新相关时间和人员信息
3. **金额验证**: 管理员确认金额应与买家申报金额进行比较
4. **文件存储**: 凭证文件URL应指向有效的文件资源

### 业务规则
1. **审核流程**: 只有PENDING状态的凭证可以进行审核
2. **支付类型限制**: 支付类型应与订单类型匹配
3. **重复上传**: 同一订单的同一支付类型不应重复上传
4. **时间逻辑**: 确认时间应晚于上传时间

### 性能优化
1. **索引设计**: 为orderType、status、businessId等查询字段建立索引
2. **分页查询**: 大量凭证查询使用分页
3. **文件处理**: 大文件上传使用异步处理
4. **缓存策略**: 频繁查询的凭证信息可以缓存

### 安全考虑
1. **权限控制**: 确保用户只能操作自己的支付凭证
2. **文件安全**: 上传文件需要进行安全扫描
3. **数据脱敏**: 敏感信息需要权限控制显示
4. **操作日志**: 记录重要的状态变更操作

### 扩展性
1. **新订单类型**: 支持新的订单类型扩展
2. **审核流程**: 支持多级审核流程
3. **通知机制**: 集成多种通知方式
4. **国际化**: 支持多语言的状态和类型描述
