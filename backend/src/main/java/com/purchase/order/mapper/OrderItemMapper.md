# OrderItemMapper.md

## 1. 文件概述

`OrderItemMapper.java` 是订单模块中的一个MyBatis-Plus Mapper接口，位于 `com.purchase.order.mapper` 包中。它继承自MyBatis-Plus的 `BaseMapper<OrderItem>` 接口，为 `OrderItem` 实体提供了基本的数据库操作能力。`OrderItem` 实体代表了订单中的具体商品或服务项，包含了商品ID、数量、单价等信息。该Mapper接口是订单服务层与数据库进行交互的桥梁，负责将业务对象的操作转换为SQL语句，实现订单项的持久化管理。

## 2. 核心功能

*   **基础CRUD操作**: 继承 `BaseMapper`，自动拥有对 `OrderItem` 实体进行插入（`insert`）、根据ID查询（`selectById`）、根据条件查询列表（`selectList`）、更新（`updateById`）和删除（`deleteById`）等基础的增删改查功能。
*   **简化开发**: 通过MyBatis-Plus的自动化特性，无需编写SQL语句即可完成常见的订单项数据库操作，极大地提高了开发效率。
*   **类型安全**: 针对 `OrderItem` 实体进行操作，提供了编译时类型检查，减少了运行时错误。
*   **数据持久化**: 负责将 `OrderItem` 领域对象的状态同步到数据库，或从数据库加载数据到 `OrderItem` 对象。

## 3. 接口说明

`OrderItemMapper` 接口本身没有定义任何自定义方法，其所有功能都继承自 `BaseMapper<OrderItem>`。以下是 `BaseMapper` 提供的一些常用方法：

### 3.1 常用继承方法

#### insert - 插入一条记录
*   **方法签名**: `int insert(OrderItem entity)`
*   **描述**: 插入一条 `OrderItem` 记录。在插入成功后，实体对象的ID会被自动填充（如果配置了自增长ID）。
*   **参数**:
    *   `entity` (OrderItem): 待插入的订单项实体对象。
*   **返回值**: `int` - 影响的行数。

#### selectById - 根据ID查询
*   **方法签名**: `OrderItem selectById(Serializable id)`
*   **描述**: 根据主键ID查询一条 `OrderItem` 记录。
*   **参数**:
    *   `id` (Serializable): 订单项的主键ID。
*   **返回值**: `OrderItem` - 匹配的订单项实体，如果不存在则返回 `null`。

#### selectList - 查询列表
*   **方法签名**: `List<OrderItem> selectList(@Param("ew") Wrapper<OrderItem> queryWrapper)`
*   **描述**: 根据条件查询 `OrderItem` 列表。`queryWrapper` 可以用于构建复杂的查询条件，如 `eq`, `like`, `in` 等。
*   **参数**:
    *   `queryWrapper` (Wrapper<OrderItem>): 查询条件构造器。
*   **返回值**: `List<OrderItem>` - 匹配的订单项实体列表。

#### updateById - 根据ID更新
*   **方法签名**: `int updateById(OrderItem entity)`
*   **描述**: 根据主键ID更新 `OrderItem` 记录。只会更新实体中非空的字段。
*   **参数**:
    *   `entity` (OrderItem): 包含待更新字段和主键ID的订单项实体对象。
*   **返回值**: `int` - 影响的行数。

#### deleteById - 根据ID删除
*   **方法签名**: `int deleteById(Serializable id)`
*   **描述**: 根据主键ID删除一条 `OrderItem` 记录。
*   **参数**:
    *   `id` (Serializable): 订单项的主键ID。
*   **返回值**: `int` - 影响的行数。

## 4. 业务规则

*   **关联性**: `OrderItem` 实体通常会与 `UnifiedOrder` 实体通过 `orderId` 字段建立关联。在业务逻辑中，订单项的生命周期应与所属订单的生命周期保持一致。
*   **数量与价格**: 订单项的数量（`quantity`）和单价（`unitPrice`）必须是正数。在服务层进行数据插入或更新前，应进行相应的业务校验。
*   **不可变性**: 一旦订单被确认，其订单项通常被视为不可变。任何修改（如数量变更）都应通过创建新的订单项或记录变更日志的方式进行，而不是直接修改已存在的订单项。

## 5. 使用示例

```java
// 1. 在 OrderService 实现中插入新的订单项
@Service
public class OrderServiceImpl implements OrderService {
    @Autowired
    private OrderItemMapper orderItemMapper;

    @Transactional
    public Long createOrder(CreateOrderRequest request) {
        // ... 创建 UnifiedOrder 实体 ...
        unifiedOrderMapper.insert(order);

        // 遍历请求中的订单项，并插入
        for (OrderItemDTO itemDTO : request.getOrderItems()) {
            OrderItem orderItem = new OrderItem();
            orderItem.setOrderId(order.getId());
            orderItem.setProductId(itemDTO.getProductId());
            orderItem.setQuantity(itemDTO.getQuantity());
            orderItem.setUnitPrice(itemDTO.getUnitPrice());
            orderItem.setTotalPrice(itemDTO.getQuantity().multiply(itemDTO.getUnitPrice()));
            orderItem.setCreatedAt(LocalDateTime.now());
            orderItemMapper.insert(orderItem);
        }
        return order.getId();
    }
}

// 2. 在 OrderService 实现中查询订单的所有订单项
@Service
public class OrderServiceImpl implements OrderService {
    @Autowired
    private OrderItemMapper orderItemMapper;

    public List<OrderItem> getOrderItemsByOrderId(Long orderId) {
        QueryWrapper<OrderItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id", orderId);
        return orderItemMapper.selectList(queryWrapper);
    }
}

// 3. 在 OrderService 实现中更新订单项数量 (示例，实际业务中可能不允许直接修改)
@Service
public class OrderServiceImpl implements OrderService {
    @Autowired
    private OrderItemMapper orderItemMapper;

    @Transactional
    public void updateOrderItemQuantity(Long orderItemId, Integer newQuantity) {
        OrderItem orderItem = orderItemMapper.selectById(orderItemId);
        if (orderItem != null) {
            orderItem.setQuantity(newQuantity);
            orderItem.setTotalPrice(orderItem.getUnitPrice().multiply(new BigDecimal(newQuantity)));
            orderItem.setUpdatedAt(LocalDateTime.now());
            orderItemMapper.updateById(orderItem);
        }
    }
}

// 4. 测试示例
@SpringBootTest
class OrderItemMapperTest {
    @Autowired
    private OrderItemMapper orderItemMapper;

    @Test
    @Transactional
    void testInsertAndSelect() {
        OrderItem orderItem = new OrderItem();
        orderItem.setOrderId(1L);
        orderItem.setProductId(101L);
        orderItem.setQuantity(2);
        orderItem.setUnitPrice(new BigDecimal("100.00"));
        orderItem.setTotalPrice(new BigDecimal("200.00"));
        orderItem.setCreatedAt(LocalDateTime.now());

        int result = orderItemMapper.insert(orderItem);
        assertThat(result).isEqualTo(1);
        assertThat(orderItem.getId()).isNotNull();

        OrderItem retrievedItem = orderItemMapper.selectById(orderItem.getId());
        assertThat(retrievedItem).isNotNull();
        assertThat(retrievedItem.getProductId()).isEqualTo(101L);
    }

    @Test
    @Transactional
    void testDelete() {
        // 假设已有一个订单项存在
        OrderItem orderItem = new OrderItem();
        orderItem.setOrderId(2L);
        orderItem.setProductId(102L);
        orderItem.setQuantity(1);
        orderItem.setUnitPrice(new BigDecimal("50.00"));
        orderItem.setTotalPrice(new BigDecimal("50.00"));
        orderItem.setCreatedAt(LocalDateTime.now());
        orderItemMapper.insert(orderItem);

        int result = orderItemMapper.deleteById(orderItem.getId());
        assertThat(result).isEqualTo(1);
        assertThat(orderItemMapper.selectById(orderItem.getId())).isNull();
    }
}
```

## 6. 注意事项

*   **MyBatis-Plus集成**: `OrderItemMapper` 继承 `BaseMapper`，这意味着它自动集成了MyBatis-Plus提供的所有便利功能，包括CRUD、分页、条件构造器等。
*   **事务管理**: 在服务层调用Mapper方法时，必须确保在事务中执行，以保证数据操作的原子性和一致性。
*   **SQL注入防护**: MyBatis-Plus的 `Wrapper` 机制和参数绑定机制可以有效防止SQL注入攻击。
*   **性能优化**: 对于大量订单项的查询，应注意SQL查询的性能，确保数据库表有合适的索引（如 `order_id` 字段）。
*   **逻辑删除**: 如果业务需要，可以在 `OrderItem` 实体中添加一个 `deleted` 字段，并配置MyBatis-Plus的逻辑删除功能，而不是物理删除数据。
*   **数据类型映射**: 确保Java实体中的数据类型与数据库表中的字段类型正确映射，特别是 `BigDecimal` 用于金额计算，以避免精度问题。
*   **可扩展性**: 如果未来需要更复杂的订单项查询或操作，可以在 `OrderItemMapper` 接口中定义自定义的Mapper方法，并编写对应的XML或注解SQL。
*   **日志记录**: 在服务层或Mapper层配置SQL日志，可以方便地查看实际执行的SQL语句，便于调试和性能调优。