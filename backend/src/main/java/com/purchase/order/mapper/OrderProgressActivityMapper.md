# OrderProgressActivityMapper.md

## 1. 文件概述

`OrderProgressActivityMapper.java` 是订单模块中的一个MyBatis-Plus Mapper接口，位于 `com.purchase.order.mapper` 包中。它继承自MyBatis-Plus的 `BaseMapper<OrderProgressActivity>` 接口，为 `OrderProgressActivity` 实体提供了基本的数据库操作能力。`OrderProgressActivity` 实体用于记录订单在处理过程中的每一个关键活动或状态变更，例如订单创建、支付完成、发货、签收等。该Mapper接口是订单服务层与数据库进行交互的桥梁，负责将业务对象的操作转换为SQL语句，实现订单活动日志的持久化管理。

## 2. 核心功能

*   **基础CRUD操作**: 继承 `BaseMapper`，自动拥有对 `OrderProgressActivity` 实体进行插入（`insert`）、根据ID查询（`selectById`）、根据条件查询列表（`selectList`）、更新（`updateById`）和删除（`deleteById`）等基础的增删改查功能。
*   **简化开发**: 通过MyBatis-Plus的自动化特性，无需编写SQL语句即可完成常见的订单活动数据库操作，极大地提高了开发效率。
*   **类型安全**: 针对 `OrderProgressActivity` 实体进行操作，提供了编译时类型检查，减少了运行时错误。
*   **数据持久化**: 负责将 `OrderProgressActivity` 领域对象的状态同步到数据库，或从数据库加载数据到 `OrderProgressActivity` 对象。

## 3. 接口说明

`OrderProgressActivityMapper` 接口本身没有定义任何自定义方法，其所有功能都继承自 `BaseMapper<OrderProgressActivity>`。以下是 `BaseMapper` 提供的一些常用方法：

### 3.1 常用继承方法

#### insert - 插入一条记录
*   **方法签名**: `int insert(OrderProgressActivity entity)`
*   **描述**: 插入一条 `OrderProgressActivity` 记录。在插入成功后，实体对象的ID会被自动填充（如果配置了自增长ID）。
*   **参数**:
    *   `entity` (OrderProgressActivity): 待插入的订单进度活动实体对象。
*   **返回值**: `int` - 影响的行数。

#### selectById - 根据ID查询
*   **方法签名**: `OrderProgressActivity selectById(Serializable id)`
*   **描述**: 根据主键ID查询一条 `OrderProgressActivity` 记录。
*   **参数**:
    *   `id` (Serializable): 订单进度活动的主键ID。
*   **返回值**: `OrderProgressActivity` - 匹配的订单进度活动实体，如果不存在则返回 `null`。

#### selectList - 查询列表
*   **方法签名**: `List<OrderProgressActivity> selectList(@Param("ew") Wrapper<OrderProgressActivity> queryWrapper)`
*   **描述**: 根据条件查询 `OrderProgressActivity` 列表。`queryWrapper` 可以用于构建复杂的查询条件，如 `eq`, `like`, `in` 等。
*   **参数**:
    *   `queryWrapper` (Wrapper<OrderProgressActivity>): 查询条件构造器。
*   **返回值**: `List<OrderProgressActivity>` - 匹配的订单进度活动实体列表。

#### updateById - 根据ID更新
*   **方法签名**: `int updateById(OrderProgressActivity entity)`
*   **描述**: 根据主键ID更新 `OrderProgressActivity` 记录。只会更新实体中非空的字段。
*   **参数**:
    *   `entity` (OrderProgressActivity): 包含待更新字段和主键ID的订单进度活动实体对象。
*   **返回值**: `int` - 影响的行数。

#### deleteById - 根据ID删除
*   **方法签名**: `int deleteById(Serializable id)`
*   **描述**: 根据主键ID删除一条 `OrderProgressActivity` 记录。
*   **参数**:
    *   `id` (Serializable): 订单进度活动的主键ID。
*   **返回值**: `int` - 影响的行数。

## 4. 业务规则

*   **关联性**: `OrderProgressActivity` 实体通常会与 `UnifiedOrder` 实体通过 `orderId` 字段建立关联。每个活动都应明确指向一个订单。
*   **时间顺序**: 订单活动应严格按照时间顺序记录，`activityTime` 字段是关键。在查询时，通常会按此字段升序排列以展示时间线。
*   **不可变性**: 一旦订单活动被记录，通常不应被修改或删除，以保证订单历史的完整性和可追溯性。如果需要修正，应记录新的活动来覆盖或解释之前的错误。

## 5. 使用示例

```java
// 1. 在 OrderService 实现中记录订单活动
@Service
public class OrderServiceImpl implements OrderService {
    @Autowired
    private OrderProgressActivityMapper activityMapper;

    @Transactional
    public void createOrder(CreateOrderRequest request) {
        // ... 创建订单 ...
        UnifiedOrder order = new UnifiedOrder();
        // ... 设置订单属性 ...
        unifiedOrderMapper.insert(order);

        // 记录订单创建活动
        OrderProgressActivity activity = new OrderProgressActivity();
        activity.setOrderId(order.getId());
        activity.setActivityType("ORDER_CREATED");
        activity.setDescription("订单已创建");
        activity.setActivityTime(LocalDateTime.now());
        activity.setOperatorId(request.getCreatorId());
        activity.setOperatorRole(request.getCreatorRole());
        activityMapper.insert(activity);
    }

    @Transactional
    public void markOrderPaid(Long orderId, Long payerId) {
        // ... 更新订单状态为已支付 ...

        // 记录支付完成活动
        OrderProgressActivity activity = new OrderProgressActivity();
        activity.setOrderId(orderId);
        activity.setActivityType("PAYMENT_COMPLETED");
        activity.setDescription("订单支付已完成");
        activity.setActivityTime(LocalDateTime.now());
        activity.setOperatorId(payerId);
        activity.setOperatorRole("BUYER"); // 假设支付者是买家
        activityMapper.insert(activity);
    }
}

// 2. 在 OrderService 实现中查询订单的活动历史
@Service
public class OrderServiceImpl implements OrderService {
    @Autowired
    private OrderProgressActivityMapper activityMapper;

    public List<OrderProgressActivity> getOrderActivityHistory(Long orderId) {
        QueryWrapper<OrderProgressActivity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id", orderId);
        queryWrapper.orderByAsc("activity_time"); // 按时间升序排列
        return activityMapper.selectList(queryWrapper);
    }
}

// 3. 测试示例
@SpringBootTest
class OrderProgressActivityMapperTest {
    @Autowired
    private OrderProgressActivityMapper activityMapper;

    @Test
    @Transactional
    void testInsertAndSelect() {
        OrderProgressActivity activity = new OrderProgressActivity();
        activity.setOrderId(1L);
        activity.setActivityType("ORDER_CREATED");
        activity.setDescription("订单已创建");
        activity.setActivityTime(LocalDateTime.now());
        activity.setOperatorId(100L);
        activity.setOperatorRole("BUYER");

        int result = activityMapper.insert(activity);
        assertThat(result).isEqualTo(1);
        assertThat(activity.getId()).isNotNull();

        OrderProgressActivity retrievedActivity = activityMapper.selectById(activity.getId());
        assertThat(retrievedActivity).isNotNull();
        assertThat(retrievedActivity.getOrderId()).isEqualTo(1L);
        assertThat(retrievedActivity.getActivityType()).isEqualTo("ORDER_CREATED");
    }

    @Test
    @Transactional
    void testSelectListByOrderId() {
        // 插入多条活动记录
        OrderProgressActivity activity1 = new OrderProgressActivity();
        activity1.setOrderId(2L); activity1.setActivityType("A"); activity1.setActivityTime(LocalDateTime.now().minusHours(2));
        activityMapper.insert(activity1);

        OrderProgressActivity activity2 = new OrderProgressActivity();
        activity2.setOrderId(2L); activity2.setActivityType("B"); activity2.setActivityTime(LocalDateTime.now().minusHours(1));
        activityMapper.insert(activity2);

        QueryWrapper<OrderProgressActivity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id", 2L);
        queryWrapper.orderByAsc("activity_time");

        List<OrderProgressActivity> activities = activityMapper.selectList(queryWrapper);
        assertThat(activities).hasSize(2);
        assertThat(activities.get(0).getActivityType()).isEqualTo("A");
        assertThat(activities.get(1).getActivityType()).isEqualTo("B");
    }
}
```

## 6. 注意事项

*   **MyBatis-Plus集成**: `OrderProgressActivityMapper` 继承 `BaseMapper`，这意味着它自动集成了MyBatis-Plus提供的所有便利功能，包括CRUD、分页、条件构造器等。
*   **事务管理**: 在服务层调用Mapper方法时，必须确保在事务中执行，以保证数据操作的原子性和一致性。
*   **SQL注入防护**: MyBatis-Plus的 `Wrapper` 机制和参数绑定机制可以有效防止SQL注入攻击。
*   **性能优化**: 对于大量订单活动记录的查询，应注意SQL查询的性能，确保数据库表有合适的索引（如 `order_id` 和 `activity_time` 字段）。
*   **日志与审计**: `OrderProgressActivity` 实体本身就是一种业务日志。它的记录对于订单的追溯、问题排查和合规性审计至关重要。
*   **数据类型映射**: 确保Java实体中的数据类型与数据库表中的字段类型正确映射。
*   **可扩展性**: 如果未来需要记录更详细的活动信息（如活动参数、操作IP等），可以在 `OrderProgressActivity` 实体中添加相应的字段。
*   **日志记录**: 在服务层或Mapper层配置SQL日志，可以方便地查看实际执行的SQL语句，便于调试和性能调优。