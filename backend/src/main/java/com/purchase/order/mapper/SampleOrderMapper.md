# SampleOrderMapper.java

## 文件概述 (File Overview)
`SampleOrderMapper.java` 是样品订单的MyBatis Mapper接口，位于 `com.purchase.order.mapper` 包中。该接口继承了MyBatis-Plus的 `BaseMapper<SampleOrder>`，提供了样品订单的基础CRUD操作，同时扩展了特定的业务查询方法。作为数据访问层的核心组件，它负责样品订单相关的所有数据库操作，包括分页查询、条件查询和订单编号生成等功能。

## 核心功能 (Core Functionality)
*   **基础CRUD操作**: 继承BaseMapper提供的insert、delete、update、select等基础操作
*   **分页查询**: 支持管理员和买家的样品订单分页查询
*   **条件查询**: 根据需求ID、买家ID、订单状态等条件查询订单
*   **订单编号生成**: 提供样品订单编号的自动生成功能
*   **多角色支持**: 区分管理员和买家的不同查询需求
*   **MyBatis-Plus集成**: 充分利用MyBatis-Plus的自动化功能和性能优化

## 接口说明 (Interface Description)

### 继承方法 (Inherited Methods)
继承自 `BaseMapper<SampleOrder>` 的基础方法：
*   `insert(SampleOrder entity)`: 插入样品订单记录
*   `deleteById(Serializable id)`: 根据ID删除订单
*   `updateById(SampleOrder entity)`: 根据ID更新订单
*   `selectById(Serializable id)`: 根据ID查询订单
*   `selectList(Wrapper<SampleOrder> queryWrapper)`: 条件查询订单列表

### 自定义方法 (Custom Methods)

#### selectAdminSampleOrderPage
*   **参数**: 
    *   `page` (Page<SampleOrder>) - MyBatis-Plus分页参数对象
    *   `status` (String) - 订单状态筛选条件
*   **返回值**: `IPage<SampleOrder>` - 分页结果对象
*   **功能**: 管理员分页查询样品订单列表
*   **业务逻辑**: 
    *   支持按订单状态筛选（如：PENDING、CONFIRMED、SHIPPED、COMPLETED等）
    *   返回所有买家的样品订单，不限制买家范围
    *   支持排序和分页功能

#### selectBuyerSampleOrderPage
*   **参数**: 
    *   `page` (Page<SampleOrder>) - MyBatis-Plus分页参数对象
    *   `buyerId` (Long) - 买家用户ID
*   **返回值**: `IPage<SampleOrder>` - 分页结果对象
*   **功能**: 买家分页查询自己的样品订单列表
*   **业务逻辑**: 
    *   只返回指定买家的样品订单
    *   支持按创建时间倒序排列
    *   包含订单的完整信息和状态

#### selectByRequirementId
*   **参数**: `requirementId` (Long) - 采购需求ID
*   **返回值**: `SampleOrder` - 样品订单对象，如果不存在则返回null
*   **功能**: 根据需求ID查询对应的样品订单
*   **业务逻辑**: 
    *   一个采购需求通常对应一个样品订单
    *   用于检查需求是否已经创建了样品订单

#### generateOrderNumber
*   **参数**: 无
*   **返回值**: `String` - 生成的订单编号
*   **功能**: 生成唯一的样品订单编号
*   **业务逻辑**: 
    *   通常格式为：SO + 日期 + 序列号（如：SO20250127001）
    *   确保订单编号的唯一性和可读性

## 使用示例 (Usage Examples)

```java
// 1. 在Service层中使用Mapper
@Service
public class SampleOrderServiceImpl implements SampleOrderService {
    
    @Autowired
    private SampleOrderMapper sampleOrderMapper;
    
    // 创建样品订单
    @Override
    @Transactional
    public SampleOrderResponse createOrder(SampleOrderCreateRequest request) {
        // 检查是否已存在订单
        SampleOrder existingOrder = sampleOrderMapper.selectByRequirementId(request.getRequirementId());
        if (existingOrder != null) {
            throw new BusinessException("该需求已存在样品订单");
        }
        
        // 生成订单编号
        String orderNumber = sampleOrderMapper.generateOrderNumber();
        
        // 创建订单对象
        SampleOrder order = new SampleOrder();
        order.setOrderNumber(orderNumber);
        order.setRequirementId(request.getRequirementId());
        order.setBuyerId(getCurrentUserId());
        order.setBuyerContact(request.getBuyerContact());
        order.setStatus("PENDING");
        
        // 插入订单
        sampleOrderMapper.insert(order);
        
        return convertToResponse(order);
    }
    
    // 管理员分页查询
    @Override
    public PageResult<SampleOrderResponse> getAdminOrderPage(int pageNum, int pageSize, String status) {
        Page<SampleOrder> page = new Page<>(pageNum, pageSize);
        IPage<SampleOrder> result = sampleOrderMapper.selectAdminSampleOrderPage(page, status);
        
        List<SampleOrderResponse> responses = result.getRecords().stream()
            .map(this::convertToResponse)
            .collect(Collectors.toList());
            
        return new PageResult<>(responses, result.getTotal(), result.getCurrent(), result.getSize());
    }
    
    // 买家分页查询
    @Override
    public PageResult<SampleOrderResponse> getBuyerOrderPage(int pageNum, int pageSize) {
        Long buyerId = getCurrentUserId();
        Page<SampleOrder> page = new Page<>(pageNum, pageSize);
        IPage<SampleOrder> result = sampleOrderMapper.selectBuyerSampleOrderPage(page, buyerId);
        
        List<SampleOrderResponse> responses = result.getRecords().stream()
            .map(this::convertToResponse)
            .collect(Collectors.toList());
            
        return new PageResult<>(responses, result.getTotal(), result.getCurrent(), result.getSize());
    }
}

// 2. 基础CRUD操作示例
// 根据ID查询订单
SampleOrder order = sampleOrderMapper.selectById(orderId);

// 更新订单状态
SampleOrder updateOrder = new SampleOrder();
updateOrder.setId(orderId);
updateOrder.setStatus("CONFIRMED");
updateOrder.setUpdatedAt(LocalDateTime.now());
sampleOrderMapper.updateById(updateOrder);

// 条件查询示例
QueryWrapper<SampleOrder> queryWrapper = new QueryWrapper<>();
queryWrapper.eq("buyer_id", buyerId)
           .eq("status", "PENDING")
           .orderByDesc("created_at");
List<SampleOrder> orders = sampleOrderMapper.selectList(queryWrapper);

// 3. 对应的XML映射文件示例
/*
<mapper namespace="com.purchase.order.mapper.SampleOrderMapper">
    
    <select id="selectAdminSampleOrderPage" resultType="com.purchase.order.entity.SampleOrder">
        SELECT * FROM sample_order 
        <where>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>
    
    <select id="selectBuyerSampleOrderPage" resultType="com.purchase.order.entity.SampleOrder">
        SELECT * FROM sample_order 
        WHERE buyer_id = #{buyerId}
        ORDER BY created_at DESC
    </select>
    
    <select id="selectByRequirementId" resultType="com.purchase.order.entity.SampleOrder">
        SELECT * FROM sample_order WHERE requirement_id = #{requirementId}
    </select>
    
    <select id="generateOrderNumber" resultType="java.lang.String">
        SELECT CONCAT('SO', DATE_FORMAT(NOW(), '%Y%m%d'), 
               LPAD(IFNULL(MAX(SUBSTRING(order_number, 11)), 0) + 1, 3, '0'))
        FROM sample_order 
        WHERE DATE(created_at) = CURDATE()
    </select>
    
</mapper>
*/
```

## 注意事项 (Notes)
*   **事务管理**: 所有的增删改操作都应该在事务环境中执行，确保数据一致性
*   **分页性能**: 大数据量分页查询时，建议在相关字段上建立索引（如buyer_id、status、created_at）
*   **订单编号唯一性**: generateOrderNumber方法需要考虑并发情况下的唯一性保证
*   **软删除**: 如果使用逻辑删除，需要在查询时注意过滤已删除的记录
*   **权限控制**: 买家只能查询自己的订单，管理员可以查询所有订单
*   **状态管理**: 订单状态的变更需要遵循业务流程，不能随意跳转
*   **XML映射文件**: 自定义方法需要在对应的XML文件中编写SQL语句
*   **参数验证**: 在Service层调用Mapper方法前，应该验证参数的有效性
*   **异常处理**: 数据库操作可能抛出异常，需要在上层进行适当的异常处理
*   **缓存策略**: 对于频繁查询的数据，可以考虑使用MyBatis的二级缓存或Redis缓存
