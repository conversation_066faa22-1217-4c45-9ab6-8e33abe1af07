# UnifiedOrderController.java

## 文件概述 (File Overview)
`UnifiedOrderController.java` 是统一订单管理的REST API控制器，位于 `com.purchase.order.controller` 包中。该控制器主要负责统一订单（UnifiedOrder）的核心操作，包括获取关联的货代订单信息和买家确认收货功能。通过集成货代服务，实现了采购订单与货代订单的关联查询，支持买家、卖家和管理员三种角色的不同权限访问。

## 核心功能 (Core Functionality)
*   **货代订单关联**: 获取采购订单关联的货代订单信息
*   **确认收货**: 买家确认收到订单货物，完成订单流程
*   **权限控制**: 基于角色和业务权限的细粒度访问控制
*   **跨服务集成**: 集成货代服务（ForwarderOrderService）实现跨模块数据查询
*   **安全认证**: 通过SecurityContextUtil获取当前用户信息
*   **异常处理**: 完善的异常处理和错误信息返回

## 接口说明 (Interface Description)

### API端点映射
**基础路径**: `/api/v1/orders`

### 主要接口方法

#### getForwarderOrder - 获取采购订单关联的货代订单
*   **HTTP方法**: GET
*   **路径**: `/api/v1/orders/{orderId}/forwarder-order`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")`
*   **参数**: `orderId` (Long) - 采购订单ID（路径参数）
*   **返回值**: `Result<ForwarderOrderVO>` - 关联的货代订单信息
*   **业务逻辑**: 
    *   验证用户权限：管理员可以查看所有订单，买家只能查看自己的订单，卖家只能查看自己参与的订单
    *   通过ForwarderOrderService查询关联的货代订单
    *   返回货代订单的详细信息，包括物流状态、运输信息等

#### confirmReceipt - 买家确认收货
*   **HTTP方法**: POST
*   **路径**: `/api/v1/orders/{orderId}/receipt`
*   **权限**: `@PreAuthorize("hasAuthority('buyer')")`
*   **参数**: 
    *   `orderId` (Long) - 订单ID（路径参数）
    *   `request` (ConfirmReceiptRequest) - 确认收货请求体（可选）
*   **返回值**: `Result<UnifiedOrder>` - 更新后的订单信息
*   **业务逻辑**: 
    *   验证买家身份和订单所有权
    *   更新订单状态为已完成
    *   记录确认收货的时间和备注信息
    *   触发订单完成相关的业务流程

### 内部类说明

#### ConfirmReceiptRequest - 确认收货请求DTO
*   **字段**: 
    *   `remark` (String) - 确认收货备注，可选字段
*   **功能**: 封装买家确认收货时的备注信息

## 使用示例 (Usage Examples)

```java
// 1. 前端JavaScript调用示例

// 获取采购订单关联的货代订单
const getForwarderOrder = async (orderId) => {
    const response = await fetch(`/api/v1/orders/${orderId}/forwarder-order`, {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
        }
    });
    
    const result = await response.json();
    if (result.success) {
        console.log('货代订单信息:', result.data);
        return result.data;
    } else {
        console.error('获取货代订单失败:', result.message);
        return null;
    }
};

// 买家确认收货
const confirmReceipt = async (orderId, remark = null) => {
    const requestBody = remark ? { remark } : {};
    
    const response = await fetch(`/api/v1/orders/${orderId}/receipt`, {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + buyerToken,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
    });
    
    const result = await response.json();
    if (result.success) {
        console.log('确认收货成功:', result.data);
        return result.data;
    } else {
        console.error('确认收货失败:', result.message);
        throw new Error(result.message);
    }
};

// 2. 服务层集成示例
@Service
public class OrderTrackingService {
    
    @Autowired
    private UnifiedOrderService unifiedOrderService;
    
    @Autowired
    private ForwarderOrderService forwarderOrderService;
    
    // 获取订单完整物流信息
    public OrderTrackingInfo getOrderTrackingInfo(Long orderId, Long userId, String userRole) {
        // 验证权限
        if (!"admin".equals(userRole)) {
            UnifiedOrder order = unifiedOrderService.getOrderById(orderId);
            if (!hasPermissionToViewOrder(order, userId, userRole)) {
                throw new IllegalArgumentException("无权访问此订单");
            }
        }
        
        // 获取采购订单信息
        UnifiedOrder order = unifiedOrderService.getOrderById(orderId);
        
        // 获取关联的货代订单
        ForwarderOrderVO forwarderOrder = forwarderOrderService.getOrderByPurchaseOrderId(orderId);
        
        // 组装完整的物流跟踪信息
        OrderTrackingInfo trackingInfo = new OrderTrackingInfo();
        trackingInfo.setOrder(order);
        trackingInfo.setForwarderOrder(forwarderOrder);
        trackingInfo.setTrackingHistory(getTrackingHistory(orderId));
        
        return trackingInfo;
    }
    
    // 自动确认收货（超时自动确认）
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void autoConfirmReceipt() {
        List<UnifiedOrder> expiredOrders = unifiedOrderService.getExpiredDeliveredOrders();
        
        for (UnifiedOrder order : expiredOrders) {
            try {
                unifiedOrderService.confirmReceipt(order.getId(), order.getBuyerId(), "系统自动确认收货");
                log.info("自动确认收货成功，订单ID: {}", order.getId());
            } catch (Exception e) {
                log.error("自动确认收货失败，订单ID: {}, 错误: {}", order.getId(), e.getMessage());
            }
        }
    }
}

// 3. 权限验证示例
// 买家查看自己的订单货代信息
GET /api/v1/orders/123/forwarder-order
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 管理员查看任意订单货代信息
GET /api/v1/orders/456/forwarder-order
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 买家确认收货（带备注）
POST /api/v1/orders/123/receipt
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
    "remark": "货物完好无损，质量满意"
}

// 买家确认收货（无备注）
POST /api/v1/orders/123/receipt
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{}

// 4. 异常处理示例
@ControllerAdvice
public class UnifiedOrderExceptionHandler {
    
    @ExceptionHandler(IllegalArgumentException.class)
    public Result<Object> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("参数错误: {}", e.getMessage());
        return Result.error("参数错误: " + e.getMessage());
    }
    
    @ExceptionHandler(OrderNotFoundException.class)
    public Result<Object> handleOrderNotFoundException(OrderNotFoundException e) {
        log.error("订单不存在: {}", e.getMessage());
        return Result.error("订单不存在");
    }
    
    @ExceptionHandler(PermissionDeniedException.class)
    public Result<Object> handlePermissionDeniedException(PermissionDeniedException e) {
        log.error("权限不足: {}", e.getMessage());
        return Result.error("无权访问此资源");
    }
}
```

## 注意事项 (Notes)
*   **权限控制**: 严格的多层权限验证，包括角色权限和业务权限，确保用户只能访问自己有权限的订单
*   **跨服务调用**: 集成了货代服务，需要确保服务间的网络连通性和数据一致性
*   **业务权限验证**: 买家只能查看和操作自己的订单，卖家只能查看参与的订单，管理员拥有全部权限
*   **异常处理**: 完善的异常处理机制，包括权限异常、业务异常和系统异常
*   **日志记录**: 重要操作都有详细的日志记录，便于问题排查和审计
*   **事务处理**: 确认收货操作可能涉及多个表的更新，需要在事务环境中执行
*   **状态管理**: 订单状态的变更需要遵循业务流程，确认收货只能在特定状态下执行
*   **安全考虑**: 使用SecurityContextUtil安全地获取当前用户信息，避免用户信息伪造
*   **数据一致性**: 跨服务数据查询时需要考虑数据一致性问题
*   **性能优化**: 货代订单查询可能涉及复杂的关联查询，需要考虑性能优化
*   **API设计**: 遵循RESTful设计原则，URL路径清晰表达资源层次关系
*   **参数验证**: 使用@NotNull等验证注解确保关键参数的有效性
*   **版本控制**: 使用/api/v1前缀进行API版本控制，便于后续升级
*   **监控告警**: 重要操作失败时应该有相应的监控告警机制
