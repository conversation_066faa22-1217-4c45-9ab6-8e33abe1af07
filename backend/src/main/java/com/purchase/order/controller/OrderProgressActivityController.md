# OrderProgressActivityController.java

## 文件概述 (File Overview)
`OrderProgressActivityController.java` 是订单进度活动的REST API控制器，位于 `com.purchase.order.controller` 包中。该控制器负责管理订单执行过程中各个阶段的进度活动，包括订单款项、营业执照、生产过程、检验包装、物流信息和验收等六个主要阶段。通过细粒度的权限控制，确保不同角色（买家、卖家、管理员、货代）只能操作其负责的阶段，实现了订单全生命周期的协同管理。

## 核心功能 (Core Functionality)
*   **进度活动管理**: 提供订单进度活动的增删改查功能
*   **阶段权限控制**: 基于订单阶段和用户角色的细粒度权限验证
*   **多角色协同**: 支持买家、卖家、管理员、货代四种角色的协同操作
*   **分页查询**: 支持进度活动的分页查询和分组查询
*   **RESTful API**: 提供标准的REST风格API接口
*   **安全认证**: 集成Spring Security进行用户认证和授权

## 接口说明 (Interface Description)

### API端点映射
**基础路径**: `/api/v1/orders/{orderId}/progress/activities`

### 主要接口方法

#### createActivity - 创建订单进度活动
*   **HTTP方法**: POST
*   **路径**: `/api/v1/orders/{orderId}/progress/activities`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer','seller','admin','forwarder')")`
*   **参数**: 
    *   `orderId` (Long) - 订单ID（路径参数）
    *   `request` (CreateProgressActivityRequest) - 创建请求体
*   **返回值**: `Result<OrderProgressActivity>` - 创建的进度活动
*   **业务逻辑**: 
    *   验证用户是否有权限操作指定阶段
    *   获取当前用户ID并创建进度活动
    *   记录操作日志

#### getActivities - 获取订单所有进度活动
*   **HTTP方法**: GET
*   **路径**: `/api/v1/orders/{orderId}/progress/activities`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer','seller','admin','forwarder')")`
*   **参数**: `orderId` (Long) - 订单ID（路径参数）
*   **返回值**: `Result<List<ProgressActivityResponse>>` - 进度活动列表
*   **业务逻辑**: 返回指定订单的所有进度活动，按时间倒序排列

#### getActivitiesGrouped - 按节点分组获取订单进度活动
*   **HTTP方法**: GET
*   **路径**: `/api/v1/orders/{orderId}/progress/activities/grouped`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer','seller','admin','forwarder')")`
*   **参数**: `orderId` (Long) - 订单ID（路径参数）
*   **返回值**: `Result<Map<Integer, List<ProgressActivityResponse>>>` - 按阶段分组的活动
*   **业务逻辑**: 将进度活动按阶段值分组，便于前端展示

#### getActivitiesByStage - 获取特定节点的进度活动
*   **HTTP方法**: GET
*   **路径**: `/api/v1/orders/{orderId}/progress/activities/stage/{stageValue}`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer','seller','admin','forwarder')")`
*   **参数**: 
    *   `orderId` (Long) - 订单ID（路径参数）
    *   `stageValue` (Integer) - 阶段值（路径参数）
*   **返回值**: `Result<List<ProgressActivityResponse>>` - 特定阶段的活动列表
*   **业务逻辑**: 返回指定订单和阶段的所有进度活动

#### updateActivity - 更新进度活动
*   **HTTP方法**: PUT
*   **路径**: `/api/v1/orders/{orderId}/progress/activities/{activityId}`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer','seller','admin','forwarder')")`
*   **参数**: 
    *   `orderId` (Long) - 订单ID（路径参数）
    *   `activityId` (Long) - 活动ID（路径参数）
    *   `request` (CreateProgressActivityRequest) - 更新请求体
*   **返回值**: `Result<OrderProgressActivity>` - 更新后的进度活动
*   **业务逻辑**: 验证权限后更新指定的进度活动

#### deleteActivity - 删除进度活动
*   **HTTP方法**: DELETE
*   **路径**: `/api/v1/orders/{orderId}/progress/activities/{activityId}`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer','seller','admin','forwarder')")`
*   **参数**: 
    *   `orderId` (Long) - 订单ID（路径参数）
    *   `activityId` (Long) - 活动ID（路径参数）
*   **返回值**: `Result<Void>` - 删除结果
*   **业务逻辑**: 验证权限后删除指定的进度活动

### 阶段权限映射 (Stage Role Mapping)
```java
private static final Map<Integer, String> STAGE_ROLE_MAPPING = Map.of(
    0, "buyer",        // 订单款项 - 买家
    20, "seller",      // 营业执照和生产环境 - 卖家
    40, "seller",      // 生产过程 - 卖家
    60, "admin",       // 检验和包装 - 管理员
    80, "forwarder",   // 物流信息 - 货代
    100, "buyer"       // 验收 - 买家
);
```

## 使用示例 (Usage Examples)

```java
// 1. 前端JavaScript调用示例

// 创建进度活动
const createProgressActivity = async (orderId, activityData) => {
    const response = await fetch(`/api/v1/orders/${orderId}/progress/activities`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify({
            stageValue: 20,
            title: "营业执照已上传",
            description: "已上传营业执照和相关资质文件",
            attachments: ["license.pdf", "certificate.jpg"]
        })
    });
    return await response.json();
};

// 获取订单所有进度活动
const getOrderActivities = async (orderId) => {
    const response = await fetch(`/api/v1/orders/${orderId}/progress/activities`, {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token
        }
    });
    return await response.json();
};

// 按阶段分组获取活动
const getGroupedActivities = async (orderId) => {
    const response = await fetch(`/api/v1/orders/${orderId}/progress/activities/grouped`, {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token
        }
    });
    return await response.json();
};

// 2. 服务层集成示例
@Service
public class OrderProgressService {
    
    @Autowired
    private OrderProgressActivityService activityService;
    
    // 自动创建阶段完成活动
    public void completeStage(Long orderId, Integer stageValue, String description) {
        CreateProgressActivityRequest request = new CreateProgressActivityRequest();
        request.setStageValue(stageValue);
        request.setTitle(getStageTitle(stageValue) + "已完成");
        request.setDescription(description);
        request.setActivityType("STAGE_COMPLETE");
        
        Long currentUserId = getCurrentUserId();
        activityService.createActivity(orderId, request, currentUserId);
    }
    
    // 检查阶段是否完成
    public boolean isStageCompleted(Long orderId, Integer stageValue) {
        List<ProgressActivityResponse> activities = 
            activityService.getActivitiesByOrderIdAndStage(orderId, stageValue);
        
        return activities.stream()
            .anyMatch(activity -> "STAGE_COMPLETE".equals(activity.getActivityType()));
    }
}

// 3. 权限验证示例
// 买家创建订单款项活动（阶段0）
POST /api/v1/orders/123/progress/activities
{
    "stageValue": 0,
    "title": "订单款项已支付",
    "description": "已完成订单款项支付，等待卖家确认",
    "attachments": ["payment_proof.jpg"]
}

// 卖家创建生产过程活动（阶段40）
POST /api/v1/orders/123/progress/activities
{
    "stageValue": 40,
    "title": "生产进度更新",
    "description": "产品生产进度已完成60%，预计3天后完成",
    "attachments": ["production_photos.jpg"]
}

// 管理员创建检验活动（阶段60）
POST /api/v1/orders/123/progress/activities
{
    "stageValue": 60,
    "title": "质量检验完成",
    "description": "产品质量检验合格，已完成包装",
    "attachments": ["inspection_report.pdf"]
}
```

## 注意事项 (Notes)
*   **权限控制**: 严格的阶段权限控制，不同角色只能操作其负责的阶段，管理员拥有所有权限
*   **业务权限验证**: 除了角色权限外，还需验证业务权限（如买家只能操作自己的订单）
*   **阶段映射**: STAGE_ROLE_MAPPING定义了各阶段的负责角色，需要与前端保持一致
*   **JWT认证**: 通过JWT Token获取用户信息，需要确保Token的有效性和安全性
*   **事务处理**: 进度活动的创建和更新可能涉及订单状态变更，需要考虑事务一致性
*   **日志记录**: 重要操作都有日志记录，便于问题排查和审计
*   **异常处理**: 权限不足时抛出IllegalArgumentException，需要在全局异常处理器中处理
*   **参数验证**: 使用@Valid注解进行请求参数验证，确保数据完整性
*   **RESTful设计**: 遵循REST设计原则，URL路径清晰表达资源层次关系
*   **并发控制**: 多用户同时操作同一订单时，需要考虑并发控制和数据一致性
