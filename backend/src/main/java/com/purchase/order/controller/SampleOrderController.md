# SampleOrderController.java

## 文件概述 (File Overview)
`SampleOrderController.java` 是样品订单管理的REST API控制器，位于 `com.purchase.order.controller` 包中。该控制器提供了样品订单的完整生命周期管理功能，支持管理员和买家两种角色的不同操作权限。管理员可以查看所有订单、处理订单和发货，买家可以查看自己的订单、确认收货、取消订单和更新收货地址。通过Spring Security集成，确保了API的安全性和权限控制。

## 核心功能 (Core Functionality)
*   **管理员功能**: 订单列表查询、订单详情查看、订单处理、发货操作、统计查询
*   **买家功能**: 个人订单查询、订单详情查看、确认收货、取消订单、更新收货地址
*   **权限控制**: 基于Spring Security的细粒度权限控制
*   **RESTful API**: 提供标准的REST风格API接口
*   **Swagger集成**: 完整的API文档和接口测试支持
*   **日志记录**: 详细的操作日志记录，便于问题排查和审计

## 接口说明 (Interface Description)

### API端点映射
**基础路径**: `/api/v1/sample-orders`

### 管理员接口 (Admin APIs)

#### getAdminSampleOrders - 管理员获取样品订单列表
*   **HTTP方法**: GET
*   **路径**: `/api/v1/sample-orders/admin`
*   **权限**: `@PreAuthorize("hasAuthority('admin')")`
*   **参数**: 
    *   `page` (Integer) - 页码，默认值1
    *   `size` (Integer) - 每页大小，默认值10
    *   `status` (String) - 订单状态筛选，可选
    *   `buyerId` (Long) - 买家ID筛选，可选
    *   `orderNumber` (String) - 订单编号筛选，可选
*   **返回值**: `Result<IPage<SampleOrderResponse>>` - 分页的样品订单列表
*   **业务逻辑**: 管理员可以查看所有买家的样品订单，支持多条件筛选和分页

#### getAdminSampleOrderDetail - 管理员获取样品订单详情
*   **HTTP方法**: GET
*   **路径**: `/api/v1/sample-orders/admin/{orderId}`
*   **权限**: `@PreAuthorize("hasAuthority('admin')")`
*   **参数**: `orderId` (Long) - 订单ID（路径参数）
*   **返回值**: `Result<SampleOrderResponse>` - 样品订单详情
*   **业务逻辑**: 管理员查看指定订单的基本信息

#### getAdminSampleOrderFullDetail - 管理员获取样品订单完整详情
*   **HTTP方法**: GET
*   **路径**: `/api/v1/sample-orders/admin/{orderId}/detail`
*   **权限**: `@PreAuthorize("hasAuthority('admin')")`
*   **参数**: `orderId` (Long) - 订单ID（路径参数）
*   **返回值**: `Result<SampleOrderDetailVO>` - 包含买家、卖家、需求信息的完整订单详情
*   **业务逻辑**: 管理员查看订单的完整信息，包括关联的买家、卖家和需求详情

#### processSampleOrder - 管理员处理样品订单
*   **HTTP方法**: POST
*   **路径**: `/api/v1/sample-orders/admin/{orderId}/process`
*   **权限**: `@PreAuthorize("hasAuthority('admin')")`
*   **参数**: 
    *   `orderId` (Long) - 订单ID（路径参数）
    *   `request` (SampleOrderAdminProcessRequest) - 处理请求体
*   **返回值**: `Result<SampleOrderResponse>` - 处理后的订单信息
*   **业务逻辑**: 管理员审核和处理样品订单，可能包括审批、拒绝等操作

#### shipSampleOrder - 管理员发货
*   **HTTP方法**: POST
*   **路径**: `/api/v1/sample-orders/admin/{orderId}/ship`
*   **权限**: `@PreAuthorize("hasAuthority('admin')")`
*   **参数**: 
    *   `orderId` (Long) - 订单ID（路径参数）
    *   `request` (SampleOrderShipRequest) - 发货请求体
*   **返回值**: `Result<SampleOrderResponse>` - 发货后的订单信息
*   **业务逻辑**: 管理员执行发货操作，更新订单状态和物流信息

### 买家接口 (Buyer APIs)

#### getBuyerSampleOrders - 买家获取自己的样品订单列表
*   **HTTP方法**: GET
*   **路径**: `/api/v1/sample-orders/buyer`
*   **权限**: `@PreAuthorize("hasAuthority('buyer')")`
*   **参数**: 
    *   `page` (Integer) - 页码，默认值1
    *   `size` (Integer) - 每页大小，默认值10
    *   `status` (String) - 订单状态筛选，可选
*   **返回值**: `Result<IPage<SampleOrderResponse>>` - 买家的样品订单列表
*   **业务逻辑**: 买家只能查看自己的订单，通过JWT Token获取买家ID

#### getBuyerSampleOrderDetail - 买家获取样品订单详情
*   **HTTP方法**: GET
*   **路径**: `/api/v1/sample-orders/buyer/{orderId}`
*   **权限**: `@PreAuthorize("hasAuthority('buyer')")`
*   **参数**: `orderId` (Long) - 订单ID（路径参数）
*   **返回值**: `Result<SampleOrderResponse>` - 样品订单详情
*   **业务逻辑**: 买家查看自己订单的详情，包含权限验证

#### confirmReceipt - 买家确认收货
*   **HTTP方法**: POST
*   **路径**: `/api/v1/sample-orders/{orderId}/confirm-receipt`
*   **权限**: `@PreAuthorize("hasAuthority('buyer')")`
*   **参数**: `orderId` (Long) - 订单ID（路径参数）
*   **返回值**: `Result<SampleOrderResponse>` - 确认收货后的订单信息
*   **业务逻辑**: 买家确认收到样品，订单状态变更为已完成

#### cancelSampleOrder - 买家取消样品订单
*   **HTTP方法**: POST
*   **路径**: `/api/v1/sample-orders/{orderId}/cancel`
*   **权限**: `@PreAuthorize("hasAuthority('buyer')")`
*   **参数**: 
    *   `orderId` (Long) - 订单ID（路径参数）
    *   `reason` (String) - 取消原因，可选
*   **返回值**: `Result<SampleOrderResponse>` - 取消后的订单信息
*   **业务逻辑**: 买家取消样品订单，只能取消特定状态的订单

#### updateShippingAddress - 买家更新收货地址
*   **HTTP方法**: PUT
*   **路径**: `/api/v1/sample-orders/{orderId}/shipping-address`
*   **权限**: `@PreAuthorize("hasAuthority('buyer')")`
*   **参数**: 
    *   `orderId` (Long) - 订单ID（路径参数）
    *   `request` (UpdateShippingAddressRequest) - 地址更新请求体
*   **返回值**: `Result<SampleOrderResponse>` - 更新后的订单信息
*   **业务逻辑**: 买家更新收货地址，只能在发货前更新

### 通用接口 (Common APIs)

#### getSampleOrderByRequirementId - 根据需求ID获取样品订单
*   **HTTP方法**: GET
*   **路径**: `/api/v1/sample-orders/by-requirement/{requirementId}`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer', 'admin')")`
*   **参数**: `requirementId` (Long) - 需求ID（路径参数）
*   **返回值**: `Result<SampleOrderResponse>` - 对应的样品订单
*   **业务逻辑**: 根据采购需求ID查找对应的样品订单

#### canUploadPaymentProof - 检查是否可以上传支付凭证
*   **HTTP方法**: GET
*   **路径**: `/api/v1/sample-orders/payment/proof/can-upload/{sampleOrderId}`
*   **权限**: `@PreAuthorize("hasAuthority('buyer')")`
*   **参数**: `sampleOrderId` (Long) - 样品订单ID（路径参数）
*   **返回值**: `Result<Boolean>` - 是否可以上传支付凭证
*   **业务逻辑**: 检查订单状态是否允许上传支付凭证

## 使用示例 (Usage Examples)

```java
// 1. 前端JavaScript调用示例

// 管理员获取样品订单列表
const getAdminSampleOrders = async (page = 1, size = 10, status = null) => {
    const params = new URLSearchParams({
        page: page.toString(),
        size: size.toString()
    });
    if (status) params.append('status', status);

    const response = await fetch(`/api/v1/sample-orders/admin?${params}`, {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + adminToken,
            'Content-Type': 'application/json'
        }
    });
    return await response.json();
};

// 买家获取自己的订单列表
const getBuyerSampleOrders = async (page = 1, size = 10) => {
    const response = await fetch(`/api/v1/sample-orders/buyer?page=${page}&size=${size}`, {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + buyerToken,
            'Content-Type': 'application/json'
        }
    });
    return await response.json();
};

// 买家确认收货
const confirmReceipt = async (orderId) => {
    const response = await fetch(`/api/v1/sample-orders/${orderId}/confirm-receipt`, {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + buyerToken,
            'Content-Type': 'application/json'
        }
    });
    return await response.json();
};

// 管理员发货
const shipSampleOrder = async (orderId, shipData) => {
    const response = await fetch(`/api/v1/sample-orders/admin/${orderId}/ship`, {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + adminToken,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            trackingNumber: shipData.trackingNumber,
            shippingCompany: shipData.shippingCompany,
            shippingDate: shipData.shippingDate,
            estimatedDeliveryDate: shipData.estimatedDeliveryDate
        })
    });
    return await response.json();
};

// 2. 服务层集成示例
@Service
public class OrderManagementService {

    @Autowired
    private SampleOrderService sampleOrderService;

    // 批量处理订单
    public void batchProcessOrders(List<Long> orderIds, String action) {
        for (Long orderId : orderIds) {
            try {
                switch (action) {
                    case "APPROVE":
                        SampleOrderAdminProcessRequest approveRequest = new SampleOrderAdminProcessRequest();
                        approveRequest.setAction("APPROVE");
                        approveRequest.setRemark("批量审批通过");
                        sampleOrderService.adminProcessSampleOrder(orderId, approveRequest, getCurrentAdminId());
                        break;
                    case "SHIP":
                        SampleOrderShipRequest shipRequest = new SampleOrderShipRequest();
                        shipRequest.setTrackingNumber(generateTrackingNumber());
                        shipRequest.setShippingCompany("顺丰快递");
                        sampleOrderService.shipSampleOrder(orderId, shipRequest, getCurrentAdminId());
                        break;
                }
            } catch (Exception e) {
                log.error("批量处理订单失败，订单ID: {}, 操作: {}, 错误: {}", orderId, action, e.getMessage());
            }
        }
    }
}

// 3. 权限验证示例
// 买家只能查看自己的订单
@GetMapping("/buyer/{orderId}")
@PreAuthorize("hasAuthority('buyer')")
public Result<SampleOrderResponse> getBuyerSampleOrderDetail(@PathVariable Long orderId) {
    Long buyerId = getCurrentUserId(); // 从JWT Token获取买家ID

    // 服务层会验证订单是否属于该买家
    SampleOrderResponse order = sampleOrderService.getSampleOrderDetail(orderId, buyerId);
    return Result.success(order);
}

// 4. 异常处理示例
@ExceptionHandler(IllegalArgumentException.class)
public Result<Object> handleIllegalArgumentException(IllegalArgumentException e) {
    log.error("参数错误: {}", e.getMessage());
    return Result.error("参数错误: " + e.getMessage());
}

@ExceptionHandler(RuntimeException.class)
public Result<Object> handleRuntimeException(RuntimeException e) {
    log.error("运行时错误: {}", e.getMessage());
    return Result.error("操作失败: " + e.getMessage());
}
```

## 注意事项 (Notes)
*   **权限控制**: 严格的角色权限控制，管理员和买家有不同的操作权限，买家只能操作自己的订单
*   **JWT认证**: 通过JWT Token获取用户信息，需要确保Token的有效性和安全性
*   **业务权限验证**: 除了角色权限外，还需要验证业务权限（如买家只能查看自己的订单）
*   **参数验证**: 使用@Valid注解进行请求参数验证，确保数据完整性和有效性
*   **事务处理**: 订单状态变更操作需要在事务环境中执行，确保数据一致性
*   **日志记录**: 所有重要操作都有详细的日志记录，便于问题排查和审计
*   **异常处理**: 需要在全局异常处理器中处理各种业务异常和系统异常
*   **状态管理**: 订单状态的变更需要遵循业务流程，不能随意跳转状态
*   **并发控制**: 多用户同时操作同一订单时，需要考虑并发控制和数据一致性
*   **API版本控制**: 使用/api/v1前缀进行API版本控制，便于后续升级和维护
*   **Swagger文档**: 集成Swagger提供完整的API文档，便于前端开发和测试
*   **安全考虑**: 敏感操作（如发货、确认收货）需要额外的安全验证
*   **性能优化**: 分页查询时需要考虑性能优化，建议在相关字段上建立索引
*   **数据脱敏**: 返回给前端的数据需要进行适当的脱敏处理，保护用户隐私
