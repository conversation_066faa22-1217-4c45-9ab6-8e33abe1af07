# OrderController.java

## 文件概述 (File Overview)
`OrderController.java` 是订单管理的REST控制器，位于 `com.purchase.order.controller` 包中。该控制器作为订单管理模块的HTTP接口层，负责处理所有订单相关的REST API请求。通过集成 `UnifiedOrderService` 和 `OrderPdfService` 等业务服务，提供了订单的完整生命周期管理功能，包括创建、查询、更新、删除、状态管理、进度跟踪、PDF生成等。该控制器实现了基于Spring Security的多层权限控制，支持买家、卖家、管理员等不同角色的差异化访问，并提供了复杂的订单进度阶段权限映射机制。

## 核心功能 (Core Functionality)
*   **订单生命周期管理**: 提供订单从创建到完成的完整生命周期管理功能
*   **多维度查询服务**: 支持按ID查询、分页查询、条件搜索等多种查询方式
*   **权限分级控制**: 基于用户角色和订单状态的多层权限验证机制
*   **订单进度跟踪**: 实现6个标准进度阶段的跟踪和管理（0-100%）
*   **动态权限映射**: 根据订单进度阶段动态分配不同角色的操作权限
*   **文档生成服务**: 集成PDF生成服务，支持订单合同、发票等文档生成
*   **数据验证处理**: 使用Bean Validation进行请求参数的完整性验证
*   **统一响应格式**: 所有接口返回统一的Result<T>格式，便于前端处理
*   **异常处理机制**: 完善的异常捕获和错误信息返回机制
*   **日志记录功能**: 详细的操作日志记录，支持业务监控和问题排查

## 接口说明 (Interface Description)

### 订单管理方法

#### createOrder - 创建新订单
*   **方法签名**: `Result<OrderDetailResponse> createOrder(@Valid @RequestBody CreateOrderRequest request)`
*   **HTTP方法**: POST
*   **路径**: `/api/v1/orders`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")`
*   **参数**: `request` (CreateOrderRequest) - 创建订单的请求对象，包含买家ID、卖家ID、需求ID、订单项等信息
*   **返回值**: `Result<OrderDetailResponse>` - 包含新创建订单详细信息的响应对象
*   **业务逻辑**:
    *   验证请求参数的完整性和有效性
    *   检查买家和卖家的有效性
    *   验证关联需求的存在性和状态
    *   计算订单总金额和各项费用
    *   创建订单记录和订单项
    *   初始化订单状态和进度
    *   发送订单创建通知
    *   返回完整的订单详情

#### getOrderById - 获取订单详情
*   **方法签名**: `Result<OrderDetailResponse> getOrderById(@PathVariable Long id)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/orders/{id}`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")`
*   **参数**: `id` (Long) - 订单ID
*   **返回值**: `Result<OrderDetailResponse>` - 订单详细信息响应对象
*   **业务逻辑**:
    *   验证订单ID的有效性
    *   检查用户是否有权限查看该订单
    *   查询订单基本信息和关联数据
    *   获取订单项列表和详细信息
    *   查询订单进度和活动记录
    *   组装完整的订单详情响应

#### updateOrder - 更新订单信息
*   **方法签名**: `Result<OrderDetailResponse> updateOrder(@PathVariable Long id, @Valid @RequestBody UpdateOrderRequest request)`
*   **HTTP方法**: PUT
*   **路径**: `/api/v1/orders/{id}`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")`
*   **参数**:
    *   `id` (Long) - 订单ID
    *   `request` (UpdateOrderRequest) - 更新请求对象
*   **返回值**: `Result<OrderDetailResponse>` - 更新后的订单详情
*   **业务逻辑**:
    *   验证订单存在性和用户权限
    *   检查订单状态是否允许更新
    *   验证更新数据的有效性
    *   更新订单基本信息
    *   重新计算相关费用
    *   记录更新操作日志
    *   发送更新通知

#### deleteOrder - 删除订单
*   **方法签名**: `Result<String> deleteOrder(@PathVariable Long id)`
*   **HTTP方法**: DELETE
*   **路径**: `/api/v1/orders/{id}`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")`
*   **参数**: `id` (Long) - 订单ID
*   **返回值**: `Result<String>` - 删除操作结果消息
*   **业务逻辑**:
    *   验证订单存在性和删除权限
    *   检查订单状态是否允许删除
    *   执行逻辑删除操作
    *   清理相关的业务数据
    *   记录删除操作日志
    *   发送删除通知

### 查询方法

#### getAllOrders - 分页获取订单列表
*   **方法签名**: `Result<IPage<OrderSimpleResponse>> getAllOrders(@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "10") Integer size)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/orders`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")`
*   **参数**:
    *   `page` (Integer) - 页码，默认为1
    *   `size` (Integer) - 每页大小，默认为10
*   **返回值**: `Result<IPage<OrderSimpleResponse>>` - 分页的订单简要信息列表
*   **业务逻辑**:
    *   验证分页参数的有效性
    *   根据用户角色过滤可见订单
    *   执行分页查询
    *   转换为简要响应格式
    *   返回分页结果

### 进度管理方法

#### updateOrderProgress - 更新订单进度
*   **方法签名**: `Result<String> updateOrderProgress(@PathVariable Long id, @Valid @RequestBody UpdateOrderProgressRequest request)`
*   **HTTP方法**: PUT
*   **路径**: `/api/v1/orders/{id}/progress`
*   **权限**: 动态权限控制（基于订单进度阶段和用户角色）
*   **参数**:
    *   `id` (Long) - 订单ID
    *   `request` (UpdateOrderProgressRequest) - 进度更新请求对象
*   **返回值**: `Result<String>` - 进度更新结果消息
*   **业务逻辑**:
    *   验证订单存在性和当前状态
    *   检查用户是否有权限更新该阶段进度
    *   验证进度更新的合法性（不能倒退）
    *   更新订单进度值和状态
    *   创建进度活动记录
    *   处理附件上传和存储
    *   发送进度更新通知
    *   触发相关业务流程

### 文档生成方法

#### generateOrderPdf - 生成订单PDF文档
*   **方法签名**: `Result<PdfGenerationResult> generateOrderPdf(@PathVariable Long orderId)`
*   **HTTP方法**: POST
*   **路径**: `/api/v1/orders/{orderId}/generate-pdf`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")`
*   **参数**: `orderId` (Long) - 订单ID
*   **返回值**: `Result<PdfGenerationResult>` - PDF生成结果，包含文件URL和相关信息
*   **业务逻辑**:
    *   验证订单存在性和用户权限
    *   检查订单状态是否允许生成PDF
    *   收集PDF生成所需的数据
    *   调用PDF生成服务
    *   存储生成的PDF文件
    *   记录文档生成日志
    *   返回文件访问信息

## 使用示例 (Usage Examples)

```java
// 1. 前端JavaScript调用示例
const OrderAPI = {
    // 创建订单
    async createOrder(orderData) {
        const response = await fetch('/api/v1/orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                buyerId: orderData.buyerId,
                sellerId: orderData.sellerId,
                requirementId: orderData.requirementId,
                totalAmount: orderData.totalAmount,
                currency: orderData.currency || 'CNY',
                deliveryAddress: orderData.deliveryAddress,
                expectedDeliveryDate: orderData.expectedDeliveryDate,
                remarks: orderData.remarks,
                orderItems: orderData.items.map(item => ({
                    productName: item.productName,
                    productDescription: item.description,
                    quantity: item.quantity,
                    unitPrice: item.unitPrice,
                    totalPrice: item.totalPrice,
                    specifications: item.specifications
                }))
            })
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('订单创建成功');
            return result.data;
        } else {
            showErrorMessage('创建失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 获取订单详情
    async getOrderDetails(orderId) {
        const response = await fetch(`/api/v1/orders/${orderId}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 更新订单进度
    async updateOrderProgress(orderId, progressData) {
        const formData = new FormData();
        formData.append('stageValue', progressData.stageValue);
        formData.append('title', progressData.title);
        formData.append('description', progressData.description);

        // 添加附件
        if (progressData.attachments) {
            progressData.attachments.forEach((file, index) => {
                formData.append(`attachments[${index}]`, file);
            });
        }

        const response = await fetch(`/api/v1/orders/${orderId}/progress`, {
            method: 'PUT',
            headers: {
                'Authorization': 'Bearer ' + token
            },
            body: formData
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('订单进度更新成功');
            return result.data;
        } else {
            showErrorMessage('更新失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 生成订单PDF
    async generateOrderPdf(orderId) {
        const response = await fetch(`/api/v1/orders/${orderId}/generate-pdf`, {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            // 下载PDF文件
            window.open(result.data.downloadUrl, '_blank');
            return result.data;
        } else {
            showErrorMessage('PDF生成失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 获取订单列表
    async getOrderList(page = 1, size = 10, filters = {}) {
        const params = new URLSearchParams({
            page: page,
            size: size,
            ...filters
        });

        const response = await fetch(`/api/v1/orders?${params}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        return await response.json();
    },

    // 渲染订单列表
    renderOrderList(orders, containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        orders.forEach(order => {
            const orderDiv = document.createElement('div');
            orderDiv.className = 'order-item';

            orderDiv.innerHTML = `
                <div class="order-header">
                    <h3>订单 #${order.orderNumber}</h3>
                    <span class="status ${order.status.toLowerCase()}">${order.statusText}</span>
                </div>
                <div class="order-content">
                    <div class="order-info">
                        <p><strong>买家:</strong> ${order.buyerName}</p>
                        <p><strong>卖家:</strong> ${order.sellerName}</p>
                        <p><strong>总金额:</strong> ¥${order.totalAmount.toLocaleString()}</p>
                        <p><strong>创建时间:</strong> ${new Date(order.createdAt).toLocaleString()}</p>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${order.progressValue}%"></div>
                        <span class="progress-text">${order.progressValue}%</span>
                    </div>
                </div>
                <div class="order-actions">
                    <button onclick="viewOrderDetails(${order.id})">查看详情</button>
                    <button onclick="updateProgress(${order.id})">更新进度</button>
                    <button onclick="generatePdf(${order.id})">生成PDF</button>
                </div>
            `;

            container.appendChild(orderDiv);
        });
    }
};

// 2. Java客户端调用示例
@Service
public class OrderClientService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${api.base-url}")
    private String baseUrl;

    // 创建订单
    public OrderDetailResponse createOrder(CreateOrderRequest request, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<CreateOrderRequest> entity = new HttpEntity<>(request, headers);

        try {
            ResponseEntity<Result<OrderDetailResponse>> response = restTemplate.exchange(
                baseUrl + "/api/v1/orders",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<Result<OrderDetailResponse>>() {}
            );

            Result<OrderDetailResponse> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("创建订单失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用创建订单API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }

    // 获取订单详情
    public OrderDetailResponse getOrderById(Long orderId, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);

        HttpEntity<?> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<Result<OrderDetailResponse>> response = restTemplate.exchange(
                baseUrl + "/api/v1/orders/" + orderId,
                HttpMethod.GET,
                entity,
                new ParameterizedTypeReference<Result<OrderDetailResponse>>() {}
            );

            Result<OrderDetailResponse> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("获取订单详情失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用获取订单详情API失败: orderId={}", orderId, e);
            throw new SystemException("网络请求失败", e);
        }
    }
}

// 3. 业务服务集成示例
@Service
public class OrderWorkflowService {

    @Autowired
    private UnifiedOrderService orderService;

    @Autowired
    private NotificationService notificationService;

    // 订单创建后的业务流程
    @Async
    public void handleOrderCreated(OrderDetailResponse order) {
        try {
            // 1. 发送创建通知
            notificationService.sendOrderCreatedNotification(order);

            // 2. 初始化订单进度
            initializeOrderProgress(order.getId());

            // 3. 创建相关业务记录
            createRelatedBusinessRecords(order);

            log.info("订单创建后处理完成: orderId={}", order.getId());

        } catch (Exception e) {
            log.error("订单创建后处理失败: orderId={}", order.getId(), e);
        }
    }

    // 订单进度更新后的业务流程
    @Async
    public void handleProgressUpdated(Long orderId, Integer newProgress) {
        try {
            // 检查是否达到关键节点
            if (newProgress == 25) {
                // 25% - 订单确认阶段
                handleOrderConfirmed(orderId);
            } else if (newProgress == 50) {
                // 50% - 生产开始阶段
                handleProductionStarted(orderId);
            } else if (newProgress == 75) {
                // 75% - 质检完成阶段
                handleQualityCheckCompleted(orderId);
            } else if (newProgress == 100) {
                // 100% - 订单完成阶段
                handleOrderCompleted(orderId);
            }

        } catch (Exception e) {
            log.error("订单进度更新后处理失败: orderId={}, progress={}", orderId, newProgress, e);
        }
    }
}

// 4. 定时任务示例
@Component
public class OrderScheduledTasks {

    @Autowired
    private UnifiedOrderService orderService;

    // 检查超时订单
    @Scheduled(cron = "0 0 */6 * * ?") // 每6小时执行一次
    public void checkOverdueOrders() {
        log.info("开始检查超时订单");

        try {
            // 获取所有进行中的订单
            List<OrderSimpleResponse> orders = orderService.getInProgressOrders();

            for (OrderSimpleResponse order : orders) {
                if (isOrderOverdue(order)) {
                    handleOverdueOrder(order);
                }
            }

            log.info("超时订单检查完成");

        } catch (Exception e) {
            log.error("检查超时订单失败", e);
        }
    }

    // 生成订单统计报告
    @Scheduled(cron = "0 0 8 * * MON") // 每周一早上8点
    public void generateWeeklyOrderReport() {
        log.info("开始生成周度订单报告");

        try {
            // 获取本周订单数据
            WeeklyOrderReport report = generateOrderReport();

            // 发送报告给管理员
            notificationService.sendWeeklyOrderReport(report);

            log.info("周度订单报告生成完成");

        } catch (Exception e) {
            log.error("生成周度订单报告失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class OrderControllerTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @MockBean
    private UnifiedOrderService orderService;

    @Test
    void testCreateOrder() {
        // 准备测试数据
        CreateOrderRequest request = new CreateOrderRequest();
        request.setBuyerId(1L);
        request.setSellerId(2L);
        request.setRequirementId(3L);
        request.setTotalAmount(new BigDecimal("1000.00"));

        OrderDetailResponse expectedResponse = new OrderDetailResponse();
        expectedResponse.setId(1L);
        expectedResponse.setOrderNumber("ORD20240101001");

        // Mock服务调用
        when(orderService.createOrder(any(CreateOrderRequest.class)))
            .thenReturn(expectedResponse);

        // 执行测试
        HttpEntity<CreateOrderRequest> entity = new HttpEntity<>(request);
        ResponseEntity<Result> response = restTemplate.postForEntity(
            "/api/v1/orders", entity, Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(orderService, times(1)).createOrder(any(CreateOrderRequest.class));
    }
}
```

## 注意事项 (Notes)
*   **权限控制**: 所有接口都使用@PreAuthorize注解进行权限验证，确保只有相应角色的用户才能访问对应功能
*   **阶段权限映射**: 订单进度更新接口实现了复杂的阶段权限控制，不同阶段只允许特定角色操作
*   **业务委托**: 控制器只负责HTTP请求处理和响应封装，具体业务逻辑委托给UnifiedOrderService处理
*   **数据验证**: 使用@Valid注解对请求参数进行验证，确保数据的完整性和有效性
*   **异常处理**: 使用统一的异常处理机制，确保在出现错误时返回友好的错误信息
*   **响应格式**: 所有接口都返回统一的Result<T>格式，确保前端能够统一处理响应数据
*   **日志记录**: 使用@Slf4j注解记录关键操作的日志信息，便于调试和监控
*   **PDF生成**: 集成了PDF生成服务，支持生成订单相关的文档，需要注意文件存储和访问权限
*   **进度管理**: 订单进度分为6个标准阶段（0%, 20%, 40%, 60%, 80%, 100%），每个阶段有特定的业务含义
*   **文件上传**: 进度更新支持附件上传，需要注意文件大小限制和安全性验证
*   **并发控制**: 订单更新操作需要考虑并发控制，避免数据不一致问题
*   **事务管理**: 复杂的订单操作需要适当的事务边界控制，确保数据一致性
*   **缓存策略**: 订单详情等频繁查询的数据可以考虑使用缓存提高性能
*   **性能优化**: 订单列表查询需要合理的分页和索引设计，避免大数据量查询的性能问题
*   **安全考虑**: 订单数据涉及商业机密，需要严格的权限控制和数据脱敏处理
*   **国际化**: 订单状态和进度描述需要支持多语言，便于国际化部署