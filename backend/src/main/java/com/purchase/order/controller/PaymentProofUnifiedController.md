# PaymentProofUnifiedController.java

## 文件概述 (File Overview)
`PaymentProofUnifiedController.java` 是统一支付凭证管理的REST控制器，位于 `com.purchase.order.controller` 包中。该控制器作为支付凭证管理模块的HTTP接口层，负责处理所有支付凭证相关的REST API请求。通过集成 `PaymentProofService`、`PaymentProofConverter` 等业务服务，提供了支付凭证的完整生命周期管理功能，包括上传、审核、查询、批量处理等。该控制器实现了基于Spring Security的多层权限控制，支持买家、卖家、管理员等不同角色的差异化访问，并提供了完善的多订单类型支付凭证统一管理功能。

## 核心功能 (Core Functionality)
*   **统一凭证管理**: 提供普通订单、样品订单、结算单等多种类型支付凭证的统一管理
*   **凭证上传服务**: 支持买家上传不同类型的支付凭证，包含文件验证和业务校验
*   **审核流程管理**: 完整的管理员审核流程，包括确认、拒绝和批量处理功能
*   **权限分级控制**: 基于用户角色的细粒度权限控制和数据访问隔离
*   **多维度查询**: 支持按订单类型、状态、用户等多种维度查询支付凭证
*   **批量操作支持**: 支持管理员批量审核和状态更新操作，提高处理效率
*   **业务上下文集成**: 自动关联订单、样品订单、结算单等业务上下文信息
*   **状态流转管理**: 完整的凭证状态流转，包括待审核、已确认、已拒绝等
*   **审计日志记录**: 详细的凭证操作审计日志，满足财务合规要求
*   **数据一致性保障**: 确保支付凭证与业务订单数据的一致性和完整性
*   **异常处理机制**: 完善的异常捕获和用户友好的错误提示
*   **统一响应格式**: 使用标准化的Result<T>响应格式，便于前端处理

## 接口说明 (Interface Description)

### 查询方法

#### getPaymentProofList - 获取支付凭证列表
*   **方法签名**: `Result<List<PaymentProofUnifiedDTO>> getPaymentProofList(@RequestParam(required = false) String orderType, @RequestParam(required = false) String status, @RequestParam(required = false) Long uploadedBy, @RequestParam(required = false) Long settlementId, @RequestParam(required = false) Long sampleOrderId, @RequestParam(required = false) Long orderId)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/payment-proof/list`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")`
*   **参数**: 
    *   `orderType` (String) - 订单类型，可选值：UNIFIED、SAMPLE、SETTLEMENT
    *   `status` (String) - 凭证状态，可选值：PENDING、CONFIRMED、REJECTED
    *   `uploadedBy` (Long) - 上传用户ID
    *   `settlementId` (Long) - 结算单ID
    *   `sampleOrderId` (Long) - 样品订单ID
    *   `orderId` (Long) - 统一订单ID
*   **返回值**: `Result<List<PaymentProofUnifiedDTO>>` - 支付凭证统一DTO列表
*   **业务逻辑**: 
    *   验证查询参数的有效性和权限
    *   根据不同参数组合执行相应的查询策略
    *   对指定订单ID的查询进行权限验证
    *   获取支付凭证的业务上下文信息
    *   转换为统一DTO格式并返回

#### getPendingPaymentProofs - 获取待审核支付凭证
*   **方法签名**: `Result<List<PaymentProofUnifiedDTO>> getPendingPaymentProofs(@RequestParam(required = false) String orderType)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/payment-proof/pending`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")`
*   **参数**: `orderType` (String) - 订单类型过滤条件
*   **返回值**: `Result<List<PaymentProofUnifiedDTO>>` - 待审核支付凭证列表
*   **业务逻辑**: 
    *   验证用户查询权限
    *   根据订单类型过滤待审核凭证
    *   获取业务上下文并进行权限验证
    *   转换为统一DTO格式

#### getProcessedPaymentProofs - 获取已处理支付凭证
*   **方法签名**: `Result<List<PaymentProofUnifiedDTO>> getProcessedPaymentProofs(@RequestParam(required = false) String orderType)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/payment-proof/processed`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")`
*   **参数**: `orderType` (String) - 订单类型过滤条件
*   **返回值**: `Result<List<PaymentProofUnifiedDTO>>` - 已处理支付凭证列表
*   **业务逻辑**: 
    *   查询所有已确认或已拒绝的支付凭证
    *   根据订单类型进行过滤
    *   验证用户访问权限
    *   返回处理结果

#### getPaymentProofById - 获取支付凭证详情
*   **方法签名**: `Result<PaymentProofUnifiedDTO> getPaymentProofById(@PathVariable @NotNull Long id)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/payment-proof/{id}`
*   **权限**: `@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")`
*   **参数**: `id` (Long) - 支付凭证ID
*   **返回值**: `Result<PaymentProofUnifiedDTO>` - 支付凭证详细信息
*   **业务逻辑**: 
    *   验证凭证ID的有效性
    *   检查用户是否有权限查看该凭证
    *   获取凭证的完整业务上下文
    *   转换为详细DTO格式

### 审核管理方法

#### confirmPaymentProof - 确认支付凭证
*   **方法签名**: `Result<PaymentProofUnifiedDTO> confirmPaymentProof(@PathVariable @NotNull Long id, @RequestBody @Valid ConfirmPaymentProofRequest request)`
*   **HTTP方法**: PUT
*   **路径**: `/api/v1/payment-proof/{id}/confirm`
*   **权限**: `@PreAuthorize("hasAuthority('admin')")`
*   **参数**: 
    *   `id` (Long) - 支付凭证ID
    *   `request` (ConfirmPaymentProofRequest) - 确认请求对象
*   **返回值**: `Result<PaymentProofUnifiedDTO>` - 确认后的凭证信息
*   **业务逻辑**: 
    *   验证管理员身份和权限
    *   检查凭证状态是否允许确认
    *   执行统一确认流程
    *   更新相关业务订单状态
    *   记录审核日志
    *   发送确认通知

#### rejectPaymentProof - 拒绝支付凭证
*   **方法签名**: `Result<PaymentProofUnifiedDTO> rejectPaymentProof(@PathVariable @NotNull Long id, @RequestBody @Valid RejectPaymentProofRequest request)`
*   **HTTP方法**: PUT
*   **路径**: `/api/v1/payment-proof/{id}/reject`
*   **权限**: `@PreAuthorize("hasAuthority('admin')")`
*   **参数**: 
    *   `id` (Long) - 支付凭证ID
    *   `request` (RejectPaymentProofRequest) - 拒绝请求对象
*   **返回值**: `Result<PaymentProofUnifiedDTO>` - 拒绝后的凭证信息
*   **业务逻辑**: 
    *   验证管理员身份和拒绝原因
    *   检查凭证状态是否允许拒绝
    *   执行统一拒绝流程
    *   记录拒绝原因和审核日志
    *   发送拒绝通知给买家

### 上传方法

#### uploadUnifiedOrderPaymentProof - 上传普通订单支付凭证
*   **方法签名**: `Result<PaymentProofUnifiedDTO> uploadUnifiedOrderPaymentProof(@RequestBody @Valid UploadUnifiedOrderPaymentProofRequest request)`
*   **HTTP方法**: POST
*   **路径**: `/api/v1/payment-proof/upload/unified`
*   **权限**: `@PreAuthorize("hasAuthority('buyer')")`
*   **参数**: `request` (UploadUnifiedOrderPaymentProofRequest) - 上传请求对象
*   **返回值**: `Result<PaymentProofUnifiedDTO>` - 上传后的凭证信息
*   **业务逻辑**:
    *   验证买家身份和订单权限
    *   检查订单状态是否允许上传凭证
    *   验证支付凭证文件格式和大小
    *   保存凭证信息到数据库
    *   更新订单支付状态
    *   发送上传成功通知

#### uploadSettlementPaymentProof - 上传结算单支付凭证
*   **方法签名**: `Result<PaymentProofUnifiedDTO> uploadSettlementPaymentProof(@RequestBody @Valid UploadSettlementPaymentProofRequest request)`
*   **HTTP方法**: POST
*   **路径**: `/api/v1/payment-proof/upload/settlement`
*   **权限**: `@PreAuthorize("hasAuthority('buyer')")`
*   **参数**: `request` (UploadSettlementPaymentProofRequest) - 结算单凭证上传请求
*   **返回值**: `Result<PaymentProofUnifiedDTO>` - 上传后的凭证信息
*   **业务逻辑**:
    *   验证买家对结算单的访问权限
    *   检查结算单状态和支付类型
    *   验证支付金额与结算单金额的匹配性
    *   保存结算单支付凭证
    *   更新结算单支付状态

#### uploadSampleOrderPaymentProof - 上传样品订单支付凭证
*   **方法签名**: `Result<PaymentProofUnifiedDTO> uploadSampleOrderPaymentProof(@RequestBody @Valid UploadSampleOrderPaymentProofRequest request)`
*   **HTTP方法**: POST
*   **路径**: `/api/v1/payment-proof/upload/sample`
*   **权限**: `@PreAuthorize("hasAuthority('buyer')")`
*   **参数**: `request` (UploadSampleOrderPaymentProofRequest) - 样品订单凭证上传请求
*   **返回值**: `Result<PaymentProofUnifiedDTO>` - 上传后的凭证信息
*   **业务逻辑**:
    *   验证买家对样品订单的访问权限
    *   检查样品订单状态是否允许支付
    *   保存样品订单支付凭证
    *   更新样品订单状态
    *   触发样品处理流程

### 批量操作方法

#### batchConfirmPaymentProofs - 批量确认支付凭证
*   **方法签名**: `Result<Map<String, Object>> batchConfirmPaymentProofs(@RequestBody @Valid BatchConfirmPaymentProofRequest request)`
*   **HTTP方法**: POST
*   **路径**: `/api/v1/payment-proof/batch/confirm`
*   **权限**: `@PreAuthorize("hasAuthority('admin')")`
*   **参数**: `request` (BatchConfirmPaymentProofRequest) - 批量确认请求对象
*   **返回值**: `Result<Map<String, Object>>` - 批量操作结果统计
*   **业务逻辑**:
    *   验证管理员权限和批量操作限制
    *   逐个验证凭证状态和权限
    *   执行批量确认操作
    *   统计成功和失败数量
    *   记录批量操作日志
    *   返回操作结果统计

#### batchRejectPaymentProofs - 批量拒绝支付凭证
*   **方法签名**: `Result<Map<String, Object>> batchRejectPaymentProofs(@RequestBody @Valid BatchRejectPaymentProofRequest request)`
*   **HTTP方法**: POST
*   **路径**: `/api/v1/payment-proof/batch/reject`
*   **权限**: `@PreAuthorize("hasAuthority('admin')")`
*   **参数**: `request` (BatchRejectPaymentProofRequest) - 批量拒绝请求对象
*   **返回值**: `Result<Map<String, Object>>` - 批量操作结果统计
*   **业务逻辑**:
    *   验证管理员权限和拒绝原因
    *   检查批量操作的数量限制
    *   逐个执行拒绝操作
    *   记录详细的拒绝日志
    *   发送批量拒绝通知
    *   返回操作结果统计

## 业务规则 (Business Rules)
*   **权限隔离**: 买家只能查看和操作自己的支付凭证，管理员可以查看和审核所有凭证
*   **状态流转**: 支付凭证状态包括待审核、已确认、已拒绝，状态流转不可逆
*   **文件验证**: 支付凭证文件必须是图片格式（JPG、PNG），大小不超过10MB
*   **金额校验**: 结算单支付凭证的金额必须与结算单应付金额匹配
*   **重复上传**: 同一订单的同一支付类型不允许重复上传凭证
*   **审核时限**: 支付凭证上传后48小时内必须完成审核
*   **批量限制**: 批量操作单次最多处理50个凭证
*   **业务关联**: 支付凭证确认后自动更新相关订单的支付状态

## 使用示例 (Usage Examples)

```java
// 1. 前端JavaScript调用示例
const PaymentProofAPI = {
    // 上传普通订单支付凭证
    async uploadUnifiedOrderPaymentProof(proofData) {
        const response = await fetch('/api/v1/payment-proof/upload/unified', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                orderId: proofData.orderId,
                proofUrl: proofData.proofUrl,
                paymentType: proofData.paymentType,
                amount: proofData.amount,
                uploadedBy: proofData.uploadedBy,
                buyerRemark: proofData.buyerRemark
            })
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('支付凭证上传成功');
            return result.data;
        } else {
            showErrorMessage('上传失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 获取待审核支付凭证列表
    async getPendingPaymentProofs(orderType) {
        const response = await fetch(`/api/v1/payment-proof/pending?orderType=${orderType || ''}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 管理员确认支付凭证
    async confirmPaymentProof(proofId, adminRemark) {
        const response = await fetch(`/api/v1/payment-proof/${proofId}/confirm`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                adminUserId: getCurrentUserId(),
                adminRemark: adminRemark
            })
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('支付凭证确认成功');
            return result.data;
        } else {
            showErrorMessage('确认失败: ' + result.message);
            throw new Error(result.message);
        }
    }
};

// 2. Java客户端调用示例
@Service
public class PaymentProofClientService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${api.base-url}")
    private String baseUrl;

    // 上传支付凭证
    public PaymentProofUnifiedDTO uploadPaymentProof(UploadUnifiedOrderPaymentProofRequest request, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<UploadUnifiedOrderPaymentProofRequest> entity = new HttpEntity<>(request, headers);

        try {
            ResponseEntity<Result<PaymentProofUnifiedDTO>> response = restTemplate.exchange(
                baseUrl + "/api/v1/payment-proof/upload/unified",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<Result<PaymentProofUnifiedDTO>>() {}
            );

            Result<PaymentProofUnifiedDTO> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("上传支付凭证失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用上传支付凭证API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }

    // 批量确认支付凭证
    public Map<String, Object> batchConfirmPaymentProofs(List<Long> proofIds, String adminRemark, String token) {
        BatchConfirmPaymentProofRequest request = new BatchConfirmPaymentProofRequest();
        request.setProofIds(proofIds);
        request.setAdminUserId(getCurrentUserId());
        request.setAdminRemark(adminRemark);

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<BatchConfirmPaymentProofRequest> entity = new HttpEntity<>(request, headers);

        try {
            ResponseEntity<Result<Map<String, Object>>> response = restTemplate.exchange(
                baseUrl + "/api/v1/payment-proof/batch/confirm",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<Result<Map<String, Object>>>() {}
            );

            Result<Map<String, Object>> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("批量确认失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用批量确认API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }
}

// 3. 业务服务集成示例
@Service
public class PaymentProofWorkflowService {

    @Autowired
    private PaymentProofService paymentProofService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private OrderService orderService;

    // 支付凭证上传后的业务流程
    @EventListener
    @Async
    public void handlePaymentProofUploaded(PaymentProofUploadedEvent event) {
        try {
            PaymentProofUnifiedDTO proof = event.getPaymentProof();

            // 1. 发送上传通知给管理员
            notificationService.sendPaymentProofUploadedNotification(proof);

            // 2. 更新订单支付状态
            updateOrderPaymentStatus(proof);

            // 3. 记录业务日志
            auditLogService.logPaymentProofUpload(proof);

            log.info("支付凭证上传后处理完成: proofId={}", proof.getId());

        } catch (Exception e) {
            log.error("支付凭证上传后处理失败: proofId={}", event.getPaymentProof().getId(), e);
        }
    }

    // 支付凭证确认后的业务流程
    @EventListener
    @Async
    public void handlePaymentProofConfirmed(PaymentProofConfirmedEvent event) {
        try {
            PaymentProofUnifiedDTO proof = event.getPaymentProof();

            // 1. 更新订单状态
            if (proof.isUnifiedOrder()) {
                orderService.updateOrderPaymentStatus(proof.getBusinessId(), "PAID");
            } else if (proof.isSampleOrder()) {
                sampleOrderService.updatePaymentStatus(proof.getBusinessId(), "PAID");
            } else if (proof.isSettlement()) {
                settlementService.updatePaymentStatus(proof.getBusinessId(), "PAID");
            }

            // 2. 发送确认通知给买家
            notificationService.sendPaymentProofConfirmedNotification(proof);

            // 3. 触发后续业务流程
            triggerNextBusinessProcess(proof);

            log.info("支付凭证确认后处理完成: proofId={}", proof.getId());

        } catch (Exception e) {
            log.error("支付凭证确认后处理失败: proofId={}", event.getPaymentProof().getId(), e);
        }
    }
}

// 4. 定时任务示例
@Component
public class PaymentProofScheduledTasks {

    @Autowired
    private PaymentProofService paymentProofService;

    // 检查超时未审核的支付凭证
    @Scheduled(cron = "0 0 */6 * * ?") // 每6小时执行一次
    public void checkOverduePaymentProofs() {
        log.info("开始检查超时未审核的支付凭证");

        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(48);
            List<PaymentProofUnifiedDTO> overdueProofs = paymentProofService.findOverduePaymentProofs(cutoffTime);

            for (PaymentProofUnifiedDTO proof : overdueProofs) {
                // 发送超时提醒
                notificationService.sendPaymentProofOverdueAlert(proof);
                log.warn("发现超时未审核支付凭证: proofId={}, uploadedAt={}",
                    proof.getId(), proof.getUploadedAt());
            }

            log.info("超时支付凭证检查完成，发现超时数量: {}", overdueProofs.size());

        } catch (Exception e) {
            log.error("检查超时支付凭证失败", e);
        }
    }

    // 生成支付凭证统计报告
    @Scheduled(cron = "0 0 8 1 * ?") // 每月1号早上8点
    public void generateMonthlyPaymentProofReport() {
        log.info("开始生成月度支付凭证报告");

        try {
            MonthlyPaymentProofReport report = new MonthlyPaymentProofReport();
            report.setReportMonth(LocalDate.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM")));
            report.setTotalUploaded(paymentProofService.getUploadedCountLastMonth());
            report.setTotalConfirmed(paymentProofService.getConfirmedCountLastMonth());
            report.setTotalRejected(paymentProofService.getRejectedCountLastMonth());
            report.setAverageProcessingTime(paymentProofService.getAverageProcessingTimeLastMonth());

            // 发送报告给管理员
            notificationService.sendMonthlyPaymentProofReport(report);

            log.info("月度支付凭证报告生成完成");

        } catch (Exception e) {
            log.error("生成月度支付凭证报告失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class PaymentProofUnifiedControllerTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @MockBean
    private PaymentProofService paymentProofService;

    @Test
    void testUploadUnifiedOrderPaymentProof() {
        // 准备测试数据
        UploadUnifiedOrderPaymentProofRequest request = new UploadUnifiedOrderPaymentProofRequest();
        request.setOrderId(1001L);
        request.setProofUrl("https://example.com/proof.jpg");
        request.setPaymentType(PaymentType.FULL_PAYMENT);
        request.setAmount(new BigDecimal("5000.00"));
        request.setUploadedBy(100L);

        PaymentProofUnifiedDTO expectedProof = new PaymentProofUnifiedDTO();
        expectedProof.setId(1L);
        expectedProof.setOrderType("UNIFIED");
        expectedProof.setStatus("PENDING");

        // Mock服务调用
        when(paymentProofService.saveUnifiedOrderPaymentProof(any(), any(), any(), any(), any(), any()))
            .thenReturn(createMockPaymentProof());

        // 执行测试
        HttpEntity<UploadUnifiedOrderPaymentProofRequest> entity = new HttpEntity<>(request);
        ResponseEntity<Result> response = restTemplate.postForEntity(
            "/api/v1/payment-proof/upload/unified", entity, Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(paymentProofService, times(1)).saveUnifiedOrderPaymentProof(any(), any(), any(), any(), any(), any());
    }

    @Test
    void testConfirmPaymentProof() {
        // 准备测试数据
        Long proofId = 1L;
        ConfirmPaymentProofRequest request = new ConfirmPaymentProofRequest();
        request.setAdminUserId(200L);
        request.setAdminRemark("支付凭证有效");

        PaymentProof mockProof = createMockPaymentProof();
        mockProof.setStatus(PaymentProof.ProofStatus.CONFIRMED);

        // Mock服务调用
        when(paymentProofService.confirmPaymentProofUnified(proofId, 200L, "支付凭证有效"))
            .thenReturn(mockProof);

        // 执行测试
        HttpEntity<ConfirmPaymentProofRequest> entity = new HttpEntity<>(request);
        ResponseEntity<Result> response = restTemplate.exchange(
            "/api/v1/payment-proof/" + proofId + "/confirm",
            HttpMethod.PUT, entity, Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(paymentProofService, times(1)).confirmPaymentProofUnified(proofId, 200L, "支付凭证有效");
    }
}
```

## 注意事项 (Notes)
*   **权限控制**: 使用Spring Security的@PreAuthorize注解进行权限验证，确保买家只能操作自己的凭证，管理员可以审核所有凭证
*   **文件安全**: 支付凭证文件必须经过严格的格式验证和病毒扫描，防止恶意文件上传
*   **数据脱敏**: 在返回支付凭证信息时需要对敏感信息进行脱敏处理，如银行卡号部分隐藏
*   **状态一致性**: 支付凭证状态变更必须与相关订单状态保持一致，避免数据不一致问题
*   **审核时效**: 支付凭证上传后应在48小时内完成审核，超时需要发送提醒通知
*   **批量限制**: 批量操作单次最多处理50个凭证，避免系统性能问题和超时
*   **业务委托**: 控制器只负责HTTP请求处理和响应封装，具体业务逻辑委托给PaymentProofService处理
*   **异常处理**: 完善的异常处理机制，确保在出现错误时返回友好的错误信息和适当的HTTP状态码
*   **事务管理**: 支付凭证的上传和审核操作涉及多个数据表，需要确保事务的一致性
*   **并发控制**: 同一支付凭证的并发审核需要使用乐观锁或分布式锁防止冲突
*   **审计日志**: 所有重要的支付凭证操作需要记录详细的审计日志，满足财务合规要求
*   **缓存策略**: 支付凭证统计等频繁查询的数据可以考虑使用缓存提高性能
*   **文件存储**: 支付凭证文件应存储在安全的云存储服务中，并设置适当的访问权限
*   **通知机制**: 支付凭证状态变更需要及时通知相关用户，可以通过邮件、短信或站内消息
*   **数据备份**: 支付凭证数据涉及财务信息，需要定期备份并确保数据的完整性
*   **国际化**: 支付凭证相关的错误消息和状态描述需要支持多语言，便于国际化部署
*   **API文档**: 使用Swagger注解为API提供清晰的接口文档，便于前端开发和第三方集成
