# Order 订单模块文档

## 模块概述

订单模块是采购系统的核心业务模块，负责管理整个订单生命周期，从订单创建、签署、支付到交付完成的全过程。该模块合并了原合同和订单概念，提供统一的订单管理体验，支持复杂的业务流程和多种订单类型。

## 目录结构概览

```
com.purchase.order/
├── controller/                    # 控制器层
│   ├── OrderController.java              # 基础订单控制器
│   ├── UnifiedOrderController.java       # 统一订单控制器
│   ├── SampleOrderController.java        # 样品订单控制器
│   ├── OrderProgressActivityController.java # 订单进度活动控制器
│   └── PaymentProofUnifiedController.java # 支付凭证统一控制器
├── dto/                          # 数据传输对象
│   ├── request/                          # 请求DTO
│   ├── response/                         # 响应DTO
│   ├── PaymentProofUnifiedDTO.java      # 支付凭证统一DTO
│   ├── BatchConfirmPaymentProofRequest.java # 批量确认支付凭证请求
│   ├── BatchRejectPaymentProofRequest.java  # 批量拒绝支付凭证请求
│   └── BatchOperationResult.java        # 批量操作结果
├── entity/                       # 实体类
│   ├── UnifiedOrder.java               # 统一订单实体
│   ├── OrderItem.java                  # 订单项目实体
│   ├── SampleOrder.java                # 样品订单实体
│   ├── SampleOrderItem.java            # 样品订单项目实体
│   ├── PaymentProof.java               # 支付凭证实体
│   ├── PaymentType.java                # 支付类型枚举
│   └── OrderProgressActivity.java      # 订单进度活动实体
├── event/                        # 事件处理
│   ├── OrderCompletedEvent.java        # 订单完成事件
│   ├── OrderEventHandler.java          # 订单事件处理器
│   ├── OrderSignCompletedEvent.java    # 订单签署完成事件
│   ├── PaymentConfirmedEvent.java      # 支付确认事件
│   └── *StatusChangedEvent.java        # 各种状态变更事件
├── mapper/                       # 数据访问层
│   ├── UnifiedOrderMapper.java         # 统一订单Mapper
│   ├── OrderItemMapper.java            # 订单项目Mapper
│   ├── SampleOrderMapper.java          # 样品订单Mapper
│   ├── PaymentProofMapper.java         # 支付凭证Mapper
│   └── OrderProgressActivityMapper.java # 订单进度活动Mapper
├── service/                      # 服务层
│   ├── UnifiedOrderService.java        # 统一订单服务接口
│   ├── SampleOrderService.java         # 样品订单服务接口
│   ├── PaymentProofService.java        # 支付凭证服务接口
│   ├── OrderProgressActivityService.java # 订单进度活动服务接口
│   ├── OrderPdfService.java            # 订单PDF服务接口
│   ├── impl/                           # 服务实现
│   └── strategy/                       # 策略模式实现
└── converter/                    # 转换器
    └── PaymentProofConverter.java      # 支付凭证转换器
```

## 核心功能详述

### 1. 统一订单管理 (UnifiedOrder)

#### 主要功能
- **订单创建**: 基于竞价结果创建订单
- **订单签署**: 买卖双方电子签名确认
- **订单项目管理**: 管理订单中的具体商品
- **状态跟踪**: 完整的订单状态流转
- **支付管理**: 定金和尾款的分期支付

#### 核心文件
- `UnifiedOrder.java`: 统一订单实体，合并合同和订单概念
- `OrderItem.java`: 订单项目实体，存储具体商品信息
- `UnifiedOrderService.java`: 统一订单服务接口
- `UnifiedOrderController.java`: 统一订单REST API

#### 业务流程
1. **创建阶段**: 基于中标竞价创建订单 → 草稿状态
2. **完善阶段**: 添加订单项目 → 完善订单信息
3. **签署阶段**: 买卖双方签署 → 处理中状态
4. **支付阶段**: 定金支付 → 尾款支付
5. **执行阶段**: 生产发货 → 进度跟踪
6. **完成阶段**: 确认收货 → 订单完成

### 2. 样品订单管理 (SampleOrder)

#### 特殊功能
- **样品订单**: 专门处理样品采购订单
- **快速流程**: 简化的订单流程
- **小额支付**: 通常金额较小的订单
- **快速交付**: 较短的交付周期

#### 核心文件
- `SampleOrder.java`: 样品订单实体
- `SampleOrderItem.java`: 样品订单项目实体
- `SampleOrderService.java`: 样品订单服务接口
- `SampleOrderController.java`: 样品订单控制器

### 3. 支付凭证管理 (PaymentProof)

#### 支付功能
- **凭证上传**: 买家上传支付凭证
- **凭证审核**: 卖家确认支付凭证
- **批量操作**: 支持批量确认或拒绝
- **状态跟踪**: 支付凭证状态管理

#### 核心文件
- `PaymentProof.java`: 支付凭证实体
- `PaymentType.java`: 支付类型枚举
- `PaymentProofService.java`: 支付凭证服务
- `PaymentProofUnifiedController.java`: 支付凭证控制器

### 4. 订单进度管理 (OrderProgressActivity)

#### 进度功能
- **进度记录**: 记录订单执行的各个阶段
- **活动日志**: 详细的操作日志
- **进度展示**: 为前端提供进度展示数据
- **通知机制**: 进度变更时发送通知

#### 核心文件
- `OrderProgressActivity.java`: 订单进度活动实体
- `OrderProgressActivityService.java`: 进度活动服务
- `OrderProgressActivityController.java`: 进度活动控制器

### 5. 事件驱动架构 (Event)

#### 事件机制
- **订单事件**: 订单状态变更事件
- **支付事件**: 支付状态变更事件
- **签署事件**: 订单签署完成事件
- **完成事件**: 订单完成事件

#### 核心文件
- `OrderCompletedEvent.java`: 订单完成事件
- `OrderEventHandler.java`: 订单事件处理器
- `PaymentConfirmedEvent.java`: 支付确认事件

## 数据模型说明

### UnifiedOrder 统一订单

#### 核心字段
- **基础信息**: id, orderNumber, roomId, orderStatus
- **买家信息**: buyerId, buyerCompany, buyerContact, buyerSignTime
- **卖家信息**: sellerId, sellerCompany, sellerContact, sellerSignTime
- **金额信息**: totalPrice, depositAmount, finalPaymentAmount
- **支付信息**: paymentStatus, paymentTime, depositPaymentTime
- **物流信息**: deliveryTerms, expectedDeliveryTime, receiverAddress
- **港口信息**: portOfLoadingName, portOfDestinationName
- **进度信息**: progressInfo, progressPercentage

#### 状态定义
- **订单状态**: draft(草稿), processing(处理中), completed(已完成), cancelled(已取消)
- **支付状态**: unpaid(未支付), deposit_paid(定金已支付), final_paid(尾款已支付)

### OrderItem 订单项目

#### 核心字段
- **产品信息**: name, description, hsCode, specification
- **数量价格**: quantity, unitPrice, totalPrice, finalPrice
- **税费折扣**: taxRate, taxAmount, discountRate, discountAmount
- **物理属性**: weight, volume
- **扩展信息**: attributesJson, images

## API 接口概览

### 统一订单接口
- `POST /api/v1/orders` - 创建订单
- `GET /api/v1/orders/{id}` - 获取订单详情
- `PUT /api/v1/orders/{id}` - 更新订单
- `PUT /api/v1/orders/{id}/sign` - 签署订单
- `PUT /api/v1/orders/{id}/status` - 更新订单状态
- `PUT /api/v1/orders/{id}/progress` - 更新订单进度

### 订单项目接口
- `GET /api/v1/orders/{orderId}/items` - 获取订单项目列表
- `POST /api/v1/orders/{orderId}/items` - 添加订单项目
- `PUT /api/v1/orders/{orderId}/items/{itemId}` - 更新订单项目
- `DELETE /api/v1/orders/{orderId}/items/{itemId}` - 删除订单项目

### 支付凭证接口
- `POST /api/v1/payment-proofs` - 上传支付凭证
- `PUT /api/v1/payment-proofs/{id}/confirm` - 确认支付凭证
- `PUT /api/v1/payment-proofs/{id}/reject` - 拒绝支付凭证
- `POST /api/v1/payment-proofs/batch-confirm` - 批量确认
- `POST /api/v1/payment-proofs/batch-reject` - 批量拒绝

### 样品订单接口
- `POST /api/v1/sample-orders` - 创建样品订单
- `GET /api/v1/sample-orders/{id}` - 获取样品订单详情
- `PUT /api/v1/sample-orders/{id}/status` - 更新样品订单状态

## 业务规则

### 订单流程规则
1. 订单必须基于中标的竞价创建
2. 订单创建后处于草稿状态
3. 买卖双方都签署后才能进入处理状态
4. 支付分为定金和尾款两个阶段
5. 确认收货后订单才能完成

### 支付规则
1. 定金通常为订单总价的30%
2. 尾款为订单总价减去定金
3. 支付凭证需要卖家确认
4. 支付完成后才能发货

### 权限规则
1. 买家只能操作自己的订单
2. 卖家只能操作自己接单的订单
3. 管理员可以查看所有订单
4. 敏感信息需要权限控制

## 集成说明

### 与其他模块的关系
- **竞价模块**: 基于中标竞价创建订单
- **用户模块**: 关联买家和卖家用户信息
- **消息模块**: 发送订单相关通知
- **佣金模块**: 订单完成后计算佣金
- **转发模块**: 处理转发订单业务

### 事件发布
- 订单创建事件
- 订单签署完成事件
- 支付确认事件
- 订单完成事件
- 订单状态变更事件

## 注意事项

### 开发注意事项
1. **数据一致性**: 订单和订单项目数据必须保持一致
2. **状态管理**: 严格按照状态流转规则
3. **权限控制**: 每个操作都要验证权限
4. **事务处理**: 关键操作使用事务保护

### 性能优化
1. **分页查询**: 所有列表查询都支持分页
2. **索引优化**: 为查询字段建立索引
3. **缓存策略**: 缓存频繁查询的数据
4. **批量操作**: 大量数据使用批量处理

### 安全考虑
1. **数据验证**: 严格验证所有输入数据
2. **权限控制**: 基于角色的访问控制
3. **敏感信息**: 保护用户敏感信息
4. **操作日志**: 记录重要操作
