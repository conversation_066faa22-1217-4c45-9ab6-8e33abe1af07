# SampleOrderCreateRequest.java

## 文件概述 (File Overview)
`SampleOrderCreateRequest.java` 是样品订单创建请求的数据传输对象（DTO），位于 `com.purchase.order.dto.request` 包中。该类用于封装前端发送的样品订单创建请求数据，包含了创建样品订单所需的所有必要信息，如需求ID、接受的竞价列表、买家联系信息和收货地址等。通过使用JSR 303验证注解，确保了请求数据的完整性和有效性。

## 核心功能 (Core Functionality)
*   **数据封装**: 封装样品订单创建请求的所有必要字段
*   **数据验证**: 通过JSR 303注解提供字段级别的数据验证
*   **类型安全**: 提供强类型的数据传输对象，避免参数传递错误
*   **序列化支持**: 支持JSON序列化和反序列化，便于前后端数据交互
*   **Lombok集成**: 使用@Data注解自动生成getter、setter、toString等方法

## 接口说明 (Interface Description)

### 主要字段 (Main Fields)

#### requirementId
*   **类型**: `Long`
*   **验证**: `@NotNull(message = "需求ID不能为空")`
*   **描述**: 样品需求的唯一标识符
*   **业务逻辑**: 关联到具体的样品需求记录，用于确定订单对应的需求

#### acceptedBiddingIds
*   **类型**: `List<Long>`
*   **验证**: `@NotEmpty(message = "竞价ID列表不能为空")`
*   **描述**: 买家接受的竞价ID列表
*   **业务逻辑**: 包含买家选择的所有竞价记录，用于创建对应的订单项

#### buyerContact
*   **类型**: `String`
*   **验证**: `@NotNull(message = "联系人不能为空")`
*   **描述**: 买家联系人姓名
*   **业务逻辑**: 用于订单沟通和后续联系

#### buyerPhone
*   **类型**: `String`
*   **验证**: `@NotNull(message = "联系电话不能为空")`
*   **描述**: 买家联系电话
*   **业务逻辑**: 主要联系方式，用于紧急沟通

#### buyerEmail
*   **类型**: `String`
*   **验证**: 无（可选字段）
*   **描述**: 买家邮箱地址
*   **业务逻辑**: 辅助联系方式，用于发送订单确认邮件

#### buyerAddress
*   **类型**: `String`
*   **验证**: `@NotNull(message = "收货地址不能为空")`
*   **描述**: 买家收货地址
*   **业务逻辑**: 样品发货的目标地址

#### remark
*   **类型**: `String`
*   **验证**: 无（可选字段）
*   **描述**: 备注信息
*   **业务逻辑**: 买家的特殊要求或补充说明

## 使用示例 (Usage Examples)

```java
// 1. 在Controller中接收请求
@PostMapping("/sample-orders")
public ApiResponse<SampleOrderResponse> createSampleOrder(
    @Valid @RequestBody SampleOrderCreateRequest request) {
    
    // 调用服务层创建样品订单
    SampleOrderResponse response = sampleOrderService.createOrder(request);
    return ApiResponse.success(response);
}

// 2. 创建请求对象示例
SampleOrderCreateRequest request = new SampleOrderCreateRequest();
request.setRequirementId(12345L);
request.setAcceptedBiddingIds(Arrays.asList(1001L, 1002L, 1003L));
request.setBuyerContact("张三");
request.setBuyerPhone("13800138000");
request.setBuyerEmail("<EMAIL>");
request.setBuyerAddress("北京市朝阳区xxx街道xxx号");
request.setRemark("请尽快发货，谢谢！");

// 3. 前端JavaScript调用示例
const createSampleOrder = async (orderData) => {
    const response = await fetch('/api/sample-orders', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify({
            requirementId: 12345,
            acceptedBiddingIds: [1001, 1002, 1003],
            buyerContact: "张三",
            buyerPhone: "13800138000",
            buyerEmail: "<EMAIL>",
            buyerAddress: "北京市朝阳区xxx街道xxx号",
            remark: "请尽快发货，谢谢！"
        })
    });
    
    return await response.json();
};

// 4. 服务层处理示例
@Service
public class SampleOrderServiceImpl implements SampleOrderService {
    
    @Override
    @Transactional
    public SampleOrderResponse createOrder(SampleOrderCreateRequest request) {
        // 验证需求是否存在
        PurchaseRequirement requirement = requirementService.getById(request.getRequirementId());
        if (requirement == null) {
            throw new BusinessException("需求不存在");
        }
        
        // 验证竞价是否有效
        List<BiddingRecord> biddings = biddingService.getByIds(request.getAcceptedBiddingIds());
        
        // 创建样品订单
        SampleOrder order = new SampleOrder();
        order.setRequirementId(request.getRequirementId());
        order.setBuyerContact(request.getBuyerContact());
        order.setBuyerPhone(request.getBuyerPhone());
        order.setBuyerEmail(request.getBuyerEmail());
        order.setBuyerAddress(request.getBuyerAddress());
        order.setRemark(request.getRemark());
        
        // 保存订单
        sampleOrderMapper.insert(order);
        
        // 创建订单项
        for (Long biddingId : request.getAcceptedBiddingIds()) {
            SampleOrderItem item = new SampleOrderItem();
            item.setOrderId(order.getId());
            item.setBiddingId(biddingId);
            sampleOrderItemMapper.insert(item);
        }
        
        return convertToResponse(order);
    }
}
```

## 注意事项 (Notes)
*   **数据验证**: 使用@Valid注解确保Controller层自动进行数据验证，验证失败会抛出MethodArgumentNotValidException
*   **必填字段**: requirementId、acceptedBiddingIds、buyerContact、buyerPhone、buyerAddress为必填字段，不能为空
*   **业务逻辑验证**: 除了基本的非空验证外，还需要在服务层进行业务逻辑验证（如需求是否存在、竞价是否有效等）
*   **事务处理**: 样品订单创建涉及多个表的操作，需要在服务层使用@Transactional注解确保数据一致性
*   **权限控制**: 只有买家角色的用户才能创建样品订单，需要在Controller层进行权限验证
*   **联系信息安全**: 买家联系信息属于敏感数据，需要注意数据保护和隐私安全
*   **地址格式**: buyerAddress字段应该包含完整的收货地址信息，便于物流配送
*   **竞价状态**: acceptedBiddingIds中的竞价记录必须是有效且可接受的状态
*   **重复提交**: 需要防止用户重复提交相同的订单创建请求
*   **异常处理**: 当需求不存在或竞价无效时，应该返回明确的错误信息给前端
