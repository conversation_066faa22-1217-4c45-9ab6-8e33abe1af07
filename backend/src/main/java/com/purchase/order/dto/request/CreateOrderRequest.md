# CreateOrderRequest.md

## 1. 文件概述

`CreateOrderRequest` 是订单模块中的一个数据传输对象（DTO），位于 `com.purchase.order.dto.request` 包中。它用于封装前端在创建新订单时提交的所有必要信息。这个DTO包含了订单的基本信息、买家和卖家信息、支付和交付条款、货物详情以及订单项列表等。它通过JSR-303（Bean Validation）注解定义了严格的输入校验规则，确保了后端接收到的数据是完整和合法的。

## 2. 核心功能

*   **数据封装**: 将创建订单所需的全部数据字段封装在一个单一的Java对象中，方便前端一次性提交所有相关信息。
*   **输入校验**: 利用 `@NotNull`, `@NotBlank`, `@NotEmpty`, `@Valid` 等注解，对各个字段进行非空、非空白、集合非空以及嵌套对象校验，确保数据的完整性和有效性。
*   **结构化**: 通过清晰的字段命名和分组（基本信息、买家信息、卖家信息、支付信息、交付信息、订单项目列表），使得请求结构清晰，易于理解和使用。
*   **类型安全**: 使用Java的强类型特性，确保了每个字段的数据类型符合预期。
*   **日期格式化**: 使用 `@JsonFormat` 注解，指定了日期字段在JSON序列化和反序列化时的格式，避免了日期解析问题。

## 3. 属性说明

### 3.1 基本信息

- **`roomId` (Long)**: 聊天室ID。**必填**。
- **`orderStatus` (String)**: 订单状态（`draft`, `processing`, `completed`, `cancelled`）。创建时通常默认为 `draft`，无需前端设置。

### 3.2 买家信息

- **`buyerId` (Long)**: 买家ID。**必填**。
- **`buyerCompany` (String)**: 买家公司名称。
- **`buyerContact` (String)**: 买家联系人。
- **`buyerPhone` (String)**: 买家联系电话。
- **`buyerEmail` (String)**: 买家邮箱。
- **`buyerAddress` (String)**: 买家地址。

### 3.3 卖家信息

- **`sellerId` (Long)**: 卖家ID。**必填**。
- **`sellerCompany` (String)**: 卖家公司名称。**必填**。
- **`sellerContact` (String)**: 卖家联系人。**必填**。
- **`sellerPhone` (String)**: 卖家联系电话。**必填**。
- **`sellerEmail` (String)**: 卖家邮箱。**必填**。
- **`sellerAddress` (String)**: 卖家地址。**必填**。

### 3.4 货物与支付信息

- **`cargoType` (String)**: 货物类型。
- **`paymentTerms` (String)**: 支付条款。**必填**。
- **`depositAmount` (BigDecimal)**: 定金金额。
- **`finalPaymentAmount` (BigDecimal)**: 尾款金额。
- **`paymentStatus` (String)**: 支付状态（`unpaid`, `deposit_paid` 等）。创建时通常默认为 `unpaid`，无需前端设置。

### 3.5 交付与收货信息

- **`deliveryTerms` (String)**: 交付条款。**必填**。
- **`expectedDeliveryTime` (Date)**: 预计交付时间。**必填**，格式为 `yyyy-MM-dd`。
- **`receiverName` (String)**: 收货人姓名。
- **`receiverPhone` (String)**: 收货人联系电话。
- **`receiverEmail` (String)**: 收货人邮箱。
- **`receiverAddress` (String)**: 收货地址。
- **`shippingAddress` (String)**: 发货地址。**必填**。
- **`shippingContact` (String)**: 发货人姓名。**必填**。
- **`shippingPhone` (String)**: 发货人电话。**必填**。
- **`shippingEmail` (String)**: 发货人邮箱。

### 3.6 额外货运信息

- **`countryOfOrigin` (String)**: 生产国别。
- **`manufacturerName` (String)**: 制造厂商名称。
- **`portOfLoadingName` (String)**: 起始港口名称。
- **`portOfLoadingCode` (String)**: 起始港口 UN/LOCODE。
- **`portOfDestinationName` (String)**: 目的港口名称。
- **`portOfDestinationCode` (String)**: 目的港口 UN/LOCODE。
- **`needCertification` (String)**: 是否需要认证（`0`-不需要，`1`-需要）。
- **`needFumigation` (String)**: 是否需要熏蒸（`0`-不需要，`1`-需要）。
- **`shipmentType` (String)**: 运输类型（`LCL`-拼柜，`FCL`-整柜）。
- **`containerSize` (String)**: 货柜尺寸（仅当 `shipmentType=FCL` 时有效）。
- **`containerQty` (Integer)**: 货柜数量（仅当 `shipmentType=FCL` 时有效）。
- **`customsService` (Integer)**: 是否包含报关服务（`0`-不包含，`1`-包含）。
- **`insuranceIncluded` (Integer)**: 是否包含保险（`0`-不包含，`1`-包含）。
- **`remarks` (String)**: 订单备注。

### 3.7 订单项目列表

- **`items` (List<OrderItemRequest>)**: 订单项目列表。**必填且不能为空**。每个 `OrderItemRequest` 都会被递归校验。

## 4. 业务规则

*   **数据完整性**: 多个字段被标记为 `@NotNull` 或 `@NotBlank`，确保了创建订单时关键信息的完整性。
*   **嵌套校验**: `@Valid` 注解在 `items` 列表上，确保了列表中的每个 `OrderItemRequest` 对象也会被自动校验。
*   **枚举值**: `orderStatus` 和 `paymentStatus` 字段虽然是 `String` 类型，但其值应遵循预定义的枚举（如 `UnifiedOrder.OrderStatus`），在服务层应进行进一步的业务校验。
*   **条件必填**: `containerSize` 和 `containerQty` 字段仅在 `shipmentType` 为 `FCL` 时有效，这部分逻辑通常在服务层进行业务校验。

## 5. 使用示例

```java
// 1. 前端 (JavaScript) 构造请求体
const createOrderPayload = {
  roomId: 12345,
  buyerId: 1001,
  buyerCompany: "买家公司A",
  // ... 其他买家信息
  sellerId: 2001,
  sellerCompany: "卖家公司B",
  sellerContact: "李四",
  sellerPhone: "13987654321",
  sellerEmail: "<EMAIL>",
  sellerAddress: "卖家地址",
  paymentTerms: "T/T in advance",
  deliveryTerms: "FOB Shanghai",
  expectedDeliveryTime: "2025-08-15", // 注意日期格式
  shippingAddress: "发货地址",
  shippingContact: "王五",
  shippingPhone: "13712345678",
  items: [
    {
      productId: 1,
      productName: "产品A",
      quantity: 100,
      unitPrice: 10.50,
      totalPrice: 1050.00
    },
    {
      productId: 2,
      productName: "产品B",
      quantity: 50,
      unitPrice: 20.00,
      totalPrice: 1000.00
    }
  ]
  // ... 其他字段
};

// 使用axios发送POST请求
axios.post('/api/v1/orders', createOrderPayload)
  .then(response => {
    console.log('订单创建成功:', response.data);
  })
  .catch(error => {
    console.error('订单创建失败:', error.response.data);
  });

// 2. 在 Controller 中接收请求
@RestController
@RequestMapping("/api/v1/orders")
@Validated // 启用JSR-303校验
public class OrderController {
    @Autowired
    private OrderService orderService;

    @PostMapping
    public Result<Long> createOrder(@RequestBody @Valid CreateOrderRequest request) {
        // 如果校验失败，会自动抛出 MethodArgumentNotValidException，由全局异常处理器处理
        Long orderId = orderService.createOrder(request);
        return Result.success(orderId);
    }
}

// 3. 在 OrderService 中处理请求
@Service
public class OrderServiceImpl implements OrderService {
    @Autowired
    private UnifiedOrderMapper orderMapper;
    @Autowired
    private OrderItemMapper orderItemMapper;

    @Transactional
    public Long createOrder(CreateOrderRequest request) {
        UnifiedOrder order = new UnifiedOrder();
        // BeanUtils.copyProperties(request, order); // 简化赋值
        // ... 手动或通过工具将 request 转换为 UnifiedOrder 实体 ...
        order.setRoomId(request.getRoomId());
        order.setBuyerId(request.getBuyerId());
        order.setSellerId(request.getSellerId());
        order.setPaymentTerms(request.getPaymentTerms());
        order.setDeliveryTerms(request.getDeliveryTerms());
        order.setExpectedDeliveryTime(request.getExpectedDeliveryTime());
        order.setShippingAddress(request.getShippingAddress());
        order.setShippingContact(request.getShippingContact());
        order.setShippingPhone(request.getShippingPhone());
        order.setOrderStatus(UnifiedOrder.OrderStatus.DRAFT.name()); // 默认草稿状态
        order.setPaymentStatus(UnifiedOrder.PaymentStatus.UNPAID.name()); // 默认未支付
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());

        orderMapper.insert(order);

        for (OrderItemRequest itemRequest : request.getItems()) {
            OrderItem orderItem = new OrderItem();
            orderItem.setOrderId(order.getId());
            orderItem.setProductId(itemRequest.getProductId());
            orderItem.setQuantity(itemRequest.getQuantity());
            orderItem.setUnitPrice(itemRequest.getUnitPrice());
            orderItem.setTotalPrice(itemRequest.getTotalPrice());
            orderItem.setCreatedAt(LocalDateTime.now());
            orderItemMapper.insert(orderItem);
        }
        return order.getId();
    }
}

// 4. 测试示例
@SpringBootTest
class CreateOrderRequestTest {
    @Autowired
    private Validator validator;

    @Test
    void testValidation_Success() {
        CreateOrderRequest request = new CreateOrderRequest();
        request.setRoomId(1L);
        request.setBuyerId(10L);
        request.setSellerId(20L);
        request.setSellerCompany("Test Seller Co.");
        request.setSellerContact("Seller Contact");
        request.setSellerPhone("12345678900");
        request.setSellerEmail("<EMAIL>");
        request.setSellerAddress("Seller Address");
        request.setPaymentTerms("TT");
        request.setDeliveryTerms("FOB");
        request.setExpectedDeliveryTime(new Date());
        request.setShippingAddress("Shipping Address");
        request.setShippingContact("Shipping Contact");
        request.setShippingPhone("09876543210");
        
        OrderItemRequest item = new OrderItemRequest();
        item.setProductId(100L);
        item.setQuantity(1);
        item.setUnitPrice(new BigDecimal("10.00"));
        item.setTotalPrice(new BigDecimal("10.00"));
        request.setItems(List.of(item));

        Set<ConstraintViolation<CreateOrderRequest>> violations = validator.validate(request);
        assertThat(violations).isEmpty();
    }

    @Test
    void testValidation_MissingRequiredField() {
        CreateOrderRequest request = new CreateOrderRequest();
        // 缺少 roomId
        // ... 填充其他必填字段 ...
        request.setBuyerId(10L);
        request.setSellerId(20L);
        request.setSellerCompany("Test Seller Co.");
        request.setSellerContact("Seller Contact");
        request.setSellerPhone("12345678900");
        request.setSellerEmail("<EMAIL>");
        request.setSellerAddress("Seller Address");
        request.setPaymentTerms("TT");
        request.setDeliveryTerms("FOB");
        request.setExpectedDeliveryTime(new Date());
        request.setShippingAddress("Shipping Address");
        request.setShippingContact("Shipping Contact");
        request.setShippingPhone("09876543210");
        request.setItems(List.of(new OrderItemRequest())); // 即使item内容不全，但列表不为空

        Set<ConstraintViolation<CreateOrderRequest>> violations = validator.validate(request);
        assertThat(violations).isNotEmpty();
        assertThat(violations).anyMatch(v -> v.getMessage().equals("聊天室ID不能为空"));
    }
}
```

## 6. 注意事项

*   **JSR-303校验**: `@Valid` 注解在Controller层配合 `@Validated` 使用，可以自动触发对请求体中所有字段的校验，包括嵌套对象的校验。校验失败会抛出 `MethodArgumentNotValidException`，通常由全局异常处理器统一处理并返回友好的错误信息。
*   **Lombok**: 使用 `@Data` 注解自动生成Getter、Setter、`equals`、`hashCode` 和 `toString` 方法，简化了DTO的编写。
*   **日期格式**: `@JsonFormat` 注解确保了日期字段在JSON和Java对象之间正确转换，避免了时区和格式问题。
*   **枚举值**: 尽管 `orderStatus` 和 `paymentStatus` 是 `String` 类型，但其值应严格遵循后端定义的枚举。在服务层进行业务逻辑处理时，应将这些字符串转换为对应的枚举类型进行操作。
*   **业务逻辑与DTO分离**: `CreateOrderRequest` 仅作为数据载体，不包含任何业务逻辑。所有业务规则的校验和处理都应在服务层完成。
*   **可扩展性**: 如果未来订单需要增加更多字段，只需在此DTO中添加相应属性和校验规则即可，无需修改核心业务逻辑。
*   **安全性**: 尽管JSR-303提供了基本的输入校验，但对于敏感数据（如金额），在服务层仍需进行更严格的业务校验，例如金额不能为负数。
