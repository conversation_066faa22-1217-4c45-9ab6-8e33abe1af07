# UpdateOrderRequest.md

## 1. 文件概述

`UpdateOrderRequest` 是订单模块中的一个数据传输对象（DTO），位于 `com.purchase.order.dto.request` 包中。它用于封装前端在更新现有订单时提交的所有必要信息。这个DTO包含了订单的唯一标识 `id` 和 `orderNumber`，以及订单的各项可修改属性，包括买家和卖家信息、支付和交付条款、货物详情、订单项列表，甚至订单的进度信息。它通过JSR-303（Bean Validation）注解定义了严格的输入校验规则，确保了后端接收到的数据是完整和合法的。

## 2. 核心功能

*   **数据封装**: 将更新订单所需的全部数据字段封装在一个单一的Java对象中，方便前端一次性提交所有相关信息。
*   **输入校验**: 利用 `@NotNull`, `@NotBlank`, `@NotEmpty`, `@Valid` 等注解，对各个字段进行非空、非空白、集合非空以及嵌套对象校验，确保数据的完整性和有效性。
*   **结构化**: 通过清晰的字段命名和分组，使得请求结构清晰，易于理解和使用。
*   **类型安全**: 使用Java的强类型特性，确保了每个字段的数据类型符合预期。
*   **日期格式化**: 使用 `@JsonFormat` 注解，指定了日期字段在JSON序列化和反序列化时的格式，避免了日期解析问题。
*   **JSON处理**: `@JsonIgnoreProperties(ignoreUnknown = true)` 允许DTO忽略JSON中未定义的字段，增加了API的兼容性。`@JsonSerialize(using = ToStringSerializer.class)` 用于处理Long类型ID在前端JavaScript中可能存在的精度丢失问题。

## 3. 属性说明

### 3.1 订单标识与基本信息

- **`id` (Long)**: 订单ID。**必填**。用于唯一标识待更新的订单。
- **`orderNumber` (String)**: 订单编号（业务编号）。**必填**。
- **`roomId` (Long)**: 聊天室ID。
- **`orderStatus` (String)**: 订单状态（`draft`, `processing`, `completed`, `cancelled`）。

### 3.2 买家信息

- **`buyerId` (Long)**: 买家ID。**必填**。
- **`buyerCompany` (String)**: 买家公司名称。
- **`buyerContact` (String)**: 买家联系人。
- **`buyerPhone` (String)**: 买家联系电话。
- **`buyerEmail` (String)**: 买家邮箱。
- **`buyerAddress` (String)**: 买家地址。
- **`buyerSignatureUrl` (String)**: 买家电子签名图片URL。

### 3.3 卖家信息

- **`sellerId` (Long)**: 卖家ID。**必填**。
- **`sellerCompany` (String)**: 卖家公司名称。**必填**。
- **`sellerContact` (String)**: 卖家联系人。**必填**。
- **`sellerPhone` (String)**: 卖家联系电话。**必填**。
- **`sellerEmail` (String)**: 卖家邮箱。
- **`sellerAddress` (String)**: 卖家地址。**必填**。
- **`sellerSignatureUrl` (String)**: 卖家电子签名图片URL。

### 3.4 货物与支付信息

- **`totalPrice` (BigDecimal)**: 手动设置的总价。**必填**。
- **`totalWeight` (BigDecimal)**: 手动设置的总重量 (kg)。
- **`totalVolume` (BigDecimal)**: 手动设置的总体积 (m³)。
- **`cargoType` (String)**: 货物类型。
- **`paymentTerms` (String)**: 支付条款。**必填**。
- **`depositAmount` (BigDecimal)**: 定金金额。
- **`finalPaymentAmount` (BigDecimal)**: 尾款金额。
- **`paymentStatus` (String)**: 支付状态（`unpaid`, `deposit_paid` 等）。

### 3.5 交付与收货信息

- **`deliveryTerms` (String)**: 交付条款。**必填**。
- **`expectedDeliveryTime` (Date)**: 预计交付时间。**必填**。
- **`receiverName` (String)**: 收货人姓名。
- **`receiverPhone` (String)**: 收货人联系电话。
- **`receiverEmail` (String)**: 收货人邮箱。
- **`receiverAddress` (String)**: 收货地址。
- **`shippingAddress` (String)**: 发货地址。**必填**。
- **`shippingContact` (String)**: 发货人姓名。**必填**。
- **`shippingPhone` (String)**: 发货人电话。**必填**。
- **`shippingEmail` (String)**: 发货人邮箱。

### 3.6 额外货运信息

- **`countryOfOrigin` (String)**: 生产国别。
- **`manufacturerName` (String)**: 制造厂商名称。
- **`portOfLoadingName` (String)**: 起始港口名称。
- **`portOfLoadingCode` (String)**: 起始港口 UN/LOCODE。
- **`portOfDestinationName` (String)**: 目的港口名称。
- **`portOfDestinationCode` (String)**: 目的港口 UN/LOCODE。
- **`needCertification` (String)**: 是否需要认证（`0`-不需要，`1`-需要）。
- **`needFumigation` (String)**: 是否需要熏蒸（`0`-不需要，`1`-需要）。
- **`shipmentType` (String)**: 运输类型（`LCL`-拼柜，`FCL`-整柜）。
- **`containerSize` (String)**: 货柜尺寸（仅当 `shipmentType=FCL` 时有效）。
- **`containerQty` (Integer)**: 货柜数量（仅当 `shipmentType=FCL` 时有效）。
- **`customsService` (Integer)**: 是否包含报关服务（`0`-不包含，`1`-包含）。
- **`insuranceIncluded` (Integer)**: 是否包含保险（`0`-不包含，`1`-包含）。

### 3.7 订单进度与备注

- **`progressInfo` (String)**: 进度信息。
- **`progressPercentage` (Integer)**: 进度百分比，0-100。
- **`remarks` (String)**: 订单备注。

### 3.8 订单项目列表

- **`items` (List<OrderItemRequest>)**: 订单项目列表。**必填且不能为空**。每个 `OrderItemRequest` 都会被递归校验。

## 4. 业务规则

*   **订单标识**: `id` 和 `orderNumber` 共同作为订单的唯一标识，在更新操作中必须提供。
*   **部分更新**: 尽管DTO包含了所有字段，但实际更新时，服务层应根据业务需求，只更新允许修改的字段，并忽略其他字段。
*   **状态流转**: `orderStatus` 和 `paymentStatus` 的更新应遵循严格的状态机规则，例如，已完成的订单不能再修改为草稿状态。
*   **嵌套校验**: `@Valid` 注解在 `items` 列表上，确保了列表中的每个 `OrderItemRequest` 对象也会被自动校验。
*   **条件必填**: `containerSize` 和 `containerQty` 字段仅在 `shipmentType` 为 `FCL` 时有效，这部分逻辑通常在服务层进行业务校验。

## 5. 使用示例

```java
// 1. 前端 (JavaScript) 构造请求体
const updateOrderPayload = {
  id: 12345, // 订单ID
  orderNumber: "PO20240727001",
  // ... 其他需要更新的字段
  totalPrice: 2500.00,
  paymentTerms: "L/C at sight",
  expectedDeliveryTime: "2025-09-01",
  items: [
    // 订单项的更新策略需要后端定义，是全量替换还是增量更新
    // 这里假设是全量替换
    {
      productId: 1,
      productName: "产品A",
      quantity: 120, // 数量更新
      unitPrice: 10.50,
      totalPrice: 1260.00
    },
    {
      productId: 3, // 新增一个产品
      productName: "产品C",
      quantity: 30,
      unitPrice: 50.00,
      totalPrice: 1500.00
    }
  ],
  progressInfo: "已完成生产，等待发货",
  progressPercentage: 70
};

// 使用axios发送PUT请求
axios.put('/api/v1/orders', updateOrderPayload)
  .then(response => {
    console.log('订单更新成功:', response.data);
  })
  .catch(error => {
    console.error('订单更新失败:', error.response.data);
  });

// 2. 在 Controller 中接收请求
@RestController
@RequestMapping("/api/v1/orders")
@Validated // 启用JSR-303校验
public class OrderController {
    @Autowired
    private OrderService orderService;

    @PutMapping
    public Result<Void> updateOrder(@RequestBody @Valid UpdateOrderRequest request) {
        // 如果校验失败，会自动抛出 MethodArgumentNotValidException，由全局异常处理器处理
        orderService.updateOrder(request);
        return Result.success();
    }
}

// 3. 在 OrderService 中处理请求
@Service
public class OrderServiceImpl implements OrderService {
    @Autowired
    private UnifiedOrderMapper orderMapper;
    @Autowired
    private OrderItemMapper orderItemMapper;

    @Transactional
    public void updateOrder(UpdateOrderRequest request) {
        UnifiedOrder existingOrder = orderMapper.selectById(request.getId());
        if (existingOrder == null) {
            throw new BusinessException("订单不存在");
        }
        // 业务校验：例如，只有草稿状态的订单才能被修改
        if (!existingOrder.getOrderStatus().equals(UnifiedOrder.OrderStatus.DRAFT.name())) {
            throw new BusinessException("只有草稿状态的订单才能修改");
        }

        // BeanUtils.copyProperties(request, existingOrder); // 简化赋值，注意忽略不可更新字段
        // ... 手动或通过工具将 request 中的可更新字段复制到 existingOrder ...
        existingOrder.setTotalPrice(request.getTotalPrice());
        existingOrder.setPaymentTerms(request.getPaymentTerms());
        existingOrder.setExpectedDeliveryTime(request.getExpectedDeliveryTime());
        existingOrder.setShippingAddress(request.getShippingAddress());
        existingOrder.setUpdatedAt(LocalDateTime.now());
        existingOrder.setProgressInfo(request.getProgressInfo());
        existingOrder.setProgressPercentage(request.getProgressPercentage());

        orderMapper.updateById(existingOrder);

        // 处理订单项更新：通常是先删除旧的，再插入新的，或者进行差异更新
        QueryWrapper<OrderItem> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("order_id", request.getId());
        orderItemMapper.delete(deleteWrapper);

        for (OrderItemRequest itemRequest : request.getItems()) {
            OrderItem orderItem = new OrderItem();
            orderItem.setOrderId(request.getId());
            orderItem.setProductId(itemRequest.getProductId());
            orderItem.setQuantity(itemRequest.getQuantity());
            orderItem.setUnitPrice(itemRequest.getUnitPrice());
            orderItem.setTotalPrice(itemRequest.getTotalPrice());
            orderItem.setCreatedAt(LocalDateTime.now()); // 更新时创建时间不变，或使用当前时间
            orderItemMapper.insert(orderItem);
        }
    }
}

// 4. 测试示例
@SpringBootTest
class UpdateOrderRequestTest {
    @Autowired
    private Validator validator;

    @Test
    void testValidation_Success() {
        UpdateOrderRequest request = new UpdateOrderRequest();
        request.setId(1L);
        request.setOrderNumber("ORD123");
        request.setBuyerId(10L);
        request.setSellerId(20L);
        request.setSellerCompany("Test Seller Co.");
        request.setSellerContact("Seller Contact");
        request.setSellerPhone("12345678900");
        request.setSellerAddress("Seller Address");
        request.setTotalPrice(new BigDecimal("100.00"));
        request.setPaymentTerms("TT");
        request.setDeliveryTerms("FOB");
        request.setExpectedDeliveryTime(new Date());
        request.setShippingAddress("Shipping Address");
        request.setShippingContact("Shipping Contact");
        request.setShippingPhone("09876543210");
        
        OrderItemRequest item = new OrderItemRequest();
        item.setProductId(100L);
        item.setQuantity(1);
        item.setUnitPrice(new BigDecimal("10.00"));
        item.setTotalPrice(new BigDecimal("10.00"));
        request.setItems(List.of(item));

        Set<ConstraintViolation<UpdateOrderRequest>> violations = validator.validate(request);
        assertThat(violations).isEmpty();
    }

    @Test
    void testValidation_MissingId() {
        UpdateOrderRequest request = new UpdateOrderRequest();
        // 缺少 id
        request.setOrderNumber("ORD123");
        // ... 填充其他必填字段 ...
        request.setBuyerId(10L);
        request.setSellerId(20L);
        request.setSellerCompany("Test Seller Co.");
        request.setSellerContact("Seller Contact");
        request.setSellerPhone("12345678900");
        request.setSellerAddress("Seller Address");
        request.setTotalPrice(new BigDecimal("100.00"));
        request.setPaymentTerms("TT");
        request.setDeliveryTerms("FOB");
        request.setExpectedDeliveryTime(new Date());
        request.setShippingAddress("Shipping Address");
        request.setShippingContact("Shipping Contact");
        request.setShippingPhone("09876543210");
        request.setItems(List.of(new OrderItemRequest()));

        Set<ConstraintViolation<UpdateOrderRequest>> violations = validator.validate(request);
        assertThat(violations).isNotEmpty();
        assertThat(violations).anyMatch(v -> v.getMessage().equals("订单ID不能为空"));
    }
}
```

## 6. 注意事项

*   **JSR-303校验**: `@Valid` 注解在Controller层配合 `@Validated` 使用，可以自动触发对请求体中所有字段的校验，包括嵌套对象的校验。校验失败会抛出 `MethodArgumentNotValidException`，通常由全局异常处理器统一处理并返回友好的错误信息。
*   **Lombok**: 使用 `@Data` 注解自动生成Getter、Setter、`equals`、`hashCode` 和 `toString` 方法，简化了DTO的编写。
*   **日期格式**: `@JsonFormat` 注解确保了日期字段在JSON和Java对象之间正确转换，避免了时区和格式问题。
*   **Long类型ID精度**: `@JsonSerialize(using = ToStringSerializer.class)` 用于解决JavaScript处理Long类型ID时可能出现的精度丢失问题，将其序列化为字符串。
*   **部分更新**: `UpdateOrderRequest` 通常用于部分更新。在服务层处理时，应注意只更新DTO中实际有值的字段，而不是简单地覆盖整个实体。`@JsonIgnoreProperties(ignoreUnknown = true)` 有助于忽略前端可能传递的额外字段。
*   **业务逻辑与DTO分离**: `UpdateOrderRequest` 仅作为数据载体，不包含任何业务逻辑。所有业务规则的校验和处理都应在服务层完成。
*   **订单项更新策略**: 对于 `items` 列表的更新，后端需要明确定义更新策略：是全量替换（先删除所有旧订单项，再插入所有新订单项），还是增量更新（对比新旧列表，进行增、删、改操作）。示例中采用了全量替换的策略。
*   **安全性**: 尽管JSR-303提供了基本的输入校验，但对于敏感数据（如金额），在服务层仍需进行更严格的业务校验，例如金额不能为负数。
