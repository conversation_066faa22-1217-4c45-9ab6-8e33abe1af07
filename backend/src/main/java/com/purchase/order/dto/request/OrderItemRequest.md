# OrderItemRequest 文档

## 文件概述

`OrderItemRequest` 是位于 `com.purchase.order.dto.request` 包中的订单项目请求数据传输对象，用于在创建或更新订单时传递单个商品项目的详细信息。该DTO支持完整的商品信息录入，包括基础信息、价格计算、税务处理和扩展属性。

## 核心功能

### 主要职责
- **商品信息采集**：收集订单中单个商品的完整信息，包括名称、描述、规格等基础属性
- **价格计算支持**：提供价格计算的完整数据基础，包括单价、数量、税费、折扣等
- **海关申报支持**：集成HS商品编码字段，支持跨境订单的海关申报需求
- **多媒体支持**：支持商品图片和扩展属性的JSON格式存储
- **数据验证**：通过JSR-303注解实现请求数据的自动验证

### 业务特点
- **完整性**：涵盖商品从基本信息到价格计算的全方位数据模型
- **灵活性**：通过JSON扩展属性支持不同业务场景的商品特性
- **精确性**：使用BigDecimal处理金融计算，避免浮点数精度问题
- **可扩展**：支持自定义排序和备注信息，适应复杂业务需求
- **标准化**：符合国际贸易标准，支持海关编码和计量单位规范

## 接口说明

### 字段规格

#### 基础信息字段
| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| `id` | Long | 可选 | 项目ID，更新时使用 |
| `orderId` | Long | 可选 | 关联的订单ID，路径参数传递时可省略 |
| `name` | String | @NotBlank | 产品名称，必填 |
| `description` | String | 可选 | 产品详细描述 |
| `categoryId` | Long | 可选 | 产品分类ID，关联分类表 |

#### 商品规格字段
| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| `hsCode` | String | 可选 | 海关商品编码(HS Code)，用于海关申报 |
| `specification` | String | 可选 | 产品规格参数 |
| `unit` | String | @NotBlank | 计量单位，如：件、kg、m等 |
| `weight` | BigDecimal | 可选 | 商品重量(kg) |
| `volume` | BigDecimal | 可选 | 商品体积(m³) |

#### 数量价格字段
| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| `quantity` | BigDecimal | @NotNull @Positive | 购买数量，必须大于0 |
| `unitPrice` | BigDecimal | @NotNull @Positive | 商品单价，必须大于0 |
| `totalPrice` | BigDecimal | 计算字段 | 总价 = 单价 × 数量 |
| `finalPrice` | BigDecimal | 计算字段 | 最终价格 = 总价 + 税额 - 折扣金额 |

#### 税务折扣字段
| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| `taxRate` | BigDecimal | 可选 | 税率(%)，如：13.00 |
| `taxAmount` | BigDecimal | 计算字段 | 税额 = 总价 × 税率% |
| `discountRate` | BigDecimal | 可选 | 折扣率(%)，如：10.00 |
| `discountAmount` | BigDecimal | 计算字段 | 折扣金额 = 总价 × 折扣率% |

#### 扩展属性字段
| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| `attributesJson` | String | 可选 | 产品扩展属性JSON，支持自定义属性 |
| `images` | String | 可选 | 产品图片URL列表，逗号分隔 |
| `sortOrder` | Integer | 可选 | 排序顺序，用于前端展示 |
| `remarks` | String | 可选 | 项目备注信息 |

## 使用示例

### 基础使用
```java
// 创建单个订单项目
OrderItemRequest item = new OrderItemRequest();
item.setName("iPhone 15 Pro");
item.setDescription("苹果15 Pro 256GB 钛金属色");
item.setHsCode("8517.12.00"); // 手机HS编码
item.setSpecification("6.1英寸, A17 Pro芯片, 256GB存储");
item.setUnit("件");
item.setQuantity(BigDecimal.valueOf(2));
item.setUnitPrice(BigDecimal.valueOf(8999.00));
item.setTaxRate(BigDecimal.valueOf(13.00));
item.setDiscountRate(BigDecimal.valueOf(5.00));
item.setWeight(BigDecimal.valueOf(0.187));
item.setVolume(BigDecimal.valueOf(0.0004));
item.setImages("https://example.com/iphone15pro-1.jpg,https://example.com/iphone15pro-2.jpg");
item.setSortOrder(1);
item.setRemarks("客户要求尽快发货");
```

### 集成示例
```java
// 在订单创建服务中使用
@Service
public class OrderService {
    
    public Order createOrderWithItems(OrderRequest orderRequest, List<OrderItemRequest> items) {
        // 验证所有项目
        for (OrderItemRequest item : items) {
            validateOrderItem(item);
            calculateItemPrices(item);
        }
        
        // 创建订单并关联项目
        Order order = new Order();
        order.setItems(convertToOrderItems(items));
        
        return orderRepository.save(order);
    }
    
    private void validateOrderItem(OrderItemRequest item) {
        // 自定义验证逻辑
        if (item.getUnitPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ValidationException("商品单价必须大于0");
        }
        
        // 验证HS编码格式
        if (StringUtils.hasText(item.getHsCode()) && !isValidHsCode(item.getHsCode())) {
            throw new ValidationException("HS编码格式不正确");
        }
    }
    
    private void calculateItemPrices(OrderItemRequest item) {
        BigDecimal totalPrice = item.getUnitPrice().multiply(item.getQuantity());
        item.setTotalPrice(totalPrice);
        
        BigDecimal taxAmount = BigDecimal.ZERO;
        if (item.getTaxRate() != null) {
            taxAmount = totalPrice.multiply(item.getTaxRate()).divide(BigDecimal.valueOf(100));
            item.setTaxAmount(taxAmount);
        }
        
        BigDecimal discountAmount = BigDecimal.ZERO;
        if (item.getDiscountRate() != null) {
            discountAmount = totalPrice.multiply(item.getDiscountRate()).divide(BigDecimal.valueOf(100));
            item.setDiscountAmount(discountAmount);
        }
        
        item.setFinalPrice(totalPrice.add(taxAmount).subtract(discountAmount));
    }
}
```

### 批量创建示例
```java
// 批量创建订单项目
List<OrderItemRequest> items = Arrays.asList(
    createPhoneItem(),
    createCaseItem(),
    createChargerItem()
);

private OrderItemRequest createPhoneItem() {
    OrderItemRequest phone = new OrderItemRequest();
    phone.setName("iPhone 15 Pro");
    phone.setQuantity(BigDecimal.valueOf(1));
    phone.setUnitPrice(BigDecimal.valueOf(8999.00));
    phone.setCategoryId(1001L);
    return phone;
}
```

## 注意事项

### 使用注意事项
1. **必填字段验证**：`name`、`unit`、`quantity`、`unitPrice`为必填字段，缺少会触发验证异常
2. **数值精度**：所有金额字段使用BigDecimal类型，避免使用double/float进行金融计算
3. **税率计算**：税率以百分比形式存储，计算时需要除以100
4. **图片处理**：图片URL需要预先上传到CDN，确保URL可访问性
5. **重量体积**：重量(kg)和体积(m³)单位必须统一，用于物流费用计算

### 性能相关注意事项
1. **JSON字段限制**：`attributesJson`字段长度建议控制在4000字符以内，避免数据库性能问题
2. **图片数量**：建议单商品图片数量不超过10张，总URL长度不超过1000字符
3. **批量操作**：单次订单项目数量建议不超过100个，大量数据考虑分页处理

### 安全相关注意事项
1. **输入验证**：启用JSR-303验证注解，防止恶意数据注入
2. **XSS防护**：商品名称和描述字段需要进行HTML转义处理
3. **文件上传**：图片URL必须是白名单域名，防止SSRF攻击
4. **数据权限**：确保用户只能操作自己订单的商品项目

### 扩展说明
- **自定义属性**：通过`attributesJson`支持业务特定属性，如电子产品可包含：{"screenSize":"6.1英寸","battery":"3274mAh","color":"深空黑"}
- **多语言支持**：商品名称和描述可结合国际化配置实现多语言展示
- **价格策略**：可扩展支持阶梯价格、会员价格等复杂定价策略
- **库存校验**：可扩展库存校验逻辑，在添加项目时实时检查库存状态

---

> **文件路径**: `backend/src/main/java/com/purchase/order/dto/request/OrderItemRequest.java`
> 
> **最后更新**: 2025-01-28
> 
> **文档状态**: ✅ 已完善（无占位符）
