# SampleOrderItemResponse 文档

## 文件概述

`SampleOrderItemResponse` 是位于 `com.purchase.order.dto.response` 包中的样品订单明细响应数据传输对象(DTO)。该DTO用于封装样品订单中单个明细项的完整信息，包括样品详情、卖家信息、价格计算和时间戳等核心数据，支持前端展示样品订单的详细内容。

## 核心功能

### 主要职责
- **样品信息展示**: 提供样品名称、规格、数量、价格等核心商品信息
- **卖家身份识别**: 包含卖家ID、用户名、公司、联系方式等完整卖家信息
- **价格计算支持**: 提供样品单价、总价计算结果，支持价格展示和验证
- **状态追踪**: 记录明细项的处理状态，支持订单状态同步
- **多媒体支持**: 支持样品图片、视频、属性JSON等多媒体信息存储
- **时间追踪**: 记录创建和更新时间，支持数据审计和版本控制

### 业务特点
- **多卖家场景**: 支持同一订单包含多个卖家的样品
- **价格透明**: 明确展示每个样品的价格构成和计算逻辑
- **状态同步**: 与样品订单主表状态保持同步更新
- **国际化支持**: 时间格式采用标准格式，支持多时区展示
- **扩展性强**: 通过JSON字段支持灵活的属性扩展

## 字段说明

### 1. 基础标识字段

| 字段名 | 类型 | 说明 | 序列化配置 |
|--------|------|------|------------|
| `id` | Long | 明细项唯一标识 | `@JsonSerialize(using = ToStringSerializer.class)` |
| `sampleOrderId` | Long | 关联的样品订单ID | `@JsonSerialize(using = ToStringSerializer.class)` |
| `biddingId` | Long | 关联的样品竞价ID | `@JsonSerialize(using = ToStringSerializer.class)` |
| `sellerId` | Long | 卖家用户ID | `@JsonSerialize(using = ToStringSerializer.class)` |

### 2. 卖家信息字段

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `sellerUsername` | String | 卖家用户名 | "supplier_001" |
| `sellerAvatar` | String | 卖家头像URL | "https://example.com/avatar.jpg" |
| `sellerCompany` | String | 卖家公司名称 | "深圳科技有限公司" |
| `sellerContact` | String | 卖家联系人姓名 | "张经理" |
| `sellerPhone` | String | 卖家联系电话 | "13800138000" |

### 3. 样品商品信息

| 字段名 | 类型 | 说明 | 计算规则 |
|--------|------|------|----------|
| `productName` | String | 样品商品名称 | - |
| `sampleSpecification` | String | 样品规格描述 | - |
| `sampleQuantity` | Integer | 样品数量 | 正整数 |
| `samplePrice` | BigDecimal | 样品单价 | 精确到小数点后2位 |
| `totalPrice` | BigDecimal | 样品总价 | `samplePrice * sampleQuantity` |

### 4. 多媒体和属性信息

| 字段名 | 类型 | 说明 | 数据格式 |
|--------|------|------|----------|
| `attributesJson` | String | 样品属性JSON字符串 | `{"color":"red","size":"M"}` |
| `images` | String | 样品图片URL列表 | 逗号分隔的URL字符串 |
| `videos` | String | 样品视频URL列表 | 逗号分隔的URL字符串 |

### 5. 状态和备注

| 字段名 | 类型 | 说明 | 状态值枚举 |
|--------|------|------|------------|
| `itemStatus` | String | 明细项状态码 | "PENDING", "CONFIRMED", "SHIPPED" |
| `itemStatusText` | String | 状态文本描述 | "待确认", "已确认", "已发货" |
| `remark` | String | 备注信息 | 用户或系统备注 |

### 6. 时间戳字段

| 字段名 | 类型 | 说明 | 格式 |
|--------|------|------|------|
| `createdAt` | LocalDateTime | 创建时间 | `yyyy-MM-dd HH:mm:ss` |
| `updatedAt` | LocalDateTime | 更新时间 | `yyyy-MM-dd HH:mm:ss` |

## 使用示例

### 基础使用

#### 1. 创建响应对象
```java
SampleOrderItemResponse item = new SampleOrderItemResponse();
item.setId(1001L);
item.setSampleOrderId(5001L);
item.setBiddingId(2001L);
item.setSellerId(3001L);

// 设置卖家信息
item.setSellerUsername("优质供应商");
item.setSellerCompany("深圳市电子科技有限公司");
item.setSellerContact("李经理");
item.setSellerPhone("0755-12345678");

// 设置样品信息
item.setProductName("智能蓝牙耳机");
item.setSampleSpecification("型号: TWS-100, 颜色: 黑色, 版本: 5.0");
item.setSampleQuantity(5);
item.setSamplePrice(new BigDecimal("25.50"));
item.setTotalPrice(new BigDecimal("127.50"));

// 设置多媒体信息
item.setImages("https://example.com/image1.jpg,https://example.com/image2.jpg");
item.setAttributesJson("{\"color\":\"black\",\"version\":\"5.0\"}");

// 设置状态
item.setItemStatus("CONFIRMED");
item.setItemStatusText("已确认");
item.setRemark("客户要求加急处理");

// 设置时间戳
item.setCreatedAt(LocalDateTime.now());
item.setUpdatedAt(LocalDateTime.now());
```

#### 2. 服务层使用示例
```java
@Service
public class SampleOrderService {
    
    @Autowired
    private SampleOrderItemMapper itemMapper;
    
    public List<SampleOrderItemResponse> getOrderItems(Long sampleOrderId) {
        return itemMapper.selectBySampleOrderId(sampleOrderId)
            .stream()
            .map(this::convertToResponse)
            .collect(Collectors.toList());
    }
    
    private SampleOrderItemResponse convertToResponse(SampleOrderItem entity) {
        SampleOrderItemResponse response = new SampleOrderItemResponse();
        response.setId(entity.getId());
        response.setSampleOrderId(entity.getSampleOrderId());
        response.setBiddingId(entity.getBiddingId());
        response.setSellerId(entity.getSellerId());
        
        // 设置卖家信息
        User seller = userService.findById(entity.getSellerId());
        response.setSellerUsername(seller.getUsername());
        response.setSellerCompany(seller.getCompanyName());
        response.setSellerContact(seller.getContactPerson());
        response.setSellerPhone(seller.getPhone());
        
        // 设置商品信息
        response.setProductName(entity.getProductName());
        response.setSampleSpecification(entity.getSpecification());
        response.setSampleQuantity(entity.getQuantity());
        response.setSamplePrice(entity.getUnitPrice());
        response.setTotalPrice(entity.getTotalAmount());
        
        // 设置多媒体信息
        response.setImages(String.join(",", entity.getImageUrls()));
        response.setAttributesJson(entity.getAttributes());
        
        // 设置状态
        response.setItemStatus(entity.getStatus());
        response.setItemStatusText(getStatusText(entity.getStatus()));
        response.setRemark(entity.getRemark());
        
        // 设置时间戳
        response.setCreatedAt(entity.getCreatedAt());
        response.setUpdatedAt(entity.getUpdatedAt());
        
        return response;
    }
}
```

### 集成示例

#### 1. 控制器层响应构建
```java
@RestController
@RequestMapping("/api/sample-orders")
public class SampleOrderController {
    
    @GetMapping("/{orderId}/items")
    public ApiResponse<List<SampleOrderItemResponse>> getOrderItems(@PathVariable Long orderId) {
        try {
            List<SampleOrderItemResponse> items = sampleOrderService.getOrderItems(orderId);
            return ApiResponse.success(items);
        } catch (Exception e) {
            log.error("获取订单明细失败: {}", orderId, e);
            return ApiResponse.error("获取订单明细失败");
        }
    }
}
```

#### 2. 前端数据展示示例
```javascript
// Vue.js组件中使用
<template>
  <div class="order-items">
    <div v-for="item in orderItems" :key="item.id" class="item-card">
      <div class="item-header">
        <h4>{{ item.productName }}</h4>
        <span :class="statusClass(item.itemStatus)">
          {{ item.itemStatusText }}
        </span>
      </div>
      
      <div class="item-content">
        <div class="seller-info">
          <img :src="item.sellerAvatar" :alt="item.sellerCompany" />
          <div>
            <p>{{ item.sellerCompany }}</p>
            <p>{{ item.sellerContact }} - {{ item.sellerPhone }}</p>
          </div>
        </div>
        
        <div class="item-details">
          <p>规格: {{ item.sampleSpecification }}</p>
          <p>数量: {{ item.sampleQuantity }}</p>
          <p>单价: ¥{{ item.samplePrice }}</p>
          <p>总价: ¥{{ item.totalPrice }}</p>
        </div>
        
        <div class="item-images" v-if="item.images">
          <img v-for="img in item.images.split(',')" 
               :src="img" :key="img" class="sample-image" />
        </div>
      </div>
      
      <div class="item-footer">
        <span>创建时间: {{ formatDateTime(item.createdAt) }}</span>
        <span v-if="item.remark">备注: {{ item.remark }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      orderItems: []
    }
  },
  
  async mounted() {
    const orderId = this.$route.params.orderId;
    const response = await this.$api.get(`/sample-orders/${orderId}/items`);
    this.orderItems = response.data;
  },
  
  methods: {
    formatDateTime(dateTime) {
      return new Date(dateTime).toLocaleString('zh-CN');
    },
    
    statusClass(status) {
      const statusMap = {
        'PENDING': 'status-pending',
        'CONFIRMED': 'status-confirmed',
        'SHIPPED': 'status-shipped'
      };
      return statusMap[status] || 'status-default';
    }
  }
}
</script>
```

## 注意事项

### 使用注意事项

1. **数据精度处理**
   - 价格字段使用 `BigDecimal` 类型，避免浮点数精度问题
   - 确保所有金额计算保留2位小数，使用 `setScale(2, RoundingMode.HALF_UP)`

2. **状态同步机制**
   - 当主订单状态变更时，需要同步更新所有明细项状态
   - 建议实现状态变更监听器，自动处理状态同步

3. **图片和视频处理**
   - 图片和视频URL应进行有效性验证
   - 建议实现CDN加速，提升图片加载速度
   - 考虑实现图片压缩和水印功能

4. **JSON属性解析**
   - 属性JSON字符串需进行格式验证，防止解析异常
   - 建议提供标准化的属性模板，确保数据结构一致性

### 性能考虑

1. **数据库查询优化**
   - 建立复合索引：`(sample_order_id, seller_id)`
   - 使用分页查询避免一次性加载大量明细数据
   - 实现懒加载机制，按需加载图片和多媒体信息

2. **缓存策略**
   - 对频繁访问的卖家信息进行Redis缓存
   - 实现图片URL的CDN缓存策略
   - 考虑使用应用级缓存减少数据库查询

3. **序列化优化**
   - 使用 `@JsonSerialize` 注解确保ID字段正确序列化为字符串
   - 避免循环引用导致的序列化问题
   - 考虑使用DTO投影减少不必要字段的序列化

### 安全要求

1. **数据权限控制**
   - 确保用户只能访问其有权限查看的订单明细
   - 实现卖家信息脱敏，隐藏敏感联系方式
   - 对图片和视频URL进行访问权限验证

2. **输入验证**
   - 所有字符串字段进行长度和格式验证
   - 价格字段进行合理范围验证（不能为负数）
   - 数量字段必须为正整数

3. **XSS防护**
   - 所有用户输入内容进行HTML转义
   - JSON属性字符串进行特殊字符过滤
   - 图片URL进行白名单验证

### 扩展说明

1. **批量操作支持**
   - 可以扩展支持批量更新明细项状态
   - 实现批量价格调整和折扣计算

2. **国际化扩展**
   - 状态文本支持多语言配置
   - 时间格式根据用户时区自动调整
   - 货币符号根据用户偏好显示

3. **审计功能**
   - 增加操作日志记录，追踪价格变更历史
   - 实现版本控制，支持历史数据回溯
   - 添加操作人信息，便于责任追踪

4. **移动端适配**
   - 优化图片加载策略，支持不同屏幕尺寸
   - 实现响应式布局，适配手机和平板设备
   - 考虑离线缓存机制，提升用户体验

---

> **文件路径**: `backend/src/main/java/com/purchase/order/dto/response/SampleOrderItemResponse.java`
> 
> **最后更新**: 2025-01-27
> 
> **相关文档**: 
> - `SampleOrderResponse.md` - 样品订单响应DTO
> - `OrderDetailResponse.md` - 订单详情响应DTO
> - `SampleOrderDetailVO.md` - 样品订单详情VO
