# OrderDetailResponse.md

## 1. 文件概述

`OrderDetailResponse` 是订单模块中的一个数据传输对象（DTO），位于 `com.purchase.order.dto.response` 包中。它用于封装后端向前端返回的订单详细信息。这个DTO包含了订单的所有核心属性，包括订单基本信息、买家和卖家详情、支付和交付条款、货物详情、订单进度以及关联的订单项列表。它的设计目标是为前端提供一个全面且易于消费的订单视图，避免前端需要进行多次请求来组装订单的完整信息。

## 2. 核心功能

*   **数据聚合**: 将订单主表信息、买家/卖家信息、支付/交付信息以及订单项列表等多个相关数据源聚合到一个单一的DTO中，减少了前端的请求次数和数据处理复杂性。
*   **信息丰富**: 包含了订单的几乎所有可展示字段，包括签名URL、总价、总重量、总体积、进度信息等，为前端提供了完整的订单视图。
*   **类型安全**: 使用Java的强类型特性，确保了每个字段的数据类型符合预期。
*   **日期格式化**: 使用 `@JsonFormat` 注解，指定了日期时间字段在JSON序列化时的格式，确保了前端接收到的日期时间格式统一且易于解析。
*   **Long类型ID处理**: 使用 `@JsonSerialize(using = ToStringSerializer.class)` 将Long类型的ID序列化为字符串，避免了JavaScript处理大整数时可能出现的精度丢失问题。

## 3. 属性说明

### 3.1 订单标识与基本信息

- **`id` (Long)**: 订单ID。
- **`orderNumber` (String)**: 订单编号（业务编号）。
- **`roomId` (Long)**: 聊天室ID。
- **`orderStatus` (String)**: 订单状态（`draft`, `processing`, `completed`, `cancelled`）。

### 3.2 买家信息

- **`buyerId` (Long)**: 买家ID。
- **`buyerName` (String)**: 买家名称。
- **`buyerCompany` (String)**: 买家公司名称。
- **`buyerContact` (String)**: 买家联系人。
- **`buyerPhone` (String)**: 买家联系电话。
- **`buyerEmail` (String)**: 买家邮箱。
- **`buyerAddress` (String)**: 买家地址。
- **`buyerSignTime` (LocalDateTime)**: 买家签署时间。
- **`buyerSignatureUrl` (String)**: 买家电子签名图片URL。

### 3.3 卖家信息

- **`sellerId` (Long)**: 卖家ID。
- **`sellerName` (String)**: 卖家名称。
- **`sellerCompany` (String)**: 卖家公司名称。
- **`sellerContact` (String)**: 卖家联系人。
- **`sellerPhone` (String)**: 卖家联系电话。
- **`sellerEmail` (String)**: 卖家邮箱。
- **`sellerAddress` (String)**: 卖家地址。
- **`sellerSignTime` (LocalDateTime)**: 卖家签署时间。
- **`sellerSignatureUrl` (String)**: 卖家电子签名图片URL。

### 3.4 货物与支付信息

- **`totalPrice` (BigDecimal)**: 订单总价。
- **`totalWeight` (BigDecimal)**: 订单总重量 (kg)。
- **`totalVolume` (BigDecimal)**: 订单总体积 (m³)。
- **`cargoType` (String)**: 货物类型。
- **`paymentTerms` (String)**: 支付条款。
- **`depositAmount` (BigDecimal)**: 定金金额。
- **`finalPaymentAmount` (BigDecimal)**: 尾款金额。
- **`paymentStatus` (String)**: 支付状态（`unpaid`, `deposit_paid` 等）。
- **`paymentTime` (LocalDateTime)**: 支付时间。
- **`depositPaymentTime` (LocalDateTime)**: 定金支付时间。

### 3.5 交付与收货信息

- **`deliveryTerms` (String)**: 交付条款。
- **`expectedDeliveryTime` (Date)**: 预计交付时间。
- **`receiverName` (String)**: 收货人姓名。
- **`receiverPhone` (String)**: 收货人联系电话。
- **`receiverEmail` (String)**: 收货人邮箱。
- **`receiverAddress` (String)**: 收货地址。
- **`shippingAddress` (String)**: 发货地址。
- **`shippingContact` (String)**: 发货人姓名。
- **`shippingPhone` (String)**: 发货人电话。
- **`shippingEmail` (String)**: 发货人邮箱。

### 3.6 额外货运信息

- **`countryOfOrigin` (String)**: 生产国别。
- **`manufacturerName` (String)**: 制造厂商名称。
- **`portOfLoadingName` (String)**: 起始港口名称。
- **`portOfLoadingCode` (String)**: 起始港口 UN/LOCODE。
- **`portOfDestinationName` (String)**: 目的港口名称。
- **`portOfDestinationCode` (String)**: 目的港口 UN/LOCODE。
- **`needCertification` (String)**: 是否需要认证（`0`-不需要，`1`-需要）。
- **`needFumigation` (String)**: 是否需要熏蒸（`0`-不需要，`1`-需要）。
- **`shipmentType` (String)**: 运输类型（`LCL`-拼柜，`FCL`-整柜）。
- **`containerSize` (String)**: 货柜尺寸（仅当 `shipmentType=FCL` 时有效）。
- **`containerQty` (Integer)**: 货柜数量（仅当 `shipmentType=FCL` 时有效）。
- **`customsService` (Integer)**: 是否包含报关服务（`0`-不包含，`1`-包含）。
- **`insuranceIncluded` (Integer)**: 是否包含保险（`0`-不包含，`1`-包含）。

### 3.7 订单进度与备注

- **`progressInfo` (String)**: 进度信息。
- **`progressPercentage` (Integer)**: 进度百分比，0-100。
- **`remarks` (String)**: 订单备注。

### 3.8 时间戳与逻辑删除

- **`createdAt` (LocalDateTime)**: 创建时间。
- **`updatedAt` (LocalDateTime)**: 更新时间。
- **`completedAt` (LocalDateTime)**: 订单完成时间。
- **`deleted` (String)**: 是否删除（`0`-未删除，`1`-已删除）。

### 3.9 订单项目列表

- **`items` (List<OrderItem>)**: 订单项目列表。每个 `OrderItem` 包含了商品ID、数量、单价等信息。

## 4. 业务规则

*   **数据聚合**: `OrderDetailResponse` 的数据通常需要通过多表联查或多次服务调用来组装，例如从 `UnifiedOrder` 表获取基本信息，从 `User` 表获取买家/卖家名称，从 `OrderItem` 表获取订单项列表。
*   **状态映射**: 订单状态和支付状态等字段通常是枚举值，在转换为DTO时，应确保与前端约定的字符串值一致。
*   **时间格式**: 所有日期时间字段都通过 `@JsonFormat` 进行了格式化，确保前端接收到的是可读性强且易于解析的字符串。

## 5. 使用示例

```java
// 1. 在 OrderService 中构建 OrderDetailResponse
@Service
public class OrderServiceImpl implements OrderService {
    @Autowired
    private UnifiedOrderMapper unifiedOrderMapper;
    @Autowired
    private OrderItemMapper orderItemMapper;
    @Autowired
    private UserService userService; // 假设有用户服务来获取用户名称

    public OrderDetailResponse getOrderDetail(Long orderId) {
        UnifiedOrder order = unifiedOrderMapper.selectById(orderId);
        if (order == null) {
            return null; // 或者抛出异常
        }

        OrderDetailResponse response = new OrderDetailResponse();
        // BeanUtils.copyProperties(order, response); // 简化赋值
        // ... 手动或通过工具将 UnifiedOrder 实体转换为 OrderDetailResponse DTO ...
        response.setId(order.getId());
        response.setOrderNumber(order.getOrderNumber());
        response.setRoomId(order.getRoomId());
        response.setOrderStatus(order.getOrderStatus());
        response.setBuyerId(order.getBuyerId());
        response.setSellerId(order.getSellerId());
        response.setTotalPrice(order.getTotalPrice());
        response.setPaymentTerms(order.getPaymentTerms());
        response.setExpectedDeliveryTime(order.getExpectedDeliveryTime());
        response.setShippingAddress(order.getShippingAddress());
        response.setCreatedAt(order.getCreatedAt());
        response.setUpdatedAt(order.getUpdatedAt());
        response.setCompletedAt(order.getCompletedAt());
        response.setDeleted(order.getDeleted());

        // 获取买家和卖家名称
        response.setBuyerName(userService.getUserNameById(order.getBuyerId()));
        response.setSellerName(userService.getUserNameById(order.getSellerId()));

        // 获取订单项列表
        QueryWrapper<OrderItem> itemQueryWrapper = new QueryWrapper<>();
        itemQueryWrapper.eq("order_id", orderId);
        List<OrderItem> items = orderItemMapper.selectList(itemQueryWrapper);
        response.setItems(items);

        return response;
    }
}

// 2. 在 Controller 中返回订单详情
@RestController
@RequestMapping("/api/v1/orders")
public class OrderController {
    @Autowired
    private OrderService orderService;

    @GetMapping("/{orderId}")
    public Result<OrderDetailResponse> getOrderById(@PathVariable Long orderId) {
        OrderDetailResponse response = orderService.getOrderDetail(orderId);
        if (response == null) {
            return Result.error("订单不存在");
        }
        return Result.success(response);
    }
}

// 3. 前端 (React) 接收并展示订单详情
/*
import React, { useEffect, useState } from 'react';
import axios from 'axios';

function OrderDetail({ orderId }) {
  const [order, setOrder] = useState(null);

  useEffect(() => {
    axios.get(`/api/v1/orders/${orderId}`)
      .then(response => {
        if (response.data.success) {
          setOrder(response.data.data);
        } else {
          console.error('获取订单详情失败:', response.data.message);
        }
      })
      .catch(error => {
        console.error('请求失败:', error);
      });
  }, [orderId]);

  if (!order) {
    return <div>加载中...</div>;
  }

  return (
    <div>
      <h1>订单详情 - {order.orderNumber}</h1>
      <p>买家: {order.buyerName} ({order.buyerCompany})</p>
      <p>总价: {order.totalPrice}</p>
      <h2>订单项:</h2>
      <ul>
        {order.items.map(item => (
          <li key={item.id}>{item.productName} x {item.quantity} @ {item.unitPrice}</li>
        ))}
      </ul>
      {/* ... 更多订单详情展示 ... */}
    </div>
  );
}
*/

// 4. 测试示例
@SpringBootTest
class OrderDetailResponseTest {
    @Test
    void testJsonSerialization() throws JsonProcessingException {
        OrderDetailResponse response = new OrderDetailResponse();
        response.setId(1234567890123456789L); // 大Long值
        response.setOrderNumber("TEST001");
        response.setCreatedAt(LocalDateTime.of(2024, 7, 27, 10, 30, 0));
        response.setExpectedDeliveryTime(Date.from(Instant.now()));

        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule()); // 注册Java 8 Date/Time模块
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        String json = mapper.writeValueAsString(response);
        System.out.println(json);

        // 验证ID是否被序列化为字符串
        assertThat(json).contains("\"id\":\"1234567890123456789\"");
        // 验证日期格式
        assertThat(json).contains("\"createdAt\":\"2024-07-27 10:30:00\"");
        // 验证Date类型日期格式
        assertThat(json).containsPattern("\"expectedDeliveryTime\":\"\\d{4}-\\d{2}-\\d{2}\"");
    }
}
```

## 6. 注意事项

*   **DTO与实体分离**: `OrderDetailResponse` 是一个DTO，与数据库实体 `UnifiedOrder` 和 `OrderItem` 保持分离。这使得前端可以根据需要获取数据，而无需了解后端数据库结构。
*   **数据聚合逻辑**: 在服务层构建 `OrderDetailResponse` 时，需要从多个数据源（主订单、订单项、用户信息等）获取数据并进行组装。这可能涉及多表联查或多次数据库查询，需要注意性能优化。
*   **Long类型ID精度**: `@JsonSerialize(using = ToStringSerializer.class)` 是处理Long类型ID在JavaScript中精度丢失问题的标准做法，确保前端能够正确处理大整数。
*   **日期时间格式**: `@JsonFormat` 注解确保了日期时间字段以统一且可读的格式返回给前端，避免了前端自行格式化的麻烦。
*   **可扩展性**: 如果未来订单详情需要展示更多信息，只需在此DTO中添加相应属性即可，无需修改核心业务逻辑。
*   **安全性**: 尽管是响应DTO，但仍需注意不要暴露敏感信息，例如内部系统ID、密码等。
*   **性能**: 对于复杂的订单详情查询，服务层应考虑使用MyBatis的 `resultMap` 进行多表联查，或者使用 `CompletableFuture` 进行异步查询，以提高响应速度。