# BatchOperationResult 文档

## 文件概述

`BatchOperationResult` 是位于 `com.purchase.order.dto` 包中的批量操作结果统计数据传输对象，专门用于封装和传递批量处理操作的执行结果统计信息。该DTO通过简洁的三字段设计，为各类批量操作（如批量导入、批量更新、批量删除等）提供标准化的结果反馈机制，支持Builder模式构建，确保代码的可读性和易用性。

## 核心功能

### 主要职责
- **操作结果统计**：精确记录批量操作的总数、成功数和失败数，提供完整的操作执行统计
- **标准化反馈**：为所有批量操作提供统一的结果返回格式，简化API接口设计
- **数据完整性验证**：通过三个字段的数学关系验证（总数=成功数+失败数），确保结果数据的准确性
- **构建器模式支持**：提供流畅的Builder API，简化对象创建过程，提高代码可读性

### 业务特点
- **轻量级设计**：仅包含三个核心统计字段，避免不必要的复杂性
- **通用性强**：适用于任何批量操作场景，不依赖具体业务逻辑
- **不可变性**：通过Builder模式构建后，字段值不可修改，保证线程安全
- **自验证能力**：通过字段间的数学关系可自动验证数据一致性
- **零依赖**：除Lombok外无外部依赖，便于在不同模块间共享使用

## 接口说明

### 核心字段

| 字段名 | 类型 | 约束 | 业务含义 | 取值范围 |
|--------|------|------|----------|----------|
| `totalCount` | Integer | 必填 ≥ 0 | 操作总记录数 | 0 到 Integer.MAX_VALUE |
| `successCount` | Integer | 必填 ≥ 0 | 成功处理记录数 | 0 到 totalCount |
| `failureCount` | Integer | 必填 ≥ 0 | 失败处理记录数 | 0 到 totalCount |

### 字段关系约束
- **数学验证**：`totalCount = successCount + failureCount` 必须成立
- **非负约束**：所有计数字段必须大于等于0
- **上限约束**：成功数和失败数不能超过总数

### Builder构建器

#### BatchOperationResultBuilder 方法
| 方法名 | 参数 | 返回值 | 描述 |
|--------|------|--------|------|
| `totalCount(Integer)` | 操作总数 | BatchOperationResultBuilder | 设置总操作数 |
| `successCount(Integer)` | 成功数 | BatchOperationResultBuilder | 设置成功操作数 |
| `failureCount(Integer)` | 失败数 | BatchOperationResultBuilder | 设置失败操作数 |
| `build()` | 无 | BatchOperationResult | 构建最终对象 |

## 使用示例

### 基础使用

#### 场景1：批量订单状态更新结果
```java
// 批量更新100个订单的状态
public BatchOperationResult updateOrderStatusBatch(List<Long> orderIds, OrderStatus newStatus) {
    int total = orderIds.size();
    int success = 0;
    int failure = 0;
    
    for (Long orderId : orderIds) {
        try {
            orderService.updateStatus(orderId, newStatus);
            success++;
        } catch (Exception e) {
            failure++;
            log.error("更新订单{}状态失败: {}", orderId, e.getMessage());
        }
    }
    
    return BatchOperationResult.builder()
            .totalCount(total)
            .successCount(success)
            .failureCount(failure)
            .build();
}
```

#### 场景2：批量导入商品结果
```java
// 批量导入商品信息
public BatchOperationResult importProducts(List<ProductImportDTO> products) {
    AtomicInteger success = new AtomicInteger(0);
    AtomicInteger failure = new AtomicInteger(0);
    
    products.parallelStream().forEach(product -> {
        try {
            productService.createProduct(product);
            success.incrementAndGet();
        } catch (ValidationException e) {
            failure.incrementAndGet();
            log.warn("商品导入失败: {}", e.getMessage());
        }
    });
    
    return BatchOperationResult.builder()
            .totalCount(products.size())
            .successCount(success.get())
            .failureCount(failure.get())
            .build();
}
```

### 集成示例

#### 场景1：REST API批量操作返回
```java
@RestController
@RequestMapping("/api/orders")
public class OrderController {
    
    @PostMapping("/batch-cancel")
    public ResponseEntity<ApiResponse<BatchOperationResult>> batchCancelOrders(@RequestBody List<Long> orderIds) {
        BatchOperationResult result = orderService.cancelOrdersBatch(orderIds);
        
        if (result.getFailureCount() == 0) {
            return ResponseEntity.ok(ApiResponse.success(result));
        } else {
            return ResponseEntity.ok(ApiResponse.partialSuccess(result, "部分订单取消失败"));
        }
    }
}
```

#### 场景2：带失败详情的扩展使用
```java
// 扩展DTO包含失败详情
@Data
public class DetailedBatchOperationResult extends BatchOperationResult {
    private List<String> failureDetails;
    
    public static DetailedBatchOperationResultBuilder detailedBuilder() {
        return new DetailedBatchOperationResultBuilder();
    }
    
    public static class DetailedBatchOperationResultBuilder {
        // 扩展Builder实现...
    }
}

// 使用示例
public DetailedBatchOperationResult deleteUsersWithDetails(List<Long> userIds) {
    List<String> failureDetails = new ArrayList<>();
    int success = 0;
    
    for (Long userId : userIds) {
        try {
            userService.deleteUser(userId);
            success++;
        } catch (UserNotFoundException e) {
            failureDetails.add("用户" + userId + "不存在");
        } catch (UserHasActiveOrdersException e) {
            failureDetails.add("用户" + userId + "有未完成订单");
        }
    }
    
    return DetailedBatchOperationResult.detailedBuilder()
            .totalCount(userIds.size())
            .successCount(success)
            .failureCount(failureDetails.size())
            .failureDetails(failureDetails)
            .build();
}
```

#### 场景3：异步批量任务结果
```java
@Service
public class AsyncBatchService {
    
    @Async
    public CompletableFuture<BatchOperationResult> asyncProcessOrders(List<Order> orders) {
        return CompletableFuture.supplyAsync(() -> {
            int total = orders.size();
            int success = 0;
            int failure = 0;
            
            for (Order order : orders) {
                try {
                    processOrderAsync(order);
                    success++;
                } catch (Exception e) {
                    failure++;
                    log.error("异步处理订单失败: {}", order.getId(), e);
                }
            }
            
            return BatchOperationResult.builder()
                    .totalCount(total)
                    .successCount(success)
                    .failureCount(failure)
                    .build();
        });
    }
}
```

## 注意事项

### 使用注意事项
1. **数据一致性**：确保 `totalCount = successCount + failureCount` 的数学关系始终成立
2. **并发安全**：虽然DTO本身线程安全，但计数过程需要在业务逻辑中保证原子性
3. **空值处理**：所有计数字段默认为null，使用builder构建时必须显式设置所有值
4. **性能考虑**：大批量操作时建议使用原子类或并发集合进行计数
5. **错误处理**：建议记录失败详情，便于后续问题排查

### 性能相关注意事项
1. **内存占用**：DTO实例非常轻量，可放心创建大量实例
2. **构建效率**：Builder模式避免了setter方法调用，提高了对象创建效率
3. **GC优化**：短生命周期的DTO实例对GC友好，不会造成内存压力
4. **批量大小**：建议单批次操作数量控制在合理范围内（如1000条以内）

### 安全相关注意事项
1. **输入验证**：确保传入的计数值非负，防止数据异常
2. **溢出保护**：对于超大规模数据，考虑使用Long类型防止整数溢出
3. **权限控制**：批量操作结果应包含操作权限验证信息
4. **敏感数据**：避免在失败详情中包含敏感信息

### 扩展说明
- **百分比计算**：可扩展增加成功率和失败率的计算方法
- **时间统计**：可扩展增加操作开始时间和结束时间字段
- **进度跟踪**：结合前端进度条，可扩展支持实时进度反馈
- **错误分类**：可扩展按错误类型分类统计失败数量
- **性能监控**：可扩展记录操作耗时、平均处理时间等性能指标

---

> **文件路径**: `backend/src/main/java/com/purchase/order/dto/BatchOperationResult.java`
> 
> **最后更新**: 2025-01-28
> 
> **文档状态**: ✅ 已完善（无占位符）
