ji# SampleOrderResponse

## 文件概述

`SampleOrderResponse` 是位于 `com.purchase.order.dto.response` 包中的数据传输对象(DTO)，专门用于封装样品订单的完整响应信息。该类作为订单查询、列表展示、详情查看等API接口的统一响应格式，为前端提供标准化的数据结构。

### 架构位置
- **层级**: 表现层/接口层
- **作用**: 订单服务 → 前端API响应的数据载体
- **关联**: 与 `SampleOrderItemResponse` 组合使用，构成完整的订单响应

### 设计思路
- **完整信息**: 包含订单全生命周期所需的全部信息
- **扁平化设计**: 将买家、管理员、物流等信息内嵌，减少API调用次数
- **类型安全**: 使用强类型字段和精确的JSON序列化配置
- **前后端兼容**: 通过 `@JsonSerialize` 处理大数字精度问题

## 核心功能

### 主要字段分类

#### 订单基础信息
- `id`: 订单唯一标识，使用String序列化避免前端精度丢失
- `orderNumber`: 业务订单号，用户可见的订单标识
- `requirementId`: 关联的样品需求ID，建立订单与需求的关联关系
- `requirementTitle`: 需求标题，便于快速识别订单来源

#### 买家信息聚合
- 基本信息: `buyerId`, `buyerUsername`, `buyerAvatar`
- 联系信息: `buyerCompany`, `buyerContact`, `buyerPhone`, `buyerEmail`
- 地址信息: `buyerAddress` - 收货地址

#### 订单状态与金额
- `orderStatus`: 订单状态枚举值（如：PENDING, PROCESSING, SHIPPED等）
- `orderStatusText`: 状态文案，直接用于前端展示
- `totalSamplePrice`: 样品总价（不含运费）
- `shippingFee`: 运费金额
- `totalAmount`: 订单总金额（样品价+运费）

#### 管理员处理信息
- `adminId`: 处理该订单的管理员ID
- `adminName`: 管理员姓名
- `adminRemark`: 管理员备注信息
- `adminProcessedAt`: 管理员处理时间

#### 物流信息
- `shippingMethod`: 配送方式（如：快递、物流、自提）
- `trackingNumber`: 物流单号
- `shippedAt`: 发货时间
- `estimatedDeliveryDate`: 预计送达日期
- `actualDeliveryDate`: 实际送达日期

#### 时间轴信息
- `createdAt`: 订单创建时间
- `updatedAt`: 最后更新时间
- `completedAt`: 订单完成时间

#### 订单明细
- `items`: 订单项目列表，类型为 `List<SampleOrderItemResponse>`

## 接口说明

### JSON序列化配置

#### 大数字处理
```java
@JsonSerialize(using = ToStringSerializer.class)
private Long id;
```
- **目的**: 防止JavaScript大数字精度丢失
- **效果**: Long类型序列化为String，前端接收为字符串

#### 日期时间格式
```java
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
private LocalDateTime createdAt;
```
- **格式**: 标准ISO日期时间格式
- **时区**: 使用系统默认时区
- **用途**: 确保前后端时间格式一致性

### 使用场景

#### 1. 订单详情查询
```java
// Controller层使用示例
@GetMapping("/{id}")
public ApiResponse<SampleOrderResponse> getOrderDetail(@PathVariable Long id) {
    SampleOrderResponse response = sampleOrderService.getOrderDetail(id);
    return ApiResponse.success(response);
}
```

#### 2. 订单列表查询
```java
// 分页查询使用示例
@GetMapping("/list")
public ApiResponse<PageResult<SampleOrderResponse>> getOrderList(
    @RequestParam(defaultValue = "1") int page,
    @RequestParam(defaultValue = "10") int size) {
    
    PageResult<SampleOrderResponse> result = sampleOrderService.getOrderList(page, size);
    return ApiResponse.success(result);
}
```

## 使用示例

### 完整API响应示例

#### 成功响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "1234567890123456789",
    "orderNumber": "SO202412250001",
    "requirementId": "987654321098765432",
    "requirementTitle": "高精度机械零件样品需求",
    "buyerId": "111222333444555666",
    "buyerUsername": "industry_buyer_2024",
    "buyerAvatar": "https://example.com/avatars/user123.jpg",
    "buyerCompany": "精密制造有限公司",
    "buyerContact": "张经理",
    "buyerPhone": "13800138000",
    "buyerEmail": "<EMAIL>",
    "buyerAddress": "广东省深圳市南山区科技园",
    "orderStatus": "SHIPPED",
    "orderStatusText": "已发货",
    "totalSamplePrice": 2580.50,
    "shippingFee": 25.00,
    "totalAmount": 2605.50,
    "adminId": "999888777666555444",
    "adminName": "李客服",
    "adminRemark": "已确认库存，安排今日发货",
    "adminProcessedAt": "2024-12-25 14:30:00",
    "shippingMethod": "顺丰速运",
    "trackingNumber": "SF1234567890123",
    "shippedAt": "2024-12-25 16:45:00",
    "estimatedDeliveryDate": "2024-12-27",
    "actualDeliveryDate": "2024-12-27",
    "createdAt": "2024-12-25 10:15:00",
    "updatedAt": "2024-12-25 16:45:00",
    "completedAt": "2024-12-27 09:20:00",
    "items": [
      {
        "id": "555666777888999000",
        "productName": "精密轴承样品",
        "specification": "6204-2RS",
        "quantity": 10,
        "unitPrice": 258.05,
        "totalPrice": 2580.50
      }
    ]
  }
}
```

### 构建响应对象示例

#### Service层构建示例
```java
public SampleOrderResponse buildSampleOrderResponse(SampleOrder order) {
    SampleOrderResponse response = new SampleOrderResponse();
    
    // 基础信息
    response.setId(order.getId());
    response.setOrderNumber(order.getOrderNumber());
    response.setRequirementId(order.getRequirementId());
    
    // 买家信息
    Buyer buyer = buyerService.getById(order.getBuyerId());
    response.setBuyerId(buyer.getId());
    response.setBuyerUsername(buyer.getUsername());
    response.setBuyerCompany(buyer.getCompanyName());
    response.setBuyerContact(buyer.getContactPerson());
    response.setBuyerPhone(buyer.getPhone());
    response.setBuyerEmail(buyer.getEmail());
    response.setBuyerAddress(order.getShippingAddress());
    
    // 金额信息
    response.setTotalSamplePrice(order.getTotalSamplePrice());
    response.setShippingFee(order.getShippingFee());
    response.setTotalAmount(order.getTotalAmount());
    
    // 状态信息
    response.setOrderStatus(order.getStatus());
    response.setOrderStatusText(OrderStatus.getDisplayText(order.getStatus()));
    
    // 时间信息
    response.setCreatedAt(order.getCreatedAt());
    response.setUpdatedAt(order.getUpdatedAt());
    response.setCompletedAt(order.getCompletedAt());
    
    // 订单明细
    List<SampleOrderItemResponse> items = orderItemService.getItemsByOrderId(order.getId());
    response.setItems(items);
    
    return response;
}
```

## 注意事项

### 序列化注意事项

#### 1. 大数字精度
- **问题**: JavaScript无法精确处理超过2^53-1的整数
- **解决**: 使用 `@JsonSerialize(using = ToStringSerializer.class)` 将Long转为String
- **影响**: 前端接收时需要将字符串转换回数字类型

#### 2. 日期时间处理
- **格式一致性**: 所有日期时间字段使用统一格式 `yyyy-MM-dd HH:mm:ss`
- **时区处理**: 确保服务器和数据库时区配置一致
- **空值处理**: 空日期字段返回null，前端需做空值判断

### 性能优化建议

#### 1. 延迟加载
- **问题**: 订单详情可能包含大量关联数据
- **建议**: 使用延迟加载或选择性加载策略
- **实现**: 根据查询参数决定加载哪些关联数据

#### 2. 缓存策略
- **买家信息缓存**: 买家信息变化频率低，可缓存30分钟
- **订单状态缓存**: 订单状态变更后及时更新缓存
- **实现**: 使用Redis缓存常用查询结果

#### 3. 分页处理
- **列表查询**: 避免一次性加载所有订单数据
- **建议**: 使用游标分页或传统分页
- **实现**: 基于时间戳的游标分页提升性能

### 数据安全

#### 1. 敏感信息脱敏
- **手机号**: 中间四位用*号替换（138****8000）
- **邮箱**: 隐藏部分字符（zhang@p***-mfg.com）
- **实现**: 在DTO构建时进行数据脱敏处理

#### 2. 权限控制
- **数据权限**: 确保用户只能查看自己的订单
- **实现**: Service层添加用户ID校验
- **SQL**: WHERE buyer_id = #{currentUserId}

### 扩展开发指南

#### 1. 新增字段
- **兼容性**: 新增字段必须为可空类型或使用默认值
- **API版本**: 重大变更需考虑API版本控制
- **文档**: 同步更新API文档和示例

#### 2. 状态扩展
- **状态机**: 使用枚举管理订单状态，便于扩展
- **状态转换**: 明确定义状态转换规则和权限
- **通知机制**: 状态变更时触发相应的通知

#### 3. 多语言支持
- **状态文案**: 将orderStatusText改为支持多语言
- **实现**: 使用国际化资源文件
- **格式**: {lang}.properties 文件管理文案

---

**文件路径**: `backend/src/main/java/com/purchase/order/dto/response/SampleOrderResponse.java`

**最后更新**: 2024-12-25
