# OrderSimpleResponse.md

## 1. 文件概述

`OrderSimpleResponse` 是订单模块中的一个数据传输对象（DTO），位于 `com.purchase.order.dto.response` 包中。它用于封装后端向前端返回的订单简要信息。与 `OrderDetailResponse` 不同，`OrderSimpleResponse` 专注于提供订单列表或概览视图所需的核心字段，例如订单ID、订单编号、买家/卖家联系人以及总金额。它的设计目标是为前端提供一个轻量级、高效的订单概览视图，避免在不需要详细信息时传输大量冗余数据。

## 2. 核心功能

*   **数据精简**: 只包含订单列表或概览视图所需的关键字段，减少了网络传输的数据量，提高了API响应速度。
*   **信息概览**: 提供了订单的基本识别信息（ID、编号）和核心业务信息（买家/卖家联系人、总金额），便于用户快速浏览和识别订单。
*   **类型安全**: 使用Java的强类型特性，确保了每个字段的数据类型符合预期。
*   **Long类型ID处理**: 使用 `@JsonSerialize(using = ToStringSerializer.class)` 将Long类型的ID序列化为字符串，避免了JavaScript处理大整数时可能出现的精度丢失问题。

## 3. 属性说明

- **`id` (Long)**: 订单ID。用于唯一标识订单。
- **`orderNumber` (String)**: 订单编号。业务上的唯一标识。
- **`buyerContact` (String)**: 买家联系人姓名。
- **`sellerContact` (String)**: 卖家联系人姓名。
- **`totalPrice` (BigDecimal)**: 订单的总金额。

## 4. 业务规则

*   **数据来源**: `OrderSimpleResponse` 的数据通常来源于 `UnifiedOrder` 实体，可能需要从关联的用户表中获取买家和卖家的联系人信息。
*   **轻量级**: 该DTO的设计理念是轻量级，因此不应包含大量嵌套对象或复杂字段，以保持其简洁性。

## 5. 使用示例

```java
// 1. 在 OrderService 中构建 OrderSimpleResponse 列表
@Service
public class OrderServiceImpl implements OrderService {
    @Autowired
    private UnifiedOrderMapper unifiedOrderMapper;
    @Autowired
    private UserService userService; // 假设有用户服务来获取联系人信息

    public List<OrderSimpleResponse> getSimpleOrderList(Long userId, String role, int page, int size) {
        // 假设这里有一个分页查询，获取 UnifiedOrder 实体列表
        // List<UnifiedOrder> orders = unifiedOrderMapper.selectPageByUserIdAndRole(page, size, userId, role);
        
        // 模拟数据
        List<UnifiedOrder> orders = List.of(
            createMockOrder(1L, "ORD001", 100L, 200L, new BigDecimal("1000.00")), 
            createMockOrder(2L, "ORD002", 100L, 201L, new BigDecimal("2500.00"))
        );

        return orders.stream().map(order -> {
            OrderSimpleResponse response = new OrderSimpleResponse();
            response.setId(order.getId());
            response.setOrderNumber(order.getOrderNumber());
            // 从用户服务获取联系人姓名
            response.setBuyerContact(userService.getUserContactName(order.getBuyerId()));
            response.setSellerContact(userService.getUserContactName(order.getSellerId()));
            response.setTotalPrice(order.getTotalPrice());
            return response;
        }).collect(Collectors.toList());
    }

    private UnifiedOrder createMockOrder(Long id, String orderNumber, Long buyerId, Long sellerId, BigDecimal totalPrice) {
        UnifiedOrder order = new UnifiedOrder();
        order.setId(id);
        order.setOrderNumber(orderNumber);
        order.setBuyerId(buyerId);
        order.setSellerId(sellerId);
        order.setTotalPrice(totalPrice);
        return order;
    }
}

// 2. 在 Controller 中返回简要订单列表
@RestController
@RequestMapping("/api/v1/orders")
public class OrderController {
    @Autowired
    private OrderService orderService;

    @GetMapping("/simple-list")
    public Result<List<OrderSimpleResponse>> getSimpleOrders(
            @RequestParam Long userId,
            @RequestParam String role,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        List<OrderSimpleResponse> response = orderService.getSimpleOrderList(userId, role, page, size);
        return Result.success(response);
    }
}

// 3. 前端 (React) 接收并展示订单列表
/*
import React, { useEffect, useState } from 'react';
import axios from 'axios';

function OrderList() {
  const [orders, setOrders] = useState([]);

  useEffect(() => {
    // 假设当前用户ID和角色
    const userId = 100L;
    const role = "BUYER";
    axios.get(`/api/v1/orders/simple-list?userId=${userId}&role=${role}`)
      .then(response => {
        if (response.data.success) {
          setOrders(response.data.data);
        } else {
          console.error('获取订单列表失败:', response.data.message);
        }
      })
      .catch(error => {
        console.error('请求失败:', error);
      });
  }, []);

  return (
    <div>
      <h1>我的订单</h1>
      <ul>
        {orders.map(order => (
          <li key={order.id}>
            订单号: {order.orderNumber}, 买家: {order.buyerContact}, 卖家: {order.sellerContact}, 总价: {order.totalPrice}
          </li>
        ))}
      </ul>
    </div>
  );
}
*/

// 4. 测试示例
@SpringBootTest
class OrderSimpleResponseTest {
    @Test
    void testJsonSerialization() throws JsonProcessingException {
        OrderSimpleResponse response = new OrderSimpleResponse();
        response.setId(1234567890123456789L); // 大Long值
        response.setOrderNumber("ORD-TEST-001");
        response.setBuyerContact("张三");
        response.setSellerContact("李四");
        response.setTotalPrice(new BigDecimal("999.99"));

        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(response);
        System.out.println(json);

        // 验证ID是否被序列化为字符串
        assertThat(json).contains("\"id\":\"1234567890123456789\"");
        assertThat(json).contains("\"orderNumber\":\"ORD-TEST-001\"");
        assertThat(json).contains("\"buyerContact\":\"张三\"");
        assertThat(json).contains("\"sellerContact\":\"李四\"");
        assertThat(json).contains("\"totalPrice\":999.99");
    }
}
```

## 6. 注意事项

*   **DTO与实体分离**: `OrderSimpleResponse` 是一个DTO，与数据库实体 `UnifiedOrder` 保持分离。它只包含前端列表展示所需的最少信息。
*   **数据聚合逻辑**: 在服务层构建 `OrderSimpleResponse` 时，需要从 `UnifiedOrder` 实体以及可能关联的用户表中获取数据并进行组装。这可能涉及联查或多次查询，需要注意性能优化。
*   **Long类型ID精度**: `@JsonSerialize(using = ToStringSerializer.class)` 是处理Long类型ID在JavaScript中精度丢失问题的标准做法，确保前端能够正确处理大整数。
*   **性能优化**: 由于 `OrderSimpleResponse` 通常用于列表展示，其查询量可能非常大。因此，在服务层构建此DTO时，应特别关注数据库查询的效率，例如使用MyBatis的 `resultMap` 进行联查，或者在SQL层面直接构建所需字段。
*   **可扩展性**: 如果未来订单列表需要展示更多简要信息，只需在此DTO中添加相应属性即可，无需修改核心业务逻辑。
*   **安全性**: 尽管是响应DTO，但仍需注意不要暴露敏感信息。