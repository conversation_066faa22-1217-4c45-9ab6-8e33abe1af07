# Settlement 结算模块文档

## 模块概述

结算模块是采购系统的重要业务模块，负责管理采购订单和货代订单的综合结算业务。该模块整合了采购、运输、支付等多个环节的数据，提供完整的端到端结算解决方案，支持复杂的多方结算场景。

## 目录结构概览

```
com.purchase.settlement/
├── controller/                    # 控制器层
│   └── PurchaseForwardingSettlementController.java  # 采购货运结算控制器
├── dto/                          # 数据传输对象
│   └── PurchaseForwardingSettlementDTO.java        # 采购货运结算DTO
├── entity/                       # 实体类
│   └── PurchaseForwardingSettlement.java           # 采购货运结算实体
├── mapper/                       # 数据访问层
│   └── PurchaseForwardingSettlementMapper.java     # 采购货运结算Mapper
├── service/                      # 服务层
│   ├── PurchaseForwardingSettlementService.java    # 采购货运结算服务接口
│   └── impl/                                       # 服务实现
└── converter/                    # 转换器
    └── SettlementConverter.java                    # 结算数据转换器
```

## 核心功能详述

### 1. 采购货运结算 (PurchaseForwardingSettlement)

#### 主要功能
- **综合结算**: 整合采购订单和货代订单的结算信息
- **多方管理**: 管理买家、卖家、货代三方的详细信息
- **支付管理**: 支持定金和尾款的分期支付模式
- **状态跟踪**: 结算状态和支付状态的独立管理
- **物流集成**: 包含完整的运输和港口信息

#### 核心文件
- `PurchaseForwardingSettlement.java`: 采购货运结算实体
- `PurchaseForwardingSettlementService.java`: 结算服务接口
- `PurchaseForwardingSettlementController.java`: 结算REST API
- `PurchaseForwardingSettlementDTO.java`: 结算数据传输对象

#### 业务流程
1. **结算创建**: 基于采购订单和货代订单创建综合结算单
2. **信息完善**: 补充三方（买家、卖家、货代）详细信息
3. **支付处理**: 处理定金和尾款的分期支付
4. **状态跟踪**: 跟踪结算和支付状态变化
5. **结算完成**: 所有支付完成后结算单完成

### 2. 数据转换 (Converter)

#### 转换功能
- **实体转换**: Entity与DTO之间的数据转换
- **格式处理**: 日期、金额等格式的标准化
- **数据映射**: 复杂对象的映射和转换
- **验证集成**: 转换过程中的数据验证

#### 核心文件
- `SettlementConverter.java`: 结算数据转换器

## 数据模型说明

### PurchaseForwardingSettlement 采购货运结算

#### 核心字段
- **基础信息**: id, settlementNumber, unifiedOrderId, forwarderOrderId
- **金额信息**: settlementAmount, purchaseAmount, freightAmount, depositAmount, finalPaymentAmount
- **贸易条款**: paymentTerms, deliveryTerms
- **港口信息**: portOfLoadingName, portOfLoadingCode, portOfDestinationName, portOfDestinationCode
- **运输配置**: shippingMethod, shipmentType, containerSize, containerQty
- **增值服务**: insuranceIncluded, customsService, needCertification, needFumigation
- **时间信息**: etd, eta, depositPaymentTime, finalPaymentTime

#### 三方信息
- **买家信息**: buyerId, buyerCompany, buyerContact, buyerPhone, buyerEmail, buyerAddress
- **卖家信息**: sellerId, sellerCompany, sellerContact, sellerPhone, sellerEmail, sellerAddress
- **货代信息**: forwarderId, forwarderCompany, forwarderContact, forwarderPhone, forwarderEmail, forwarderAddress

#### 状态定义
- **结算状态**: pending(待处理), processing(处理中), completed(已完成), cancelled(已取消)
- **支付状态**: unpaid(未支付), deposit_paid(定金已支付), final_paid(尾款已支付), full_paid(全款已支付)

## API 接口概览

### 结算管理接口
- `POST /api/v1/settlements` - 创建结算单
- `GET /api/v1/settlements` - 获取结算单列表
- `GET /api/v1/settlements/{id}` - 获取结算单详情
- `PUT /api/v1/settlements/{id}` - 更新结算单
- `PUT /api/v1/settlements/{id}/status` - 更新结算状态

### 支付管理接口
- `PUT /api/v1/settlements/{id}/deposit-payment` - 处理定金支付
- `PUT /api/v1/settlements/{id}/final-payment` - 处理尾款支付
- `PUT /api/v1/settlements/{id}/payment-status` - 更新支付状态
- `GET /api/v1/settlements/{id}/payment-history` - 获取支付历史

### 查询统计接口
- `GET /api/v1/settlements/by-buyer/{buyerId}` - 按买家查询结算单
- `GET /api/v1/settlements/by-seller/{sellerId}` - 按卖家查询结算单
- `GET /api/v1/settlements/by-forwarder/{forwarderId}` - 按货代查询结算单
- `GET /api/v1/settlements/statistics` - 获取结算统计数据

## 业务规则

### 结算创建规则
1. 结算单必须基于有效的采购订单和货代订单
2. 结算总金额应等于采购金额加货运金额
3. 定金和尾款之和应等于结算总金额
4. 三方信息必须完整填写

### 支付流程规则
1. 支付通常按照定金→尾款的顺序进行
2. 定金比例通常为总金额的30%-50%
3. 支付状态变更需要相应的支付凭证
4. 支付完成后不能随意修改金额

### 状态流转规则
1. 结算状态：pending → processing → completed
2. 支付状态：unpaid → deposit_paid → final_paid
3. 已完成的结算单不能修改核心信息
4. 取消的结算单需要处理退款

### 权限控制规则
1. 买家只能查看自己相关的结算单
2. 卖家只能查看自己参与的结算单
3. 货代只能查看自己承运的结算单
4. 管理员可以查看和管理所有结算单

## 集成说明

### 与其他模块的关系
- **订单模块**: 基于采购订单和货代订单创建结算单
- **用户模块**: 关联买家、卖家、货代用户信息
- **支付模块**: 集成支付处理和凭证管理
- **消息模块**: 发送结算相关通知
- **佣金模块**: 结算完成后触发佣金计算

### 事件发布
- 结算单创建事件
- 支付状态变更事件
- 结算完成事件
- 结算取消事件

## 注意事项

### 开发注意事项
1. **数据一致性**: 确保结算金额与订单金额的一致性
2. **状态管理**: 严格按照状态流转规则
3. **权限控制**: 每个操作都要验证用户权限
4. **事务处理**: 关键操作使用事务保护

### 性能优化
1. **分页查询**: 所有列表查询都支持分页
2. **索引优化**: 为查询字段建立索引
3. **缓存策略**: 缓存频繁查询的数据
4. **批量操作**: 大量数据使用批量处理

### 安全考虑
1. **数据验证**: 严格验证所有输入数据
2. **权限控制**: 基于角色的访问控制
3. **敏感信息**: 保护财务敏感信息
4. **操作日志**: 记录重要操作

### 财务合规
1. **金额精度**: 使用BigDecimal确保金额计算精度
2. **货币支持**: 支持多种货币的结算
3. **审计追踪**: 完整的操作审计日志
4. **数据备份**: 重要财务数据的备份策略

## 扩展功能

### 未来规划
1. **多币种结算**: 支持更复杂的多币种结算场景
2. **自动对账**: 与银行系统对接实现自动对账
3. **发票集成**: 集成发票开具和管理功能
4. **风险控制**: 增加结算风险评估和控制

### 集成接口
1. **银行接口**: 与银行支付系统集成
2. **税务接口**: 与税务系统集成处理税务
3. **ERP接口**: 与企业ERP系统集成
4. **报表接口**: 生成各种财务报表
