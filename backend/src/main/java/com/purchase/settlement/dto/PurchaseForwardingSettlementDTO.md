# PurchaseForwardingSettlementDTO 采购货运结算DTO文档

## 文件概述

`PurchaseForwardingSettlementDTO` 是采购货运结算数据传输对象，用于前后端数据交互。该DTO封装了结算的完整信息，包括基础信息、三方详情、金额信息、物流配置、时间节点等，为前端提供标准化的结算数据结构。

## 核心功能

### 主要职责
- **数据传输**: 在前后端之间传输结算数据
- **数据封装**: 封装结算的完整信息
- **JSON序列化**: 提供标准的JSON序列化配置
- **类型安全**: 确保数据类型的安全性和一致性

### 业务特点
- 支持复杂的多方结算场景
- 完整的金额和时间信息
- 详细的物流配置数据
- 灵活的JSON序列化控制

## 接口说明

### 基础信息字段

#### id
- **类型**: Long
- **序列化**: @JsonSerialize(using = ToStringSerializer.class)
- **描述**: 结算记录主键ID
- **用途**: 结算的唯一标识

#### settlementNumber
- **类型**: String
- **描述**: 结算编号
- **用途**: 业务可见的结算编号

#### unifiedOrderId
- **类型**: Long
- **序列化**: @JsonSerialize(using = ToStringSerializer.class)
- **描述**: 采购订单ID
- **用途**: 关联的采购订单

#### forwarderOrderId
- **类型**: Long
- **序列化**: @JsonSerialize(using = ToStringSerializer.class)
- **描述**: 货代订单ID
- **用途**: 关联的货代订单

### 金额信息字段

#### settlementAmount
- **类型**: BigDecimal
- **描述**: 结算总金额
- **用途**: 结算的总金额

#### purchaseAmount
- **类型**: BigDecimal
- **描述**: 采购金额
- **用途**: 采购部分的金额

#### freightAmount
- **类型**: BigDecimal
- **描述**: 货运金额
- **用途**: 货运部分的金额

#### depositAmount
- **类型**: BigDecimal
- **描述**: 定金金额
- **用途**: 预付的定金金额

#### finalPaymentAmount
- **类型**: BigDecimal
- **描述**: 尾款金额
- **用途**: 剩余的尾款金额

#### currency
- **类型**: String
- **描述**: 货币单位
- **用途**: 金额的货币类型

#### insuranceAmount
- **类型**: BigDecimal
- **描述**: 保险金额
- **用途**: 保险费用

### 贸易条款字段

#### paymentTerms
- **类型**: String
- **描述**: 支付条款
- **用途**: 支付方式和比例说明

#### deliveryTerms
- **类型**: String
- **描述**: 交付条款
- **用途**: 国际贸易条款（FOB/CIF/EXW等）

### 港口和运输信息

#### portOfLoadingName / portOfLoadingCode
- **类型**: String
- **描述**: 起运港名称和代码
- **用途**: 货物起运港信息

#### portOfDestinationName / portOfDestinationCode
- **类型**: String
- **描述**: 目的港名称和代码
- **用途**: 货物目的港信息

#### shippingMethod
- **类型**: String
- **描述**: 运输方式
- **用途**: 货物运输方式

#### shipmentType
- **类型**: String
- **描述**: 装运类型
- **用途**: FCL整柜或LCL拼箱

#### containerSize
- **类型**: String
- **描述**: 集装箱尺寸
- **用途**: 集装箱规格

#### containerQty
- **类型**: Integer
- **描述**: 集装箱数量
- **用途**: 使用的集装箱数量

### 增值服务字段

#### insuranceIncluded
- **类型**: Boolean
- **描述**: 是否包含保险
- **用途**: 保险服务标识

#### customsService
- **类型**: Boolean
- **描述**: 是否提供清关服务
- **用途**: 清关服务标识

#### needCertification
- **类型**: String
- **描述**: 需要认证（1是0否）
- **用途**: 产品认证需求

#### needFumigation
- **类型**: String
- **描述**: 需要熏蒸（1是0否）
- **用途**: 货物熏蒸需求

### 时间信息字段

#### etd
- **类型**: LocalDateTime
- **格式**: @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
- **描述**: 预计离港时间
- **用途**: 货物预计离港时间

#### eta
- **类型**: LocalDateTime
- **格式**: @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
- **描述**: 预计到港时间
- **用途**: 货物预计到港时间

#### depositPaymentTime
- **类型**: LocalDateTime
- **格式**: @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
- **描述**: 定金支付时间
- **用途**: 定金支付的时间记录

#### finalPaymentTime
- **类型**: LocalDateTime
- **格式**: @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
- **描述**: 尾款支付时间
- **用途**: 尾款支付的时间记录

### 三方信息字段

#### 买家信息
- `buyerId`: 买家ID
- `buyerCompany`: 买家公司名称
- `buyerContact`: 买家联系人
- `buyerPhone`: 买家联系电话
- `buyerEmail`: 买家邮箱
- `buyerAddress`: 买家地址

#### 卖家信息
- `sellerId`: 卖家ID
- `sellerCompany`: 卖家公司名称
- `sellerContact`: 卖家联系人
- `sellerPhone`: 卖家联系电话
- `sellerEmail`: 卖家邮箱
- `sellerAddress`: 卖家地址

#### 货代信息
- `forwarderId`: 货代ID
- `forwarderCompany`: 货代公司名称
- `forwarderContact`: 货代联系人
- `forwarderPhone`: 货代联系电话
- `forwarderEmail`: 货代邮箱
- `forwarderAddress`: 货代地址

#### 收货信息
- `receiverName`: 收货人姓名
- `receiverPhone`: 收货人联系电话
- `receiverEmail`: 收货人邮箱
- `receiverAddress`: 收货地址

### 状态和扩展字段

#### settlementStatus
- **类型**: String
- **描述**: 结算状态
- **用途**: 结算当前的处理状态

#### paymentStatus
- **类型**: String
- **描述**: 支付状态
- **用途**: 支付的当前状态

#### orderItems
- **类型**: String
- **描述**: 订单项JSON数据
- **用途**: 存储订单项的详细信息

#### remarks
- **类型**: String
- **描述**: 备注
- **用途**: 结算相关的备注信息

## 使用示例

### 控制器中的使用
```java
@RestController
@RequestMapping("/api/settlements")
public class SettlementController {
    
    @PostMapping
    public ApiResponse<PurchaseForwardingSettlementDTO> createSettlement(
            @RequestBody PurchaseForwardingSettlementDTO settlementDTO) {
        
        PurchaseForwardingSettlementDTO result = settlementService.createSettlement(settlementDTO);
        return ApiResponse.success(result);
    }
    
    @GetMapping("/{id}")
    public ApiResponse<PurchaseForwardingSettlementDTO> getSettlement(@PathVariable Long id) {
        PurchaseForwardingSettlementDTO settlement = settlementService.getSettlementById(id);
        return ApiResponse.success(settlement);
    }
    
    @PutMapping("/{id}")
    public ApiResponse<PurchaseForwardingSettlementDTO> updateSettlement(
            @PathVariable Long id,
            @RequestBody PurchaseForwardingSettlementDTO settlementDTO) {
        
        settlementDTO.setId(id);
        PurchaseForwardingSettlementDTO result = settlementService.updateSettlement(settlementDTO);
        return ApiResponse.success(result);
    }
}
```

### 服务层转换
```java
@Service
public class SettlementService {
    
    public PurchaseForwardingSettlementDTO createSettlement(PurchaseForwardingSettlementDTO dto) {
        // 数据验证
        validateSettlementData(dto);
        
        // DTO转Entity
        PurchaseForwardingSettlement entity = convertToEntity(dto);
        
        // 保存到数据库
        settlementMapper.insert(entity);
        
        // Entity转DTO返回
        return convertToDTO(entity);
    }
    
    private PurchaseForwardingSettlement convertToEntity(PurchaseForwardingSettlementDTO dto) {
        PurchaseForwardingSettlement entity = new PurchaseForwardingSettlement();
        BeanUtils.copyProperties(dto, entity);
        
        // 设置默认值
        if (entity.getId() == null) {
            entity.setId(snowflakeIdGenerator.nextId());
        }
        if (entity.getSettlementStatus() == null) {
            entity.setSettlementStatus("pending");
        }
        if (entity.getPaymentStatus() == null) {
            entity.setPaymentStatus("unpaid");
        }
        
        return entity;
    }
    
    private PurchaseForwardingSettlementDTO convertToDTO(PurchaseForwardingSettlement entity) {
        PurchaseForwardingSettlementDTO dto = new PurchaseForwardingSettlementDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}
```

### 前端使用示例
```javascript
// 创建结算单
const createSettlement = async (settlementData) => {
  try {
    const response = await fetch('/api/settlements', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(settlementData)
    });
    
    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建结算单失败:', error);
    throw error;
  }
};

// 格式化结算数据
const formatSettlementData = (settlement) => {
  return {
    ...settlement,
    formattedAmount: `${settlement.currency} ${settlement.settlementAmount}`,
    formattedDepositAmount: `${settlement.currency} ${settlement.depositAmount}`,
    formattedFinalAmount: `${settlement.currency} ${settlement.finalPaymentAmount}`,
    etdFormatted: formatDateTime(settlement.etd),
    etaFormatted: formatDateTime(settlement.eta),
    statusText: getSettlementStatusText(settlement.settlementStatus),
    paymentStatusText: getPaymentStatusText(settlement.paymentStatus)
  };
};

// 结算表单组件
const SettlementForm = ({ initialData, onSubmit }) => {
  const [formData, setFormData] = useState(initialData || {});
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const result = await createSettlement(formData);
      onSubmit(result);
      message.success('结算单创建成功');
    } catch (error) {
      message.error('创建失败: ' + error.message);
    }
  };
  
  return (
    <Form onSubmit={handleSubmit}>
      <FormItem label="结算编号">
        <Input 
          value={formData.settlementNumber}
          onChange={(e) => setFormData({...formData, settlementNumber: e.target.value})}
        />
      </FormItem>
      
      <FormItem label="结算总金额">
        <InputNumber
          value={formData.settlementAmount}
          onChange={(value) => setFormData({...formData, settlementAmount: value})}
          precision={2}
        />
      </FormItem>
      
      <FormItem label="货币单位">
        <Select 
          value={formData.currency}
          onChange={(value) => setFormData({...formData, currency: value})}
        >
          <Option value="USD">USD</Option>
          <Option value="EUR">EUR</Option>
          <Option value="CNY">CNY</Option>
        </Select>
      </FormItem>
      
      <Button type="primary" htmlType="submit">
        创建结算单
      </Button>
    </Form>
  );
};
```

## 注意事项

### JSON序列化
1. **Long类型**: 使用ToStringSerializer避免JavaScript精度丢失
2. **时间格式**: 统一使用"yyyy-MM-dd HH:mm:ss"格式
3. **BigDecimal**: 确保金额精度不丢失
4. **空值处理**: 合理处理null值的序列化

### 数据验证
1. **必填字段**: 关键业务字段不能为空
2. **数据格式**: 确保数据格式的正确性
3. **业务规则**: 验证业务逻辑的合理性
4. **关联关系**: 验证关联数据的有效性

### 性能优化
1. **字段选择**: 只传输必要的字段
2. **数据压缩**: 大数据量时考虑压缩
3. **缓存策略**: 频繁使用的数据可以缓存
4. **分页处理**: 列表数据使用分页

### 安全考虑
1. **敏感信息**: 敏感字段需要权限控制
2. **数据脱敏**: 必要时对数据进行脱敏
3. **输入验证**: 严格验证前端输入数据
4. **权限验证**: 确保用户有相应的操作权限
