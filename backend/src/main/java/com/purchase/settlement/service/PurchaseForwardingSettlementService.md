# PurchaseForwardingSettlementService 采购货运结算服务接口文档

## 文件概述

`PurchaseForwardingSettlementService` 是采购货运结算服务接口，继承自MyBatis-Plus的IService接口，定义了结算业务的核心方法。该接口提供了结算单的创建、查询、状态管理、支付处理等完整的业务功能，为结算业务提供标准化的服务接口。

## 核心功能

### 主要职责
- **结算单管理**: 提供结算单的创建、查询、更新等基础操作
- **状态管理**: 管理结算状态和支付状态的变更
- **关联查询**: 支持基于订单ID的关联查询
- **用户查询**: 提供买家和卖家相关的结算单查询
- **分页查询**: 支持分页和条件查询功能

### 业务特点
- 继承IService提供基础CRUD操作
- 支持复杂的业务查询场景
- 提供灵活的状态管理机制
- 支持多维度的数据查询
- 完整的分页和过滤功能

## 接口说明

### 结算单创建方法

#### createSettlement(Long unifiedOrderId, Long forwarderOrderId)
```java
PurchaseForwardingSettlement createSettlement(Long unifiedOrderId, Long forwarderOrderId)
```
- **功能**: 基于采购订单和货代订单创建结算单
- **参数**:
  - `unifiedOrderId`: 采购订单ID
  - `forwarderOrderId`: 货代订单ID
- **返回值**: 创建的结算单对象
- **业务逻辑**: 
  - 验证订单的有效性和关联性
  - 自动计算结算金额和分解
  - 设置初始状态和默认值
  - 生成结算编号

### 查询方法

#### getSettlementList(String status, String paymentStatus, Integer page, Integer size)
```java
Map<String, Object> getSettlementList(String status, String paymentStatus, Integer page, Integer size)
```
- **功能**: 获取结算单分页列表
- **参数**:
  - `status`: 结算状态（可选）
  - `paymentStatus`: 支付状态（可选）
  - `page`: 页码
  - `size`: 每页数量
- **返回值**: 包含结算单列表和分页信息的Map
- **返回结构**:
  ```json
  {
    "records": [...],  // 结算单列表
    "total": 100,      // 总记录数
    "current": 1,      // 当前页
    "size": 10         // 每页大小
  }
  ```

#### getByUnifiedOrderId(Long unifiedOrderId)
```java
PurchaseForwardingSettlement getByUnifiedOrderId(Long unifiedOrderId)
```
- **功能**: 根据采购订单ID查询结算单
- **参数**: `unifiedOrderId`: 采购订单ID
- **返回值**: 结算单对象，不存在返回null
- **用途**: 检查采购订单是否已有结算单

#### getByForwarderOrderId(Long forwarderOrderId)
```java
PurchaseForwardingSettlement getByForwarderOrderId(Long forwarderOrderId)
```
- **功能**: 根据货代订单ID查询结算单
- **参数**: `forwarderOrderId`: 货代订单ID
- **返回值**: 结算单对象，不存在返回null
- **用途**: 检查货代订单是否已有结算单

#### getSettlementIdsByBuyerId(Long buyerId)
```java
List<Long> getSettlementIdsByBuyerId(Long buyerId)
```
- **功能**: 获取买家相关的所有结算单ID
- **参数**: `buyerId`: 买家ID
- **返回值**: 结算单ID列表
- **用途**: 权限控制和数据过滤

#### getSettlementIdsBySellerId(Long sellerId)
```java
List<Long> getSettlementIdsBySellerId(Long sellerId)
```
- **功能**: 获取卖家相关的所有结算单ID
- **参数**: `sellerId`: 卖家ID
- **返回值**: 结算单ID列表
- **用途**: 权限控制和数据过滤

### 状态管理方法

#### updateSettlementStatus(Long id, String status)
```java
boolean updateSettlementStatus(Long id, String status)
```
- **功能**: 更新结算状态
- **参数**:
  - `id`: 结算单ID
  - `status`: 新的结算状态
- **返回值**: 更新是否成功
- **状态值**: pending(待处理), processing(处理中), completed(已完成), cancelled(已取消)

#### updatePaymentStatus(Long id, String status, boolean isDeposit)
```java
boolean updatePaymentStatus(Long id, String status, boolean isDeposit)
```
- **功能**: 更新支付状态
- **参数**:
  - `id`: 结算单ID
  - `status`: 新的支付状态
  - `isDeposit`: 是否为定金支付
- **返回值**: 更新是否成功
- **业务逻辑**: 根据isDeposit参数更新相应的支付时间字段

## 使用示例

### 服务实现示例
```java
@Service
public class PurchaseForwardingSettlementServiceImpl 
    extends ServiceImpl<PurchaseForwardingSettlementMapper, PurchaseForwardingSettlement>
    implements PurchaseForwardingSettlementService {
    
    @Autowired
    private OrderService orderService;
    
    @Autowired
    private ForwarderOrderService forwarderOrderService;
    
    @Override
    public PurchaseForwardingSettlement createSettlement(Long unifiedOrderId, Long forwarderOrderId) {
        // 验证订单存在性
        Order purchaseOrder = orderService.getById(unifiedOrderId);
        if (purchaseOrder == null) {
            throw new BusinessException("采购订单不存在");
        }
        
        ForwarderOrder forwarderOrder = forwarderOrderService.getById(forwarderOrderId);
        if (forwarderOrder == null) {
            throw new BusinessException("货代订单不存在");
        }
        
        // 检查是否已有结算单
        PurchaseForwardingSettlement existing = getByUnifiedOrderId(unifiedOrderId);
        if (existing != null) {
            throw new BusinessException("该采购订单已有结算单");
        }
        
        // 创建结算单
        PurchaseForwardingSettlement settlement = new PurchaseForwardingSettlement();
        settlement.setId(snowflakeIdGenerator.nextId());
        settlement.setSettlementNumber(generateSettlementNumber());
        settlement.setUnifiedOrderId(unifiedOrderId);
        settlement.setForwarderOrderId(forwarderOrderId);
        
        // 计算金额
        settlement.setPurchaseAmount(purchaseOrder.getTotalAmount());
        settlement.setFreightAmount(forwarderOrder.getTotalAmount());
        settlement.setSettlementAmount(
            settlement.getPurchaseAmount().add(settlement.getFreightAmount())
        );
        
        // 计算定金和尾款（30%定金，70%尾款）
        BigDecimal depositRatio = new BigDecimal("0.3");
        settlement.setDepositAmount(
            settlement.getSettlementAmount().multiply(depositRatio)
        );
        settlement.setFinalPaymentAmount(
            settlement.getSettlementAmount().subtract(settlement.getDepositAmount())
        );
        
        // 设置初始状态
        settlement.setSettlementStatus("pending");
        settlement.setPaymentStatus("unpaid");
        settlement.setCurrency("USD");
        
        // 复制相关信息
        copyOrderInfo(settlement, purchaseOrder, forwarderOrder);
        
        // 保存
        save(settlement);
        
        return settlement;
    }
    
    @Override
    public Map<String, Object> getSettlementList(String status, String paymentStatus, 
                                                Integer page, Integer size) {
        // 构建查询条件
        QueryWrapper<PurchaseForwardingSettlement> wrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(status)) {
            wrapper.eq("settlement_status", status);
        }
        if (StringUtils.hasText(paymentStatus)) {
            wrapper.eq("payment_status", paymentStatus);
        }
        
        wrapper.eq("deleted", "0");
        wrapper.orderByDesc("created_at");
        
        // 分页查询
        Page<PurchaseForwardingSettlement> pageParam = new Page<>(page, size);
        IPage<PurchaseForwardingSettlement> pageResult = page(pageParam, wrapper);
        
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("records", pageResult.getRecords());
        result.put("total", pageResult.getTotal());
        result.put("current", pageResult.getCurrent());
        result.put("size", pageResult.getSize());
        
        return result;
    }
    
    @Override
    public boolean updateSettlementStatus(Long id, String status) {
        PurchaseForwardingSettlement settlement = getById(id);
        if (settlement == null) {
            return false;
        }
        
        // 验证状态流转
        if (!isValidStatusTransition(settlement.getSettlementStatus(), status)) {
            throw new BusinessException("无效的状态流转");
        }
        
        settlement.setSettlementStatus(status);
        return updateById(settlement);
    }
    
    @Override
    public boolean updatePaymentStatus(Long id, String status, boolean isDeposit) {
        PurchaseForwardingSettlement settlement = getById(id);
        if (settlement == null) {
            return false;
        }
        
        settlement.setPaymentStatus(status);
        
        // 更新支付时间
        if ("paid".equals(status)) {
            if (isDeposit) {
                settlement.setDepositPaymentTime(LocalDateTime.now());
            } else {
                settlement.setFinalPaymentTime(LocalDateTime.now());
            }
        }
        
        return updateById(settlement);
    }
}
```

### 控制器中的使用
```java
@RestController
@RequestMapping("/api/settlements")
public class SettlementController {
    
    @Autowired
    private PurchaseForwardingSettlementService settlementService;
    
    @PostMapping("/create")
    public ApiResponse<PurchaseForwardingSettlement> createSettlement(
            @RequestParam Long unifiedOrderId,
            @RequestParam Long forwarderOrderId) {
        
        PurchaseForwardingSettlement settlement = settlementService.createSettlement(
            unifiedOrderId, forwarderOrderId
        );
        
        return ApiResponse.success(settlement);
    }
    
    @GetMapping("/list")
    public ApiResponse<Map<String, Object>> getSettlementList(
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String paymentStatus,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        Map<String, Object> result = settlementService.getSettlementList(
            status, paymentStatus, page, size
        );
        
        return ApiResponse.success(result);
    }
    
    @PutMapping("/{id}/status")
    public ApiResponse<Boolean> updateSettlementStatus(
            @PathVariable Long id,
            @RequestParam String status) {
        
        boolean success = settlementService.updateSettlementStatus(id, status);
        return ApiResponse.success(success);
    }
    
    @PutMapping("/{id}/payment-status")
    public ApiResponse<Boolean> updatePaymentStatus(
            @PathVariable Long id,
            @RequestParam String status,
            @RequestParam boolean isDeposit) {
        
        boolean success = settlementService.updatePaymentStatus(id, status, isDeposit);
        return ApiResponse.success(success);
    }
}
```

### 权限控制示例
```java
@Service
public class SettlementPermissionService {
    
    @Autowired
    private PurchaseForwardingSettlementService settlementService;
    
    public boolean canAccessSettlement(Long settlementId, Long userId, String userRole) {
        if ("ADMIN".equals(userRole)) {
            return true;
        }
        
        PurchaseForwardingSettlement settlement = settlementService.getById(settlementId);
        if (settlement == null) {
            return false;
        }
        
        // 检查用户是否为买家
        if ("BUYER".equals(userRole)) {
            List<Long> buyerSettlements = settlementService.getSettlementIdsByBuyerId(userId);
            return buyerSettlements.contains(settlementId);
        }
        
        // 检查用户是否为卖家
        if ("SELLER".equals(userRole)) {
            List<Long> sellerSettlements = settlementService.getSettlementIdsBySellerId(userId);
            return sellerSettlements.contains(settlementId);
        }
        
        return false;
    }
}
```

## 注意事项

### 业务规则
1. **订单关联**: 确保采购订单和货代订单的有效关联
2. **状态流转**: 严格按照业务流程进行状态变更
3. **金额计算**: 确保金额计算的准确性和一致性
4. **唯一性**: 每个订单组合只能有一个结算单

### 数据一致性
1. **事务管理**: 关键操作使用事务保护
2. **并发控制**: 防止并发创建重复结算单
3. **状态同步**: 确保相关状态的同步更新
4. **数据完整性**: 维护数据的完整性约束

### 性能优化
1. **索引使用**: 确保查询使用合适的索引
2. **分页查询**: 大数据量查询使用分页
3. **缓存策略**: 频繁查询的数据考虑缓存
4. **批量操作**: 大量数据操作使用批量处理

### 安全考虑
1. **权限验证**: 每个操作都要进行权限验证
2. **数据脱敏**: 敏感信息需要权限控制显示
3. **操作日志**: 记录重要的业务操作
4. **异常处理**: 妥善处理各种异常情况
