# PurchaseForwardingSettlementServiceImpl 采购货运结算服务实现文档

## 文件概述

`PurchaseForwardingSettlementServiceImpl` 是采购货运结算服务的具体实现类，继承自MyBatis-Plus的ServiceImpl，实现了PurchaseForwardingSettlementService接口。该实现类包含了结算业务的完整逻辑，包括结算单创建、状态管理、权限控制、数据查询等核心功能。

## 核心功能

### 主要职责
- **结算单创建**: 基于采购订单和货代订单创建结算单
- **业务逻辑**: 实现复杂的结算业务逻辑和规则
- **数据管理**: 提供完整的数据操作和管理功能
- **权限控制**: 实现基于角色的数据访问控制
- **状态管理**: 管理结算和支付状态的变更

### 业务特点
- 使用@Lazy注解避免循环依赖
- 集成事务管理确保数据一致性
- 完善的异常处理和日志记录
- 支持复杂的权限控制逻辑
- 自动化的金额计算和分解

## 接口说明

### 依赖注入

#### 服务依赖
```java
@Autowired
@Lazy
private UnifiedOrderService unifiedOrderService;

@Autowired
@Lazy
private ForwarderOrderService forwarderOrderService;

@Autowired
private UserService userService;

@Autowired
private SnowflakeIdGenerator snowflakeIdGenerator;
```
- **@Lazy注解**: 避免与订单服务的循环依赖
- **延迟加载**: 只在需要时才初始化依赖的服务
- **雪花算法**: 用于生成唯一的结算单ID

### 核心业务方法

#### createSettlement(Long unifiedOrderId, Long forwarderOrderId)
```java
@Override
@Transactional
public PurchaseForwardingSettlement createSettlement(Long unifiedOrderId, Long forwarderOrderId)
```
- **功能**: 基于采购订单和货代订单创建结算单
- **事务**: 使用@Transactional确保数据一致性
- **业务逻辑**:
  1. 验证订单的存在性和有效性
  2. 检查是否已存在结算单
  3. 自动计算结算金额和分解
  4. 复制订单相关信息
  5. 设置初始状态和默认值
  6. 生成唯一的结算编号

#### getSettlementList(String status, String paymentStatus, Integer page, Integer size)
```java
@Override
public Map<String, Object> getSettlementList(String status, String paymentStatus, Integer page, Integer size)
```
- **功能**: 获取结算单分页列表
- **权限控制**: 根据当前用户角色过滤数据
- **查询逻辑**:
  1. 获取当前用户信息和角色
  2. 根据角色构建不同的查询条件
  3. 应用状态和支付状态筛选
  4. 执行分页查询
  5. 返回格式化的结果

#### updateSettlementStatus(Long id, String status)
```java
@Override
@Transactional
public boolean updateSettlementStatus(Long id, String status)
```
- **功能**: 更新结算状态
- **事务**: 确保状态更新的原子性
- **验证**: 检查状态流转的合法性

#### updatePaymentStatus(Long id, String status, boolean isDeposit)
```java
@Override
@Transactional
public boolean updatePaymentStatus(Long id, String status, boolean isDeposit)
```
- **功能**: 更新支付状态
- **特殊处理**: 根据isDeposit参数更新相应的支付时间
- **业务逻辑**: 支付成功时自动记录支付时间

### 查询方法

#### getByUnifiedOrderId(Long unifiedOrderId)
```java
@Override
public PurchaseForwardingSettlement getByUnifiedOrderId(Long unifiedOrderId)
```
- **功能**: 根据采购订单ID查询结算单
- **用途**: 检查订单是否已有结算单

#### getByForwarderOrderId(Long forwarderOrderId)
```java
@Override
public PurchaseForwardingSettlement getByForwarderOrderId(Long forwarderOrderId)
```
- **功能**: 根据货代订单ID查询结算单
- **用途**: 检查货代订单是否已有结算单

#### getSettlementIdsByBuyerId(Long buyerId) / getSettlementIdsBySellerId(Long sellerId)
```java
@Override
public List<Long> getSettlementIdsByBuyerId(Long buyerId)
@Override
public List<Long> getSettlementIdsBySellerId(Long sellerId)
```
- **功能**: 获取用户相关的结算单ID列表
- **用途**: 权限控制和数据过滤

## 使用示例

### 创建结算单示例
```java
@Service
public class SettlementBusinessService {
    
    @Autowired
    private PurchaseForwardingSettlementService settlementService;
    
    public PurchaseForwardingSettlement createSettlementForOrder(Long orderId, Long forwarderOrderId) {
        try {
            // 创建结算单
            PurchaseForwardingSettlement settlement = settlementService.createSettlement(
                orderId, forwarderOrderId
            );
            
            log.info("结算单创建成功: {}", settlement.getSettlementNumber());
            
            // 发送通知
            notificationService.notifySettlementCreated(settlement);
            
            return settlement;
            
        } catch (BusinessException e) {
            log.error("创建结算单失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建结算单异常", e);
            throw new BusinessException("创建结算单失败");
        }
    }
}
```

### 权限控制示例
```java
@Service
public class SettlementPermissionService {
    
    @Autowired
    private PurchaseForwardingSettlementService settlementService;
    
    public List<PurchaseForwardingSettlement> getUserSettlements(Long userId, String userRole) {
        List<Long> settlementIds;
        
        switch (userRole) {
            case "buyer":
                settlementIds = settlementService.getSettlementIdsByBuyerId(userId);
                break;
            case "seller":
                settlementIds = settlementService.getSettlementIdsBySellerId(userId);
                break;
            case "admin":
                // 管理员可以查看所有结算单
                return settlementService.list();
            default:
                return Collections.emptyList();
        }
        
        if (settlementIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return settlementService.listByIds(settlementIds);
    }
}
```

### 状态管理示例
```java
@Service
public class SettlementStatusService {
    
    @Autowired
    private PurchaseForwardingSettlementService settlementService;
    
    public void processSettlementPayment(Long settlementId, String paymentType, BigDecimal amount) {
        PurchaseForwardingSettlement settlement = settlementService.getById(settlementId);
        if (settlement == null) {
            throw new BusinessException("结算单不存在");
        }
        
        boolean isDeposit = "deposit".equals(paymentType);
        
        // 验证支付金额
        BigDecimal expectedAmount = isDeposit ? 
            settlement.getDepositAmount() : settlement.getFinalPaymentAmount();
        
        if (amount.compareTo(expectedAmount) != 0) {
            throw new BusinessException("支付金额不正确");
        }
        
        // 更新支付状态
        boolean success = settlementService.updatePaymentStatus(settlementId, "paid", isDeposit);
        if (!success) {
            throw new BusinessException("更新支付状态失败");
        }
        
        // 检查是否全部支付完成
        settlement = settlementService.getById(settlementId);
        if (settlement.getDepositPaymentTime() != null && 
            settlement.getFinalPaymentTime() != null) {
            // 全部支付完成，更新结算状态
            settlementService.updateSettlementStatus(settlementId, "completed");
        }
    }
}
```

### 金额计算示例
```java
public class SettlementCalculationService {
    
    public void calculateSettlementAmounts(PurchaseForwardingSettlement settlement,
                                         UnifiedOrder purchaseOrder,
                                         ForwarderOrder forwarderOrder) {
        // 计算基础金额
        settlement.setPurchaseAmount(purchaseOrder.getTotalAmount());
        settlement.setFreightAmount(forwarderOrder.getTotalAmount());
        
        // 计算总金额
        BigDecimal totalAmount = settlement.getPurchaseAmount()
            .add(settlement.getFreightAmount());
        settlement.setSettlementAmount(totalAmount);
        
        // 计算定金和尾款（默认30%定金）
        BigDecimal depositRatio = new BigDecimal("0.30");
        BigDecimal depositAmount = totalAmount.multiply(depositRatio)
            .setScale(2, RoundingMode.HALF_UP);
        BigDecimal finalAmount = totalAmount.subtract(depositAmount);
        
        settlement.setDepositAmount(depositAmount);
        settlement.setFinalPaymentAmount(finalAmount);
        
        // 设置货币单位
        settlement.setCurrency(purchaseOrder.getCurrency());
        
        // 复制保险信息
        if (forwarderOrder.getInsuranceIncluded() != null && 
            forwarderOrder.getInsuranceIncluded()) {
            settlement.setInsuranceIncluded(true);
            settlement.setInsuranceAmount(forwarderOrder.getInsuranceAmount());
        }
    }
}
```

### 数据复制示例
```java
private void copyOrderInformation(PurchaseForwardingSettlement settlement,
                                UnifiedOrder purchaseOrder,
                                ForwarderOrder forwarderOrder) {
    // 复制买家信息
    if (purchaseOrder.getBuyerId() != null) {
        User buyer = userService.getById(purchaseOrder.getBuyerId());
        if (buyer != null) {
            settlement.setBuyerId(buyer.getId());
            settlement.setBuyerCompany(buyer.getCompany());
            settlement.setBuyerContact(buyer.getContactPerson());
            settlement.setBuyerPhone(buyer.getPhone());
            settlement.setBuyerEmail(buyer.getEmail());
            settlement.setBuyerAddress(buyer.getAddress());
        }
    }
    
    // 复制卖家信息
    if (purchaseOrder.getSellerId() != null) {
        User seller = userService.getById(purchaseOrder.getSellerId());
        if (seller != null) {
            settlement.setSellerId(seller.getId());
            settlement.setSellerCompany(seller.getCompany());
            settlement.setSellerContact(seller.getContactPerson());
            settlement.setSellerPhone(seller.getPhone());
            settlement.setSellerEmail(seller.getEmail());
            settlement.setSellerAddress(seller.getAddress());
        }
    }
    
    // 复制货代信息
    if (forwarderOrder.getForwarderId() != null) {
        User forwarder = userService.getById(forwarderOrder.getForwarderId());
        if (forwarder != null) {
            settlement.setForwarderId(forwarder.getId());
            settlement.setForwarderCompany(forwarder.getCompany());
            settlement.setForwarderContact(forwarder.getContactPerson());
            settlement.setForwarderPhone(forwarder.getPhone());
            settlement.setForwarderEmail(forwarder.getEmail());
            settlement.setForwarderAddress(forwarder.getAddress());
        }
    }
    
    // 复制物流信息
    settlement.setPortOfLoadingName(forwarderOrder.getPortOfLoadingName());
    settlement.setPortOfLoadingCode(forwarderOrder.getPortOfLoadingCode());
    settlement.setPortOfDestinationName(forwarderOrder.getPortOfDestinationName());
    settlement.setPortOfDestinationCode(forwarderOrder.getPortOfDestinationCode());
    settlement.setShippingMethod(forwarderOrder.getShippingMethod());
    settlement.setShipmentType(forwarderOrder.getShipmentType());
    settlement.setContainerSize(forwarderOrder.getContainerSize());
    settlement.setContainerQty(forwarderOrder.getContainerQty());
    settlement.setEtd(forwarderOrder.getEtd().atStartOfDay());
    settlement.setEta(forwarderOrder.getEta().atStartOfDay());
    
    // 复制贸易条款
    settlement.setDeliveryTerms(forwarderOrder.getDeliveryTerms());
    settlement.setPaymentTerms("30% deposit, 70% before shipment"); // 默认支付条款
    
    // 复制增值服务
    settlement.setCustomsService(forwarderOrder.getCustomsService());
    settlement.setNeedCertification(forwarderOrder.getNeedCertification());
    settlement.setNeedFumigation(forwarderOrder.getNeedFumigation());
}
```

## 注意事项

### 循环依赖处理
1. **@Lazy注解**: 使用@Lazy注解避免与订单服务的循环依赖
2. **延迟初始化**: 只在需要时才初始化依赖的服务
3. **接口设计**: 合理设计接口避免不必要的依赖
4. **模块划分**: 清晰的模块边界减少循环依赖

### 事务管理
1. **@Transactional**: 关键操作使用事务保护
2. **事务边界**: 合理设置事务边界
3. **异常回滚**: 确保异常时的数据回滚
4. **性能考虑**: 避免长事务影响性能

### 权限控制
1. **角色验证**: 根据用户角色过滤数据
2. **数据权限**: 确保用户只能访问有权限的数据
3. **安全检查**: 每个操作都要进行安全检查
4. **日志记录**: 记录重要的权限操作

### 异常处理
1. **业务异常**: 抛出明确的业务异常
2. **日志记录**: 详细记录异常信息
3. **错误恢复**: 提供合理的错误恢复机制
4. **用户友好**: 提供用户友好的错误信息

### 性能优化
1. **查询优化**: 优化数据库查询性能
2. **缓存策略**: 合理使用缓存提高性能
3. **批量操作**: 大量数据操作使用批量处理
4. **索引使用**: 确保查询使用合适的索引
