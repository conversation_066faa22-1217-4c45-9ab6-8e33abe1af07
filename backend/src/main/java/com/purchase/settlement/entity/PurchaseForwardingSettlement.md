# PurchaseForwardingSettlement 采购货运结算实体文档

## 文件概述

`PurchaseForwardingSettlement` 是采购货运结算实体类，用于统一管理采购订单和货代订单的综合结算信息。该实体整合了采购、运输、支付等多个环节的数据，提供完整的端到端结算解决方案。

## 核心功能

### 主要职责
- **综合结算**: 整合采购订单和货代订单的结算信息
- **多方信息**: 管理买家、卖家、货代三方的详细信息
- **支付管理**: 支持定金和尾款的分期支付模式
- **物流跟踪**: 包含完整的运输和港口信息
- **状态管理**: 结算状态和支付状态的独立管理

### 业务特点
- 支持多种贸易条款和支付方式
- 完整的三方（买家、卖家、货代）信息管理
- 灵活的运输配置和增值服务
- 详细的时间节点跟踪

## 接口说明

### 常量定义

#### SettlementStatus 结算状态
- `PENDING`: 待处理 - 结算单创建但未开始处理
- `PROCESSING`: 处理中 - 结算正在进行中
- `COMPLETED`: 已完成 - 结算已完成
- `CANCELLED`: 已取消 - 结算被取消

#### PaymentStatus 支付状态
- `UNPAID`: 未支付 - 初始状态
- `DEPOSIT_PAID`: 定金已支付
- `DEPOSIT_REFUNDED`: 定金已退款
- `FINAL_PAID`: 尾款已支付
- `FINAL_REFUNDED`: 尾款已退款
- `FULL_PAID`: 全款已支付
- `FULL_REFUNDED`: 全款已退款

### 核心字段分类

#### 基础信息
- `id`: 结算记录主键ID（雪花算法生成）
- `settlementNumber`: 结算编号（业务编号）
- `unifiedOrderId`: 采购订单ID
- `forwarderOrderId`: 货代订单ID
- `creatorId`: 创建者ID
- `creatorName`: 创建者名称

#### 金额信息
- `settlementAmount`: 结算总金额
- `purchaseAmount`: 采购金额
- `freightAmount`: 货运金额
- `depositAmount`: 定金金额
- `finalPaymentAmount`: 尾款金额
- `currency`: 货币单位（默认USD）
- `insuranceAmount`: 保险金额

#### 贸易条款
- `paymentTerms`: 支付条款/支付比例
- `deliveryTerms`: 交付条款（FOB/CIF/EXW等）

#### 港口和运输信息
- `portOfLoadingName`: 起运港名称
- `portOfLoadingCode`: 起运港代码
- `portOfDestinationName`: 目的港名称
- `portOfDestinationCode`: 目的港代码
- `shippingMethod`: 运输方式（SEA/AIR/RAIL/ROAD）

#### 货物配置
- `shipmentType`: 装运类型（FCL整柜/LCL拼箱）
- `containerSize`: 集装箱尺寸
- `containerQty`: 集装箱数量
- `needCertification`: 需要认证（1是0否）
- `needFumigation`: 需要熏蒸（1是0否）

#### 增值服务
- `insuranceIncluded`: 是否包含保险
- `customsService`: 是否提供清关服务

#### 时间信息
- `etd`: 预计离港时间（Estimated Time of Departure）
- `eta`: 预计到港时间（Estimated Time of Arrival）
- `depositPaymentTime`: 定金支付时间
- `finalPaymentTime`: 尾款支付时间

#### 三方信息

##### 买家信息
- `buyerId`: 买家ID
- `buyerCompany`: 买家公司名称
- `buyerContact`: 买家联系人
- `buyerPhone`: 买家联系电话
- `buyerEmail`: 买家邮箱
- `buyerAddress`: 买家地址

##### 卖家信息
- `sellerId`: 卖家ID
- `sellerCompany`: 卖家公司名称
- `sellerContact`: 卖家联系人
- `sellerPhone`: 卖家联系电话
- `sellerEmail`: 卖家邮箱
- `sellerAddress`: 卖家地址

##### 货代信息
- `forwarderId`: 货代ID
- `forwarderCompany`: 货代公司名称
- `forwarderContact`: 货代联系人
- `forwarderPhone`: 货代联系电话
- `forwarderEmail`: 货代邮箱
- `forwarderAddress`: 货代地址

#### 收货信息
- `receiverName`: 收货人姓名
- `receiverPhone`: 收货人联系电话
- `receiverEmail`: 收货人邮箱
- `receiverAddress`: 收货地址

#### 扩展信息
- `orderItems`: 订单项JSON数据
- `remarks`: 备注
- `settlementStatus`: 结算状态
- `paymentStatus`: 支付状态

## 使用示例

### 创建结算记录
```java
PurchaseForwardingSettlement settlement = PurchaseForwardingSettlement.builder()
    // 基础信息
    .id(snowflakeIdGenerator.nextId())
    .settlementNumber("SETTLE-2024-001")
    .unifiedOrderId(1001L)
    .forwarderOrderId(2001L)
    .creatorId(3001L)
    .creatorName("系统管理员")
    
    // 金额信息
    .settlementAmount(new BigDecimal("15000.00"))
    .purchaseAmount(new BigDecimal("10000.00"))
    .freightAmount(new BigDecimal("5000.00"))
    .depositAmount(new BigDecimal("4500.00")) // 30%定金
    .finalPaymentAmount(new BigDecimal("10500.00")) // 70%尾款
    .currency("USD")
    
    // 贸易条款
    .paymentTerms("30%定金，70%尾款")
    .deliveryTerms("CIF")
    
    // 港口信息
    .portOfLoadingName("Shanghai")
    .portOfLoadingCode("CNSHA")
    .portOfDestinationName("Hamburg")
    .portOfDestinationCode("DEHAM")
    .shippingMethod("SEA")
    
    // 货物配置
    .shipmentType("FCL")
    .containerSize("40HQ")
    .containerQty(1)
    .needCertification("1")
    .needFumigation("0")
    
    // 增值服务
    .insuranceIncluded(true)
    .insuranceAmount(new BigDecimal("300.00"))
    .customsService(true)
    
    // 时间信息
    .etd(LocalDateTime.now().plusDays(7))
    .eta(LocalDateTime.now().plusDays(32))
    
    // 买家信息
    .buyerId(4001L)
    .buyerCompany("ABC贸易有限公司")
    .buyerContact("张三")
    .buyerPhone("+86 13800138000")
    .buyerEmail("<EMAIL>")
    .buyerAddress("上海市浦东新区")
    
    // 卖家信息
    .sellerId(5001L)
    .sellerCompany("XYZ制造有限公司")
    .sellerContact("李四")
    .sellerPhone("+86 13900139000")
    .sellerEmail("<EMAIL>")
    .sellerAddress("深圳市南山区")
    
    // 货代信息
    .forwarderId(6001L)
    .forwarderCompany("环球货运有限公司")
    .forwarderContact("王五")
    .forwarderPhone("+86 13700137000")
    .forwarderEmail("<EMAIL>")
    .forwarderAddress("广州市天河区")
    
    // 收货信息
    .receiverName("赵六")
    .receiverPhone("+49 *********")
    .receiverEmail("<EMAIL>")
    .receiverAddress("Hamburg, Germany")
    
    // 状态信息
    .settlementStatus(PurchaseForwardingSettlement.SettlementStatus.PENDING)
    .paymentStatus(PurchaseForwardingSettlement.PaymentStatus.UNPAID)
    .remarks("首次合作，请注意质量控制")
    
    .build();
```

### 支付状态管理
```java
// 定金支付
public void processDepositPayment(Long settlementId) {
    PurchaseForwardingSettlement settlement = settlementMapper.selectById(settlementId);
    
    settlement.setPaymentStatus(PurchaseForwardingSettlement.PaymentStatus.DEPOSIT_PAID);
    settlement.setDepositPaymentTime(LocalDateTime.now());
    
    settlementMapper.updateById(settlement);
    
    // 发送定金支付通知
    notificationService.notifyDepositPaid(settlement);
}

// 尾款支付
public void processFinalPayment(Long settlementId) {
    PurchaseForwardingSettlement settlement = settlementMapper.selectById(settlementId);
    
    if (!PurchaseForwardingSettlement.PaymentStatus.DEPOSIT_PAID.equals(settlement.getPaymentStatus())) {
        throw new BusinessException("必须先支付定金才能支付尾款");
    }
    
    settlement.setPaymentStatus(PurchaseForwardingSettlement.PaymentStatus.FINAL_PAID);
    settlement.setFinalPaymentTime(LocalDateTime.now());
    
    settlementMapper.updateById(settlement);
    
    // 发送尾款支付通知
    notificationService.notifyFinalPaymentPaid(settlement);
}
```

### 结算状态流转
```java
// 开始处理结算
public void startProcessing(Long settlementId) {
    PurchaseForwardingSettlement settlement = settlementMapper.selectById(settlementId);
    
    if (!PurchaseForwardingSettlement.SettlementStatus.PENDING.equals(settlement.getSettlementStatus())) {
        throw new BusinessException("只有待处理状态的结算单才能开始处理");
    }
    
    settlement.setSettlementStatus(PurchaseForwardingSettlement.SettlementStatus.PROCESSING);
    settlementMapper.updateById(settlement);
}

// 完成结算
public void completeSettlement(Long settlementId) {
    PurchaseForwardingSettlement settlement = settlementMapper.selectById(settlementId);
    
    // 检查支付状态
    if (!PurchaseForwardingSettlement.PaymentStatus.FINAL_PAID.equals(settlement.getPaymentStatus())) {
        throw new BusinessException("必须完成全部支付才能完成结算");
    }
    
    settlement.setSettlementStatus(PurchaseForwardingSettlement.SettlementStatus.COMPLETED);
    settlementMapper.updateById(settlement);
    
    // 发送结算完成通知
    notificationService.notifySettlementCompleted(settlement);
}
```

### 查询和统计
```java
// 按状态查询结算记录
public List<PurchaseForwardingSettlement> getSettlementsByStatus(String status) {
    return settlementMapper.selectList(
        new QueryWrapper<PurchaseForwardingSettlement>()
            .eq("settlement_status", status)
            .eq("deleted", "0")
            .orderByDesc("created_at")
    );
}

// 按买家查询结算记录
public IPage<PurchaseForwardingSettlement> getBuyerSettlements(Long buyerId, Integer page, Integer size) {
    Page<PurchaseForwardingSettlement> pageParam = new Page<>(page, size);
    return settlementMapper.selectPage(pageParam,
        new QueryWrapper<PurchaseForwardingSettlement>()
            .eq("buyer_id", buyerId)
            .eq("deleted", "0")
            .orderByDesc("created_at")
    );
}

// 统计结算金额
public Map<String, BigDecimal> getSettlementStatistics(String currency) {
    List<PurchaseForwardingSettlement> settlements = settlementMapper.selectList(
        new QueryWrapper<PurchaseForwardingSettlement>()
            .eq("currency", currency)
            .eq("deleted", "0")
    );
    
    Map<String, BigDecimal> stats = new HashMap<>();
    stats.put("totalAmount", settlements.stream()
        .map(PurchaseForwardingSettlement::getSettlementAmount)
        .reduce(BigDecimal.ZERO, BigDecimal::add));
    
    stats.put("paidAmount", settlements.stream()
        .filter(s -> PurchaseForwardingSettlement.PaymentStatus.FINAL_PAID.equals(s.getPaymentStatus()))
        .map(PurchaseForwardingSettlement::getSettlementAmount)
        .reduce(BigDecimal.ZERO, BigDecimal::add));
    
    return stats;
}
```

## 注意事项

### 数据完整性
1. **关联关系**: `unifiedOrderId` 和 `forwarderOrderId` 应关联到有效的订单记录
2. **金额一致性**: 定金+尾款应等于结算总金额
3. **三方信息**: 买家、卖家、货代信息应完整填写
4. **港口代码**: 使用标准的UN/LOCODE港口代码

### 状态管理
1. **结算状态**: 按照 PENDING → PROCESSING → COMPLETED 流转
2. **支付状态**: 支持多种支付状态的灵活流转
3. **状态一致性**: 结算状态和支付状态应保持逻辑一致
4. **操作权限**: 不同状态下允许的操作不同

### 业务规则
1. **支付顺序**: 通常先支付定金再支付尾款
2. **贸易条款**: 根据不同贸易条款确定责任分工
3. **时间节点**: ETD和ETA应符合实际运输时间
4. **增值服务**: 保险和清关服务影响总费用

### 性能优化
1. **索引优化**: 为查询字段建立合适的数据库索引
2. **分页查询**: 大量数据查询使用分页
3. **JSON处理**: orderItems字段存储复杂数据时注意性能
4. **缓存策略**: 频繁查询的数据可以缓存

### 安全考虑
1. **权限控制**: 确保用户只能访问相关的结算记录
2. **数据脱敏**: 敏感信息需要权限控制显示
3. **操作日志**: 记录重要的状态变更操作
4. **数据验证**: 严格验证金额和状态数据
