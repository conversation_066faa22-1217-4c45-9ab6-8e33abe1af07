# PurchaseForwardingSettlementController 采购货运结算控制器文档

## 文件概述

`PurchaseForwardingSettlementController` 是采购货运结算的REST API控制器，提供结算单的创建、查询、更新等HTTP接口。该控制器集成了权限控制、数据转换、异常处理等功能，为前端提供完整的结算管理API服务。

## 核心功能

### 主要职责
- **API接口**: 提供RESTful风格的结算管理接口
- **权限控制**: 基于Spring Security的权限验证
- **数据转换**: 使用SettlementConverter进行Entity和DTO转换
- **异常处理**: 统一的异常处理和错误响应
- **参数验证**: 请求参数的验证和处理

### 业务特点
- 支持多角色权限控制（买家、卖家、管理员）
- 提供完整的CRUD操作接口
- 支持分页查询和条件筛选
- 统一的响应格式和错误处理
- 详细的API文档注释

## 接口说明

### 查询接口

#### GET /api/v1/settlements/list
```java
@GetMapping("/list")
@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")
public Result<Map<String, Object>> getSettlementList(
    @RequestParam(required = false) String status,
    @RequestParam(required = false) String paymentStatus,
    @RequestParam(defaultValue = "1") Integer page,
    @RequestParam(defaultValue = "10") Integer size)
```
- **功能**: 获取结算单分页列表
- **权限**: 买家、卖家、管理员
- **参数**:
  - `status`: 结算状态（可选）
  - `paymentStatus`: 支付状态（可选）
  - `page`: 页码，默认1
  - `size`: 每页数量，默认10
- **返回值**: 包含结算单列表和分页信息的Map
- **权限控制**: 根据用户角色自动过滤数据

#### GET /api/v1/settlements/{id}
```java
@GetMapping("/{id}")
@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")
public Result<PurchaseForwardingSettlementDTO> getSettlementById(@PathVariable Long id)
```
- **功能**: 根据ID获取结算单详情
- **权限**: 买家、卖家、管理员
- **参数**: `id`: 结算单ID
- **返回值**: 结算单DTO对象
- **权限控制**: 验证用户是否有权限查看该结算单

#### GET /api/v1/settlements/order/{unifiedOrderId}
```java
@GetMapping("/order/{unifiedOrderId}")
@PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")
public Result<PurchaseForwardingSettlementDTO> getSettlementByOrderId(@PathVariable Long unifiedOrderId)
```
- **功能**: 根据采购订单ID获取结算单
- **权限**: 买家、卖家、管理员
- **参数**: `unifiedOrderId`: 采购订单ID
- **返回值**: 结算单DTO对象，不存在返回null

### 创建接口

#### POST /api/v1/settlements
```java
@PostMapping
@PreAuthorize("hasAuthority('admin')")
public Result<PurchaseForwardingSettlementDTO> createSettlement(
    @RequestBody PurchaseForwardingSettlementDTO settlementDTO)
```
- **功能**: 创建新的结算单
- **权限**: 仅管理员
- **参数**: `settlementDTO`: 结算单数据传输对象
- **返回值**: 创建成功的结算单DTO
- **业务逻辑**: 
  - 验证数据完整性
  - 检查订单关联性
  - 自动生成结算编号

#### POST /api/v1/settlements/create
```java
@PostMapping("/create")
@PreAuthorize("hasAuthority('admin')")
public Result<PurchaseForwardingSettlementDTO> createSettlementByOrders(
    @RequestParam Long unifiedOrderId,
    @RequestParam Long forwarderOrderId)
```
- **功能**: 基于采购订单和货代订单创建结算单
- **权限**: 仅管理员
- **参数**:
  - `unifiedOrderId`: 采购订单ID
  - `forwarderOrderId`: 货代订单ID
- **返回值**: 创建成功的结算单DTO
- **业务逻辑**: 自动计算金额和设置默认值

### 更新接口

#### PUT /api/v1/settlements/{id}
```java
@PutMapping("/{id}")
@PreAuthorize("hasAuthority('admin')")
public Result<PurchaseForwardingSettlementDTO> updateSettlement(
    @PathVariable Long id,
    @RequestBody PurchaseForwardingSettlementDTO settlementDTO)
```
- **功能**: 更新结算单信息
- **权限**: 仅管理员
- **参数**:
  - `id`: 结算单ID
  - `settlementDTO`: 更新的结算单数据
- **返回值**: 更新后的结算单DTO
- **业务逻辑**: 使用DTO数据更新现有实体

#### PUT /api/v1/settlements/{id}/status
```java
@PutMapping("/{id}/status")
@PreAuthorize("hasAuthority('admin')")
public Result<Boolean> updateSettlementStatus(
    @PathVariable Long id,
    @RequestParam String status)
```
- **功能**: 更新结算状态
- **权限**: 仅管理员
- **参数**:
  - `id`: 结算单ID
  - `status`: 新的结算状态
- **返回值**: 更新是否成功
- **状态值**: pending, processing, completed, cancelled

#### PUT /api/v1/settlements/{id}/payment-status
```java
@PutMapping("/{id}/payment-status")
@PreAuthorize("hasAuthority('admin')")
public Result<Boolean> updatePaymentStatus(
    @PathVariable Long id,
    @RequestParam String status,
    @RequestParam boolean isDeposit)
```
- **功能**: 更新支付状态
- **权限**: 仅管理员
- **参数**:
  - `id`: 结算单ID
  - `status`: 新的支付状态
  - `isDeposit`: 是否为定金支付
- **返回值**: 更新是否成功
- **业务逻辑**: 根据isDeposit更新相应的支付时间

### 删除接口

#### DELETE /api/v1/settlements/{id}
```java
@DeleteMapping("/{id}")
@PreAuthorize("hasAuthority('admin')")
public Result<Boolean> deleteSettlement(@PathVariable Long id)
```
- **功能**: 删除结算单（软删除）
- **权限**: 仅管理员
- **参数**: `id`: 结算单ID
- **返回值**: 删除是否成功
- **业务逻辑**: 执行软删除操作

## 使用示例

### 前端API调用示例
```javascript
// 获取结算单列表
const getSettlementList = async (params = {}) => {
  try {
    const queryParams = new URLSearchParams({
      page: params.page || 1,
      size: params.size || 10,
      ...(params.status && { status: params.status }),
      ...(params.paymentStatus && { paymentStatus: params.paymentStatus })
    });
    
    const response = await fetch(`/api/v1/settlements/list?${queryParams}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取结算单列表失败:', error);
    throw error;
  }
};

// 创建结算单
const createSettlement = async (settlementData) => {
  try {
    const response = await fetch('/api/v1/settlements', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(settlementData)
    });
    
    const result = await response.json();
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建结算单失败:', error);
    throw error;
  }
};

// 更新结算状态
const updateSettlementStatus = async (id, status) => {
  try {
    const response = await fetch(`/api/v1/settlements/${id}/status?status=${status}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('更新结算状态失败:', error);
    throw error;
  }
};
```

### React组件使用示例
```javascript
// 结算单列表组件
const SettlementList = () => {
  const [settlements, setSettlements] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({});
  
  const loadSettlements = async (page = 1, size = 10, filterParams = {}) => {
    setLoading(true);
    try {
      const params = { page, size, ...filterParams };
      const data = await getSettlementList(params);
      
      setSettlements(data.records);
      setPagination({
        current: data.current,
        pageSize: data.size,
        total: data.total
      });
    } catch (error) {
      message.error('加载结算单列表失败');
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    loadSettlements();
  }, []);
  
  const handleStatusChange = async (id, status) => {
    try {
      await updateSettlementStatus(id, status);
      message.success('状态更新成功');
      loadSettlements(pagination.current, pagination.pageSize, filters);
    } catch (error) {
      message.error('状态更新失败');
    }
  };
  
  return (
    <div>
      <Table
        dataSource={settlements}
        loading={loading}
        pagination={{
          ...pagination,
          onChange: (page, size) => loadSettlements(page, size, filters)
        }}
        columns={[
          {
            title: '结算编号',
            dataIndex: 'settlementNumber',
            key: 'settlementNumber'
          },
          {
            title: '结算金额',
            dataIndex: 'settlementAmount',
            key: 'settlementAmount',
            render: (amount, record) => `${record.currency} ${amount}`
          },
          {
            title: '结算状态',
            dataIndex: 'settlementStatus',
            key: 'settlementStatus',
            render: (status, record) => (
              <Select
                value={status}
                onChange={(value) => handleStatusChange(record.id, value)}
                style={{ width: 120 }}
              >
                <Option value="pending">待处理</Option>
                <Option value="processing">处理中</Option>
                <Option value="completed">已完成</Option>
                <Option value="cancelled">已取消</Option>
              </Select>
            )
          },
          {
            title: '支付状态',
            dataIndex: 'paymentStatus',
            key: 'paymentStatus',
            render: (status) => (
              <Tag color={getPaymentStatusColor(status)}>
                {getPaymentStatusText(status)}
              </Tag>
            )
          }
        ]}
      />
    </div>
  );
};
```

### 权限控制示例
```java
// 自定义权限验证
@Component
public class SettlementPermissionEvaluator {
    
    @Autowired
    private PurchaseForwardingSettlementService settlementService;
    
    public boolean canAccessSettlement(Authentication authentication, Long settlementId) {
        String role = authentication.getAuthorities().iterator().next().getAuthority();
        Long userId = Long.valueOf(authentication.getName());
        
        // 管理员可以访问所有结算单
        if ("admin".equals(role)) {
            return true;
        }
        
        PurchaseForwardingSettlement settlement = settlementService.getById(settlementId);
        if (settlement == null) {
            return false;
        }
        
        // 买家只能访问自己的结算单
        if ("buyer".equals(role)) {
            return Objects.equals(settlement.getBuyerId(), userId);
        }
        
        // 卖家只能访问相关的结算单
        if ("seller".equals(role)) {
            return Objects.equals(settlement.getSellerId(), userId);
        }
        
        return false;
    }
}
```

## 注意事项

### 权限控制
1. **角色验证**: 使用@PreAuthorize注解进行角色验证
2. **数据权限**: 根据用户角色过滤可访问的数据
3. **操作权限**: 不同角色有不同的操作权限
4. **权限检查**: 每个接口都要进行权限检查

### 数据验证
1. **参数验证**: 验证请求参数的有效性
2. **业务规则**: 验证业务逻辑的合理性
3. **数据格式**: 确保数据格式的正确性
4. **关联验证**: 验证关联数据的存在性

### 异常处理
1. **统一响应**: 使用统一的响应格式
2. **错误码**: 定义明确的错误码和消息
3. **日志记录**: 记录重要的操作和错误信息
4. **降级处理**: 提供合理的降级处理策略

### 性能优化
1. **分页查询**: 大数据量查询使用分页
2. **缓存策略**: 频繁查询的数据考虑缓存
3. **查询优化**: 优化数据库查询性能
4. **响应压缩**: 大数据量响应考虑压缩
