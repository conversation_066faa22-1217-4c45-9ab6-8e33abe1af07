# SettlementConverter 结算数据转换器文档

## 文件概述

`SettlementConverter` 是结算数据转换器组件，负责在结算实体（Entity）和数据传输对象（DTO）之间进行数据转换。该转换器处理复杂的数据类型转换，特别是JSON格式的订单项数据，确保数据在不同层之间的正确传递和格式化。

## 核心功能

### 主要职责
- **实体转DTO**: 将数据库实体转换为前端可用的DTO对象
- **DTO转实体**: 将前端DTO转换为数据库实体对象
- **JSON处理**: 处理订单项的JSON序列化和反序列化
- **批量转换**: 支持列表数据的批量转换
- **更新操作**: 支持从DTO更新实体的部分字段

### 业务特点
- 使用Spring的BeanUtils进行基础属性复制
- 集成Jackson ObjectMapper处理JSON转换
- 完善的异常处理和日志记录
- 支持空值和边界情况处理
- 提供灵活的更新机制

## 接口说明

### 核心转换方法

#### toDTO(PurchaseForwardingSettlement entity)
```java
public PurchaseForwardingSettlementDTO toDTO(PurchaseForwardingSettlement entity)
```
- **功能**: 将结算实体转换为DTO对象
- **参数**: `entity` - 结算实体对象
- **返回值**: 转换后的DTO对象，如果输入为null则返回null
- **特殊处理**: 
  - 将entity中的orderItems JSON字符串转换为DTO中的orderItemList对象列表
  - 异常情况下返回空列表而不是抛出异常

#### toEntity(PurchaseForwardingSettlementDTO dto)
```java
public PurchaseForwardingSettlement toEntity(PurchaseForwardingSettlementDTO dto)
```
- **功能**: 将DTO对象转换为结算实体
- **参数**: `dto` - DTO对象
- **返回值**: 转换后的实体对象，如果输入为null则返回null
- **特殊处理**:
  - 将DTO中的orderItemList对象列表转换为entity中的orderItems JSON字符串
  - 排除orderItemList字段的直接复制，使用自定义转换逻辑

#### updateEntityFromDTO(PurchaseForwardingSettlementDTO dto, PurchaseForwardingSettlement entity)
```java
public void updateEntityFromDTO(PurchaseForwardingSettlementDTO dto, PurchaseForwardingSettlement entity)
```
- **功能**: 使用DTO数据更新现有实体对象
- **参数**: 
  - `dto` - 数据源DTO
  - `entity` - 目标实体对象
- **返回值**: void（直接修改传入的entity对象）
- **特殊处理**:
  - 排除id、createdAt等不应更新的字段
  - 保持实体的关键标识信息不变

#### toDTOList(List<PurchaseForwardingSettlement> entityList)
```java
public List<PurchaseForwardingSettlementDTO> toDTOList(List<PurchaseForwardingSettlement> entityList)
```
- **功能**: 批量转换实体列表为DTO列表
- **参数**: `entityList` - 实体对象列表
- **返回值**: DTO对象列表，空列表或null输入返回空列表
- **用途**: 用于列表查询结果的批量转换

## 使用示例

### 服务层中的使用
```java
@Service
public class PurchaseForwardingSettlementService {
    
    @Autowired
    private SettlementConverter settlementConverter;
    
    @Autowired
    private PurchaseForwardingSettlementMapper settlementMapper;
    
    public PurchaseForwardingSettlementDTO getSettlementById(Long id) {
        // 查询实体
        PurchaseForwardingSettlement entity = settlementMapper.selectById(id);
        if (entity == null) {
            throw new BusinessException("结算单不存在");
        }
        
        // 转换为DTO
        return settlementConverter.toDTO(entity);
    }
    
    public PurchaseForwardingSettlementDTO createSettlement(PurchaseForwardingSettlementDTO dto) {
        // DTO转实体
        PurchaseForwardingSettlement entity = settlementConverter.toEntity(dto);
        
        // 设置默认值
        entity.setId(snowflakeIdGenerator.nextId());
        entity.setSettlementStatus("pending");
        entity.setPaymentStatus("unpaid");
        
        // 保存到数据库
        settlementMapper.insert(entity);
        
        // 返回转换后的DTO
        return settlementConverter.toDTO(entity);
    }
    
    public PurchaseForwardingSettlementDTO updateSettlement(Long id, PurchaseForwardingSettlementDTO dto) {
        // 查询现有实体
        PurchaseForwardingSettlement entity = settlementMapper.selectById(id);
        if (entity == null) {
            throw new BusinessException("结算单不存在");
        }
        
        // 使用DTO更新实体
        settlementConverter.updateEntityFromDTO(dto, entity);
        
        // 更新数据库
        settlementMapper.updateById(entity);
        
        // 返回更新后的DTO
        return settlementConverter.toDTO(entity);
    }
    
    public List<PurchaseForwardingSettlementDTO> getSettlementList(QueryWrapper<PurchaseForwardingSettlement> wrapper) {
        // 查询实体列表
        List<PurchaseForwardingSettlement> entityList = settlementMapper.selectList(wrapper);
        
        // 批量转换为DTO列表
        return settlementConverter.toDTOList(entityList);
    }
}
```

### 控制器中的使用
```java
@RestController
@RequestMapping("/api/settlements")
public class SettlementController {
    
    @Autowired
    private PurchaseForwardingSettlementService settlementService;
    
    @GetMapping("/{id}")
    public ApiResponse<PurchaseForwardingSettlementDTO> getSettlement(@PathVariable Long id) {
        PurchaseForwardingSettlementDTO settlement = settlementService.getSettlementById(id);
        return ApiResponse.success(settlement);
    }
    
    @PostMapping
    public ApiResponse<PurchaseForwardingSettlementDTO> createSettlement(
            @RequestBody PurchaseForwardingSettlementDTO dto) {
        
        PurchaseForwardingSettlementDTO result = settlementService.createSettlement(dto);
        return ApiResponse.success(result);
    }
    
    @PutMapping("/{id}")
    public ApiResponse<PurchaseForwardingSettlementDTO> updateSettlement(
            @PathVariable Long id,
            @RequestBody PurchaseForwardingSettlementDTO dto) {
        
        PurchaseForwardingSettlementDTO result = settlementService.updateSettlement(id, dto);
        return ApiResponse.success(result);
    }
}
```

### 订单项处理示例
```java
// 创建包含订单项的结算单
public void createSettlementWithItems() {
    // 准备订单项数据
    List<OrderItem> orderItems = Arrays.asList(
        OrderItem.builder()
            .productName("产品A")
            .quantity(100)
            .unitPrice(new BigDecimal("10.00"))
            .totalPrice(new BigDecimal("1000.00"))
            .build(),
        OrderItem.builder()
            .productName("产品B")
            .quantity(50)
            .unitPrice(new BigDecimal("20.00"))
            .totalPrice(new BigDecimal("1000.00"))
            .build()
    );
    
    // 创建DTO
    PurchaseForwardingSettlementDTO dto = PurchaseForwardingSettlementDTO.builder()
        .settlementNumber("SETTLE-2024-001")
        .settlementAmount(new BigDecimal("2000.00"))
        .currency("USD")
        .orderItemList(orderItems)  // 设置订单项列表
        .build();
    
    // 转换为实体（订单项会自动转换为JSON）
    PurchaseForwardingSettlement entity = settlementConverter.toEntity(dto);
    
    // 保存到数据库
    settlementMapper.insert(entity);
    
    // 从数据库查询并转换回DTO（JSON会自动转换为对象列表）
    PurchaseForwardingSettlement savedEntity = settlementMapper.selectById(entity.getId());
    PurchaseForwardingSettlementDTO resultDTO = settlementConverter.toDTO(savedEntity);
    
    // resultDTO.getOrderItemList() 包含原始的订单项对象列表
}
```

### 异常处理示例
```java
@Component
public class SafeSettlementConverter extends SettlementConverter {
    
    @Override
    public PurchaseForwardingSettlementDTO toDTO(PurchaseForwardingSettlement entity) {
        try {
            return super.toDTO(entity);
        } catch (Exception e) {
            log.error("转换实体为DTO失败: {}", e.getMessage(), e);
            // 返回基础DTO，不包含订单项
            PurchaseForwardingSettlementDTO dto = new PurchaseForwardingSettlementDTO();
            BeanUtils.copyProperties(entity, dto);
            dto.setOrderItemList(Collections.emptyList());
            return dto;
        }
    }
    
    @Override
    public PurchaseForwardingSettlement toEntity(PurchaseForwardingSettlementDTO dto) {
        try {
            return super.toEntity(dto);
        } catch (Exception e) {
            log.error("转换DTO为实体失败: {}", e.getMessage(), e);
            // 返回基础实体，不包含订单项JSON
            PurchaseForwardingSettlement entity = new PurchaseForwardingSettlement();
            BeanUtils.copyProperties(dto, entity, "orderItemList");
            return entity;
        }
    }
}
```

## 注意事项

### JSON处理
1. **异常处理**: JSON转换失败时不抛出异常，而是记录日志并返回空列表
2. **类型安全**: 使用ObjectMapper的TypeFactory确保类型安全
3. **性能考虑**: 大量数据转换时注意JSON处理的性能影响
4. **编码问题**: 确保JSON字符串的编码正确

### 数据一致性
1. **字段排除**: 更新操作时排除不应修改的字段（如id、createdAt）
2. **空值处理**: 正确处理null值和空集合
3. **默认值**: 转换过程中设置合理的默认值
4. **数据验证**: 转换前后进行必要的数据验证

### 性能优化
1. **批量转换**: 使用toDTOList进行批量转换而不是循环调用toDTO
2. **对象复用**: 避免不必要的对象创建
3. **缓存策略**: 对于频繁转换的数据考虑缓存
4. **懒加载**: 大对象的转换考虑懒加载策略

### 扩展性
1. **转换规则**: 新增字段时更新转换规则
2. **自定义转换**: 支持特殊字段的自定义转换逻辑
3. **版本兼容**: 考虑DTO版本变化的兼容性
4. **配置化**: 将转换规则配置化以支持灵活调整

### 错误处理
1. **日志记录**: 详细记录转换过程中的错误信息
2. **降级策略**: 转换失败时的降级处理策略
3. **监控告警**: 对转换失败进行监控和告警
4. **数据恢复**: 提供数据恢复机制
