# PurchaseForwardingSettlementMapper 采购货运结算数据访问层文档

## 文件概述

`PurchaseForwardingSettlementMapper` 是采购货运结算的数据访问层接口，继承自MyBatis-Plus的BaseMapper，提供结算数据的数据库操作方法。该Mapper除了基础的CRUD操作外，还可以扩展复杂查询、统计分析、关联查询等高级功能。

## 核心功能

### 主要职责
- **基础CRUD**: 继承BaseMapper提供基础的增删改查操作
- **复杂查询**: 支持多条件组合查询和关联查询
- **统计分析**: 提供各种统计和计数功能
- **业务查询**: 提供特定业务场景的查询方法
- **性能优化**: 通过自定义SQL优化查询性能

### 业务特点
- 支持结算状态和支付状态的查询
- 提供订单关联的查询功能
- 支持用户维度的数据查询
- 提供金额统计和分析功能
- 优化的查询性能

## 接口说明

### 基础操作方法（继承自BaseMapper）

#### 基础CRUD操作
```java
// 插入操作
int insert(PurchaseForwardingSettlement entity);

// 根据ID删除
int deleteById(Serializable id);

// 根据ID更新
int updateById(PurchaseForwardingSettlement entity);

// 根据ID查询
PurchaseForwardingSettlement selectById(Serializable id);

// 查询所有
List<PurchaseForwardingSettlement> selectList(Wrapper<PurchaseForwardingSettlement> queryWrapper);

// 分页查询
IPage<PurchaseForwardingSettlement> selectPage(IPage<PurchaseForwardingSettlement> page, 
                                               Wrapper<PurchaseForwardingSettlement> queryWrapper);
```

### 扩展查询方法（可在XML中实现）

#### selectSettlementWithOrderInfo
```java
List<PurchaseForwardingSettlement> selectSettlementWithOrderInfo(
    @Param("status") String status,
    @Param("paymentStatus") String paymentStatus,
    @Param("buyerId") Long buyerId,
    @Param("sellerId") Long sellerId
)
```
- **功能**: 查询结算单并关联订单信息
- **参数**:
  - `status`: 结算状态筛选
  - `paymentStatus`: 支付状态筛选
  - `buyerId`: 买家ID筛选
  - `sellerId`: 卖家ID筛选
- **返回值**: 包含关联订单信息的结算单列表

#### selectSettlementStatistics
```java
Map<String, Object> selectSettlementStatistics(
    @Param("startDate") LocalDateTime startDate,
    @Param("endDate") LocalDateTime endDate
)
```
- **功能**: 查询结算统计数据
- **参数**:
  - `startDate`: 开始时间
  - `endDate`: 结束时间
- **返回值**: 包含统计信息的Map

#### selectByUnifiedOrderId
```java
PurchaseForwardingSettlement selectByUnifiedOrderId(@Param("unifiedOrderId") Long unifiedOrderId)
```
- **功能**: 根据采购订单ID查询结算单
- **参数**: `unifiedOrderId`: 采购订单ID
- **返回值**: 结算单对象

#### selectByForwarderOrderId
```java
PurchaseForwardingSettlement selectByForwarderOrderId(@Param("forwarderOrderId") Long forwarderOrderId)
```
- **功能**: 根据货代订单ID查询结算单
- **参数**: `forwarderOrderId`: 货代订单ID
- **返回值**: 结算单对象

#### selectSettlementIdsByBuyerId
```java
List<Long> selectSettlementIdsByBuyerId(@Param("buyerId") Long buyerId)
```
- **功能**: 获取买家相关的所有结算单ID
- **参数**: `buyerId`: 买家ID
- **返回值**: 结算单ID列表

#### selectSettlementIdsBySellerId
```java
List<Long> selectSettlementIdsBySellerId(@Param("sellerId") Long sellerId)
```
- **功能**: 获取卖家相关的所有结算单ID
- **参数**: `sellerId`: 卖家ID
- **返回值**: 结算单ID列表

#### selectAmountStatistics
```java
Map<String, BigDecimal> selectAmountStatistics(
    @Param("status") String status,
    @Param("currency") String currency
)
```
- **功能**: 查询金额统计信息
- **参数**:
  - `status`: 结算状态
  - `currency`: 货币类型
- **返回值**: 包含各种金额统计的Map

## 使用示例

### 基础操作使用
```java
@Service
public class SettlementService {
    
    @Autowired
    private PurchaseForwardingSettlementMapper settlementMapper;
    
    public PurchaseForwardingSettlement createSettlement(PurchaseForwardingSettlement settlement) {
        // 插入结算单
        settlementMapper.insert(settlement);
        return settlement;
    }
    
    public PurchaseForwardingSettlement getSettlementById(Long id) {
        return settlementMapper.selectById(id);
    }
    
    public boolean updateSettlement(PurchaseForwardingSettlement settlement) {
        return settlementMapper.updateById(settlement) > 0;
    }
    
    public boolean deleteSettlement(Long id) {
        return settlementMapper.deleteById(id) > 0;
    }
}
```

### 复杂查询使用
```java
@Service
public class SettlementQueryService {
    
    @Autowired
    private PurchaseForwardingSettlementMapper settlementMapper;
    
    public List<PurchaseForwardingSettlement> getSettlementsByConditions(
            String status, String paymentStatus, Long buyerId) {
        
        QueryWrapper<PurchaseForwardingSettlement> wrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(status)) {
            wrapper.eq("settlement_status", status);
        }
        if (StringUtils.hasText(paymentStatus)) {
            wrapper.eq("payment_status", paymentStatus);
        }
        if (buyerId != null) {
            wrapper.eq("buyer_id", buyerId);
        }
        
        wrapper.eq("deleted", "0");
        wrapper.orderByDesc("created_at");
        
        return settlementMapper.selectList(wrapper);
    }
    
    public IPage<PurchaseForwardingSettlement> getSettlementsPage(
            Integer page, Integer size, String status) {
        
        Page<PurchaseForwardingSettlement> pageParam = new Page<>(page, size);
        
        QueryWrapper<PurchaseForwardingSettlement> wrapper = new QueryWrapper<>();
        if (StringUtils.hasText(status)) {
            wrapper.eq("settlement_status", status);
        }
        wrapper.eq("deleted", "0");
        wrapper.orderByDesc("created_at");
        
        return settlementMapper.selectPage(pageParam, wrapper);
    }
}
```

### 统计查询使用
```java
@Service
public class SettlementStatisticsService {
    
    @Autowired
    private PurchaseForwardingSettlementMapper settlementMapper;
    
    public Map<String, Object> getSettlementStatistics() {
        // 按状态统计
        QueryWrapper<PurchaseForwardingSettlement> wrapper = new QueryWrapper<>();
        wrapper.select("settlement_status", "count(*) as count")
               .eq("deleted", "0")
               .groupBy("settlement_status");
        
        List<Map<String, Object>> statusStats = settlementMapper.selectMaps(wrapper);
        
        // 按支付状态统计
        QueryWrapper<PurchaseForwardingSettlement> paymentWrapper = new QueryWrapper<>();
        paymentWrapper.select("payment_status", "count(*) as count")
                     .eq("deleted", "0")
                     .groupBy("payment_status");
        
        List<Map<String, Object>> paymentStats = settlementMapper.selectMaps(paymentWrapper);
        
        // 总金额统计
        QueryWrapper<PurchaseForwardingSettlement> amountWrapper = new QueryWrapper<>();
        amountWrapper.select("sum(settlement_amount) as total_amount", 
                           "sum(deposit_amount) as total_deposit",
                           "sum(final_payment_amount) as total_final")
                    .eq("deleted", "0")
                    .eq("settlement_status", "completed");
        
        Map<String, Object> amountStats = settlementMapper.selectOne(amountWrapper);
        
        // 组装结果
        Map<String, Object> result = new HashMap<>();
        result.put("statusStatistics", statusStats);
        result.put("paymentStatistics", paymentStats);
        result.put("amountStatistics", amountStats);
        
        return result;
    }
    
    public List<PurchaseForwardingSettlement> getRecentSettlements(Integer limit) {
        QueryWrapper<PurchaseForwardingSettlement> wrapper = new QueryWrapper<>();
        wrapper.eq("deleted", "0")
               .orderByDesc("created_at")
               .last("LIMIT " + limit);
        
        return settlementMapper.selectList(wrapper);
    }
}
```

### 关联查询使用（需要在XML中实现）
```xml
<!-- PurchaseForwardingSettlementMapper.xml -->
<mapper namespace="com.purchase.settlement.mapper.PurchaseForwardingSettlementMapper">
    
    <select id="selectSettlementWithOrderInfo" resultType="com.purchase.settlement.entity.PurchaseForwardingSettlement">
        SELECT 
            s.*,
            o.order_number as purchase_order_number,
            fo.order_number as forwarder_order_number
        FROM purchase_forwarding_settlement s
        LEFT JOIN unified_order o ON s.unified_order_id = o.id
        LEFT JOIN forwarder_order fo ON s.forwarder_order_id = fo.id
        WHERE s.deleted = '0'
        <if test="status != null and status != ''">
            AND s.settlement_status = #{status}
        </if>
        <if test="paymentStatus != null and paymentStatus != ''">
            AND s.payment_status = #{paymentStatus}
        </if>
        <if test="buyerId != null">
            AND s.buyer_id = #{buyerId}
        </if>
        <if test="sellerId != null">
            AND s.seller_id = #{sellerId}
        </if>
        ORDER BY s.created_at DESC
    </select>
    
    <select id="selectSettlementIdsByBuyerId" resultType="java.lang.Long">
        SELECT DISTINCT s.id
        FROM purchase_forwarding_settlement s
        WHERE s.deleted = '0'
        AND s.buyer_id = #{buyerId}
    </select>
    
    <select id="selectSettlementIdsBySellerId" resultType="java.lang.Long">
        SELECT DISTINCT s.id
        FROM purchase_forwarding_settlement s
        WHERE s.deleted = '0'
        AND s.seller_id = #{sellerId}
    </select>
    
    <select id="selectAmountStatistics" resultType="java.util.Map">
        SELECT 
            SUM(settlement_amount) as totalAmount,
            SUM(deposit_amount) as totalDeposit,
            SUM(final_payment_amount) as totalFinalPayment,
            AVG(settlement_amount) as avgAmount,
            COUNT(*) as settlementCount
        FROM purchase_forwarding_settlement
        WHERE deleted = '0'
        <if test="status != null and status != ''">
            AND settlement_status = #{status}
        </if>
        <if test="currency != null and currency != ''">
            AND currency = #{currency}
        </if>
    </select>
    
</mapper>
```

## 注意事项

### SQL优化
1. **索引使用**: 确保查询字段有合适的数据库索引
2. **查询性能**: 复杂查询应该在XML中优化SQL语句
3. **分页处理**: 大数据量查询使用数据库级分页
4. **关联查询**: 合理使用JOIN避免N+1查询问题

### 数据一致性
1. **软删除**: 查询时应该排除已删除的记录
2. **事务管理**: 关键操作使用事务保护
3. **并发控制**: 防止并发操作导致的数据不一致
4. **数据完整性**: 确保关联数据的完整性

### 性能监控
1. **慢查询**: 监控慢查询并优化
2. **索引优化**: 定期检查和优化索引
3. **查询统计**: 记录查询性能统计信息
4. **缓存策略**: 频繁查询的数据考虑缓存

### 扩展性
1. **方法命名**: 使用清晰的方法命名规范
2. **参数设计**: 预留参数扩展的可能性
3. **返回类型**: 考虑返回类型的扩展性
4. **XML配置**: 复杂查询在XML中实现便于维护
