# ChatWebSocketController WebSocket聊天控制器文档

## 文件概述

`ChatWebSocketController` 是WebSocket聊天消息控制器，负责处理通过WebSocket协议发送的实时聊天消息。该控制器基于Spring WebSocket和STOMP协议，提供消息发送、状态通知、错误处理等功能，支持多方聊天室的实时通信需求。

## 核心功能

### 主要职责
- **消息处理**: 处理客户端发送的聊天消息
- **实时推送**: 向聊天室成员实时推送消息
- **状态管理**: 处理用户输入状态和在线状态
- **未读计数**: 管理聊天室的未读消息计数
- **错误处理**: 处理消息发送过程中的异常

### 业务特点
- 支持多方聊天室（买家、卖家、货代、管理员）
- 实时消息推送和状态同步
- 完整的未读消息管理
- 灵活的错误处理机制
- 基于用户角色的消息路由

## 接口说明

### 依赖注入

#### 核心服务
```java
@Autowired
private SimpMessagingTemplate messagingTemplate;  // WebSocket消息模板

@Autowired
private ChatMessageService chatMessageService;    // 聊天消息服务

@Autowired
private ChatRoomService chatRoomService;          // 聊天室服务

@Autowired
private ChatRoomUnreadService chatRoomUnreadService; // 未读消息服务
```

### 消息处理方法

#### sendMessage(@Payload MessageRequest request, Principal principal)
```java
@MessageMapping("/chat.send")
public void sendMessage(@Payload MessageRequest request, Principal principal)
```
- **功能**: 处理客户端发送的聊天消息
- **映射路径**: `/app/chat.send`
- **参数**:
  - `request`: 消息请求对象，包含消息内容和房间信息
  - `principal`: 当前用户身份信息
- **业务流程**:
  1. 获取发送者ID和角色
  2. 保存消息到数据库
  3. 向聊天室所有成员推送消息
  4. 更新未读消息计数
  5. 处理发送异常

#### userTyping(@Payload TypingStatusRequest request, Principal principal)
```java
@MessageMapping("/chat.typing")
public void userTyping(@Payload TypingStatusRequest request, Principal principal)
```
- **功能**: 处理用户"正在输入"状态
- **映射路径**: `/app/chat.typing`
- **参数**:
  - `request`: 输入状态请求对象
  - `principal`: 当前用户身份信息
- **业务流程**:
  1. 获取用户ID和聊天室信息
  2. 构建输入状态消息
  3. 向其他聊天室成员推送状态

### 核心业务方法

#### sendMessageToRoomMembers(ChatMessage message)
```java
private void sendMessageToRoomMembers(ChatMessage message)
```
- **功能**: 向聊天室所有成员发送消息
- **参数**: `message`: 要发送的聊天消息
- **业务逻辑**:
  1. 获取聊天室信息
  2. 创建WebSocket消息包装
  3. 向买家、卖家、货代、管理员分别推送
  4. 为非发送者更新未读计数

## 使用示例

### 前端消息发送
```javascript
// 连接WebSocket
const connectWebSocket = () => {
  const socket = new SockJS('/ws/message');
  const stompClient = Stomp.over(socket);
  
  const connectHeaders = {
    'Authorization': `Bearer ${getAuthToken()}`
  };
  
  stompClient.connect(connectHeaders, (frame) => {
    console.log('WebSocket连接成功');
    
    // 订阅个人消息队列
    stompClient.subscribe('/user/queue/messages', (message) => {
      const wsMessage = JSON.parse(message.body);
      handleIncomingMessage(wsMessage);
    });
    
    // 订阅错误消息
    stompClient.subscribe('/user/queue/errors', (message) => {
      const errorMessage = JSON.parse(message.body);
      handleError(errorMessage);
    });
    
    // 订阅输入状态
    stompClient.subscribe('/user/queue/typing', (message) => {
      const typingMessage = JSON.parse(message.body);
      handleTypingStatus(typingMessage);
    });
  });
  
  return stompClient;
};

// 发送聊天消息
const sendChatMessage = (stompClient, roomId, content, messageType = 'text') => {
  const messageRequest = {
    roomId: roomId,
    content: content,
    messageType: messageType,
    senderRole: getCurrentUserRole()
  };
  
  stompClient.send('/app/chat.send', {}, JSON.stringify(messageRequest));
};

// 发送输入状态
const sendTypingStatus = (stompClient, roomId, isTyping) => {
  const typingRequest = {
    roomId: roomId,
    isTyping: isTyping
  };
  
  stompClient.send('/app/chat.typing', {}, JSON.stringify(typingRequest));
};

// 处理接收到的消息
const handleIncomingMessage = (wsMessage) => {
  if (wsMessage.type === 'chat_message') {
    const chatMessage = wsMessage.data;
    
    // 添加到消息列表
    addMessageToChat(chatMessage);
    
    // 播放提示音
    playNotificationSound();
    
    // 更新未读计数
    updateUnreadCount(chatMessage.roomId);
  }
};

// 处理输入状态
const handleTypingStatus = (wsMessage) => {
  if (wsMessage.type === 'user_typing') {
    const typingData = wsMessage.data;
    
    // 显示输入状态
    showTypingIndicator(typingData.userId, typingData.isTyping);
  }
};
```

### React聊天组件
```javascript
const ChatRoom = ({ roomId, currentUser }) => {
  const [stompClient, setStompClient] = useState(null);
  const [messages, setMessages] = useState([]);
  const [typingUsers, setTypingUsers] = useState(new Set());
  const [inputValue, setInputValue] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  
  // WebSocket连接
  useEffect(() => {
    const socket = new SockJS('/ws/message');
    const client = Stomp.over(socket);
    
    client.connect({ 'Authorization': `Bearer ${getAuthToken()}` }, 
      (frame) => {
        setIsConnected(true);
        setStompClient(client);
        
        // 订阅消息
        client.subscribe('/user/queue/messages', (message) => {
          const wsMessage = JSON.parse(message.body);
          if (wsMessage.type === 'chat_message') {
            setMessages(prev => [...prev, wsMessage.data]);
          }
        });
        
        // 订阅输入状态
        client.subscribe('/user/queue/typing', (message) => {
          const wsMessage = JSON.parse(message.body);
          if (wsMessage.type === 'user_typing') {
            const { userId, isTyping } = wsMessage.data;
            setTypingUsers(prev => {
              const newSet = new Set(prev);
              if (isTyping) {
                newSet.add(userId);
              } else {
                newSet.delete(userId);
              }
              return newSet;
            });
          }
        });
      },
      (error) => {
        console.error('WebSocket连接失败:', error);
        setIsConnected(false);
      }
    );
    
    return () => {
      if (client && client.connected) {
        client.disconnect();
      }
    };
  }, [roomId]);
  
  // 发送消息
  const sendMessage = () => {
    if (!stompClient || !inputValue.trim()) return;
    
    const messageRequest = {
      roomId: roomId,
      content: inputValue.trim(),
      messageType: 'text',
      senderRole: currentUser.role
    };
    
    stompClient.send('/app/chat.send', {}, JSON.stringify(messageRequest));
    setInputValue('');
  };
  
  // 处理输入变化
  const handleInputChange = (e) => {
    setInputValue(e.target.value);
    
    // 发送输入状态
    if (stompClient) {
      stompClient.send('/app/chat.typing', {}, JSON.stringify({
        roomId: roomId,
        isTyping: e.target.value.length > 0
      }));
    }
  };
  
  // 停止输入状态
  const handleInputBlur = () => {
    if (stompClient) {
      stompClient.send('/app/chat.typing', {}, JSON.stringify({
        roomId: roomId,
        isTyping: false
      }));
    }
  };
  
  return (
    <div className="chat-room">
      <div className="connection-status">
        {isConnected ? (
          <span className="connected">已连接</span>
        ) : (
          <span className="disconnected">连接中...</span>
        )}
      </div>
      
      <div className="message-list">
        {messages.map(message => (
          <MessageComponent 
            key={message.id} 
            message={message} 
            isOwn={message.senderId === currentUser.id}
          />
        ))}
        
        {typingUsers.size > 0 && (
          <div className="typing-indicator">
            {Array.from(typingUsers).join(', ')} 正在输入...
          </div>
        )}
      </div>
      
      <div className="message-input">
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          placeholder="输入消息..."
          disabled={!isConnected}
        />
        <button onClick={sendMessage} disabled={!isConnected || !inputValue.trim()}>
          发送
        </button>
      </div>
    </div>
  );
};
```

### 服务端消息处理扩展
```java
@Controller
public class EnhancedChatWebSocketController {
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @Autowired
    private ChatMessageService chatMessageService;
    
    @Autowired
    private FileUploadService fileUploadService;
    
    /**
     * 处理文件消息发送
     */
    @MessageMapping("/chat.sendFile")
    public void sendFileMessage(@Payload FileMessageRequest request, Principal principal) {
        Long senderId = Long.valueOf(principal.getName());
        
        try {
            // 验证文件
            validateFile(request.getFileData(), request.getFileName());
            
            // 上传文件
            String fileUrl = fileUploadService.uploadChatFile(
                request.getFileData(), 
                request.getFileName()
            );
            
            // 创建文件消息
            MessageRequest messageRequest = new MessageRequest();
            messageRequest.setRoomId(request.getRoomId());
            messageRequest.setMessageType("file");
            messageRequest.setFileUrl(fileUrl);
            messageRequest.setFileName(request.getFileName());
            messageRequest.setSenderRole(request.getSenderRole());
            
            // 保存并发送消息
            Long messageId = chatMessageService.sendMessage(senderId, request.getSenderRole(), messageRequest);
            ChatMessage message = chatMessageService.getById(messageId);
            sendMessageToRoomMembers(message);
            
        } catch (Exception e) {
            // 发送错误消息
            WebSocketMessage<String> errorMessage = WebSocketMessage.of(
                "error", 
                "文件发送失败: " + e.getMessage()
            );
            messagingTemplate.convertAndSendToUser(
                senderId.toString(), 
                "/queue/errors", 
                errorMessage
            );
        }
    }
    
    /**
     * 处理消息撤回
     */
    @MessageMapping("/chat.recall")
    public void recallMessage(@Payload RecallMessageRequest request, Principal principal) {
        Long userId = Long.valueOf(principal.getName());
        
        try {
            // 验证权限和时间限制
            ChatMessage message = chatMessageService.getById(request.getMessageId());
            if (!message.getSenderId().equals(userId)) {
                throw new AccessDeniedException("只能撤回自己的消息");
            }
            
            if (message.getCreatedAt().isBefore(LocalDateTime.now().minusMinutes(2))) {
                throw new BusinessException("消息发送超过2分钟，无法撤回");
            }
            
            // 标记消息为已撤回
            chatMessageService.recallMessage(request.getMessageId());
            
            // 通知聊天室成员
            WebSocketMessage<Object> recallNotice = WebSocketMessage.of(
                "message_recalled", 
                Map.of("messageId", request.getMessageId())
            );
            
            ChatRoom room = chatRoomService.getById(message.getRoomId());
            sendToRoomMembers(room, recallNotice);
            
        } catch (Exception e) {
            WebSocketMessage<String> errorMessage = WebSocketMessage.of(
                "error", 
                "撤回失败: " + e.getMessage()
            );
            messagingTemplate.convertAndSendToUser(
                userId.toString(), 
                "/queue/errors", 
                errorMessage
            );
        }
    }
    
    /**
     * 处理消息已读状态
     */
    @MessageMapping("/chat.markRead")
    public void markMessagesAsRead(@Payload MarkReadRequest request, Principal principal) {
        Long userId = Long.valueOf(principal.getName());
        
        try {
            // 标记消息为已读
            chatRoomUnreadService.markAsRead(request.getRoomId(), userId);
            
            // 通知发送者消息已读（可选）
            WebSocketMessage<Object> readNotice = WebSocketMessage.of(
                "messages_read", 
                Map.of("roomId", request.getRoomId(), "readerId", userId)
            );
            
            // 可以选择性地通知其他成员
            // sendToRoomMembers(room, readNotice);
            
        } catch (Exception e) {
            log.error("标记消息已读失败", e);
        }
    }
}
```

## 注意事项

### 消息可靠性
1. **异常处理**: 完善的异常处理机制，确保消息发送失败时的用户反馈
2. **重试机制**: 客户端实现消息发送失败的重试逻辑
3. **消息确认**: 重要消息需要确认机制
4. **离线消息**: 支持离线用户的消息存储和推送

### 性能优化
1. **消息批量**: 大量消息时考虑批量处理
2. **连接管理**: 合理管理WebSocket连接数量
3. **内存控制**: 控制内存中的消息缓存大小
4. **异步处理**: 耗时操作使用异步处理

### 安全考虑
1. **权限验证**: 确保用户只能发送到有权限的聊天室
2. **内容过滤**: 对消息内容进行安全过滤
3. **频率限制**: 防止消息发送频率过高
4. **文件安全**: 上传文件的类型和大小限制

### 用户体验
1. **实时性**: 确保消息的实时传输
2. **状态反馈**: 提供消息发送状态的实时反馈
3. **输入提示**: 显示其他用户的输入状态
4. **错误提示**: 友好的错误提示和处理建议
