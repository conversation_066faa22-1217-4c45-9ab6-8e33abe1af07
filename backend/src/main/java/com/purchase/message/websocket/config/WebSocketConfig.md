# WebSocketConfig WebSocket配置类文档

## 文件概述

`WebSocketConfig` 是WebSocket消息代理的配置类，基于Spring WebSocket和STOMP协议实现实时通信功能。该配置类启用了WebSocket消息代理，配置了消息路由、端点注册、权限拦截等核心功能，为聊天系统提供了完整的实时通信基础设施。

## 核心功能

### 主要职责
- **消息代理配置**: 配置STOMP消息代理的路由规则
- **端点注册**: 注册WebSocket连接端点
- **权限控制**: 集成身份验证和权限拦截器
- **跨域支持**: 配置跨域访问策略
- **SockJS支持**: 提供SockJS降级支持

### 业务特点
- 基于STOMP协议的消息传输
- 支持点对点和广播消息
- 集成Spring Security权限控制
- 提供完整的连接生命周期管理
- 支持多种客户端连接方式

## 接口说明

### 核心配置方法

#### configureMessageBroker(MessageBrokerRegistry registry)
```java
@Override
public void configureMessageBroker(MessageBrokerRegistry registry)
```
- **功能**: 配置消息代理的路由规则
- **参数**: `registry`: 消息代理注册器
- **配置项**:
  - **简单代理**: `/topic`, `/queue` - 服务端推送给客户端的路径前缀
  - **应用目标前缀**: `/app` - 客户端发送消息给服务端的路径前缀
  - **用户目标前缀**: `/user` - 用户独享消息的路径前缀

#### registerStompEndpoints(StompEndpointRegistry registry)
```java
@Override
public void registerStompEndpoints(StompEndpointRegistry registry)
```
- **功能**: 注册STOMP连接端点
- **参数**: `registry`: STOMP端点注册器
- **配置项**:
  - **端点路径**: `/ws/message` - WebSocket连接端点
  - **跨域策略**: 允许所有来源（开发环境）
  - **SockJS支持**: 启用SockJS降级支持

#### configureClientInboundChannel(ChannelRegistration registration)
```java
@Override
public void configureClientInboundChannel(ChannelRegistration registration)
```
- **功能**: 配置客户端入站通道拦截器
- **参数**: `registration`: 通道注册器
- **配置项**: 添加身份验证拦截器

### 依赖注入

#### authChannelInterceptor
- **类型**: AuthChannelInterceptor
- **描述**: 身份验证通道拦截器
- **用途**: 在消息传输过程中进行身份验证和权限控制

## 使用示例

### 前端连接示例
```javascript
// 使用SockJS和STOMP连接WebSocket
const connectWebSocket = () => {
  // 创建SockJS连接
  const socket = new SockJS('/ws/message');
  
  // 创建STOMP客户端
  const stompClient = Stomp.over(socket);
  
  // 连接配置
  const connectHeaders = {
    'Authorization': `Bearer ${getAuthToken()}`,
    'X-User-Id': getCurrentUserId()
  };
  
  // 建立连接
  stompClient.connect(connectHeaders, 
    // 连接成功回调
    (frame) => {
      console.log('WebSocket连接成功:', frame);
      
      // 订阅聊天室消息
      stompClient.subscribe('/topic/room/123', (message) => {
        const chatMessage = JSON.parse(message.body);
        handleNewMessage(chatMessage);
      });
      
      // 订阅个人消息
      stompClient.subscribe('/user/queue/private', (message) => {
        const privateMessage = JSON.parse(message.body);
        handlePrivateMessage(privateMessage);
      });
      
      // 发送消息
      const sendMessage = (roomId, content) => {
        stompClient.send('/app/chat.sendMessage', {}, JSON.stringify({
          roomId: roomId,
          content: content,
          messageType: 'text'
        }));
      };
    },
    // 连接失败回调
    (error) => {
      console.error('WebSocket连接失败:', error);
      // 重连逻辑
      setTimeout(connectWebSocket, 5000);
    }
  );
  
  return stompClient;
};

// React Hook示例
const useWebSocket = (roomId) => {
  const [stompClient, setStompClient] = useState(null);
  const [messages, setMessages] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  
  useEffect(() => {
    const socket = new SockJS('/ws/message');
    const client = Stomp.over(socket);
    
    const connectHeaders = {
      'Authorization': `Bearer ${getAuthToken()}`
    };
    
    client.connect(connectHeaders, 
      (frame) => {
        setConnectionStatus('connected');
        setStompClient(client);
        
        // 订阅房间消息
        client.subscribe(`/topic/room/${roomId}`, (message) => {
          const chatMessage = JSON.parse(message.body);
          setMessages(prev => [...prev, chatMessage]);
        });
      },
      (error) => {
        setConnectionStatus('error');
        console.error('连接失败:', error);
      }
    );
    
    return () => {
      if (client && client.connected) {
        client.disconnect();
      }
    };
  }, [roomId]);
  
  const sendMessage = useCallback((content) => {
    if (stompClient && stompClient.connected) {
      stompClient.send('/app/chat.sendMessage', {}, JSON.stringify({
        roomId: roomId,
        content: content,
        messageType: 'text'
      }));
    }
  }, [stompClient, roomId]);
  
  return {
    messages,
    sendMessage,
    connectionStatus,
    isConnected: connectionStatus === 'connected'
  };
};
```

### 服务端控制器示例
```java
@Controller
public class ChatWebSocketController {
    
    @Autowired
    private ChatMessageService messageService;
    
    @Autowired
    private ChatRoomService roomService;
    
    /**
     * 处理发送消息请求
     * 客户端发送到: /app/chat.sendMessage
     * 服务端广播到: /topic/room/{roomId}
     */
    @MessageMapping("/chat.sendMessage")
    @SendTo("/topic/room/{roomId}")
    public ChatMessageDTO sendMessage(@DestinationVariable Long roomId,
                                    @Payload MessageRequest request,
                                    Principal principal) {
        
        // 验证用户权限
        Long userId = Long.valueOf(principal.getName());
        if (!roomService.hasPermission(roomId, userId)) {
            throw new AccessDeniedException("无权限访问该聊天室");
        }
        
        // 创建消息
        ChatMessageDTO message = messageService.createMessage(roomId, userId, request);
        
        // 保存到数据库
        messageService.saveMessage(message);
        
        // 返回消息（自动广播给订阅者）
        return message;
    }
    
    /**
     * 处理用户加入聊天室
     */
    @MessageMapping("/chat.join")
    @SendTo("/topic/room/{roomId}")
    public ChatMessageDTO joinRoom(@DestinationVariable Long roomId,
                                 Principal principal) {
        
        Long userId = Long.valueOf(principal.getName());
        User user = userService.getById(userId);
        
        // 创建系统消息
        ChatMessageDTO systemMessage = messageService.createSystemMessage(
            roomId, user.getName() + " 加入了聊天室"
        );
        
        return systemMessage;
    }
    
    /**
     * 发送私人消息
     * 客户端发送到: /app/chat.private
     * 服务端发送到: /user/{targetUserId}/queue/private
     */
    @MessageMapping("/chat.private")
    public void sendPrivateMessage(@Payload PrivateMessageRequest request,
                                 Principal principal) {
        
        Long senderId = Long.valueOf(principal.getName());
        
        // 创建私人消息
        ChatMessageDTO message = messageService.createPrivateMessage(
            senderId, request.getTargetUserId(), request.getContent()
        );
        
        // 保存消息
        messageService.saveMessage(message);
        
        // 发送给目标用户
        messagingTemplate.convertAndSendToUser(
            request.getTargetUserId().toString(),
            "/queue/private",
            message
        );
    }
}
```

### 高级配置示例
```java
@Configuration
@EnableWebSocketMessageBroker
public class AdvancedWebSocketConfig implements WebSocketMessageBrokerConfigurer {
    
    @Autowired
    private AuthChannelInterceptor authChannelInterceptor;
    
    @Autowired
    private MessageLoggingInterceptor loggingInterceptor;
    
    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {
        // 使用外部消息代理（如RabbitMQ）
        registry.enableStompBrokerRelay("/topic", "/queue")
                .setRelayHost("localhost")
                .setRelayPort(61613)
                .setClientLogin("guest")
                .setClientPasscode("guest")
                .setSystemLogin("guest")
                .setSystemPasscode("guest")
                .setVirtualHost("/");
        
        registry.setApplicationDestinationPrefixes("/app");
        registry.setUserDestinationPrefix("/user");
    }
    
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/ws/message")
                .setAllowedOriginPatterns("https://*.example.com", "http://localhost:*")
                .withSockJS()
                .setSessionCookieNeeded(false)
                .setHeartbeatTime(25000);
    }
    
    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        registration.interceptors(authChannelInterceptor, loggingInterceptor)
                   .taskExecutor()
                   .corePoolSize(4)
                   .maxPoolSize(8)
                   .keepAliveSeconds(60);
    }
    
    @Override
    public void configureClientOutboundChannel(ChannelRegistration registration) {
        registration.taskExecutor()
                   .corePoolSize(4)
                   .maxPoolSize(8);
    }
    
    @Override
    public void configureWebSocketTransport(WebSocketTransportRegistration registry) {
        registry.setMessageSizeLimit(64 * 1024)
               .setSendBufferSizeLimit(512 * 1024)
               .setSendTimeLimit(20000);
    }
}
```

### 监控和管理
```java
@Component
public class WebSocketEventListener {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketEventListener.class);
    
    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        String userId = headerAccessor.getUser().getName();
        
        logger.info("用户连接WebSocket: userId={}, sessionId={}", userId, sessionId);
        
        // 记录在线用户
        onlineUserService.addOnlineUser(userId, sessionId);
    }
    
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        
        logger.info("用户断开WebSocket连接: sessionId={}", sessionId);
        
        // 移除在线用户
        onlineUserService.removeOnlineUser(sessionId);
    }
    
    @EventListener
    public void handleSubscribeEvent(SessionSubscribeEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String destination = headerAccessor.getDestination();
        String userId = headerAccessor.getUser().getName();
        
        logger.info("用户订阅频道: userId={}, destination={}", userId, destination);
    }
}
```

## 注意事项

### 安全配置
1. **身份验证**: 确保所有WebSocket连接都经过身份验证
2. **权限控制**: 验证用户对特定频道的访问权限
3. **跨域策略**: 生产环境应限制允许的来源域名
4. **消息过滤**: 对传输的消息内容进行安全过滤

### 性能优化
1. **连接池配置**: 合理配置线程池大小
2. **消息大小限制**: 设置合适的消息大小限制
3. **心跳机制**: 配置适当的心跳间隔
4. **缓冲区设置**: 优化发送缓冲区大小

### 可靠性保证
1. **重连机制**: 客户端实现自动重连逻辑
2. **消息确认**: 重要消息需要确认机制
3. **异常处理**: 完善的异常处理和错误恢复
4. **监控告警**: 实时监控连接状态和消息传输

### 扩展性考虑
1. **外部代理**: 大规模部署时使用外部消息代理
2. **集群支持**: 支持多实例集群部署
3. **负载均衡**: 配置WebSocket负载均衡
4. **水平扩展**: 支持水平扩展的架构设计
