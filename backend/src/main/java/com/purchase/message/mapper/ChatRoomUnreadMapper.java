package com.purchase.message.mapper;

import com.purchase.message.entity.ChatRoomUnread;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 聊天室未读消息计数Mapper接口
 */
@Mapper
public interface ChatRoomUnreadMapper {
    
    /**
     * 插入或更新未读消息计数
     */
    @Insert("INSERT INTO chat_room_unread (room_id, user_id, unread_count) " +
            "VALUES (#{roomId}, #{userId}, 1) " +
            "ON DUPLICATE KEY UPDATE unread_count = unread_count + 1")
    void incrementUnreadCount(@Param("roomId") Long roomId, @Param("userId") Long userId);

    /**
     * 重置指定聊天室的未读消息计数
     */
    @Update("UPDATE chat_room_unread SET unread_count = 0 " +
            "WHERE room_id = #{roomId} AND user_id = #{userId}")
    void resetUnreadCount(@Param("roomId") Long roomId, @Param("userId") Long userId);

    /**
     * 获取用户所有聊天室的未读消息计数
     */
    @Select("SELECT * FROM chat_room_unread WHERE user_id = #{userId}")
    List<ChatRoomUnread> getUserUnreadCounts(@Param("userId") Long userId);

    /**
     * 获取指定聊天室的未读消息计数
     */
    @Select("SELECT * FROM chat_room_unread WHERE room_id = #{roomId} AND user_id = #{userId}")
    ChatRoomUnread getUnreadCount(@Param("roomId") Long roomId, @Param("userId") Long userId);
} 