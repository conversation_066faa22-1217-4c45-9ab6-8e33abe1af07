# ChatRoomMapper.md

## 1. 文件概述

`ChatRoomMapper.java` 是消息模块中的一个MyBatis-Plus Mapper接口，位于 `com.purchase.message.mapper` 包中。它继承自MyBatis-Plus的 `BaseMapper<ChatRoom>` 接口，并在此基础上定义了一个自定义的查询方法 `getUserChatRooms`。`ChatRoom` 实体代表了系统中的一个聊天室，包含了参与者信息。该Mapper接口是聊天室服务层与数据库进行交互的桥梁，负责将业务对象的操作转换为SQL语句，实现聊天室的持久化管理和特定查询。

## 2. 核心功能

*   **基础CRUD操作**: 继承 `BaseMapper`，自动拥有对 `ChatRoom` 实体进行插入（`insert`）、根据ID查询（`selectById`）、根据条件查询列表（`selectList`）、更新（`updateById`）和删除（`deleteById`）等基础的增删改查功能。
*   **用户聊天室列表查询**: 提供了 `getUserChatRooms` 方法，这是一个自定义的复杂查询，用于分页获取指定用户（及其角色）参与的所有聊天室列表。该方法直接返回 `ChatRoomDTO`，意味着它可能包含了JOIN查询和数据转换逻辑。
*   **简化开发**: 通过MyBatis-Plus的自动化特性，无需编写SQL语句即可完成常见的聊天室数据库操作，极大地提高了开发效率。
*   **数据持久化**: 负责将 `ChatRoom` 领域对象的状态同步到数据库，或从数据库加载数据到 `ChatRoom` 对象。

## 3. 接口说明

`ChatRoomMapper` 在继承 `BaseMapper` 的基础上，定义了以下自定义方法：

### 3.1 自定义查询方法

#### getUserChatRooms - 获取用户的聊天室列表
*   **方法签名**: `IPage<ChatRoomDTO> getUserChatRooms(Page<ChatRoomDTO> page, @Param("userId") Long userId, @Param("role") String role)`
*   **描述**: 分页查询指定用户（通过 `userId` 和 `role` 确定）参与的所有聊天室。此方法通常会进行多表联查，以获取聊天室的详细信息，并直接映射到 `ChatRoomDTO`。
*   **参数**:
    *   `page` (Page<ChatRoomDTO>): MyBatis-Plus提供的分页对象，用于传入分页参数（当前页、每页大小）并接收分页结果。
    *   `userId` (Long): 用户的ID。
    *   `role` (String): 用户的角色（例如：`buyer`, `seller`, `admin`, `forwarder`）。
*   **返回值**: `IPage<ChatRoomDTO>` - 包含分页信息和 `ChatRoomDTO` 列表的MyBatis-Plus分页对象。
*   **业务逻辑**: 该方法通常需要在对应的XML Mapper文件中编写复杂的SQL语句，包括 `JOIN` 操作，以根据用户ID和角色查询其所属的聊天室，并可能包含对聊天室最新消息、未读数等信息的聚合。

## 4. 业务规则

*   **多方参与**: 聊天室可能涉及多个参与者（买家、卖家、货代、管理员），`ChatRoom` 实体和 `getUserChatRooms` 的SQL实现需要正确处理这些多对多或多对一的关联关系。
*   **分页**: 聊天室列表通常需要分页展示，以应对用户拥有大量聊天室的情况。
*   **数据转换**: `getUserChatRooms` 直接返回 `ChatRoomDTO`，这意味着Mapper层负责将数据库查询结果映射到DTO，这通常通过MyBatis的 `resultMap` 或直接在SQL中选择所需字段实现。

## 5. 使用示例

```java
// 1. 在 ChatRoomService 实现中获取用户聊天室列表
@Service
public class ChatRoomServiceImpl implements ChatRoomService {
    @Autowired
    private ChatRoomMapper chatRoomMapper;

    @Override
    public IPage<ChatRoomDTO> getUserChatRooms(Long userId, String role, Integer pageNum, Integer pageSize) {
        Page<ChatRoomDTO> page = new Page<>(pageNum, pageSize);
        return chatRoomMapper.getUserChatRooms(page, userId, role);
    }
}

// 2. 在 ChatRoomController 中调用服务获取聊天室列表
@RestController
@RequestMapping("/api/v1/message/rooms")
public class ChatRoomController {
    @Autowired
    private ChatRoomService chatRoomService;

    @GetMapping
    @PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')")
    public Result<IPage<ChatRoomDTO>> getUserChatRooms(
            @RequestParam Long userId,
            @RequestParam String role,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        // 权限验证：确保userId与当前认证用户匹配
        // ...

        IPage<ChatRoomDTO> chatRooms = chatRoomService.getUserChatRooms(userId, role, page, size);
        return Result.success(chatRooms);
    }
}

// 3. 对应的 XML Mapper 文件 (ChatRoomMapper.xml) 示例
<!-- 假设 chat_room 表有 buyer_id, seller_id, forwarder_id, admin_id 字段 -->
<!-- 假设 chat_room_member 表用于多对多关系 -->
<mapper namespace="com.purchase.message.mapper.ChatRoomMapper">
    <select id="getUserChatRooms" resultType="com.purchase.message.dto.ChatRoomDTO">
        SELECT
            cr.id,
            cr.buyer_id,
            cr.seller_id,
            cr.forwarder_id,
            cr.admin_id,
            cr.created_at,
            cr.updated_at
            <!-- 还可以联查最新消息、未读数等 -->
        FROM
            chat_room cr
        WHERE
            (cr.buyer_id = #{userId} AND #{role} = 'buyer') OR
            (cr.seller_id = #{userId} AND #{role} = 'seller') OR
            (cr.forwarder_id = #{userId} AND #{role} = 'forwarder') OR
            (cr.admin_id = #{userId} AND #{role} = 'admin')
        ORDER BY cr.updated_at DESC
    </select>
</mapper>

// 4. 测试示例
@SpringBootTest
class ChatRoomMapperTest {
    @Autowired
    private ChatRoomMapper chatRoomMapper;

    @Test
    @Transactional
    void testGetUserChatRooms() {
        // 插入测试数据
        ChatRoom room1 = new ChatRoom(); room1.setBuyerId(1L); room1.setSellerId(2L); chatRoomMapper.insert(room1);
        ChatRoom room2 = new ChatRoom(); room2.setBuyerId(1L); room2.setForwarderId(3L); chatRoomMapper.insert(room2);
        ChatRoom room3 = new ChatRoom(); room3.setSellerId(2L); room3.setForwarderId(3L); chatRoomMapper.insert(room3);

        Page<ChatRoomDTO> page = new Page<>(1, 10);
        IPage<ChatRoomDTO> buyerRooms = chatRoomMapper.getUserChatRooms(page, 1L, "buyer");
        assertThat(buyerRooms.getRecords()).hasSize(2);
        assertThat(buyerRooms.getRecords().get(0).getBuyerId()).isEqualTo(1L);

        IPage<ChatRoomDTO> sellerRooms = chatRoomMapper.getUserChatRooms(page, 2L, "seller");
        assertThat(sellerRooms.getRecords()).hasSize(2);
    }
}
```

## 6. 注意事项

*   **MyBatis-Plus集成**: 继承 `BaseMapper` 使得该Mapper自动拥有强大的CRUD能力，减少了重复代码。
*   **自定义SQL**: `getUserChatRooms` 方法需要手动编写SQL，通常在XML文件中实现。SQL的复杂性取决于 `ChatRoomDTO` 需要包含的字段以及聊天室成员关系的存储方式（例如，是直接在 `chat_room` 表中存储 `buyer_id`, `seller_id`，还是通过一个 `chat_room_member` 关联表）。
*   **性能优化**: `getUserChatRooms` 的SQL查询需要特别关注性能。应确保 `chat_room` 表中与用户ID和角色相关的字段（如 `buyer_id`, `seller_id`, `forwarder_id`, `admin_id`）以及 `updated_at` 字段建立了合适的索引。
*   **事务管理**: 在服务层调用Mapper方法时，必须确保在事务中执行，以保证数据操作的原子性和一致性。
*   **SQL注入防护**: 使用 `@Param` 注解和MyBatis的参数绑定机制可以有效防止SQL注入攻击。
*   **DTO映射**: 确保XML中 `resultType` 或 `resultMap` 的配置能够正确地将查询结果映射到 `ChatRoomDTO` 对象。
*   **逻辑删除**: 如果 `ChatRoom` 实体支持逻辑删除，那么 `getUserChatRooms` 的SQL中也应包含 `AND deleted = '0'` 条件。