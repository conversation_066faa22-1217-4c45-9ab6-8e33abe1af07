package com.purchase.message.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class MessageRequest {
    @NotNull(message = "聊天室ID不能为空")
    private Long roomId;
    
    @NotBlank(message = "消息类型不能为空")
    private String messageType;
    
    private String textContent;
    
    private String imageUrl;
    
    private String fileUrl;
    
    // 发送者角色，用于WebSocket通信
    private String senderRole;
} 