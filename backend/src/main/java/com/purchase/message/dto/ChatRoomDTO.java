package com.purchase.message.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ChatRoomDTO {
    private Long id;
    private Long buyerId;
    private Long sellerId;
    private Long forwarderId;
    private Long adminId;
    private LocalDateTime lastMessageTime;
    private Integer status;
    private LocalDateTime createdAt;
    private String lastMessage;
    
    // 添加用户名字段，方便前端直接显示
    private String buyerName;      // 买家用户名
    private String sellerName;     // 卖家用户名
    private String forwarderName;  // 货代用户名
    private String adminName;      // 管理员用户名
} 