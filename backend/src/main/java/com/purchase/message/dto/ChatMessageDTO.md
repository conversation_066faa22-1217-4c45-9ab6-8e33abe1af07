# ChatMessageDTO.java

## 文件概述 (File Overview)
`ChatMessageDTO.java` 是聊天消息的数据传输对象，位于 `com.purchase.message.dto` 包中。该类定义了聊天消息在前后端传输时的数据结构，通过Bean Validation注解提供数据验证功能，确保消息数据的完整性和安全性。作为聊天系统的核心DTO，它封装了聊天消息的所有相关信息，包括消息内容、发送者信息、消息类型、时间戳等数据，并支持多种消息格式和实时通信需求。

## 核心功能 (Core Functionality)
*   **消息数据传输**: 封装聊天消息的所有相关信息
*   **数据验证**: 通过Bean Validation注解提供消息内容验证
*   **类型安全**: 提供强类型的数据结构，确保消息数据的准确性
*   **序列化支持**: 支持JSON序列化和反序列化，便于实时通信
*   **多媒体支持**: 支持文本、图片、文件、语音等多种消息类型
*   **状态管理**: 包含消息状态信息，如已发送、已读等
*   **时间戳**: 精确记录消息的发送和接收时间
*   **发送者信息**: 包含发送者的基本身份信息
*   **聊天室关联**: 明确消息所属的聊天室或会话
*   **回复支持**: 支持消息回复和引用功能
*   **附件处理**: 支持消息附件的元数据信息
*   **安全过滤**: 集成内容安全过滤和敏感词检测

## 业务规则 (Business Rules)
*   **必填字段**: 消息内容、发送者ID、聊天室ID等关键字段必须提供
*   **内容限制**: 消息内容有长度限制，防止过长消息影响性能
*   **类型约束**: 消息类型必须是预定义的有效类型
*   **权限验证**: 发送者必须有权限在指定聊天室发送消息
*   **内容安全**: 消息内容需要通过安全检查和敏感词过滤
*   **文件限制**: 文件类型消息有大小和格式限制
*   **频率控制**: 消息发送有频率限制，防止刷屏
*   **状态一致性**: 消息状态变更必须符合业务逻辑

## 注意事项 (Notes)
*   **数据验证**: 使用Bean Validation注解进行严格的消息内容验证
*   **序列化**: 确保所有字段都能正确序列化，特别是时间和附件字段
*   **空值处理**: 合理处理可选字段的空值情况
*   **类型转换**: 注意数据类型的正确转换，特别是时间戳字段
*   **安全考虑**: 对消息内容进行安全过滤，防止XSS攻击
*   **版本兼容**: 字段变更需要考虑客户端兼容性
*   **文档维护**: 及时更新字段注释和消息类型说明
*   **测试覆盖**: 为不同类型的消息编写充分的测试
*   **性能考虑**: 避免包含过多不必要的字段，优化传输效率
*   **实时性**: 确保DTO设计支持实时消息推送需求
