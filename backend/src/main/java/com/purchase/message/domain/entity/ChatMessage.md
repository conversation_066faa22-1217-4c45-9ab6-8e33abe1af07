# ChatMessage.md

## 1. 文件概述

`ChatMessage` 是消息模块中的一个实体（Entity），位于 `com.purchase.message.entity` 包中。它直接映射到数据库的 `chat_message` 表，用于持久化存储聊天室中的每一条消息记录。这个实体包含了消息的所有核心属性，如发送者、消息类型、内容（文本、图片、文件或系统消息），以及消息所属的聊天室和创建时间。它是构建即时通讯功能的基础数据模型。

## 2. 核心功能

*   **消息存储**: 作为数据模型，它定义了聊天消息在数据库中的存储结构，包括消息ID、所属聊天室ID、发送者信息、消息内容等。
*   **多媒体消息支持**: 通过 `textContent`, `imageUrl`, `fileUrl`, `systemContent` 等字段，支持多种消息类型（文本、图片、文件、系统消息），使得聊天功能更加丰富。
*   **发送者身份记录**: 记录了 `senderId` 和 `senderRole`，便于追溯消息来源和进行权限管理。
*   **时间戳**: `createdAt` 字段记录了消息的创建时间，对于消息排序和历史记录查询至关重要。
*   **ORM映射**: 通过MyBatis-Plus的注解（`@TableName`, `@TableId`），实现了与数据库表的自动映射，简化了数据访问层的开发。

## 3. 接口说明

作为实体类，`ChatMessage` 主要通过其属性的Getter和Setter方法与外部交互。它没有复杂的业务方法，其行为主要由服务层（如 `ChatMessageService`）来驱动。

### 3.1 属性列表

- **`id` (Long)**: 消息的唯一标识符，主键，自增长。
- **`roomId` (Long)**: 消息所属的聊天室ID。
- **`senderId` (Long)**: 消息发送者的用户ID。
- **`senderRole` (String)**: 消息发送者的角色（例如：`buyer`, `seller`, `admin`）。
- **`messageType` (String)**: 消息的类型（例如：`text`, `image`, `file`, `system`）。
- **`textContent` (String)**: 文本消息的内容。当 `messageType` 为 `text` 或 `system` 时使用。
- **`imageUrl` (String)**: 图片消息的URL。当 `messageType` 为 `image` 时使用。
- **`fileUrl` (String)**: 文件消息的URL。当 `messageType` 为 `file` 时使用。
- **`systemContent` (String)**: 系统消息的内容。当 `messageType` 为 `system` 时使用。
- **`createdAt` (LocalDateTime)**: 消息的创建时间。

## 4. 业务规则

*   **消息类型互斥**: 在一条 `ChatMessage` 记录中，`textContent`, `imageUrl`, `fileUrl`, `systemContent` 字段通常是互斥的，即只有与 `messageType` 匹配的字段才会有值，其他字段为 `null`。例如，如果 `messageType` 是 `image`，那么 `imageUrl` 字段会有值，而 `textContent`, `fileUrl`, `systemContent` 则为 `null`。
*   **数据完整性**: `roomId`, `senderId`, `senderRole`, `messageType` 是消息记录的必要字段，在创建消息时必须提供。
*   **时间戳**: `createdAt` 字段应在消息创建时自动生成，确保消息的顺序性。

## 5. 使用示例

```java
// 1. 在 ChatMessageService 实现中创建并保存消息
@Service
public class ChatMessageServiceImpl implements ChatMessageService {
    @Autowired
    private ChatMessageMapper chatMessageMapper;

    @Override
    @Transactional
    public Long sendMessage(Long senderId, String senderRole, MessageRequest request) {
        ChatMessage message = new ChatMessage();
        message.setRoomId(request.getRoomId());
        message.setSenderId(senderId);
        message.setSenderRole(senderRole);
        message.setCreatedAt(LocalDateTime.now());

        // 根据消息类型设置内容
        switch (request.getMessageType()) {
            case "text":
                message.setMessageType("text");
                message.setTextContent(request.getContent());
                break;
            case "image":
                message.setMessageType("image");
                message.setImageUrl(request.getContent()); // 假设content是图片URL
                break;
            // ... 其他类型
            default:
                throw new IllegalArgumentException("不支持的消息类型");
        }

        chatMessageMapper.insert(message);
        return message.getId();
    }
}

// 2. 在 Mapper 接口中定义查询方法
@Mapper
public interface ChatMessageMapper extends BaseMapper<ChatMessage> {
    // 获取某个聊天室的历史消息，按时间倒序
    List<ChatMessage> findByRoomIdOrderByCreatedAtDesc(@Param("roomId") Long roomId, Page<ChatMessage> page);
}

// 3. 在 Controller 中获取聊天记录并转换为 DTO
@RestController
@RequestMapping("/api/v1/message/rooms/{roomId}/messages")
public class ChatMessageController {
    @Autowired
    private ChatMessageService chatMessageService;

    @GetMapping
    public Result<IPage<ChatMessageDTO>> getMessages(
            @PathVariable Long roomId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        IPage<ChatMessageDTO> messages = chatMessageService.getChatMessages(roomId, page, size);
        return Result.success(messages);
    }
}

// 4. 测试示例
@SpringBootTest
class ChatMessageTest {
    @Autowired
    private ChatMessageMapper chatMessageMapper;

    @Test
    @Transactional
    void testInsertAndRetrieveMessage() {
        ChatMessage message = new ChatMessage();
        message.setRoomId(1L);
        message.setSenderId(100L);
        message.setSenderRole("buyer");
        message.setMessageType("text");
        message.setTextContent("Hello, this is a test message.");
        message.setCreatedAt(LocalDateTime.now());

        chatMessageMapper.insert(message);

        assertThat(message.getId()).isNotNull();

        ChatMessage retrievedMessage = chatMessageMapper.selectById(message.getId());
        assertThat(retrievedMessage).isNotNull();
        assertThat(retrievedMessage.getTextContent()).isEqualTo("Hello, this is a test message.");
        assertThat(retrievedMessage.getRoomId()).isEqualTo(1L);
    }
}
```

## 6. 注意事项

*   **数据库映射**: `@TableName` 和 `@TableId` 注解确保了实体与数据库表和主键的正确映射。`IdType.AUTO` 表示主键由数据库自增长。
*   **Lombok**: 使用 `@Data` 注解自动生成Getter、Setter、`equals`、`hashCode` 和 `toString` 方法，简化了实体类的编写。
*   **消息内容存储**: 对于不同类型的消息内容，使用独立的字段存储是一种常见做法。在实际应用中，也可以考虑使用一个JSON字段来存储所有类型的消息内容，以增加灵活性。
*   **索引**: `roomId` 和 `createdAt` 字段在数据库中应建立索引，以优化按聊天室查询历史消息的性能。
*   **数据量**: 聊天消息是典型的海量数据。需要考虑数据库的分库分表策略，以及历史消息的归档和清理机制。
*   **安全性**: 消息内容可能包含敏感信息或恶意脚本。在存储和展示前，应对 `textContent` 等字段进行内容过滤和XSS防护。
*   **时间同步**: 确保应用服务器和数据库的时间同步，以保证 `createdAt` 字段的准确性。
*   **可扩展性**: 如果未来需要支持更多消息类型（如语音、视频、表情包），可以通过扩展 `messageType` 枚举和增加相应的字段来支持。
