package com.purchase.message.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.common.response.Result;
import com.purchase.message.dto.ChatMessageDTO;
import com.purchase.message.dto.ChatRoomDTO;
import com.purchase.message.dto.MessageRequest;
import com.purchase.message.service.ChatMessageService;
import com.purchase.message.service.ChatRoomService;
import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/message")
public class ChatMessageController {

    @Autowired
    private ChatMessageService chatMessageService;
    
    @Autowired
    private ChatRoomService chatRoomService;

    /**
     * 获取聊天室的消息列表
     */
    @GetMapping("/room/{roomId}")
    public Result<IPage<ChatMessageDTO>> getChatMessages(
            @PathVariable Long roomId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        // 验证用户权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUserId = ((Claims) authentication.getCredentials()).get("userId", String.class);
        
        // 验证聊天室是否存在
        ChatRoomDTO chatRoom = chatRoomService.getChatRoomDetail(roomId);
        if (chatRoom == null) {
            return Result.error("聊天室不存在");
        }
        
        // 验证当前用户是否是聊天室的成员，安全处理null值
        boolean isMember = (chatRoom.getBuyerId() != null && chatRoom.getBuyerId().toString().equals(currentUserId)) || 
                          (chatRoom.getSellerId() != null && chatRoom.getSellerId().toString().equals(currentUserId)) || 
                          (chatRoom.getForwarderId() != null && chatRoom.getForwarderId().toString().equals(currentUserId)) ||
                          (chatRoom.getAdminId() != null && chatRoom.getAdminId().toString().equals(currentUserId));
                          
        if (!isMember) {
            return Result.error("无权查看该聊天室的消息");
        }
        
        IPage<ChatMessageDTO> messages = chatMessageService.getChatMessages(roomId, page, size);
        return Result.success(messages);
    }

    /**
     * 发送消息
     */
    @PostMapping("/send")
    public Result<Long> sendMessage(
            @RequestParam String senderRole,
            @RequestBody @Validated MessageRequest request) {
        // 验证用户权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUserId = ((Claims) authentication.getCredentials()).get("userId", String.class);
        Long senderId = Long.parseLong(currentUserId);
        
        // 验证用户是否是聊天室的成员
        ChatRoomDTO chatRoom = chatRoomService.getChatRoomDetail(request.getRoomId());
        if (chatRoom == null) {
            return Result.error("聊天室不存在");
        }
        
        // 验证发送者角色是否正确，安全处理null值
        boolean isValid = false;
        switch (senderRole) {
            case "buyer":
                isValid = chatRoom.getBuyerId() != null && chatRoom.getBuyerId().equals(senderId);
                break;
            case "seller":
                isValid = chatRoom.getSellerId() != null && chatRoom.getSellerId().equals(senderId);
                break;
            case "forwarder":
                isValid = chatRoom.getForwarderId() != null && chatRoom.getForwarderId().equals(senderId);
                break;
            case "admin":
                isValid = chatRoom.getAdminId() != null && chatRoom.getAdminId().equals(senderId);
                break;
        }
        
        if (!isValid) {
            return Result.error("发送者角色与实际不符");
        }
        
        Long messageId = chatMessageService.sendMessage(senderId, senderRole, request);
        return Result.success(messageId);
    }

    /**
     * 发送系统消息
     * @param roomId 聊天室ID
     * @param type 消息类型
     * @return 消息ID
     */
    @PostMapping("/system/send")
    public Result<Long> sendSystemMessage(
            @RequestParam Long roomId,
            @RequestParam String type) {
        // 验证用户权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUserId = ((Claims) authentication.getCredentials()).get("userId", String.class);
        Long userId = Long.parseLong(currentUserId);
        
        // 获取用户角色
        String role = authentication.getAuthorities().iterator().next().getAuthority();
        
        // 验证聊天室是否存在
        ChatRoomDTO chatRoom = chatRoomService.getChatRoomDetail(roomId);
        if (chatRoom == null) {
            return Result.error("聊天室不存在");
        }
        
        // 验证权限：管理员可以发送任何系统消息，买家/货代/卖家只能在自己的聊天室发送
        if (!"admin".equals(role)) {
            boolean isParticipant = (chatRoom.getBuyerId() != null && chatRoom.getBuyerId().equals(userId)) || 
                                   (chatRoom.getSellerId() != null && chatRoom.getSellerId().equals(userId)) || 
                                   (chatRoom.getForwarderId() != null && chatRoom.getForwarderId().equals(userId));
            if (!isParticipant) {
            return Result.error("您没有权限发送系统消息");
            }
        }
        
        try {
            Long messageId = chatMessageService.sendSystemMessage(roomId, type);
            return Result.success(messageId);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
} 