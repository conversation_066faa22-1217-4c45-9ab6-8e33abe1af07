package com.purchase.message.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.common.response.Result;
import com.purchase.message.dto.ChatRoomDTO;
import com.purchase.message.service.ChatRoomService;
import com.purchase.message.service.ChatRoomUnreadService;
import com.purchase.user.service.UserService;
import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 聊天室管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/message/rooms")
public class ChatRoomController {

    @Autowired
    private ChatRoomService chatRoomService;

    @Autowired
    private ChatRoomUnreadService chatRoomUnreadService;

    @Autowired
    private UserService userService;

    /**
     * 获取用户的聊天室列表
     * 
     * @param userId 用户ID
     * @param role 用户角色（buyer-买家, seller-卖家, forwarder-货代, admin-管理员）
     * @param page 页码，从1开始
     * @param size 每页大小
     * @return 分页的聊天室列表
     * 
     * @throws SecurityException 当尝试查询其他用户的聊天室时抛出
     * @see ChatRoomDTO
     */
    @GetMapping
    @PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')")
    public Result<IPage<ChatRoomDTO>> getUserChatRooms(
            @RequestParam Long userId,
            @RequestParam String role,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        System.out.println("查询聊天室");
        // 验证用户权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUserId = ((Claims) authentication.getCredentials()).get("userId", String.class);
        
        // 只能查询自己的聊天室
        if (!userId.toString().equals(currentUserId)) {
            return Result.error("无权查询其他用户的聊天室");
        }
        
        IPage<ChatRoomDTO> chatRooms = chatRoomService.getUserChatRooms(userId, role, page, size);
        return Result.success(chatRooms);
    }

    /**
     * 创建聊天室
     * 
     * @param buyerId 买家用户ID (可选)
     * @param sellerId 卖家用户ID (可选)
     * @param forwarderId 货代用户ID (可选)
     * @param adminId 管理员用户ID (可选，默认为3)
     * @return 创建的聊天室ID
     */
    @PostMapping
    @PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')")
    public Result<Long> createChatRoom(
            @RequestParam(required = false, defaultValue = "0") Long buyerId,
            @RequestParam(required = false, defaultValue = "0") Long sellerId,
            @RequestParam(required = false, defaultValue = "0") Long forwarderId,
            @RequestParam(required = false, defaultValue = "3") Long adminId) {
        // 验证用户权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Claims claims = (Claims) authentication.getCredentials();
        String currentUserId = claims.get("userId", String.class);
        String currentRole = claims.get("role", String.class);
        
        // 验证至少有两个参与者
        int participantCount = 0;
        if (buyerId > 0) participantCount++;
        if (sellerId > 0) participantCount++;
        if (forwarderId > 0) participantCount++;
        if (adminId > 0) participantCount++;
        
        if (participantCount < 2) {
            return Result.error("聊天室至少需要两个参与者");
        }
        
        // 验证创建权限：用户必须是参与者之一
        boolean isParticipant = (buyerId > 0 && currentUserId.equals(buyerId.toString())) || 
                               (sellerId > 0 && currentUserId.equals(sellerId.toString())) || 
                               (forwarderId > 0 && currentUserId.equals(forwarderId.toString())) ||
                               (adminId > 0 && currentUserId.equals(adminId.toString()));
        
        if (!isParticipant) {
            return Result.error("只能创建自己参与的聊天室");
        }
        
        // 验证角色匹配：确保传递的ID对应的是正确的角色
        if (buyerId > 0 && currentUserId.equals(buyerId.toString()) && !"buyer".equals(currentRole) && !"admin".equals(currentRole)) {
            return Result.error("您不是买家，无法以买家身份创建聊天室");
        }
        
        if (sellerId > 0 && currentUserId.equals(sellerId.toString()) && !"seller".equals(currentRole) && !"admin".equals(currentRole)) {
            return Result.error("您不是卖家，无法以卖家身份创建聊天室");
        }
        
        if (forwarderId > 0 && currentUserId.equals(forwarderId.toString()) && !"forwarder".equals(currentRole) && !"admin".equals(currentRole)) {
            return Result.error("您不是货代，无法以货代身份创建聊天室");
        }
        
        if (adminId > 0 && currentUserId.equals(adminId.toString()) && !"admin".equals(currentRole)) {
            return Result.error("您不是管理员，无法以管理员身份创建聊天室");
        }
        
        // 验证其他参与者的角色身份
        try {
            // 验证买家ID是否有效
            if (buyerId > 0) {
                boolean isBuyerValid = userService.validateUserRole(buyerId, "buyer");
                if (!isBuyerValid) {
                    return Result.error("指定的买家ID无效");
                }
            }
            
            // 验证卖家ID是否有效
            if (sellerId > 0) {
                boolean isSellerValid = userService.validateUserRole(sellerId, "seller");
                if (!isSellerValid) {
                    return Result.error("指定的卖家ID无效");
                }
            }
            
            // 如果使用了货代ID，验证货代ID是否有效
            if (forwarderId > 0) {
                boolean isForwarderValid = userService.validateUserRole(forwarderId, "forwarder");
                if (!isForwarderValid) {
                    return Result.error("指定的货代ID无效");
                }
            }
            
            // 验证管理员ID
            if (adminId > 0 && adminId != 3) { // 假设3是默认管理员ID
                boolean isAdminValid = userService.validateUserRole(adminId, "admin");
                if (!isAdminValid) {
                    return Result.error("指定的管理员ID无效");
                }
            }
        } catch (Exception e) {
            return Result.error("验证用户角色时发生错误: " + e.getMessage());
        }
        
        // 转换值为0的ID为null，以避免外键约束错误
        Long finalBuyerId = buyerId > 0 ? buyerId : null;
        Long finalSellerId = sellerId > 0 ? sellerId : null;
        Long finalForwarderId = forwarderId > 0 ? forwarderId : null;
        Long finalAdminId = adminId > 0 ? adminId : null;
        
        Long roomId = chatRoomService.createChatRoom(finalBuyerId, finalSellerId, finalForwarderId, finalAdminId);
        return Result.success(roomId);
    }
    
    /**
     * 通过买家ID和卖家ID搜索聊天室
     * 
     * @param buyerId 买家ID
     * @param sellerId 卖家ID
     * @return 聊天室信息，如果不存在则返回null
     */
    @GetMapping("/search/buyer-seller")
    @PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')")
    public Result<ChatRoomDTO> searchChatRoomByBuyerAndSeller(
            @RequestParam Long buyerId,
            @RequestParam Long sellerId) {
        // 验证用户权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Claims claims = (Claims) authentication.getCredentials();
        String currentUserId = claims.get("userId", String.class);
        String currentRole = claims.get("role", String.class);
        
        // 非管理员只能查询与自己相关的聊天室
        if (!"admin".equals(currentRole)) {
            boolean isParticipant = currentUserId.equals(buyerId.toString()) || 
                                   currentUserId.equals(sellerId.toString());
            
            if (!isParticipant) {
                return Result.error("只能查询与自己相关的聊天室");
            }
        }
        
        ChatRoomDTO chatRoom = chatRoomService.findByBuyerAndSeller(buyerId, sellerId);
        return Result.success(chatRoom);
    }
    
    /**
     * 通过买家ID和货代ID搜索聊天室
     * 
     * @param buyerId 买家ID
     * @param forwarderId 货代ID
     * @return 聊天室信息，如果不存在则返回null
     */
    @GetMapping("/search/buyer-forwarder")
    @PreAuthorize("hasAnyAuthority('admin', 'buyer', 'forwarder')")
    public Result<ChatRoomDTO> searchChatRoomByBuyerAndForwarder(
            @RequestParam Long buyerId,
            @RequestParam Long forwarderId) {
        // 验证用户权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Claims claims = (Claims) authentication.getCredentials();
        String currentUserId = claims.get("userId", String.class);
        String currentRole = claims.get("role", String.class);
        
        // 非管理员只能查询与自己相关的聊天室
        if (!"admin".equals(currentRole)) {
            boolean isParticipant = currentUserId.equals(buyerId.toString()) || 
                                   currentUserId.equals(forwarderId.toString());
            
            if (!isParticipant) {
                return Result.error("只能查询与自己相关的聊天室");
            }
        }
        
        ChatRoomDTO chatRoom = chatRoomService.findByBuyerAndForwarder(buyerId, forwarderId);
        return Result.success(chatRoom);
    }
    
    /**
     * 通过卖家ID和货代ID搜索聊天室
     * 
     * @param sellerId 卖家ID
     * @param forwarderId 货代ID
     * @return 聊天室信息，如果不存在则返回null
     */
    @GetMapping("/search/seller-forwarder")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder')")
    public Result<ChatRoomDTO> searchChatRoomBySellerAndForwarder(
            @RequestParam Long sellerId,
            @RequestParam Long forwarderId) {
        // 验证用户权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Claims claims = (Claims) authentication.getCredentials();
        String currentUserId = claims.get("userId", String.class);
        String currentRole = claims.get("role", String.class);
        
        // 非管理员只能查询与自己相关的聊天室
        if (!"admin".equals(currentRole)) {
            boolean isParticipant = currentUserId.equals(sellerId.toString()) || 
                                   currentUserId.equals(forwarderId.toString());
            
            if (!isParticipant) {
                return Result.error("只能查询与自己相关的聊天室");
            }
        }
        
        ChatRoomDTO chatRoom = chatRoomService.findBySellerAndForwarder(sellerId, forwarderId);
        return Result.success(chatRoom);
    }

    /**
     * 获取聊天室详情
     * 
     * @param roomId 聊天室ID
     * @return 聊天室详细信息
     * 
     * @throws SecurityException 当用户不是聊天室成员时抛出
     * @see ChatRoomDTO
     */
    @GetMapping("/{roomId}")
    @PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')")
    public Result<ChatRoomDTO> getChatRoomDetail(@PathVariable Long roomId) {
        // 验证用户权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUserId = ((Claims) authentication.getCredentials()).get("userId", String.class);
        
        ChatRoomDTO chatRoom = chatRoomService.getChatRoomDetail(roomId);
        if (chatRoom == null) {
            return Result.error("聊天室不存在");
        }
        
        // 验证用户是否是聊天室的成员
        if (!chatRoom.getBuyerId().toString().equals(currentUserId) && 
            !chatRoom.getSellerId().toString().equals(currentUserId) && 
            !chatRoom.getForwarderId().toString().equals(currentUserId) &&
            !chatRoom.getAdminId().toString().equals(currentUserId)) {
            return Result.error("无权查看该聊天室");
        }
        
        return Result.success(chatRoom);
    }

    /**
     * 获取当前用户所有聊天室未读消息统计
     */
    @GetMapping("/unread-count")
    @PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')")
    public Map<String, Object> getMyUnreadCount() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Claims claims = (Claims) authentication.getCredentials();
        Long userId = Long.parseLong(claims.get("userId", String.class));
        Map<Long, Integer> roomUnread = chatRoomUnreadService.getUserUnreadCounts(userId);
        int total = roomUnread.values().stream().mapToInt(Integer::intValue).sum();
        Map<String, Object> result = new HashMap<>();
        result.put("total", total);
        result.put("rooms", roomUnread);
        return result;
    }
    
    /**
     * 兼容旧版本API，创建聊天室
     * 
     * @param buyerId 买家用户ID
     * @param sellerId 卖家用户ID
     * @param adminId 管理员用户ID
     * @return 创建的聊天室ID
     */
    @PostMapping("/legacy")
    @PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller')")
    public Result<Long> createChatRoomLegacy(
            @RequestParam Long buyerId,
            @RequestParam Long sellerId,
            @RequestParam(required = false, defaultValue = "3") Long adminId) {
        // 验证用户权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Claims claims = (Claims) authentication.getCredentials();
        String currentUserId = claims.get("userId", String.class);
        String currentRole = claims.get("role", String.class);
        
        // 验证创建权限：用户必须是参与者之一
        boolean isParticipant = currentUserId.equals(buyerId.toString()) || 
                               currentUserId.equals(sellerId.toString()) || 
                               currentUserId.equals(adminId.toString());
        
        if (!isParticipant) {
            return Result.error("只能创建自己参与的聊天室");
        }
        
        Long roomId = chatRoomService.createChatRoom(buyerId, sellerId, null, adminId);
        return Result.success(roomId);
    }
} 