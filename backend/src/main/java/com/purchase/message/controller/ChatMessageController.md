# ChatMessageController.java

## 文件概述 (File Overview)
`ChatMessageController.java` 是聊天消息管理的REST控制器，负责处理聊天消息相关的HTTP请求。它提供了获取聊天室消息列表、发送消息、标记消息已读等功能。该控制器通过调用 `ChatMessageService` 和 `ChatRoomService` 处理具体的业务逻辑，实现了严格的权限控制，确保只有聊天室成员才能访问相应的消息数据。

## 核心功能 (Core Functionality)
*   **消息列表查询:** 分页获取指定聊天室的消息历史记录。
*   **消息发送:** 支持发送文本消息到指定聊天室。
*   **消息已读标记:** 标记指定聊天室的消息为已读状态。
*   **权限验证:** 验证用户是否为聊天室成员，确保数据安全。
*   **聊天室验证:** 验证聊天室是否存在，防止无效请求。
*   **用户身份识别:** 通过JWT Token获取当前用户身份信息。
*   **异常处理:** 提供完善的异常处理机制，返回友好的错误信息。

## 接口说明 (Interface Description)

### 主要方法 (Main Methods)

#### 1. getChatMessages(@PathVariable Long roomId, @RequestParam Integer page, @RequestParam Integer size)
*   **HTTP方法:** GET
*   **路径:** `/api/v1/message/room/{roomId}`
*   **参数:**
    *   `roomId` - 聊天室ID
    *   `page` - 页码（默认1）
    *   `size` - 每页大小（默认20）
*   **返回值:** `Result<IPage<ChatMessageDTO>>`
*   **功能:** 分页获取指定聊天室的消息列表
*   **业务逻辑:**
    1. 从JWT Token获取当前用户ID
    2. 验证聊天室是否存在
    3. 验证当前用户是否为聊天室成员
    4. 分页查询消息列表并返回

#### 2. sendMessage(@RequestBody MessageRequest request)
*   **HTTP方法:** POST
*   **路径:** `/api/v1/message/send`
*   **参数:** `request` - 消息发送请求对象
*   **返回值:** `Result<ChatMessageDTO>`
*   **功能:** 发送消息到指定聊天室
*   **业务逻辑:**
    1. 验证请求参数
    2. 验证用户权限
    3. 发送消息并返回消息详情

#### 3. markAsRead(@PathVariable Long roomId)
*   **HTTP方法:** PUT
*   **路径:** `/api/v1/message/room/{roomId}/read`
*   **参数:** `roomId` - 聊天室ID
*   **返回值:** `Result<String>`
*   **功能:** 标记指定聊天室的消息为已读
*   **业务逻辑:**
    1. 获取当前用户ID
    2. 验证聊天室权限
    3. 更新未读消息状态

### 权限验证逻辑 (Permission Verification)
控制器实现了严格的权限验证机制：
*   验证用户是否为聊天室的买家、卖家、货代或管理员
*   支持null值安全处理，避免空指针异常
*   确保只有聊天室成员才能访问消息数据

## 使用示例 (Usage Examples)

```java
// 前端JavaScript调用示例
// 1. 获取聊天室消息列表
const getChatMessages = async (roomId, page = 1, size = 20) => {
    try {
        const response = await fetch(`/api/v1/message/room/${roomId}?page=${page}&size=${size}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        if (result.code === 200) {
            console.log('消息列表:', result.data);
            return result.data; // IPage<ChatMessageDTO>
        } else {
            console.error('获取消息失败:', result.message);
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('请求失败:', error);
        throw error;
    }
};

// 2. 发送消息
const sendMessage = async (roomId, content, messageType = 'TEXT') => {
    try {
        const response = await fetch('/api/v1/message/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                roomId: roomId,
                content: content,
                messageType: messageType
            })
        });

        const result = await response.json();
        if (result.code === 200) {
            console.log('消息发送成功:', result.data);
            return result.data;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('发送消息失败:', error);
        throw error;
    }
};

// 3. 标记消息已读
const markMessagesAsRead = async (roomId) => {
    try {
        const response = await fetch(`/api/v1/message/room/${roomId}/read`, {
            method: 'PUT',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        if (result.code === 200) {
            console.log('消息已标记为已读');
            return true;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('标记已读失败:', error);
        throw error;
    }
};

// Java客户端调用示例
@Service
public class ChatClientService {
    @Autowired
    private RestTemplate restTemplate;

    public IPage<ChatMessageDTO> getChatMessages(Long roomId, Integer page, Integer size, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        String url = String.format("/api/v1/message/room/%d?page=%d&size=%d", roomId, page, size);

        try {
            ResponseEntity<Result> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                Result.class
            );

            Result result = response.getBody();
            if (result != null && result.getCode() == 200) {
                return (IPage<ChatMessageDTO>) result.getData();
            }
        } catch (Exception e) {
            log.error("获取聊天消息失败", e);
        }
        return null;
    }

    public ChatMessageDTO sendMessage(MessageRequest request, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<MessageRequest> entity = new HttpEntity<>(request, headers);

        try {
            ResponseEntity<Result> response = restTemplate.postForEntity(
                "/api/v1/message/send",
                entity,
                Result.class
            );

            Result result = response.getBody();
            if (result != null && result.getCode() == 200) {
                return (ChatMessageDTO) result.getData();
            }
        } catch (Exception e) {
            log.error("发送消息失败", e);
        }
        return null;
    }
}
```

## 注意事项 (Notes)
*   **权限验证:** 所有接口都进行严格的权限验证，确保只有聊天室成员才能访问相应的消息数据。
*   **JWT Token解析:** 通过Spring Security的Authentication对象获取JWT Token中的用户信息，需要确保Token的有效性。
*   **空值安全:** 在权限验证时对所有可能为null的字段进行安全处理，避免空指针异常。
*   **聊天室验证:** 在访问消息前先验证聊天室是否存在，防止无效的数据访问。
*   **分页查询:** 消息列表查询支持分页，默认每页20条记录，避免一次性加载过多数据。
*   **异常处理:** 提供完善的异常处理机制，返回友好的错误信息给前端。
*   **数据传输对象:** 使用 `ChatMessageDTO` 等DTO对象进行数据传输，避免直接暴露实体类。
*   **业务委托:** 控制器只负责HTTP请求处理和权限验证，具体的业务逻辑委托给Service层处理。
*   **响应格式:** 所有接口都返回统一的 `Result<T>` 格式，确保前端能够统一处理响应数据。
