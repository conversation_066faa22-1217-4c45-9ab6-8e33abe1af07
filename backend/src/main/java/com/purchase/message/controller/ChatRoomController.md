# ChatRoomController.java

## 文件概述 (File Overview)
`ChatRoomController.java` 是聊天室管理的REST控制器，位于 `com.purchase.message.controller` 包中。该控制器作为聊天室管理模块的HTTP接口层，负责处理所有聊天室相关的REST API请求。通过集成 `ChatRoomService`、`ChatRoomUnreadService` 和 `UserService`，提供了聊天室的完整生命周期管理功能，包括创建、查询、搜索、详情获取等。该控制器实现了基于Spring Security的多层权限控制，支持买家、卖家、货代、管理员等不同角色的差异化访问，并提供了完善的多方聊天室管理功能。

## 核心功能 (Core Functionality)
*   **聊天室列表查询**: 支持分页查询用户的聊天室列表，提供灵活的角色过滤功能
*   **聊天室创建管理**: 支持多方参与的聊天室创建，包含买家、卖家、货代、管理员等角色
*   **多维度搜索**: 支持按买家-卖家、买家-货代、卖家-货代等组合搜索聊天室
*   **聊天室详情查看**: 提供完整的聊天室详情信息，包含参与者信息和最后消息
*   **未读消息统计**: 获取用户所有聊天室的未读消息统计，支持实时更新
*   **权限分级控制**: 基于用户角色的细粒度权限控制和数据访问隔离
*   **参与者验证**: 严格的聊天室参与者身份验证和角色匹配
*   **兼容性支持**: 提供旧版API的兼容接口，确保系统平滑升级
*   **异常处理机制**: 完善的异常捕获和用户友好的错误提示
*   **数据一致性保障**: 确保聊天室数据与用户、消息数据的一致性
*   **审计日志记录**: 详细的聊天室操作审计日志，满足业务合规要求
*   **统一响应格式**: 使用标准化的Result<T>响应格式，便于前端处理

## 接口说明 (Interface Description)

### 查询方法

#### getUserChatRooms - 获取用户聊天室列表（分页）
*   **方法签名**: `Result<IPage<ChatRoomDTO>> getUserChatRooms(@RequestParam Long userId, @RequestParam String role, @RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "10") Integer size)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/message/rooms`
*   **权限**: `@PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')")`
*   **参数**:
    *   `userId` (Long) - 用户ID
    *   `role` (String) - 用户角色（buyer、seller、forwarder、admin）
    *   `page` (Integer) - 页码，默认为1
    *   `size` (Integer) - 每页大小，默认为10
*   **返回值**: `Result<IPage<ChatRoomDTO>>` - 分页的聊天室列表
*   **业务逻辑**:
    *   验证用户权限，确保只能查询自己的聊天室
    *   管理员可以查询任意用户的聊天室
    *   执行分页查询，按最后消息时间倒序排列
    *   返回包含参与者信息的聊天室详情
    *   包含最后消息内容和时间信息

#### getChatRoomDetail - 获取聊天室详情
*   **方法签名**: `Result<ChatRoomDTO> getChatRoomDetail(@PathVariable Long roomId)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/message/rooms/{roomId}`
*   **权限**: `@PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')")`
*   **参数**: `roomId` (Long) - 聊天室ID
*   **返回值**: `Result<ChatRoomDTO>` - 聊天室详细信息
*   **业务逻辑**:
    *   验证聊天室ID的有效性
    *   检查用户是否为聊天室成员
    *   查询聊天室的完整信息
    *   包含所有参与者的详细信息
    *   返回聊天室状态和最后活动时间

#### getUnreadCount - 获取未读消息统计
*   **方法签名**: `Result<Map<String, Object>> getUnreadCount()`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/message/rooms/unread-count`
*   **权限**: `@PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')")`
*   **返回值**: `Result<Map<String, Object>>` - 未读消息统计信息
*   **业务逻辑**:
    *   获取当前登录用户信息
    *   统计用户所有聊天室的未读消息
    *   返回总未读数和各聊天室未读数
    *   提供实时的未读消息状态

### 创建方法

#### createChatRoom - 创建聊天室
*   **方法签名**: `Result<Long> createChatRoom(@RequestParam(required = false, defaultValue = "0") Long buyerId, @RequestParam(required = false, defaultValue = "0") Long sellerId, @RequestParam(required = false, defaultValue = "0") Long forwarderId, @RequestParam(required = false, defaultValue = "3") Long adminId)`
*   **HTTP方法**: POST
*   **路径**: `/api/v1/message/rooms`
*   **权限**: `@PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')")`
*   **参数**:
    *   `buyerId` (Long) - 买家ID，可选，默认为0
    *   `sellerId` (Long) - 卖家ID，可选，默认为0
    *   `forwarderId` (Long) - 货代ID，可选，默认为0
    *   `adminId` (Long) - 管理员ID，可选，默认为3
*   **返回值**: `Result<Long>` - 创建的聊天室ID
*   **业务逻辑**:
    *   验证创建者权限和身份
    *   确保创建者是参与者之一
    *   验证所有参与者的角色和存在性
    *   检查是否已存在相同参与者的聊天室
    *   创建新的聊天室记录
    *   返回聊天室ID

#### createChatRoomLegacy - 兼容旧版创建聊天室
*   **方法签名**: `Result<Long> createChatRoomLegacy(@RequestParam Long buyerId, @RequestParam Long sellerId, @RequestParam(required = false, defaultValue = "3") Long adminId)`
*   **HTTP方法**: POST
*   **路径**: `/api/v1/message/rooms/legacy`
*   **权限**: `@PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller')")`
*   **参数**:
    *   `buyerId` (Long) - 买家ID
    *   `sellerId` (Long) - 卖家ID
    *   `adminId` (Long) - 管理员ID，可选，默认为3
*   **返回值**: `Result<Long>` - 创建的聊天室ID
*   **业务逻辑**:
    *   兼容旧版API的聊天室创建逻辑
    *   只支持买家和卖家的聊天室
    *   验证参与者权限和角色
    *   调用新版创建方法实现

### 搜索方法

#### searchChatRoomByBuyerAndSeller - 按买家和卖家搜索聊天室
*   **方法签名**: `Result<ChatRoomDTO> searchChatRoomByBuyerAndSeller(@RequestParam Long buyerId, @RequestParam Long sellerId)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/message/rooms/search/buyer-seller`
*   **权限**: `@PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')")`
*   **参数**:
    *   `buyerId` (Long) - 买家ID
    *   `sellerId` (Long) - 卖家ID
*   **返回值**: `Result<ChatRoomDTO>` - 聊天室信息，不存在则返回null
*   **业务逻辑**:
    *   验证用户权限，非管理员只能搜索自己参与的聊天室
    *   根据买家和卖家ID查找聊天室
    *   返回聊天室详细信息
    *   如果不存在则返回null

#### searchChatRoomByBuyerAndForwarder - 按买家和货代搜索聊天室
*   **方法签名**: `Result<ChatRoomDTO> searchChatRoomByBuyerAndForwarder(@RequestParam Long buyerId, @RequestParam Long forwarderId)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/message/rooms/search/buyer-forwarder`
*   **权限**: `@PreAuthorize("hasAnyAuthority('admin', 'buyer', 'forwarder')")`
*   **参数**:
    *   `buyerId` (Long) - 买家ID
    *   `forwarderId` (Long) - 货代ID
*   **返回值**: `Result<ChatRoomDTO>` - 聊天室信息
*   **业务逻辑**:
    *   验证用户权限和参与者身份
    *   根据买家和货代ID查找聊天室
    *   返回聊天室详细信息

#### searchChatRoomBySellerAndForwarder - 按卖家和货代搜索聊天室
*   **方法签名**: `Result<ChatRoomDTO> searchChatRoomBySellerAndForwarder(@RequestParam Long sellerId, @RequestParam Long forwarderId)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/message/rooms/search/seller-forwarder`
*   **权限**: `@PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder')")`
*   **参数**:
    *   `sellerId` (Long) - 卖家ID
    *   `forwarderId` (Long) - 货代ID
*   **返回值**: `Result<ChatRoomDTO>` - 聊天室信息
*   **业务逻辑**:
    *   验证用户权限和参与者身份
    *   根据卖家和货代ID查找聊天室
    *   返回聊天室详细信息

## 业务规则 (Business Rules)
*   **权限隔离**: 用户只能查看和操作自己参与的聊天室，管理员可以查看所有聊天室
*   **参与者验证**: 创建聊天室时必须验证所有参与者的角色和存在性
*   **创建者限制**: 聊天室创建者必须是参与者之一，不能创建不包含自己的聊天室
*   **最少参与者**: 聊天室至少需要两名参与者，单人聊天室不被允许
*   **重复检查**: 系统会检查是否已存在相同参与者组合的聊天室，避免重复创建
*   **角色匹配**: 参与者的角色必须与其用户账户的实际角色匹配
*   **分页限制**: 分页查询的每页大小限制在1-100之间，防止大量数据查询
*   **状态管理**: 聊天室支持活跃、归档等状态，影响查询和显示逻辑

## 使用示例 (Usage Examples)

```java
// 1. 前端JavaScript调用示例
const ChatRoomAPI = {
    // 获取聊天室列表
    async getChatRooms(userId, role, page = 1, size = 10) {
        const response = await fetch(`/api/v1/message/rooms?userId=${userId}&role=${role}&page=${page}&size=${size}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 创建聊天室
    async createChatRoom(participants) {
        const params = new URLSearchParams();
        if (participants.buyerId) params.append('buyerId', participants.buyerId);
        if (participants.sellerId) params.append('sellerId', participants.sellerId);
        if (participants.forwarderId) params.append('forwarderId', participants.forwarderId);
        if (participants.adminId) params.append('adminId', participants.adminId);

        const response = await fetch(`/api/v1/message/rooms?${params}`, {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('聊天室创建成功');
            return result.data;
        } else {
            showErrorMessage('创建失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 搜索聊天室
    async searchChatRoom(searchType, params) {
        let url = `/api/v1/message/rooms/search/${searchType}?`;
        const searchParams = new URLSearchParams(params);
        url += searchParams.toString();

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 获取未读消息统计
    async getUnreadCount() {
        const response = await fetch('/api/v1/message/rooms/unread-count', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    }
};

// 2. Java客户端调用示例
@Service
public class ChatRoomClientService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${api.base-url}")
    private String baseUrl;

    // 获取聊天室列表
    public IPage<ChatRoomDTO> getChatRooms(Long userId, String role, int page, int size, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);

        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            String url = String.format("%s/api/v1/message/rooms?userId=%d&role=%s&page=%d&size=%d",
                baseUrl, userId, role, page, size);

            ResponseEntity<Result<IPage<ChatRoomDTO>>> response = restTemplate.exchange(
                url, HttpMethod.GET, entity,
                new ParameterizedTypeReference<Result<IPage<ChatRoomDTO>>>() {}
            );

            Result<IPage<ChatRoomDTO>> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("获取聊天室列表失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用获取聊天室列表API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }

    // 创建聊天室
    public Long createChatRoom(Long buyerId, Long sellerId, Long forwarderId, Long adminId, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);

        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            String url = String.format("%s/api/v1/message/rooms?buyerId=%d&sellerId=%d&forwarderId=%d&adminId=%d",
                baseUrl, buyerId, sellerId, forwarderId, adminId);

            ResponseEntity<Result<Long>> response = restTemplate.exchange(
                url, HttpMethod.POST, entity,
                new ParameterizedTypeReference<Result<Long>>() {}
            );

            Result<Long> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("创建聊天室失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用创建聊天室API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }
}

// 3. 业务服务集成示例
@Service
public class ChatRoomWorkflowService {

    @Autowired
    private ChatRoomService chatRoomService;

    @Autowired
    private ChatRoomController chatRoomController;

    // 订单创建后自动创建聊天室
    @EventListener
    @Async
    public void handleOrderCreated(OrderCreatedEvent event) {
        try {
            // 创建买家-卖家聊天室
            Long buyerSellerRoomId = chatRoomService.createChatRoom(
                event.getBuyerId(), event.getSellerId(), null, 3L);

            log.info("订单创建后自动创建买家-卖家聊天室: orderId={}, roomId={}",
                event.getOrderId(), buyerSellerRoomId);

            // 如果有货代，创建买家-货代聊天室
            if (event.getForwarderId() != null) {
                Long buyerForwarderRoomId = chatRoomService.createChatRoom(
                    event.getBuyerId(), null, event.getForwarderId(), 3L);

                log.info("订单创建后自动创建买家-货代聊天室: orderId={}, roomId={}",
                    event.getOrderId(), buyerForwarderRoomId);
            }

        } catch (Exception e) {
            log.error("订单创建后自动创建聊天室失败: orderId={}", event.getOrderId(), e);
        }
    }

    // 用户注册后创建与管理员的聊天室
    @EventListener
    @Async
    public void handleUserRegistered(UserRegisteredEvent event) {
        try {
            Long userId = event.getUserId();
            String role = event.getRole();

            // 根据角色创建与管理员的聊天室
            Long roomId = null;
            switch (role) {
                case "buyer":
                    roomId = chatRoomService.createChatRoom(userId, null, null, 3L);
                    break;
                case "seller":
                    roomId = chatRoomService.createChatRoom(null, userId, null, 3L);
                    break;
                case "forwarder":
                    roomId = chatRoomService.createChatRoom(null, null, userId, 3L);
                    break;
            }

            if (roomId != null) {
                log.info("用户注册后自动创建与管理员聊天室: userId={}, role={}, roomId={}",
                    userId, role, roomId);
            }

        } catch (Exception e) {
            log.error("用户注册后自动创建聊天室失败: userId={}", event.getUserId(), e);
        }
    }
}

// 4. 定时任务示例
@Component
public class ChatRoomScheduledTasks {

    @Autowired
    private ChatRoomService chatRoomService;

    // 清理长期不活跃的聊天室
    @Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点
    public void cleanupInactiveChatRooms() {
        log.info("开始清理不活跃聊天室");

        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(90);
            int archivedCount = chatRoomService.archiveInactiveChatRooms(cutoffDate);

            log.info("不活跃聊天室清理完成，归档数量: {}", archivedCount);

        } catch (Exception e) {
            log.error("清理不活跃聊天室失败", e);
        }
    }

    // 生成聊天室活跃度报告
    @Scheduled(cron = "0 0 9 1 * ?") // 每月1号早上9点
    public void generateChatRoomActivityReport() {
        log.info("开始生成聊天室活跃度报告");

        try {
            ChatRoomActivityReport report = new ChatRoomActivityReport();
            report.setReportMonth(LocalDate.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM")));
            report.setTotalRooms(chatRoomService.getTotalRoomsLastMonth());
            report.setActiveRooms(chatRoomService.getActiveRoomsLastMonth());
            report.setNewRooms(chatRoomService.getNewRoomsLastMonth());
            report.setMessageCount(chatRoomService.getMessageCountLastMonth());

            // 发送报告给管理员
            chatRoomService.sendActivityReport(report);

            log.info("聊天室活跃度报告生成完成");

        } catch (Exception e) {
            log.error("生成聊天室活跃度报告失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class ChatRoomControllerTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @MockBean
    private ChatRoomService chatRoomService;

    @MockBean
    private ChatRoomUnreadService chatRoomUnreadService;

    @Test
    void testGetUserChatRooms() {
        // 准备测试数据
        IPage<ChatRoomDTO> mockPage = new Page<>(1, 10);
        mockPage.setTotal(1);
        mockPage.setRecords(Arrays.asList(createMockChatRoomDTO()));

        // Mock服务调用
        when(chatRoomService.getUserChatRooms(100L, "buyer", 1, 10))
            .thenReturn(mockPage);

        // 执行测试
        ResponseEntity<Result> response = restTemplate.getForEntity(
            "/api/v1/message/rooms?userId=100&role=buyer&page=1&size=10", Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(chatRoomService, times(1)).getUserChatRooms(100L, "buyer", 1, 10);
    }

    @Test
    void testCreateChatRoom() {
        // 准备测试数据
        Long expectedRoomId = 123L;

        // Mock服务调用
        when(chatRoomService.createChatRoom(100L, 200L, null, 3L))
            .thenReturn(expectedRoomId);

        // 执行测试
        ResponseEntity<Result> response = restTemplate.postForEntity(
            "/api/v1/message/rooms?buyerId=100&sellerId=200&adminId=3",
            null, Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(chatRoomService, times(1)).createChatRoom(100L, 200L, null, 3L);
    }

    @Test
    void testSearchChatRoomByBuyerAndSeller() {
        // 准备测试数据
        ChatRoomDTO mockRoom = createMockChatRoomDTO();

        // Mock服务调用
        when(chatRoomService.findByBuyerAndSeller(100L, 200L))
            .thenReturn(mockRoom);

        // 执行测试
        ResponseEntity<Result> response = restTemplate.getForEntity(
            "/api/v1/message/rooms/search/buyer-seller?buyerId=100&sellerId=200",
            Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(chatRoomService, times(1)).findByBuyerAndSeller(100L, 200L);
    }
}
```

## 注意事项 (Notes)
*   **权限验证**: 所有接口都使用@PreAuthorize注解进行权限控制，确保用户只能操作自己参与的聊天室
*   **身份验证**: 通过SecurityContextHolder获取当前用户信息，严格验证用户身份和权限
*   **角色匹配**: 创建聊天室时验证参与者的角色与用户实际角色是否匹配，防止角色伪造
*   **参数验证**: 创建聊天室时至少需要两名参与者，且创建者必须是参与者之一
*   **重复检查**: 系统会检查是否已存在相同参与者组合的聊天室，避免重复创建
*   **业务委托**: 控制器只负责HTTP请求处理和权限验证，具体业务逻辑委托给Service层处理
*   **异常处理**: 完善的异常处理机制，区分业务异常和系统异常，返回友好的错误信息
*   **分页性能**: 聊天室列表查询使用分页机制，避免大量数据查询影响系统性能
*   **数据一致性**: 聊天室相关操作涉及多个表，需要在事务中执行以保证数据一致性
*   **缓存策略**: 聊天室列表和未读消息统计可以考虑使用Redis缓存提高响应速度
*   **实时更新**: 未读消息统计需要与WebSocket消息推送配合，实现实时更新
*   **兼容性**: 提供legacy接口保证向后兼容，支持系统平滑升级
*   **审计日志**: 聊天室创建、搜索等关键操作需要记录详细的审计日志
*   **监控告警**: 对聊天室创建失败、权限异常等关键指标进行监控和告警
*   **数据备份**: 聊天室数据需要定期备份，确保数据安全
*   **国际化**: 错误消息和提示信息需要支持多语言，便于国际化部署
*   **API文档**: 使用Swagger注解为API提供清晰的接口文档，便于前端开发和第三方集成