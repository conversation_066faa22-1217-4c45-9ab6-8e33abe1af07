# ChatMessage.java

## 文件概述 (File Overview)
`ChatMessage.java` 是聊天消息实体类，位于 `com.purchase.message.entity` 包中，使用MyBatis-Plus注解进行ORM映射。该实体类定义了聊天消息的完整数据模型，包含消息基本信息、发送者信息、消息内容、状态管理等核心属性。通过完善的字段设计和业务逻辑，支持多种消息类型和聊天场景，实现了消息数据的标准化存储和高效查询，并提供了完善的消息状态跟踪和实时通信机制。

## 核心功能 (Core Functionality)
*   **消息基础信息**: 存储消息ID、聊天室ID、发送时间等基本消息信息
*   **发送者信息**: 记录消息发送者的用户ID、用户名、角色等身份信息
*   **消息内容管理**: 支持文本、图片、文件、语音等多种消息内容类型
*   **消息类型分类**: 支持普通消息、系统消息、通知消息等多种消息类型
*   **状态管理**: 管理消息的发送状态、送达状态、阅读状态等
*   **时间信息记录**: 记录消息的创建时间、发送时间、阅读时间等关键时间点
*   **消息回复**: 支持消息回复和引用功能，建立消息之间的关联关系
*   **消息撤回**: 支持消息撤回功能，允许用户在一定时间内撤回消息
*   **消息转发**: 支持消息转发功能，便于信息分享和传播
*   **附件管理**: 支持消息附件的存储和管理，包括文件、图片等
*   **消息搜索**: 提供消息内容的搜索和检索功能
*   **消息统计**: 提供消息发送和接收的统计分析数据

## 业务规则 (Business Rules)
*   **消息类型**: 支持文本、图片、文件、语音等多种消息类型
*   **状态流转**: 消息状态包括已发送、已送达、已读等状态
*   **权限控制**: 用户只能在有权限的聊天室中发送和查看消息
*   **撤回限制**: 消息撤回有时间限制，超过时间后不能撤回
*   **内容规范**: 消息内容需要符合社区规范，过滤敏感词汇
*   **文件限制**: 文件类型消息有大小和格式限制
*   **消息持久化**: 所有消息都需要持久化存储，便于历史查询
*   **实时性**: 消息需要实时推送，确保及时送达

## 注意事项 (Notes)
*   **数据索引**: 合理设计数据库索引，提高消息查询和搜索性能
*   **字段验证**: 使用Bean Validation注解进行数据验证
*   **软删除**: 建议使用软删除机制，保留消息历史记录
*   **并发控制**: 消息发送需要考虑并发控制，防止重复发送
*   **缓存策略**: 最近消息可以适当缓存，提高查询性能
*   **文件存储**: 消息附件需要合理的文件存储策略
*   **内容安全**: 消息内容需要进行安全过滤和敏感词检测
*   **性能优化**: 大量消息数据需要考虑分库分表等优化策略
*   **数据清理**: 定期清理过期的临时文件和无效消息
*   **隐私保护**: 保护用户聊天数据的隐私和安全
