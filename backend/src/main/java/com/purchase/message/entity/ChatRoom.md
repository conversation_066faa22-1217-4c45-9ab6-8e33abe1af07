# ChatRoom.java

## 文件概述 (File Overview)
`ChatRoom.java` 是聊天室实体类，位于 `com.purchase.message.entity` 包中，使用MyBatis-Plus注解进行ORM映射。该实体类定义了聊天室的完整数据模型，是消息通信领域的核心聚合根，包含聊天室基本信息、成员管理、权限控制、消息统计等核心属性。通过完善的字段设计和业务逻辑，支持聊天室的全生命周期管理，实现了聊天室数据的标准化存储和高效查询，并提供了完善的成员管理和权限控制机制。

## 核心功能 (Core Functionality)
*   **聊天室信息管理**: 存储聊天室的基本信息，包括名称、描述、类型、创建者等
*   **成员管理**: 管理聊天室成员的加入、退出、权限分配等
*   **权限控制**: 控制不同角色成员的操作权限，如发言、邀请、管理等
*   **消息统计**: 统计聊天室的消息数量、活跃度等指标
*   **状态管理**: 管理聊天室的状态，包括正常、归档、禁用等
*   **类型分类**: 支持多种聊天室类型，如私聊、群聊、系统通知等
*   **设置管理**: 管理聊天室的各种设置，如消息保留期、成员限制等
*   **邀请机制**: 支持成员邀请和邀请链接功能
*   **公告管理**: 支持聊天室公告的发布和管理
*   **禁言功能**: 支持对特定成员的禁言管理
*   **关联关系维护**: 与消息、用户等建立关联关系
*   **业务集成**: 与业务流程集成，如订单讨论、需求沟通等

## DDD分析 (DDD Analysis)

### 限界上下文识别
*   **所属上下文**: 消息通信上下文（Message Communication Context）
*   **上下文边界**: 与用户上下文、订单上下文、通知上下文的交互边界
*   **领域语言**: 聊天室、成员管理、权限控制、消息统计、群聊、私聊等核心业务术语

### 聚合设计分析
*   **聚合根判断**: ✅ 是聚合根 - 聊天室是独立的业务概念，具有完整的生命周期
*   **聚合边界**: 包含聊天室基本信息、成员列表、权限设置、统计信息等
*   **业务不变量**: 
    *   聊天室必须有至少一个管理员
    *   成员数量不能超过聊天室类型的限制
    *   私聊只能有两个成员
    *   聊天室创建者默认为管理员
    *   被禁用的聊天室不能发送消息
*   **状态一致性**: 聚合内部状态变更必须保持一致性，成员变更需要更新统计信息

### 领域概念分析
*   **核心领域概念**: 聊天室是用户进行实时沟通的虚拟空间
*   **业务价值**: 促进用户交流，提高业务协作效率，增强用户粘性
*   **生命周期**: 创建→活跃→归档→删除的完整业务流程
*   **状态机**: 
    *   ACTIVE（活跃）→ ARCHIVED（已归档）
    *   ACTIVE → DISABLED（已禁用）
    *   ARCHIVED → ACTIVE（重新激活）
    *   任意状态 → DELETED（已删除）

### 值对象候选
*   **聊天室信息**: RoomInfo（名称、描述、头像、类型）
*   **成员信息**: MemberInfo（用户ID、角色、加入时间、最后活跃时间）
*   **权限设置**: PermissionSettings（发言权限、邀请权限、管理权限）
*   **统计信息**: RoomStatistics（成员数量、消息数量、活跃度）
*   **设置配置**: RoomSettings（消息保留期、成员限制、公开性）

## 业务规则 (Business Rules)
*   **成员限制**: 不同类型的聊天室有不同的成员数量限制
*   **权限分级**: 聊天室有创建者、管理员、普通成员等不同权限级别
*   **私聊规则**: 私聊只能有两个成员，不支持邀请其他成员
*   **管理员规则**: 聊天室必须至少有一个管理员，创建者不能退出
*   **邀请规则**: 只有有权限的成员才能邀请新成员加入
*   **禁言规则**: 被禁言的成员不能发送消息，但可以查看历史消息
*   **归档规则**: 长时间无活动的聊天室可以自动归档
*   **删除规则**: 只有创建者可以删除聊天室

## 注意事项 (Notes)
*   **聚合根设计**: 作为聚合根，需要控制对聚合内部对象的访问
*   **状态一致性**: 状态变更需要通过聚合根的方法进行，确保业务规则得到执行
*   **领域事件**: 状态变更时应该发布相应的领域事件，如成员加入事件、聊天室创建事件等
*   **数据完整性**: 使用Bean Validation注解确保数据完整性
*   **索引设计**: 合理设计数据库索引，支持高效的聊天室查询和成员查询
*   **并发控制**: 成员管理操作需要考虑并发控制，防止数据冲突
*   **软删除**: 建议使用软删除机制，保留聊天室历史数据
*   **缓存策略**: 聊天室信息可以适当缓存，提高查询性能
*   **性能优化**: 大量聊天室数据需要考虑分库分表等性能优化策略
*   **隐私保护**: 保护聊天室数据的隐私和安全，防止未授权访问
