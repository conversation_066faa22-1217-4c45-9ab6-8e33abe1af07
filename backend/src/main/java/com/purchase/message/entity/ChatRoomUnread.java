package com.purchase.message.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 聊天室未读消息计数实体类
 */
@Data
public class ChatRoomUnread {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 聊天室ID
     */
    private Long roomId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 未读消息数量
     */
    private Integer unreadCount;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 