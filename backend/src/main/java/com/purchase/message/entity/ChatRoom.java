package com.purchase.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("chat_room")
public class ChatRoom {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    private Long buyerId;
    
    private Long sellerId;
    
    private Long forwarderId;
    
    private Long adminId;
    
    private LocalDateTime lastMessageTime;
    
    private Integer status;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
} 