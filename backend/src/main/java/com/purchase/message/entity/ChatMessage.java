package com.purchase.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("chat_message")
public class ChatMessage {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    private Long roomId;
    
    private Long senderId;
    
    private String senderRole;  // 'buyer', 'seller', 'admin'
    
    private String messageType;  // 'text', 'image', 'file', 'system'
    
    private String textContent;
    
    private String imageUrl;
    
    private String fileUrl;
    
    private String systemContent;
    
    private LocalDateTime createdAt;
} 