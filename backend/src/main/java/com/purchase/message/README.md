# Message 消息模块文档

## 模块概述

消息模块是采购系统的重要功能模块，提供完整的即时通讯解决方案。该模块支持多方聊天、实时消息推送、通知管理等功能，为买家、卖家、货代和管理员之间的沟通提供便利的平台。

## 目录结构概览

```
com.purchase.message/
├── config/                       # 配置
│   └── WebMvcConfig.java                # Web MVC配置
├── controller/                   # 控制器层
│   ├── ChatMessageController.java       # 聊天消息控制器
│   └── ChatRoomController.java          # 聊天室控制器
├── dto/                         # 数据传输对象
│   ├── ChatMessageDTO.java             # 聊天消息DTO
│   ├── ChatRoomDTO.java                # 聊天室DTO
│   ├── MessageRequest.java             # 消息请求DTO
│   └── TypingStatusRequest.java        # 输入状态请求DTO
├── entity/                      # 实体类
│   ├── ChatMessage.java               # 聊天消息实体
│   ├── ChatRoom.java                  # 聊天室实体
│   └── ChatRoomUnread.java            # 聊天室未读消息实体
├── exception/                   # 异常处理
│   └── BusinessException.java         # 业务异常
├── mapper/                      # 数据访问层
│   ├── ChatMessageMapper.java         # 聊天消息Mapper
│   ├── ChatRoomMapper.java            # 聊天室Mapper
│   └── ChatRoomUnreadMapper.java      # 未读消息Mapper
├── notification/                # 通知子模块
│   ├── controller/                     # 通知控制器
│   ├── entity/                         # 通知实体
│   ├── enums/                          # 通知枚举
│   ├── mapper/                         # 通知数据访问层
│   └── service/                        # 通知服务层
├── service/                     # 服务层
│   ├── ChatMessageService.java        # 聊天消息服务接口
│   ├── ChatRoomService.java           # 聊天室服务接口
│   ├── ChatRoomUnreadService.java     # 未读消息服务接口
│   └── impl/                           # 服务实现
└── websocket/                   # WebSocket子模块
    ├── config/                         # WebSocket配置
    ├── controller/                     # WebSocket控制器
    ├── dto/                            # WebSocket DTO
    ├── handler/                        # WebSocket处理器
    └── interceptor/                    # WebSocket拦截器
```

## 核心功能详述

### 1. 聊天室管理 (ChatRoom)

#### 主要功能
- **多方聊天**: 支持买家、卖家、货代、管理员的多方聊天
- **聊天室创建**: 基于业务需求自动创建聊天室
- **状态管理**: 聊天室的激活、关闭状态管理
- **权限控制**: 确保只有相关方可以参与聊天

#### 核心实体
- `ChatRoom.java`: 聊天室实体
  - `buyerId`: 买家用户ID
  - `sellerId`: 卖家用户ID  
  - `forwarderId`: 货代用户ID
  - `adminId`: 管理员用户ID
  - `lastMessageTime`: 最后消息时间
  - `status`: 聊天室状态

### 2. 聊天消息管理 (ChatMessage)

#### 主要功能
- **消息发送**: 支持文本、图片、文件等多种消息类型
- **消息存储**: 持久化存储所有聊天消息
- **消息查询**: 支持分页查询历史消息
- **消息状态**: 消息的发送、接收、已读状态管理

#### 核心实体
- `ChatMessage.java`: 聊天消息实体
  - 消息内容、类型、发送者信息
  - 消息状态和时间戳
  - 附件和媒体文件支持

### 3. 未读消息管理 (ChatRoomUnread)

#### 主要功能
- **未读计数**: 统计每个用户在每个聊天室的未读消息数
- **已读标记**: 用户查看消息后标记为已读
- **未读提醒**: 为用户提供未读消息提醒
- **批量操作**: 支持批量标记已读

#### 核心实体
- `ChatRoomUnread.java`: 未读消息实体
  - 用户和聊天室的关联
  - 未读消息计数
  - 最后阅读时间

### 4. 实时通讯 (WebSocket)

#### 主要功能
- **实时消息**: 基于WebSocket的实时消息推送
- **在线状态**: 用户在线状态管理
- **输入状态**: 显示用户正在输入的状态
- **连接管理**: WebSocket连接的建立、维护和断开

#### 核心组件
- **WebSocket配置**: 连接配置和安全设置
- **消息处理器**: 处理不同类型的WebSocket消息
- **拦截器**: 身份验证和权限控制
- **DTO**: WebSocket消息的数据传输对象

### 5. 通知系统 (Notification)

#### 主要功能
- **系统通知**: 发送系统级通知消息
- **业务通知**: 订单、竞价等业务相关通知
- **通知类型**: 支持多种通知类型和优先级
- **通知历史**: 保存和查询通知历史记录

#### 核心组件
- **通知实体**: 存储通知的详细信息
- **通知枚举**: 定义通知类型和状态
- **通知服务**: 发送和管理通知的业务逻辑

## 数据模型说明

### ChatRoom 聊天室

#### 核心字段
- `id`: 聊天室唯一标识
- `buyerId`: 买家用户ID
- `sellerId`: 卖家用户ID
- `forwarderId`: 货代用户ID（可选）
- `adminId`: 管理员用户ID（可选）
- `lastMessageTime`: 最后消息时间
- `status`: 聊天室状态（1-活跃，0-关闭）

### ChatMessage 聊天消息

#### 核心字段
- `id`: 消息唯一标识
- `roomId`: 所属聊天室ID
- `senderId`: 发送者用户ID
- `messageType`: 消息类型（text/image/file）
- `content`: 消息内容
- `attachments`: 附件信息
- `status`: 消息状态
- `sentAt`: 发送时间

### ChatRoomUnread 未读消息

#### 核心字段
- `id`: 记录唯一标识
- `roomId`: 聊天室ID
- `userId`: 用户ID
- `unreadCount`: 未读消息数量
- `lastReadTime`: 最后阅读时间

## API 接口概览

### 聊天室接口
- `POST /api/v1/chat-rooms` - 创建聊天室
- `GET /api/v1/chat-rooms` - 获取用户聊天室列表
- `GET /api/v1/chat-rooms/{id}` - 获取聊天室详情
- `PUT /api/v1/chat-rooms/{id}/status` - 更新聊天室状态

### 聊天消息接口
- `POST /api/v1/chat-messages` - 发送消息
- `GET /api/v1/chat-rooms/{roomId}/messages` - 获取聊天记录
- `PUT /api/v1/chat-messages/{id}/read` - 标记消息已读
- `DELETE /api/v1/chat-messages/{id}` - 删除消息

### 未读消息接口
- `GET /api/v1/chat-rooms/unread-count` - 获取总未读数
- `GET /api/v1/chat-rooms/{roomId}/unread-count` - 获取特定聊天室未读数
- `PUT /api/v1/chat-rooms/{roomId}/mark-read` - 标记聊天室已读

### WebSocket接口
- `ws://host/ws/chat` - WebSocket连接端点
- 支持消息发送、接收、状态同步等实时操作

## 业务规则

### 聊天室规则
1. 每个业务场景（如订单）可以创建对应的聊天室
2. 聊天室成员基于业务关系自动确定
3. 管理员可以加入任何聊天室进行监管
4. 聊天室可以根据业务状态激活或关闭

### 消息规则
1. 只有聊天室成员可以发送和接收消息
2. 消息一旦发送不能修改，只能撤回（限时）
3. 支持多种消息类型：文本、图片、文件
4. 消息内容需要进行安全过滤

### 权限规则
1. 用户只能访问自己参与的聊天室
2. 管理员拥有所有聊天室的访问权限
3. 消息发送需要验证用户身份
4. 敏感信息需要权限控制

### 实时通讯规则
1. WebSocket连接需要身份验证
2. 支持断线重连和消息补发
3. 在线状态实时更新
4. 输入状态有时效性限制

## 集成说明

### 与其他模块的关系
- **用户模块**: 获取用户信息和权限验证
- **订单模块**: 基于订单创建聊天室
- **竞价模块**: 竞价相关的沟通需求
- **文件模块**: 处理消息中的文件附件

### 事件发布
- 消息发送事件
- 聊天室创建事件
- 用户在线状态变更事件
- 未读消息更新事件

## 注意事项

### 开发注意事项
1. **实时性**: 确保消息的实时传递
2. **可靠性**: 消息不丢失，支持离线消息
3. **安全性**: 防止消息泄露和恶意攻击
4. **性能**: 支持大量并发连接和消息

### 性能优化
1. **连接池**: WebSocket连接池管理
2. **消息队列**: 使用消息队列处理高并发
3. **缓存策略**: 缓存热点数据和在线状态
4. **分页加载**: 历史消息分页加载

### 安全考虑
1. **身份验证**: 严格的WebSocket身份验证
2. **内容过滤**: 消息内容的安全过滤
3. **权限控制**: 基于业务关系的权限控制
4. **防刷机制**: 防止消息刷屏和恶意攻击

### 扩展性
1. **水平扩展**: 支持多实例部署
2. **消息类型**: 支持更多消息类型扩展
3. **集成能力**: 与第三方通讯工具集成
4. **国际化**: 支持多语言消息处理
