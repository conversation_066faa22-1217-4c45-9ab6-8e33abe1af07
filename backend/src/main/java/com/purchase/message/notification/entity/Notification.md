# Notification.java

## 文件概述 (File Overview)
`Notification.java` 是通知实体类，位于 `com.purchase.message.notification.entity` 包中，使用MyBatis-Plus注解进行ORM映射。该实体类定义了系统通知的完整数据模型，包含通知基本信息、接收者信息、通知内容、状态管理等核心属性。通过完善的字段设计和业务逻辑，支持多种通知类型和推送渠道，实现了通知数据的标准化存储和高效查询，并提供了完善的通知状态跟踪和用户交互机制。

## 核心功能 (Core Functionality)
*   **通知基础信息**: 存储通知ID、标题、内容、类型等基本通知信息
*   **接收者管理**: 记录通知接收者的用户ID、角色等身份信息
*   **通知内容存储**: 支持富文本内容、附件、链接等多种内容格式
*   **通知类型分类**: 支持系统通知、订单通知、支付通知等多种通知类型
*   **状态管理**: 管理通知的发送状态、阅读状态、删除状态等
*   **时间信息记录**: 记录通知的创建时间、发送时间、阅读时间等关键时间点
*   **优先级设置**: 支持通知优先级设置，确保重要通知优先处理
*   **推送渠道**: 支持多种推送渠道，包括站内信、邮件、短信等
*   **过期机制**: 支持通知过期时间设置，自动清理过期通知
*   **关联信息**: 与业务对象建立关联，支持通知的业务上下文
*   **用户交互**: 支持用户对通知的操作，如标记已读、删除等
*   **统计分析**: 提供通知发送和阅读的统计分析数据

## 业务规则 (Business Rules)
*   **通知类型**: 支持系统、订单、支付、竞价等多种通知类型
*   **状态流转**: 通知状态包括未发送、已发送、已读、已删除等
*   **权限控制**: 用户只能查看和操作自己的通知
*   **优先级规则**: 高优先级通知优先推送和显示
*   **过期处理**: 过期通知自动标记为无效或删除
*   **推送策略**: 根据用户偏好和通知类型选择推送渠道
*   **内容规范**: 通知内容需要符合格式规范和长度限制
*   **频率控制**: 防止通知轰炸，控制通知发送频率

## 注意事项 (Notes)
*   **数据索引**: 合理设计数据库索引，提高通知查询性能
*   **字段验证**: 使用Bean Validation注解进行数据验证
*   **软删除**: 建议使用软删除机制，保留通知历史数据
*   **并发控制**: 通知状态更新需要考虑并发控制
*   **缓存策略**: 未读通知数量等信息可以适当缓存
*   **数据清理**: 定期清理过期和无效的通知数据
*   **内容安全**: 通知内容需要进行安全过滤，防止XSS攻击
*   **性能优化**: 大量通知数据需要考虑分页和异步处理
*   **隐私保护**: 保护用户通知数据的隐私和安全
*   **审计日志**: 记录重要通知的发送和操作历史
