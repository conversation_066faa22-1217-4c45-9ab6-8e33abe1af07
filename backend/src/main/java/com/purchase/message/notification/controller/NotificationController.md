# NotificationController.java

## 文件概述 (File Overview)
`NotificationController.java` 是系统通知管理的REST控制器，位于 `com.purchase.message.notification.controller` 包中。该控制器作为通知系统的HTTP接口层，负责处理所有通知相关的REST API请求。通过集成 `NotificationService` 和 `SimpMessagingTemplate`，提供了通知的完整生命周期管理功能，包括查询、标记已读、删除、实时推送等。该控制器支持多种通知类型和接收者角色，实现了基于WebSocket的实时通知推送，并提供了完善的通知管理功能。

## 核心功能 (Core Functionality)
*   **通知查询管理**: 支持分页查询用户通知列表，提供灵活的过滤和排序功能
*   **未读通知处理**: 专门的未读通知查询和计数功能，支持实时状态更新
*   **通知状态管理**: 支持单个和批量标记通知为已读，提供便捷的状态管理
*   **通知删除功能**: 支持单个、批量和全部删除通知，满足不同的清理需求
*   **实时推送服务**: 基于WebSocket的实时通知推送，确保用户及时收到通知
*   **多角色支持**: 支持不同用户角色的通知管理，包括买家、卖家、货代、管理员等
*   **分页查询优化**: 高效的分页查询机制，支持大量通知数据的处理
*   **通知类型分类**: 支持多种通知类型，包括订单、竞价、支付、系统等通知
*   **权限验证机制**: 确保用户只能操作自己的通知，保护数据安全
*   **异常处理机制**: 完善的异常捕获和用户友好的错误提示
*   **统一响应格式**: 使用标准化的Result<T>响应格式，便于前端处理
*   **测试接口支持**: 提供测试接口用于系统调试和健康检查

## 接口说明 (Interface Description)

### 查询方法

#### getNotifications - 获取用户通知列表（分页）
*   **方法签名**: `Result<Page<Notification>> getNotifications(@RequestParam Long receiverId, @RequestParam String receiverRole, @RequestParam(defaultValue = "1") int page, @RequestParam(defaultValue = "10") int size)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/notifications`
*   **参数**:
    *   `receiverId` (Long) - 接收者用户ID
    *   `receiverRole` (String) - 接收者角色（buyer、seller、forwarder、admin）
    *   `page` (int) - 页码，默认为1
    *   `size` (int) - 每页大小，默认为10
*   **返回值**: `Result<Page<Notification>>` - 分页的通知列表
*   **业务逻辑**:
    *   验证接收者ID和角色的有效性
    *   执行分页查询，按创建时间倒序排列
    *   返回包含通知详情的分页结果
    *   支持通知状态、类型等信息的完整展示

#### getUnreadNotifications - 获取未读通知列表
*   **方法签名**: `Result<List<Notification>> getUnreadNotifications(@RequestParam Long receiverId, @RequestParam String receiverRole, @RequestParam(defaultValue = "1") int page, @RequestParam(defaultValue = "10") int size)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/notifications/unread`
*   **参数**:
    *   `receiverId` (Long) - 接收者用户ID
    *   `receiverRole` (String) - 接收者角色
    *   `page` (int) - 页码，默认为1
    *   `size` (int) - 每页大小，默认为10
*   **返回值**: `Result<List<Notification>>` - 未读通知列表
*   **业务逻辑**:
    *   查询指定用户的所有未读通知
    *   按创建时间倒序排列
    *   支持分页查询，避免数据量过大
    *   返回详细的通知信息

#### getUnreadCount - 获取未读通知数量
*   **方法签名**: `Result<Long> getUnreadCount(@RequestParam Long receiverId, @RequestParam String receiverRole)`
*   **HTTP方法**: GET
*   **路径**: `/api/v1/notifications/unread-count`
*   **参数**:
    *   `receiverId` (Long) - 接收者用户ID
    *   `receiverRole` (String) - 接收者角色
*   **返回值**: `Result<Long>` - 未读通知数量
*   **业务逻辑**:
    *   统计指定用户的未读通知总数
    *   用于前端显示通知徽章数量
    *   高效的计数查询，性能优化
    *   实时反映通知状态变化

### 状态管理方法

#### markAsRead - 标记通知为已读
*   **方法签名**: `Result<Void> markAsRead(@PathVariable Long id)`
*   **HTTP方法**: PUT
*   **路径**: `/api/v1/notifications/{id}/read`
*   **参数**: `id` (Long) - 通知ID
*   **返回值**: `Result<Void>` - 操作结果
*   **业务逻辑**:
    *   验证通知ID的有效性
    *   更新通知状态为已读
    *   记录阅读时间
    *   验证用户权限（实际项目中需要从JWT获取用户信息）
    *   返回操作结果

#### markAllAsRead - 批量标记为已读
*   **方法签名**: `Result<Void> markAllAsRead(@RequestParam Long receiverId, @RequestParam String receiverRole)`
*   **HTTP方法**: PUT
*   **路径**: `/api/v1/notifications/read-all`
*   **参数**:
    *   `receiverId` (Long) - 接收者用户ID
    *   `receiverRole` (String) - 接收者角色
*   **返回值**: `Result<Void>` - 操作结果
*   **业务逻辑**:
    *   查询指定用户的所有未读通知
    *   批量更新通知状态为已读
    *   记录批量操作的时间戳
    *   优化批量操作性能
    *   返回操作结果

### 删除管理方法

#### deleteNotification - 删除通知
*   **方法签名**: `Result<Void> deleteNotification(@PathVariable Long id)`
*   **HTTP方法**: DELETE
*   **路径**: `/api/v1/notifications/{id}`
*   **参数**: `id` (Long) - 通知ID
*   **返回值**: `Result<Void>` - 删除结果
*   **业务逻辑**:
    *   验证通知ID的有效性
    *   检查用户删除权限
    *   执行物理删除操作
    *   清理相关的关联数据
    *   返回删除结果

#### batchDeleteNotifications - 批量删除通知
*   **方法签名**: `Result<Void> batchDeleteNotifications(@RequestBody List<Long> ids)`
*   **HTTP方法**: DELETE
*   **路径**: `/api/v1/notifications/batch-delete`
*   **参数**: `ids` (List<Long>) - 通知ID列表
*   **返回值**: `Result<Void>` - 批量删除结果
*   **业务逻辑**:
    *   验证ID列表的有效性
    *   检查批量删除的数量限制
    *   逐个验证删除权限
    *   执行批量删除操作
    *   返回操作结果统计

#### clearAllNotifications - 清空所有通知
*   **方法签名**: `Result<Void> clearAllNotifications(@RequestParam Long receiverId, @RequestParam String receiverRole)`
*   **HTTP方法**: DELETE
*   **路径**: `/api/v1/notifications/clear-all`
*   **参数**:
    *   `receiverId` (Long) - 接收者用户ID
    *   `receiverRole` (String) - 接收者角色
*   **返回值**: `Result<Void>` - 清空结果
*   **业务逻辑**:
    *   验证用户身份和权限
    *   查询用户的所有通知
    *   执行批量删除操作
    *   清理相关的索引和缓存
    *   返回清空结果

### 实时推送方法

#### sendNotificationToUser - 实时推送通知
*   **方法签名**: `void sendNotificationToUser(Long userId, Notification notification)`
*   **功能**: 内部方法，通过WebSocket向指定用户推送实时通知
*   **参数**:
    *   `userId` (Long) - 目标用户ID
    *   `notification` (Notification) - 通知内容对象
*   **业务逻辑**:
    *   验证用户ID和通知对象的有效性
    *   构建WebSocket消息格式
    *   通过SimpMessagingTemplate发送消息
    *   发送到用户专属队列：/queue/notifications
    *   记录推送日志和状态

## 业务规则 (Business Rules)
*   **权限隔离**: 用户只能查看和操作自己的通知，不能访问其他用户的通知数据
*   **角色验证**: 接收者角色必须与用户实际角色匹配，防止角色伪造
*   **状态管理**: 通知状态包括未读、已读，状态变更需要记录时间戳
*   **分页限制**: 分页查询的每页大小限制在1-100之间，防止大量数据查询
*   **批量限制**: 批量删除操作单次最多处理100个通知，避免系统性能问题
*   **实时推送**: WebSocket推送需要用户在线且已建立连接，离线用户通过查询接口获取
*   **数据保留**: 已读通知保留30天，未读通知永久保留直到用户删除
*   **通知类型**: 支持订单、支付、系统、竞价等多种通知类型分类

## 使用示例 (Usage Examples)

```java
// 1. 前端JavaScript调用示例
const NotificationAPI = {
    // 获取通知列表
    async getNotifications(receiverId, receiverRole, page = 1, size = 10) {
        const response = await fetch(`/api/v1/notifications?receiverId=${receiverId}&receiverRole=${receiverRole}&page=${page}&size=${size}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 获取未读通知数量
    async getUnreadCount(receiverId, receiverRole) {
        const response = await fetch(`/api/v1/notifications/unread-count?receiverId=${receiverId}&receiverRole=${receiverRole}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 标记通知为已读
    async markAsRead(notificationId) {
        const response = await fetch(`/api/v1/notifications/${notificationId}/read`, {
            method: 'PUT',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('通知已标记为已读');
            return result.data;
        } else {
            showErrorMessage('操作失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 批量删除通知
    async batchDeleteNotifications(notificationIds) {
        const response = await fetch('/api/v1/notifications/batch-delete', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify(notificationIds)
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('通知删除成功');
            return result.data;
        } else {
            showErrorMessage('删除失败: ' + result.message);
            throw new Error(result.message);
        }
    }
};

// WebSocket连接示例
const connectWebSocket = () => {
    const socket = new SockJS('/ws');
    const stompClient = Stomp.over(socket);

    stompClient.connect({}, function(frame) {
        console.log('Connected: ' + frame);

        // 订阅个人通知队列
        stompClient.subscribe('/queue/notifications', function(notification) {
            const notificationData = JSON.parse(notification.body);
            displayNotification(notificationData);
            updateUnreadCount();
        });
    });
};

// 2. Java客户端调用示例
@Service
public class NotificationClientService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${api.base-url}")
    private String baseUrl;

    // 获取通知列表
    public Page<Notification> getNotifications(Long receiverId, String receiverRole, int page, int size, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);

        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            String url = String.format("%s/api/v1/notifications?receiverId=%d&receiverRole=%s&page=%d&size=%d",
                baseUrl, receiverId, receiverRole, page, size);

            ResponseEntity<Result<Page<Notification>>> response = restTemplate.exchange(
                url, HttpMethod.GET, entity,
                new ParameterizedTypeReference<Result<Page<Notification>>>() {}
            );

            Result<Page<Notification>> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("获取通知列表失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用获取通知列表API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }

    // 标记所有通知为已读
    public void markAllAsRead(Long receiverId, String receiverRole, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);

        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            String url = String.format("%s/api/v1/notifications/read-all?receiverId=%d&receiverRole=%s",
                baseUrl, receiverId, receiverRole);

            ResponseEntity<Result<Void>> response = restTemplate.exchange(
                url, HttpMethod.PUT, entity,
                new ParameterizedTypeReference<Result<Void>>() {}
            );

            Result<Void> result = response.getBody();
            if (result == null || !result.isSuccess()) {
                throw new BusinessException("标记已读失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用标记已读API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }
}

// 3. 业务服务集成示例
@Service
public class NotificationWorkflowService {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private NotificationController notificationController;

    // 订单状态变更通知
    @EventListener
    @Async
    public void handleOrderStatusChanged(OrderStatusChangedEvent event) {
        try {
            // 创建通知对象
            Notification notification = new Notification();
            notification.setReceiverId(event.getBuyerId());
            notification.setReceiverRole("buyer");
            notification.setType("ORDER_STATUS");
            notification.setTitle("订单状态更新");
            notification.setContent(String.format("您的订单 %s 状态已更新为：%s",
                event.getOrderNumber(), event.getNewStatus()));
            notification.setRelatedId(event.getOrderId());
            notification.setCreatedAt(LocalDateTime.now());
            notification.setIsRead(false);

            // 保存通知到数据库
            notificationService.saveNotification(notification);

            // 实时推送给用户
            notificationController.sendNotificationToUser(event.getBuyerId(), notification);

            log.info("订单状态变更通知发送成功: orderId={}, buyerId={}",
                event.getOrderId(), event.getBuyerId());

        } catch (Exception e) {
            log.error("发送订单状态变更通知失败: orderId={}", event.getOrderId(), e);
        }
    }

    // 支付成功通知
    @EventListener
    @Async
    public void handlePaymentSuccess(PaymentSuccessEvent event) {
        try {
            // 通知买家
            Notification buyerNotification = createPaymentNotification(
                event.getBuyerId(), "buyer", "支付成功",
                String.format("您的订单 %s 支付成功，金额：￥%.2f",
                    event.getOrderNumber(), event.getAmount())
            );

            // 通知卖家
            Notification sellerNotification = createPaymentNotification(
                event.getSellerId(), "seller", "收到付款",
                String.format("订单 %s 已收到付款，金额：￥%.2f",
                    event.getOrderNumber(), event.getAmount())
            );

            // 保存并推送通知
            notificationService.saveNotification(buyerNotification);
            notificationService.saveNotification(sellerNotification);

            notificationController.sendNotificationToUser(event.getBuyerId(), buyerNotification);
            notificationController.sendNotificationToUser(event.getSellerId(), sellerNotification);

        } catch (Exception e) {
            log.error("发送支付成功通知失败: orderId={}", event.getOrderId(), e);
        }
    }
}

// 4. 定时任务示例
@Component
public class NotificationScheduledTasks {

    @Autowired
    private NotificationService notificationService;

    // 清理过期通知
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点
    public void cleanupExpiredNotifications() {
        log.info("开始清理过期通知");

        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30);
            int deletedCount = notificationService.deleteReadNotificationsBefore(cutoffDate);

            log.info("过期通知清理完成，删除数量: {}", deletedCount);

        } catch (Exception e) {
            log.error("清理过期通知失败", e);
        }
    }

    // 生成通知统计报告
    @Scheduled(cron = "0 0 8 1 * ?") // 每月1号早上8点
    public void generateMonthlyNotificationReport() {
        log.info("开始生成月度通知报告");

        try {
            MonthlyNotificationReport report = new MonthlyNotificationReport();
            report.setReportMonth(LocalDate.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM")));
            report.setTotalSent(notificationService.getSentCountLastMonth());
            report.setTotalRead(notificationService.getReadCountLastMonth());
            report.setReadRate(notificationService.getReadRateLastMonth());
            report.setTypeDistribution(notificationService.getTypeDistributionLastMonth());

            // 发送报告给管理员
            notificationService.sendMonthlyNotificationReport(report);

            log.info("月度通知报告生成完成");

        } catch (Exception e) {
            log.error("生成月度通知报告失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class NotificationControllerTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @MockBean
    private NotificationService notificationService;

    @Test
    void testGetNotifications() {
        // 准备测试数据
        Page<Notification> mockPage = new Page<>(1, 10);
        mockPage.setTotal(1);
        mockPage.setRecords(Arrays.asList(createMockNotification()));

        // Mock服务调用
        when(notificationService.getNotificationPage(100L, "buyer", 1, 10))
            .thenReturn(mockPage);

        // 执行测试
        ResponseEntity<Result> response = restTemplate.getForEntity(
            "/api/v1/notifications?receiverId=100&receiverRole=buyer&page=1&size=10", Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(notificationService, times(1)).getNotificationPage(100L, "buyer", 1, 10);
    }

    @Test
    void testMarkAsRead() {
        // 准备测试数据
        Long notificationId = 1L;

        // Mock服务调用
        doNothing().when(notificationService).markAsRead(notificationId);

        // 执行测试
        ResponseEntity<Result> response = restTemplate.exchange(
            "/api/v1/notifications/" + notificationId + "/read",
            HttpMethod.PUT, null, Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(notificationService, times(1)).markAsRead(notificationId);
    }

    @Test
    void testGetUnreadCount() {
        // 准备测试数据
        Long receiverId = 100L;
        String receiverRole = "buyer";
        Long expectedCount = 5L;

        // Mock服务调用
        when(notificationService.getUnreadCount(receiverId, receiverRole))
            .thenReturn(expectedCount);

        // 执行测试
        ResponseEntity<Result> response = restTemplate.getForEntity(
            "/api/v1/notifications/unread-count?receiverId=" + receiverId + "&receiverRole=" + receiverRole,
            Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(notificationService, times(1)).getUnreadCount(receiverId, receiverRole);
    }
}
```

### 示例1: 获取用户ID为123的买家的第一页通知
```bash
curl -X GET "http://localhost:8080/api/v1/notifications?receiverId=123&receiverRole=BUYER&page=1&size=10" \
-H "Authorization: Bearer <your_token>"
```

### 示例2: 将通知ID为456的通知标记为已读
```bash
curl -X PUT "http://localhost:8080/api/v1/notifications/456/read" \
-H "Authorization: Bearer <your_token>"
```

### 示例3: 获取用户ID为123的买家的未读通知数量
```bash
curl -X GET "http://localhost:8080/api/v1/notifications/unread-count?receiverId=123&receiverRole=BUYER" \
-H "Authorization: Bearer <your_token>"
```

### 示例4: 批量删除通知
```bash
curl -X DELETE "http://localhost:8080/api/v1/notifications/batch-delete" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <your_token>" \
-d '[101, 102, 103]'
```

### 示例5: 清空用户ID为123的买家的所有通知
```bash
curl -X DELETE "http://localhost:8080/api/v1/notifications/clear-all?receiverId=123&receiverRole=BUYER" \
-H "Authorization: Bearer <your_token>"
```

## 注意事项 (Notes)
*   **权限验证**: 确保用户只能访问和操作自己的通知，需要严格的身份验证和权限检查
*   **角色匹配**: 验证receiverRole参数与当前用户的实际角色是否匹配，防止角色伪造
*   **参数验证**: 使用@Valid注解对请求参数进行验证，确保数据的完整性和有效性
*   **分页性能**: 分页查询的每页大小限制在1-100之间，避免大量数据查询影响系统性能
*   **WebSocket管理**: 实时推送需要管理WebSocket连接状态，处理连接断开和重连机制
*   **业务委托**: 控制器只负责HTTP请求处理和响应封装，具体业务逻辑委托给NotificationService处理
*   **异常处理**: 完善的异常处理机制，区分业务异常和系统异常，返回友好的错误信息
*   **事务管理**: 批量操作需要使用@Transactional注解确保数据一致性
*   **并发控制**: 通知状态更新需要考虑并发控制，避免数据不一致问题
*   **缓存策略**: 未读通知数量等频繁查询的数据可以考虑使用Redis缓存提高性能
*   **数据清理**: 定期清理过期的已读通知，避免数据表过大影响查询性能
*   **推送限制**: WebSocket推送需要检查用户在线状态，离线用户通过查询接口获取通知
*   **通知模板**: 使用统一的通知模板系统，便于管理和国际化
*   **监控告警**: 对通知发送失败、WebSocket连接异常等关键指标进行监控和告警
*   **数据备份**: 重要通知数据需要定期备份，确保数据安全
*   **国际化**: 通知内容和错误消息需要支持多语言，便于国际化部署
*   **API文档**: 使用Swagger注解为API提供清晰的接口文档，便于前端开发和第三方集成