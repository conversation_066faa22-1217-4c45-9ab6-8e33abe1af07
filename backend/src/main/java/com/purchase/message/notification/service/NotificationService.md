# NotificationService.java

## 文件概述 (File Overview)
`NotificationService.java` 是系统通知管理的核心业务服务接口，位于 `com.purchase.message.notification.service` 包中，继承了MyBatis-Plus的 `IService<Notification>` 接口。该接口定义了通知系统的完整业务规范，提供了通知创建、发送、查询、状态管理等全生命周期功能。通过集成WebSocket实时推送、邮件通知、短信通知等多种通知渠道，支持多种通知类型（订单、支付、系统、竞价等），实现了基于用户角色的个性化通知管理，并提供了完善的通知模板系统和批量处理功能。

## 核心功能 (Core Functionality)
*   **通知创建管理**: 支持创建各种类型的通知，包含完整的业务验证和模板渲染
*   **多渠道推送**: 集成WebSocket、邮件、短信等多种通知渠道的统一推送服务
*   **实时通知推送**: 基于WebSocket的实时通知推送，确保用户及时收到通知
*   **通知查询服务**: 提供多维度的通知查询，支持分页、过滤、排序等功能
*   **状态管理**: 完善的通知状态管理，包括已读、未读、删除等状态控制
*   **批量操作**: 支持批量发送、批量标记、批量删除等高效处理功能
*   **通知模板**: 统一的通知模板管理，支持多语言和个性化定制
*   **用户偏好**: 基于用户偏好的通知设置和推送策略
*   **统计分析**: 通知发送统计、阅读率分析等数据统计功能
*   **定时通知**: 支持定时发送和延迟通知的调度管理
*   **通知分类**: 支持多种通知类型和优先级的分类管理
*   **数据清理**: 自动清理过期通知和垃圾数据的管理功能

## 业务规则 (Business Rules)
*   **权限隔离**: 用户只能查看和操作自己的通知，确保数据安全
*   **通知类型**: 支持订单、支付、系统、竞价等多种通知类型分类
*   **状态管理**: 通知状态包括未读、已读、删除，状态变更需要记录时间
*   **过期机制**: 通知有过期时间，过期后自动清理或标记为无效
*   **推送策略**: 根据用户偏好和通知类型选择合适的推送渠道
*   **模板系统**: 使用统一的通知模板，支持多语言和个性化定制
*   **批量限制**: 批量操作有数量限制，防止系统性能问题
*   **实时性要求**: 重要通知需要实时推送，确保用户及时收到

## 注意事项 (Notes)
*   **继承IService**: 接口继承了MyBatis-Plus的IService，实现类将自动拥有丰富的CRUD基础方法
*   **多渠道集成**: 需要集成WebSocket、邮件、短信等多种通知渠道，确保消息送达
*   **模板管理**: 使用统一的通知模板系统，支持多语言和个性化定制
*   **权限控制**: 严格的用户权限验证，确保通知数据的安全性
*   **性能优化**: 批量操作和分页查询需要考虑性能，避免大量数据处理
*   **实时性**: WebSocket推送需要管理连接状态，确保实时性
*   **数据清理**: 定期清理过期通知，避免数据积累过多
*   **异常处理**: 完善的异常处理机制，确保通知系统的稳定性
*   **监控告警**: 对通知发送失败、推送异常等关键指标进行监控
*   **事务管理**: 通知创建和发送需要事务保证，确保数据一致性
