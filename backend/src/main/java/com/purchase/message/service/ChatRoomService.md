# ChatRoomService.java

## 文件概述 (File Overview)
`ChatRoomService.java` 是聊天室管理的核心业务服务接口，位于 `com.purchase.message.service` 包中，继承了MyBatis-Plus的 `IService<ChatRoom>` 接口。该接口定义了聊天室系统的完整业务规范，提供了聊天室创建、管理、成员管理、权限控制等全生命周期功能。通过集成聊天室类型管理、成员邀请、权限分配等功能，支持多种聊天室类型（私聊、群聊、系统通知等），实现了基于业务场景的聊天室管理，并提供了完善的聊天室设置和统计功能。

## 核心功能 (Core Functionality)
*   **聊天室创建管理**: 支持创建各种类型的聊天室，包含完整的业务验证和初始化设置
*   **成员管理**: 提供聊天室成员的添加、移除、权限管理等功能
*   **权限控制**: 完善的聊天室权限管理，支持管理员、普通成员等不同角色
*   **聊天室查询**: 提供多维度的聊天室查询，支持分页、过滤、搜索等功能
*   **聊天室设置**: 支持聊天室名称、描述、头像等基本信息的管理
*   **聊天室类型**: 支持私聊、群聊、系统通知等多种聊天室类型
*   **成员邀请**: 提供成员邀请功能，支持邀请链接和直接邀请
*   **聊天室统计**: 提供聊天室活跃度、消息统计等数据分析功能
*   **聊天室归档**: 支持聊天室归档和恢复功能，便于管理历史聊天室
*   **聊天室搜索**: 提供强大的聊天室搜索功能，支持名称、成员等条件
*   **聊天室公告**: 支持聊天室公告功能，便于发布重要通知
*   **聊天室禁言**: 支持聊天室禁言功能，维护聊天室秩序

## 业务规则 (Business Rules)
*   **权限分级**: 聊天室有创建者、管理员、普通成员等不同权限级别
*   **聊天室类型**: 支持私聊（1对1）、群聊（多人）、系统通知等类型
*   **成员限制**: 群聊有成员数量限制，防止聊天室过大影响性能
*   **权限验证**: 只有有权限的用户才能查看和操作聊天室
*   **创建限制**: 用户创建聊天室有数量限制，防止滥用
*   **归档规则**: 长时间无活动的聊天室可以自动归档
*   **邀请限制**: 聊天室邀请有权限控制，防止恶意邀请
*   **数据保护**: 聊天室数据需要保护，防止未授权访问

## 注意事项 (Notes)
*   **继承IService**: 接口继承了MyBatis-Plus的IService，实现类将自动拥有丰富的CRUD基础方法
*   **权限管理**: 需要实现复杂的权限管理逻辑，确保聊天室安全
*   **成员管理**: 聊天室成员的增删改需要实时同步，保证数据一致性
*   **性能优化**: 聊天室查询和成员管理需要考虑性能，合理使用缓存
*   **并发控制**: 聊天室操作可能存在并发问题，需要适当的锁机制
*   **数据清理**: 定期清理无效的聊天室和过期数据
*   **异常处理**: 完善的异常处理机制，确保聊天室系统的稳定性
*   **监控告警**: 对聊天室创建异常、权限异常等关键指标进行监控
*   **事务管理**: 聊天室创建和成员管理需要事务保证，确保数据一致性
*   **缓存策略**: 合理使用缓存提高聊天室查询性能
