# ChatRoomUnreadService.md

## 1. 文件概述

`ChatRoomUnreadService.java` 是消息模块中一个高度专一化的服务接口，位于 `com.purchase.message.service` 包中。它的核心且唯一的职责是管理聊天室的未读消息计数。在即时通讯（IM）系统中，这是一个至关重要的功能，用于在UI上（如聊天列表、应用图标）显示小红点或数字角标，提示用户有新消息。该接口的设计体现了单一职责原则，将未读计数的复杂逻辑（如原子性增减、批量查询）从主聊天服务中分离出来，使其更易于维护和优化。

## 2. 核心功能

*   **原子性增加计数**: `incrementUnreadCount` 方法用于为一个或多个用户（通常是聊天室中除发送者外的所有成员）安全地增加未读消息数。这是在每次有新消息发送时调用的核心功能。
*   **计数清零**: `resetUnreadCount` 方法用于将指定用户在特定聊天室的未读数清零。这通常在用户进入聊天室或阅读了消息后调用。
*   **批量查询**: `getUserUnreadCounts` 方法允许一次性获取一个用户所有聊天室的未读消息数，返回一个以 `roomId` 为键、未读数为值的Map。这对于在聊天列表页面高效地展示所有对话的未读状态至关重要。
*   **单个查询**: `getUnreadCount` 方法用于获取单个聊天室的未读数，可用于进入聊天室时获取具体数字。
*   **高性能设计**: 该服务的实现通常会基于高性能的存储，如Redis，以应对高并发的读写请求，而不是直接操作主数据库。

## 3. 接口说明

### 未读计数操作接口

#### incrementUnreadCount - 增加未读消息计数
*   **方法签名**: `void incrementUnreadCount(Long roomId, Long userId)`
*   **参数**:
    *   `roomId` (Long): 需要增加未读计数的聊天室ID。
    *   `userId` (Long): 需要为其增加未读计数的接收方用户ID。
*   **返回值**: `void`
*   **业务逻辑**: 服务的实现需要原子性地对 `userId` 在 `roomId` 中的未读消息计数执行 `+1` 操作。通常，当一条消息被发送到聊天室时，会为聊天室中的所有其他成员调用此方法。

#### resetUnreadCount - 重置未读消息计数
*   **方法签名**: `void resetUnreadCount(Long roomId, Long userId)`
*   **参数**:
    *   `roomId` (Long): 需要重置未读计数的聊天室ID。
    *   `userId` (Long): 需要为其重置未读计数的接收方用户ID。
*   **返回值**: `void`
*   **业务逻辑**: 将 `userId` 在 `roomId` 中的未读消息计数直接设置为 `0`。这通常在用户点开某个聊天对话时触发。

#### getUserUnreadCounts - 获取用户所有聊天室的未读消息计数
*   **方法签名**: `Map<Long, Integer> getUserUnreadCounts(Long userId)`
*   **参数**:
    *   `userId` (Long): 需要查询其所有未读信息的用户ID。
*   **返回值**: `Map<Long, Integer>` - 一个Map，键是聊天室ID (`roomId`)，值是该聊天室对应的未读消息数。
*   **业务逻辑**: 从存储中一次性获取该用户参与的所有聊天室的未读计数值。如果使用Redis，这可能对应于一个 `HGETALL` 操作。

#### getUnreadCount - 获取指定聊天室的未读消息计数
*   **方法签名**: `Integer getUnreadCount(Long roomId, Long userId)`
*   **参数**:
    *   `roomId` (Long): 聊天室ID。
    *   `userId` (Long): 用户ID。
*   **返回值**: `Integer` - 指定用户在指定聊天室的未读消息数。如果不存在，应返回 `0`。

## 4. 业务规则

*   **原子性**: `incrementUnreadCount` 的实现必须是原子操作，以防止在高并发下出现计数不准确的问题。使用Redis的 `HINCRBY` 命令是实现这一点的理想方式。
*   **数据结构**: 在Redis中，存储未读数的一个高效方式是使用哈希表（Hash）。例如，每个用户一个Hash，键为 `unread:user:{userId}`，哈希表中的字段是 `roomId`，值是未读数。
*   **消息发送联动**: `ChatMessageService` 在发送一条新消息后，必须负责调用 `incrementUnreadCount` 来为聊天室的其他成员更新未读数。
*   **用户进入联动**: 当用户进入一个聊天室时（例如，前端WebSocket连接成功并订阅了该聊天室的主题），客户端应通知后端，后端随即调用 `resetUnreadCount` 来清零计数。

## 5. 使用示例

```java
// 1. 服务实现类示例 (基于Redis)
@Service
public class ChatRoomUnreadServiceImpl implements ChatRoomUnreadService {
    @Autowired
    private StringRedisTemplate redisTemplate;

    private String getKey(Long userId) {
        return "unread:user:" + userId;
    }

    @Override
    public void incrementUnreadCount(Long roomId, Long userId) {
        redisTemplate.opsForHash().increment(getKey(userId), roomId.toString(), 1);
    }

    @Override
    public void resetUnreadCount(Long roomId, Long userId) {
        redisTemplate.opsForHash().put(getKey(userId), roomId.toString(), "0");
    }

    @Override
    public Map<Long, Integer> getUserUnreadCounts(Long userId) {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(getKey(userId));
        Map<Long, Integer> result = new HashMap<>();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            result.put(Long.parseLong((String) entry.getKey()), Integer.parseInt((String) entry.getValue()));
        }
        return result;
    }
    // ...
}

// 2. 在ChatMessageService中发送消息时调用
@Service
public class ChatMessageServiceImpl implements ChatMessageService {
    @Autowired
    private ChatRoomUnreadService unreadService;
    @Autowired
    private ChatRoomMemberRepository memberRepository;

    @Override
    @Transactional
    public Long sendMessage(Long senderId, String senderRole, MessageRequest request) {
        // ... 保存消息到数据库 ...
        ChatMessage savedMessage = // ...

        // 为聊天室所有其他成员增加未读计数
        List<Long> otherMemberIds = memberRepository.findMemberIdsByRoomId(request.getRoomId())
            .stream().filter(id -> !id.equals(senderId)).collect(Collectors.toList());

        for (Long memberId : otherMemberIds) {
            unreadService.incrementUnreadCount(request.getRoomId(), memberId);
        }
        
        // ... 推送WebSocket消息 ...
        return savedMessage.getId();
    }
}

// 3. 在WebSocket控制器中处理用户进入房间事件时调用
@Controller
public class WebSocketController {
    @Autowired
    private ChatRoomUnreadService unreadService;

    @MessageMapping("/chat.enterRoom")
    public void handleEnterRoom(@Payload EnterRoomEvent event, SimpMessageHeaderAccessor headerAccessor) {
        Long userId = // ... 从headerAccessor获取用户ID
        Long roomId = event.getRoomId();
        
        // 用户进入房间，清空该房间的未读计数
        unreadService.resetUnreadCount(roomId, userId);
    }
}

// 4. 测试示例
@SpringBootTest
class ChatRoomUnreadServiceTest {
    @Autowired
    private ChatRoomUnreadService unreadService;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Test
    void testIncrementAndReset() {
        Long userId = 1L;
        Long roomId = 100L;
        String key = "unread:user:" + userId;
        String field = roomId.toString();

        // 初始状态应为0或null
        Integer initialCount = unreadService.getUnreadCount(roomId, userId);
        assertThat(initialCount).isEqualTo(0);

        // 增加3次
        unreadService.incrementUnreadCount(roomId, userId);
        unreadService.incrementUnreadCount(roomId, userId);
        unreadService.incrementUnreadCount(roomId, userId);
        
        // 验证计数为3
        assertThat(redisTemplate.opsForHash().get(key, field)).isEqualTo("3");
        assertThat(unreadService.getUnreadCount(roomId, userId)).isEqualTo(3);

        // 重置计数
        unreadService.resetUnreadCount(roomId, userId);
        assertThat(redisTemplate.opsForHash().get(key, field)).isEqualTo("0");
        assertThat(unreadService.getUnreadCount(roomId, userId)).isEqualTo(0);
    }
}
```

## 6. 注意事项

*   **性能与存储**: 未读消息计数是典型的“写多读多”场景，强烈建议使用Redis等内存数据库进行存储，以避免对主数据库造成性能压力。
*   **数据类型**: 在Redis中存储计数值时，应确保使用能被原子性增减的数据类型，如Hash中的字段值或独立的String。直接存为整数，使用 `INCRBY` 或 `HINCRBY`。
*   **最终一致性**: 未读数与实际消息数之间可能存在短暂的不一致（例如，在消息写入DB和未读数增加的间隙），但这在IM系统中通常是可接受的。系统设计保证最终一致性即可。
*   **冷启动**: 如果Redis数据丢失，未读数也会丢失。需要有恢复机制，例如在用户下次登录时，可以从数据库中比对最后阅读时间和消息时间来重建未读数。
*   **离线用户**: `incrementUnreadCount` 需要对离线用户同样有效。当用户再次上线时，通过调用 `getUserUnreadCounts` 就能获取其离线期间收到的所有新消息的未读总数。
*   **多端同步**: 如果用户在多个设备上登录（如PC和手机），当一个设备阅读了消息后，`resetUnreadCount` 被调用，其他设备应能通过WebSocket或轮询同步到这个“已读”状态，从而清除小红点。
*   **接口职责**: 该接口非常专注，只处理计数，不关心消息内容本身，这是优秀微服务或模块化设计的体现。