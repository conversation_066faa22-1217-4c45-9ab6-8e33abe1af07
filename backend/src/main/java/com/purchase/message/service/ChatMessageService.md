# ChatMessageService.java

## 文件概述 (File Overview)
`ChatMessageService.java` 是聊天消息管理的核心业务服务接口，位于 `com.purchase.message.service` 包中，继承了MyBatis-Plus的 `IService<ChatMessage>` 接口。该接口定义了聊天消息系统的完整业务规范，提供了消息发送、接收、查询、状态管理等全生命周期功能。通过集成实时消息推送、消息持久化、消息状态跟踪等功能，支持多种消息类型（文本、图片、文件等），实现了基于聊天室的消息管理，并提供了完善的消息历史记录和搜索功能。

## 核心功能 (Core Functionality)
*   **消息发送管理**: 支持发送各种类型的聊天消息，包含完整的业务验证和格式检查
*   **实时消息推送**: 基于WebSocket的实时消息推送，确保消息及时送达
*   **消息查询服务**: 提供多维度的消息查询，支持分页、过滤、搜索等功能
*   **消息状态管理**: 完善的消息状态管理，包括已发送、已送达、已读等状态控制
*   **消息历史记录**: 支持消息历史记录的存储和查询，便于用户回顾聊天内容
*   **消息类型支持**: 支持文本、图片、文件、语音等多种消息类型
*   **消息搜索**: 提供强大的消息搜索功能，支持关键词、时间范围等条件
*   **消息撤回**: 支持消息撤回功能，允许用户在一定时间内撤回已发送的消息
*   **消息转发**: 支持消息转发功能，便于用户分享重要信息
*   **消息统计**: 提供消息发送统计、活跃度分析等数据统计功能
*   **敏感词过滤**: 集成敏感词过滤功能，确保聊天内容的合规性
*   **消息加密**: 支持消息加密传输，保护用户隐私和数据安全

## 业务规则 (Business Rules)
*   **权限控制**: 用户只能在有权限的聊天室中发送和查看消息
*   **消息类型**: 支持文本、图片、文件、语音等多种消息类型
*   **状态管理**: 消息状态包括已发送、已送达、已读，状态变更需要实时更新
*   **撤回限制**: 消息撤回有时间限制，超过时间后不能撤回
*   **文件大小限制**: 文件类型消息有大小限制，防止系统资源占用过多
*   **敏感词过滤**: 自动过滤敏感词汇，确保聊天内容合规
*   **消息持久化**: 所有消息都需要持久化存储，便于历史查询
*   **实时性要求**: 消息需要实时推送，确保用户及时收到

## 注意事项 (Notes)
*   **继承IService**: 接口继承了MyBatis-Plus的IService，实现类将自动拥有丰富的CRUD基础方法
*   **实时推送**: 需要集成WebSocket实现实时消息推送功能
*   **文件处理**: 对于文件类型消息，需要处理文件上传、存储和下载
*   **权限验证**: 严格的聊天室权限验证，确保消息安全
*   **性能优化**: 消息查询和历史记录需要考虑性能，合理使用索引
*   **数据清理**: 定期清理过期的临时文件和无效消息
*   **异常处理**: 完善的异常处理机制，确保消息系统的稳定性
*   **监控告警**: 对消息发送失败、推送异常等关键指标进行监控
*   **事务管理**: 消息发送和状态更新需要事务保证，确保数据一致性
*   **安全防护**: 防止消息轰炸、恶意消息等安全问题
