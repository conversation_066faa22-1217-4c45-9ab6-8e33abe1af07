# ChatRoomUnreadServiceImpl.java

## 文件概述 (File Overview)
`ChatRoomUnreadServiceImpl.java` 是聊天室未读消息计数服务的具体实现类，实现了 `ChatRoomUnreadService` 接口。该类负责管理聊天室中每个用户的未读消息计数，提供增加未读数、重置未读数、查询未读数等核心功能。它通过 `ChatRoomUnreadMapper` 与数据库交互，为实时聊天系统提供准确的未读消息统计功能，确保用户能够及时了解未读消息的数量。

## 核心功能 (Core Functionality)
*   **未读计数增加:** 当有新消息发送到聊天室时，为相关用户增加未读消息计数。
*   **未读计数重置:** 当用户查看聊天室消息时，将该用户在该聊天室的未读计数重置为0。
*   **用户未读统计:** 获取指定用户在所有聊天室的未读消息计数映射。
*   **单室未读查询:** 查询指定用户在特定聊天室的未读消息数量。
*   **数据库交互:** 通过Mapper接口与数据库进行高效的未读计数操作。
*   **集合转换:** 将数据库查询结果转换为便于使用的Map结构。
*   **空值处理:** 对查询结果进行空值检查，确保返回值的安全性。

## 接口说明 (Interface Description)

### 主要方法 (Main Methods)

#### 1. incrementUnreadCount(Long roomId, Long userId)
*   **参数:**
    *   `roomId` - 聊天室ID
    *   `userId` - 用户ID
*   **返回值:** `void`
*   **功能:** 增加指定用户在指定聊天室的未读消息计数
*   **业务逻辑:**
    1. 调用Mapper的incrementUnreadCount方法
    2. 如果该用户在该聊天室没有未读记录，则创建新记录并设置计数为1
    3. 如果已有记录，则将计数加1

#### 2. resetUnreadCount(Long roomId, Long userId)
*   **参数:**
    *   `roomId` - 聊天室ID
    *   `userId` - 用户ID
*   **返回值:** `void`
*   **功能:** 重置指定用户在指定聊天室的未读消息计数为0
*   **业务逻辑:**
    1. 调用Mapper的resetUnreadCount方法
    2. 将指定用户在指定聊天室的未读计数设置为0
    3. 通常在用户查看聊天室消息时调用

#### 3. getUserUnreadCounts(Long userId)
*   **参数:** `userId` - 用户ID
*   **返回值:** `Map<Long, Integer>` - 聊天室ID到未读计数的映射
*   **功能:** 获取指定用户在所有聊天室的未读消息计数
*   **业务逻辑:**
    1. 查询用户在所有聊天室的未读记录
    2. 使用Stream API将结果转换为Map结构
    3. Key为聊天室ID，Value为未读消息数量

#### 4. getUnreadCount(Long roomId, Long userId)
*   **参数:**
    *   `roomId` - 聊天室ID
    *   `userId` - 用户ID
*   **返回值:** `Integer` - 未读消息数量
*   **功能:** 获取指定用户在指定聊天室的未读消息数量
*   **业务逻辑:**
    1. 查询指定用户在指定聊天室的未读记录
    2. 如果记录存在，返回未读计数
    3. 如果记录不存在，返回0

### 依赖注入 (Dependency Injection)

#### ChatRoomUnreadMapper
*   **注入方式:** `@Autowired`
*   **作用:** 提供数据库访问功能
*   **主要方法:**
    *   `incrementUnreadCount(Long roomId, Long userId)` - 增加未读计数
    *   `resetUnreadCount(Long roomId, Long userId)` - 重置未读计数
    *   `getUserUnreadCounts(Long userId)` - 查询用户所有未读计数
    *   `getUnreadCount(Long roomId, Long userId)` - 查询特定未读计数

## 使用示例 (Usage Examples)

```java
// 在ChatMessageService中使用ChatRoomUnreadServiceImpl
@Service
public class ChatMessageServiceImpl implements ChatMessageService {
    @Autowired
    private ChatRoomUnreadService chatRoomUnreadService;

    @Autowired
    private ChatRoomService chatRoomService;

    public ChatMessageDTO sendMessage(MessageRequest request, Long senderId) {
        // 发送消息的业务逻辑
        ChatMessage message = new ChatMessage();
        message.setRoomId(request.getRoomId());
        message.setSenderId(senderId);
        message.setTextContent(request.getTextContent());
        message.setMessageType(request.getMessageType());

        // 保存消息
        chatMessageMapper.insert(message);

        // 获取聊天室成员
        List<Long> memberIds = chatRoomService.getRoomMemberIds(request.getRoomId());

        // 为除发送者外的所有成员增加未读计数
        memberIds.stream()
            .filter(memberId -> !memberId.equals(senderId))
            .forEach(memberId ->
                chatRoomUnreadService.incrementUnreadCount(request.getRoomId(), memberId)
            );

        return convertToDTO(message);
    }
}

// 在ChatMessageController中使用
@RestController
@RequestMapping("/api/v1/message")
public class ChatMessageController {
    @Autowired
    private ChatRoomUnreadService chatRoomUnreadService;

    @PutMapping("/room/{roomId}/read")
    public Result<String> markAsRead(@PathVariable Long roomId, Authentication authentication) {
        Long userId = getUserIdFromAuthentication(authentication);

        // 重置未读计数
        chatRoomUnreadService.resetUnreadCount(roomId, userId);

        return Result.success("消息已标记为已读");
    }

    @GetMapping("/unread-counts")
    public Result<Map<Long, Integer>> getUnreadCounts(Authentication authentication) {
        Long userId = getUserIdFromAuthentication(authentication);

        // 获取用户所有聊天室的未读计数
        Map<Long, Integer> unreadCounts = chatRoomUnreadService.getUserUnreadCounts(userId);

        return Result.success(unreadCounts);
    }

    @GetMapping("/room/{roomId}/unread-count")
    public Result<Integer> getRoomUnreadCount(@PathVariable Long roomId, Authentication authentication) {
        Long userId = getUserIdFromAuthentication(authentication);

        // 获取特定聊天室的未读计数
        Integer unreadCount = chatRoomUnreadService.getUnreadCount(roomId, userId);

        return Result.success(unreadCount);
    }
}

// 在WebSocket处理器中使用
@Component
public class ChatWebSocketHandler {
    @Autowired
    private ChatRoomUnreadService chatRoomUnreadService;

    public void handleMessage(WebSocketSession session, ChatMessage message) {
        // 处理WebSocket消息
        Long senderId = getUserIdFromSession(session);
        Long roomId = message.getRoomId();

        // 广播消息给聊天室成员
        List<Long> memberIds = getRoomMembers(roomId);

        for (Long memberId : memberIds) {
            if (!memberId.equals(senderId)) {
                // 为其他成员增加未读计数
                chatRoomUnreadService.incrementUnreadCount(roomId, memberId);

                // 发送未读计数更新通知
                sendUnreadCountUpdate(memberId, roomId);
            }
        }
    }

    private void sendUnreadCountUpdate(Long userId, Long roomId) {
        Integer unreadCount = chatRoomUnreadService.getUnreadCount(roomId, userId);
        Map<Long, Integer> allUnreadCounts = chatRoomUnreadService.getUserUnreadCounts(userId);

        // 通过WebSocket发送未读计数更新
        WebSocketMessage updateMessage = new WebSocketMessage();
        updateMessage.setType("unread_count_update");
        updateMessage.setData(Map.of(
            "roomId", roomId,
            "unreadCount", unreadCount,
            "totalUnreadCount", allUnreadCounts.values().stream().mapToInt(Integer::intValue).sum()
        ));

        sendToUser(userId, updateMessage);
    }
}
```

## 注意事项 (Notes)
*   **线程安全:** 该服务的方法可能在多线程环境下被调用，需要确保数据库操作的原子性。
*   **性能考虑:** 未读计数的更新操作频繁，建议在数据库层面优化相关SQL语句的性能。
*   **数据一致性:** 增加和重置未读计数的操作需要保证数据一致性，避免并发问题。
*   **空值处理:** `getUnreadCount` 方法对查询结果进行了空值检查，确保返回值不为null。
*   **集合转换:** `getUserUnreadCounts` 方法使用Stream API进行集合转换，提高了代码的可读性。
*   **业务逻辑:** 该服务只负责未读计数的管理，不涉及具体的消息内容处理。
*   **依赖关系:** 该服务依赖于 `ChatRoomUnreadMapper` 进行数据库操作，需要确保Mapper的正确配置。
*   **扩展性:** 如果需要支持更复杂的未读消息统计（如按消息类型统计），可以扩展相应的方法。
*   **缓存考虑:** 对于频繁查询的未读计数，可以考虑引入缓存机制来提高性能。
