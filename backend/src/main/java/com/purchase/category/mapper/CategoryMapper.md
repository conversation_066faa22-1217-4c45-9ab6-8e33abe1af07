# CategoryMapper.md

## 1. 文件概述

`CategoryMapper.java` 是分类模块中的一个MyBatis-Plus Mapper接口，位于 `com.purchase.category.mapper` 包中。它继承自MyBatis-Plus的 `BaseMapper<Category>` 接口，并在此基础上定义了多个自定义的查询方法。`Category` 实体代表了商品或需求的分类信息。该Mapper接口是分类服务层与数据库进行交互的桥梁，负责将业务对象的操作转换为SQL语句，实现分类数据的持久化管理和多维度查询。

## 2. 核心功能

*   **基础CRUD操作**: 继承 `BaseMapper`，自动拥有对 `Category` 实体进行插入（`insert`）、根据ID查询（`selectById`）、根据条件查询列表（`selectList`）、更新（`updateById`）和删除（`deleteById`）等基础的增删改查功能。
*   **层级查询**: 提供了 `selectRootCategories`（获取所有根分类）和 `selectChildrenCategories`（获取指定父分类的子分类）方法，支持分类树的懒加载和层级遍历。
*   **子分类检查**: 提供了 `hasChildren` 方法，用于快速判断某个分类是否包含子分类。
*   **分类路径查询**: 提供了 `selectCategoryPath` 方法，使用递归CTE（Common Table Expression）查询从根分类到指定分类的完整路径。
*   **模糊搜索**: 提供了 `searchCategories` 方法，用于根据关键词模糊搜索分类名称，并支持按匹配度进行排序。
*   **全文索引搜索**: 提供了 `searchCategoriesWithFullText` 方法，利用MySQL的全文索引功能进行更高效的英文关键词搜索，并支持按相关性排序。
*   **分类级别查询**: 提供了 `selectCategoryLevel` 方法，用于查询指定分类的层级。

## 3. 接口说明

`CategoryMapper` 在继承 `BaseMapper` 的基础上，定义了以下自定义方法：

### 3.1 层级查询方法

#### selectRootCategories - 获取根分类列表
*   **方法签名**: `List<Category> selectRootCategories()`
*   **描述**: 查询所有 `parent_id` 为 `NULL` 或 `0` 且状态为 `1`（启用）的根分类，并计算其子分类数量。
*   **SQL**: `@Select(...)` 注解中包含了子查询来计算 `children_count`。

#### selectChildrenCategories - 获取指定父分类的直接子分类
*   **方法签名**: `List<Category> selectChildrenCategories(@Param("parentId") Long parentId)`
*   **描述**: 根据父分类ID查询其直接子分类，并计算每个子分类的子分类数量。
*   **参数**:
    *   `parentId` (Long): 父分类的ID。
*   **SQL**: `@Select(...)` 注解中包含了子查询来计算 `children_count`。

#### hasChildren - 检查是否有子分类
*   **方法签名**: `boolean hasChildren(@Param("categoryId") Long categoryId)`
*   **描述**: 检查指定分类下是否存在状态为 `1` 的子分类。
*   **参数**:
    *   `categoryId` (Long): 分类ID。
*   **返回值**: `boolean` - 如果存在子分类，返回 `true`；否则返回 `false`。

#### selectCategoryPath - 获取分类路径
*   **方法签名**: `List<Category> selectCategoryPath(@Param("categoryId") Long categoryId)`
*   **描述**: 使用递归CTE查询从根分类到指定分类的完整路径。返回的列表按层级降序排列（根分类在最后）。
*   **参数**:
    *   `categoryId` (Long): 分类ID。
*   **SQL**: `@Select(...)` 注解中使用了 `WITH RECURSIVE` 语法。

### 3.2 搜索查询方法

#### searchCategories - 模糊搜索分类
*   **方法签名**: `List<Category> searchCategories(@Param("keyword") String keyword, @Param("offset") Integer offset, @Param("size") Integer size)`
*   **描述**: 根据关键词在 `name` 字段中进行模糊搜索，并支持分页。结果按匹配度（关键词在开头、结尾、中间）和 `sort_order` 排序。
*   **参数**:
    *   `keyword` (String): 搜索关键词。
    *   `offset` (Integer): 分页偏移量。
    *   `size` (Integer): 每页大小。
*   **SQL**: `@Select(...)` 注解中使用了 `LIKE CONCAT` 和 `ORDER BY CASE`。

#### searchCategoriesWithFullText - 全文索引搜索分类
*   **方法签名**: `List<Category> searchCategoriesWithFullText(@Param("exactKeyword") String exactKeyword, @Param("wildcardKeyword") String wildcardKeyword, @Param("naturalKeyword") String naturalKeyword, @Param("offset") Integer offset, @Param("size") Integer size)`
*   **描述**: 利用MySQL的全文索引功能，对 `name_en` 字段进行搜索。支持精确匹配、通配符匹配和自然语言匹配，并按相关性排序。
*   **参数**:
    *   `exactKeyword` (String): 精确匹配关键词。
    *   `wildcardKeyword` (String): 通配符匹配关键词。
    *   `naturalKeyword` (String): 自然语言匹配关键词。
    *   `offset` (Integer): 分页偏移量。
    *   `size` (Integer): 每页大小。
*   **SQL**: `@Select(...)` 注解中使用了 `MATCH() AGAINST()` 语法和 `relevance_score` 计算。

### 3.3 辅助查询方法

#### selectCategoryLevel - 获取分类的级别
*   **方法签名**: `Integer selectCategoryLevel(@Param("categoryId") Long categoryId)`
*   **描述**: 查询指定分类的层级（从1开始）。
*   **参数**:
    *   `categoryId` (Long): 分类ID。
*   **返回值**: `Integer` - 分类的层级。
*   **SQL**: `@Select(...)` 注解中使用了 `WITH RECURSIVE` 语法。

## 4. 业务规则

*   **MyBatis-Plus集成**: 继承 `BaseMapper` 使得该Mapper自动拥有强大的CRUD能力，减少了重复代码。
*   **SQL注解**: 大量使用了 `@Select` 注解直接编写SQL。这使得SQL逻辑清晰可见，但对于非常复杂的查询，可能导致注解过长，难以维护。此时可以考虑将SQL提取到XML文件中。
*   **参数绑定**: 使用 `@Param` 注解将Java方法参数绑定到SQL中的命名参数，有效防止SQL注入。
*   **性能优化**: 考虑到分类数据量可能较大且查询频繁，Mapper中的SQL查询必须高度优化。应为 `parent_id`, `status`, `sort_order`, `name`, `name_en` 等常用查询字段建立合适的索引。对于全文搜索，需要确保数据库表已创建全文索引。
*   **事务管理**: 在服务层调用Mapper方法时，必须确保在事务中执行，以保证数据操作的原子性和一致性。
*   **递归查询**: `selectCategoryPath` 和 `selectCategoryLevel` 使用了递归CTE，这在MySQL 8.0+版本中支持，用于处理层级数据。
*   **搜索策略**: `searchCategories` 和 `searchCategoriesWithFullText` 提供了两种不同的搜索策略，分别适用于不同语言和索引类型。

## 5. 使用示例

```java
// 1. 在 CategoryService 实现中获取根分类
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category>
        implements CategoryService {
    @Autowired
    private CategoryMapper categoryMapper;

    @Override
    public List<CategoryDTO> getRootCategories() {
        List<Category> categories = categoryMapper.selectRootCategories();
        return categories.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<CategoryDTO> getChildrenCategories(Long parentId) {
        List<Category> categories = categoryMapper.selectChildrenCategories(parentId);
        return categories.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<CategoryDTO> getCategoryPath(Long categoryId) {
        List<Category> categories = categoryMapper.selectCategoryPath(categoryId);
        // 路径是倒序的，需要反转
        Collections.reverse(categories);
        return categories.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    // 辅助方法：实体转换为DTO
    private CategoryDTO convertToDTO(Category entity) {
        CategoryDTO dto = new CategoryDTO();
        BeanUtils.copyProperties(entity, dto);
        // 填充非持久化字段
        dto.setHasChildren(categoryMapper.hasChildren(entity.getId()));
        dto.setLevel(categoryMapper.selectCategoryLevel(entity.getId()));
        return dto;
    }
}

// 2. 测试示例
@SpringBootTest
class CategoryMapperTest {
    @Autowired
    private CategoryMapper categoryMapper;

    @Test
    @Transactional
    void testSelectRootCategories() {
        // 插入一些根分类
        categoryMapper.insert(Category.builder().name("Root1").parentId(0L).status(1).build());
        categoryMapper.insert(Category.builder().name("Root2").parentId(0L).status(1).build());

        List<Category> rootCategories = categoryMapper.selectRootCategories();
        assertThat(rootCategories).anyMatch(c -> c.getName().equals("Root1"));
        assertThat(rootCategories).anyMatch(c -> c.getName().equals("Root2"));
    }

    @Test
    @Transactional
    void testSelectCategoryPath() {
        // 插入层级数据
        Category root = Category.builder().name("Root").parentId(0L).status(1).build();
        categoryMapper.insert(root);
        Category child1 = Category.builder().name("Child1").parentId(root.getId()).status(1).build();
        categoryMapper.insert(child1);
        Category child2 = Category.builder().name("Child2").parentId(child1.getId()).status(1).build();
        categoryMapper.insert(child2);

        List<Category> path = categoryMapper.selectCategoryPath(child2.getId());
        assertThat(path).hasSize(3);
        assertThat(path.get(0).getName()).isEqualTo("Child2");
        assertThat(path.get(1).getName()).isEqualTo("Child1");
        assertThat(path.get(2).getName()).isEqualTo("Root");
    }

    @Test
    @Transactional
    void testSearchCategories() {
        categoryMapper.insert(Category.builder().name("办公桌").nameEn("Office Desk").status(1).build());
        categoryMapper.insert(Category.builder().name("办公椅").nameEn("Office Chair").status(1).build());

        List<Category> results = categoryMapper.searchCategories("办公", 0, 10);
        assertThat(results).hasSize(2);
        assertThat(results.get(0).getName()).isEqualTo("办公桌");
    }
}
```

## 6. 注意事项

*   **MyBatis-Plus集成**: 继承 `BaseMapper` 使得该Mapper自动拥有强大的CRUD能力，减少了重复代码。
*   **注解SQL**: 大量使用了 `@Select` 注解直接编写SQL。这使得SQL逻辑清晰可见，但对于非常复杂的查询，可能导致注解过长，难以维护。此时可以考虑将SQL提取到XML文件中。
*   **参数绑定**: 使用 `@Param` 注解将Java方法参数绑定到SQL中的命名参数，有效防止SQL注入。
*   **性能优化**: 考虑到分类数据量可能较大且查询频繁，Mapper中的SQL查询必须高度优化。应为 `parent_id`, `status`, `sort_order`, `name`, `name_en` 等常用查询字段建立合适的索引。对于全文搜索，需要确保数据库表已创建全文索引。
*   **事务管理**: 在服务层调用Mapper方法时，必须确保在事务中执行，以保证数据操作的原子性和一致性。
*   **递归查询**: `selectCategoryPath` 和 `selectCategoryLevel` 使用了递归CTE，这在MySQL 8.0+版本中支持，用于处理层级数据。
*   **搜索策略**: `searchCategories` 和 `searchCategoriesWithFullText` 提供了两种不同的搜索策略，分别适用于不同语言和索引类型。
*   **非持久化字段**: 实体中的非持久化字段（如 `children_count`, `level`）在Mapper中通过SQL计算并返回，这是一种将展示逻辑与持久化模型分离的良好实践。
