# com.purchase.category.mapper 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.category.mapper` 包包含了商品分类模块的 MyBatis Mapper 接口。这些接口定义了与数据库中 `requirement_category` 表进行交互的方法。通过继承 MyBatis-Plus 的 `BaseMapper`，它们获得了基本的 CRUD（创建、读取、更新、删除）操作，并在此基础上扩展了针对特定业务需求的自定义查询方法，如多级分类的查询、子分类数量统计以及基于关键词的分类搜索。该包是数据持久化层与业务逻辑层之间的桥梁。

## 目录结构概览 (Directory Structure Overview)
*   `CategoryMapper.java`: 商品分类表的 Mapper 接口。
*   `CategoryMapper.md`: `CategoryMapper.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.category.mapper` 包中的 Mapper 接口与 `com.purchase.category.entity` 包中的实体类以及 `com.purchase.category.service` 包中的服务层紧密协作，共同实现了商品分类的数据持久化功能：

1.  **实体与 Mapper 的映射:** `CategoryMapper` 接口以 `Category` 实体作为其泛型参数，表明它操作的是这种类型的实体。MyBatis-Plus 会根据这种关联自动生成大部分 SQL 操作。

2.  **继承 `BaseMapper`:** `CategoryMapper` 继承自 `com.baomidou.mybatisplus.core.mapper.BaseMapper`。这使得它无需编写任何 SQL 即可获得对 `Category` 实体进行基本的单表 CRUD 操作的能力，极大地提高了开发效率。

3.  **自定义 SQL 操作:** 对于 `BaseMapper` 不支持的复杂查询或特定业务逻辑，`CategoryMapper` 中定义了多个自定义方法，并通过 `@Select` 注解直接编写 SQL 语句。这些自定义方法包括：
    *   `selectRootCategories()` 和 `selectChildrenCategories()`：用于查询分类的层级结构，并计算子分类数量。
    *   `hasChildren()`：快速判断某个分类是否有子分类。
    *   `selectCategoryPath()` 和 `selectCategoryLevel()`：利用 SQL 的递归 CTE 功能，高效地查询分类的完整路径和级别。
    *   `searchCategories()` 和 `searchCategoriesWithFullText()`：提供了两种不同的搜索策略，分别适用于中文模糊搜索和英文/数字全文索引搜索，并包含排序优化。

4.  **与服务层的交互:** 服务层（如 `CategoryServiceImpl`）通过 `@Autowired` 注入 `CategoryMapper` 接口的实例。服务层负责调用 Mapper 接口的方法来执行数据库操作，然后处理业务逻辑（例如，将 `Category` 实体转换为 `CategoryDTO`），并将结果返回给控制器层或上游调用者。

**协作流程总结:**

*   当服务层需要对分类数据进行操作时，它会调用 `CategoryMapper` 接口中相应的方法。
*   这些 Mapper 方法会与 MyBatis-Plus 框架协作，将操作转换为实际的 SQL 语句并发送到数据库执行。例如，`selectRootCategories()` 会执行一个包含子查询的 SQL 来获取根分类及其子分类数量。
*   数据库执行结果会再通过 Mapper 接口返回给服务层。服务层会进一步处理这些数据，例如计算 `level` 和 `fullPath`，然后转换为 DTO 返回。

这种数据访问层设计确保了业务逻辑与数据访问逻辑的清晰分离，提高了代码的可维护性和可测试性，并充分利用了 MyBatis-Plus 和数据库的查询能力。
