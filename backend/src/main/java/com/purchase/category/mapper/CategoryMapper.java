package com.purchase.category.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.purchase.category.entity.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 分类Mapper接口
 */
@Mapper
public interface CategoryMapper extends BaseMapper<Category> {

    /**
     * 获取根分类列表（一级分类）
     */
    @Select("SELECT *, " +
            "(SELECT COUNT(*) FROM requirement_category c2 WHERE c2.parent_id = c1.id AND c2.status = 1) as children_count " +
            "FROM requirement_category c1 " +
            "WHERE c1.parent_id IS NULL AND c1.status = 1 " +
            "ORDER BY c1.sort_order ASC, c1.id ASC")
    List<Category> selectRootCategories();

    /**
     * 获取指定父分类的直接子分类
     */
    @Select("SELECT *, " +
            "(SELECT COUNT(*) FROM requirement_category c2 WHERE c2.parent_id = c1.id AND c2.status = 1) as children_count " +
            "FROM requirement_category c1 " +
            "WHERE c1.parent_id = #{parentId} AND c1.status = 1 " +
            "ORDER BY c1.sort_order ASC, c1.id ASC")
    List<Category> selectChildrenCategories(@Param("parentId") Long parentId);

    /**
     * 检查是否有子分类
     */
    @Select("SELECT COUNT(*) > 0 FROM requirement_category " +
            "WHERE parent_id = #{categoryId} AND status = 1")
    boolean hasChildren(@Param("categoryId") Long categoryId);

    /**
     * 获取分类路径（从根到指定分类）
     */
    @Select("WITH RECURSIVE category_path AS (" +
            "  SELECT id, name, name_en, parent_id, 1 as level " +
            "  FROM requirement_category " +
            "  WHERE id = #{categoryId} AND status = 1 " +
            "  UNION ALL " +
            "  SELECT c.id, c.name, c.name_en, c.parent_id, cp.level + 1 " +
            "  FROM requirement_category c " +
            "  INNER JOIN category_path cp ON c.id = cp.parent_id " +
            "  WHERE c.status = 1" +
            ") " +
            "SELECT * FROM category_path ORDER BY level DESC")
    List<Category> selectCategoryPath(@Param("categoryId") Long categoryId);

    /**
     * 使用 LIKE 模糊搜索分类（主要用于中文等场景的回退方案）。
     * 关键词在最后的记录优先级更高
     * @param keyword 搜索关键词
     * @param offset  分页偏移量
     * @param size    每页大小
     * @return 分类列表
     */
    // TODO: 未来 Elasticsearch 重构点。此为针对中文等无法有效使用FULLTEXT索引的语言的回退方案。
    @Select("SELECT *, " +
            "(SELECT COUNT(*) FROM requirement_category c2 WHERE c2.parent_id = c1.id AND c2.status = 1) as children_count " +
            "FROM requirement_category c1 " +
            "WHERE c1.status = 1 " +
            "AND c1.name LIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY " +
            "CASE " +
            "  WHEN c1.name LIKE CONCAT('%', #{keyword}) THEN 1 " +
            "  WHEN c1.name LIKE CONCAT(#{keyword}, '%') THEN 2 " +
            "  ELSE 3 " +
            "END, " +
            "c1.sort_order ASC, c1.id ASC " +
            "LIMIT #{offset}, #{size}")
    List<Category> searchCategories(@Param("keyword") String keyword, 
                                   @Param("offset") Integer offset, 
                                   @Param("size") Integer size);

    /**
     * 使用 MySQL 全文索引搜索分类（主要用于英文、数字等高性能场景）。
     * 优化版本：支持部分词匹配，如"office desk"可以匹配"Office Desks"
     * 按相关性排序，相关性高的记录优先
     * @param keyword 搜索关键词
     * @param offset  分页偏移量
     * @param size    每页大小
     * @return 分类列表
     */
    // TODO: 未来 Elasticsearch 重构点。此为针对英文及数字的 FULLTEXT 索引优化方案。
    @Select("SELECT *, " +
        "(SELECT COUNT(*) FROM requirement_category c2 WHERE c2.parent_id = c1.id AND c2.status = 1) as children_count, " +
        "(" +
        "  CASE " +
        "    WHEN MATCH(name_en) AGAINST(#{exactKeyword} IN BOOLEAN MODE) > 0 THEN " +
        "      MATCH(name_en) AGAINST(#{exactKeyword} IN BOOLEAN MODE) * 2 " +
        "    WHEN MATCH(name_en) AGAINST(#{wildcardKeyword} IN BOOLEAN MODE) > 0 THEN " +
        "      MATCH(name_en) AGAINST(#{wildcardKeyword} IN BOOLEAN MODE) * 1.5 " +
        "    WHEN MATCH(name_en) AGAINST(#{naturalKeyword}) > 0 THEN " +
        "      MATCH(name_en) AGAINST(#{naturalKeyword}) " +
        "    ELSE 0 " +
        "  END" +
        ") as relevance_score " +
        "FROM requirement_category c1 " +
        "WHERE c1.status = 1 " +
        "AND (" +
        "  MATCH(name_en) AGAINST(#{exactKeyword} IN BOOLEAN MODE) " +
        "  OR MATCH(name_en) AGAINST(#{wildcardKeyword} IN BOOLEAN MODE) " +
        "  OR MATCH(name_en) AGAINST(#{naturalKeyword}) " +
        ") " +
        "ORDER BY relevance_score DESC, c1.sort_order ASC, c1.id ASC " +
        "LIMIT #{offset}, #{size}")
        List<Category> searchCategoriesWithFullText(@Param("exactKeyword") String exactKeyword,
                                                @Param("wildcardKeyword") String wildcardKeyword,
                                                @Param("naturalKeyword") String naturalKeyword,
                                                @Param("offset") Integer offset,
                                                @Param("size") Integer size);

    /**
     * 获取分类的级别
     */
    @Select("WITH RECURSIVE category_level AS (" +
            "  SELECT id, parent_id, 1 as level " +
            "  FROM requirement_category " +
            "  WHERE parent_id IS NULL AND status = 1 " +
            "  UNION ALL " +
            "  SELECT c.id, c.parent_id, cl.level + 1 " +
            "  FROM requirement_category c " +
            "  INNER JOIN category_level cl ON c.parent_id = cl.id " +
            "  WHERE c.status = 1" +
            ") " +
            "SELECT level FROM category_level WHERE id = #{categoryId}")
    Integer selectCategoryLevel(@Param("categoryId") Long categoryId);
} 