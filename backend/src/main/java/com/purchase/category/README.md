# com.purchase.category 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.category` 包是采购系统后端应用程序中负责商品分类管理的核心模块。它遵循领域驱动设计（DDD）的架构原则，将分类相关的业务逻辑划分为清晰的层次：接口层、应用服务层、数据访问层。该模块提供了商品分类的创建、查询（包括根分类、子分类、分类路径）、搜索以及详情获取等功能，旨在为前端提供灵活、高效的分类数据访问能力，并确保分类数据的完整性和一致性。

## 目录结构概览 (Directory Structure Overview)
*   `controller/`: 接口层，包含商品分类的 RESTful API 控制器。
*   `dto/`: 应用层，包含用于前端与应用服务之间数据传输的分类数据传输对象（DTO）。
*   `entity/`: 领域层，包含商品分类的持久化实体。
*   `mapper/`: 基础设施层，包含 MyBatis Mapper 接口，用于与数据库表交互。
*   `service/`: 应用层/领域层，包含商品分类服务的接口及其实现。
*   `controller/README.md`: `controller` 子包的文档。
*   `dto/README.md`: `dto` 子包的文档。
*   `entity/README.md`: `entity` 子包的文档。
*   `mapper/README.md`: `mapper` 子包的文档。
*   `service/README.md`: `service` 子包的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.category` 模块的各个层次和组件协同工作，形成了一个内聚且职责清晰的商品分类管理系统：

1.  **接口层 (`controller`):**
    *   `CategoryController` 作为模块的对外入口，接收前端的 HTTP 请求，例如获取根分类、子分类、搜索分类等。
    *   它将请求数据绑定到 `dto` 中的 DTOs，并调用 `service` 层执行业务逻辑。
    *   控制器使用 Swagger/OpenAPI 注解为 API 文档提供清晰说明，并返回统一的 `Result` 对象（来自 `com.purchase.common.response` 包）。

2.  **数据传输对象 (`dto`):**
    *   `CategoryDTO` 是核心 DTO，用于封装分类的详细信息，包括其层级关系、状态、属性模板以及多语言支持。它还包含 `hasChildren` 和 `childrenCount` 等字段，支持前端的懒加载优化。

3.  **实体层 (`entity`):**
    *   `Category` 是领域实体，直接映射数据库表 `requirement_category`。它定义了分类的持久化结构，并与 MyBatis-Plus 集成，通过 `com.purchase.config.MyMetaObjectHandler` 实现自动填充时间戳等功能。

4.  **数据访问层 (`mapper`):**
    *   `CategoryMapper` 是 MyBatis Mapper 接口，继承自 MyBatis-Plus 的 `BaseMapper`，提供了对 `Category` 实体进行 CRUD 操作的能力。
    *   它还包含自定义 SQL 查询，支持复杂的层级查询（递归 CTE）、子分类统计和多种搜索策略（`LIKE` 模糊搜索和 MySQL 全文索引）。

5.  **服务层 (`service`):**
    *   `CategoryService` 接口定义了分类模块的业务契约。
    *   `service/impl.CategoryServiceImpl` 是其具体实现，承载了核心业务逻辑。它协调 `mapper` 进行数据库操作，执行数据转换（`Category` 实体到 `CategoryDTO`），并利用 Spring Cache (`@Cacheable`) 优化查询性能。
    *   服务层负责事务管理（尽管分类查询通常是只读事务），并返回统一的 `Result` 对象。

**整体协作流程总结:**

*   **请求入口:** 前端通过 `controller` 层的 API 发送请求。
*   **业务逻辑执行:** 控制器调用 `service` 层的方法。服务实现类（`CategoryServiceImpl`）首先检查缓存。如果缓存未命中，它会调用 `mapper` 层的方法从数据库获取 `Category` 实体。
*   **数据转换与填充:** `CategoryServiceImpl` 将获取到的 `Category` 实体转换为 `dto` 中的 `CategoryDTO`，并在此过程中计算和填充 `level`, `hasChildren`, `childrenCount`, `fullPath` 等非持久化字段。
*   **响应返回:** 服务层将 `CategoryDTO`（或其列表）返回给控制器，控制器将其封装在 `Result` 对象中，最终返回给前端。

这种模块化和分层设计使得 `com.purchase.category` 包成为一个高内聚、低耦合、易于理解和维护的商品分类管理系统。
