package com.purchase.category.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 分类实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("requirement_category")
public class Category {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("name")
    private String name;

    @TableField("name_en")
    private String nameEn;

    @TableField("description")
    private String description;

    @TableField("icon")
    private String icon;

    @TableField("sort_order")
    private Integer sortOrder;

    @TableField("parent_id")
    private Long parentId;

    @TableField("status")
    private Integer status;

    @TableField("attributes_json")
    private String attributesJson;

    @TableField("attributes_json_en")
    private String attributesJsonEn;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 计算分类级别
     */
    @TableField(exist = false)
    private Integer level;

    /**
     * 是否有子分类
     */
    @TableField(exist = false)
    private Boolean hasChildren;

    /**
     * 子分类数量
     */
    @TableField(exist = false)
    private Integer childrenCount;

    /**
     * 完整路径
     */
    @TableField(exist = false)
    private String fullPath;
} 