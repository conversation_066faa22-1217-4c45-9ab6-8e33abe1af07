# com.purchase.category.entity 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.category.entity` 包包含了商品分类模块的领域实体。在领域驱动设计（DDD）中，实体是具有唯一标识和生命周期的对象，它们封装了核心业务逻辑和状态。该包的核心是 `Category` 实体，它定义了商品分类的完整数据模型，包括其层级关系、状态、属性模板以及多语言支持。该实体与 MyBatis-Plus 框架集成，是分类管理功能的核心数据模型。

## 目录结构概览 (Directory Structure Overview)
*   `Category.java`: 商品分类实体类，对应数据库中的 `requirement_category` 表。
*   `Category.md`: `Category.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.category.entity` 包中的实体是领域模型的核心，它们与以下组件紧密协作：

1.  **MyBatis-Plus 框架:**
    *   `Category` 实体通过 `@TableName` 注解映射到数据库表 `requirement_category`。
    *   `@TableId` 配置主键的自动增长。
    *   `@TableField` 用于属性与列的映射，并支持 `fill` 属性（如 `FieldFill.INSERT`, `FieldFill.INSERT_UPDATE`）与 `com.purchase.config.MyMetaObjectHandler` 配合，实现 `createdAt` 和 `updatedAt` 字段的自动填充。
    *   `@TableField(exist = false)` 标记了 `level`, `hasChildren`, `childrenCount`, `fullPath` 等字段不映射到数据库，这些字段的值通常在业务逻辑层进行计算或填充，以满足前端 DTO 的需求。

2.  **数据访问层 (`com.purchase.category.mapper`):**
    *   `Category` 实体通过 `CategoryMapper` 接口进行持久化。Mapper 负责将实体从内存映射到数据库，并在从数据库读取数据后将其转换回实体。

3.  **服务层 (`com.purchase.category.service`):**
    *   服务层（如 `CategoryServiceImpl`）是 `Category` 实体的直接操作者。它会从 Mapper 加载 `Category` 实体，执行业务逻辑（如计算分类级别、判断是否有子分类），然后将实体转换为 DTO 返回给控制器。

4.  **数据传输对象 (`com.purchase.category.dto`):**
    *   `Category` 实体通常不会直接暴露给前端。在数据传输时，它会被转换为 `CategoryDTO`。`CategoryDTO` 包含了 `Category` 实体的大部分信息，并额外包含了 `level`, `hasChildren`, `childrenCount`, `fullPath` 等计算字段，以提供更丰富的前端视图。

**协作流程总结:**

*   **数据创建/更新:** 当服务层需要创建或更新分类时，它会构建或修改 `Category` 实体。这些实体通过 `CategoryMapper` 被持久化到数据库。在持久化过程中，`MyMetaObjectHandler` 会自动填充时间戳。
*   **数据查询:** 当服务层需要查询分类时，它会通过 `CategoryMapper` 从数据库加载 `Category` 实体。然后，服务层会根据业务需求计算 `level`, `hasChildren`, `childrenCount`, `fullPath` 等字段，并将 `Category` 实体转换为 `CategoryDTO` 返回给控制器。

这种实体设计确保了业务数据模型的清晰定义，并与持久化框架和业务逻辑层进行了良好的集成。
