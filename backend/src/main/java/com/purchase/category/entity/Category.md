# Category.md

## 1. 文件概述

`Category` 是分类模块中的一个实体（Entity），位于 `com.purchase.category.entity` 包中。它直接映射到数据库的 `requirement_category` 表，用于持久化存储商品或需求的分类信息。这个实体包含了分类的名称（中英文）、描述、图标、排序、父分类ID、状态以及扩展属性等。它是构建分类树结构和管理商品/需求分类的基础数据模型，通过MyBatis-Plus的注解实现了与数据库表的自动化ORM映射。

## 2. 核心功能

*   **数据持久化**: 定义了分类在数据库中的存储结构，是分类领域模型在持久化层的具体表现。
*   **多语言支持**: 提供了 `name`, `nameEn`, `attributesJson`, `attributesJsonEn` 字段，支持分类名称和属性的多语言存储。
*   **层级结构**: 通过 `parentId` 字段支持无限层级的分类树结构，便于组织和管理复杂的分类体系。
*   **状态管理**: 包含了 `status` 字段，用于控制分类的启用/禁用状态。
*   **MyBatis-Plus集成**: 通过 `@TableName`, `@TableId`, `@TableField`, `@Builder`, `@NoArgsConstructor`, `@AllArgsConstructor` 等注解与MyBatis-Plus框架深度集成，实现了主键自增长、字段映射和自动填充时间戳等功能，极大地简化了数据库操作。
*   **扩展属性**: `attributesJson` 和 `attributesJsonEn` 字段允许存储JSON格式的额外属性，增加了分类的灵活性和可扩展性。
*   **非持久化字段**: 包含了 `level`, `hasChildren`, `childrenCount`, `fullPath` 等 `@TableField(exist = false)` 标记的字段，这些字段不在数据库中存储，而是通过业务逻辑计算或在查询时动态填充，用于前端展示或业务逻辑判断。

## 3. 属性说明

- **`id` (Long)**: 分类的主键ID，自增长。
- **`name` (String)**: 分类名称（中文）。
- **`nameEn` (String)**: 分类名称（英文）。
- **`description` (String)**: 分类描述。
- **`icon` (String)**: 分类图标的URL或标识。
- **`sortOrder` (Integer)**: 排序字段，用于控制分类的显示顺序。
- **`parentId` (Long)**: 父分类的ID。根分类的 `parentId` 通常为 `null` 或 `0`。
- **`status` (Integer)**: 分类状态（例如：`0`-禁用，`1`-启用）。
- **`attributesJson` (String)**: 分类特有属性的JSON字符串（中文）。
- **`attributesJsonEn` (String)**: 分类特有属性的JSON字符串（英文）。
- **`createdAt` (LocalDateTime)**: 记录创建时间，插入时自动填充。
- **`updatedAt` (LocalDateTime)**: 记录更新时间，插入和更新时自动填充。

### 3.1 非持久化属性

- **`level` (Integer)**: 分类层级（例如：根分类为1级，其子分类为2级）。
- **`hasChildren` (Boolean)**: 是否有子分类。
- **`childrenCount` (Integer)**: 子分类数量。
- **`fullPath` (String)**: 从根分类到当前分类的完整路径（例如：`电子产品 > 手机 > 智能手机`）。

## 4. 业务规则

*   **层级结构**: `parentId` 字段是构建分类树的关键。在业务逻辑中，需要确保分类层级关系的正确性，避免循环引用。
*   **唯一性**: 分类名称（`name` 或 `nameEn`）在同一层级下通常应保持唯一。
*   **状态管理**: `status` 字段控制分类的可用性。只有启用状态的分类才能被用户选择或展示。
*   **多语言**: `name`, `nameEn`, `attributesJson`, `attributesJsonEn` 的存在表明系统支持多语言。在前端展示时，应根据用户语言偏好选择合适的字段。
*   **扩展属性**: `attributesJson` 字段允许灵活地为不同分类定义不同的属性，例如“手机”分类可以有“屏幕尺寸”、“操作系统”等属性，而“服装”分类可以有“材质”、“尺码”等属性。

## 5. 使用示例

```java
// 1. 在 CategoryMapper 接口中定义对 Category 的操作
@Mapper
public interface CategoryMapper extends BaseMapper<Category> {
    // 继承 BaseMapper 提供了基本的 CRUD 方法
    // 也可以在此定义自定义查询方法，例如：
    @Select("SELECT * FROM requirement_category WHERE parent_id IS NULL OR parent_id = 0 AND status = 1 ORDER BY sort_order ASC")
    List<Category> selectRootCategories();

    @Select("SELECT * FROM requirement_category WHERE parent_id = #{parentId} AND status = 1 ORDER BY sort_order ASC")
    List<Category> selectChildrenCategories(@Param("parentId") Long parentId);

    @Select("SELECT COUNT(*) FROM requirement_category WHERE parent_id = #{categoryId} AND status = 1")
    int countChildren(@Param("categoryId") Long categoryId);
}

// 2. 在 CategoryService 实现中创建和查询 Category
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category>
        implements CategoryService {
    @Autowired
    private CategoryMapper categoryMapper;

    @Transactional
    public CategoryDTO createCategory(CategoryDTO request) {
        Category category = new Category();
        BeanUtils.copyProperties(request, category);
        category.setStatus(1); // 默认启用
        category.setCreatedAt(LocalDateTime.now());
        category.setUpdatedAt(LocalDateTime.now());

        categoryMapper.insert(category);
        return convertToDTO(category);
    }

    public List<CategoryDTO> getRootCategories() {
        List<Category> categories = categoryMapper.selectRootCategories();
        return categories.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    public List<CategoryDTO> getCategoryPath(Long categoryId) {
        List<CategoryDTO> path = new ArrayList<>();
        Category current = categoryMapper.selectById(categoryId);
        while (current != null) {
            path.add(0, convertToDTO(current)); // 插入到列表开头
            if (current.getParentId() == null || current.getParentId() == 0) {
                break;
            }
            current = categoryMapper.selectById(current.getParentId());
        }
        return path;
    }

    // 辅助方法：实体转换为DTO
    private CategoryDTO convertToDTO(Category entity) {
        CategoryDTO dto = new CategoryDTO();
        BeanUtils.copyProperties(entity, dto);
        // 填充非持久化字段
        dto.setHasChildren(categoryMapper.countChildren(entity.getId()) > 0);
        // level 和 fullPath 可以在服务层或前端计算
        return dto;
    }
}

// 3. 测试示例
@SpringBootTest
class CategoryTest {
    @Autowired
    private CategoryMapper categoryMapper;

    @Test
    @Transactional
    void testInsertAndRetrieve() {
        Category category = Category.builder()
            .name("测试分类")
            .nameEn("Test Category")
            .parentId(0L)
            .status(1)
            .sortOrder(10)
            .build();

        int result = categoryMapper.insert(category);
        assertThat(result).isEqualTo(1);
        assertThat(category.getId()).isNotNull();

        Category retrievedCategory = categoryMapper.selectById(category.getId());
        assertThat(retrievedCategory).isNotNull();
        assertThat(retrievedCategory.getName()).isEqualTo("测试分类");
        assertThat(retrievedCategory.getParentId()).isEqualTo(0L);
    }

    @Test
    @Transactional
    void testSelectRootCategories() {
        // 插入一些根分类
        categoryMapper.insert(Category.builder().name("Root1").parentId(0L).status(1).build());
        categoryMapper.insert(Category.builder().name("Root2").parentId(0L).status(1).build());

        List<Category> rootCategories = categoryMapper.selectRootCategories();
        assertThat(rootCategories).anyMatch(c -> c.getName().equals("Root1"));
        assertThat(rootCategories).anyMatch(c -> c.getName().equals("Root2"));
    }
}
```

## 6. 注意事项

*   **领域实体**: `Category` 是一个典型的领域实体，它包含了数据和行为，并且其行为（如层级关系）直接反映了业务概念。
*   **MyBatis-Plus集成**: 充分利用MyBatis-Plus的注解和功能，可以大大简化数据访问层的开发。
*   **Lombok注解**: `@Data`, `@Builder`, `@NoArgsConstructor`, `@AllArgsConstructor` 极大地简化了实体类的编写，减少了样板代码。
*   **层级结构**: `parentId` 字段是构建分类树的关键。在业务逻辑中，需要确保分类层级关系的正确性，避免循环引用。
*   **多语言**: `name`, `nameEn`, `attributesJson`, `attributesJsonEn` 的存在表明系统支持多语言。在前端展示时，应根据用户语言偏好选择合适的字段。
*   **扩展属性**: `attributesJson` 字段允许灵活地为不同分类定义不同的属性，例如“手机”分类可以有“屏幕尺寸”、“操作系统”等属性，而“服装”分类可以有“材质”、“尺码”等属性。
*   **非持久化字段**: `@TableField(exist = false)` 标记的字段（`level`, `hasChildren`, `childrenCount`, `fullPath`）不在数据库中存储，而是通过业务逻辑计算或在查询时动态填充。这是一种将展示逻辑与持久化模型分离的良好实践。
*   **状态管理**: `status` 字段控制分类的可用性。只有启用状态的分类才能被用户选择或展示。
*   **唯一性**: 分类名称（`name` 或 `nameEn`）在同一层级下通常应保持唯一，在服务层应进行校验。
