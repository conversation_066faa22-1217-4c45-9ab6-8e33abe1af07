# com.purchase.category.service.impl 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.category.service.impl` 包包含了商品分类模块业务逻辑服务接口的具体实现。这些实现类是业务逻辑的核心，负责协调数据访问层（Mapper）和通用工具类，以完成商品分类的各种查询功能，如获取根分类、子分类、检查是否有子分类、获取分类路径、搜索分类以及获取分类详情。该包通过实现 `CategoryService` 接口，提供了具体的业务流程编排和数据操作逻辑，并利用 Spring Cache 提高了查询性能。

## 目录结构概览 (Directory Structure Overview)
*   `CategoryServiceImpl.java`: `CategoryService` 接口的实现类，包含了商品分类所有查询业务用例的具体逻辑。
*   `CategoryServiceImpl.md`: `CategoryServiceImpl.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.category.service.impl` 包中的服务实现类是应用程序核心业务能力的体现。它们与以下组件紧密协作：

1.  **服务接口 (`com.purchase.category.service.CategoryService`):**
    *   `CategoryServiceImpl` 实现了 `CategoryService` 接口，提供了所有定义业务用例的具体实现。

2.  **数据访问层 (`com.purchase.category.mapper.CategoryMapper`):**
    *   `CategoryServiceImpl` 通过 `@Autowired` 或构造函数注入 `CategoryMapper`。它通过调用 Mapper 方法来执行数据库的查询操作，例如 `selectRootCategories()`, `selectChildrenCategories()`, `hasChildren()`, `selectCategoryPath()`, `selectCategoryLevel()`, `searchCategories()`, `searchCategoriesWithFullText()`。

3.  **数据转换 (`com.purchase.category.dto.CategoryDTO`):**
    *   服务实现类负责将从 `CategoryMapper` 获取的 `Category` 实体转换为 `CategoryDTO`。在转换过程中，它会计算并填充 `CategoryDTO` 中的 `level`, `hasChildren`, `childrenCount`, `fullPath` 等非持久化字段，以满足前端展示的需求。

4.  **Spring Cache (`@Cacheable`):**
    *   `CategoryServiceImpl` 广泛使用了 Spring Cache 的 `@Cacheable` 注解。这使得对分类数据的频繁查询（如获取根分类、子分类、分类详情、分类路径）能够被缓存起来，显著提高了读取性能，减少了数据库负载。

5.  **搜索策略选择:**
    *   `searchCategories` 方法根据搜索关键词是否包含中文字符，动态地选择调用 `CategoryMapper` 中不同的搜索方法（`searchCategories` 使用 `LIKE` 模糊匹配，适用于中文；`searchCategoriesWithFullText` 使用 MySQL 全文索引，适用于英文和数字）。这是一种针对数据库特性进行的优化。

**协作流程总结:**

*   **控制器调用服务:** 控制器接收到前端请求后，调用 `CategoryService` 接口中相应的方法。
*   **缓存检查:** 在方法执行前，Spring Cache 会检查缓存中是否存在对应的结果。如果存在且未过期，则直接返回缓存结果，不执行方法体。
*   **业务逻辑执行:** 如果缓存未命中，`CategoryServiceImpl` 会执行业务逻辑。这包括：
    *   调用 `CategoryMapper` 进行数据库查询。
    *   根据查询结果，将 `Category` 实体转换为 `CategoryDTO`，并填充计算字段。
    *   处理分页和搜索逻辑。
*   **缓存更新:** 方法执行成功后，其结果会被放入缓存，供后续相同请求使用。
*   **返回结果:** 服务将业务处理结果（`CategoryDTO` 列表或单个 `CategoryDTO`）返回给控制器。

这种服务实现层设计确保了业务逻辑的集中、模块化和可测试性，并通过缓存和优化的搜索策略提高了应用程序的性能。
