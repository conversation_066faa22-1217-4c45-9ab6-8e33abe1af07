# CategoryServiceImpl.md

## 1. 文件概述

`CategoryServiceImpl.java` 是分类模块中 `CategoryService` 接口的实现类，位于 `com.purchase.category.service.impl` 包中。它实现了对商品或需求分类进行查询和管理的核心业务逻辑。该服务通过与 `CategoryMapper` 交互来访问数据库，并利用Spring Cache进行数据缓存以提高性能。它还包含了复杂的搜索逻辑，能够根据关键词是否包含中文来动态选择不同的SQL查询策略（LIKE或全文索引）。

## 2. 核心功能

*   **分类层级查询**: 实现了获取根分类、子分类以及分类路径的逻辑，支持前端的懒加载和树形结构展示。
*   **子分类判断**: 实现了判断分类是否包含子分类的功能。
*   **分类详情获取**: 实现了根据ID获取分类详细信息，并补充了层级和子分类数量等非持久化字段。
*   **批量获取分类**: 实现了根据ID列表批量获取分类信息的功能。
*   **智能搜索**: 提供了根据关键词搜索分类的实现，能够根据关键词是否包含中文智能选择SQL的 `LIKE` 操作或MySQL的 `FULLTEXT` 索引进行优化搜索。
*   **缓存机制**: 利用Spring Cache对分类查询结果进行缓存，显著提升了查询性能。
*   **数据转换**: 负责将 `Category` 实体转换为 `CategoryDTO`，并填充额外信息。

## 3. 接口说明

### 3.1 分类查询实现

#### getRootCategories - 获取根分类列表
*   **方法签名**: `List<CategoryDTO> getRootCategories()`
*   **描述**: 获取所有顶级（一级）分类的列表。结果会被缓存。
*   **业务逻辑**: 调用 `categoryMapper.selectRootCategories()` 获取根分类实体列表，然后通过 `convertToDTO` 转换为DTO列表。使用了 `@Cacheable` 注解进行缓存。

#### getChildrenCategories - 获取指定父分类的直接子分类
*   **方法签名**: `List<CategoryDTO> getChildrenCategories(Long parentId)`
*   **描述**: 根据父分类ID获取其直接子分类的列表。结果会被缓存。
*   **业务逻辑**: 调用 `categoryMapper.selectChildrenCategories(parentId)` 获取子分类实体列表，然后转换为DTO列表。使用了 `@Cacheable` 注解进行缓存。

#### hasChildren - 检查是否有子分类
*   **方法签名**: `boolean hasChildren(Long categoryId)`
*   **描述**: 检查指定分类是否包含子分类。结果会被缓存。
*   **业务逻辑**: 调用 `categoryMapper.hasChildren(categoryId)` 获取结果。使用了 `@Cacheable` 注解进行缓存。

#### getCategoryPath - 获取分类路径
*   **方法签名**: `List<CategoryDTO> getCategoryPath(Long categoryId)`
*   **描述**: 获取从根分类到指定分类的完整路径。结果会被缓存。
*   **业务逻辑**: 调用 `categoryMapper.selectCategoryPath(categoryId)` 获取分类实体列表，然后反转列表顺序，并通过 `convertToDTO` 转换为DTO列表。使用了 `@Cacheable` 注解进行缓存。

#### getCategoryById - 根据ID获取分类详情
*   **方法签名**: `CategoryDTO getCategoryById(Long categoryId)`
*   **描述**: 根据分类ID获取分类的详细信息。结果会被缓存。
*   **业务逻辑**: 调用 `categoryMapper.selectById(categoryId)` 获取分类实体。如果找到，则补充 `hasChildren` 和 `level` 等额外信息，然后转换为DTO。使用了 `@Cacheable` 注解进行缓存。

#### getCategoriesByIds - 批量获取分类信息
*   **方法签名**: `List<CategoryDTO> getCategoriesByIds(List<Long> categoryIds)`
*   **描述**: 根据分类ID列表批量获取分类信息。
*   **业务逻辑**: 构建 `LambdaQueryWrapper`，使用 `in` 条件查询指定ID列表的分类，并按 `sort_order` 排序。然后转换为DTO列表。

#### searchCategories - 搜索分类
*   **方法签名**: `List<CategoryDTO> searchCategories(String keyword, Integer page, Integer size)`
*   **描述**: 根据关键词搜索分类。根据关键词是否包含中文，动态选择使用SQL的 `LIKE` 操作或MySQL的 `FULLTEXT` 索引进行搜索。
*   **业务逻辑**: 
    1.  判断关键词是否为空。
    2.  计算分页偏移量 `offset`。
    3.  调用 `containsChinese` 判断关键词是否包含中文。
    4.  如果包含中文，调用 `categoryMapper.searchCategories` (使用LIKE)。
    5.  如果不包含中文，调用 `prepareExactKeyword`, `prepareWildcardKeyword` 准备关键词变体，然后调用 `categoryMapper.searchCategoriesWithFullText` (使用FULLTEXT索引)。
    6.  将查询结果转换为 `CategoryDTO` 列表。

### 3.2 辅助方法

#### convertToDTO - 实体转DTO
*   **方法签名**: `private CategoryDTO convertToDTO(Category category)`
*   **描述**: 将 `Category` 实体对象转换为 `CategoryDTO` 数据传输对象，并填充 `hasChildren` 和 `level` 等非持久化字段。

#### containsChinese - 检查字符串是否包含中文字符
*   **方法签名**: `private boolean containsChinese(String s)`
*   **描述**: 使用正则表达式判断字符串是否包含中文字符。

#### prepareExactKeyword - 准备精确匹配的关键词
*   **方法签名**: `private String prepareExactKeyword(String keyword)`
*   **描述**: 为全文索引搜索准备精确匹配的关键词，为每个词添加 `+` 前缀。

#### prepareWildcardKeyword - 准备通配符匹配的关键词
*   **方法签名**: `private String prepareWildcardKeyword(String keyword)`
*   **描述**: 为全文索引搜索准备通配符匹配的关键词，为每个词添加 `+` 前缀和 `*` 后缀。

## 4. 业务规则

*   **DDD分层**: `CategoryServiceImpl` 明确属于应用服务层，其职责是协调领域对象和仓储，实现业务用例。它不直接处理HTTP请求或数据库操作，而是委托给Controller和Mapper。
*   **缓存策略**: 对分类的查询操作（`getRootCategories`, `getChildrenCategories`, `hasChildren`, `getCategoryPath`, `getCategoryById`）都使用了Spring Cache进行缓存。缓存的key设计考虑了参数，`unless = "#result.isEmpty()"` 避免缓存空结果。
*   **搜索优化**: `searchCategories` 方法根据关键词的特性（是否包含中文）动态选择不同的底层Mapper方法，以充分利用数据库的索引能力，提高搜索效率。
*   **数据转换**: 服务层负责将从Mapper获取的 `Category` 实体转换为前端所需的 `CategoryDTO`，并填充计算属性（如 `hasChildren`, `level`）。
*   **异常处理**: 服务在遇到无效参数或数据不存在时，会返回 `null` 或空列表，由Controller层进行统一的错误处理。
*   **可扩展性**: 通过添加新的辅助方法和Mapper查询，可以方便地扩展分类的搜索和查询功能。

## 5. 使用示例

```java
// 1. 在 CategoryController 中调用 CategoryService
@RestController
@RequestMapping("/api/v1/categories")
public class CategoryController {
    @Autowired
    private CategoryService categoryService;

    @GetMapping("/root")
    public Result<List<CategoryDTO>> getRootCategories() {
        List<CategoryDTO> categories = categoryService.getRootCategories();
        return Result.success(categories);
    }

    @GetMapping("/search")
    public Result<List<CategoryDTO>> searchCategories(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        List<CategoryDTO> categories = categoryService.searchCategories(keyword, page, size);
        return Result.success(categories);
    }
}

// 2. 测试示例
@SpringBootTest
class CategoryServiceImplTest {
    @Autowired
    private CategoryService categoryService;

    @MockBean
    private CategoryMapper categoryMapper;

    @Test
    void testGetRootCategories_Cached() {
        Category cat1 = new Category(); cat1.setId(1L); cat1.setName("电子产品"); cat1.setParentId(0L); cat1.setStatus(1); cat1.setChildrenCount(0);
        when(categoryMapper.selectRootCategories()).thenReturn(List.of(cat1));
        when(categoryMapper.hasChildren(anyLong())).thenReturn(false);
        when(categoryMapper.selectCategoryLevel(anyLong())).thenReturn(1);

        // 第一次调用，应该会调用mapper
        List<CategoryDTO> rootCategories1 = categoryService.getRootCategories();
        assertThat(rootCategories1).hasSize(1);
        verify(categoryMapper, times(1)).selectRootCategories();

        // 第二次调用，应该从缓存中获取，不会调用mapper
        List<CategoryDTO> rootCategories2 = categoryService.getRootCategories();
        assertThat(rootCategories2).hasSize(1);
        verify(categoryMapper, times(1)).selectRootCategories(); // 仍然是1次
    }

    @Test
    void testSearchCategories_ChineseKeyword() {
        String keyword = "办公";
        Category cat1 = new Category(); cat1.setId(1L); cat1.setName("办公桌"); cat1.setStatus(1); cat1.setChildrenCount(0);
        when(categoryMapper.searchCategories(eq(keyword), anyInt(), anyInt())).thenReturn(List.of(cat1));
        when(categoryMapper.hasChildren(anyLong())).thenReturn(false);
        when(categoryMapper.selectCategoryLevel(anyLong())).thenReturn(1);

        List<CategoryDTO> results = categoryService.searchCategories(keyword, 1, 10);
        assertThat(results).hasSize(1);
        assertThat(results.get(0).getName()).isEqualTo("办公桌");
        verify(categoryMapper, times(1)).searchCategories(eq(keyword), anyInt(), anyInt());
        verify(categoryMapper, never()).searchCategoriesWithFullText(anyString(), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    void testSearchCategories_EnglishKeyword() {
        String keyword = "office";
        Category cat1 = new Category(); cat1.setId(1L); cat1.setNameEn("Office Desk"); cat1.setStatus(1); cat1.setChildrenCount(0);
        when(categoryMapper.searchCategoriesWithFullText(anyString(), anyString(), anyString(), anyInt(), anyInt())).thenReturn(List.of(cat1));
        when(categoryMapper.hasChildren(anyLong())).thenReturn(false);
        when(categoryMapper.selectCategoryLevel(anyLong())).thenReturn(1);

        List<CategoryDTO> results = categoryService.searchCategories(keyword, 1, 10);
        assertThat(results).hasSize(1);
        assertThat(results.get(0).getNameEn()).isEqualTo("Office Desk");
        verify(categoryMapper, never()).searchCategories(anyString(), anyInt(), anyInt());
        verify(categoryMapper, times(1)).searchCategoriesWithFullText(anyString(), anyString(), anyString(), anyInt(), anyInt());
    }
}
```

## 6. 注意事项

*   **DDD分层**: `CategoryServiceImpl` 明确属于应用服务层，其职责是协调领域对象和仓储，实现业务用例。它不直接处理HTTP请求或数据库操作，而是委托给Controller和Mapper。
*   **缓存策略**: 对分类的查询操作（`getRootCategories`, `getChildrenCategories`, `hasChildren`, `getCategoryPath`, `getCategoryById`）都使用了Spring Cache进行缓存。缓存的key设计考虑了参数，`unless = "#result.isEmpty()"` 避免缓存空结果。
*   **搜索优化**: `searchCategories` 方法根据关键词的特性（是否包含中文）动态选择不同的底层Mapper方法，以充分利用数据库的索引能力，提高搜索效率。这体现了对性能的关注。
*   **数据转换**: 服务层负责将从Mapper获取的 `Category` 实体转换为前端所需的 `CategoryDTO`，并填充计算属性（如 `hasChildren`, `level`）。
*   **异常处理**: 服务在遇到无效参数或数据不存在时，会返回 `null` 或空列表，由Controller层进行统一的错误处理。
*   **可扩展性**: 通过添加新的辅助方法和Mapper查询，可以方便地扩展分类的搜索和查询功能。
*   **日志记录**: 服务中使用了 `log.info` 和 `log.debug` 记录关键操作和调试信息，这对于监控和问题排查非常重要。
*   **TODO注释**: 代码中明确指出了未来可以重构为使用Elasticsearch，这表明了对技术演进的规划。
