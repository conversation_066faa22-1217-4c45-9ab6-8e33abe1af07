# com.purchase.category.service 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.category.service` 包包含了商品分类模块的业务逻辑服务接口及其实现。这些服务定义了与商品分类相关的核心业务操作，如获取根分类、子分类、检查是否有子分类、获取分类路径、搜索分类以及获取分类详情等功能。该包旨在抽象分类业务逻辑的具体实现细节，使上层控制器能够以统一的方式调用分类功能，并确保业务流程的正确性和一致性。

## 目录结构概览 (Directory Structure Overview)
*   `impl/`: 包含服务接口的具体实现类，如 `CategoryServiceImpl.java`。
*   `CategoryService.java`: 商品分类服务接口，定义了分类相关的业务操作。
*   `impl/README.md`: `impl` 子包的文档。
*   `CategoryService.md`: `CategoryService.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.category.service` 包中的服务接口及其实现与控制器层（`com.purchase.category.controller`）和数据访问层（`com.purchase.category.mapper`）紧密协作，共同实现了商品分类的各项功能：

1.  **服务接口定义业务契约:**
    *   `CategoryService` 接口定义了分类模块对外提供的业务能力。它作为业务逻辑的抽象，不包含具体的实现细节。
    *   接口方法通常以 DTO（来自 `com.purchase.category.dto` 包）作为参数和返回值，确保了与外部接口的解耦。

2.  **服务实现类承载业务逻辑:** (`impl` 子包)
    *   `CategoryServiceImpl` 是 `CategoryService` 接口的具体实现。它通过 `@Service` 注解被 Spring 管理。
    *   在实现方法内部，它负责编排复杂的业务流程，包括：
        *   调用 `com.purchase.category.mapper.CategoryMapper` 进行数据库操作（查询分类、统计子分类数量、查询分类路径等）。
        *   执行数据转换，将从 Mapper 获取的 `Category` 实体转换为 `CategoryDTO`。在转换过程中，会计算并填充 `CategoryDTO` 中的 `level`, `hasChildren`, `childrenCount`, `fullPath` 等非持久化字段。
        *   处理搜索逻辑，根据关键词选择不同的 Mapper 查询方法（例如，`searchCategories` 或 `searchCategoriesWithFullText`）。
        *   管理事务，确保业务操作的原子性（尽管分类查询通常是只读事务）。
        *   处理业务异常，并返回统一的 `Result` 对象（来自 `com.purchase.common.response` 包）。

3.  **与数据访问层的协作:**
    *   服务实现类通过注入 `CategoryMapper` 接口的实例来与数据库进行交互。它们不直接操作数据库，而是通过 Mapper 接口来执行持久化操作。

**协作流程总结:**

*   **控制器调用服务:** 控制器接收到前端请求后，调用 `service` 包中相应的服务接口方法（例如 `CategoryController` 调用 `CategoryService.getRootCategories()`）。
*   **服务执行业务逻辑:** 服务实现类接收参数，执行复杂的业务逻辑。这可能包括：
    *   调用 Mapper 进行数据库查询。
    *   根据查询结果构建 `CategoryDTO`，并填充所有相关字段。
    *   处理分页和搜索逻辑。
*   **返回结果:** 服务将业务处理结果（`CategoryDTO` 列表或单个 `CategoryDTO`）返回给控制器。如果发生业务错误，则返回包含错误信息的 `Result`。

这种服务层设计确保了业务逻辑的集中、模块化和可测试性，是应用程序核心业务能力的体现。
