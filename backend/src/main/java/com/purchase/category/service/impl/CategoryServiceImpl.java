package com.purchase.category.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.purchase.category.dto.CategoryDTO;
import com.purchase.category.entity.Category;
import com.purchase.category.mapper.CategoryMapper;
import com.purchase.category.service.CategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 分类服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CategoryServiceImpl implements CategoryService {

    private final CategoryMapper categoryMapper;

    @Override
    @Cacheable(value = "categories", key = "'root'", unless = "#result.isEmpty()")
    public List<CategoryDTO> getRootCategories() {
        log.info("获取根分类列表");
        List<Category> categories = categoryMapper.selectRootCategories();
        return categories.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "categories", key = "'children:' + #parentId", unless = "#result.isEmpty()")
    public List<CategoryDTO> getChildrenCategories(Long parentId) {
        log.info("获取父分类ID: {} 的子分类", parentId);
        List<Category> categories = categoryMapper.selectChildrenCategories(parentId);
        return categories.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "categories", key = "'hasChildren:' + #categoryId")
    public boolean hasChildren(Long categoryId) {
        log.info("检查分类ID: {} 是否有子分类", categoryId);
        return categoryMapper.hasChildren(categoryId);
    }

    @Override
    @Cacheable(value = "categories", key = "'path:' + #categoryId", unless = "#result.isEmpty()")
    public List<CategoryDTO> getCategoryPath(Long categoryId) {
        log.info("获取分类ID: {} 的路径", categoryId);
        List<Category> categories = categoryMapper.selectCategoryPath(categoryId);
        return categories.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "categories", key = "'detail:' + #categoryId")
    public CategoryDTO getCategoryById(Long categoryId) {
        log.info("获取分类详情，ID: {}", categoryId);
        Category category = categoryMapper.selectById(categoryId);
        if (category == null) {
            return null;
        }
        
        // 补充额外信息
        category.setHasChildren(categoryMapper.hasChildren(categoryId));
        Integer level = categoryMapper.selectCategoryLevel(categoryId);
        category.setLevel(level);
        
        return convertToDTO(category);
    }

    @Override
    public List<CategoryDTO> getCategoriesByIds(List<Long> categoryIds) {
        log.info("批量获取分类信息，IDs: {}", categoryIds);
        
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Category::getId, categoryIds)
                   .eq(Category::getStatus, 1)
                   .orderByAsc(Category::getSortOrder, Category::getId);
        
        List<Category> categories = categoryMapper.selectList(queryWrapper);
        
        return categories.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 检查字符串是否包含中文字符
     *
     * @param s 要检查的字符串
     * @return 如果包含中文字符则返回 true，否则返回 false
     */
    private boolean containsChinese(String s) {
        if (s == null || s.isEmpty()) {
            return false;
        }
        // 正则表达式匹配中文字符的 Unicode 范围
        return Pattern.compile("[\u4e00-\u9fa5]").matcher(s).find();
    }

    /**
     * 准备精确匹配的关键词（使用+操作符要求所有词都必须存在）
     * 例如："office desk" -> "+office +desk"
     *
     * @param keyword 原始关键词
     * @return 格式化后的精确匹配关键词
     */
    private String prepareExactKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return keyword;
        }
        
        // 分割关键词并为每个词添加+前缀
        String[] words = keyword.trim().toLowerCase().split("\\s+");
        return String.join(" ", 
               java.util.Arrays.stream(words)
                   .filter(word -> !word.isEmpty())
                   .map(word -> "+" + word)
                   .toArray(String[]::new));
    }

    /**
     * 准备通配符匹配的关键词（使用*操作符支持前缀匹配）
     * 例如："office desk" -> "+office* +desk*"
     *
     * @param keyword 原始关键词
     * @return 格式化后的通配符匹配关键词
     */
    private String prepareWildcardKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return keyword;
        }
        
        // 分割关键词，为每个词添加+前缀和*后缀
        String[] words = keyword.trim().toLowerCase().split("\\s+");
        return String.join(" ", 
               java.util.Arrays.stream(words)
                   .filter(word -> !word.isEmpty())
                   .map(word -> "+" + word + "*")
                   .toArray(String[]::new));
    }

    /**
     * 实体转DTO
     */
    private CategoryDTO convertToDTO(Category category) {
        if (category == null) {
            return null;
        }
        
        return CategoryDTO.builder()
                .id(category.getId())
                .name(category.getName())
                .nameEn(category.getNameEn())
                .description(category.getDescription())
                .icon(category.getIcon())
                .sortOrder(category.getSortOrder())
                .parentId(category.getParentId())
                .level(category.getLevel())
                .hasChildren(category.getChildrenCount() != null && category.getChildrenCount() > 0)
                .childrenCount(category.getChildrenCount())
                .status(category.getStatus())
                .attributesJson(category.getAttributesJson())
                .attributesJsonEn(category.getAttributesJsonEn())
                .fullPath(category.getFullPath())
                .createdAt(category.getCreatedAt())
                .updatedAt(category.getUpdatedAt())
                .build();
    }

    /**
     * 根据关键词搜索分类。
     * <p>
     * TODO: 此处为临时的搜索方案，未来应使用 Elasticsearch 进行重构，以提供更强大、高效的搜索功能。
     * 当前实现会根据关键词是否包含中文来动态选择不同的查询策略：
     * - 如果包含中文：由于标准全文索引对中文分词不佳，回退到使用 SQL 的 LIKE 操作进行模糊匹配。
     * - 如果不包含中文：利用 MySQL 的全文索引 (MATCH...AGAINST) 进行搜索，以获得更好的性能。
     * </p>
     *
     * @param keyword 搜索关键词
     * @param page    当前页码
     * @param size    每页大小
     * @return 分类列表
     */
    @Override
    public List<CategoryDTO> searchCategories(String keyword, Integer page, Integer size) {
        log.info("搜索分类，关键词: {}, 页码: {}, 每页大小: {}", keyword, page, size);

        if (keyword == null || keyword.trim().isEmpty()) {
            return Collections.emptyList();
        }

        Integer offset = (page - 1) * size;
        List<Category> categories;
        String trimmedKeyword = keyword.trim();

        if (containsChinese(trimmedKeyword)) {
            log.info("关键词 '{}' 包含中文，使用 LIKE 搜索", trimmedKeyword);
            categories = categoryMapper.searchCategories(trimmedKeyword, offset, size);
        } else {
            log.info("关键词 '{}' 不包含中文，使用优化的 FULLTEXT 搜索", trimmedKeyword);
            
            // 准备不同的搜索关键词变体以提高匹配率
            String exactKeyword = prepareExactKeyword(trimmedKeyword);
            String wildcardKeyword = prepareWildcardKeyword(trimmedKeyword);
            String naturalKeyword = trimmedKeyword;
            
            log.debug("搜索关键词变体 - 精确: '{}', 通配符: '{}', 自然语言: '{}'", 
                     exactKeyword, wildcardKeyword, naturalKeyword);
            
            categories = categoryMapper.searchCategoriesWithFullText(
                exactKeyword, wildcardKeyword, naturalKeyword, offset, size);
        }

        return categories.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
} 