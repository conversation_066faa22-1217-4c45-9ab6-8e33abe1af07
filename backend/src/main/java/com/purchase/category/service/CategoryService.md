# CategoryService.md

## 1. 文件概述

`CategoryService` 是分类模块中的一个服务接口，位于 `com.purchase.category.service` 包中。它定义了对商品或需求分类进行查询和管理的核心业务操作。该接口旨在为 `CategoryController` 提供清晰、独立的业务逻辑，并通过 `CategoryDTO` 实现了内外模型的分离。它是构建分类树结构和管理商品/需求分类的基础业务逻辑层。

## 2. 核心功能

*   **层级查询**: 提供了 `getRootCategories`（获取所有根分类）和 `getChildrenCategories`（获取指定父分类的子分类）方法，支持分类树的懒加载和层级遍历。
*   **子分类检查**: 提供了 `hasChildren` 方法，用于快速判断某个分类是否包含子分类，辅助前端UI的展示。
*   **分类路径查询**: 提供了 `getCategoryPath` 方法，用于获取从根分类到指定分类的完整路径，便于面包屑导航或分类层级展示。
*   **分类搜索**: 提供了 `searchCategories` 方法，支持根据关键词模糊搜索分类，并支持分页。
*   **分类详情**: 提供了 `getCategoryById` 方法，用于根据分类ID获取单个分类的详细信息。
*   **批量获取**: 提供了 `getCategoriesByIds` 方法，用于根据ID列表批量获取分类信息。

## 3. 接口说明

### 3.1 分类查询方法

#### getRootCategories - 获取根分类列表
*   **方法签名**: `List<CategoryDTO> getRootCategories()`
*   **描述**: 获取所有顶级（一级）分类的列表。
*   **参数**: 无。
*   **返回值**: `List<CategoryDTO>` - 包含根分类列表的DTO。

#### getChildrenCategories - 获取指定父分类的直接子分类
*   **方法签名**: `List<CategoryDTO> getChildrenCategories(Long parentId)`
*   **描述**: 根据父分类ID获取其直接子分类的列表。
*   **参数**:
    *   `parentId` (Long): 父分类的ID。
*   **返回值**: `List<CategoryDTO>` - 包含子分类列表的DTO。

#### hasChildren - 检查是否有子分类
*   **方法签名**: `boolean hasChildren(Long categoryId)`
*   **描述**: 检查指定分类是否包含子分类。
*   **参数**:
    *   `categoryId` (Long): 分类ID。
*   **返回值**: `boolean` - 如果有子分类返回 `true`，否则返回 `false`。

#### getCategoryPath - 获取分类路径
*   **方法签名**: `List<CategoryDTO> getCategoryPath(Long categoryId)`
*   **描述**: 获取从根分类到指定分类的完整路径。
*   **参数**:
    *   `categoryId` (Long): 分类ID。
*   **返回值**: `List<CategoryDTO>` - 包含分类路径的DTO列表。

#### searchCategories - 搜索分类
*   **方法签名**: `List<CategoryDTO> searchCategories(String keyword, Integer page, Integer size)`
*   **描述**: 根据关键词搜索分类，并支持分页。
*   **参数**:
    *   `keyword` (String): 搜索关键词。
    *   `page` (Integer): 页码。
    *   `size` (Integer): 每页大小。
*   **返回值**: `List<CategoryDTO>` - 包含搜索结果分类列表的DTO。

#### getCategoryById - 根据ID获取分类详情
*   **方法签名**: `CategoryDTO getCategoryById(Long categoryId)`
*   **描述**: 根据分类ID获取分类的详细信息。
*   **参数**:
    *   `categoryId` (Long): 分类ID。
*   **返回值**: `CategoryDTO` - 包含分类详细信息的DTO。

#### getCategoriesByIds - 批量获取分类信息
*   **方法签名**: `List<CategoryDTO> getCategoriesByIds(List<Long> categoryIds)`
*   **描述**: 根据分类ID列表批量获取分类信息。
*   **参数**:
    *   `categoryIds` (List<Long>): 分类ID列表。
*   **返回值**: `List<CategoryDTO>` - 包含分类信息列表的DTO。

## 4. 业务规则

*   **层级结构**: 分类之间存在父子层级关系，服务实现需要确保分类层级关系的正确性，避免循环引用。
*   **数据一致性**: 分类数据的增删改操作应确保分类树结构的完整性和一致性。
*   **多语言**: 分类名称和属性支持多语言，服务实现需要处理多语言数据的存储和查询。
*   **懒加载**: 服务支持前端的懒加载模式，通过按需加载子分类数据来优化性能。

## 5. 使用示例

```java
// 1. 在 CategoryController 中调用 CategoryService
@RestController
@RequestMapping("/api/v1/categories")
public class CategoryController {
    @Autowired
    private CategoryService categoryService;

    @GetMapping("/root")
    public Result<List<CategoryDTO>> getRootCategories() {
        List<CategoryDTO> categories = categoryService.getRootCategories();
        return Result.success(categories);
    }

    @GetMapping("/children/{parentId}")
    public Result<List<CategoryDTO>> getChildrenCategories(@PathVariable Long parentId) {
        List<CategoryDTO> categories = categoryService.getChildrenCategories(parentId);
        return Result.success(categories);
    }

    @GetMapping("/path/{categoryId}")
    public Result<List<CategoryDTO>> getCategoryPath(@PathVariable Long categoryId) {
        List<CategoryDTO> path = categoryService.getCategoryPath(categoryId);
        return Result.success(path);
    }
}

// 2. 在其他服务中调用 CategoryService 获取分类信息
@Service
public class ProductService {
    @Autowired
    private CategoryService categoryService;

    public ProductDTO getProductDetail(Long productId) {
        // ... 获取产品信息 ...
        Long categoryId = getProductCategoryId(productId);
        CategoryDTO category = categoryService.getCategoryById(categoryId);
        // ... 将分类信息添加到产品DTO ...
        return productDTO;
    }
}

// 3. 测试示例
@SpringBootTest
class CategoryServiceTest {
    @Autowired
    private CategoryService categoryService;

    @MockBean
    private CategoryMapper categoryMapper;

    @Test
    void testGetRootCategories() {
        Category cat1 = new Category(); cat1.setId(1L); cat1.setName("电子产品"); cat1.setParentId(0L); cat1.setStatus(1);
        Category cat2 = new Category(); cat2.setId(2L); cat2.setName("服装"); cat2.setParentId(0L); cat2.setStatus(1);
        when(categoryMapper.selectRootCategories()).thenReturn(List.of(cat1, cat2));
        when(categoryMapper.countChildren(anyLong())).thenReturn(0); // 模拟没有子分类

        List<CategoryDTO> rootCategories = categoryService.getRootCategories();
        assertThat(rootCategories).hasSize(2);
        assertThat(rootCategories.get(0).getName()).isEqualTo("电子产品");
    }

    @Test
    void testGetCategoryPath() {
        Category root = new Category(); root.setId(1L); root.setName("Root"); root.setParentId(0L); root.setStatus(1);
        Category child1 = new Category(); child1.setId(2L); child1.setName("Child1"); child1.setParentId(1L); child1.setStatus(1);
        Category child2 = new Category(); child2.setId(3L); child2.setName("Child2"); child2.setParentId(2L); child2.setStatus(1);

        when(categoryMapper.selectById(1L)).thenReturn(root);
        when(categoryMapper.selectById(2L)).thenReturn(child1);
        when(categoryMapper.selectById(3L)).thenReturn(child2);
        when(categoryMapper.selectCategoryPath(3L)).thenReturn(List.of(child2, child1, root)); // 模拟Mapper返回的路径
        when(categoryMapper.countChildren(anyLong())).thenReturn(0);
        when(categoryMapper.selectCategoryLevel(anyLong())).thenReturn(1); // 简化模拟

        List<CategoryDTO> path = categoryService.getCategoryPath(3L);
        assertThat(path).hasSize(3);
        assertThat(path.get(0).getName()).isEqualTo("Root");
        assertThat(path.get(1).getName()).isEqualTo("Child1");
        assertThat(path.get(2).getName()).isEqualTo("Child2");
    }
}
```

## 6. 注意事项

*   **DDD分层**: `CategoryService` 明确属于领域服务层，其职责是封装业务逻辑，协调领域对象和仓储。它不直接处理HTTP请求或数据库操作，而是委托给Controller和Mapper。
*   **数据转换**: 服务层负责将从Mapper获取的 `Category` 实体转换为前端所需的 `CategoryDTO`。这通常涉及从其他服务（如 `CategoryMapper`）获取关联数据。
*   **缓存**: 分类数据通常不经常变动，可以考虑在服务层对分类查询结果进行缓存，以提高查询性能。
*   **异常处理**: 服务应定义并抛出具体的业务异常（如 `CategoryNotFoundException`），而不是直接抛出通用异常，以便上层调用者能够进行针对性的处理。
*   **性能优化**: 对于层级查询和搜索，服务层应确保底层Mapper的查询效率，例如使用合适的索引。
*   **可扩展性**: 如果未来需要增加新的分类属性或查询维度，可以在 `Category` 实体和 `CategoryDTO` 中添加相应属性，并在此服务中实现新的业务逻辑。
