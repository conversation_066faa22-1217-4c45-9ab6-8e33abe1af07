package com.purchase.category.service;

import com.purchase.category.dto.CategoryDTO;

import java.util.List;

/**
 * 分类服务接口
 */
public interface CategoryService {

    /**
     * 获取根分类列表（一级分类）
     */
    List<CategoryDTO> getRootCategories();

    /**
     * 获取指定父分类的直接子分类
     */
    List<CategoryDTO> getChildrenCategories(Long parentId);

    /**
     * 检查是否有子分类
     */
    boolean hasChildren(Long categoryId);

    /**
     * 获取分类路径（从根到指定分类）
     */
    List<CategoryDTO> getCategoryPath(Long categoryId);

    /**
     * 搜索分类
     */
    List<CategoryDTO> searchCategories(String keyword, Integer page, Integer size);

    /**
     * 根据ID获取分类详情
     */
    CategoryDTO getCategoryById(Long categoryId);

    /**
     * 批量获取分类信息
     */
    List<CategoryDTO> getCategoriesByIds(List<Long> categoryIds);
} 