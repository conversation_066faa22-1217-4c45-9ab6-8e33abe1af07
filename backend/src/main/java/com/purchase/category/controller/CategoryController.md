# CategoryController.md

## 1. 文件概述

`CategoryController.java` 是分类模块的控制器，位于 `com.purchase.category.controller` 包中。它作为分类功能的API入口，负责处理所有与商品分类数据相关的HTTP请求。该控制器通过依赖注入 `CategoryService` 来执行具体的业务逻辑，为前端应用提供了灵活的分类查询功能，包括获取根分类、子分类、分类路径、以及按关键词搜索分类。它特别支持分类数据的懒加载模式，以优化前端性能。

## 2. 核心功能

*   **根分类列表**: 提供 `/root` 接口，用于获取所有顶级（一级）分类，通常用于构建分类树的起始点。
*   **子分类列表**: 提供 `/children/{parentId}` 接口，支持根据父分类ID获取其直接子分类，实现了分类数据的懒加载，避免一次性加载所有分类数据。
*   **子分类检查**: 提供 `/has-children/{categoryId}` 接口，用于快速判断某个分类是否包含子分类，辅助前端UI的展示（例如，是否显示展开/折叠图标）。
*   **分类路径**: 提供 `/path/{categoryId}` 接口，用于获取从根分类到指定分类的完整路径，便于面包屑导航或分类层级展示。
*   **分类搜索**: 提供 `/search` 接口，支持根据关键词模糊搜索分类，并支持分页，方便用户快速查找所需分类。
*   **分类详情**: 提供 `/{categoryId}` 接口，用于根据分类ID获取单个分类的详细信息。
*   **统一响应**: 所有接口都返回一个标准化的 `Result` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **日志记录**: 对所有接收到的请求都记录了详细的日志，包括请求参数，便于系统监控和调试。

## 3. 接口说明

### 3.1 分类查询接口

#### getRootCategories - 获取根分类列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/categories/root`
*   **权限**: 无显式权限，通常为公开接口。
*   **参数**: 无。
*   **返回值**: `Result<List<CategoryDTO>>` - 包含根分类列表的响应对象。
*   **业务逻辑**: 调用 `categoryService.getRootCategories` 获取数据。

#### getChildrenCategories - 获取子分类列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/categories/children/{parentId}`
*   **权限**: 无显式权限，通常为公开接口。
*   **参数**:
    *   `parentId` (Long, path, required): 父分类的ID。
*   **返回值**: `Result<List<CategoryDTO>>` - 包含子分类列表的响应对象。
*   **业务逻辑**: 调用 `categoryService.getChildrenCategories` 获取数据。

#### hasChildren - 检查是否有子分类
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/categories/has-children/{categoryId}`
*   **权限**: 无显式权限，通常为公开接口。
*   **参数**:
    *   `categoryId` (Long, path, required): 分类ID。
*   **返回值**: `Result<Boolean>` - 如果有子分类返回 `true`，否则返回 `false`。
*   **业务逻辑**: 调用 `categoryService.hasChildren` 获取结果。

#### getCategoryPath - 获取分类路径
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/categories/path/{categoryId}`
*   **权限**: 无显式权限，通常为公开接口。
*   **参数**:
    *   `categoryId` (Long, path, required): 分类ID。
*   **返回值**: `Result<List<CategoryDTO>>` - 包含从根分类到指定分类的路径列表的响应对象。
*   **业务逻辑**: 调用 `categoryService.getCategoryPath` 获取数据。

#### searchCategories - 搜索分类
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/categories/search`
*   **权限**: 无显式权限，通常为公开接口。
*   **参数**:
    *   `keyword` (String, query, required): 搜索关键词。
    *   `page` (Integer, query, optional, default=1): 页码。
    *   `size` (Integer, query, optional, default=20): 每页大小。
*   **返回值**: `Result<List<CategoryDTO>>` - 包含搜索结果分类列表的响应对象。
*   **业务逻辑**: 调用 `categoryService.searchCategories` 获取数据。

#### getCategoryById - 获取分类详情
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/categories/{categoryId}`
*   **权限**: 无显式权限，通常为公开接口。
*   **参数**:
    *   `categoryId` (Long, path, required): 分类ID。
*   **返回值**: `Result<CategoryDTO>` - 包含分类详细信息的响应对象。
*   **业务逻辑**: 调用 `categoryService.getCategoryById` 获取数据。

## 4. 业务规则

*   **懒加载**: 分类数据通常以树形结构组织，控制器通过提供 `getRootCategories` 和 `getChildrenCategories` 接口支持前端的懒加载模式，避免一次性加载所有数据造成的性能问题。
*   **层级关系**: 分类之间存在父子层级关系，`getCategoryPath` 接口能够正确回溯并返回完整的路径。
*   **搜索逻辑**: 搜索功能应支持对分类名称、描述等字段的模糊匹配。
*   **数据一致性**: 分类数据的增删改操作应确保分类树结构的完整性和一致性。

## 5. 使用示例

```java
// 1. 前端 (Vue + Element UI Tree) 懒加载分类树
/*
<el-tree
  :props="props"
  :load="loadNode"
  lazy
  show-checkbox
  @check-change="handleCheckChange"
>
</el-tree>

// Vue methods
async loadNode(node, resolve) {
  if (node.level === 0) { // 根节点
    const response = await axios.get('/api/v1/categories/root');
    resolve(response.data.data.map(item => ({ ...item, leaf: !item.hasChildren })));
  } else { // 子节点
    const response = await axios.get(`/api/v1/categories/children/${node.data.id}`);
    resolve(response.data.data.map(item => ({ ...item, leaf: !item.hasChildren })));
  }
}
*/

// 2. Java后端服务间调用 (RestTemplate)
@Service
public class ProductCategoryClient {
    @Autowired
    private RestTemplate restTemplate;

    private final String BASE_URL = "http://localhost:8080/api/v1/categories";

    public List<CategoryDTO> getRootCategories() {
        ResponseEntity<Result> response = restTemplate.exchange(BASE_URL + "/root", HttpMethod.GET, null, Result.class);
        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null && response.getBody().isSuccess()) {
            // 需要手动将List<Map>转换为List<CategoryDTO>
            return (List<CategoryDTO>) response.getBody().getData();
        }
        throw new RuntimeException("Failed to fetch root categories");
    }
}

// 3. 测试示例 (MockMvc)
@SpringBootTest
@AutoConfigureMockMvc
class CategoryControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CategoryService categoryService;

    @Test
    void testGetRootCategories_Success() throws Exception {
        CategoryDTO cat1 = new CategoryDTO(); cat1.setId(1L); cat1.setName("电子产品");
        CategoryDTO cat2 = new CategoryDTO(); cat2.setId(2L); cat2.setName("服装");
        List<CategoryDTO> mockCategories = List.of(cat1, cat2);
        when(categoryService.getRootCategories()).thenReturn(mockCategories);

        mockMvc.perform(get("/api/v1/categories/root"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].name").value("电子产品"));
    }

    @Test
    void testGetChildrenCategories_Success() throws Exception {
        Long parentId = 1L;
        CategoryDTO child1 = new CategoryDTO(); child1.setId(11L); child1.setName("手机");
        List<CategoryDTO> mockChildren = List.of(child1);
        when(categoryService.getChildrenCategories(parentId)).thenReturn(mockChildren);

        mockMvc.perform(get("/api/v1/categories/children/{parentId}", parentId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].name").value("手机"));
    }
}
```

## 6. 注意事项

*   **权限控制**: 虽然当前接口没有显式使用 `@PreAuthorize`，但对于分类的增删改操作，通常需要管理员权限。查询接口可以根据业务需求设置为公开或需要认证。
*   **懒加载**: 控制器通过提供 `getRootCategories` 和 `getChildrenCategories` 接口支持前端的懒加载模式，这对于大型分类树的性能优化非常重要。
*   **统一响应**: 所有接口都返回 `Result` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **日志记录**: 控制器中使用了 `log.info` 记录请求参数，这对于监控和问题排查非常重要。
*   **职责分离**: 控制器仅负责请求的接收、参数的初步校验和转发，具体的业务逻辑和数据处理委托给 `CategoryService`。
*   **Swagger集成**: 使用 `@Tag`, `@Operation`, `@Parameter` 等Swagger注解，有助于自动生成和维护API文档，提高了API的可用性。
*   **缓存**: 分类数据通常不经常变动，可以考虑在服务层对分类查询结果进行缓存，以提高查询性能。
