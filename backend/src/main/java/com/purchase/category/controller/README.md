# com.purchase.category.controller 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.category.controller` 包包含了商品分类模块的 RESTful API 控制器。这些控制器作为应用程序的对外接口，负责接收来自前端的 HTTP 请求，调用应用服务层（`com.purchase.category.service`）执行业务逻辑，并最终将处理结果封装为统一的 API 响应返回给客户端。该包提供了商品分类的查询功能，支持多级分类的懒加载和搜索，旨在为前端提供灵活的分类数据访问能力。

## 目录结构概览 (Directory Structure Overview)
*   `CategoryController.java`: 商品分类的 RESTful API 控制器。
*   `CategoryController.md`: `CategoryController.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.category.controller` 包中的控制器与以下组件紧密协作，共同实现了商品分类的对外功能：

1.  **应用服务层 (`com.purchase.category.service`):**
    *   `CategoryController` 通过 `@Autowired` 或构造函数注入 `CategoryService` 的实例。它是控制器与后端业务逻辑之间的主要交互点。
    *   控制器将接收到的请求参数直接传递给 `CategoryService` 的方法，并接收服务层返回的 `CategoryDTO` 列表或单个 `CategoryDTO`。

2.  **数据传输对象 (DTOs):** (`com.purchase.category.dto`)
    *   控制器方法返回 `CategoryDTO` 列表或单个 `CategoryDTO`。这些 DTO 封装了分类的 ID、名称、父 ID 等信息，作为响应数据返回给前端。

3.  **统一响应格式:**
    *   所有控制器方法都返回 `com.purchase.common.response.Result<T>`。`Result` 提供了一个标准化的响应结构，包含 `code`、`message` 和 `data`，便于前端统一处理成功和失败的响应。

4.  **Swagger 文档:**
    *   控制器类和方法上使用了 Swagger/OpenAPI 注解（`@Tag`, `@Operation`, `@Parameter`），这些注解用于自动生成交互式的 API 文档，极大地便利了前后端开发人员的协作和 API 测试。

**协作流程总结:**

*   **请求进入:** 前端发送 HTTP 请求到 `CategoryController` 的某个 API 端点（例如，`/api/v1/categories/root`）。
*   **调用应用服务:** 控制器接收请求，并调用 `CategoryService` 中相应的方法（例如 `getRootCategories()`）。
*   **处理业务结果:** `CategoryService` 执行查询逻辑，从数据访问层获取分类数据，并将其转换为 `CategoryDTO` 列表。
*   **封装响应:** 控制器将 `CategoryDTO` 列表封装在 `Result` 对象中。
*   **响应返回:** 最终，一个统一格式的 HTTP 响应被返回给前端。

这种接口层设计确保了 API 的清晰性、可维护性，并为前端提供了友好且一致的交互体验，同时将业务逻辑与外部交互细节分离。
