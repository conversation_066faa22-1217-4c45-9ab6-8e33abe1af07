package com.purchase.category.controller;

import com.purchase.category.dto.CategoryDTO;
import com.purchase.category.service.CategoryService;
import com.purchase.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分类控制器 - 支持懒加载
 */
@Tag(name = "分类管理", description = "分类相关接口")
@RestController
@RequestMapping("/api/v1/categories")
@RequiredArgsConstructor
@Slf4j
public class CategoryController {

    private final CategoryService categoryService;

    @Operation(summary = "获取根分类列表", description = "获取所有一级分类")
    @GetMapping("/root")
    public Result<List<CategoryDTO>> getRootCategories() {
        log.info("获取根分类列表");
        List<CategoryDTO> categories = categoryService.getRootCategories();
        return Result.success(categories);
    }

    @Operation(summary = "获取子分类列表", description = "根据父分类ID获取直接子分类")
    @GetMapping("/children/{parentId}")
    public Result<List<CategoryDTO>> getChildrenCategories(
            @Parameter(description = "父分类ID") @PathVariable Long parentId) {
        log.info("获取分类ID: {} 的子分类", parentId);
        List<CategoryDTO> categories = categoryService.getChildrenCategories(parentId);
        return Result.success(categories);
    }

    @Operation(summary = "检查是否有子分类", description = "检查指定分类是否有子分类")
    @GetMapping("/has-children/{categoryId}")
    public Result<Boolean> hasChildren(
            @Parameter(description = "分类ID") @PathVariable Long categoryId) {
        log.info("检查分类ID: {} 是否有子分类", categoryId);
        boolean hasChildren = categoryService.hasChildren(categoryId);
        return Result.success(hasChildren);
    }

    @Operation(summary = "获取分类路径", description = "获取从根分类到指定分类的完整路径")
    @GetMapping("/path/{categoryId}")
    public Result<List<CategoryDTO>> getCategoryPath(
            @Parameter(description = "分类ID") @PathVariable Long categoryId) {
        log.info("获取分类ID: {} 的路径", categoryId);
        List<CategoryDTO> path = categoryService.getCategoryPath(categoryId);
        return Result.success(path);
    }

    @Operation(summary = "搜索分类", description = "根据关键词搜索分类")
    @GetMapping("/search")
    public Result<List<CategoryDTO>> searchCategories(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") Integer size) {
        log.info("搜索分类，关键词: {}, 页码: {}, 每页大小: {}", keyword, page, size);
        List<CategoryDTO> categories = categoryService.searchCategories(keyword, page, size);
        return Result.success(categories);
    }

    @Operation(summary = "获取分类详情", description = "根据ID获取分类详细信息")
    @GetMapping("/{categoryId}")
    public Result<CategoryDTO> getCategoryById(
            @Parameter(description = "分类ID") @PathVariable Long categoryId) {
        log.info("获取分类详情，ID: {}", categoryId);
        CategoryDTO category = categoryService.getCategoryById(categoryId);
        return Result.success(category);
    }
} 