package com.purchase.category.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 分类DTO - 懒加载优化版本
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分类信息")
public class CategoryDTO {

    @Schema(description = "分类ID")
    private Long id;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "分类英文名称")
    private String nameEn;

    @Schema(description = "分类描述")
    private String description;

    @Schema(description = "分类图标")
    private String icon;

    @Schema(description = "排序顺序")
    private Integer sortOrder;

    @Schema(description = "父分类ID")
    private Long parentId;

    @Schema(description = "分类级别（1-4级）")
    private Integer level;

    @Schema(description = "是否有子分类")
    private Boolean hasChildren;

    @Schema(description = "子分类数量")
    private Integer childrenCount;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "分类属性模板JSON")
    private String attributesJson;

    @Schema(description = "分类英文属性模板JSON")
    private String attributesJsonEn;

    @Schema(description = "完整路径（用于面包屑导航）")
    private String fullPath;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
} 