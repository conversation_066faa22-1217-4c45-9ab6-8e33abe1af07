# com.purchase.category.dto 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.category.dto` 包包含了商品分类模块的数据传输对象（DTO）。这些 DTO 旨在封装和标准化前端与后端应用服务之间传输的分类数据格式。该包的核心是 `CategoryDTO`，它提供了分类的详细信息，包括其层级关系、状态、属性模板以及多语言支持。通过使用这些 DTO，该包实现了数据传输的清晰性、类型安全性和验证机制，同时避免了将内部领域模型直接暴露给外部接口。

## 目录结构概览 (Directory Structure Overview)
*   `CategoryDTO.java`: 商品分类的详细数据传输对象，支持懒加载优化。
*   `CategoryDTO.md`: `CategoryDTO.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.category.dto` 包中的 DTOs 主要作为数据载体，在前端与后端应用服务层（`com.purchase.category.service`）之间传递数据。它们与 `com.purchase.category.service.CategoryService` 紧密协作，实现数据在不同层之间的映射和传输。

1.  **响应数据封装:**
    *   `CategoryDTO` 用于将商品分类的详细信息返回给前端。它包含了分类的 ID、名称（中英文）、描述、图标、排序顺序、父分类 ID、级别、是否有子分类、子分类数量、状态、属性模板（中英文 JSON）、完整路径以及创建和更新时间。
    *   这些字段的设计考虑了前端展示分类树、面包屑导航、搜索结果和分类详情页面的需求。

2.  **与服务层的协作:**
    *   `CategoryService` 的实现类（如 `CategoryServiceImpl`）负责从数据访问层获取 `Category` 实体，然后将其转换为 `CategoryDTO` 返回给控制器。
    *   在转换过程中，服务层会计算或查询 `hasChildren` 和 `childrenCount` 等字段，以支持前端的懒加载功能。

3.  **与 Swagger 文档的集成:**
    *   `CategoryDTO` 类和其字段上使用了 Swagger/OpenAPI 的 `@Schema` 注解，这使得在生成 API 文档时，能够为分类相关的 API 提供详细的请求和响应模型说明，包括字段描述、示例值和允许值，极大地提高了 API 文档的质量和可读性。

**协作流程总结:**

*   **前端请求:** 前端请求获取分类数据（例如，根分类、子分类、搜索结果）。
*   **控制器调用服务:** 控制器接收请求，并调用 `CategoryService` 中相应的方法。
*   **服务层处理与转换:** `CategoryService` 从数据库获取 `Category` 实体，然后将其转换为 `CategoryDTO`。在转换时，会填充所有相关字段，包括动态计算的 `hasChildren` 和 `childrenCount`。
*   **控制器返回响应:** 控制器将 `CategoryDTO`（或其列表）封装在 `com.purchase.common.response.Result` 对象中，并返回给前端。

这种 DTO 设计模式确保了数据在应用程序不同层级之间传输时的结构化和类型安全，同时为前端提供了丰富且易于消费的分类数据。
