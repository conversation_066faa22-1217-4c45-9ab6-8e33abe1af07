# CategoryDTO.java

## 文件概述 (File Overview)
`CategoryDTO.java` 是一个数据传输对象（DTO），用于封装和传输商品分类的详细信息。它旨在为前端提供一个全面的分类视图，支持多级分类结构和懒加载优化。该 DTO 包含了分类的 ID、名称（中英文）、描述、图标、排序顺序、父分类 ID、级别、是否有子分类、子分类数量、状态、属性模板（中英文 JSON）、完整路径以及创建和更新时间。它利用 Lombok 注解减少样板代码，并使用 Swagger 注解进行 API 文档说明。

## 核心功能 (Core Functionality)
*   **分类信息封装:** 聚合了分类的所有关键属性，包括基本信息、层级关系、状态和时间戳。
*   **多语言支持:** 包含 `nameEn`（英文名称）和 `attributesJsonEn`（英文属性模板 JSON），支持国际化。
*   **层级结构支持:** 包含 `parentId` 和 `level` 字段，用于表示分类的层级关系。
*   **懒加载优化:** 包含 `hasChildren` 和 `childrenCount` 字段，用于前端判断是否需要进一步加载子分类，支持懒加载树形结构。
*   **属性模板:** `attributesJson` 和 `attributesJsonEn` 字段用于存储分类的属性定义，通常是 JSON 格式。
*   **完整路径:** `fullPath` 字段用于表示分类的完整层级路径，便于面包屑导航。
*   **时间戳格式化:** `@JsonFormat` 注解确保 `LocalDateTime` 字段在 JSON 序列化时使用指定的日期时间格式。
*   **Swagger 文档支持:** 使用 `@Schema` 注解为 OpenAPI/Swagger 文档提供了详细的描述和示例值。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `Long id`: 分类 ID。
*   `String name`: 分类名称（中文）。
*   `String nameEn`: 分类英文名称。
*   `String description`: 分类描述。
*   `String icon`: 分类图标的 URL 或路径。
*   `Integer sortOrder`: 排序顺序。
*   `Long parentId`: 父分类 ID。如果为根分类，则为 `null`。
*   `Integer level`: 分类级别（1-4级）。
*   `Boolean hasChildren`: 是否有子分类。`true` 表示有，`false` 表示没有。
*   `Integer childrenCount`: 子分类数量。
*   `Integer status`: 状态：`0` 禁用，`1` 启用。
*   `String attributesJson`: 分类属性模板的 JSON 字符串。
*   `String attributesJsonEn`: 分类英文属性模板的 JSON 字符串。
*   `String fullPath`: 完整路径（例如 "电子产品/手机/智能手机"）。
*   `LocalDateTime createdAt`: 创建时间。`@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")`。
*   `LocalDateTime updatedAt`: 更新时间。`@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")`。

## 使用示例 (Usage Examples)

```java
// 在CategoryService中将Category实体转换为CategoryDTO
@Service
public class CategoryServiceImpl implements CategoryService {
    @Autowired
    private CategoryMapper categoryMapper;

    public List<CategoryDTO> getRootCategories() {
        List<Category> categories = categoryMapper.selectList(new QueryWrapper<Category>().isNull("parent_id").eq("deleted", "0").orderByAsc("sort_order"));
        return categories.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private CategoryDTO convertToDTO(Category category) {
        CategoryDTO dto = CategoryDTO.builder()
                .id(category.getId())
                .name(category.getName())
                .nameEn(category.getNameEn())
                .description(category.getDescription())
                .icon(category.getIcon())
                .sortOrder(category.getSortOrder())
                .parentId(category.getParentId())
                .level(category.getLevel())
                .status(category.getStatus())
                .attributesJson(category.getAttributesJson())
                .attributesJsonEn(category.getAttributesJsonEn())
                .fullPath(category.getFullPath())
                .createdAt(category.getCreatedAt())
                .updatedAt(category.getUpdatedAt())
                .build();
        
        // 填充hasChildren和childrenCount
        dto.setHasChildren(categoryMapper.hasChildren(category.getId()) > 0);
        dto.setChildrenCount(categoryMapper.countChildren(category.getId()));
        
        return dto;
    }
}

// 前端接收到的JSON响应示例
/*
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "name": "电子产品",
            "nameEn": "Electronics",
            "description": "各类电子产品",
            "icon": "/icons/electronics.png",
            "sortOrder": 1,
            "parentId": null,
            "level": 1,
            "hasChildren": true,
            "childrenCount": 3,
            "status": 1,
            "attributesJson": "{}",
            "attributesJsonEn": "{}",
            "fullPath": "电子产品",
            "createdAt": "2023-01-01 10:00:00",
            "updatedAt": "2023-01-01 10:00:00"
        }
    ]
}
*/
```

## 注意事项 (Notes)
*   **Lombok `@Data`, `@Builder`, `@NoArgsConstructor`, `@AllArgsConstructor`:** 该类使用了 Lombok 的多个注解，自动生成了样板代码，简化了 DTO 的创建和使用。
*   **懒加载优化:** `hasChildren` 和 `childrenCount` 字段的引入是为了支持前端的树形结构懒加载，避免一次性加载所有子分类数据，提高性能。这些字段的值通常需要在 Service 层进行计算或查询。
*   **多语言支持:** `nameEn` 和 `attributesJsonEn` 字段的存在表明系统支持多语言分类名称和属性。
*   **属性模板:** `attributesJson` 字段存储的是 JSON 字符串，这意味着前端需要解析这个字符串来获取分类的属性定义。
*   **时间格式化:** `@JsonFormat` 注解确保日期时间字段在 JSON 序列化时以可读的字符串格式输出，而不是默认的时间戳。
*   **字段完整性:** 尽管是 DTO，但它包含了分类的几乎所有核心信息，适用于需要完整分类详情的场景。