package com.purchase.file.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

/**
 * 文件服务接口
 * 负责文件上传、验证和管理
 */
public interface FileService {
    
    /**
     * 上传文件到阿里云OSS
     *
     * @param file 上传的文件
     * @return 包含上传结果的Map
     * @throws IOException 如果文件处理过程中发生IO异常
     */
    Map<String, Object> uploadFile(MultipartFile file) throws IOException;
    
    /**
     * 上传字节数组到阿里云OSS
     *
     * @param bytes 文件字节数据
     * @param fileName 文件名
     * @param contentType 内容类型，例如"application/pdf"
     * @return 文件URL
     */
    String uploadBytes(byte[] bytes, String fileName, String contentType);
    
    /**
     * 验证文件大小是否符合限制
     *
     * @param fileSize 文件大小(字节)
     * @param fileType 文件类型(image, video, document)
     * @return 是否符合大小限制
     */
    boolean validateFileSize(long fileSize, String fileType);
    
    /**
     * 获取文件类型的最大允许大小
     *
     * @param fileType 文件类型
     * @return 最大允许大小(字节)
     */
    long getMaxSizeForFileType(String fileType);
    
    /**
     * 格式化文件大小为易读格式
     *
     * @param bytes 文件大小(字节)
     * @return 格式化后的大小(如: 1.5 MB)
     */
    String formatFileSize(long bytes);
} 