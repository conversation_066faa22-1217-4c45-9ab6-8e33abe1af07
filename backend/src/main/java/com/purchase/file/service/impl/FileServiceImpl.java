package com.purchase.file.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.purchase.file.config.OssConfig;
import com.purchase.file.service.FileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileServiceImpl implements FileService {

    private final OssConfig ossConfig;

    @Value("${spring.servlet.multipart.max-file-size}")
    private String maxFileSize;

    // 定义各文件类型的大小限制（基于业务需求）
    private static final Map<String, String> FILE_TYPE_LIMITS = Map.of(
        "image", "100MB",    // 图片文件限制
        "video", "2GB",     // 视频文件限制
        "document", "200MB"  // 文档文件限制
    );

    private final Map<String, Long> sizeMap = new HashMap<>();

    @Override
    public Map<String, Object> uploadFile(MultipartFile file) throws IOException {
        initSizeLimits();
        
        String contentType = file.getContentType();
        String originalFileName = file.getOriginalFilename();
        long fileSize = file.getSize();

        if (originalFileName == null) {
            return createErrorResponse(400, "文件名为空");
        }

        String randomFileName;
        File tempFile;
        String fileType;

        String lowerFileName = originalFileName.toLowerCase();
        if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg")) {
            randomFileName = UUID.randomUUID().toString() + ".jpg";
            tempFile = File.createTempFile("temp", ".jpg");
            fileType = "image";
        } else if (lowerFileName.endsWith(".png")) {
            randomFileName = UUID.randomUUID().toString() + ".png";
            tempFile = File.createTempFile("temp", ".png");
            fileType = "image";
        } else if (lowerFileName.endsWith(".gif")) {
            randomFileName = UUID.randomUUID().toString() + ".gif";
            tempFile = File.createTempFile("temp", ".gif");
            fileType = "image";
        } else if (lowerFileName.endsWith(".webp")) {
            randomFileName = UUID.randomUUID().toString() + ".webp";
            tempFile = File.createTempFile("temp", ".webp");
            fileType = "image";
        } else if (lowerFileName.endsWith(".mp4")) {
            randomFileName = UUID.randomUUID().toString() + ".mp4";
            tempFile = File.createTempFile("temp", ".mp4");
            fileType = "video";
        } else if (lowerFileName.endsWith(".pdf") || lowerFileName.endsWith(".doc") || lowerFileName.endsWith(".docx")) {
            String extension = lowerFileName.substring(lowerFileName.lastIndexOf("."));
            randomFileName = UUID.randomUUID().toString() + extension;
            tempFile = File.createTempFile("temp", extension);
            fileType = "document";
        } else if (contentType != null && contentType.startsWith("application/")) {
            String extension = lowerFileName.substring(lowerFileName.lastIndexOf("."));
            randomFileName = UUID.randomUUID().toString() + extension;
            tempFile = File.createTempFile("temp", extension);
            fileType = "document";
        } else {
            return createErrorResponse(400, "文件类型不支持,请上传jpg, jpeg, png, gif, webp, mp4, pdf, doc或docx文件");
        }

        if (!validateFileSize(fileSize, fileType)) {
            return createErrorResponse(400, "文件大小超过限制：" + formatFileSize(getMaxSizeForFileType(fileType)));
        }

        file.transferTo(tempFile);
        
        try {
            String folderName = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String fullPath = folderName + "/" + randomFileName;
            
            // 获取OSS临时凭证
            AssumeRoleResponse.Credentials credentials = getOssTemporaryCredentials();
            
            if (credentials == null) {
                return createErrorResponse(500, "获取OSS临时凭证失败");
            }
            
            // 上传文件到OSS
            String ossUrl = uploadToOss(tempFile, fullPath, credentials);
            
            return Map.of(
                "code", 200,
                "message", "文件上传成功",
                "data", ossUrl
            );
            
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return createErrorResponse(500, "文件上传失败: " + e.getMessage());
        } finally {
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }
    
    @Override
    public String uploadBytes(byte[] bytes, String fileName, String contentType) {
        try {
            // 获取OSS临时凭证
            AssumeRoleResponse.Credentials credentials = getOssTemporaryCredentials();
            
            if (credentials == null) {
                throw new RuntimeException("获取OSS临时凭证失败");
            }
            
            String folderName = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String fullPath = folderName + "/" + fileName;
            
            // 使用OSS临时凭证创建OSS客户端
            OSS ossClient = new OSSClientBuilder().build(
                ossConfig.getEndpoint(),
                credentials.getAccessKeyId(),
                credentials.getAccessKeySecret(),
                credentials.getSecurityToken()
            );
            
            try {
                // 上传字节数组
                ossClient.putObject(
                    ossConfig.getBucketName(),
                    fullPath,
                    new ByteArrayInputStream(bytes)
                );
                
                // 返回访问URL
                return ossConfig.getEndpoint().replace("https://", "https://" + ossConfig.getBucketName() + ".") + "/" + fullPath;
            } finally {
                ossClient.shutdown();
            }
        } catch (Exception e) {
            log.error("上传字节数组失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean validateFileSize(long fileSize, String fileType) {
        initSizeLimits();
        Long maxSize = sizeMap.get(fileType);
        return maxSize == null || fileSize <= maxSize;
    }
    
    @Override
    public long getMaxSizeForFileType(String fileType) {
        initSizeLimits();
        return sizeMap.getOrDefault(fileType, 0L);
    }
    
    @Override
    public String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024L * 1024L * 1024L) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
        }
    }
    
    /**
     * 获取OSS临时访问凭证
     */
    private AssumeRoleResponse.Credentials getOssTemporaryCredentials() {
        try {
            String regionId = "";
            DefaultProfile.addEndpoint(regionId, "Sts", ossConfig.getStsEndpoint());
            
            IClientProfile profile = DefaultProfile.getProfile(
                regionId, 
                ossConfig.getAccessKeyId(), 
                ossConfig.getAccessKeySecret()
            );
            
            DefaultAcsClient client = new DefaultAcsClient(profile);
            
            final AssumeRoleRequest request = new AssumeRoleRequest();
            request.setSysMethod(MethodType.POST);
            request.setRoleArn(ossConfig.getRoleArn());
            request.setRoleSessionName(ossConfig.getRoleSessionName());
            request.setDurationSeconds(ossConfig.getDurationSeconds());
            
            final AssumeRoleResponse response = client.getAcsResponse(request);
            
            log.debug("获取OSS临时凭证成功, Expiration: {}", response.getCredentials().getExpiration());
            
            return response.getCredentials();
        } catch (ClientException e) {
            log.error("获取OSS临时凭证失败", e);
            return null;
        }
    }
    
    /**
     * 上传文件到OSS
     */
    private String uploadToOss(File file, String ossPath, AssumeRoleResponse.Credentials credentials) {
        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(
                ossConfig.getEndpoint(),
                credentials.getAccessKeyId(),
                credentials.getAccessKeySecret(),
                credentials.getSecurityToken()
            );
            
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                ossConfig.getBucketName(), 
                ossPath, 
                file
            );
            
            ossClient.putObject(putObjectRequest);
            
            return ossConfig.getEndpoint().replace("https://", "https://" + ossConfig.getBucketName() + ".") + "/" + ossPath;
        } catch (OSSException e) {
            log.error("OSS上传失败", e);
            throw e;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
    
    /**
     * 初始化文件大小限制
     * 使用预定义的文件类型限制，同时确保不超过Spring Boot的全局限制
     */
    private void initSizeLimits() {
        if (sizeMap.isEmpty()) {
            long globalMaxBytes = parseSizeToBytes(maxFileSize);

            FILE_TYPE_LIMITS.forEach((fileType, sizeLimit) -> {
                long typeLimitBytes = parseSizeToBytes(sizeLimit);
                // 确保文件类型限制不超过全局限制
                long actualLimit = Math.min(typeLimitBytes, globalMaxBytes);
                sizeMap.put(fileType, actualLimit);
            });

            log.info("文件大小限制初始化完成 - 全局限制: {}, 各类型限制: image={}, video={}, document={}",
                formatFileSize(globalMaxBytes),
                formatFileSize(sizeMap.get("image")),
                formatFileSize(sizeMap.get("video")),
                formatFileSize(sizeMap.get("document")));
        }
    }
    
    /**
     * 解析配置的文件大小限制
     */
    private long parseSizeToBytes(String size) {
        long bytes = 0;
        size = size.toUpperCase();
        if (size.endsWith("GB")) {
            bytes = Long.parseLong(size.substring(0, size.length() - 2)) * 1024L * 1024L * 1024L;
        } else if (size.endsWith("MB")) {
            bytes = Long.parseLong(size.substring(0, size.length() - 2)) * 1024L * 1024L;
        } else if (size.endsWith("KB")) {
            bytes = Long.parseLong(size.substring(0, size.length() - 2)) * 1024L;
        } else if (size.endsWith("B")) {
            bytes = Long.parseLong(size.substring(0, size.length() - 1));
        }
        return bytes;
    }
    
    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(int code, String message) {
        return Map.of(
            "code", code,
            "message", message
        );
    }
} 