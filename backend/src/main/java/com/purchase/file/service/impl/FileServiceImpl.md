# FileServiceImpl.java

## 文件概述 (File Overview)
`FileServiceImpl.java` 是 `FileService` 接口的具体实现类，基于阿里云OSS（Object Storage Service）提供文件管理功能。该实现类负责处理文件上传、字节数组上传、文件大小验证等核心业务逻辑，并与阿里云OSS服务进行集成。它使用STS（Security Token Service）临时凭证机制确保安全性，支持多种文件类型的上传和管理，为采购系统提供可靠的文件存储解决方案。

## 核心功能 (Core Functionality)
*   **文件上传:** 实现 `MultipartFile` 文件上传到阿里云OSS，包括文件类型验证、大小检查和路径生成。
*   **字节数组上传:** 支持直接上传字节数组数据到OSS，适用于程序生成的文件内容。
*   **文件验证:** 实现文件大小验证逻辑，根据不同文件类型（图片、视频、文档）应用不同的大小限制。
*   **STS凭证管理:** 获取和使用阿里云STS临时凭证，确保文件操作的安全性。
*   **配置管理:** 通过 `OssConfig` 管理OSS相关配置，支持不同环境的配置切换。
*   **错误处理:** 提供完善的错误处理机制，返回标准化的错误响应。

## 接口说明 (Interface Description)

### 主要方法 (Main Methods)
*   `Map<String, Object> uploadFile(MultipartFile file) throws IOException`:
    *   **功能:** 上传文件到阿里云OSS
    *   **参数:** `file` - Spring MVC的文件上传对象
    *   **返回值:** 包含上传结果的Map，包括状态码、消息和文件URL
    *   **业务逻辑:**
        1. 验证文件是否为空
        2. 检查文件类型是否支持
        3. 验证文件大小是否符合限制
        4. 生成唯一文件名和路径
        5. 获取OSS临时凭证
        6. 上传文件到OSS
        7. 返回文件访问URL

*   `String uploadBytes(byte[] bytes, String fileName, String contentType)`:
    *   **功能:** 上传字节数组到阿里云OSS
    *   **参数:** `bytes` - 文件字节数据，`fileName` - 文件名，`contentType` - MIME类型
    *   **返回值:** 文件访问URL
    *   **业务逻辑:** 直接将字节数组上传到OSS，适用于程序生成的文件

*   `boolean validateFileSize(long fileSize, String fileType)`:
    *   **功能:** 验证文件大小是否符合限制
    *   **参数:** `fileSize` - 文件大小（字节），`fileType` - 文件类型分类
    *   **返回值:** 是否符合大小限制
    *   **业务逻辑:** 根据文件类型检查大小限制

*   `long getMaxSizeForFileType(String fileType)`:
    *   **功能:** 获取指定文件类型的最大允许大小
    *   **参数:** `fileType` - 文件类型分类
    *   **返回值:** 最大允许大小（字节）

*   `String formatFileSize(long bytes)`:
    *   **功能:** 格式化文件大小为易读格式
    *   **参数:** `bytes` - 文件大小（字节）
    *   **返回值:** 格式化后的大小字符串（如 "1.5 MB"）

### 私有方法 (Private Methods)
*   `AssumeRoleResponse.Credentials getOssTemporaryCredentials()`: 获取OSS临时凭证
*   `String uploadToOss(File file, String objectKey, AssumeRoleResponse.Credentials credentials)`: 上传文件到OSS
*   `Map<String, Object> createErrorResponse(int code, String message)`: 创建错误响应
*   `void initSizeLimits()`: 初始化文件大小限制配置

## 使用示例 (Usage Examples)

```java
// 在Controller中使用FileServiceImpl
@RestController
public class FileController {
    @Autowired
    private FileService fileService; // 注入的是FileServiceImpl实例

    @PostMapping("/upload")
    public ResponseEntity<?> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            Map<String, Object> result = fileService.uploadFile(file);
            int statusCode = (int) result.getOrDefault("code", 500);
            return ResponseEntity.status(statusCode).body(result);
        } catch (IOException e) {
            return ResponseEntity.status(500).body(
                Map.of("code", 500, "message", "文件上传失败: " + e.getMessage())
            );
        }
    }
}

// 在Service中使用字节数组上传
@Service
public class ReportService {
    @Autowired
    private FileService fileService;

    public String generateAndUploadReport(ReportData data) {
        // 生成PDF报告
        byte[] pdfBytes = generatePdfReport(data);

        // 上传到OSS
        String fileName = "report_" + System.currentTimeMillis() + ".pdf";
        return fileService.uploadBytes(pdfBytes, fileName, "application/pdf");
    }

    private byte[] generatePdfReport(ReportData data) {
        // PDF生成逻辑
        return new byte[0]; // 示例
    }
}

// 文件大小验证示例
@Component
public class FileValidator {
    @Autowired
    private FileService fileService;

    public boolean validateUpload(MultipartFile file) {
        String fileType = determineFileType(file.getContentType());

        if (!fileService.validateFileSize(file.getSize(), fileType)) {
            long maxSize = fileService.getMaxSizeForFileType(fileType);
            String maxSizeStr = fileService.formatFileSize(maxSize);
            throw new IllegalArgumentException("文件大小超过限制，最大允许: " + maxSizeStr);
        }

        return true;
    }

    private String determineFileType(String contentType) {
        if (contentType.startsWith("image/")) return "image";
        if (contentType.startsWith("video/")) return "video";
        return "document";
    }
}
```

## 注意事项 (Notes)
*   **配置依赖:** 该实现类依赖 `OssConfig` 配置类，需要正确配置阿里云OSS的相关参数，包括endpoint、bucketName、accessKeyId、accessKeySecret等。
*   **STS凭证安全:** 使用STS临时凭证而非长期密钥，提高了安全性。凭证有时效性，需要定期刷新。
*   **文件类型限制:** 只支持预定义的安全文件类型，包括常见的图片、视频和文档格式，防止恶意文件上传。
*   **大小限制配置:** 不同文件类型有不同的大小限制，可以通过修改 `FILE_TYPE_LIMITS` 常量进行调整。
*   **异常处理:** 方法中的异常会被适当处理并转换为用户友好的错误信息，避免敏感信息泄露。
*   **临时文件管理:** 上传过程中会创建临时文件，需要确保在操作完成后正确清理，避免磁盘空间浪费。
*   **并发安全:** 该实现类是线程安全的，可以在多线程环境中安全使用。
*   **性能考虑:** 对于大文件上传，建议在前端实现分片上传，减少单次请求的负载。
*   **监控和日志:** 实现类使用 `@Slf4j` 记录关键操作日志，便于问题排查和性能监控。