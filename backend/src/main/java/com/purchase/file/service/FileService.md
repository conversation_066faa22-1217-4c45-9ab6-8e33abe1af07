# FileService.java

## 文件概述 (File Overview)
`FileService.java` 是文件服务的接口，定义了与文件管理相关的核心业务操作。它提供了文件上传、字节数组上传、文件大小验证、文件类型限制管理以及文件大小格式化等全面的文件处理功能。该接口旨在抽象文件业务逻辑的具体实现细节，使上层控制器能够以统一的方式调用文件管理功能，特别是与阿里云OSS对象存储服务的集成。

## 核心功能 (Core Functionality)
*   **文件上传:** 支持 MultipartFile 文件上传到阿里云OSS，返回上传结果信息。
*   **字节数组上传:** 支持直接上传字节数组数据到OSS，适用于程序生成的文件内容。
*   **文件验证:** 提供文件大小验证功能，根据文件类型检查是否符合大小限制。
*   **配置管理:** 提供获取不同文件类型最大允许大小的配置功能。
*   **工具功能:** 提供文件大小格式化功能，将字节数转换为易读的格式。

## 接口说明 (Interface Description)

### 方法 (Methods)
*   `Map<String, Object> uploadFile(MultipartFile file) throws IOException`: 上传文件到阿里云OSS，返回包含上传结果的Map。
*   `String uploadBytes(byte[] bytes, String fileName, String contentType)`: 上传字节数组到阿里云OSS，返回文件URL。
*   `boolean validateFileSize(long fileSize, String fileType)`: 验证文件大小是否符合指定文件类型的限制。
*   `long getMaxSizeForFileType(String fileType)`: 获取指定文件类型的最大允许大小（字节）。
*   `String formatFileSize(long bytes)`: 格式化文件大小为易读格式（如: 1.5 MB）。

### 参数说明 (Parameter Details)
*   `MultipartFile file`: Spring MVC 的文件上传对象，包含文件内容和元数据。
*   `byte[] bytes`: 文件的字节数组数据。
*   `String fileName`: 文件名，用于确定存储路径和文件标识。
*   `String contentType`: MIME 内容类型，如 "application/pdf", "image/jpeg" 等。
*   `String fileType`: 文件类型分类，如 "image", "video", "document"。
*   `long fileSize`: 文件大小，以字节为单位。

## 使用示例 (Usage Examples)

```java
// 在FileController中注入并调用FileService
@RestController
@RequestMapping("/api/v1/files")
public class FileController {
    @Autowired
    private FileService fileService;

    @PostMapping("/upload")
    public Result<Map<String, Object>> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件大小
            String fileType = determineFileType(file.getContentType());
            if (!fileService.validateFileSize(file.getSize(), fileType)) {
                long maxSize = fileService.getMaxSizeForFileType(fileType);
                String maxSizeStr = fileService.formatFileSize(maxSize);
                return Result.error("文件大小超过限制，最大允许: " + maxSizeStr);
            }

            // 上传文件
            Map<String, Object> result = fileService.uploadFile(file);
            return Result.success(result);
        } catch (IOException e) {
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload-bytes")
    public Result<String> uploadBytes(@RequestBody UploadBytesRequest request) {
        // 验证文件大小
        if (!fileService.validateFileSize(request.getBytes().length, request.getFileType())) {
            return Result.error("文件大小超过限制");
        }

        String fileUrl = fileService.uploadBytes(
            request.getBytes(),
            request.getFileName(),
            request.getContentType()
        );
        return Result.success(fileUrl);
    }
}

// 在其他服务中调用FileService
@Service
public class UserService {
    @Autowired
    private FileService fileService;

    public String uploadUserAvatar(MultipartFile avatarFile) throws IOException {
        // 验证头像文件大小
        if (!fileService.validateFileSize(avatarFile.getSize(), "image")) {
            throw new BusinessException("头像文件过大");
        }

        Map<String, Object> uploadResult = fileService.uploadFile(avatarFile);
        return (String) uploadResult.get("fileUrl");
    }

    public String generateAndUploadReport(ReportData data) {
        // 生成报告PDF
        byte[] pdfBytes = generatePdfReport(data);

        // 上传到OSS
        String fileName = "report_" + System.currentTimeMillis() + ".pdf";
        return fileService.uploadBytes(pdfBytes, fileName, "application/pdf");
    }
}
```

## 注意事项 (Notes)
*   **接口与实现分离:** 该接口定义了文件服务的契约，其具体实现（如 `FileServiceImpl`）将负责与阿里云OSS客户端集成，实现具体的文件操作逻辑。
*   **异常处理:** `uploadFile` 方法声明了 `IOException`，实现类应妥善处理文件IO异常、网络异常和OSS服务异常。
*   **文件类型分类:** `fileType` 参数使用分类而非具体的MIME类型，便于统一管理不同类型文件的大小限制策略。
*   **返回值设计:** `uploadFile` 返回 `Map<String, Object>` 而非简单的URL字符串，允许返回更丰富的上传结果信息（如文件大小、上传时间等）。
*   **字节数组上传:** `uploadBytes` 方法适用于程序生成的文件内容（如报告、图表等），避免了创建临时文件的开销。
*   **文件大小验证:** 实现类应根据业务需求配置不同文件类型的大小限制，并提供清晰的错误提示。
*   **安全考虑:** 实现类应验证文件类型、检查文件内容安全性，防止恶意文件上传。
*   **性能优化:** 对于大文件上传，实现类应考虑分片上传、进度跟踪等优化策略。