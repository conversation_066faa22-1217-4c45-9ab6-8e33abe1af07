package com.purchase.file.config;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * 阿里云OSS配置类
 */
@Data
@Component
public class OssConfig {
    
    /**
     * OSS区域地址
     */
    private String endpoint = "https://oss-cn-hongkong.aliyuncs.com";
    
    /**
     * OSS存储桶名称
     */
    private String bucketName = "purchase-william";
    
    /**
     * STS服务端点
     */
    private String stsEndpoint = "sts.cn-hongkong.aliyuncs.com";
    
    /**
     * 临时凭证有效期(秒)
     */
    private Long durationSeconds = 3600L;
    
    /**
     * 角色会话名称
     */
    private String roleSessionName = "SessionTest";
    
    /**
     * 从环境变量获取AccessKeyId
     */
    public String getAccessKeyId() {
        return System.getenv("OSS_ACCESS_KEY_ID");
    }
    
    /**
     * 从环境变量获取AccessKeySecret
     */
    public String getAccessKeySecret() {
        return System.getenv("OSS_ACCESS_KEY_SECRET");
    }
    
    /**
     * 从环境变量获取STS角色ARN
     */
    public String getRoleArn() {
        return System.getenv("OSS_STS_ROLE_ARN");
    }
} 