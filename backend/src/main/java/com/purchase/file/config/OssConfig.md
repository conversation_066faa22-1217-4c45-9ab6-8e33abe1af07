# OssConfig 阿里云OSS配置类文档

## 文件概述

`OssConfig` 是阿里云OSS（对象存储服务）的配置类，负责管理OSS连接参数、STS临时凭证配置等。该配置类采用环境变量的方式管理敏感信息，确保安全性的同时提供了灵活的配置管理，为文件上传和存储服务提供基础配置支持。

## 核心功能

### 主要职责
- **OSS配置管理**: 管理OSS服务的连接参数和存储桶配置
- **STS凭证配置**: 配置STS临时凭证的相关参数
- **安全管理**: 通过环境变量管理敏感的访问密钥
- **区域配置**: 配置OSS服务的区域和端点信息
- **会话管理**: 配置角色会话的相关参数

### 业务特点
- 使用环境变量保护敏感信息
- 支持STS临时凭证机制
- 提供灵活的区域配置
- 简化的配置管理方式
- 安全的密钥管理策略

## 接口说明

### 基础配置字段

#### endpoint
- **类型**: String
- **默认值**: "https://oss-cn-hongkong.aliyuncs.com"
- **描述**: OSS区域地址
- **用途**: 指定OSS服务的访问端点
- **配置**: 可根据实际使用的OSS区域进行调整

#### bucketName
- **类型**: String
- **默认值**: "purchase-william"
- **描述**: OSS存储桶名称
- **用途**: 指定文件存储的目标存储桶
- **注意**: 存储桶名称必须全局唯一

#### stsEndpoint
- **类型**: String
- **默认值**: "sts.cn-hongkong.aliyuncs.com"
- **描述**: STS服务端点
- **用途**: 指定STS临时凭证服务的端点
- **配置**: 应与OSS区域保持一致

### STS配置字段

#### durationSeconds
- **类型**: Long
- **默认值**: 3600L
- **描述**: 临时凭证有效期（秒）
- **用途**: 控制STS临时凭证的有效时间
- **范围**: 900秒（15分钟）到43200秒（12小时）

#### roleSessionName
- **类型**: String
- **默认值**: "SessionTest"
- **描述**: 角色会话名称
- **用途**: 标识STS会话的名称
- **格式**: 2-64个字符，支持字母、数字、点号、下划线、连字符

### 环境变量方法

#### getAccessKeyId()
```java
public String getAccessKeyId()
```
- **功能**: 从环境变量获取AccessKeyId
- **环境变量**: OSS_ACCESS_KEY_ID
- **返回值**: 阿里云访问密钥ID
- **安全**: 避免在代码中硬编码敏感信息

#### getAccessKeySecret()
```java
public String getAccessKeySecret()
```
- **功能**: 从环境变量获取AccessKeySecret
- **环境变量**: OSS_ACCESS_KEY_SECRET
- **返回值**: 阿里云访问密钥Secret
- **安全**: 通过环境变量保护密钥安全

#### getRoleArn()
```java
public String getRoleArn()
```
- **功能**: 从环境变量获取STS角色ARN
- **环境变量**: OSS_STS_ROLE_ARN
- **返回值**: STS角色的ARN标识
- **用途**: 用于STS临时凭证的角色扮演

## 使用示例

### 环境变量配置
```bash
# 设置阿里云访问密钥
export OSS_ACCESS_KEY_ID="your_access_key_id"
export OSS_ACCESS_KEY_SECRET="your_access_key_secret"

# 设置STS角色ARN
export OSS_STS_ROLE_ARN="acs:ram::123456789012:role/AliyunOSSRole"

# Docker环境配置
docker run -e OSS_ACCESS_KEY_ID="your_access_key_id" \
           -e OSS_ACCESS_KEY_SECRET="your_access_key_secret" \
           -e OSS_STS_ROLE_ARN="acs:ram::123456789012:role/AliyunOSSRole" \
           your-app:latest

# Kubernetes ConfigMap
apiVersion: v1
kind: Secret
metadata:
  name: oss-credentials
type: Opaque
data:
  OSS_ACCESS_KEY_ID: <base64-encoded-access-key-id>
  OSS_ACCESS_KEY_SECRET: <base64-encoded-access-key-secret>
  OSS_STS_ROLE_ARN: <base64-encoded-role-arn>
```

### 服务中使用配置
```java
@Service
public class OssService {
    
    @Autowired
    private OssConfig ossConfig;
    
    /**
     * 创建OSS客户端
     */
    public OSS createOssClient() {
        return new OSSClientBuilder().build(
            ossConfig.getEndpoint(),
            ossConfig.getAccessKeyId(),
            ossConfig.getAccessKeySecret()
        );
    }
    
    /**
     * 获取STS临时凭证
     */
    public AssumeRoleResponse getStsCredentials() {
        // 创建STS客户端
        DefaultProfile profile = DefaultProfile.getProfile(
            "cn-hongkong",  // 区域ID
            ossConfig.getAccessKeyId(),
            ossConfig.getAccessKeySecret()
        );
        
        IAcsClient client = new DefaultAcsClient(profile);
        
        // 创建AssumeRole请求
        AssumeRoleRequest request = new AssumeRoleRequest();
        request.setSysEndpoint(ossConfig.getStsEndpoint());
        request.setRoleArn(ossConfig.getRoleArn());
        request.setRoleSessionName(ossConfig.getRoleSessionName());
        request.setDurationSeconds(ossConfig.getDurationSeconds());
        
        try {
            return client.getAcsResponse(request);
        } catch (ClientException e) {
            throw new RuntimeException("获取STS凭证失败", e);
        }
    }
    
    /**
     * 使用临时凭证创建OSS客户端
     */
    public OSS createOssClientWithSts() {
        AssumeRoleResponse stsResponse = getStsCredentials();
        AssumeRoleResponse.Credentials credentials = stsResponse.getCredentials();
        
        return new OSSClientBuilder().build(
            ossConfig.getEndpoint(),
            credentials.getAccessKeyId(),
            credentials.getAccessKeySecret(),
            credentials.getSecurityToken()
        );
    }
}
```

### 文件上传服务
```java
@Service
public class FileUploadService {
    
    @Autowired
    private OssConfig ossConfig;
    
    @Autowired
    private OssService ossService;
    
    /**
     * 上传文件到OSS
     */
    public String uploadFile(MultipartFile file, String directory) {
        OSS ossClient = null;
        try {
            // 创建OSS客户端
            ossClient = ossService.createOssClientWithSts();
            
            // 生成文件名
            String fileName = generateFileName(file.getOriginalFilename());
            String objectKey = directory + "/" + fileName;
            
            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                ossConfig.getBucketName(),
                objectKey,
                file.getInputStream()
            );
            
            // 设置文件元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());
            putObjectRequest.setMetadata(metadata);
            
            // 执行上传
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            
            // 返回文件URL
            return generateFileUrl(objectKey);
            
        } catch (Exception e) {
            throw new RuntimeException("文件上传失败", e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
    
    /**
     * 生成文件名
     */
    private String generateFileName(String originalFilename) {
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        return UUID.randomUUID().toString() + extension;
    }
    
    /**
     * 生成文件访问URL
     */
    private String generateFileUrl(String objectKey) {
        return ossConfig.getEndpoint().replace("://", "://" + ossConfig.getBucketName() + ".") 
               + "/" + objectKey;
    }
    
    /**
     * 删除文件
     */
    public void deleteFile(String objectKey) {
        OSS ossClient = null;
        try {
            ossClient = ossService.createOssClientWithSts();
            ossClient.deleteObject(ossConfig.getBucketName(), objectKey);
        } catch (Exception e) {
            throw new RuntimeException("文件删除失败", e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
}
```

### 配置验证
```java
@Component
public class OssConfigValidator {
    
    @Autowired
    private OssConfig ossConfig;
    
    @PostConstruct
    public void validateConfig() {
        // 验证必要的环境变量
        if (ossConfig.getAccessKeyId() == null || ossConfig.getAccessKeyId().isEmpty()) {
            throw new IllegalStateException("OSS_ACCESS_KEY_ID环境变量未设置");
        }
        
        if (ossConfig.getAccessKeySecret() == null || ossConfig.getAccessKeySecret().isEmpty()) {
            throw new IllegalStateException("OSS_ACCESS_KEY_SECRET环境变量未设置");
        }
        
        if (ossConfig.getRoleArn() == null || ossConfig.getRoleArn().isEmpty()) {
            throw new IllegalStateException("OSS_STS_ROLE_ARN环境变量未设置");
        }
        
        // 验证配置格式
        if (!ossConfig.getEndpoint().startsWith("http")) {
            throw new IllegalStateException("OSS endpoint格式错误");
        }
        
        if (ossConfig.getDurationSeconds() < 900 || ossConfig.getDurationSeconds() > 43200) {
            throw new IllegalStateException("STS凭证有效期必须在900-43200秒之间");
        }
        
        log.info("OSS配置验证通过: endpoint={}, bucket={}", 
                ossConfig.getEndpoint(), ossConfig.getBucketName());
    }
}
```

### Spring Boot配置文件集成
```yaml
# application.yml
oss:
  endpoint: ${OSS_ENDPOINT:https://oss-cn-hongkong.aliyuncs.com}
  bucket-name: ${OSS_BUCKET_NAME:purchase-william}
  sts-endpoint: ${OSS_STS_ENDPOINT:sts.cn-hongkong.aliyuncs.com}
  duration-seconds: ${OSS_DURATION_SECONDS:3600}
  role-session-name: ${OSS_ROLE_SESSION_NAME:SessionTest}

# 使用@ConfigurationProperties注解
@ConfigurationProperties(prefix = "oss")
@Component
public class EnhancedOssConfig {
    private String endpoint;
    private String bucketName;
    private String stsEndpoint;
    private Long durationSeconds;
    private String roleSessionName;
    
    // getters and setters
}
```

## 注意事项

### 安全配置
1. **环境变量**: 敏感信息必须通过环境变量配置，不能硬编码
2. **权限最小化**: STS角色权限应遵循最小权限原则
3. **凭证轮换**: 定期轮换访问密钥和角色配置
4. **网络安全**: 使用HTTPS端点确保传输安全

### 性能优化
1. **连接复用**: 合理复用OSS客户端连接
2. **并发控制**: 控制并发上传的数量
3. **分片上传**: 大文件使用分片上传
4. **缓存策略**: 缓存STS临时凭证

### 可靠性保证
1. **异常处理**: 完善的异常处理和重试机制
2. **资源释放**: 确保OSS客户端的正确释放
3. **监控告警**: 监控OSS操作的成功率和延迟
4. **备份策略**: 重要文件的备份和恢复策略

### 成本控制
1. **存储类型**: 根据访问频率选择合适的存储类型
2. **生命周期**: 配置文件的生命周期管理
3. **流量优化**: 优化文件访问的流量成本
4. **清理策略**: 定期清理无用的文件
