package com.purchase.file.config;

import com.aliyun.sts20150401.Client;
import com.aliyun.teaopenapi.models.Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * STS客户端配置
 * 使用现有的OssConfig凭据
 */
@Slf4j
@Configuration
public class StsClientConfiguration {

    @Autowired
    private OssConfig ossConfig;

    @Bean
    public Client stsClient() {
        try {
            // 使用现有的OssConfig中的凭据
            Config config = new Config();
            // Endpoint 请参考 https://api.aliyun.com/product/Sts
            config.endpoint = "sts.cn-hangzhou.aliyuncs.com";

            // 直接使用AccessKey和Secret，不依赖默认凭据链
            config.accessKeyId = ossConfig.getAccessKeyId();
            config.accessKeySecret = ossConfig.getAccessKeySecret();

            log.info("STS客户端初始化成功，endpoint: {}", config.endpoint);
            return new Client(config);

        } catch (Exception e) {
            log.error("STS客户端初始化失败", e);
            e.printStackTrace();
            return null;
        }
    }
}
