# StsClientConfiguration STS客户端配置类文档

## 文件概述

`StsClientConfiguration` 是阿里云STS（Security Token Service）客户端的Spring配置类，负责创建和配置STS客户端Bean。该配置类使用现有的OssConfig凭据，为应用程序提供STS临时凭证服务，支持安全的临时访问令牌获取，是文件上传安全机制的重要组成部分。

## 核心功能

### 主要职责
- **STS客户端创建**: 创建阿里云STS服务的客户端实例
- **凭据复用**: 复用OssConfig中的访问凭据
- **Bean管理**: 将STS客户端注册为Spring Bean
- **配置管理**: 管理STS服务的端点和连接配置
- **异常处理**: 处理客户端初始化过程中的异常

### 业务特点
- 基于Spring Configuration注解的配置类
- 复用现有的OSS访问凭据
- 提供统一的STS客户端实例
- 完善的日志记录和异常处理
- 支持依赖注入和生命周期管理

## 接口说明

### 依赖注入

#### ossConfig
- **类型**: OssConfig
- **注解**: @Autowired
- **描述**: OSS配置对象
- **用途**: 获取访问密钥和相关配置信息

### Bean配置方法

#### stsClient()
```java
@Bean
public Client stsClient()
```
- **功能**: 创建STS客户端Bean
- **返回值**: 阿里云STS客户端实例
- **配置**: 使用OssConfig中的凭据和端点配置
- **异常处理**: 初始化失败时返回null并记录错误日志

### 配置参数

#### endpoint
- **值**: "sts.cn-hangzhou.aliyuncs.com"
- **描述**: STS服务端点
- **用途**: 指定STS服务的访问地址
- **参考**: https://api.aliyun.com/product/Sts

#### accessKeyId / accessKeySecret
- **来源**: ossConfig.getAccessKeyId() / ossConfig.getAccessKeySecret()
- **描述**: 阿里云访问凭据
- **用途**: 用于STS客户端的身份验证

## 使用示例

### STS服务使用
```java
@Service
public class StsService {
    
    @Autowired
    private Client stsClient;
    
    @Autowired
    private OssConfig ossConfig;
    
    /**
     * 获取STS临时凭证
     */
    public StsCredentials getStsCredentials() {
        try {
            // 创建AssumeRole请求
            AssumeRoleRequest request = new AssumeRoleRequest();
            request.setRoleArn(ossConfig.getRoleArn());
            request.setRoleSessionName(ossConfig.getRoleSessionName());
            request.setDurationSeconds(ossConfig.getDurationSeconds());
            
            // 设置权限策略（可选）
            String policy = buildPolicy();
            if (policy != null) {
                request.setPolicy(policy);
            }
            
            // 调用STS服务
            AssumeRoleResponse response = stsClient.assumeRole(request);
            AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials credentials = 
                response.getBody().getCredentials();
            
            // 转换为自定义凭证对象
            return StsCredentials.builder()
                .accessKeyId(credentials.getAccessKeyId())
                .accessKeySecret(credentials.getAccessKeySecret())
                .securityToken(credentials.getSecurityToken())
                .expiration(credentials.getExpiration())
                .build();
                
        } catch (Exception e) {
            log.error("获取STS临时凭证失败", e);
            throw new RuntimeException("获取STS临时凭证失败", e);
        }
    }
    
    /**
     * 构建权限策略
     */
    private String buildPolicy() {
        // 构建最小权限策略
        Map<String, Object> policy = new HashMap<>();
        policy.put("Version", "1");
        
        List<Map<String, Object>> statements = new ArrayList<>();
        Map<String, Object> statement = new HashMap<>();
        statement.put("Effect", "Allow");
        statement.put("Action", Arrays.asList("oss:PutObject", "oss:GetObject", "oss:DeleteObject"));
        statement.put("Resource", "acs:oss:*:*:" + ossConfig.getBucketName() + "/*");
        statements.add(statement);
        
        policy.put("Statement", statements);
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(policy);
        } catch (Exception e) {
            log.warn("构建权限策略失败", e);
            return null;
        }
    }
    
    /**
     * 验证凭证是否有效
     */
    public boolean isCredentialsValid(StsCredentials credentials) {
        if (credentials == null || credentials.getExpiration() == null) {
            return false;
        }
        
        // 检查是否在有效期内（提前5分钟过期）
        LocalDateTime expiration = LocalDateTime.parse(
            credentials.getExpiration(), 
            DateTimeFormatter.ISO_DATE_TIME
        );
        
        return expiration.isAfter(LocalDateTime.now().plusMinutes(5));
    }
}
```

### 文件上传控制器
```java
@RestController
@RequestMapping("/api/file")
public class FileUploadController {
    
    @Autowired
    private StsService stsService;
    
    /**
     * 获取上传凭证
     */
    @GetMapping("/upload-credentials")
    public ApiResponse<StsCredentials> getUploadCredentials() {
        try {
            StsCredentials credentials = stsService.getStsCredentials();
            return ApiResponse.success(credentials);
        } catch (Exception e) {
            return ApiResponse.error("获取上传凭证失败: " + e.getMessage());
        }
    }
    
    /**
     * 刷新凭证
     */
    @PostMapping("/refresh-credentials")
    public ApiResponse<StsCredentials> refreshCredentials(
            @RequestBody StsCredentials oldCredentials) {
        
        // 检查旧凭证是否即将过期
        if (stsService.isCredentialsValid(oldCredentials)) {
            return ApiResponse.success(oldCredentials);
        }
        
        // 获取新凭证
        try {
            StsCredentials newCredentials = stsService.getStsCredentials();
            return ApiResponse.success(newCredentials);
        } catch (Exception e) {
            return ApiResponse.error("刷新凭证失败: " + e.getMessage());
        }
    }
}
```

### 前端集成示例
```javascript
// STS凭证管理
class StsCredentialsManager {
  constructor() {
    this.credentials = null;
    this.refreshTimer = null;
  }
  
  // 获取有效凭证
  async getValidCredentials() {
    if (!this.credentials || this.isExpiringSoon(this.credentials)) {
      await this.refreshCredentials();
    }
    return this.credentials;
  }
  
  // 刷新凭证
  async refreshCredentials() {
    try {
      const response = await fetch('/api/file/upload-credentials', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      
      const result = await response.json();
      if (result.success) {
        this.credentials = result.data;
        this.scheduleRefresh();
        console.log('STS凭证刷新成功');
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('获取STS凭证失败:', error);
      throw error;
    }
  }
  
  // 检查凭证是否即将过期
  isExpiringSoon(credentials) {
    if (!credentials || !credentials.expiration) {
      return true;
    }
    
    const expiration = new Date(credentials.expiration);
    const now = new Date();
    const fiveMinutesLater = new Date(now.getTime() + 5 * 60 * 1000);
    
    return expiration <= fiveMinutesLater;
  }
  
  // 定时刷新凭证
  scheduleRefresh() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
    
    // 提前5分钟刷新
    const expiration = new Date(this.credentials.expiration);
    const refreshTime = expiration.getTime() - Date.now() - 5 * 60 * 1000;
    
    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(() => {
        this.refreshCredentials();
      }, refreshTime);
    }
  }
}

// 使用STS凭证上传文件
const uploadFileWithSts = async (file, directory = 'uploads') => {
  const credentialsManager = new StsCredentialsManager();
  
  try {
    // 获取有效凭证
    const credentials = await credentialsManager.getValidCredentials();
    
    // 创建OSS客户端
    const client = new OSS({
      region: 'oss-cn-hongkong',
      accessKeyId: credentials.accessKeyId,
      accessKeySecret: credentials.accessKeySecret,
      stsToken: credentials.securityToken,
      bucket: 'purchase-william'
    });
    
    // 生成文件名
    const fileName = generateFileName(file.name);
    const objectKey = `${directory}/${fileName}`;
    
    // 上传文件
    const result = await client.put(objectKey, file, {
      progress: (p) => {
        console.log('上传进度:', Math.round(p * 100) + '%');
      }
    });
    
    return {
      url: result.url,
      objectKey: objectKey,
      fileName: fileName
    };
    
  } catch (error) {
    console.error('文件上传失败:', error);
    throw error;
  }
};
```

### 配置验证和监控
```java
@Component
public class StsClientHealthIndicator implements HealthIndicator {
    
    @Autowired
    private Client stsClient;
    
    @Autowired
    private StsService stsService;
    
    @Override
    public Health health() {
        try {
            if (stsClient == null) {
                return Health.down()
                    .withDetail("error", "STS客户端未初始化")
                    .build();
            }
            
            // 尝试获取临时凭证来验证连接
            StsCredentials credentials = stsService.getStsCredentials();
            
            return Health.up()
                .withDetail("status", "STS服务正常")
                .withDetail("credentialsExpiration", credentials.getExpiration())
                .build();
                
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", "STS服务异常: " + e.getMessage())
                .build();
        }
    }
}

@Component
public class StsMetrics {
    
    private final Counter stsRequestCounter;
    private final Timer stsRequestTimer;
    
    public StsMetrics(MeterRegistry meterRegistry) {
        this.stsRequestCounter = Counter.builder("sts.requests.total")
            .description("STS请求总数")
            .register(meterRegistry);
            
        this.stsRequestTimer = Timer.builder("sts.request.duration")
            .description("STS请求耗时")
            .register(meterRegistry);
    }
    
    public void recordStsRequest(boolean success, Duration duration) {
        stsRequestCounter.increment(
            Tags.of("status", success ? "success" : "failure")
        );
        stsRequestTimer.record(duration);
    }
}
```

## 注意事项

### 配置安全
1. **凭据保护**: 确保访问密钥通过环境变量安全配置
2. **权限最小化**: STS角色权限应遵循最小权限原则
3. **网络安全**: 使用HTTPS端点确保通信安全
4. **凭据轮换**: 定期轮换访问密钥

### 性能优化
1. **客户端复用**: STS客户端应作为单例使用
2. **连接池**: 合理配置HTTP连接池参数
3. **缓存策略**: 缓存有效的临时凭证
4. **异步处理**: 凭证刷新使用异步处理

### 可靠性保证
1. **异常处理**: 完善的异常处理和重试机制
2. **健康检查**: 定期检查STS服务的可用性
3. **监控告警**: 监控STS请求的成功率和延迟
4. **降级策略**: STS服务不可用时的降级方案

### 最佳实践
1. **凭证管理**: 合理管理临时凭证的生命周期
2. **错误处理**: 提供友好的错误信息和处理建议
3. **日志记录**: 记录重要的操作和异常信息
4. **配置验证**: 启动时验证配置的正确性
