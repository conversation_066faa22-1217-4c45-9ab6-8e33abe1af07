# FileController.java

## 文件概述 (File Overview)
`FileController.java` 是文件管理的REST控制器，负责处理文件上传相关的HTTP请求。它作为文件管理模块的对外接口，接收前端的文件上传请求，调用 `FileService` 进行业务处理，并返回标准化的响应结果。该控制器支持多种文件类型的上传，包括图片、视频和文档等，为采购系统中的各种文件管理需求提供统一的API接口。

## 核心功能 (Core Functionality)
*   **文件上传处理:** 接收前端的文件上传请求，支持多种文件类型（图片、视频、文档）。
*   **请求验证:** 对上传的文件进行基本验证，确保文件的有效性。
*   **业务委托:** 将文件处理逻辑委托给 `FileService` 进行具体的业务操作。
*   **响应封装:** 根据业务处理结果返回相应的HTTP状态码和响应数据。
*   **日志记录:** 记录文件上传的详细信息，便于调试和监控。

## 接口说明 (Interface Description)

### 方法 (Methods)
*   `ResponseEntity<?> uploadFile(@RequestParam("file") MultipartFile file) throws IOException`:
    *   **HTTP方法:** POST
    *   **路径:** `/api/v1/files/upload`
    *   **参数:** `file` (MultipartFile) - 通过表单上传的文件对象
    *   **返回值:** `ResponseEntity<?>` - 包含上传结果的HTTP响应
    *   **抛出异常:** `IOException` - 文件处理过程中的IO异常
    *   **业务逻辑:** 接收文件上传请求，记录请求信息，调用 `FileService.uploadFile()` 进行处理，根据处理结果返回相应的HTTP状态码和响应数据

### 请求格式 (Request Format)
*   **Content-Type:** `multipart/form-data`
*   **参数名:** `file`
*   **支持的文件类型:**
    *   图片：jpg, jpeg, png, gif, webp
    *   视频：mp4
    *   文档：pdf, doc, docx

### 响应格式 (Response Format)
*   **成功响应 (200):**
    ```json
    {
        "code": 200,
        "message": "文件上传成功",
        "data": "https://example.com/path/to/uploaded/file.jpg"
    }
    ```
*   **失败响应 (400/500):**
    ```json
    {
        "code": 400,
        "message": "文件类型不支持,请上传jpg, jpeg, png, gif, webp, mp4, pdf, doc或docx文件",
        "data": null
    }
    ```

## 使用示例 (Usage Examples)

```java
// 前端JavaScript调用示例
const uploadFile = async (file) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await fetch('/api/v1/files/upload', {
            method: 'POST',
            body: formData,
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.code === 200) {
            console.log('文件上传成功:', result.data);
            return result.data; // 返回文件URL
        } else {
            console.error('文件上传失败:', result.message);
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('上传请求失败:', error);
        throw error;
    }
};

// Java客户端调用示例
@Service
public class FileUploadService {
    @Autowired
    private RestTemplate restTemplate;

    public String uploadFile(MultipartFile file) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", file.getResource());

        HttpEntity<MultiValueMap<String, Object>> requestEntity =
            new HttpEntity<>(body, headers);

        ResponseEntity<Map> response = restTemplate.postForEntity(
            "/api/v1/files/upload",
            requestEntity,
            Map.class
        );

        Map<String, Object> result = response.getBody();
        if ((Integer) result.get("code") == 200) {
            return (String) result.get("data");
        } else {
            throw new RuntimeException((String) result.get("message"));
        }
    }
}

// 在其他Controller中使用
@RestController
public class UserController {
    @Autowired
    private FileUploadService fileUploadService;

    @PostMapping("/users/avatar")
    public Result<String> uploadAvatar(@RequestParam("avatar") MultipartFile avatar) {
        try {
            String avatarUrl = fileUploadService.uploadFile(avatar);
            // 更新用户头像URL到数据库
            return Result.success(avatarUrl);
        } catch (Exception e) {
            return Result.error("头像上传失败: " + e.getMessage());
        }
    }
}
```

## 注意事项 (Notes)
*   **文件大小限制:** 不同类型的文件有不同的大小限制，具体限制由 `FileService` 实现类定义。图片文件通常限制为100MB，视频文件为2GB，文档文件为200MB。
*   **文件类型验证:** 控制器依赖 `FileService` 进行文件类型验证，只支持预定义的安全文件类型。
*   **异常处理:** `IOException` 会被Spring框架的全局异常处理器捕获，返回友好的错误信息给前端。
*   **日志记录:** 使用 `@Slf4j` 注解记录文件上传的详细信息，包括文件名、大小和类型，便于调试和监控。
*   **响应状态码:** 根据 `FileService` 返回的结果中的 `code` 字段设置HTTP状态码，确保前端能够正确处理不同的响应情况。
*   **安全考虑:** 应配合Spring Security进行权限验证，确保只有授权用户才能上传文件。
*   **并发处理:** 控制器本身是无状态的，可以安全地处理并发请求，但需要注意 `FileService` 实现的线程安全性。
*   **文件存储:** 实际的文件存储逻辑由 `FileService` 处理，通常存储到阿里云OSS等对象存储服务中。