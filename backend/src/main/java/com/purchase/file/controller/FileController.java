package com.purchase.file.controller;

import com.purchase.file.service.FileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

/**
 * 文件控制器
 * 处理文件上传等请求
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/files")
@RequiredArgsConstructor
public class FileController {

    private final FileService fileService;

    /**
     * 文件上传接口
     * 支持图片、视频和文档等文件类型
     *
     * @param file 上传的文件
     * @return 上传结果
     * @throws IOException 如果文件处理过程中发生IO异常
     */
    @PostMapping("/upload")
    public ResponseEntity<?> uploadFile(@RequestParam("file") MultipartFile file) throws IOException {
        log.debug("文件上传请求: 文件名={}, 大小={}, 类型={}", 
            file.getOriginalFilename(), file.getSize(), file.getContentType());
        
        Map<String, Object> result = fileService.uploadFile(file);
        
        int statusCode = (int) result.getOrDefault("code", 500);
        if (statusCode == 200) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(statusCode).body(result);
        }
    }
}