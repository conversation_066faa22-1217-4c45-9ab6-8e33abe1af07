package com.purchase.file.controller;

import com.aliyun.sts20150401.Client;
import com.aliyun.sts20150401.models.AssumeRoleRequest;
import com.aliyun.sts20150401.models.AssumeRoleResponse;
import com.aliyun.sts20150401.models.AssumeRoleResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.purchase.file.config.OssConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.aliyun.teautil.Common.assertAsString;

/**
 * STS临时凭证控制器
 * 按照阿里云官方示例代码实现
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/oss")
@RequiredArgsConstructor
public class StsController {

    @Autowired
    private Client stsClient;

    private final OssConfig ossConfig;

    /**
     * 获取OSS上传的STS临时凭证
     * 按照官方示例代码实现
     */
    @GetMapping("/get_sts_token_for_oss_upload")
    public AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials generateStsToken() {
        AssumeRoleRequest assumeRoleRequest = new AssumeRoleRequest()
            .setDurationSeconds(3600L)
            // 设置自定义的会话名称
            .setRoleSessionName("purchase-system-upload-" + System.currentTimeMillis())
            // 使用配置中的角色ARN
            .setRoleArn(ossConfig.getRoleArn());

        RuntimeOptions runtime = new RuntimeOptions();

        try {
            AssumeRoleResponse response = stsClient.assumeRoleWithOptions(assumeRoleRequest, runtime);
            log.debug("STS凭证获取成功，过期时间: {}", response.body.credentials.getExpiration());
            return response.body.credentials;
        } catch (TeaException error) {
            // 如有需要，请打印 error
            log.error("STS凭证获取失败: {}", error.message);
            assertAsString(error.message);
            return null;
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 如有需要，请打印 error
            log.error("STS凭证获取异常", error);
            assertAsString(error.message);
            return null;
        }
    }




}
