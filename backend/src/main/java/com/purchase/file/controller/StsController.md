# StsController.md

## 1. 文件概述

`StsController.java` 是文件服务模块中的核心控制器，位于 `com.purchase.file.controller` 包中。该控制器作为文件服务模块的API层，专门负责处理与阿里云STS（Security Token Service）相关的请求。通过集成阿里云STS Java SDK，它提供了获取临时访问凭证（STS Token）的功能，主要用于授权客户端（如Web前端）直接上传文件到阿里云OSS（Object Storage Service）。该控制器严格遵循了阿里云官方的最佳实践，确保了凭证获取过程的安全和高效。

## 2. 核心功能

*   **STS凭证生成**: 动态生成有时效性的STS临时访问凭证。这是控制器的唯一但核心的功能。
*   **安全授权**: 通过STS，实现了应用服务器不直接处理文件上传、不暴露永久AccessKey的目的，遵循了最小权限原则。
*   **配置驱动**: 凭证的生成依赖于外部化的配置（`OssConfig`），如角色ARN（Role ARN），使得配置更加灵活，易于在不同环境间切换。
*   **会话命名**: 为每个STS会话生成一个唯一的名称（`RoleSessionName`），便于在阿里云控制台进行审计和追踪。
*   **官方SDK集成**: 完全基于阿里云官方提供的 `aliyun-sts20150401` SDK进行开发，保证了稳定性和兼容性。
*   **日志记录**: 在凭证获取成功或失败时，都会记录详细的日志，便于监控和问题排查。

## 3. 接口说明

### STS凭证获取

#### generateStsToken - 获取OSS上传的STS临时凭证
*   **方法签名**: `AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials generateStsToken()`
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/oss/get_sts_token_for_oss_upload`
*   **权限**: 默认未设置显式权限，通常需要在安全框架层面（如Spring Security）进行保护，确保只有认证用户才能调用。
*   **参数**: 无
*   **返回值**: `AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials` - 一个包含临时访问密钥ID（`AccessKeyId`）、访问密钥（`AccessKeySecret`）、安全令牌（`SecurityToken`）和过期时间（`Expiration`）的对象。
*   **业务逻辑**:
    1.  创建一个 `AssumeRoleRequest` 对象。
    2.  设置凭证的有效时间（`DurationSeconds`），当前硬编码为3600秒（1小时）。
    3.  从 `OssConfig` 中获取配置的角色ARN（`RoleArn`）并设置到请求中。
    4.  生成一个基于当前时间戳的唯一 `RoleSessionName`，用于审计。
    5.  调用 `stsClient.assumeRoleWithOptions` 方法，向阿里云STS服务发起请求。
    6.  如果请求成功，记录成功日志并返回包含凭证的 `credentials` 对象。
    7.  如果发生 `TeaException` 或其他异常，记录详细的错误日志，并返回 `null`。

## 4. 业务规则

*   **凭证有效期**: 临时凭证的有效期被设置为1小时。这意味着客户端必须在获取凭证后的1小时内完成文件上传操作。
*   **角色权限**: 返回的STS凭证所拥有的权限，完全由其扮演的角色（`RoleArn`）在阿里云RAM（Resource Access Management）中被授予的权限策略决定。通常该角色应仅被授予指定OSS存储桶（Bucket）的写入权限。
*   **会话命名约定**: `RoleSessionName` 的格式为 `purchase-system-upload-{timestamp}`，提供了基本的审计信息。

## 5. 使用示例

```java
// 1. 前端JavaScript (axios) 调用示例
// 假设前端需要上传一个文件，首先向后端请求STS凭证

async function getStsToken() {
  try {
    const response = await axios.get('/api/v1/oss/get_sts_token_for_oss_upload', {
      headers: {
        'Authorization': `Bearer ${your_jwt_token}`
      }
    });
    return response.data; // { accessKeyId, accessKeySecret, securityToken, expiration }
  } catch (error) {
    console.error('获取STS凭证失败:', error);
    throw error;
  }
}

async function uploadFile(file) {
  const stsToken = await getStsToken();
  
  const client = new OSS({
    region: 'oss-cn-hangzhou', // 根据你的OSS区域修改
    accessKeyId: stsToken.accessKeyId,
    accessKeySecret: stsToken.accessKeySecret,
    stsToken: stsToken.securityToken,
    bucket: 'your-bucket-name'
  });

  try {
    const result = await client.put(`uploads/${file.name}`, file);
    console.log('上传成功:', result);
  } catch (e) {
    console.error('上传失败:', e);
  }
}

// 2. Java客户端调用示例 (RestTemplate)
@Service
public class FileUploadClient {
    @Autowired
    private RestTemplate restTemplate;

    private final String STS_URL = "http://localhost:8080/api/v1/oss/get_sts_token_for_oss_upload";

    public AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials fetchStsToken() {
        HttpHeaders headers = new HttpHeaders();
        // 假设需要认证
        // headers.setBearerAuth(jwtToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials> response = 
            restTemplate.exchange(STS_URL, HttpMethod.GET, entity, AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials.class);

        if (response.getStatusCode() == HttpStatus.OK) {
            return response.getBody();
        } else {
            // 处理错误
            return null;
        }
    }
}

// 3. 测试示例
@SpringBootTest
@AutoConfigureMockMvc
class StsControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private Client stsClient;

    @Test
    void testGenerateStsToken_Success() throws Exception {
        // 模拟阿里云SDK的成功响应
        AssumeRoleResponse mockResponse = new AssumeRoleResponse();
        AssumeRoleResponseBody mockBody = new AssumeRoleResponseBody();
        AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials credentials = new AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials();
        credentials.setAccessKeyId("test-key-id");
        credentials.setAccessKeySecret("test-key-secret");
        credentials.setSecurityToken("test-security-token");
        mockBody.setCredentials(credentials);
        mockResponse.setBody(mockBody);

        when(stsClient.assumeRoleWithOptions(any(), any())).thenReturn(mockResponse);

        mockMvc.perform(get("/api/v1/oss/get_sts_token_for_oss_upload"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessKeyId").value("test-key-id"));
    }

    @Test
    void testGenerateStsToken_Failure() throws Exception {
        // 模拟阿里云SDK抛出异常
        when(stsClient.assumeRoleWithOptions(any(), any())).thenThrow(new TeaException("STS error", new RuntimeException()));

        mockMvc.perform(get("/api/v1/oss/get_sts_token_for_oss_upload"))
                .andExpect(status().isOk()) // 控制器内部处理了异常，可能返回null或一个特定的错误结构
                .andExpect(content().string("")); // 假设失败时返回空内容
    }
}
```

## 6. 注意事项

*   **安全**: API端点 `/api/v1/oss/get_sts_token_for_oss_upload` 必须被保护，只有经过身份验证和授权的用户才能调用，以防止匿名用户滥用STS资源。
*   **配置管理**: `OssConfig` 中的 `roleArn` 是关键敏感配置，必须通过安全的配置管理方式（如配置中心、环境变量）注入，严禁硬编码在代码中。
*   **异常处理**: 当前的异常处理逻辑是记录错误并返回 `null`。在生产环境中，建议返回一个统一的、包含错误码和错误信息的JSON结构，便于前端处理。
*   **网络策略**: 应用服务器需要有访问阿里云STS服务终端节点（endpoint）的网络权限。如果服务器在VPC内，需要配置相应的网络策略或NAT网关。
*   **RAM角色权限**: 必须遵循最小权限原则，为`RoleArn`对应的RAM角色精确授权。例如，只允许其向指定的Bucket下的特定前缀（如 `uploads/`）执行 `PutObject` 操作。
*   **凭证有效期**: 3600秒的有效期需要与前端上传组件的超时设置相匹配。如果预计有大文件上传，可能需要适当延长有效期或实现凭证刷新机制。
*   **监控与审计**: 应在阿里云控制台开启对STS调用和RAM角色使用的审计日志，以便监控异常活动。
*   **SDK版本**: 保持 `aliyun-sts20150401` SDK为最新版本，以获取最新的功能和安全更新。
*   **成本考量**: STS服务本身是免费的，但频繁调用会产生API请求费用，需根据业务量进行评估。
*   **国际化**: 错误信息 `error.message` 可能来自阿里云SDK，可能是英文。如果需要，应在控制器层将其转换为多语言的错误提示。
*   **测试**: 单元测试中应使用 `@MockBean` 来模拟 `stsClient`，以避免在测试过程中产生真实的网络调用和费用。