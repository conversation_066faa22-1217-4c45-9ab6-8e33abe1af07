# File 文件模块文档

## 模块概述

文件模块是采购系统的基础设施模块，提供完整的文件管理解决方案。该模块支持文件上传、下载、存储管理等功能，集成阿里云OSS对象存储服务，为系统中的图片、文档、视频等文件提供可靠的存储和访问服务。

## 目录结构概览

```
com.purchase.file/
├── config/                      # 配置
│   ├── OssConfig.java                  # OSS配置
│   └── StsClientConfiguration.java     # STS客户端配置
├── controller/                  # 控制器层
│   ├── FileController.java            # 文件控制器
│   └── StsController.java             # STS令牌控制器
├── service/                     # 服务层
│   ├── FileService.java               # 文件服务接口
│   └── impl/                           # 服务实现
```

## 核心功能详述

### 1. 文件上传下载 (FileController)

#### 主要功能
- **文件上传**: 支持多种文件类型的上传
- **文件下载**: 提供文件下载和预览功能
- **批量操作**: 支持批量文件上传和下载
- **文件验证**: 文件类型、大小、安全性验证
- **进度跟踪**: 大文件上传进度跟踪

#### 支持的文件类型
- **图片文件**: JPG, PNG, GIF, BMP, WEBP
- **文档文件**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- **视频文件**: MP4, AVI, MOV, WMV
- **压缩文件**: ZIP, RAR, 7Z
- **其他文件**: TXT, CSV等

### 2. OSS存储服务 (OssConfig)

#### 主要功能
- **对象存储**: 基于阿里云OSS的文件存储
- **CDN加速**: 文件访问CDN加速
- **存储桶管理**: 多存储桶的管理和配置
- **访问控制**: 文件访问权限控制
- **生命周期**: 文件生命周期管理

#### 存储策略
- **分类存储**: 按文件类型和业务分类存储
- **路径规则**: 规范的文件路径命名规则
- **版本控制**: 支持文件版本管理
- **备份策略**: 重要文件的备份机制

### 3. STS临时授权 (StsController)

#### 主要功能
- **临时令牌**: 生成STS临时访问令牌
- **权限控制**: 基于业务场景的权限控制
- **安全访问**: 避免长期密钥泄露风险
- **令牌管理**: 令牌的生成、刷新和撤销

#### 授权场景
- **前端直传**: 前端直接上传到OSS
- **移动端上传**: 移动应用的文件上传
- **第三方集成**: 第三方系统的文件访问
- **临时分享**: 文件的临时分享链接

### 4. 文件服务 (FileService)

#### 主要功能
- **文件管理**: 文件的增删改查操作
- **元数据管理**: 文件元数据的管理
- **缩略图生成**: 图片缩略图自动生成
- **格式转换**: 文件格式转换服务
- **病毒扫描**: 文件安全扫描

#### 业务集成
- **产品图片**: 产品展示图片管理
- **文档附件**: 合同、证书等文档管理
- **用户头像**: 用户头像上传和管理
- **聊天文件**: 聊天中的文件分享

## 配置说明

### OSS配置 (OssConfig)

#### 基础配置
```yaml
oss:
  endpoint: https://oss-cn-hangzhou.aliyuncs.com
  access-key-id: ${OSS_ACCESS_KEY_ID}
  access-key-secret: ${OSS_ACCESS_KEY_SECRET}
  bucket-name: purchase-system-files
  domain: https://files.purchase-system.com
```

#### 高级配置
- **存储类型**: 标准存储、低频访问、归档存储
- **传输加速**: 全球加速配置
- **图片处理**: 自动图片处理参数
- **防盗链**: 防盗链配置

### STS配置 (StsClientConfiguration)

#### 权限策略
```json
{
  "Version": "1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "oss:PutObject",
        "oss:GetObject"
      ],
      "Resource": [
        "acs:oss:*:*:bucket-name/user-uploads/*"
      ]
    }
  ]
}
```

## API 接口概览

### 文件管理接口
- `POST /api/v1/files/upload` - 上传文件
- `POST /api/v1/files/batch-upload` - 批量上传文件
- `GET /api/v1/files/{fileId}` - 获取文件信息
- `GET /api/v1/files/{fileId}/download` - 下载文件
- `DELETE /api/v1/files/{fileId}` - 删除文件

### 图片处理接口
- `GET /api/v1/files/{fileId}/thumbnail` - 获取缩略图
- `GET /api/v1/files/{fileId}/resize` - 图片尺寸调整
- `POST /api/v1/files/images/compress` - 图片压缩

### STS令牌接口
- `POST /api/v1/sts/token` - 获取STS临时令牌
- `POST /api/v1/sts/refresh` - 刷新STS令牌
- `POST /api/v1/sts/revoke` - 撤销STS令牌

### 文件分享接口
- `POST /api/v1/files/{fileId}/share` - 创建分享链接
- `GET /api/v1/files/shared/{shareId}` - 访问分享文件
- `DELETE /api/v1/files/shared/{shareId}` - 取消分享

## 使用示例

### 文件上传
```javascript
// 前端文件上传示例
const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('category', 'product-images');
  
  const response = await fetch('/api/v1/files/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });
  
  const result = await response.json();
  return result.data.fileUrl;
};
```

### STS直传
```javascript
// 获取STS令牌并直传OSS
const uploadWithSts = async (file) => {
  // 1. 获取STS令牌
  const stsResponse = await fetch('/api/v1/sts/token', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      category: 'product-images',
      duration: 3600
    })
  });
  
  const stsData = await stsResponse.json();
  
  // 2. 使用STS令牌直传OSS
  const ossClient = new OSS({
    region: 'oss-cn-hangzhou',
    accessKeyId: stsData.accessKeyId,
    accessKeySecret: stsData.accessKeySecret,
    stsToken: stsData.securityToken,
    bucket: stsData.bucket
  });
  
  const result = await ossClient.put(stsData.objectKey, file);
  return result.url;
};
```

### 后端文件处理
```java
@Service
public class FileServiceImpl implements FileService {
    
    @Autowired
    private OSSClient ossClient;
    
    @Override
    public String uploadFile(MultipartFile file, String category) {
        try {
            // 1. 文件验证
            validateFile(file);
            
            // 2. 生成文件路径
            String objectKey = generateObjectKey(file, category);
            
            // 3. 上传到OSS
            PutObjectResult result = ossClient.putObject(
                bucketName, 
                objectKey, 
                file.getInputStream()
            );
            
            // 4. 生成访问URL
            String fileUrl = generateFileUrl(objectKey);
            
            // 5. 保存文件记录
            saveFileRecord(file, objectKey, fileUrl, category);
            
            return fileUrl;
            
        } catch (Exception e) {
            throw new BusinessException("文件上传失败", e);
        }
    }
    
    private void validateFile(MultipartFile file) {
        // 文件大小验证
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BusinessException("文件大小超过限制");
        }
        
        // 文件类型验证
        String contentType = file.getContentType();
        if (!ALLOWED_CONTENT_TYPES.contains(contentType)) {
            throw new BusinessException("不支持的文件类型");
        }
        
        // 文件名验证
        String filename = file.getOriginalFilename();
        if (filename == null || filename.contains("..")) {
            throw new BusinessException("非法文件名");
        }
    }
}
```

## 业务规则

### 文件上传规则
1. 文件大小限制：单个文件不超过100MB
2. 文件类型限制：只允许安全的文件类型
3. 文件名规范：不允许特殊字符和路径穿越
4. 病毒扫描：上传文件需要通过安全扫描

### 存储管理规则
1. 分类存储：按业务类型分目录存储
2. 命名规范：使用UUID避免文件名冲突
3. 生命周期：临时文件定期清理
4. 备份策略：重要文件异地备份

### 访问控制规则
1. 权限验证：文件访问需要权限验证
2. 防盗链：配置防盗链保护资源
3. 临时链接：敏感文件使用临时访问链接
4. 水印保护：图片文件添加水印保护

### 安全规则
1. 文件扫描：上传文件进行病毒扫描
2. 内容检测：图片内容合规检测
3. 访问日志：记录文件访问日志
4. 异常监控：监控异常访问行为

## 集成说明

### 与其他模块的关系
- **用户模块**: 用户头像和身份证件上传
- **产品模块**: 产品图片和视频管理
- **订单模块**: 合同文档和支付凭证
- **消息模块**: 聊天中的文件分享

### 第三方集成
- **阿里云OSS**: 主要存储服务
- **阿里云STS**: 临时授权服务
- **CDN服务**: 文件访问加速
- **图片处理**: 自动图片处理服务

## 注意事项

### 开发注意事项
1. **异常处理**: 完善的文件操作异常处理
2. **事务管理**: 文件上传与数据库操作的事务一致性
3. **内存管理**: 大文件处理时的内存控制
4. **并发控制**: 高并发文件上传的处理

### 性能优化
1. **分片上传**: 大文件分片上传
2. **并发上传**: 支持多文件并发上传
3. **缓存策略**: 文件元数据缓存
4. **CDN加速**: 文件访问CDN加速

### 安全考虑
1. **文件验证**: 严格的文件类型和内容验证
2. **权限控制**: 基于业务的文件访问权限
3. **防病毒**: 文件上传病毒扫描
4. **数据加密**: 敏感文件加密存储

### 运维监控
1. **存储监控**: OSS存储使用量监控
2. **访问统计**: 文件访问量统计
3. **错误监控**: 文件操作错误监控
4. **性能监控**: 文件上传下载性能监控
