# JwtAuthenticationFilter.java

## 文件概述 (File Overview)
`JwtAuthenticationFilter.java` 是一个基于 JWT（JSON Web Token）的认证过滤器，继承自 `OncePerRequestFilter`。它在每个 HTTP 请求到达 Spring Security 的认证链之前执行，负责从请求中提取 JWT，验证其有效性，并根据 JWT 中的信息设置 Spring Security 的认证上下文。这使得应用程序能够实现无状态的认证机制。

## 核心功能 (Core Functionality)
*   **JWT 提取与验证:** 从 HTTP 请求（通过 Cookie）中提取 JWT，并使用 `JwtUtil` 对其进行验证。
*   **认证信息设置:** 如果 JWT 有效，则解析其中的用户信息（如用户名、用户 ID、角色），并构建 `UsernamePasswordAuthenticationToken`，将其设置到 `SecurityContextHolder` 中，以便后续的 Spring Security 组件进行授权。
*   **请求过滤:**
    *   **跳过 OPTIONS 请求:** 忽略 CORS 预检请求，避免不必要的认证处理。
    *   **跳过 Actuator 端点:** 忽略 `/actuator/**` 路径下的请求，允许 Spring Boot Actuator 端点在没有认证的情况下访问（通常用于健康检查等）。
*   **异常处理:** 捕获 JWT 解析或验证过程中可能发生的异常，并记录日志，但不中断请求链，允许后续过滤器处理。

## 接口说明 (Interface Description)

### `doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)`
*   **参数:**
    *   `request` (HttpServletRequest): 当前的 HTTP 请求对象。
    *   `response` (HttpServletResponse): 当前的 HTTP 响应对象。
    *   `filterChain` (FilterChain): 过滤器链，用于将请求传递给下一个过滤器或目标资源。
*   **抛出:** `ServletException`, `IOException`
*   **功能:** 这是 `OncePerRequestFilter` 抽象类要求实现的抽象方法，确保该过滤器在每个请求中只执行一次。它负责 JWT 的提取、验证、认证信息的设置，并处理 OPTIONS 请求和 Actuator 端点的跳过逻辑。

## 使用示例 (Usage Examples)
`JwtAuthenticationFilter` 通常需要与 Spring Security 配置（如 `SecurityConfig`）结合使用，将其添加到 Spring Security 的过滤器链中。

```java
// 在 SecurityConfig.java 中配置
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            .csrf().disable() // 禁用CSRF
            .sessionManagement().sessionCreationPolicy(SessionManagement.SessionCreationPolicy.STATELESS) // 无状态会话
            .and()
            .authorizeRequests()
                .antMatchers("/auth/**", "/public/**", "/actuator/**").permitAll() // 允许公共访问的路径
                .anyRequest().authenticated() // 其他所有请求都需要认证
            .and()
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class); // 在UsernamePasswordAuthenticationFilter之前添加JWT过滤器
    }

    // ... 其他配置，如PasswordEncoder, UserDetailsService等
}
```

## 注意事项 (Notes)
*   **无状态认证:** 该过滤器实现了无状态认证，即服务器不保存会话信息，所有认证状态都包含在 JWT 中。这对于微服务架构和 API 驱动的应用程序非常有利。
*   **安全性:** JWT 的安全性依赖于密钥的保密性。密钥泄露将导致所有 JWT 失效。
*   **刷新机制:** JWT 通常有过期时间。对于需要长期会话的应用程序，需要实现 JWT 的刷新机制（例如，通过 Refresh Token）。
*   **异常处理:** 过滤器内部的异常处理仅记录日志，不会阻止请求继续处理。这意味着即使 JWT 验证失败，请求也可能继续到达 Controller，但此时 `SecurityContextHolder` 中将没有认证信息。更严格的错误处理可能需要在过滤器中直接返回错误响应。
*   **依赖:** 依赖于 `CookieUtil` 从请求中获取 JWT，以及 `JwtUtil` 进行 JWT 的验证和解析。
*   **CORS 预检:** 对 OPTIONS 请求的跳过是处理 CORS（跨域资源共享）预检请求的常见做法，因为预检请求不携带认证信息。
