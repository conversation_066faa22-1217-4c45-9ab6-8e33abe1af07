# MybatisPlusConfig.java

## 文件概述 (File Overview)
`MybatisPlusConfig.java` 是一个 Spring 配置类，用于对 MyBatis-Plus 进行全局配置。它主要负责集成 MyBatis-Plus 的各种插件，特别是分页插件，以简化数据库操作中的分页逻辑。通过统一配置，避免了在各个模块中重复设置 MyBatis-Plus 的相关功能。

## 核心功能 (Core Functionality)
*   **MyBatis-Plus 拦截器配置:** 定义并配置了 `MybatisPlusInterceptor`，它是 MyBatis-Plus 插件的核心入口。
*   **分页插件集成:** 向 `MybatisPlusInterceptor` 中添加了 `PaginationInnerInterceptor`，实现了数据库层面的物理分页。
*   **分页限制:** 设置了分页插件的最大单页限制数量为 100 条，防止一次性查询过多数据。

## 接口说明 (Interface Description)

### `mybatisPlusInterceptor()`
*   **注解:** `@Bean`, `@Primary`
*   **返回值:** `MybatisPlusInterceptor` - 配置好的 MyBatis-Plus 拦截器实例。
*   **功能:** 创建并配置一个 `MybatisPlusInterceptor` 实例。此方法是一个 Spring Bean，会被 Spring 容器管理。它向拦截器链中添加了 `PaginationInnerInterceptor`，并指定了数据库类型为 MySQL，同时设置了单页最大限制。`@Primary` 注解确保当存在多个 `MybatisPlusInterceptor` Bean 时，此配置优先被使用。

## 使用示例 (Usage Examples)
此配置类由 Spring 框架自动加载和应用，无需手动调用。在 Mapper 接口中，可以直接使用 MyBatis-Plus 提供的分页功能。

```java
// 示例：在Service层使用MyBatis-Plus的分页功能
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.purchase.common.dto.PageResult;
import com.purchase.user.entity.User;
import com.purchase.user.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserService {

    @Autowired
    private UserMapper userMapper;

    public PageResult<User> getUserPage(int current, int size) {
        // 创建MyBatis-Plus的Page对象
        Page<User> page = new Page<>(current, size);
        // 执行分页查询
        userMapper.selectPage(page, null); // null表示没有查询条件

        // 将MyBatis-Plus的Page对象转换为自定义的PageResult
        return PageResult.of(page.getRecords(), page.getTotal(), (int) page.getCurrent(), (int) page.getSize());
    }
}

// 示例：UserMapper接口
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.purchase.user.entity.User;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserMapper extends BaseMapper<User> {
    // BaseMapper提供了selectPage方法
}
```

## 注意事项 (Notes)
*   **数据库类型:** `PaginationInnerInterceptor` 的构造函数中指定了 `DbType.MYSQL`。如果项目使用其他数据库，需要相应地修改此处的数据库类型。
*   **最大单页限制:** `setMaxLimit(100L)` 设置了每页最大查询记录数。如果前端请求的 `size` 超过此限制，MyBatis-Plus 会自动将其调整为 100。这有助于防止恶意或错误的请求导致数据库负载过高。
*   **逻辑分页与物理分页:** MyBatis-Plus 的分页插件实现的是物理分页，即在 SQL 层面添加 `LIMIT` 等关键字，只查询当前页的数据，而不是先查询所有数据再进行内存分页，从而提高了性能。
*   **`@Primary` 注解:** 如果项目中存在多个 MyBatis-Plus 配置类，`@Primary` 注解确保 Spring 优先使用此配置。
