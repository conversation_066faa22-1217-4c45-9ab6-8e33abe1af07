# MyMetaObjectHandler.java

## 文件概述 (File Overview)
`MyMetaObjectHandler.java` 是一个 MyBatis-Plus 的元对象处理器，用于实现数据库实体中公共字段的自动填充。它通过实现 `MetaObjectHandler` 接口，在执行插入（insert）和更新（update）操作时，自动为实体对象的 `createdAt`（创建时间）和 `updatedAt`（更新时间）字段填充当前时间，支持 `LocalDateTime` 和 `Date` 两种时间类型。这极大地简化了数据持久化层的开发，减少了重复代码。

## 核心功能 (Core Functionality)
*   **自动填充创建时间:** 在执行插入操作时，如果实体中存在 `createdAt` 字段，则自动填充为当前时间。
*   **自动填充更新时间:** 在执行插入和更新操作时，如果实体中存在 `updatedAt` 字段，则自动填充为当前时间。
*   **支持多种时间类型:** 同时支持 `java.time.LocalDateTime` 和 `java.util.Date` 类型的字段填充。

## 接口说明 (Interface Description)

### `insertFill(MetaObject metaObject)`
*   **参数:** `metaObject` (MetaObject) - MyBatis-Plus 提供的元对象，包含了当前操作的实体对象信息。
*   **功能:** 在执行插入操作时被调用。它会检查 `metaObject` 中是否存在名为 `createdAt` 和 `updatedAt` 的字段，如果存在且未被手动设置，则自动填充为当前的 `LocalDateTime.now()` 或 `new Date()`。

### `updateFill(MetaObject metaObject)`
*   **参数:** `metaObject` (MetaObject) - MyBatis-Plus 提供的元对象，包含了当前操作的实体对象信息。
*   **功能:** 在执行更新操作时被调用。它会检查 `metaObject` 中是否存在名为 `updatedAt` 的字段，如果存在且未被手动设置，则自动填充为当前的 `LocalDateTime.now()` 或 `new Date()`。

## 使用示例 (Usage Examples)
此处理器通过 `@Component` 注解被 Spring 容器管理，并通过 `@Primary` 注解确保在存在多个 `MetaObjectHandler` 实现时优先使用。在实体类中，需要使用 `@TableField(fill = FieldFill.INSERT)` 或 `@TableField(fill = FieldFill.INSERT_UPDATE)` 注解来标记需要自动填充的字段。

```java
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class User {
    private Long id;
    private String username;
    private String email;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    // ... 其他字段和方法
}

// 在Mapper或Service中执行插入或更新操作时，这些字段会自动填充
// 例如：
// userMapper.insert(user); // createdAt 和 updatedAt 会自动填充
// userMapper.updateById(user); // updatedAt 会自动填充
```

## 注意事项 (Notes)
*   **字段命名:** 自动填充依赖于实体类中字段的名称（`createdAt`, `updatedAt`）。如果字段名称不同，需要修改 `MyMetaObjectHandler` 中的硬编码字段名，或者使用 MyBatis-Plus 提供的其他配置方式。
*   **`@TableField(fill = ...)`:** 确保实体类中需要自动填充的字段正确使用了 `@TableField` 注解，并指定了正确的填充策略（`FieldFill.INSERT` 或 `FieldFill.INSERT_UPDATE`）。
*   **时间类型:** 确保实体类中 `createdAt` 和 `updatedAt` 字段的类型与 `MyMetaObjectHandler` 中处理的类型（`LocalDateTime` 或 `Date`）一致。
*   **`@Primary` 注解:** 如果项目中存在多个 `MetaObjectHandler` 实现，`@Primary` 注解确保 Spring 优先使用此配置。
