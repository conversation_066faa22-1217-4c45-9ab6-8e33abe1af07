package com.purchase.config;

import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import com.purchase.common.util.CookieUtil;
import com.purchase.common.util.JwtUtil;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Collections;

@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private CookieUtil cookieUtil;

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // 🔥 跳过OPTIONS请求（CORS预检）
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            filterChain.doFilter(request, response);
            return;
        }

        // 🔥 跳过Actuator端点，避免认证拦截
        String requestPath = request.getRequestURI();
        if (requestPath.startsWith("/actuator/")) {
            filterChain.doFilter(request, response);
            return;
        }

        try {
            String jwt = cookieUtil.getTokenFromRequest(request);

            if (jwt != null && jwtUtil.validateToken(jwt)) {
                // 使用JwtUtil解析token
                Claims claims = jwtUtil.parseToken(jwt);

                String username = claims.getSubject();
                String userIdStr = claims.get("userId", String.class);
                Long userId = Long.parseLong(userIdStr);
                String role = claims.get("role", String.class);
                List<SimpleGrantedAuthority> authorities = Collections.singletonList(new SimpleGrantedAuthority(role));

                // 设置认证信息：principal为userId字符串，credentials为Claims对象
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(userId.toString(), claims, authorities);
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
        } catch (Exception ex) {
            logger.error("认证过滤器处理失败", ex);
        }

        filterChain.doFilter(request, response);
    }



} 