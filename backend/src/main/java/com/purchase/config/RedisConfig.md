# RedisConfig.java

## 文件概述 (File Overview)
`RedisConfig.java` 是一个 Spring 配置类，用于配置和定制 Spring Data Redis 的 `RedisTemplate`。它主要解决了在使用 Redis 存储 Java 对象时，默认序列化器可能导致的问题，特别是对 `LocalDateTime` 等 Java 8 日期时间类型的支持。通过统一配置，确保了 Redis 操作的一致性和正确性。

## 核心功能 (Core Functionality)
*   **自定义 `RedisTemplate`:** 配置了一个 `RedisTemplate` Bean，用于与 Redis 进行交互。
*   **JSON 序列化:** 使用 `Jackson2JsonRedisSerializer` 作为默认的 Value 序列化器，将 Java 对象序列化为 JSON 格式存储在 Redis 中，反之亦然。
*   **`LocalDateTime` 支持:** 配置 `ObjectMapper` 注册 `JavaTimeModule`，确保 `LocalDateTime` 等 Java 8 日期时间类型能够正确地序列化和反序列化。
*   **Key 序列化:** 设置 Key 的序列化器为 `StringRedisSerializer`，确保 Redis 中的 Key 以可读的字符串形式存储。

## 接口说明 (Interface Description)

### `redisTemplate(RedisConnectionFactory connectionFactory)`
*   **注解:** `@Bean`, `@Primary`
*   **参数:** `connectionFactory` (RedisConnectionFactory) - Redis 连接工厂，由 Spring 自动注入。
*   **返回值:** `RedisTemplate<String, Object>` - 配置好的 `RedisTemplate` 实例。
*   **功能:** 创建并配置一个 `RedisTemplate` 实例。此方法是一个 Spring Bean，会被 Spring 容器管理。它设置了 Key 和 Value 的序列化器，并特别配置了 Jackson 以支持 Java 8 日期时间类型。`@Primary` 注解确保当存在多个 `RedisTemplate` Bean 时，此配置优先被使用。

## 使用示例 (Usage Examples)
此配置类由 Spring 框架自动加载和应用，无需手动调用。在需要使用 Redis 的 Service 或 Component 中，可以直接注入 `RedisTemplate`。

```java
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

@Service
public class RedisService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public void saveUser(String userId, User user) {
        redisTemplate.opsForValue().set("user:" + userId, user, 1, TimeUnit.HOURS);
    }

    public User getUser(String userId) {
        return (User) redisTemplate.opsForValue().get("user:" + userId);
    }

    public void saveLoginTime(String username) {
        redisTemplate.opsForValue().set("login:time:" + username, LocalDateTime.now());
    }

    public LocalDateTime getLoginTime(String username) {
        return (LocalDateTime) redisTemplate.opsForValue().get("login:time:" + username);
    }
}

// 假设User类
// import lombok.Data;
// import java.io.Serializable;
// @Data
// public class User implements Serializable {
//     private Long id;
//     private String username;
//     private String email;
// }
```

## 注意事项 (Notes)
*   **序列化:** `Jackson2JsonRedisSerializer` 会将 Java 对象序列化为 JSON 字符串。这意味着在 Redis 中存储的数据是可读的 JSON 格式。如果需要存储二进制数据或对性能有极高要求，可能需要考虑其他序列化器（如 `JdkSerializationRedisSerializer` 或 `ProtobufRedisSerializer`）。
*   **`@Primary` 注解:** 如果项目中存在多个 `RedisTemplate` Bean 的定义，`@Primary` 注解确保 Spring 优先使用此配置。
*   **`ObjectMapper` 配置:** 如果 Java 对象中包含其他复杂的类型（如自定义枚举、集合等），可能需要进一步配置 `ObjectMapper` 以确保正确的序列化和反序列化。
*   **连接工厂:** `RedisTemplate` 依赖于 `RedisConnectionFactory`。确保项目中已正确配置了 Redis 连接信息（例如在 `application.yml` 中）。
