package com.purchase.config;

import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.aop.interceptor.SimpleAsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Spring 异步任务执行器配置
 */
@Configuration
@EnableAsync // 启用异步支持
public class AsyncConfig implements AsyncConfigurer {

    /**
     * 定义名为 taskExecutor 的线程池执行器
     * 用于 @Async("taskExecutor") 注解的方法
     */
    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数：CPU核心数
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors()); 
        // 最大线程数：CPU核心数 * 2
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2); 
        // 队列容量
        executor.setQueueCapacity(100); 
        // 线程名称前缀
        executor.setThreadNamePrefix("AsyncTask-"); 
        // 拒绝策略：由调用者线程处理（确保任务不丢失）
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy()); 
        // 初始化
        executor.initialize();
        return executor;
    }

    /**
     * 配置异步任务未捕获异常的处理器
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new SimpleAsyncUncaughtExceptionHandler();
    }
} 