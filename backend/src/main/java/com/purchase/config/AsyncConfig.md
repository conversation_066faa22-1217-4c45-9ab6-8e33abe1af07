# AsyncConfig.java

## 文件概述 (File Overview)
`AsyncConfig.java` 是一个 Spring 配置类，用于配置和启用应用程序的异步任务执行。通过 `@EnableAsync` 注解，它允许 Spring 识别和处理带有 `@Async` 注解的方法，使其在独立的线程池中异步执行，从而提高应用程序的响应性和吞吐量。该配置类定义了一个名为 `taskExecutor` 的线程池，并配置了其核心参数和异常处理策略。

## 核心功能 (Core Functionality)
*   **启用异步支持:** 通过 `@EnableAsync` 注解，激活 Spring 的异步方法执行能力。
*   **自定义线程池:** 定义了一个 `ThreadPoolTaskExecutor` 实例作为异步任务的执行器，名为 `taskExecutor`。
*   **线程池参数配置:**
    *   **核心线程数 (`CorePoolSize`):** 设置为 CPU 核心数，保证基本并发处理能力。
    *   **最大线程数 (`MaxPoolSize`):** 设置为 CPU 核心数的两倍，允许在高峰期处理更多任务。
    *   **队列容量 (`QueueCapacity`):** 设置为 100，用于存放等待执行的任务。
    *   **线程名称前缀 (`ThreadNamePrefix`):** 设置为 "AsyncTask-"，便于日志追踪和线程管理。
    *   **拒绝策略 (`RejectedExecutionHandler`):** 采用 `ThreadPoolExecutor.CallerRunsPolicy()`，当线程池和队列都满时，由提交任务的线程自己执行任务，确保任务不会丢失。
*   **异步异常处理:** 配置了 `AsyncUncaughtExceptionHandler`，用于处理异步方法中未捕获的异常，这里使用了 `SimpleAsyncUncaughtExceptionHandler`，它会将异常记录到日志中。

## 接口说明 (Interface Description)

### `taskExecutor()`
*   **注解:** `@Bean(name = "taskExecutor")`
*   **返回值:** `Executor` - 配置好的线程池执行器实例。
*   **功能:** 创建、配置并返回一个 `ThreadPoolTaskExecutor` 实例。这个 Bean 会被 Spring 容器管理，并可以通过名称 `taskExecutor` 注入到其他组件中，或者通过 `@Async("taskExecutor")` 注解指定为异步方法的执行器。

### `getAsyncUncaughtExceptionHandler()`
*   **注解:** `@Override`
*   **返回值:** `AsyncUncaughtExceptionHandler` - 异步任务未捕获异常的处理器。
*   **功能:** 返回一个 `SimpleAsyncUncaughtExceptionHandler` 实例，用于处理异步方法执行过程中抛出但未被捕获的异常。默认情况下，`SimpleAsyncUncaughtExceptionHandler` 会将异常信息打印到日志中。

## 使用示例 (Usage Examples)
```java
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class MyAsyncService {

    private static final Logger logger = LoggerFactory.getLogger(MyAsyncService.class);

    // 使用默认的异步执行器
    @Async
    public void doSomethingAsync() {
        logger.info("Executing doSomethingAsync in thread: {}", Thread.currentThread().getName());
        // 模拟耗时操作
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        logger.info("Finished doSomethingAsync");
    }

    // 使用名为 "taskExecutor" 的异步执行器
    @Async("taskExecutor")
    public void doSomethingElseAsync() {
        logger.info("Executing doSomethingElseAsync in thread: {}", Thread.currentThread().getName());
        // 模拟耗时操作
        try {
            Thread.sleep(1000);
            // 模拟一个异常
            if (true) {
                throw new RuntimeException("Async task failed!");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        logger.info("Finished doSomethingElseAsync");
    }
}
```

## 注意事项 (Notes)
*   **异常处理:** 异步方法中的异常不会传播到调用线程。如果需要处理异步方法的异常，可以通过 `Future` 对象捕获，或者依赖 `AsyncUncaughtExceptionHandler` 进行统一处理。
*   **事务管理:** 异步方法中的事务管理需要特别注意。如果异步方法内部有事务，它将在自己的事务上下文中运行，与调用方的事务是独立的。
*   **线程池参数:** 线程池的核心线程数、最大线程数和队列容量需要根据应用程序的实际负载和服务器资源进行合理配置，以避免资源耗尽或任务堆积。
*   **`CallerRunsPolicy`:** 这种拒绝策略虽然保证了任务不丢失，但在高并发且线程池饱和的情况下，可能会导致调用线程被阻塞，从而影响主线程的响应性。在某些场景下，可能需要考虑其他拒绝策略，如 `AbortPolicy`（直接抛弃任务并抛出异常）或 `DiscardPolicy`（直接抛弃任务）。
