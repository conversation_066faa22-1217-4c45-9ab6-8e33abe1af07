# OpenApiConfig.java

## 文件概述 (File Overview)
`OpenApiConfig.java` 是一个 Spring 配置类，用于配置和生成 OpenAPI（Swagger）文档。它通过定义 API 的基本信息（如标题、描述、版本、联系方式、许可证）、服务器信息以及 API 标签（Tag），帮助开发者自动生成交互式的 API 文档，方便前后端协作和 API 测试。

## 核心功能 (Core Functionality)
*   **API 基本信息配置:** 设置 API 的名称、描述、版本、联系人信息和许可证信息。
*   **服务器信息配置:** 定义 API 部署的不同环境（如开发环境、生产环境）的 URL 和描述。
*   **API 标签（Tag）定义:** 预定义 API 的分类标签，每个标签可以包含名称和描述，用于对 API 进行逻辑分组。

## 接口说明 (Interface Description)

### `customOpenAPI()`
*   **注解:** `@Bean`
*   **返回值:** `OpenAPI` - 配置好的 OpenAPI 实例。
*   **功能:** 创建并配置一个 `OpenAPI` 实例。此方法是一个 Spring Bean，会被 Spring 容器管理。它通过链式调用设置了 API 的 `Info`（标题、描述、版本、联系人、许可证）、`Servers`（服务器列表）和 `Tags`（API 标签）。

## 使用示例 (Usage Examples)
此配置类由 Spring 框架自动加载和应用，无需手动调用。配置完成后，通常可以通过访问 `/swagger-ui.html` 或 `/webjars/swagger-ui/index.html`（具体路径可能因 Spring Boot 版本和配置而异）来查看生成的 API 文档。

```java
// 示例：在Controller中使用@Tag和@Operation注解，这些注解会被OpenAPI配置识别并用于生成文档
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "用户管理", description = "用户相关的API操作")
@RestController
@RequestMapping("/users")
public class UserController {

    @Operation(summary = "获取所有用户", description = "返回系统中所有用户的列表")
    @GetMapping
    public String getAllUsers() {
        return "List of users";
    }
}
```

## 注意事项 (Notes)
*   **依赖:** 确保项目中已引入 SpringDoc OpenAPI 相关的依赖（例如 `springdoc-openapi-ui`）。
*   **路径:** 访问 Swagger UI 的路径可能因 Spring Boot 版本和配置而异。常见的路径是 `/swagger-ui.html` 或 `/v3/api-docs`。
*   **信息准确性:** `OpenApiConfig` 中配置的信息会直接显示在 API 文档中，因此应确保这些信息的准确性和完整性。
*   **标签分类:** 合理的 API 标签分类有助于提高文档的可读性和导航性。
*   **安全性配置:** 对于生产环境，通常需要对 Swagger UI 进行安全保护，例如通过 Spring Security 进行认证授权，防止未经授权的访问。
