package com.purchase.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI 配置类
 * 配置Swagger文档生成
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Configuration
public class OpenApiConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("采购系统API文档")
                        .description("采购系统统一支付凭证管理API文档")
                        .version("v1.0.0")
                        .contact(new Contact()
                                .name("采购系统开发团队")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:8080")
                                .description("开发环境"),
                        new Server()
                                .url("https://api.purchase-system.com")
                                .description("生产环境")))
                .tags(List.of(
                        new Tag()
                                .name("统一支付凭证管理")
                                .description("支付凭证统一管理API，支持普通订单、样品订单、结算单的支付凭证管理"),
                        new Tag()
                                .name("结算单支付凭证管理")
                                .description("结算单支付凭证相关API，包括上传、查询、权限检查等功能"),
                        new Tag()
                                .name("样品订单支付凭证管理")
                                .description("样品订单支付凭证相关API，包括上传、查询、审核等功能"),
                        new Tag()
                                .name("普通订单支付凭证管理")
                                .description("普通订单支付凭证相关API，包括上传、查询、审核等功能")
                ));
    }
} 