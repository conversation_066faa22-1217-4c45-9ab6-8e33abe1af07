# SecurityConfig.java

## 文件概述 (File Overview)
`SecurityConfig.java` 是一个 Spring Security 的全局安全配置类，用于定义应用程序的认证和授权规则。它通过 `@EnableWebSecurity` 启用 Spring Security 的 Web 安全功能，并通过 `@EnableGlobalMethodSecurity(prePostEnabled = true)` 启用方法级别的安全注解。该配置类统一管理了 CORS、CSRF、会话管理、URL 权限控制以及 JWT 认证过滤器的集成，替代了各模块中可能存在的重复安全配置。

## 核心功能 (Core Functionality)
*   **CORS (跨域资源共享):** 配置了允许的源、方法、头部和凭证，以支持前端跨域请求。
*   **CSRF (跨站请求伪造) 禁用:** 禁用了 CSRF 保护，这在无状态的 RESTful API 中是常见的做法，因为 JWT 本身提供了足够的安全性。
*   **无状态会话管理:** 将会话创建策略设置为 `STATELESS`，表示应用程序不创建、不使用、不管理 HTTP 会话，所有认证状态都通过 JWT 在客户端维护。
*   **URL 权限控制:** 定义了不同 URL 路径的访问权限，包括：
    *   允许所有 OPTIONS 请求（CORS 预检请求）。
    *   允许 Actuator 端点（`/actuator/**`）无需认证。
    *   允许用户登录、注册、登出、刷新 token、邮箱验证、邮箱登录/注册、重置密码等公共接口无需认证。
    *   允许管理员登录、登出接口无需认证。
    *   允许 Swagger 相关接口（`/swagger-ui.html`, `/v3/api-docs/**` 等）无需认证。
    *   允许邮箱验证相关接口。
    *   允许产品、需求分类、分类、港口、文件上传、OSS STS 等公开接口无需认证。
    *   允许 WebSocket 握手端点无需认证。
    *   允许邀请相关 API（已登录用户）。
    *   **其他所有请求都需要认证**。
*   **JWT 认证集成:** 将自定义的 `JwtAuthenticationFilter` 添加到 Spring Security 的过滤器链中，用于处理 JWT 认证。

## 接口说明 (Interface Description)

### `filterChain(HttpSecurity http)`
*   **注解:** `@Bean`
*   **参数:** `http` (HttpSecurity) - 用于配置 HTTP 安全的构建器。
*   **返回值:** `SecurityFilterChain` - 配置好的安全过滤器链。
*   **抛出:** `Exception`
*   **功能:** 这是 Spring Security 5.x 推荐的配置方式。它配置了 HTTP 请求的安全性，包括 CORS、CSRF、会话管理、请求授权规则，并将 `jwtAuthenticationFilter` 添加到 `UsernamePasswordAuthenticationFilter.class` 之前。

### `corsConfigurationSource()`
*   **注解:** `@Bean`
*   **返回值:** `CorsConfigurationSource` - 配置好的 CORS 配置源。
*   **功能:** 创建并配置一个 `CorsConfigurationSource` 实例。它定义了 CORS 的详细规则，包括允许的来源（开发环境允许所有，生产环境指定具体域名）、允许的 HTTP 方法、允许的请求头、暴露的响应头、是否允许发送凭证以及预检请求的缓存时间。

## 使用示例 (Usage Examples)
此配置类由 Spring 框架自动加载和应用，无需手动调用。它定义了整个应用程序的安全策略。

```java
// 示例：在Controller中使用Spring Security的注解进行方法级别的权限控制
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin")
public class AdminController {

    @GetMapping("/dashboard")
    @PreAuthorize("hasRole('ADMIN')") // 只有拥有ADMIN角色的用户才能访问
    public String getAdminDashboard() {
        return "Admin Dashboard Data";
    }
}
```

## 注意事项 (Notes)
*   **安全性:** 禁用 CSRF 保护在无状态 API 中是可接受的，但如果应用程序同时处理基于会话的认证，则需要重新考虑。
*   **CORS 配置:** 生产环境中的 `allowedOriginPatterns("*")` 应该被替换为具体的、受信任的域名，以增强安全性。
*   **URL 权限顺序:** `antMatchers` 的配置顺序很重要，更具体的路径应该放在更通用的路径之前。例如，`/actuator/**` 应该在 `anyRequest().authenticated()` 之前。
*   **JWT 过滤器:** `JwtAuthenticationFilter` 必须正确配置并添加到安全过滤器链中，才能使 JWT 认证生效。
*   **方法安全:** `@EnableGlobalMethodSecurity(prePostEnabled = true)` 允许使用 `@PreAuthorize`, `@PostAuthorize`, `@PreFilter`, `@PostFilter` 等注解进行方法级别的权限控制。
*   **WebSocket:** WebSocket 握手端点通常需要单独配置允许访问，以确保 WebSocket 连接的建立。
