package com.purchase.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * 全局安全配置
 * 统一配置，替代各模块中的重复配置
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@Primary
public class SecurityConfig {

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .cors().and()
            .csrf().disable()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeRequests()
            // 🔥 优先允许所有OPTIONS请求（CORS预检）
            .antMatchers(HttpMethod.OPTIONS, "/**").permitAll()
            // 🔥 优先允许Actuator端点（必须在最前面）
            .antMatchers("/actuator/**").permitAll()
            // 允许所有用户接口无需认证（临时调试用）
            .antMatchers("/api/v1/users/login", "/api/v1/users/register", "/api/v1/users/logout", "/api/v1/users/refresh-token").permitAll()
            // 允许管理员登录和登出接口无需认证
            .antMatchers("/api/v1/admin/login", "/api/v1/admin/logout").permitAll()
            // 允许Swagger相关接口无需认证
            .antMatchers("/swagger-ui.html", "/swagger-ui/**", "/v3/api-docs/**", "/webjars/**").permitAll()
            // 允许邮箱验证相关接口
            .antMatchers("/api/v1/email/send-verification-code", "/api/v1/email/verify-code", "/api/v1/users/email-login", "/api/v1/users/email-register", "/api/v1/users/reset-password-by-email").permitAll()
            // 允许产品相关公开接口无需认证
            .antMatchers("/api/v1/products/featured", "/api/v1/products/categories", "/api/v1/products/search", "/api/v1/requirements/featured").permitAll()
            // 允许需求分类相关公开接口无需认证
            .antMatchers("/api/v1/requirement-categories/enabled", "/api/v1/requirement-categories/tree/enabled").permitAll()
            // 允许分类相关接口无需认证
            .antMatchers("/api/v1/categories/**").permitAll()
            // 允许港口相关接口无需认证
            .antMatchers("/api/v1/ports/**").permitAll()
            // 允许文件上传接口无需认证
            .antMatchers("/api/v1/files/upload").permitAll()
            // 允许OSS STS接口无需认证（用于前端直传）
            .antMatchers("/api/v1/oss/**").permitAll()
            // 允许WebSocket握手端点访问
            .antMatchers("/ws/**", "/ws/message/**", "/ws/message/info").permitAll()
            // 允许邀请相关API访问（已登录用户）（佣金API已移除）
            .antMatchers("/api/v1/invite/**").authenticated()
            
            // 其他所有请求需要认证
            .anyRequest().authenticated()
            .and()
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        
        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        // 🔥 开发环境：允许所有来源（包括file://协议）
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));

        // 生产环境的域名（保留备用）
        configuration.setAllowedOrigins(Arrays.asList(
            "https://www.bmbidmatch.com",
            "https://bmbidmatch.com",
            "http://localhost:5173",
            "http://localhost:3000",
            "http://127.0.0.1:3000",
            "http://localhost:8000",
            "http://localhost:8001"
        ));

        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setExposedHeaders(Arrays.asList("x-auth-token", "Authorization", "Content-Type"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L); // 预检请求缓存1小时

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
} 