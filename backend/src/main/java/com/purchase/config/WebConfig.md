# WebConfig.java

## 文件概述 (File Overview)
`WebConfig.java` 是一个 Spring 配置类，实现了 `WebMvcConfigurer` 接口，用于对 Spring MVC 进行定制化配置。它主要负责注册自定义的拦截器和配置跨域资源共享（CORS）策略，以满足应用程序的特定 Web 行为需求。

## 核心功能 (Core Functionality)
*   **拦截器注册:** 注册 `LogInterceptor`，使其能够拦截特定的 API 请求，并排除一些不需要日志记录的路径（如登录、注册、邮件验证、监控端点和 Swagger 文档）。
*   **CORS 配置:** 配置了允许的源、HTTP 方法、请求头和凭证，以支持前端的跨域请求。这里的 CORS 配置主要针对开发环境的 `http://localhost:*`。

## 接口说明 (Interface Description)

### `addInterceptors(InterceptorRegistry registry)`
*   **注解:** `@Override`
*   **参数:** `registry` (InterceptorRegistry) - 拦截器注册器，用于添加和配置拦截器。
*   **功能:** 向 Spring MVC 注册 `LogInterceptor`。它指定了 `LogInterceptor` 将拦截所有 `/api/**` 路径下的请求，并明确排除了登录、注册、邮件验证、Actuator 监控端点和 Swagger 文档相关的路径，以避免不必要的日志记录或敏感信息泄露。

### `addCorsMappings(CorsRegistry registry)`
*   **注解:** `@Override`
*   **参数:** `registry` (CorsRegistry) - CORS 注册器，用于配置跨域规则。
*   **功能:** 配置全局 CORS 策略。它允许来自 `http://localhost:*` 的所有 HTTP 方法（GET, POST, PUT, DELETE, OPTIONS）、所有请求头，并允许发送凭据。`maxAge(3600)` 设置了预检请求的缓存时间为 1 小时。

## 使用示例 (Usage Examples)
此配置类由 Spring 框架自动加载和应用，无需手动调用。它定义了 Web 层的行为，例如哪些请求会被 `LogInterceptor` 拦截，以及哪些跨域请求会被允许。

```java
// LogInterceptor 会拦截 /api/v1/products/list 请求，但不会拦截 /api/v1/users/login 请求。
// 前端从 http://localhost:3000 访问后端API时，如果配置正确，将不会遇到CORS问题。
```

## 注意事项 (Notes)
*   **拦截器路径:** `addPathPatterns` 和 `excludePathPatterns` 的配置需要仔细审查，确保拦截器只作用于预期的路径，并排除敏感或不需要拦截的路径。
*   **CORS 配置:** `allowedOriginPatterns("http://localhost:*")` 适用于开发环境。在生产环境中，应将其替换为实际的、受信任的前端域名，以增强安全性。
*   **与 `SecurityConfig` 的 CORS 区别:** `SecurityConfig` 中也可能配置 CORS。Spring Security 的 CORS 配置通常在过滤器链中更早生效，而 `WebMvcConfigurer` 中的 CORS 配置则在 Spring MVC 层面生效。通常建议在 Spring Security 中统一配置 CORS，以避免潜在的冲突或重复。
*   **依赖注入:** `LogInterceptor` 通过 `@Autowired` 注入，确保 Spring 容器能够正确管理其生命周期。
