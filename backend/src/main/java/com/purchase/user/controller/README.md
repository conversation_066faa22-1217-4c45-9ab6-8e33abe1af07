# com.purchase.user.controller 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.controller` 包包含了采购系统后端应用程序中与用户相关的 RESTful API 控制器。这些控制器负责处理来自前端的 HTTP 请求，调用相应的业务逻辑服务，并返回统一的响应结果。该包涵盖了用户认证（登录、注册、密码重置）、用户信息管理、邀请功能以及提现管理等核心用户功能。通过 Spring MVC 和 Spring Security 的集成，这些控制器确保了 API 的安全性和健壮性。

## 目录结构概览 (Directory Structure Overview)
*   `AdminController.java`: 管理员认证（登录、登出）接口。
*   `AdminWithdrawalController.java`: 管理员提现记录管理（查询、批准、拒绝）接口。
*   `EmailController.java`: 邮件验证码发送和验证，以及管理员发送邮件功能。
*   `InviteController.java`: 用户邀请码、邀请摘要和邀请用户列表查询接口。
*   `UserController.java`: 用户注册、登录、信息管理、密码修改、状态更新、用户列表查询、JWT 刷新等核心用户接口。
*   `WithdrawalController.java`: 用户提现申请、可提现余额查询和用户提现记录查询接口。
*   `AdminController.md`: `AdminController.java` 的文档。
*   `AdminWithdrawalController.md`: `AdminWithdrawalController.java` 的文档。
*   `EmailController.md`: `EmailController.java` 的文档。
*   `InviteController.md`: `InviteController.java` 的文档。
*   `UserController.md`: `UserController.java` 的文档。
*   `WithdrawalController.md`: `WithdrawalController.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.controller` 包中的控制器与 `com.purchase.user.service` 包中的服务层以及 `com.purchase.common` 包中的工具类和响应结构紧密协作，共同实现了用户模块的各项功能：

1.  **认证与会话管理:**
    *   `UserController` 和 `AdminController` 提供了用户和管理员的登录/注册接口。它们接收用户凭证，调用 `UserService` 进行认证，并在成功后利用 `com.purchase.common.util.CookieUtil` 将 JWT 和 Refresh Token 安全地设置到 HttpOnly Cookie 中。
    *   `UserController` 还提供了 `logout` 和 `refreshToken` 接口，分别用于清除认证 Cookie 和刷新 JWT，确保会话的有效管理。
    *   `com.purchase.config.JwtAuthenticationFilter`（位于 `config` 包）在请求到达控制器之前拦截并验证 JWT，将认证信息设置到 Spring Security 上下文中，供控制器和业务逻辑层使用。

2.  **用户信息与权限:**
    *   `UserController` 提供了获取和更新用户基本信息、详细信息、公司信息以及修改密码的接口。这些操作通常会调用 `UserService` 来执行实际的业务逻辑和数据库交互。
    *   `com.purchase.common.util.SecurityContextUtil`（位于 `common.util` 包）被广泛用于控制器中，以获取当前登录用户的 ID、角色，并进行方法级别的权限检查（例如，`@PreAuthorize` 注解）。这确保了只有授权用户才能执行敏感操作或访问特定资源。
    *   `AdminController` 和 `AdminWithdrawalController` 专门处理管理员特有的功能，如管理员登录和提现管理，这些接口通常需要 `admin` 角色权限。

3.  **邮件与邀请:**
    *   `EmailController` 负责处理邮件验证码的发送和验证，以及管理员发送邮件的功能。它依赖于 `com.purchase.user.service.EmailService` 来执行邮件发送的实际操作。
    *   `InviteController` 提供了用户邀请相关的接口，允许用户获取邀请码、查看邀请摘要和邀请列表。它调用 `UserService` 来获取邀请相关的数据。

4.  **提现流程:**
    *   `WithdrawalController` 允许普通用户申请提现、查询余额和查看记录。它调用 `com.purchase.user.service.WithdrawalService` 来处理这些业务。
    *   `AdminWithdrawalController` 则提供了管理员对提现请求的审批功能，同样调用 `WithdrawalService`。

**统一响应:**
所有控制器都使用 `com.purchase.common.response.Result`（或 `ApiResponse`）作为统一的 API 响应格式，确保了前端能够一致地处理成功和失败的响应。同时，`com.purchase.common.exception.GlobalExceptionHandler`（位于 `common.exception` 包）会捕获控制器中抛出的各种异常，并将其转换为统一的错误响应格式返回给客户端。

总而言之，`com.purchase.user.controller` 包是用户模块的对外接口层，它协调了认证、授权、业务逻辑和数据响应，为前端应用程序提供了全面的用户管理功能。
