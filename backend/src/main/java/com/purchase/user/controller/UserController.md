# UserController.java

## 文件概述 (File Overview)
`UserController.java` 是用户管理的REST控制器，位于 `com.purchase.user.controller` 包中。该控制器作为用户管理模块的HTTP接口层，负责处理所有用户相关的REST API请求。通过集成 `UserService`、`JwtUtil`、`CookieUtil` 等服务组件，提供了用户的完整生命周期管理功能，包括注册、登录、认证、信息管理、权限控制等。该控制器实现了基于JWT的无状态认证机制，支持HttpOnly Cookie的安全存储，并提供了多种登录方式（用户名/邮箱）和完善的权限分级控制。

## 核心功能 (Core Functionality)
*   **多方式用户认证**: 支持用户名/密码和邮箱/验证码两种注册和登录方式
*   **JWT无状态认证**: 基于JWT的无状态认证机制，支持token刷新和安全存储
*   **HttpOnly Cookie安全**: 使用HttpOnly Cookie存储JWT，防止XSS攻击
*   **用户信息管理**: 完整的用户资料管理，包括基本信息和公司信息
*   **密码安全管理**: 支持密码修改和通过邮箱验证码重置密码
*   **权限分级控制**: 基于Spring Security的多层权限验证（用户、管理员）
*   **用户状态管理**: 支持用户状态的启用、禁用和逻辑删除
*   **多维度用户查询**: 支持用户详情查询、列表查询、统计查询等
*   **角色分类管理**: 支持买家、卖家等不同角色的用户管理
*   **实时用户验证**: 提供用户存在性检查和重复性验证
*   **会话管理**: 完整的用户会话管理和token生命周期控制
*   **审计日志记录**: 详细的用户操作日志记录，支持安全审计

## 接口说明 (Interface Description)

### `test()`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/users/test`
*   **摘要:** 测试接口
*   **描述:** 用于验证控制器是否正常工作。
*   **参数:** 无
*   **返回值:** `String` - 返回字符串 "UserController is working!"。
*   **业务逻辑:** 仅用于简单的连通性测试，无复杂业务逻辑。

### `register(@Validated @RequestBody UserRegisterDTO registerDTO)`
*   **HTTP 方法:** `POST`
*   **路径:** `/api/v1/users/register`
*   **摘要:** 用户注册
*   **描述:** 创建新用户并返回用户信息。
*   **参数:**
    *   `registerDTO` (UserRegisterDTO, `@RequestBody`, `@Validated`): 包含用户名、密码等注册信息的数据传输对象。
*   **返回值:** `Result<User>` - 注册结果，成功则返回新创建的用户信息。
*   **业务逻辑:** 调用 `userService.register()` 方法处理用户注册。

### `login(@Validated @RequestBody UserLoginDTO loginDTO, HttpServletResponse response)`
*   **HTTP 方法:** `POST`
*   **路径:** `/api/v1/users/login`
*   **摘要:** 用户登录
*   **描述:** 验证用户凭证并设置 HttpOnly Cookie。
*   **参数:**
    *   `loginDTO` (UserLoginDTO, `@RequestBody`, `@Validated`): 包含凭证（用户名或邮箱）和密码的登录信息。
    *   `response` (HttpServletResponse): HTTP 响应对象，用于设置认证相关的 HttpOnly Cookie。
*   **返回值:** `Result<Map<String, Object>>` - 登录结果，成功则返回用户信息（不包含 token）。
*   **业务逻辑:**
    1.  调用 `userService.login()` 方法进行用户认证。
    2.  如果认证成功，从结果中获取 token 和 refreshToken。
    3.  使用 `cookieUtil` 将 token 和 refreshToken 设置为 HttpOnly Cookie。
    4.  从返回给前端的数据中移除 token 和 refreshToken。

### `emailRegister(@Validated @RequestBody EmailRegisterDTO emailRegisterDTO)`
*   **HTTP 方法:** `POST`
*   **路径:** `/api/v1/users/email-register`
*   **摘要:** 邮箱注册
*   **描述:** 使用邮箱验证码进行用户注册。
*   **参数:**
    *   `emailRegisterDTO` (EmailRegisterDTO, `@RequestBody`, `@Validated`): 包含邮箱、验证码、密码等注册信息的数据传输对象。
*   **返回值:** `Result<User>` - 注册结果，成功则返回新创建的用户信息。
*   **业务逻辑:** 调用 `userService.emailRegister()` 方法处理邮箱注册。

### `emailLogin(@Validated @RequestBody EmailLoginDTO emailLoginDTO, HttpServletResponse response)`
*   **HTTP 方法:** `POST`
*   **路径:** `/api/v1/users/email-login`
*   **摘要:** 邮箱登录
*   **描述:** 使用邮箱验证码进行登录并设置 HttpOnly Cookie。
*   **参数:**
    *   `emailLoginDTO` (EmailLoginDTO, `@RequestBody`, `@Validated`): 包含邮箱、验证码的登录信息。
    *   `response` (HttpServletResponse): HTTP 响应对象，用于设置认证相关的 HttpOnly Cookie。
*   **返回值:** `Result<Map<String, Object>>` - 登录结果，成功则返回用户信息（不包含 token）。
*   **业务逻辑:** 与 `login` 方法类似，调用 `userService.emailLogin()` 进行认证，并设置 HttpOnly Cookie。

### `resetPasswordByEmail(@Validated @RequestBody ResetPasswordByEmailDTO resetPasswordByEmailDTO)`
*   **HTTP 方法:** `POST`
*   **路径:** `/api/v1/users/reset-password-by-email`
*   **摘要:** 通过邮箱验证码重置密码
*   **描述:** 使用邮箱验证码重置用户密码，不需要登录。
*   **参数:**
    *   `resetPasswordByEmailDTO` (ResetPasswordByEmailDTO, `@RequestBody`, `@Validated`): 包含邮箱、验证码和新密码的数据传输对象。
*   **返回值:** `Result<Void>` - 重置结果。
*   **业务逻辑:** 调用 `userService.resetPasswordByEmail()` 方法处理密码重置。

### `updateUser(@PathVariable Long id, @RequestBody User user)`
*   **HTTP 方法:** `PUT`
*   **路径:** `/api/v1/users/{id}`
*   **摘要:** 更新用户信息
*   **描述:** 更新指定 ID 的用户信息。
*   **权限:** `hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')`
*   **参数:**
    *   `id` (Long, `@PathVariable`): 待更新用户的 ID。
    *   `user` (User, `@RequestBody`): 包含待更新字段的用户实体。
*   **返回值:** `Result<User>` - 更新结果，成功则返回更新后的用户信息。
*   **业务逻辑:**
    1.  权限检查：只有管理员或用户自己可以更新信息。
    2.  设置用户 ID。
    3.  调用 `userService.updateUser()` 方法更新用户信息。

### `changePassword(@PathVariable Long id, @RequestParam String oldPassword, @RequestParam String newPassword)`
*   **HTTP 方法:** `POST`
*   **路径:** `/api/v1/users/{id}/password`
*   **摘要:** 修改用户密码
*   **描述:** 修改指定 ID 的用户密码。
*   **权限:** `hasAnyAuthority('buyer', 'seller', 'forwarder')`
*   **参数:**
    *   `id` (Long, `@PathVariable`): 待修改密码用户的 ID。
    *   `oldPassword` (String, `@RequestParam`): 用户原密码。
    *   `newPassword` (String, `@RequestParam`): 用户新密码。
*   **返回值:** `Result<Void>` - 修改结果。
*   **业务逻辑:**
    1.  权限检查：管理员不允许修改密码，只有用户自己可以修改密码。
    2.  调用 `userService.changePassword()` 方法修改密码。

### `getUserById(@PathVariable Long id)`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/users/{id}`
*   **摘要:** 获取用户基本信息
*   **描述:** 获取指定 ID 的用户基本信息，包含 ID、用户名、头像等。
*   **权限:** `hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')`
*   **参数:**
    *   `id` (Long, `@PathVariable`): 用户 ID。
*   **返回值:** `Result<UserBasicInfoDTO>` - 用户基本信息。
*   **业务逻辑:** 调用 `userService.getUserBasicInfo()` 方法获取用户基本信息。

### `getUserDetail(@PathVariable Long id)`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/users/{id}/detail`
*   **摘要:** 获取用户详细信息
*   **描述:** 获取指定 ID 的用户详细信息，包含基本信息和公司详情。
*   **权限:** `hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')`
*   **参数:**
    *   `id` (Long, `@PathVariable`): 用户 ID。
*   **返回值:** `Result<UserDetailDTO>` - 用户详细信息。
*   **业务逻辑:**
    1.  权限检查：只有管理员或用户自己可以查看详细信息。
    2.  调用 `userService.getUserDetailInfo()` 方法获取用户详细信息。

### `checkUserExists(@PathVariable Long id)`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/users/{id}/exists`
*   **摘要:** 检查用户是否存在
*   **描述:** 检查指定 ID 的用户是否存在。
*   **参数:**
    *   `id` (Long, `@PathVariable`): 用户 ID。
*   **返回值:** `Result<Boolean>` - 检查结果，存在则返回 `true`。
*   **业务逻辑:** 调用 `userService.checkUserExists()` 方法检查用户是否存在。

### `getUserStats()`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/users/stats`
*   **摘要:** 获取用户统计信息
*   **描述:** 获取包括买家和卖家数量统计在内的统计信息。
*   **权限:** `hasAuthority('admin')`
*   **参数:** 无
*   **返回值:** `Result<Map<String, Object>>` - 统计信息 Map。
*   **业务逻辑:** 调用 `userService.getUserStats()` 方法获取用户统计信息。

### `getAllUsers(@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "10") Integer size)`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/users`
*   **摘要:** 获取所有用户列表
*   **描述:** 仅管理员可访问，支持分页，返回用户分页列表，过滤掉密码等敏感信息。
*   **权限:** `hasAuthority('admin')`
*   **参数:**
    *   `page` (Integer, 默认值: 1): 当前页码。
    *   `size` (Integer, 默认值: 10): 每页大小。
*   **返回值:** `Result<Page<User>>` - 用户分页列表。
*   **业务逻辑:** 调用 `userService.getAllUsers()` 方法获取所有用户列表。

### `deleteUser(@PathVariable Long id)`
*   **HTTP 方法:** `DELETE`
*   **路径:** `/api/v1/users/{id}`
*   **摘要:** 删除用户（逻辑删除）
*   **描述:** 仅管理员可操作，对指定 ID 的用户进行逻辑删除。
*   **权限:** `hasAuthority('admin')`
*   **参数:**
    *   `id` (Long, `@PathVariable`): 待删除用户的 ID。
*   **返回值:** `Result<Void>` - 删除结果。
*   **业务逻辑:** 调用 `userService.deleteUser()` 方法逻辑删除用户。

### `logout(HttpServletResponse response)`
*   **HTTP 方法:** `POST`
*   **路径:** `/api/v1/users/logout`
*   **摘要:** 用户登出
*   **描述:** 清除 HttpOnly Cookie。
*   **参数:**
    *   `response` (HttpServletResponse): HTTP 响应对象，用于清除 Cookie。
*   **返回值:** `Result<Void>` - 登出结果。
*   **业务逻辑:** 调用 `cookieUtil.clearAllAuthCookies()` 清除所有认证相关的 Cookie。

### `refreshToken(HttpServletRequest request, HttpServletResponse response)`
*   **HTTP 方法:** `POST`
*   **路径:** `/api/v1/users/refresh-token`
*   **摘要:** 刷新 JWT token
*   **描述:** 使用 refreshToken 生成新的 token。
*   **参数:**
    *   `request` (HttpServletRequest): HTTP 请求对象，用于获取 refreshToken cookie。
    *   `response` (HttpServletResponse): HTTP 响应对象，用于设置新的 token cookie。
*   **返回值:** `Result<Void>` - 刷新结果。
*   **业务逻辑:**
    1.  从 Cookie 中获取 refreshToken。
    2.  验证 refreshToken 的有效性。
    3.  解析 refreshToken 获取用户信息。
    4.  生成新的 token。
    5.  使用 `cookieUtil` 设置新的 token cookie。

### `getCurrentUser()`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/users/me`
*   **摘要:** 获取当前登录用户信息
*   **描述:** 用于前端检查认证状态，返回当前用户的详细信息。
*   **权限:** `hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')`
*   **参数:** 无
*   **返回值:** `Result<Map<String, Object>>` - 当前用户的详细信息。
*   **业务逻辑:**
    1.  从 Spring Security 认证上下文中获取当前用户的 ID。
    2.  调用 `userService.findById()` 查询用户。
    3.  构建包含用户所有详细信息的 Map。

### `updateUserStatus(@PathVariable Long id, @RequestParam Integer status)`
*   **HTTP 方法:** `PUT`
*   **路径:** `/api/v1/users/{id}/status`
*   **摘要:** 更新用户状态
*   **描述:** 仅管理员可操作，更新指定 ID 的用户状态。
*   **权限:** `hasAuthority('admin')`
*   **参数:**
    *   `id` (Long, `@PathVariable`): 待更新用户的 ID。
    *   `status` (Integer, `@RequestParam`): 新的状态值。
*   **返回值:** `Result<Void>` - 更新结果。
*   **业务逻辑:** 调用 `userService.updateUserStatus()` 方法更新用户状态。

### `getBuyerList(@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "10") Integer size)`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/users/buyers`
*   **摘要:** 获取买家列表
*   **描述:** 支持分页查询，返回买家用户分页列表，过滤掉密码等敏感信息。
*   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder')`
*   **参数:**
    *   `page` (Integer, 默认值: 1): 当前页码。
    *   `size` (Integer, 默认值: 10): 每页大小。
*   **返回值:** `Result<Page<User>>` - 买家用户分页列表。
*   **业务逻辑:** 调用 `userService.getBuyerList()` 方法获取买家列表。

### `getSellerList(@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "10") Integer size)`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/users/sellers`
*   **摘要:** 获取卖家列表
*   **描述:** 支持分页查询，返回卖家用户分页列表，过滤掉密码等敏感信息。
*   **权限:** `hasAnyAuthority('admin', 'buyer', 'forwarder')`
*   **参数:**
    *   `page` (Integer, 默认值: 1): 当前页码。
    *   `size` (Integer, 默认值: 10): 每页大小。
*   **返回值:** `Result<Page<User>>` - 卖家用户分页列表。
*   **业务逻辑:** 调用 `userService.getSellerList()` 方法获取卖家列表。

### `getUserBasicInfo(@PathVariable Long id)`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/users/{id}/basic-info`
*   **摘要:** 获取用户基本信息（简化版）
*   **描述:** 获取指定 ID 的用户基本信息，包含 ID、用户名、头像、联系方式等。
*   **权限:** `hasAnyAuthority('admin', 'buyer', 'seller', 'forwarder')`
*   **参数:**
    *   `id` (Long, `@PathVariable`): 用户 ID。
*   **返回值:** `Result<UserBasicInfoDTO>` - 用户基本信息。
*   **业务逻辑:** 调用 `userService.getUserBasicInfo()` 方法获取用户基本信息。

### `updateCompanyInfo(@PathVariable Long id, @RequestBody CompanyInfoDTO companyInfoDTO)`
*   **HTTP 方法:** `POST`
*   **路径:** `/api/v1/users/{id}/company-info`
*   **摘要:** 更新用户公司信息
*   **描述:** 仅管理员或用户自己可操作，更新指定 ID 的用户公司信息。
*   **权限:** `hasAnyAuthority('admin','seller', 'forwarder')`
*   **参数:**
    *   `id` (Long, `@PathVariable`): 待更新用户的 ID。
    *   `companyInfoDTO` (CompanyInfoDTO, `@RequestBody`): 包含公司信息的数据传输对象。
*   **返回值:** `Result<Void>` - 更新结果。
*   **业务逻辑:**
    1.  获取当前登录用户的 ID 和角色。
    2.  调用 `userService.updateCompanyInfo()` 方法更新公司信息。

### `getCompanyInfo(@PathVariable Long id)`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/users/{id}/company-info`
*   **摘要:** 获取用户公司信息
*   **描述:** 获取指定 ID 的用户公司详细信息。
*   **参数:**
    *   `id` (Long, `@PathVariable`): 用户 ID。
*   **返回值:** `Result<CompanyInfoDTO>` - 公司详细信息。
*   **业务逻辑:**
    1.  查询用户。
    2.  权限检查：只有管理员、用户自己或已建立业务关系的用户才能查看公司信息。
    3.  构建 `CompanyInfoDTO` 并返回。

## 使用示例 (Usage Examples)

```java
// 1. 前端JavaScript调用示例
const UserAPI = {
    // 用户注册
    async register(userData) {
        const response = await fetch('/api/v1/users/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: userData.username,
                password: userData.password,
                email: userData.email,
                phone: userData.phone,
                realName: userData.realName,
                userType: userData.userType || 'buyer',
                companyName: userData.companyName,
                companyAddress: userData.companyAddress
            })
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('注册成功，请登录');
            window.location.href = '/login';
            return result.data;
        } else {
            showErrorMessage('注册失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 用户登录
    async login(credential, password) {
        const response = await fetch('/api/v1/users/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                credential: credential, // 可以是用户名或邮箱
                password: password
            }),
            credentials: 'include' // 重要：包含cookies
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('登录成功');
            localStorage.setItem('userInfo', JSON.stringify(result.data.user));
            window.location.href = '/dashboard';
            return result.data;
        } else {
            showErrorMessage('登录失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 获取当前用户信息
    async getCurrentUser() {
        const response = await fetch('/api/v1/users/me', {
            method: 'GET',
            credentials: 'include' // 包含HttpOnly Cookie
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            if (result.code === 401) {
                window.location.href = '/login';
            }
            throw new Error(result.message);
        }
    },

    // 更新用户信息
    async updateUserInfo(userInfo) {
        const response = await fetch('/api/v1/users/update-info', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userInfo),
            credentials: 'include'
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('信息更新成功');
            localStorage.setItem('userInfo', JSON.stringify(result.data));
            return result.data;
        } else {
            showErrorMessage('更新失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 修改密码
    async changePassword(oldPassword, newPassword) {
        const response = await fetch('/api/v1/users/change-password', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                oldPassword: oldPassword,
                newPassword: newPassword
            }),
            credentials: 'include'
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('密码修改成功，请重新登录');
            localStorage.removeItem('userInfo');
            window.location.href = '/login';
            return result.data;
        } else {
            showErrorMessage('密码修改失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 用户登出
    async logout() {
        try {
            const response = await fetch('/api/v1/users/logout', {
                method: 'POST',
                credentials: 'include'
            });

            localStorage.removeItem('userInfo');
            showSuccessMessage('已安全退出');
            window.location.href = '/login';
        } catch (error) {
            localStorage.removeItem('userInfo');
            window.location.href = '/login';
        }
    }
};

// 2. Java客户端调用示例
@Service
public class UserClientService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${api.base-url}")
    private String baseUrl;

    // 用户注册
    public UserDTO registerUser(UserRegistrationRequest request) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<UserRegistrationRequest> entity = new HttpEntity<>(request, headers);

        try {
            ResponseEntity<Result<UserDTO>> response = restTemplate.exchange(
                baseUrl + "/api/v1/users/register",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<Result<UserDTO>>() {}
            );

            Result<UserDTO> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("用户注册失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用用户注册API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }

    // 用户登录
    public LoginResponse loginUser(String credential, String password) {
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setCredential(credential);
        loginRequest.setPassword(password);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<LoginRequest> entity = new HttpEntity<>(loginRequest, headers);

        try {
            ResponseEntity<Result<LoginResponse>> response = restTemplate.exchange(
                baseUrl + "/api/v1/users/login",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<Result<LoginResponse>>() {}
            );

            Result<LoginResponse> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("用户登录失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用用户登录API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }

    // 获取用户信息
    public UserDTO getUserById(Long userId, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);

        HttpEntity<?> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<Result<UserDTO>> response = restTemplate.exchange(
                baseUrl + "/api/v1/users/" + userId,
                HttpMethod.GET,
                entity,
                new ParameterizedTypeReference<Result<UserDTO>>() {}
            );

            Result<UserDTO> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("获取用户信息失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用获取用户信息API失败: userId={}", userId, e);
            throw new SystemException("网络请求失败", e);
        }
    }
}

// 3. 业务服务集成示例
@Service
public class UserManagementService {

    @Autowired
    private UserService userService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private AuditLogService auditLogService;

    // 用户注册后的业务流程
    @Async
    public void handleUserRegistered(UserDTO user) {
        try {
            // 1. 发送欢迎邮件
            notificationService.sendWelcomeEmail(user);

            // 2. 创建用户默认设置
            createDefaultUserSettings(user.getId());

            // 3. 记录注册日志
            auditLogService.logUserRegistration(user);

            // 4. 如果是企业用户，创建企业资料
            if ("seller".equals(user.getUserType()) || "forwarder".equals(user.getUserType())) {
                initializeCompanyProfile(user.getId());
            }

            log.info("用户注册后处理完成: userId={}", user.getId());

        } catch (Exception e) {
            log.error("用户注册后处理失败: userId={}", user.getId(), e);
        }
    }

    // 用户登录后的业务流程
    @Async
    public void handleUserLoggedIn(UserDTO user, String loginIp) {
        try {
            // 1. 更新最后登录时间
            userService.updateLastLoginTime(user.getId());

            // 2. 记录登录日志
            auditLogService.logUserLogin(user.getId(), loginIp);

            // 3. 检查用户状态
            if ("inactive".equals(user.getStatus())) {
                // 激活长期未登录的用户
                userService.activateUser(user.getId());
            }

            // 4. 发送登录通知（如果启用）
            if (user.isLoginNotificationEnabled()) {
                notificationService.sendLoginNotification(user, loginIp);
            }

            log.info("用户登录后处理完成: userId={}", user.getId());

        } catch (Exception e) {
            log.error("用户登录后处理失败: userId={}", user.getId(), e);
        }
    }
}

// 4. 定时任务示例
@Component
public class UserScheduledTasks {

    @Autowired
    private UserService userService;

    // 清理过期的验证码
    @Scheduled(cron = "0 */10 * * * ?") // 每10分钟执行一次
    public void cleanExpiredVerificationCodes() {
        log.info("开始清理过期验证码");

        try {
            int cleanedCount = userService.cleanExpiredVerificationCodes();
            log.info("清理过期验证码完成，清理数量: {}", cleanedCount);
        } catch (Exception e) {
            log.error("清理过期验证码失败", e);
        }
    }

    // 检查长期未登录用户
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void checkInactiveUsers() {
        log.info("开始检查长期未登录用户");

        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(90);
            List<UserDTO> inactiveUsers = userService.findUsersNotLoggedInSince(cutoffDate);

            for (UserDTO user : inactiveUsers) {
                // 发送提醒邮件
                notificationService.sendInactivityReminder(user);

                // 如果超过180天未登录，标记为非活跃
                LocalDateTime longInactiveCutoff = LocalDateTime.now().minusDays(180);
                if (user.getLastLoginTime().isBefore(longInactiveCutoff)) {
                    userService.markUserAsInactive(user.getId());
                }
            }

            log.info("长期未登录用户检查完成，处理用户数: {}", inactiveUsers.size());

        } catch (Exception e) {
            log.error("检查长期未登录用户失败", e);
        }
    }

    // 生成用户统计报告
    @Scheduled(cron = "0 0 8 * * MON") // 每周一早上8点
    public void generateWeeklyUserReport() {
        log.info("开始生成周度用户报告");

        try {
            WeeklyUserReport report = new WeeklyUserReport();
            report.setReportPeriod("最近一周");
            report.setNewRegistrations(userService.countNewRegistrationsThisWeek());
            report.setActiveUsers(userService.countActiveUsersThisWeek());
            report.setTotalUsers(userService.countTotalUsers());
            report.setUserTypeDistribution(userService.getUserTypeDistribution());

            // 发送报告给管理员
            notificationService.sendWeeklyUserReport(report);

            log.info("周度用户报告生成完成");

        } catch (Exception e) {
            log.error("生成周度用户报告失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class UserControllerTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @MockBean
    private UserService userService;

    @MockBean
    private JwtUtil jwtUtil;

    @Test
    void testUserRegistration() {
        // 准备测试数据
        UserRegistrationRequest request = new UserRegistrationRequest();
        request.setUsername("testuser");
        request.setPassword("password123");
        request.setEmail("<EMAIL>");
        request.setRealName("测试用户");
        request.setUserType("buyer");

        UserDTO expectedUser = new UserDTO();
        expectedUser.setId(1L);
        expectedUser.setUsername("testuser");
        expectedUser.setEmail("<EMAIL>");

        // Mock服务调用
        when(userService.registerUser(any(UserRegistrationRequest.class)))
            .thenReturn(expectedUser);

        // 执行测试
        HttpEntity<UserRegistrationRequest> entity = new HttpEntity<>(request);
        ResponseEntity<Result> response = restTemplate.postForEntity(
            "/api/v1/users/register", entity, Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(userService, times(1)).registerUser(any(UserRegistrationRequest.class));
    }

    @Test
    void testUserLogin() {
        // 准备测试数据
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setCredential("testuser");
        loginRequest.setPassword("password123");

        UserDTO user = new UserDTO();
        user.setId(1L);
        user.setUsername("testuser");

        LoginResponse expectedResponse = new LoginResponse();
        expectedResponse.setUser(user);
        expectedResponse.setToken("mock-jwt-token");

        // Mock服务调用
        when(userService.login(anyString(), anyString())).thenReturn(expectedResponse);
        when(jwtUtil.generateToken(any(UserDTO.class))).thenReturn("mock-jwt-token");

        // 执行测试
        HttpEntity<LoginRequest> entity = new HttpEntity<>(loginRequest);
        ResponseEntity<Result> response = restTemplate.postForEntity(
            "/api/v1/users/login", entity, Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(userService, times(1)).login(anyString(), anyString());
    }

    @Test
    void testGetCurrentUser() {
        // 准备测试数据
        UserDTO currentUser = new UserDTO();
        currentUser.setId(1L);
        currentUser.setUsername("testuser");

        // Mock服务调用
        when(userService.getCurrentUser()).thenReturn(currentUser);

        // 执行测试
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer mock-jwt-token");
        HttpEntity<?> entity = new HttpEntity<>(headers);

        ResponseEntity<Result> response = restTemplate.exchange(
            "/api/v1/users/me", HttpMethod.GET, entity, Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(userService, times(1)).getCurrentUser();
    }
}
```

## 注意事项 (Notes)
*   **权限控制**: 使用Spring Security的@PreAuthorize注解进行权限验证，确保只有相应角色的用户才能访问对应功能
*   **JWT认证**: 使用JWT进行用户认证，并通过HttpOnly Cookie存储JWT，提高安全性，防止XSS攻击
*   **密码安全**: 密码在存储前进行BCrypt加密处理，确保用户密码的安全性，永不明文存储
*   **邮箱验证**: 支持通过邮箱验证码进行注册和登录，提高账户安全性，防止恶意注册
*   **数据验证**: 使用@Valid注解对请求参数进行验证，确保数据的完整性和有效性
*   **异常处理**: 使用统一的异常处理机制，确保在出现错误时返回友好的错误信息
*   **响应格式**: 所有接口都返回统一的Result<T>格式，确保前端能够统一处理响应数据
*   **Cookie管理**: 使用CookieUtil工具类管理Cookie的设置和清除，确保Cookie的安全性和一致性
*   **业务委托**: 控制器只负责HTTP请求处理和响应封装，具体业务逻辑委托给UserService处理
*   **日志记录**: 使用@Slf4j注解记录关键操作的日志信息，便于调试和监控
*   **会话管理**: 实现完整的用户会话管理，包括登录、登出、token刷新等功能
*   **并发控制**: 用户信息更新操作需要考虑并发控制，避免数据不一致问题
*   **缓存策略**: 用户信息等频繁查询的数据可以考虑使用缓存提高性能
*   **审计日志**: 重要的用户操作需要记录审计日志，满足合规要求
*   **国际化**: 用户界面和错误消息需要支持多语言，便于国际化部署
*   **安全考虑**: 用户数据涉及个人隐私，需要严格的权限控制和数据脱敏处理
*   **Swagger注解**: 使用@Tag和@Operation注解为Swagger/OpenAPI文档提供清晰的接口说明
