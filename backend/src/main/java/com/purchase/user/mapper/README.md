# com.purchase.user.mapper 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.mapper` 包包含了采购系统后端应用程序中与用户相关的 MyBatis Mapper 接口。这些接口定义了与数据库中用户、邮箱验证码和提现记录表进行交互的方法。通过继承 MyBatis-Plus 的 `BaseMapper`，它们获得了基本的 CRUD（创建、读取、更新、删除）操作，并在此基础上扩展了针对特定业务需求的自定义查询和更新方法。该包是数据持久化层与业务逻辑层之间的桥梁。

## 目录结构概览 (Directory Structure Overview)
*   `EmailVerificationCodeMapper.java`: 邮件验证码表的 Mapper 接口。
*   `UserMapper.java`: 用户表的 Mapper 接口。
*   `WithdrawalRecordMapper.java`: 提现记录表的 Mapper 接口。
*   `EmailVerificationCodeMapper.md`: `EmailVerificationCodeMapper.java` 的文档。
*   `UserMapper.md`: `UserMapper.java` 的文档。
*   `WithdrawalRecordMapper.md`: `WithdrawalRecordMapper.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.mapper` 包中的 Mapper 接口与 `com.purchase.user.entity` 包中的实体类以及 `com.purchase.user.service` 包中的服务层紧密协作，共同实现了用户模块的数据持久化功能：

1.  **实体与 Mapper 的映射:** 每个 Mapper 接口都通过泛型参数与一个具体的实体类（例如 `UserMapper` 对应 `User` 实体）关联。MyBatis-Plus 会根据这种关联自动生成大部分 SQL 操作。

2.  **继承 `BaseMapper`:** 所有 Mapper 接口都继承自 `com.baomidou.mybatisplus.core.mapper.BaseMapper`。这使得它们无需编写任何 SQL 即可获得对相应实体进行基本的单表 CRUD 操作的能力，极大地提高了开发效率。

3.  **自定义 SQL 操作:** 对于 `BaseMapper` 不支持的复杂查询或特定业务逻辑，可以在 Mapper 接口中定义自定义方法，并通过 `@Select`、`@Update`、`@Insert`、`@Delete` 等注解直接编写 SQL 语句，或者在对应的 XML Mapper 文件中定义 SQL（例如 `src/main/resources/mapper/UserMapper.xml`）。例如：
    *   `UserMapper` 包含了根据邀请码查询用户、查询直接邀请用户列表、统计邀请码数量以及查询特定角色用户等自定义方法。
    *   `EmailVerificationCodeMapper` 包含了查询最新有效验证码、统计发送数量和标记验证码为已使用等方法。

4.  **与服务层的交互:** 服务层（如 `UserService`, `EmailService`, `WithdrawalService`）通过 `@Autowired` 注入这些 Mapper 接口的实例。服务层负责调用 Mapper 接口的方法来执行数据库操作，然后处理业务逻辑，并将结果返回给控制器层或上游调用者。

**协作流程总结:**
当服务层需要对用户数据进行操作时，它会调用相应的 Mapper 接口方法。这些 Mapper 方法（无论是继承自 `BaseMapper` 还是自定义的）会与 MyBatis-Plus 框架和底层的 JDBC 驱动程序协作，将操作转换为实际的 SQL 语句并发送到数据库执行。数据库执行结果会再通过 Mapper 接口返回给服务层。这种分层设计确保了业务逻辑与数据访问逻辑的清晰分离，提高了代码的可维护性和可测试性。
