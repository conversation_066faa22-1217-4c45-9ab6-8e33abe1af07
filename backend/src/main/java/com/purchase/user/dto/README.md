# com.purchase.user.dto 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.dto` 包包含了采购系统后端应用程序中与用户相关的各种数据传输对象（DTO）。这些 DTO 旨在封装和标准化前端与后端之间传输的数据格式，涵盖了用户认证（登录、注册、密码重置）、用户信息管理、以及提现请求和记录等。通过使用 DTO，该包实现了数据传输的清晰性、类型安全性和验证机制，从而简化了 API 接口的定义和维护。

## 目录结构概览 (Directory Structure Overview)
*   `AdminLoginDTO.java`: 管理员登录请求的数据封装。
*   `CompanyInfoDTO.java`: 用户公司信息的封装，用于更新或展示公司详情。
*   `EmailLoginDTO.java`: 邮箱登录请求的数据封装（邮箱+验证码）。
*   `EmailRegisterDTO.java`: 邮箱注册请求的数据封装（用户名+邮箱+密码+验证码）。
*   `EmailVerificationDTO.java`: 邮箱验证请求的数据封装（邮箱+验证码+类型）。
*   `ResetPasswordByEmailDTO.java`: 通过邮箱重置密码请求的数据封装。
*   `UserBasicInfoDTO.java`: 用户基本信息的简化视图，用于概览展示。
*   `UserDetailDTO.java`: 用户详细信息的完整视图，包含个人和公司所有资料。
*   `UserLoginDTO.java`: 用户登录请求的数据封装（用户名/邮箱+密码）。
*   `UserRegisterDTO.java`: 用户注册请求的数据封装（用户名+密码+邮箱）。
*   `UserSimpleDTO.java`: 用户的简化信息，用于列表展示，包含业务指标。
*   `WithdrawalRecordDTO.java`: 提现记录的详细信息封装。
*   `WithdrawalRequestDTO.java`: 用户提现申请请求的数据封装。
*   `AdminLoginDTO.md`: `AdminLoginDTO.java` 的文档。
*   `CompanyInfoDTO.md`: `CompanyInfoDTO.java` 的文档。
*   `EmailLoginDTO.md`: `EmailLoginDTO.java` 的文档。
*   `EmailRegisterDTO.md`: `EmailRegisterDTO.java` 的文档。
*   `EmailVerificationDTO.md`: `EmailVerificationDTO.java` 的文档。
*   `ResetPasswordByEmailDTO.md`: `ResetPasswordByEmailDTO.java` 的文档。
*   `UserBasicInfoDTO.md`: `UserBasicInfoDTO.java` 的文档。
*   `UserDetailDTO.md`: `UserDetailDTO.java` 的文档。
*   `UserLoginDTO.md`: `UserLoginDTO.java` 的文档。
*   `UserRegisterDTO.md`: `UserRegisterDTO.java` 的文档。
*   `UserSimpleDTO.md`: `UserSimpleDTO.java` 的文档。
*   `WithdrawalRecordDTO.md`: `WithdrawalRecordDTO.java` 的文档。
*   `WithdrawalRequestDTO.md`: `WithdrawalRequestDTO.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.dto` 包中的 DTOs 主要作为数据载体，在应用程序的不同层之间（特别是控制器层和服务层）以及前端与后端之间传递数据。它们与 `com.purchase.user.controller` 包中的控制器和 `com.purchase.user.service` 包中的服务层紧密协作：

1.  **请求数据验证:** 大多数用于接收前端输入的 DTO（如 `AdminLoginDTO`, `EmailLoginDTO`, `EmailRegisterDTO`, `ResetPasswordByEmailDTO`, `UserLoginDTO`, `UserRegisterDTO`, `WithdrawalRequestDTO`）都使用了 JSR 303/349（Bean Validation）注解（如 `@NotBlank`, `@Email`, `@Pattern`, `@Size`, `@NotNull`, `@DecimalMin`）。这些注解与控制器层（通过 `@Validated` 或 `@Valid`）结合使用，确保在业务逻辑处理之前，传入的数据已经过初步的合法性检查。

2.  **响应数据封装:** 用于返回给前端的 DTO（如 `UserBasicInfoDTO`, `UserDetailDTO`, `UserSimpleDTO`, `WithdrawalRecordDTO`, `CompanyInfoDTO`）封装了从数据库实体或其他业务逻辑中获取的数据。服务层负责将领域实体转换为这些 DTO，然后控制器层将这些 DTO 包装在 `com.purchase.common.response.Result` 对象中返回给客户端。这种分离确保了领域模型不直接暴露给外部，提高了系统的安全性和灵活性。

3.  **特定场景的数据视图:**
    *   `UserBasicInfoDTO` 和 `UserDetailDTO` 提供了用户信息的不同粒度视图，满足不同前端页面或权限级别对用户数据展示的需求。
    *   `UserSimpleDTO` 进一步简化了用户信息，并加入了 `hasOrder` 和 `contribution` 等业务指标，适用于用户列表或邀请列表等场景。
    *   `WithdrawalRequestDTO` 和 `WithdrawalRecordDTO` 分别对应提现的请求和响应数据，清晰地定义了提现流程中数据流的结构。

**协作流程总结:**
前端发送请求时，请求体中的 JSON 数据会被 Spring MVC 自动绑定到相应的 DTO 对象上，并触发 DTO 中定义的验证规则。验证通过后，控制器将 DTO 传递给服务层。服务层根据业务需求，可能会将 DTO 转换为领域实体进行操作，或者直接使用 DTO 中的数据。在返回响应时，服务层会将处理结果（通常是领域实体）转换为相应的响应 DTO，再由控制器封装为统一的 `Result` 对象返回给前端。这种模式确保了数据在整个应用程序生命周期中的结构化和一致性。
