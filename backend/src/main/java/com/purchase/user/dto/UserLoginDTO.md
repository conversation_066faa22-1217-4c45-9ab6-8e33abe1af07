# UserLoginDTO.java

## 文件概述 (File Overview)
`UserLoginDTO.java` 是用户登录的数据传输对象，位于 `com.purchase.user.dto` 包中。该类定义了用户登录时需要传递的所有参数和数据结构，通过Bean Validation注解提供数据验证功能，确保登录请求数据的完整性和安全性。作为用户认证系统的核心DTO，它封装了用户登录所需的凭证信息，包括用户名、密码、验证码等认证数据，并提供了完善的安全验证机制。

## 核心功能 (Core Functionality)
*   **登录凭证传输**: 封装用户登录所需的所有认证信息
*   **数据验证**: 通过Bean Validation注解提供字段级别的安全验证
*   **类型安全**: 提供强类型的数据结构，确保认证数据的准确性
*   **序列化支持**: 支持JSON序列化和反序列化，便于前后端认证交互
*   **安全设计**: 采用安全的数据传输设计，保护用户凭证信息
*   **验证码支持**: 集成验证码验证功能，增强登录安全性
*   **多种登录方式**: 支持用户名、邮箱、手机号等多种登录方式
*   **记住登录**: 支持记住登录状态的功能选项
*   **设备信息**: 可选包含设备信息，用于安全审计
*   **IP地址记录**: 记录登录IP地址，用于安全监控
*   **时间戳**: 包含请求时间戳，防止重放攻击
*   **错误处理**: 提供详细的验证错误信息，便于用户修正

## 业务规则 (Business Rules)
*   **必填字段**: 用户名和密码是必填字段，不能为空
*   **格式验证**: 用户名、密码必须符合预定义的格式要求
*   **长度限制**: 各字段都有相应的长度限制，防止恶意输入
*   **安全规则**: 密码传输需要加密，不能明文传输
*   **验证码**: 在特定情况下需要提供验证码
*   **频率限制**: 登录尝试有频率限制，防止暴力破解
*   **账户状态**: 只有正常状态的账户才能登录
*   **多设备**: 支持多设备同时登录或限制单设备登录

## 注意事项 (Notes)
*   **密码安全**: 密码字段需要特殊处理，避免日志记录和缓存
*   **数据验证**: 使用Bean Validation注解进行严格的数据验证
*   **序列化安全**: 确保敏感字段在序列化时得到适当保护
*   **空值处理**: 合理处理可选字段的空值情况
*   **类型转换**: 注意数据类型的正确转换，特别是密码字段
*   **安全传输**: 确保登录数据通过HTTPS等安全协议传输
*   **版本兼容**: 字段变更需要考虑客户端兼容性
*   **审计日志**: 登录尝试需要记录审计日志，但不包含密码
*   **性能考虑**: 避免包含不必要的字段，减少传输开销
*   **错误信息**: 提供安全的错误信息，不泄露系统内部信息
