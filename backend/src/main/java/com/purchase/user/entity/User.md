# User.java

## 文件概述 (File Overview)
`User.java` 是用户实体类，位于 `com.purchase.user.entity` 包中，使用MyBatis-Plus注解进行ORM映射。该实体类定义了系统用户的完整数据模型，包含用户基本信息、认证信息、角色权限、状态管理等核心属性。通过完善的字段设计和约束定义，支持多角色用户管理（买家、卖家、管理员等），实现了用户数据的标准化存储和高效查询，并提供了完善的数据验证和安全保护机制。

## 核心功能 (Core Functionality)
*   **用户身份管理**: 存储用户的基本身份信息，包括用户名、邮箱、手机号等
*   **认证信息存储**: 安全存储用户密码、登录凭证等认证相关信息
*   **角色权限定义**: 支持多种用户角色，包括买家、卖家、管理员等不同权限级别
*   **用户状态管理**: 管理用户账户状态，包括正常、禁用、删除等状态
*   **个人信息维护**: 存储用户的详细个人信息，支持信息更新和维护
*   **安全设置**: 包含安全相关设置，如密码策略、登录限制等
*   **业务关联**: 与订单、需求、竞价等业务实体建立关联关系
*   **统计信息**: 记录用户的活跃度、交易统计等业务数据
*   **审计追踪**: 记录用户数据的创建、修改时间等审计信息
*   **扩展属性**: 支持用户自定义属性和业务扩展字段
*   **数据验证**: 提供完整的数据验证规则和约束检查
*   **索引优化**: 合理设计数据库索引，提高查询性能

## 业务规则 (Business Rules)
*   **唯一性约束**: 用户名、邮箱、手机号等关键字段必须唯一
*   **角色分类**: 用户角色分为买家、卖家、管理员等，权限不同
*   **状态管理**: 用户状态包括正常、禁用、删除，状态变更需要记录
*   **密码安全**: 密码必须加密存储，符合安全规范要求
*   **实名认证**: 部分功能需要用户完成实名认证才能使用
*   **数据保护**: 用户敏感信息需要加密存储，保护用户隐私
*   **审计要求**: 用户数据变更需要记录操作日志，便于审计
*   **合规性**: 遵守数据保护法规，保护用户个人信息安全

## 注意事项 (Notes)
*   **数据安全**: 用户密码等敏感信息必须加密存储，不能明文保存
*   **索引设计**: 合理设计数据库索引，提高用户查询和认证性能
*   **字段验证**: 使用Bean Validation注解进行数据验证，确保数据完整性
*   **软删除**: 建议使用软删除机制，保留用户历史数据便于审计
*   **并发控制**: 用户信息更新需要考虑并发控制，防止数据冲突
*   **缓存策略**: 用户信息可以适当缓存，提高系统性能
*   **数据迁移**: 用户数据结构变更需要考虑数据迁移和兼容性
*   **隐私保护**: 严格控制用户信息的访问权限，保护用户隐私
*   **审计日志**: 记录用户数据的变更历史，便于问题追踪
*   **性能优化**: 大量用户数据需要考虑分库分表等性能优化策略
