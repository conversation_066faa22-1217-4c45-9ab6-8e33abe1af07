# com.purchase.user.entity 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.entity` 包包含了采购系统后端应用程序中与用户相关的核心持久化实体类。这些实体类直接映射到数据库表，定义了用户、邮箱验证码和提现记录的数据结构。它们是应用程序领域模型的基础，承载了业务数据，并与 MyBatis-Plus 框架集成，简化了数据库操作。

## 目录结构概览 (Directory Structure Overview)
*   `EmailVerificationCode.java`: 邮箱验证码实体，用于存储发送给用户的验证码信息。
*   `User.java`: 用户实体，定义了系统中用户的完整数据模型，包括基本信息、公司信息和邀请关系。
*   `WithdrawalRecord.java`: 提现记录实体，用于存储用户的提现请求和审批信息。
*   `EmailVerificationCode.md`: `EmailVerificationCode.java` 的文档。
*   `User.md`: `User.java` 的文档。
*   `WithdrawalRecord.md`: `WithdrawalRecord.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.entity` 包中的实体类是应用程序数据持久化层的核心。它们与 `com.purchase.user.mapper` 包中的 Mapper 接口以及 `com.purchase.config` 包中的 MyBatis-Plus 配置紧密协作：

1.  **数据映射:** 每个实体类（如 `User`, `EmailVerificationCode`, `WithdrawalRecord`）都通过 `@TableName` 注解映射到数据库中的相应表。字段通过属性名与表列名进行映射，或者通过 `@TableId` 等注解进行特殊配置。

2.  **MyBatis-Plus 集成:**
    *   `@TableId(type = IdType.AUTO)` 用于配置主键的生成策略（例如，数据库自增）。
    *   `@TableField(fill = FieldFill.INSERT)` 和 `@TableField(fill = FieldFill.INSERT_UPDATE)` 与 `com.purchase.config.MyMetaObjectHandler` 配合使用，实现 `createdAt` 和 `updatedAt` 等公共字段的自动填充。
    *   `@TableLogic` 注解（在 `User` 和 `WithdrawalRecord` 中）实现了逻辑删除功能，使得数据在数据库中被标记为删除而不是物理删除，这对于数据恢复和审计非常有用。

3.  **业务数据载体:** 这些实体类是业务逻辑层（Service 层）操作数据的基本单位。例如，`UserService` 会操作 `User` 实体来执行用户注册、更新等操作；`EmailService` 会操作 `EmailVerificationCode` 来管理验证码的生命周期；`WithdrawalService` 会操作 `WithdrawalRecord` 来处理提现请求。

4.  **与 DTO 的转换:** 实体类通常不会直接暴露给前端。在数据传输时，它们会被转换为 `com.purchase.user.dto` 包中的相应 DTO 对象（例如，`User` 转换为 `UserBasicInfoDTO` 或 `UserDetailDTO`），以提供更精简或定制化的数据视图，并隐藏敏感信息。

**协作流程总结:**
当业务操作需要与数据库交互时，服务层会创建或修改实体对象。这些实体对象通过 Mapper 接口（由 MyBatis-Plus 提供）被持久化到数据库。在持久化过程中，`MyMetaObjectHandler` 会自动填充时间戳和逻辑删除标志。从数据库读取数据时，Mapper 接口会返回实体对象，然后服务层可能会将这些实体对象转换为 DTOs，再由控制器返回给前端。这种模式确保了数据在应用程序内部的结构化管理和外部接口的清晰分离。
