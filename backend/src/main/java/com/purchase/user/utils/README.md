# com.purchase.user.utils 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.utils` 包包含了采购系统后端应用程序中与用户相关的通用工具类。这些工具类旨在提供可重用的辅助功能，特别是与邀请码生成和验证相关的逻辑。通过将这些通用功能封装在独立的工具类中，该包提高了代码的模块化、可维护性和开发效率，并确保了特定业务逻辑的正确实现。

## 目录结构概览 (Directory Structure Overview)
*   `InviteCodeGenerator.java`: 邀请码生成和验证的工具类。
*   `InviteCodeGenerator.md`: `InviteCodeGenerator.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.utils` 包中的工具类与 `com.purchase.user.service` 包中的服务层以及 `com.purchase.user.mapper` 包中的数据访问层紧密协作，共同实现了用户邀请功能：

1.  **邀请码生成与验证:**
    *   `InviteCodeGenerator` 提供了生成随机邀请码和验证邀请码格式合法性的核心功能。它使用加密安全的随机数和 UUID 来确保生成邀请码的随机性和唯一性（在格式层面）。
    *   `InviteCodeService` 的实现类（`com.purchase.user.service.impl.InviteCodeServiceImpl`）会调用 `InviteCodeGenerator.generateInviteCode()` 来获取一个候选邀请码，然后通过 `com.purchase.user.mapper.UserMapper` 查询数据库来验证其在系统中的真正唯一性。同样，在验证邀请码时，`InviteCodeServiceImpl` 会先调用 `InviteCodeGenerator.isValidInviteCode()` 进行格式验证，再进行数据库查询。

2.  **与服务层的协作:**
    *   `InviteCodeService` 接口（位于 `com.purchase.user.service` 包）定义了邀请码服务的契约，而 `InviteCodeServiceImpl` 则实现了这些契约，并在其内部使用了 `InviteCodeGenerator`。
    *   `UserServiceImpl`（也位于 `com.purchase.user.service.impl` 包）在用户注册时会调用 `InviteCodeService` 来生成和验证邀请码，从而建立用户之间的邀请关系。

**协作流程总结:**

*   当一个新用户注册时，如果需要为该用户生成邀请码，`UserServiceImpl` 会调用 `InviteCodeService.generateUniqueInviteCode()`。
*   `InviteCodeServiceImpl` 在内部会循环调用 `InviteCodeGenerator.generateInviteCode()` 生成一个随机码，并通过 `UserMapper` 查询数据库来确保这个码是全局唯一的。一旦找到唯一的码，它就会返回给 `UserServiceImpl`。
*   如果新用户是通过邀请码注册的，`UserServiceImpl` 会调用 `InviteCodeService.validateInviteCode()` 来验证传入的邀请码是否有效。`InviteCodeServiceImpl` 会先使用 `InviteCodeGenerator.isValidInviteCode()` 进行格式检查，然后通过 `UserMapper` 检查邀请码是否存在且有效。

这种工具类设计模式确保了邀请码生成和验证逻辑的封装和重用，同时将其与核心业务逻辑（如用户注册）分离，提高了代码的清晰度和可维护性。