# com.purchase.user.paymentaccount.infrastructure.repository 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.infrastructure.repository` 包包含了收款账户模块领域仓储接口的具体实现。在领域驱动设计（DDD）中，仓储实现位于基础设施层，负责将领域层定义的抽象数据访问契约与底层具体的持久化技术（如 MyBatis-Plus）进行集成。该包的核心是 `PaymentAccountRepositoryImpl`，它通过与 MyBatis Mapper 和领域-持久化转换器协作，实现了 `PaymentAccount` 聚合根的持久化和检索功能，从而将领域模型与数据库细节解耦。

## 目录结构概览 (Directory Structure Overview)
*   `PaymentAccountRepositoryImpl.java`: `PaymentAccountRepository` 接口的具体实现类。
*   `PaymentAccountRepositoryImpl.md`: `PaymentAccountRepositoryImpl.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.infrastructure.repository` 包中的仓储实现是连接领域层和数据库的关键组件。它与以下组件紧密协作：

1.  **领域仓储接口 (`com.purchase.user.paymentaccount.domain.repository.PaymentAccountRepository`):**
    *   `PaymentAccountRepositoryImpl` 实现了领域层定义的 `PaymentAccountRepository` 接口。这意味着它履行了领域层对数据持久化的所有要求，但具体如何实现这些要求则由基础设施层决定。

2.  **MyBatis Mapper (`com.purchase.user.paymentaccount.infrastructure.mapper.PaymentAccountMapper`):**
    *   `PaymentAccountRepositoryImpl` 通过注入 `PaymentAccountMapper` 来执行实际的数据库操作。`PaymentAccountMapper` 是一个 MyBatis Mapper 接口，它直接与数据库表 `user_payment_account` 交互，并操作 `PaymentAccountPO` 持久化对象。
    *   仓储实现类会调用 Mapper 接口中定义的方法（包括继承自 `BaseMapper` 的通用方法和自定义的 SQL 方法）来执行插入、更新、查询和删除操作。

3.  **持久化对象 (`com.purchase.user.paymentaccount.infrastructure.po.PaymentAccountPO`):**
    *   `PaymentAccountRepositoryImpl` 不直接操作领域实体 `PaymentAccount` 进行数据库交互。相反，它操作的是 `PaymentAccountPO`。这是因为 `PaymentAccountPO` 是与数据库表结构紧密耦合的 POJO，更适合持久化框架处理。

4.  **领域-持久化转换器 (`com.purchase.user.paymentaccount.infrastructure.converter.PaymentAccountConverter`):**
    *   `PaymentAccountRepositoryImpl` 通过注入 `PaymentAccountConverter` 来实现 `PaymentAccount` 领域实体与 `PaymentAccountPO` 持久化对象之间的双向转换。
    *   在保存数据时，它会调用 `converter.toPO()` 将领域实体转换为 POJO。
    *   在读取数据时，它会调用 `converter.toDomain()` 将 POJO 转换为领域实体。

**协作流程总结:**

*   **数据写入:** 当应用服务层（通过领域仓储接口）请求保存或更新 `PaymentAccount` 领域实体时，`PaymentAccountRepositoryImpl` 会接收到这个领域实体。它首先调用 `PaymentAccountConverter.toPO()` 将领域实体转换为 `PaymentAccountPO`。然后，它将 `PaymentAccountPO` 传递给 `PaymentAccountMapper`，由 Mapper 执行实际的数据库 `INSERT` 或 `UPDATE` 操作。在更新操作中，如果账户被设置为默认，`PaymentAccountRepositoryImpl` 还会调用 `mapper.unsetOtherDefaultAccounts()` 来维护默认账户的唯一性。
*   **数据读取:** 当应用服务层请求查询 `PaymentAccount` 领域实体时，`PaymentAccountRepositoryImpl` 会调用 `PaymentAccountMapper` 从数据库查询 `PaymentAccountPO`。然后，它调用 `PaymentAccountConverter.toDomain()` 将 `PaymentAccountPO` 转换为 `PaymentAccount` 领域实体，并将其返回给应用服务层。

这种基础设施层的仓储实现模式确保了：
*   **领域模型与持久化技术解耦:** 领域层无需关心数据如何存储，只需通过仓储接口进行操作。
*   **可替换的持久化技术:** 可以在不影响领域模型和应用服务层的情况下，更换底层持久化技术（例如从 MyBatis-Plus 切换到 JPA）。
*   **数据转换逻辑集中:** 所有的领域-持久化对象转换逻辑都集中在 `PaymentAccountConverter` 中。
