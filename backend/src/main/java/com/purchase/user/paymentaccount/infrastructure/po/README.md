# com.purchase.user.paymentaccount.infrastructure.po 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.infrastructure.po` 包包含了收款账户模块的持久化对象（POJO）。这些 POJO 直接映射到数据库表结构，是基础设施层与数据库之间进行数据交互的载体。它们通常不包含复杂的业务逻辑，主要用于数据的存储和检索。该包的核心是 `PaymentAccountPO`，它与 MyBatis-Plus 框架紧密集成，简化了数据库操作的映射配置。

## 目录结构概览 (Directory Structure Overview)
*   `PaymentAccountPO.java`: 收款账户的持久化对象，直接对应数据库表 `user_payment_account`。
*   `PaymentAccountPO.md`: `PaymentAccountPO.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.infrastructure.po` 包中的 POJO 与以下组件紧密协作：

1.  **MyBatis Mapper (`com.purchase.user.paymentaccount.infrastructure.mapper.PaymentAccountMapper`):**
    *   `PaymentAccountMapper` 接口（通常继承自 MyBatis-Plus 的 `BaseMapper`）以 `PaymentAccountPO` 作为其泛型参数，表明它操作的是这种类型的持久化对象。
    *   Mapper 接口的方法（无论是继承的还是自定义的）会直接使用 `PaymentAccountPO` 来进行数据库的插入、更新、查询和删除操作。

2.  **领域-持久化转换器 (`com.purchase.user.paymentaccount.infrastructure.converter.PaymentAccountConverter`):**
    *   `PaymentAccountConverter` 负责在领域实体（`com.purchase.user.paymentaccount.domain.entity.PaymentAccount`）和 `PaymentAccountPO` 之间进行双向转换。
    *   当数据从领域层进入持久化层时，`PaymentAccountConverter.toPO()` 方法会将 `PaymentAccount` 领域实体转换为 `PaymentAccountPO`。
    *   当数据从数据库读取并需要转换为领域实体时，`PaymentAccountConverter.toDomain()` 方法会将 `PaymentAccountPO` 转换为 `PaymentAccount` 领域实体。

3.  **MyBatis-Plus 框架:**
    *   `PaymentAccountPO` 类使用了 MyBatis-Plus 提供的注解，如 `@TableName`（指定表名）、`@TableId`（指定主键和 ID 生成策略）、`@TableField`（指定列名映射和字段填充策略）和 `@TableLogic`（指定逻辑删除字段）。这些注解使得 MyBatis-Plus 能够自动化地处理 POJO 与数据库表之间的映射关系和一些通用操作。

**协作流程总结:**

*   **数据写入数据库:** 当应用服务层需要持久化一个 `PaymentAccount` 领域实体时，它会通过领域仓储接口（`PaymentAccountRepository`）调用。仓储的实现类（`PaymentAccountRepositoryImpl`）会使用 `PaymentAccountConverter` 将领域实体转换为 `PaymentAccountPO`。然后，`PaymentAccountPO` 会被传递给 `PaymentAccountMapper`，由 MyBatis-Plus 框架将其映射为 SQL 语句并执行到数据库中。
*   **数据从数据库读取:** 当应用服务层需要从数据库检索 `PaymentAccount` 实体时，它会通过领域仓储接口调用。仓储的实现类会使用 `PaymentAccountMapper` 从数据库查询数据，返回 `PaymentAccountPO`。然后，`PaymentAccountConverter` 会将 `PaymentAccountPO` 转换为 `PaymentAccount` 领域实体，并将其返回给应用服务层。

这种模式确保了领域模型与数据库模型之间的清晰分离，使得持久化细节被封装在基础设施层，从而提高了系统的可维护性和可扩展性。
