# com.purchase.user.paymentaccount.infrastructure 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.infrastructure` 包是收款账户模块的基础设施层。在领域驱动设计（DDD）中，基础设施层负责提供技术能力，支持领域层和应用层。该包包含了与数据持久化相关的组件，如领域实体与持久化对象之间的转换器、MyBatis Mapper 接口以及领域仓储接口的具体实现。它将领域模型与底层数据库技术（MyBatis-Plus）解耦，确保了系统的可维护性、可扩展性和技术栈的灵活性。

## 目录结构概览 (Directory Structure Overview)
*   `converter/`: 包含领域实体与持久化对象之间转换的转换器，如 `PaymentAccountConverter.java`。
*   `mapper/`: 包含 MyBatis Mapper 接口，用于直接与数据库表交互，如 `PaymentAccountMapper.java`。
*   `po/`: 包含持久化对象（POJO），直接映射数据库表结构，如 `PaymentAccountPO.java`。
*   `repository/`: 包含领域仓储接口的具体实现，如 `PaymentAccountRepositoryImpl.java`。
*   `converter/README.md`: `converter` 子包的文档。
*   `mapper/README.md`: `mapper` 子包的文档。
*   `po/README.md`: `po` 子包的文档。
*   `repository/README.md`: `repository` 子包的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.infrastructure` 包中的组件协同工作，为收款账户领域模型提供了数据持久化支持：

1.  **持久化对象 (POJO):** (`po` 子包)
    *   `PaymentAccountPO` 是与数据库表 `user_payment_account` 直接对应的 POJO。它包含了数据库表中所有列的属性，并使用 MyBatis-Plus 的注解（如 `@TableName`, `@TableId`, `@TableField`, `@TableLogic`）进行映射和自动化配置（如 ID 生成、字段填充、逻辑删除）。

2.  **MyBatis Mapper 接口:** (`mapper` 子包)
    *   `PaymentAccountMapper` 是一个 MyBatis Mapper 接口，它以 `PaymentAccountPO` 作为操作对象。它继承自 MyBatis-Plus 的 `BaseMapper`，提供了基本的 CRUD 操作，并定义了针对 `PaymentAccountPO` 的自定义 SQL 查询和更新方法。

3.  **领域-持久化转换器:** (`converter` 子包)
    *   `PaymentAccountConverter` 是该层的重要组成部分。它负责在领域层实体 `PaymentAccount`（位于 `com.purchase.user.paymentaccount.domain.entity`）和基础设施层 POJO `PaymentAccountPO` 之间进行双向转换。
    *   `toPO()` 方法将领域实体转换为 POJO，处理领域模型中可能存在的复杂结构（如值对象）到扁平化 POJO 字段的映射。
    *   `toDomain()` 方法将 POJO 转换为领域实体，重新构建领域模型中的值对象和业务状态。

4.  **领域仓储实现:** (`repository` 子包)
    *   `PaymentAccountRepositoryImpl` 实现了领域层定义的 `PaymentAccountRepository` 接口。它是领域层与数据库之间的实际桥梁。
    *   在 `PaymentAccountRepositoryImpl` 内部，它通过注入 `PaymentAccountMapper` 来执行实际的数据库操作，并通过 `PaymentAccountConverter` 来完成领域实体与 POJO 之间的数据转换。
    *   它还封装了与持久化相关的业务逻辑，例如在设置默认账户时取消其他账户的默认状态。

**协作流程总结:**

*   **数据写入:** 当应用服务层（通过领域仓储接口）请求保存或更新 `PaymentAccount` 领域实体时，请求会到达 `PaymentAccountRepositoryImpl`。`PaymentAccountRepositoryImpl` 首先调用 `PaymentAccountConverter.toPO()` 将领域实体转换为 `PaymentAccountPO`。然后，它将 `PaymentAccountPO` 传递给 `PaymentAccountMapper`，由 MyBatis-Plus 框架将其映射为 SQL 语句并执行到数据库中。
*   **数据读取:** 当应用服务层请求查询 `PaymentAccount` 领域实体时，请求会到达 `PaymentAccountRepositoryImpl`。`PaymentAccountRepositoryImpl` 调用 `PaymentAccountMapper` 从数据库查询 `PaymentAccountPO`。然后，它调用 `PaymentAccountConverter.toDomain()` 将 `PaymentAccountPO` 转换为 `PaymentAccount` 领域实体，并将其返回给应用服务层。

这种基础设施层设计模式确保了：
*   **领域模型与技术细节解耦:** 领域层无需关心数据如何存储，只需通过仓储接口进行操作。
*   **持久化技术可替换性:** 可以在不影响领域模型和应用服务层的情况下，更换底层持久化技术。
*   **数据映射逻辑集中:** 所有的领域-持久化对象转换逻辑都集中在 `PaymentAccountConverter` 中。
*   **代码结构清晰:** 职责明确，易于理解和维护。
