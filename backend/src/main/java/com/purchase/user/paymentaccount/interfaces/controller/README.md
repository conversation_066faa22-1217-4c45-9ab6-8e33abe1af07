# com.purchase.user.paymentaccount.interfaces.controller 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.interfaces.controller` 包包含了收款账户模块的 RESTful API 控制器。这些控制器作为应用程序的对外接口，负责接收来自前端的 HTTP 请求，将请求数据绑定到 DTOs，调用应用服务层（`com.purchase.user.paymentaccount.application.service`）执行业务逻辑，并最终将处理结果封装为统一的 API 响应返回给客户端。该包通过 Spring MVC 和 Spring Security 的集成，确保了 API 的安全性和健壮性。

## 目录结构概览 (Directory Structure Overview)
*   `AdminPaymentAccountController.java`: 为管理员提供查看和管理用户支付账户的 API 接口。
*   `PaymentAccountController.java`: 为普通用户提供收款账户的创建、查询、更新、删除和设置默认账户等操作的 API 接口。
*   `AdminPaymentAccountController.md`: `AdminPaymentAccountController.java` 的文档。
*   `PaymentAccountController.md`: `PaymentAccountController.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.interfaces.controller` 包中的控制器与以下组件紧密协作，共同实现了收款账户模块的对外功能：

1.  **应用服务层 (`com.purchase.user.paymentaccount.application.service`):**
    *   控制器通过 `@Autowired` 注入 `PaymentAccountApplicationService` 和 `AdminPaymentAccountService` 的实例。它们是控制器与后端业务逻辑之间的主要交互点。
    *   控制器将接收到的请求 DTO 直接传递给应用服务层的方法，并接收应用服务层返回的响应 DTO。

2.  **数据传输对象 (DTOs):** (`com.purchase.user.paymentaccount.application.dto`)
    *   控制器方法使用 `CreatePaymentAccountRequest`, `UpdatePaymentAccountRequest` 等 DTO 作为 `@RequestBody` 参数来接收前端的请求数据。这些 DTO 通常会进行 JSR 303/349（Bean Validation）验证。
    *   控制器方法返回 `PaymentAccountResponse`, `UserPaymentAccountSummaryResponse` 等 DTO，这些 DTO 封装了业务处理结果，并通过 `com.purchase.common.response.ApiResponse` 进行统一包装。

3.  **Spring Security 权限控制:**
    *   控制器方法广泛使用 `@PreAuthorize` 注解来定义访问权限。例如，`AdminPaymentAccountController` 中的所有接口都要求 `admin` 角色，而 `PaymentAccountController` 中的接口则根据操作类型要求 `buyer`、`seller` 或 `forwarder` 角色。
    *   这确保了只有经过认证且具有相应权限的用户才能访问特定的 API 资源。

4.  **统一响应格式:**
    *   所有控制器方法都返回 `ResponseEntity<ApiResponse<T>>`。`ApiResponse`（来自 `com.purchase.common.response` 包）提供了一个标准化的响应结构，包含成功状态、消息和数据，便于前端统一处理。
    *   `ResponseEntity` 允许控制器灵活控制 HTTP 状态码（例如，`HttpStatus.CREATED` 用于创建成功，`HttpStatus.NO_CONTENT` 用于删除成功）。

**协作流程总结:**

*   **请求接收与验证:** 前端发送 HTTP 请求到控制器。Spring MVC 将请求体绑定到相应的 DTO，并触发 DTO 中定义的验证规则。如果验证失败，请求会在进入控制器方法之前被拦截并返回错误响应。
*   **调用应用服务:** 验证通过后，控制器从 DTO 中提取必要的数据，并调用 `PaymentAccountApplicationService` 或 `AdminPaymentAccountService` 中相应的方法来执行业务逻辑。
*   **处理业务结果:** 应用服务层返回处理结果（通常是响应 DTO）。
*   **封装响应并返回:** 控制器将响应 DTO 封装在 `ApiResponse` 中，并结合 `ResponseEntity` 设置适当的 HTTP 状态码，最终将统一格式的响应返回给前端。

这种接口层设计确保了 API 的清晰性、安全性、可维护性，并为前端提供了友好且一致的交互体验。
