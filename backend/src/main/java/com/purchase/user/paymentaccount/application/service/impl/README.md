# com.purchase.user.paymentaccount.application.service.impl 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.application.service.impl` 包包含了收款账户模块应用服务接口的具体实现。这些实现类是业务逻辑的核心，负责协调领域模型、数据转换器和持久化层，以完成收款账户的创建、查询、更新、删除、默认设置以及验证等业务用例。该包通过实现 `PaymentAccountApplicationService` 接口，提供了具体的业务流程编排和数据操作逻辑。

## 目录结构概览 (Directory Structure Overview)
*   `PaymentAccountApplicationServiceImpl.java`: `PaymentAccountApplicationService` 接口的实现类，包含了收款账户所有业务用例的具体逻辑。
*   `PaymentAccountApplicationServiceImpl.md`: `PaymentAccountApplicationServiceImpl.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.application.service.impl` 包中的服务实现类是应用服务层与领域层和基础设施层交互的中心。它们与以下组件紧密协作：

1.  **应用服务接口 (`PaymentAccountApplicationService`):** `PaymentAccountApplicationServiceImpl` 实现了该接口，提供了所有定义业务用例的具体实现。

2.  **数据转换器 (`com.purchase.user.paymentaccount.application.converter.PaymentAccountDTOConverter`):**
    *   在接收到前端请求 DTO（如 `CreatePaymentAccountRequest`, `UpdatePaymentAccountRequest`）时，`PaymentAccountApplicationServiceImpl` 会调用 `PaymentAccountDTOConverter` 的 `toEntity()` 方法将其转换为 `PaymentAccount` 领域实体。
    *   在业务逻辑处理完成后，它会再次调用 `PaymentAccountDTOConverter` 的 `toResponse()` 或 `toResponseList()` 方法将 `PaymentAccount` 领域实体转换为 `PaymentAccountResponse` DTO 返回给控制器。

3.  **领域仓储 (`com.purchase.user.paymentaccount.domain.repository.PaymentAccountRepository`):**
    *   `PaymentAccountApplicationServiceImpl` 通过注入 `PaymentAccountRepository` 来执行对 `PaymentAccount` 领域实体的持久化操作（如保存、更新、删除）和查询操作（如根据 ID 查找、根据用户 ID 查找、查找默认账户）。

4.  **领域实体 (`com.purchase.user.paymentaccount.domain.entity.PaymentAccount`):**
    *   服务实现类会调用 `PaymentAccount` 领域实体自身的方法来执行核心业务规则和状态变更，例如 `account.setAsDefault()`、`account.updateBasicInfo()`、`account.approve()`、`account.reject()`。这体现了领域驱动设计中“充血模型”的思想，将业务逻辑封装在领域实体中。

5.  **异常处理:**
    *   服务实现类会根据业务规则抛出特定的业务异常，如 `PaymentAccountNotFoundException`（账户不存在）和 `PaymentAccountAccessDeniedException`（无权访问）。这些异常通常由全局异常处理器（如 `com.purchase.common.exception.GlobalExceptionHandler`）统一捕获和处理。

6.  **事务管理:**
    *   类级别 `@Transactional` 注解确保了所有公共方法默认在事务中运行，保证了数据库操作的原子性和数据一致性。对于只读操作，会使用 `@Transactional(readOnly = true)` 进行优化。

**协作流程总结:**

`PaymentAccountApplicationServiceImpl` 作为业务流程的协调者，接收来自控制器层的 DTO 请求，将其转换为领域实体，然后调用领域实体的方法执行业务逻辑，并通过仓储进行数据持久化。在整个过程中，它处理各种业务规则和异常情况，并将最终结果转换为响应 DTO 返回。这种实现模式确保了业务逻辑的清晰、可维护和可测试性。
