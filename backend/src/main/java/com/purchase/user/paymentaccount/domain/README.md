# com.purchase.user.paymentaccount.domain 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.domain` 包是收款账户模块的领域层核心。在领域驱动设计（DDD）中，领域层包含了业务的核心逻辑、实体、值对象、领域服务和仓储接口。该包定义了收款账户的聚合根 `PaymentAccount`，以及与其相关的异常、仓储契约和值对象。它专注于封装和强制执行业务规则，确保领域模型的一致性和完整性，是应用程序业务复杂性的核心所在。

## 目录结构概览 (Directory Structure Overview)
*   `entity/`: 包含领域实体，如 `PaymentAccount.java`（收款账户聚合根）。
*   `exception/`: 包含领域层特有的业务异常，如 `PaymentAccountAccessDeniedException.java` 和 `PaymentAccountNotFoundException.java`。
*   `repository/`: 包含领域仓储接口，如 `PaymentAccountRepository.java`，定义了数据访问的契约。
*   `valueobject/`: 包含领域值对象，如 `AccountType.java`（账户类型枚举）和 `BankInfo.java`（银行信息）。
*   `entity/README.md`: `entity` 子包的文档。
*   `exception/README.md`: `exception` 子包的文档。
*   `repository/README.md`: `repository` 子包的文档。
*   `valueobject/README.md`: `valueobject` 子包的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.domain` 包中的组件是相互协作的，共同构建了收款账户的领域模型：

1.  **聚合根 (`entity` 子包):**
    *   `PaymentAccount` 是收款账户的聚合根，它封装了账户的所有业务逻辑和状态。所有对账户的修改都应该通过 `PaymentAccount` 实体自身的方法来完成，而不是直接修改其内部属性。这确保了业务规则的强制执行和数据的一致性。
    *   `PaymentAccount` 实体内部会使用 `valueobject` 子包中的值对象（如 `AccountType` 和 `BankInfo`）来表示其属性，从而提高类型安全性和业务表达力。

2.  **值对象 (`valueobject` 子包):**
    *   `AccountType` 枚举提供了强类型的账户类型定义，并封装了与类型相关的业务规则（如是否需要银行信息）。它通过 `fromString()` 方法支持从字符串安全转换。
    *   `BankInfo` 值对象封装了银行转账账户的详细信息，并强制其不可变性。它在创建时进行严格的验证，并提供了脱敏显示等行为。
    *   这些值对象在 `PaymentAccount` 实体中被使用，共同构成了领域模型的丰富语义。

3.  **领域异常 (`exception` 子包):**
    *   `PaymentAccountAccessDeniedException` 和 `PaymentAccountNotFoundException` 是领域层特有的业务异常。它们在领域逻辑执行过程中，当检测到权限不足或资源不存在等业务错误时被抛出。
    *   这些异常向上冒泡到应用服务层，并最终被全局异常处理器捕获，转换为统一的错误响应返回给前端。

4.  **领域仓储接口 (`repository` 子包):**
    *   `PaymentAccountRepository` 接口定义了 `PaymentAccount` 聚合根的持久化和检索契约。它位于领域层，不依赖于任何具体的持久化技术，从而保持了领域模型的纯粹性。
    *   应用服务层通过这个接口与数据持久化层进行交互，而具体的实现（通常在基础设施层）负责将领域实体映射到数据库。

**协作流程总结:**

*   **业务用例执行:** 应用服务层（位于 `application.service` 包）接收到前端请求后，会协调领域层组件来执行业务用例。它会从 `PaymentAccountRepository` 加载 `PaymentAccount` 聚合根，然后调用聚合根上的业务方法来执行操作（例如，`account.setAsDefault()`，`account.approve()`）。
*   **业务规则强制:** `PaymentAccount` 实体内部的方法会利用 `AccountType` 和 `BankInfo` 值对象来强制执行业务规则和数据验证。如果发生业务错误，领域异常会被抛出。
*   **数据持久化:** 领域实体在被修改后，会通过 `PaymentAccountRepository` 接口被持久化到数据库。仓储的实现负责将领域实体转换为数据库可识别的格式。

这种领域层设计确保了业务逻辑的集中、内聚和独立于技术细节，是构建复杂企业级应用程序的关键。
