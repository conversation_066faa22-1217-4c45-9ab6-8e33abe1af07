# com.purchase.user.paymentaccount.infrastructure.converter 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.infrastructure.converter` 包包含了收款账户模块基础设施层中的数据转换器。这些转换器负责在领域实体（`com.purchase.user.paymentaccount.domain.entity.PaymentAccount`）与持久化对象（POJO，`com.purchase.user.paymentaccount.infrastructure.po.PaymentAccountPO`）之间进行双向映射。该包是领域模型与底层数据存储技术（如 MyBatis-Plus）之间的“防腐层”，确保领域模型的纯粹性，并处理数据在不同表示形式之间的转换细节。

## 目录结构概览 (Directory Structure Overview)
*   `PaymentAccountConverter.java`: 收款账户领域对象与持久化对象之间的双向转换器。
*   `PaymentAccountConverter.md`: `PaymentAccountConverter.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.infrastructure.converter` 包中的转换器是基础设施层与领域层交互的关键组件。它与以下组件紧密协作：

1.  **领域实体 (`com.purchase.user.paymentaccount.domain.entity.PaymentAccount`):**
    *   `PaymentAccountConverter` 的 `toPO()` 方法将 `PaymentAccount` 领域实体作为输入，将其转换为适合持久化的 `PaymentAccountPO`。
    *   `toDomain()` 方法则将从数据库读取的 `PaymentAccountPO` 转换为 `PaymentAccount` 领域实体，以便在领域层进行业务操作。

2.  **持久化对象 (`com.purchase.user.paymentaccount.infrastructure.po.PaymentAccountPO`):**
    *   `PaymentAccountPO` 是与数据库表结构直接对应的 POJO。`PaymentAccountConverter` 负责将领域实体中的复杂结构（如值对象）扁平化映射到 `PaymentAccountPO` 的简单字段，反之亦然。

3.  **领域仓储实现 (`com.purchase.user.paymentaccount.infrastructure.repository.impl.PaymentAccountRepositoryImpl`):**
    *   仓储实现类是 `PaymentAccountConverter` 的主要消费者。当仓储需要将领域实体保存到数据库时，它会调用 `toPO()` 方法进行转换。当从数据库读取数据并需要将其转换为领域实体时，它会调用 `toDomain()` 方法。

**协作流程总结:**

*   **持久化操作:** 当应用服务层通过领域仓储接口（`PaymentAccountRepository`）请求保存或更新 `PaymentAccount` 领域实体时，仓储的实现类（`PaymentAccountRepositoryImpl`）会调用 `PaymentAccountConverter.toPO()` 方法，将领域实体转换为 `PaymentAccountPO`。然后，`PaymentAccountPO` 会被传递给 MyBatis Mapper 进行实际的数据库操作。
*   **数据检索操作:** 当应用服务层通过领域仓储接口请求查询 `PaymentAccount` 实体时，仓储的实现类会从 MyBatis Mapper 获取 `PaymentAccountPO`。然后，它会调用 `PaymentAccountConverter.toDomain()` 方法，将 `PaymentAccountPO` 转换为 `PaymentAccount` 领域实体，并将其返回给应用服务层。

这种转换模式确保了：
*   **领域模型纯粹性:** 领域模型不包含任何持久化框架的细节，专注于业务逻辑。
*   **持久化技术可替换性:** 可以在不影响领域模型的情况下更换底层持久化技术。
*   **数据映射清晰:** 明确定义了领域模型与数据库模型之间的映射规则。
*   **敏感数据处理:** 转换器可以在映射过程中处理敏感数据的加密/解密或脱敏，尽管当前实现中脱敏主要在值对象内部完成。
