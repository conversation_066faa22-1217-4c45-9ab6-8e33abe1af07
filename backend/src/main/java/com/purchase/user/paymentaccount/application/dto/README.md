# com.purchase.user.paymentaccount.application.dto 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.application.dto` 包包含了收款账户模块应用服务层中使用的数据传输对象（DTO）。这些 DTO 旨在封装和标准化前端与后端应用服务之间传输的数据格式，涵盖了创建、更新、查询收款账户以及获取账户概览信息等场景。通过使用这些 DTO，该包实现了数据传输的清晰性、类型安全性和验证机制，同时避免了将内部领域模型直接暴露给外部接口。

## 目录结构概览 (Directory Structure Overview)
*   `BankInfo.java`: 银行信息 DTO，通常作为 `PaymentAccountResponse` 的嵌套字段。
*   `CreatePaymentAccountRequest.java`: 创建收款账户请求的数据封装。
*   `PaymentAccountResponse.java`: 收款账户的详细响应数据封装。
*   `UpdatePaymentAccountRequest.java`: 更新收款账户请求的数据封装。
*   `UserPaymentAccountSummaryResponse.java`: 用户支付账户概览信息的响应数据封装。
*   `BankInfo.md`: `BankInfo.java` 的文档。
*   `CreatePaymentAccountRequest.md`: `CreatePaymentAccountRequest.java` 的文档。
*   `PaymentAccountResponse.md`: `PaymentAccountResponse.java` 的文档。
*   `UpdatePaymentAccountRequest.md`: `UpdatePaymentAccountRequest.java` 的文档。
*   `UserPaymentAccountSummaryResponse.md`: `UserPaymentAccountSummaryResponse.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.application.dto` 包中的 DTOs 主要作为数据载体，在前端与后端应用服务层（`com.purchase.user.paymentaccount.application.service`）之间传递数据。它们与 `com.purchase.user.paymentaccount.application.converter.PaymentAccountDTOConverter` 紧密协作，实现 DTO 与领域实体之间的双向转换。

1.  **请求 DTOs:**
    *   `CreatePaymentAccountRequest` 和 `UpdatePaymentAccountRequest` 用于接收前端提交的创建或更新收款账户的请求数据。这些 DTOs 通常会使用 JSR 303/349（Bean Validation）注解（如 `@NotBlank`, `@NotNull`, `@Size`, `@DecimalMin` 等）进行初步的数据格式和非空验证。在控制器层接收到这些 DTO 后，会将其传递给应用服务层。

2.  **响应 DTOs:**
    *   `PaymentAccountResponse` 用于将单个收款账户的详细信息返回给前端。它包含了账户的通用属性以及根据账户类型（银行转账、支付宝、微信）特有的信息。`BankInfo` DTO 作为其嵌套字段，用于封装银行转账账户的详细信息。
    *   `UserPaymentAccountSummaryResponse` 提供了一个用户支付账户的概览视图，包含统计信息（如总账户数、已验证账户数）和用户状态，主要用于仪表盘或概览页面。

3.  **与 Converter 的协作:**
    *   `PaymentAccountDTOConverter` 负责将请求 DTOs 转换为领域实体（`com.purchase.user.paymentaccount.domain.entity.PaymentAccount`），并在业务逻辑处理完成后，将领域实体转换为响应 DTOs 返回给前端。这种转换机制确保了领域模型的独立性，并允许 API 接口与内部实现解耦。

**协作流程总结:**

*   **创建/更新流程:** 前端发送包含 `CreatePaymentAccountRequest` 或 `UpdatePaymentAccountRequest` 的请求。控制器接收并验证 DTO，然后将其传递给应用服务层。应用服务层调用 `PaymentAccountDTOConverter` 将请求 DTO 转换为领域实体，执行业务逻辑（如持久化），然后再次使用 `PaymentAccountDTOConverter` 将领域实体转换为 `PaymentAccountResponse` 返回给前端。
*   **查询流程:** 前端请求获取收款账户列表或概览信息。应用服务层从领域层获取 `PaymentAccount` 实体或相关统计数据，然后调用 `PaymentAccountDTOConverter` 将其转换为 `PaymentAccountResponse` 或 `UserPaymentAccountSummaryResponse` 返回给前端。

这种设计模式确保了数据在应用程序不同层级之间传输时的结构化、类型安全和验证，同时维护了领域模型的清晰边界。
