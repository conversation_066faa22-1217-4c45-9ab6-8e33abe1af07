# com.purchase.user.paymentaccount.domain.valueobject 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.domain.valueobject` 包包含了收款账户模块领域层的值对象。在领域驱动设计（DDD）中，值对象是描述领域中某个概念的不可变对象，它们没有唯一标识，其相等性基于其属性的值。该包的核心是 `AccountType` 枚举和 `BankInfo` 类，它们封装了与收款账户类型和银行信息相关的业务规则和行为，确保了领域模型的一致性、类型安全性和业务逻辑的内聚性。

## 目录结构概览 (Directory Structure Overview)
*   `AccountType.java`: 收款账户类型枚举，定义了支持的账户类型及其相关业务规则。
*   `BankInfo.java`: 银行信息值对象，封装了银行转账账户的详细信息，并强制进行验证。
*   `AccountType.md`: `AccountType.java` 的文档。
*   `BankInfo.md`: `BankInfo.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.domain.valueobject` 包中的值对象是领域模型的重要组成部分，它们与领域实体（`com.purchase.user.paymentaccount.domain.entity.PaymentAccount`）以及应用服务层（`com.purchase.user.paymentaccount.application.service`）紧密协作：

1.  **`AccountType` 枚举:**
    *   作为强类型，`AccountType` 枚举在 `PaymentAccount` 实体中被用作 `accountType` 字段的类型，确保了账户类型始终是有效的、预定义的值。
    *   其 `fromString()` 静态方法被应用服务层（如 `PaymentAccountApplicationServiceImpl`）和自定义验证器（如 `ValidAccountTypeValidator`）调用，用于将外部传入的字符串转换为领域层的强类型枚举，并在转换过程中强制执行有效性检查。
    *   它内部封装了与账户类型相关的业务规则（如 `requiresBankInfo()`），使得这些规则与数据紧密结合。

2.  **`BankInfo` 值对象:**
    *   `BankInfo` 值对象在 `PaymentAccount` 实体中被用作银行转账账户的详细信息。它通过私有构造函数和静态工厂方法 `create()` 强制创建有效实例，并在创建时对银行名称、账号、支行和 SWIFT 代码进行严格的验证。
    *   它提供了银行账号的脱敏显示 (`getMaskedAccountNumber()`) 和格式化为显示字符串 (`toDisplayString()`) 的行为，这些行为是数据展示的一部分，但逻辑内聚在值对象内部。
    *   由于其不可变性，当需要修改银行信息时，会创建新的 `BankInfo` 实例来替换旧的实例。

**协作流程总结:**

*   **数据创建/更新:** 当应用服务层接收到创建或更新收款账户的请求时，它会从请求 DTO 中提取原始数据。对于账户类型，它会调用 `AccountType.fromString()` 将字符串转换为 `AccountType` 枚举。对于银行信息，它会调用 `BankInfo.create()` 工厂方法来创建 `BankInfo` 值对象。这些值对象在创建时会进行严格的验证，确保只有合法的数据才能用于构建 `PaymentAccount` 领域实体。
*   **领域实体内部使用:** `PaymentAccount` 实体内部持有 `AccountType` 和 `BankInfo` 的实例。当 `PaymentAccount` 实体执行其业务行为（如更新账户信息）时，它会利用这些值对象来执行其内部逻辑，并确保数据的一致性。
*   **数据展示:** 当需要将领域实体转换为响应 DTO 返回给前端时，`PaymentAccountDTOConverter` 会从 `PaymentAccount` 实体中获取 `AccountType` 和 `BankInfo` 值对象的数据，并将其映射到应用层的 DTO（例如 `PaymentAccountResponse` 中的 `accountType` 字符串和嵌套的 `BankInfo` DTO）。在映射过程中，可能会调用值对象的方法（如 `getMaskedAccountNumber()`）进行数据处理。

这种模式确保了领域模型中的核心概念以强类型、不可变和内聚的方式进行建模，从而提高了代码的质量、可读性和可测试性。
