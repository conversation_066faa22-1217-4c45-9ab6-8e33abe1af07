# com.purchase.user.paymentaccount.interfaces 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.interfaces` 包是收款账户模块的接口层。在领域驱动设计（DDD）中，接口层负责处理用户界面和外部系统与应用程序之间的交互。该包包含了收款账户相关的 RESTful API 控制器和模块特定的异常处理器。它作为应用程序的对外接口，负责接收前端请求，将请求数据绑定到 DTOs，调用应用服务层执行业务逻辑，并最终将处理结果封装为统一的 API 响应返回给客户端。该包通过 Spring MVC 和 Spring Security 的集成，确保了 API 的安全性和健壮性。

## 目录结构概览 (Directory Structure Overview)
*   `controller/`: 包含收款账户相关的 RESTful API 控制器，如 `PaymentAccountController.java` 和 `AdminPaymentAccountController.java`。
*   `exception/`: 包含收款账户模块的自定义异常处理器，如 `PaymentAccountExceptionHandler.java`。
*   `controller/README.md`: `controller` 子包的文档。
*   `exception/README.md`: `exception` 子包的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.interfaces` 包中的组件协同工作，共同实现了收款账户模块的对外功能：

1.  **控制器 (`controller` 子包):**
    *   `PaymentAccountController` 提供了用户对收款账户进行增删改查和设置默认账户的 API 接口。
    *   `AdminPaymentAccountController` 提供了管理员对用户支付账户进行概览查询和详细列表查询的 API 接口。
    *   这些控制器通过 `@Autowired` 注入 `com.purchase.user.paymentaccount.application.service.PaymentAccountApplicationService` 和 `com.purchase.user.paymentaccount.application.service.AdminPaymentAccountService` 的实例，作为与应用服务层交互的主要入口。
    *   它们接收来自前端的请求 DTO（来自 `com.purchase.user.paymentaccount.application.dto` 包），并将其传递给应用服务层。同时，将应用服务层返回的响应 DTO 封装在 `com.purchase.common.response.ApiResponse` 中返回给前端。

2.  **异常处理器 (`exception` 子包):**
    *   `PaymentAccountExceptionHandler` 是一个模块特定的 `@RestControllerAdvice`。它通过 `@Order(Ordered.HIGHEST_PRECEDENCE)` 确保优先处理本模块控制器抛出的异常。
    *   它捕获各种异常，包括 Bean Validation 相关的验证异常（如 `MethodArgumentNotValidException`）、领域层抛出的业务异常（如 `PaymentAccountNotFoundException`, `PaymentAccountAccessDeniedException`）以及其他运行时异常。
    *   该处理器将捕获到的异常转换为统一的 `ApiResponse` 格式，并设置相应的 HTTP 状态码（如 400, 403, 404, 500），从而为前端提供一致且友好的错误响应。

3.  **权限控制:**
    *   控制器方法广泛使用 Spring Security 的 `@PreAuthorize` 注解来定义访问权限。这确保了只有经过认证且具有相应角色的用户才能访问特定的 API 资源，例如，管理员接口只允许 `admin` 角色访问，而普通用户接口则根据业务需求允许 `buyer`、`seller` 或 `forwarder` 角色访问。

**协作流程总结:**

*   **请求进入:** 前端发送 HTTP 请求到 `interfaces.controller` 包中的某个控制器。
*   **参数绑定与验证:** Spring MVC 将请求数据绑定到 DTOs（来自 `application.dto` 包），并触发 DTO 中定义的验证规则（可能包括 `application.validation` 包中的自定义验证器）。
*   **权限检查:** Spring Security 的 `@PreAuthorize` 注解在方法执行前进行权限检查。
*   **调用应用服务:** 验证和权限检查通过后，控制器调用 `application.service` 包中的应用服务方法执行业务逻辑。
*   **处理业务结果与异常:** 应用服务返回业务处理结果（响应 DTO），或者抛出业务异常。如果抛出异常，`interfaces.exception` 包中的 `PaymentAccountExceptionHandler` 会捕获并处理它。
*   **封装响应:** 控制器将响应 DTO 封装在 `ApiResponse` 中，并结合 `ResponseEntity` 设置适当的 HTTP 状态码。
*   **响应返回:** 最终，一个统一格式的 HTTP 响应（无论是成功数据还是错误信息）被返回给前端。

这种接口层设计确保了 API 的清晰性、安全性、可维护性，并为前端提供了友好且一致的交互体验，同时将业务逻辑与外部交互细节分离。
