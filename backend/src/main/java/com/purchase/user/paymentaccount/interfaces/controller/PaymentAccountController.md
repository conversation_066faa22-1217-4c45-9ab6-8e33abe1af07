# PaymentAccountController.java

## 文件概述 (File Overview)
`PaymentAccountController.java` 是用户收款账户管理的REST控制器，位于 `com.purchase.user.paymentaccount.interfaces.controller` 包中。该控制器作为收款账户管理模块的HTTP接口层，负责处理所有用户收款账户相关的REST API请求。通过集成 `PaymentAccountApplicationService` 等业务服务，提供了收款账户的完整生命周期管理功能，包括创建、查询、更新、删除、验证、默认设置等。该控制器实现了基于Spring Security的严格权限控制，支持用户自主管理和管理员审核的双重机制，并提供了完善的账户安全验证和风险控制功能。

## 核心功能 (Core Functionality)
*   **收款账户管理**: 提供收款账户的完整CRUD操作，支持多种账户类型
*   **默认账户设置**: 支持用户设置和管理默认收款账户，简化支付流程
*   **账户验证审核**: 管理员可对用户收款账户进行审核验证，确保账户真实性
*   **权限分级控制**: 基于用户角色的细粒度权限控制，保护账户数据安全
*   **多账户类型支持**: 支持银行卡、支付宝、微信等多种收款方式
*   **账户状态管理**: 完整的账户状态流转，包括待审核、已验证、已禁用等
*   **安全验证机制**: 账户信息的加密存储和敏感数据脱敏处理
*   **批量操作支持**: 支持管理员批量审核和状态更新操作
*   **审计日志记录**: 详细的账户操作审计日志，满足合规要求
*   **数据一致性保障**: 确保用户账户数据的一致性和完整性
*   **异常处理机制**: 完善的异常捕获和用户友好的错误提示
*   **统一响应格式**: 使用标准化的API响应格式，便于前端处理

## 接口说明 (Interface Description)

### `getUserPaymentAccounts(@Parameter(description = "用户ID") @RequestParam @NotNull Long userId)`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/payment-accounts`
*   **摘要:** 获取用户收款账户列表
*   **描述:** 查询指定用户的所有收款账户。
*   **权限:** `hasAuthority('buyer') or hasAuthority('seller') or hasAuthority('forwarder')`
*   **参数:** `userId` (Long, `@RequestParam`, `@NotNull`): 用户 ID。
*   **返回值:** `ResponseEntity<ApiResponse<List<PaymentAccountResponse>>>` - 包含收款账户列表的 HTTP 响应。
*   **业务逻辑:** 调用 `paymentAccountApplicationService.getUserPaymentAccounts()` 获取数据，并封装在 `ApiResponse.success()` 中返回。

### `createPaymentAccount(@Valid @RequestBody CreatePaymentAccountRequest request)`
*   **HTTP 方法:** `POST`
*   **路径:** `/api/v1/payment-accounts`
*   **摘要:** 创建收款账户
*   **描述:** 用户创建新的收款账户。
*   **权限:** `hasAuthority('buyer') or hasAuthority('seller') or hasAuthority('forwarder')`
*   **参数:** `request` (CreatePaymentAccountRequest, `@RequestBody`, `@Valid`): 包含创建账户所需数据的 DTO。
*   **返回值:** `ResponseEntity<ApiResponse<PaymentAccountResponse>>` - 包含创建账户信息的 HTTP 响应。
*   **业务逻辑:** 调用 `paymentAccountApplicationService.createPaymentAccount()` 处理创建请求，并返回 `HttpStatus.CREATED` 状态码。

### `getPaymentAccountById(@Parameter(description = "账户ID") @PathVariable @NotNull Long accountId)`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/payment-accounts/{accountId}`
*   **摘要:** 获取收款账户详情
*   **描述:** 根据账户 ID 获取收款账户详细信息。
*   **权限:** `hasAuthority('buyer') or hasAuthority('seller') or hasAuthority('forwarder') or hasAuthority('admin')`
*   **参数:** `accountId` (Long, `@PathVariable`, `@NotNull`): 账户 ID。
*   **返回值:** `ResponseEntity<ApiResponse<PaymentAccountResponse>>` - 包含账户详情的 HTTP 响应。
*   **业务逻辑:** 调用 `paymentAccountApplicationService.getPaymentAccountById()` 获取数据，并封装在 `ApiResponse.success()` 中返回。

### `setDefaultAccount(@Parameter(description = "账户ID") @PathVariable @NotNull Long accountId, @Parameter(description = "用户ID") @RequestParam @NotNull Long userId)`
*   **HTTP 方法:** `PUT`
*   **路径:** `/api/v1/payment-accounts/{accountId}/default`
*   **摘要:** 设置默认收款账户
*   **描述:** 将指定账户设置为用户的默认收款账户。
*   **权限:** `hasAuthority('buyer') or hasAuthority('seller') or hasAuthority('forwarder')`
*   **参数:** `accountId` (Long, `@PathVariable`, `@NotNull`): 账户 ID；`userId` (Long, `@RequestParam`, `@NotNull`): 用户 ID。
*   **返回值:** `ResponseEntity<ApiResponse<Void>>` - 操作结果的 HTTP 响应。
*   **业务逻辑:** 调用 `paymentAccountApplicationService.setDefaultAccount()` 处理请求，并返回成功消息。

### `getDefaultAccount(@Parameter(description = "用户ID") @RequestParam @NotNull Long userId)`
*   **HTTP 方法:** `GET`
*   **路径:** `/api/v1/payment-accounts/default`
*   **摘要:** 获取默认收款账户
*   **描述:** 获取指定用户的默认收款账户。
*   **权限:** `hasAuthority('buyer') or hasAuthority('seller') or hasAuthority('admin')`
*   **参数:** `userId` (Long, `@RequestParam`, `@NotNull`): 用户 ID。
*   **返回值:** `ResponseEntity<ApiResponse<PaymentAccountResponse>>` - 包含默认账户信息的 HTTP 响应。
*   **业务逻辑:** 调用 `paymentAccountApplicationService.getDefaultAccount()` 获取数据，并封装在 `ApiResponse.success()` 中返回。

### `deletePaymentAccount(@Parameter(description = "账户ID") @PathVariable @NotNull Long accountId, @Parameter(description = "用户ID") @RequestParam @NotNull Long userId)`
*   **HTTP 方法:** `DELETE`
*   **路径:** `/api/v1/payment-accounts/{accountId}`
*   **摘要:** 删除收款账户
*   **描述:** 逻辑删除指定的收款账户。
*   **权限:** `hasAuthority('buyer') or hasAuthority('seller') or hasAuthority('forwarder')`
*   **参数:** `accountId` (Long, `@PathVariable`, `@NotNull`): 账户 ID；`userId` (Long, `@RequestParam`, `@NotNull`): 用户 ID。
*   **返回值:** `ResponseEntity<ApiResponse<Void>>` - 操作结果的 HTTP 响应（通常是 204 No Content）。
*   **业务逻辑:** 调用 `paymentAccountApplicationService.deletePaymentAccount()` 处理删除请求，并返回 `HttpStatus.NO_CONTENT` 状态码。

### `updatePaymentAccount(@Parameter(description = "账户ID") @PathVariable @NotNull Long accountId, @Valid @RequestBody UpdatePaymentAccountRequest request)`
*   **HTTP 方法:** `PUT`
*   **路径:** `/api/v1/payment-accounts/{accountId}`
*   **摘要:** 更新收款账户
*   **描述:** 更新指定的收款账户信息。
*   **权限:** `hasAuthority('buyer') or hasAuthority('seller') or hasAuthority('forwarder')`
*   **参数:** `accountId` (Long, `@PathVariable`, `@NotNull`): 账户 ID；`request` (UpdatePaymentAccountRequest, `@RequestBody`, `@Valid`): 包含更新数据的 DTO。
*   **返回值:** `ResponseEntity<ApiResponse<PaymentAccountResponse>>` - 包含更新后账户信息的 HTTP 响应。
*   **业务逻辑:** 调用 `paymentAccountApplicationService.updatePaymentAccount()` 处理更新请求，并返回成功消息。

### `verifyAccount(@Parameter(description = "账户ID") @PathVariable @NotNull Long accountId, @Parameter(description = "是否通过验证") @RequestParam @NotNull Boolean approved, @Parameter(description = "验证备注") @RequestParam(required = false) String notes, @Parameter(description = "验证人") @RequestParam @NotNull String verifiedBy)`
*   **HTTP 方法:** `PUT`
*   **路径:** `/api/v1/payment-accounts/{accountId}/verify`
*   **摘要:** 验证收款账户
*   **描述:** 管理员审核验证用户的收款账户。
*   **权限:** `hasAuthority('admin')`
*   **参数:** `accountId` (Long, `@PathVariable`, `@NotNull`): 账户 ID；`approved` (Boolean, `@RequestParam`, `@NotNull`): 是否通过验证；`notes` (String, `@RequestParam`, 可选): 验证备注；`verifiedBy` (String, `@RequestParam`, `@NotNull`): 验证人。
*   **返回值:** `ResponseEntity<ApiResponse<Void>>` - 操作结果的 HTTP 响应。
*   **业务逻辑:** 调用 `paymentAccountApplicationService.verifyAccount()` 处理验证请求，并返回成功消息。

## 使用示例 (Usage Examples)

```java
// 1. 前端JavaScript调用示例
const PaymentAccountAPI = {
    // 创建收款账户
    async createPaymentAccount(accountData) {
        const response = await fetch('/api/v1/payment-accounts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                userId: accountData.userId,
                accountType: accountData.accountType, // BANK_TRANSFER, ALIPAY, WECHAT_PAY
                accountName: accountData.accountName,
                accountNumber: accountData.accountNumber,
                bankName: accountData.bankName,
                bankBranch: accountData.bankBranch,
                holderName: accountData.holderName,
                holderIdCard: accountData.holderIdCard,
                remarks: accountData.remarks
            })
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('收款账户创建成功，等待审核');
            return result.data;
        } else {
            showErrorMessage('创建失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 获取用户收款账户列表
    async getUserPaymentAccounts(userId) {
        const response = await fetch(`/api/v1/payment-accounts?userId=${userId}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 设置默认收款账户
    async setDefaultAccount(accountId, userId) {
        const response = await fetch(`/api/v1/payment-accounts/${accountId}/default?userId=${userId}`, {
            method: 'PUT',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('默认收款账户设置成功');
            return result.data;
        } else {
            showErrorMessage('设置失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 更新收款账户
    async updatePaymentAccount(accountId, updateData) {
        const response = await fetch(`/api/v1/payment-accounts/${accountId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify(updateData)
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('收款账户更新成功');
            return result.data;
        } else {
            showErrorMessage('更新失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 删除收款账户
    async deletePaymentAccount(accountId) {
        const response = await fetch(`/api/v1/payment-accounts/${accountId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('收款账户删除成功');
            return result.data;
        } else {
            showErrorMessage('删除失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 管理员验证账户
    async adminVerifyAccount(accountId, approved, notes) {
        const response = await fetch(`/api/v1/payment-accounts/${accountId}/verify`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                approved: approved,
                notes: notes,
                verifiedBy: getCurrentUserId()
            })
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage(approved ? '账户审核通过' : '账户审核拒绝');
            return result.data;
        } else {
            showErrorMessage('审核失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 渲染收款账户列表
    renderPaymentAccountList(accounts, containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        accounts.forEach(account => {
            const accountDiv = document.createElement('div');
            accountDiv.className = 'payment-account-item';

            accountDiv.innerHTML = `
                <div class="account-header">
                    <h4>${account.accountName}</h4>
                    <div class="account-badges">
                        <span class="account-type ${account.accountType.toLowerCase()}">${account.accountTypeText}</span>
                        <span class="account-status ${account.status.toLowerCase()}">${account.statusText}</span>
                        ${account.isDefault ? '<span class="default-badge">默认</span>' : ''}
                    </div>
                </div>
                <div class="account-content">
                    <div class="account-info">
                        <p><strong>账户号码:</strong> ${maskAccountNumber(account.accountNumber)}</p>
                        <p><strong>持卡人:</strong> ${account.holderName}</p>
                        ${account.bankName ? `<p><strong>开户银行:</strong> ${account.bankName}</p>` : ''}
                        ${account.bankBranch ? `<p><strong>开户支行:</strong> ${account.bankBranch}</p>` : ''}
                        <p><strong>创建时间:</strong> ${new Date(account.createdAt).toLocaleString()}</p>
                    </div>
                </div>
                <div class="account-actions">
                    <button onclick="viewAccountDetails(${account.id})">查看详情</button>
                    <button onclick="editAccount(${account.id})">编辑</button>
                    ${!account.isDefault ?
                        `<button onclick="setAsDefault(${account.id})">设为默认</button>` : ''}
                    ${account.status === 'PENDING' ?
                        `<button onclick="deleteAccount(${account.id})" class="delete-btn">删除</button>` : ''}
                </div>
            `;

            container.appendChild(accountDiv);
        });
    }
};

// 2. Java客户端调用示例
@Service
public class PaymentAccountClientService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${api.base-url}")
    private String baseUrl;

    // 创建收款账户
    public PaymentAccountDTO createPaymentAccount(CreatePaymentAccountRequest request, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<CreatePaymentAccountRequest> entity = new HttpEntity<>(request, headers);

        try {
            ResponseEntity<ApiResponse<PaymentAccountDTO>> response = restTemplate.exchange(
                baseUrl + "/api/v1/payment-accounts",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<ApiResponse<PaymentAccountDTO>>() {}
            );

            ApiResponse<PaymentAccountDTO> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("创建收款账户失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用创建收款账户API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }

    // 获取用户收款账户列表
    public List<PaymentAccountDTO> getUserPaymentAccounts(Long userId, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);

        HttpEntity<?> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<ApiResponse<List<PaymentAccountDTO>>> response = restTemplate.exchange(
                baseUrl + "/api/v1/payment-accounts?userId=" + userId,
                HttpMethod.GET,
                entity,
                new ParameterizedTypeReference<ApiResponse<List<PaymentAccountDTO>>>() {}
            );

            ApiResponse<List<PaymentAccountDTO>> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("获取收款账户列表失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用获取收款账户列表API失败: userId={}", userId, e);
            throw new SystemException("网络请求失败", e);
        }
    }
}

// 3. 业务服务集成示例
@Service
public class PaymentAccountWorkflowService {

    @Autowired
    private PaymentAccountApplicationService paymentAccountService;

    @Autowired
    private NotificationService notificationService;

    // 账户创建后的业务流程
    @EventListener
    @Async
    public void handleAccountCreated(PaymentAccountCreatedEvent event) {
        try {
            PaymentAccountDTO account = event.getPaymentAccount();

            // 1. 发送创建通知
            notificationService.sendAccountCreatedNotification(account);

            // 2. 如果是用户的第一个账户，自动设为默认
            List<PaymentAccountDTO> userAccounts = paymentAccountService.getUserPaymentAccounts(account.getUserId());
            if (userAccounts.size() == 1) {
                paymentAccountService.setDefaultAccount(account.getId(), account.getUserId());
            }

            // 3. 发送审核提醒给管理员
            notificationService.sendAccountReviewReminder(account);

            log.info("收款账户创建后处理完成: accountId={}", account.getId());

        } catch (Exception e) {
            log.error("收款账户创建后处理失败: accountId={}", event.getPaymentAccount().getId(), e);
        }
    }

    // 账户审核后的业务流程
    @EventListener
    @Async
    public void handleAccountVerified(PaymentAccountVerifiedEvent event) {
        try {
            PaymentAccountDTO account = event.getPaymentAccount();
            boolean approved = event.isApproved();

            // 1. 发送审核结果通知
            if (approved) {
                notificationService.sendAccountApprovedNotification(account);
            } else {
                notificationService.sendAccountRejectedNotification(account, event.getNotes());
            }

            // 2. 记录审核日志
            auditLogService.logAccountVerification(account.getId(), approved, event.getNotes());

            log.info("收款账户审核后处理完成: accountId={}, approved={}", account.getId(), approved);

        } catch (Exception e) {
            log.error("收款账户审核后处理失败: accountId={}", event.getPaymentAccount().getId(), e);
        }
    }
}

// 4. 定时任务示例
@Component
public class PaymentAccountScheduledTasks {

    @Autowired
    private PaymentAccountApplicationService paymentAccountService;

    // 检查待审核账户
    @Scheduled(cron = "0 0 9 * * ?") // 每天早上9点
    public void checkPendingAccounts() {
        log.info("开始检查待审核收款账户");

        try {
            List<PaymentAccountDTO> pendingAccounts = paymentAccountService.getPendingAccounts();

            // 检查超过3天未审核的账户
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(3);

            for (PaymentAccountDTO account : pendingAccounts) {
                if (account.getCreatedAt().isBefore(cutoffTime)) {
                    // 发送超时提醒
                    notificationService.sendAccountReviewOverdueAlert(account);
                }
            }

            log.info("待审核收款账户检查完成，待审核数量: {}", pendingAccounts.size());

        } catch (Exception e) {
            log.error("检查待审核收款账户失败", e);
        }
    }

    // 生成账户统计报告
    @Scheduled(cron = "0 0 8 1 * ?") // 每月1号早上8点
    public void generateMonthlyAccountReport() {
        log.info("开始生成月度收款账户报告");

        try {
            MonthlyAccountReport report = new MonthlyAccountReport();
            report.setReportMonth(LocalDate.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM")));
            report.setNewAccountCount(paymentAccountService.getNewAccountCountLastMonth());
            report.setVerifiedAccountCount(paymentAccountService.getVerifiedAccountCountLastMonth());
            report.setAccountTypeDistribution(paymentAccountService.getAccountTypeDistributionLastMonth());

            // 发送报告给管理员
            notificationService.sendMonthlyAccountReport(report);

            log.info("月度收款账户报告生成完成");

        } catch (Exception e) {
            log.error("生成月度收款账户报告失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class PaymentAccountControllerTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @MockBean
    private PaymentAccountApplicationService paymentAccountService;

    @Test
    void testCreatePaymentAccount() {
        // 准备测试数据
        CreatePaymentAccountRequest request = new CreatePaymentAccountRequest();
        request.setUserId(1L);
        request.setAccountType("BANK_TRANSFER");
        request.setAccountName("工商银行储蓄卡");
        request.setAccountNumber("6222021234567890123");
        request.setBankName("中国工商银行");
        request.setHolderName("张三");

        PaymentAccountDTO expectedAccount = new PaymentAccountDTO();
        expectedAccount.setId(1L);
        expectedAccount.setAccountName("工商银行储蓄卡");
        expectedAccount.setStatus("PENDING");

        // Mock服务调用
        when(paymentAccountService.createPaymentAccount(any(CreatePaymentAccountRequest.class)))
            .thenReturn(expectedAccount);

        // 执行测试
        HttpEntity<CreatePaymentAccountRequest> entity = new HttpEntity<>(request);
        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
            "/api/v1/payment-accounts", entity, ApiResponse.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(paymentAccountService, times(1)).createPaymentAccount(any(CreatePaymentAccountRequest.class));
    }
}
```

## 注意事项 (Notes)
*   **权限控制**: 使用Spring Security的@PreAuthorize注解进行权限验证，确保只有相应角色的用户才能访问对应功能
*   **数据安全**: 收款账户信息涉及敏感的财务数据，需要严格的加密存储和传输保护
*   **审核流程**: 所有收款账户都需要经过管理员审核，确保账户信息的真实性和合法性
*   **参数验证**: 使用@Valid注解对请求参数进行验证，确保数据的完整性和有效性
*   **统一响应**: 使用ApiResponse作为统一的响应格式，确保前端能够统一处理响应数据
*   **默认账户**: 用户可以设置默认收款账户，简化支付流程，但需要确保默认账户的唯一性
*   **账户类型**: 支持多种收款方式（银行卡、支付宝、微信等），需要根据类型进行不同的验证
*   **状态管理**: 账户状态包括待审核、已验证、已拒绝、已禁用等，需要严格控制状态流转
*   **数据脱敏**: 在返回账户信息时需要对敏感信息进行脱敏处理，如账户号码部分隐藏
*   **并发控制**: 账户操作需要考虑并发控制，避免数据不一致问题
*   **审计日志**: 重要的账户操作需要记录详细的审计日志，满足合规要求
*   **异常处理**: 完善的异常处理机制，确保在出现错误时返回友好的错误信息
*   **业务委托**: 控制器只负责HTTP请求处理和响应封装，具体业务逻辑委托给应用服务处理
*   **缓存策略**: 用户账户列表等频繁查询的数据可以考虑使用缓存提高性能
*   **国际化**: 账户类型和状态描述需要支持多语言，便于国际化部署
*   **Swagger注解**: 使用完整的Swagger注解为API文档提供清晰的接口说明
