# com.purchase.user.paymentaccount 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount` 包是采购系统中负责用户收款账户管理的核心模块。它遵循领域驱动设计（DDD）的架构原则，将业务逻辑划分为清晰的层次：领域层、应用层和基础设施层，并通过接口层对外暴露功能。该模块提供了用户收款账户的创建、查询、更新、删除、默认设置以及管理员审核等功能，确保了支付账户数据的完整性、安全性和业务流程的正确性。

## 目录结构概览 (Directory Structure Overview)
*   `application/`: 应用层，包含应用服务接口、其实现、数据传输对象（DTO）和自定义验证组件。
*   `domain/`: 领域层，包含领域实体、值对象、领域异常和领域仓储接口。
*   `infrastructure/`: 基础设施层，包含领域实体与持久化对象之间的转换器、MyBatis Mapper 接口和领域仓储接口的具体实现。
*   `interfaces/`: 接口层，包含 RESTful API 控制器和模块特定的异常处理器。
*   `application/README.md`: `application` 子包的文档。
*   `domain/README.md`: `domain` 子包的文档。
*   `infrastructure/README.md`: `infrastructure` 子包的文档。
*   `interfaces/README.md`: `interfaces` 子包的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount` 模块的各个层次和组件协同工作，形成了一个内聚且职责清晰的系统：

1.  **接口层 (`interfaces`):**
    *   作为模块的对外入口，包含 `PaymentAccountController`（面向普通用户）和 `AdminPaymentAccountController`（面向管理员）。
    *   这些控制器接收前端的 HTTP 请求，将请求数据绑定到 `application.dto` 中的 DTOs。
    *   `PaymentAccountExceptionHandler` 集中处理本模块控制器抛出的异常，并返回统一的 `ApiResponse` 格式的错误响应。
    *   通过 Spring Security 的 `@PreAuthorize` 进行权限控制。

2.  **应用层 (`application`):**
    *   `PaymentAccountApplicationService` 接口定义了模块的所有业务用例，其实现类 `PaymentAccountApplicationServiceImpl` 负责编排这些用例的执行。
    *   `AdminPaymentAccountService` 提供管理员专用的查询服务。
    *   `application.dto` 中的 DTOs 作为应用层接口的输入和输出，实现了与外部接口的解耦。
    *   `application.converter.PaymentAccountDTOConverter` 负责 DTO 与领域实体之间的双向转换。
    *   `application.validation` 中的自定义验证器（如 `ValidAccountTypeValidator`）在数据进入领域层之前进行业务规则验证。

3.  **领域层 (`domain`):**
    *   `domain.entity.PaymentAccount` 是收款账户的聚合根，封装了账户的所有业务逻辑和状态。所有对账户的修改都通过其自身的方法完成。
    *   `domain.valueobject` 中的值对象（如 `AccountType`, `BankInfo`）为领域实体提供强类型和不可变属性，并封装了相关业务规则。
    *   `domain.exception` 中的领域异常（如 `PaymentAccountNotFoundException`, `PaymentAccountAccessDeniedException`）明确表示业务错误，并向上抛出。
    *   `domain.repository.PaymentAccountRepository` 定义了领域仓储的契约，是领域层对数据持久化的抽象要求。

4.  **基础设施层 (`infrastructure`):**
    *   `infrastructure.repository.PaymentAccountRepositoryImpl` 实现了领域层的 `PaymentAccountRepository` 接口，负责与底层数据库进行实际交互。
    *   `infrastructure.mapper.PaymentAccountMapper` 是 MyBatis Mapper 接口，直接操作 `infrastructure.po.PaymentAccountPO`。
    *   `infrastructure.po.PaymentAccountPO` 是与数据库表结构直接对应的 POJO，用于 MyBatis-Plus 的映射。
    *   `infrastructure.converter.PaymentAccountConverter` 负责 `PaymentAccount` 领域实体与 `PaymentAccountPO` 之间的双向转换，作为领域层与持久化技术之间的“防腐层”。

**数据流和协作总结:**

*   **请求流:** 前端请求 -> `interfaces.controller` (DTO 绑定, 验证, 权限检查) -> `application.service` (业务用例编排, DTO-实体转换, 验证) -> `domain.entity` (执行业务逻辑, 状态变更) -> `domain.repository` (数据持久化契约)。
*   **持久化流:** `domain.repository` -> `infrastructure.repository.impl` (调用 `infrastructure.converter.toPO` 转换, 调用 `infrastructure.mapper` 执行数据库操作) -> 数据库。
*   **响应流:** 数据库 -> `infrastructure.mapper` (返回 `infrastructure.po`) -> `infrastructure.repository.impl` (调用 `infrastructure.converter.toDomain` 转换) -> `domain.entity` -> `application.service` (调用 `application.converter.toResponse` 转换) -> `interfaces.controller` (封装 `ApiResponse`) -> 前端。
*   **异常流:** 领域层抛出 `domain.exception` -> 传播到 `application.service` -> 传播到 `interfaces.controller` -> 被 `interfaces.exception.PaymentAccountExceptionHandler` 捕获并处理，返回统一错误响应。

这种分层架构和组件协作模式确保了收款账户模块的高内聚、低耦合、可测试性强，并且能够灵活应对业务需求和技术变化。
