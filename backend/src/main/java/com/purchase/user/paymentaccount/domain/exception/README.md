# com.purchase.user.paymentaccount.domain.exception 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.domain.exception` 包包含了收款账户模块领域层定义的自定义异常类。这些异常旨在明确地表示与收款账户相关的特定业务错误，例如账户不存在或用户无权访问某个账户。通过在领域层定义这些业务异常，可以使错误处理更具语义性，并与通用的系统异常区分开来，从而提高应用程序的可维护性和错误诊断能力。

## 目录结构概览 (Directory Structure Overview)
*   `PaymentAccountAccessDeniedException.java`: 当用户无权访问或操作收款账户时抛出的异常。
*   `PaymentAccountNotFoundException.java`: 当尝试查找或操作的收款账户不存在时抛出的异常。
*   `PaymentAccountAccessDeniedException.md`: `PaymentAccountAccessDeniedException.java` 的文档。
*   `PaymentAccountNotFoundException.md`: `PaymentAccountNotFoundException.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.domain.exception` 包中的异常类是领域模型的一部分，它们与应用服务层（`com.purchase.user.paymentaccount.application.service`）以及全局异常处理器（`com.purchase.common.exception.GlobalExceptionHandler` 或 `com.purchase.user.paymentaccount.interfaces.exception.PaymentAccountExceptionHandler`）紧密协作：

1.  **领域层抛出:** 这些异常通常在领域实体（如 `PaymentAccount`）的方法内部，或在领域服务（如果存在）中，当检测到不符合业务规则或数据完整性约束的情况时被抛出。例如，当尝试根据 ID 加载一个不存在的 `PaymentAccount` 实体时，或者当一个用户尝试修改不属于自己的 `PaymentAccount` 时。

2.  **应用服务层捕获与传递:** 应用服务层（如 `PaymentAccountApplicationServiceImpl`）在调用领域逻辑时，会捕获这些领域异常。应用服务可能会直接将这些异常重新抛出，或者将其转换为更适合应用层或外部接口的错误表示（尽管当前设计中通常是直接抛出）。

3.  **全局异常处理器处理:** 最终，这些领域异常会被 Spring 的全局异常处理器（例如 `com.purchase.common.exception.GlobalExceptionHandler` 或模块特定的 `PaymentAccountExceptionHandler`）捕获。异常处理器会根据异常类型，将其映射到适当的 HTTP 状态码（例如，`PaymentAccountNotFoundException` 映射到 404 Not Found，`PaymentAccountAccessDeniedException` 映射到 403 Forbidden），并生成统一的错误响应格式（如 `com.purchase.common.response.Result`）返回给客户端。

**协作流程总结:**

*   当应用服务层尝试执行一个操作（如删除收款账户）时，它会首先从仓储中加载相应的 `PaymentAccount` 实体。如果账户不存在，仓储或应用服务会抛出 `PaymentAccountNotFoundException`。
*   如果账户存在，应用服务会进一步验证当前操作用户是否拥有该账户。如果用户无权操作，则抛出 `PaymentAccountAccessDeniedException`。
*   这些领域异常会沿着调用栈向上冒泡，最终被全局异常处理器捕获。处理器将这些业务异常转换为友好的 HTTP 错误响应，并返回给前端。

这种模式确保了业务错误在应用程序中得到清晰的识别和统一的处理，提高了 API 的可用性和可理解性。
