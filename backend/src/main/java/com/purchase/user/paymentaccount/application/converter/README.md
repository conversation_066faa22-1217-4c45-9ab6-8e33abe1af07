# com.purchase.user.paymentaccount.application.converter 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.application.converter` 包包含了收款账户模块中用于数据传输对象（DTO）与领域实体之间进行转换的转换器。该包的核心职责是实现应用程序不同层级之间数据模型的映射，确保数据在前端、应用服务层和领域模型之间能够顺畅、安全地流动。通过这种转换机制，它避免了将内部领域模型直接暴露给外部接口，从而维护了领域模型的独立性和完整性。

## 目录结构概览 (Directory Structure Overview)
*   `PaymentAccountDTOConverter.java`: 收款账户 DTO 与领域实体之间的双向转换器。
*   `PaymentAccountDTOConverter.md`: `PaymentAccountDTOConverter.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.application.converter` 包中的转换器是应用服务层（`com.purchase.user.paymentaccount.application.service`）与领域模型（`com.purchase.user.paymentaccount.domain.entity` 和 `com.purchase.user.paymentaccount.domain.valueobject`）之间进行数据交互的关键组件。它遵循了“防腐层”的思想，将外部数据格式与内部领域模型解耦。

1.  **请求 DTO 到领域实体:**
    *   当控制器接收到来自前端的 `CreatePaymentAccountRequest` DTO 时，应用服务层会调用 `PaymentAccountDTOConverter` 的 `toEntity()` 方法。
    *   `toEntity()` 方法负责将 DTO 中的数据映射到 `PaymentAccount` 领域实体。这个过程可能涉及将字符串类型的账户类型转换为强类型的 `AccountType` 枚举，并根据账户类型创建包含不同详细信息的 `PaymentAccount` 实例（例如，银行转账账户会包含 `BankInfo` 值对象）。
    *   这种转换确保了进入领域模型的数据是经过验证且符合领域规则的。

2.  **领域实体到响应 DTO:**
    *   当应用服务层需要将领域模型中的 `PaymentAccount` 实体返回给前端时，它会调用 `PaymentAccountDTOConverter` 的 `toResponse()` 方法。
    *   `toResponse()` 方法负责将 `PaymentAccount` 实体中的数据映射到 `PaymentAccountResponse` DTO。这个过程可能涉及将领域层的 `BankInfo` 值对象转换为应用层的 `BankInfo` DTO，并确保所有相关字段都被正确复制。
    *   `toResponseList()` 方法则提供了批量转换的能力，方便处理列表数据。

**协作流程总结:**

*   **前端请求:** 前端发送包含 `CreatePaymentAccountRequest` 的 JSON 数据到控制器。
*   **控制器:** 接收请求，并将其传递给应用服务层。
*   **应用服务层:** 调用 `PaymentAccountDTOConverter.toEntity()` 将 `CreatePaymentAccountRequest` 转换为 `PaymentAccount` 领域实体。然后，应用服务层执行业务逻辑（例如，保存实体到数据库）。在操作完成后，应用服务层调用 `PaymentAccountDTOConverter.toResponse()` 将 `PaymentAccount` 实体转换为 `PaymentAccountResponse` DTO。
*   **控制器返回:** 控制器将 `PaymentAccountResponse` DTO 封装在统一的 `Result` 对象中返回给前端。

这种转换模式确保了：
*   **领域模型独立性:** 领域模型可以专注于业务逻辑，而不受外部数据格式的影响。
*   **API 灵活性:** 外部 API 格式可以独立于内部领域模型进行演变。
*   **数据安全:** 可以在转换过程中对敏感数据进行过滤或脱敏，避免不必要的信息泄露。
