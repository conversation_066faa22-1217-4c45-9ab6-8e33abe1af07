# com.purchase.user.paymentaccount.domain.entity 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.user.paymentaccount.domain.entity` 包包含了收款账户模块的领域实体。在领域驱动设计（DDD）中，实体是具有唯一标识和生命周期的对象，它们封装了核心业务逻辑和状态。该包的核心是 `PaymentAccount` 实体，它作为聚合根，定义了收款账户的所有业务行为和属性，确保了领域模型的一致性和业务规则的强制执行。

## 目录结构概览 (Directory Structure Overview)
*   `PaymentAccount.java`: 收款账户聚合根实体，封装了收款账户的业务逻辑和状态。
*   `PaymentAccount.md`: `PaymentAccount.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.user.paymentaccount.domain.entity` 包中的实体是领域模型的核心，它们与以下组件紧密协作：

1.  **值对象 (`com.purchase.user.paymentaccount.domain.valueobject`):**
    *   `PaymentAccount` 实体内部使用了值对象，例如 `AccountType` 枚举和 `BankInfo` 值对象。这些值对象封装了更小的、无标识的概念，并强制其不变性，从而使领域实体更加清晰和健壮。

2.  **领域服务 (`com.purchase.user.paymentaccount.domain.service` - 如果存在):**
    *   虽然当前目录结构中没有明确的领域服务包，但如果存在跨多个实体或需要协调多个聚合的复杂业务逻辑，这些逻辑将封装在领域服务中。领域服务会调用 `PaymentAccount` 实体的方法来执行业务操作。

3.  **领域仓储 (`com.purchase.user.paymentaccount.domain.repository`):**
    *   `PaymentAccount` 实体通过领域仓储接口（如 `PaymentAccountRepository`）进行持久化。仓储负责将领域实体从内存映射到数据库，反之亦然。实体本身不关心持久化的细节，保持了领域模型的纯粹性。

4.  **应用服务 (`com.purchase.user.paymentaccount.application.service`):**
    *   应用服务层（如 `PaymentAccountApplicationServiceImpl`）是领域实体的直接调用者。它接收来自前端的 DTO，将其转换为领域实体，然后调用领域实体的方法来执行业务用例。在操作完成后，应用服务会将领域实体转换回 DTO 返回给前端。

**协作流程总结:**

*   **创建账户:** 应用服务层接收到创建账户的请求 DTO 后，会调用 `PaymentAccount` 实体中的静态工厂方法（如 `createBankTransferAccount`）来创建新的 `PaymentAccount` 实例。这些工厂方法在创建时会强制执行业务规则和参数验证。
*   **修改账户状态/信息:** 当需要修改账户的状态（如设置默认、验证、批准、拒绝、停用、激活）或更新账户信息时，应用服务层会从仓储中加载 `PaymentAccount` 实体，然后调用实体自身的方法（如 `setAsDefault()`, `approve()`, `updateBasicInfo()`）来执行这些业务行为。这些方法内部封装了状态转换和业务规则。
*   **持久化:** 领域实体在被修改后，会通过领域仓储的 `save()` 或 `update()` 方法被持久化到数据库。

这种设计模式确保了业务逻辑集中在领域实体内部，使得领域模型更加健壮、可测试和易于理解，是 DDD 的核心实践之一。
