# PaymentAccountMapper.java

## 文件概述 (File Overview)
`PaymentAccountMapper.java` 是一个 MyBatis Mapper 接口，用于定义与 `user_payment_account` 表（对应 `PaymentAccountPO` 持久化对象）进行数据库交互的方法。它继承自 MyBatis-Plus 的 `BaseMapper`，从而获得了基本的 CRUD 操作。此外，该接口还定义了多个自定义查询和更新方法，用于根据用户 ID、账户类型、默认状态和验证状态等条件查询和修改支付账户信息，是基础设施层中数据访问的核心组件。

## 核心功能 (Core Functionality)
*   **继承 `BaseMapper`:** 自动拥有对 `PaymentAccountPO` 实体的基本 CRUD 操作（插入、删除、更新、查询）。
*   **根据用户 ID 查询:** `findByUserId` 用于查询指定用户的所有未逻辑删除的账户。
*   **根据用户 ID 和账户类型查询:** `findByUserIdAndAccountType` 用于查询指定用户和账户类型的账户。
*   **查找默认账户:** `findDefaultAccountByUserId` 和 `findDefaultAccountByUserIdAndType` 用于查找用户的默认账户。
*   **查找待验证账户:** `findPendingAccounts` 用于查询所有状态为 `pending` 的账户。
*   **统计和存在性检查:** `countByUserId` 和 `countByUserIdAndAccountType` 用于统计账户数量和检查账户是否存在。
*   **逻辑删除:** `softDeleteById` 方法用于对指定 ID 的账户进行逻辑删除。
*   **取消其他默认账户:** `unsetOtherDefaultAccounts` 方法用于将指定用户除某个账户外的所有其他账户的默认状态取消。
*   **包含已删除查询:** `findByIdIncludeDeleted` 用于查询包含已逻辑删除的账户。

## 接口说明 (Interface Description)

### 继承自 `BaseMapper<PaymentAccountPO>`
*   提供了 `insert`, `deleteById`, `updateById`, `selectById`, `selectList`, `selectPage` 等通用方法。

### 自定义方法 (Custom Methods)
*   `List<PaymentAccountPO> findByUserId(@Param("userId") Long userId)`:
    *   **SQL:** `SELECT * FROM user_payment_account WHERE user_id = #{userId} AND deleted = '0' ORDER BY created_at DESC`
    *   **参数:** `userId` (Long) - 用户 ID。
    *   **返回值:** `List<PaymentAccountPO>` - 账户列表。
    *   **业务逻辑:** 查询指定用户的所有未逻辑删除的支付账户，并按创建时间倒序排序。

*   `Optional<PaymentAccountPO> findByUserIdAndAccountType(@Param("userId") Long userId, @Param("accountType") String accountType)`:
    *   **SQL:** `SELECT * FROM user_payment_account WHERE user_id = #{userId} AND account_type = #{accountType} AND deleted = '0' LIMIT 1`
    *   **参数:** `userId` (Long), `accountType` (String)。
    *   **返回值:** `Optional<PaymentAccountPO>` - 账户信息。
    *   **业务逻辑:** 查找指定用户和账户类型的未逻辑删除的单个账户。

*   `Optional<PaymentAccountPO> findDefaultAccountByUserId(@Param("userId") Long userId)`:
    *   **SQL:** `SELECT * FROM user_payment_account WHERE user_id = #{userId} AND is_default = 1 AND deleted = '0' LIMIT 1`
    *   **参数:** `userId` (Long)。
    *   **返回值:** `Optional<PaymentAccountPO>` - 默认账户。
    *   **业务逻辑:** 查找指定用户的默认收款账户。

*   `Optional<PaymentAccountPO> findDefaultAccountByUserIdAndType(@Param("userId") Long userId, @Param("accountType") String accountType)`:
    *   **SQL:** `SELECT * FROM user_payment_account WHERE user_id = #{userId} AND account_type = #{accountType} AND is_default = 1 AND deleted = '0' LIMIT 1`
    *   **参数:** `userId` (Long), `accountType` (String)。
    *   **返回值:** `Optional<PaymentAccountPO>` - 默认账户。
    *   **业务逻辑:** 查找指定用户和账户类型的默认收款账户。

*   `List<PaymentAccountPO> findPendingAccounts()`:
    *   **SQL:** `SELECT * FROM user_payment_account WHERE status = 'pending' AND deleted = '0' ORDER BY created_at ASC`
    *   **参数:** 无。
    *   **返回值:** `List<PaymentAccountPO>` - 待验证账户列表。
    *   **业务逻辑:** 查询所有状态为 `pending` 且未逻辑删除的收款账户，并按创建时间升序排序。

*   `long countByUserId(@Param("userId") Long userId)`:
    *   **SQL:** `SELECT COUNT(*) FROM user_payment_account WHERE user_id = #{userId} AND deleted = '0'`
    *   **参数:** `userId` (Long)。
    *   **返回值:** `long` - 账户数量。
    *   **业务逻辑:** 统计指定用户拥有的未逻辑删除的收款账户数量。

*   `long countByUserIdAndAccountType(@Param("userId") Long userId, @Param("accountType") String accountType)`:
    *   **SQL:** `SELECT COUNT(*) FROM user_payment_account WHERE user_id = #{userId} AND account_type = #{accountType} AND deleted = '0'`
    *   **参数:** `userId` (Long), `accountType` (String)。
    *   **返回值:** `long` - 存在数量。
    *   **业务逻辑:** 检查指定用户是否已存在指定类型的未逻辑删除账户。

*   `int softDeleteById(@Param("id") Long id)`:
    *   **SQL:** `UPDATE user_payment_account SET deleted = '1', updated_at = NOW() WHERE id = #{id}`
    *   **参数:** `id` (Long)。
    *   **返回值:** `int` - 影响行数。
    *   **业务逻辑:** 对指定 ID 的账户执行逻辑删除，将 `deleted` 字段设为 '1' 并更新 `updated_at`。

*   `Optional<PaymentAccountPO> findByIdIncludeDeleted(@Param("id") Long id)`:
    *   **SQL:** `SELECT * FROM user_payment_account WHERE id = #{id}`
    *   **参数:** `id` (Long)。
    *   **返回值:** `Optional<PaymentAccountPO>` - 账户信息。
    *   **业务逻辑:** 根据 ID 查找账户，包括已逻辑删除的账户。

*   `int unsetOtherDefaultAccounts(@Param("userId") Long userId, @Param("excludeId") Long excludeId)`:
    *   **SQL:** `UPDATE user_payment_account SET is_default = 0, updated_at = NOW() WHERE user_id = #{userId} AND id != #{excludeId} AND deleted = '0'`
    *   **参数:** `userId` (Long), `excludeId` (Long)。
    *   **返回值:** `int` - 影响行数。
    *   **业务逻辑:** 将指定用户下除 `excludeId` 之外的所有未逻辑删除账户的 `is_default` 字段设为 0，并更新 `updated_at`。

## 使用示例 (Usage Examples)

```java
// 在基础设施层的仓储实现中注入并使用Mapper接口
@Repository
public class PaymentAccountRepositoryImpl implements PaymentAccountRepository {
    @Autowired
    private PaymentAccountMapper paymentAccountMapper;
    @Autowired
    private PaymentAccountConverter converter;

    @Override
    public PaymentAccount save(PaymentAccount domain) {
        PaymentAccountPO po = converter.toPO(domain);
        paymentAccountMapper.insert(po);
        // 假设insert后PO的ID会被填充，再转回领域对象以包含ID
        return converter.toDomain(po);
    }

    @Override
    public Optional<PaymentAccount> findById(Long id) {
        PaymentAccountPO po = paymentAccountMapper.selectById(id);
        return Optional.ofNullable(converter.toDomain(po));
    }

    @Override
    public void softDelete(Long id) {
        paymentAccountMapper.softDeleteById(id);
    }

    @Override
    public Optional<PaymentAccount> findDefaultAccountByUserId(Long userId) {
        Optional<PaymentAccountPO> poOpt = paymentAccountMapper.findDefaultAccountByUserId(userId);
        return poOpt.map(converter::toDomain);
    }
}
```

## 注意事项 (Notes)
*   **`@Mapper` 注解:** 确保接口上添加了 `@Mapper` 注解，以便 Spring 能够扫描并将其注册为 MyBatis Mapper Bean。
*   **`BaseMapper`:** 继承 `BaseMapper` 极大地减少了编写通用 CRUD SQL 的工作量。对于复杂的查询或非通用操作，可以在此接口中定义自定义方法，并通过 `@Select`、`@Update` 等注解直接编写 SQL 语句。
*   **持久化对象 (`PaymentAccountPO`):** 该 Mapper 接口操作的是 `PaymentAccountPO`，而不是领域实体 `PaymentAccount`，这符合基础设施层与领域层解耦的原则。
*   **`@Param` 注解:** 在自定义方法中，如果参数多于一个，或者需要为参数指定明确的名称以便在 XML Mapper 文件中引用，应使用 `@Param` 注解。
*   **逻辑删除:** 所有的查询都包含了 `AND deleted = '0'` 条件，以确保只查询未逻辑删除的记录。`softDeleteById` 方法实现了逻辑删除的更新操作。
*   **事务管理:** Mapper 方法通常在服务层的方法中进行事务管理，以保证数据库操作的原子性。
