# PortService.java

## 文件概述 (File Overview)
`PortService.java` 是港口服务接口，位于 `com.purchase.ports.service` 包中。该接口定义了港口数据管理的完整业务规范，提供了港口信息的查询、搜索、分页等核心功能。作为采购系统中物流港口模块的服务层接口，它支持多种查询方式（按代码、按名称、关键词搜索等），并为前端组件（如港口选择器）提供了专用的数据接口。该接口包含11个方法，总计76行代码，是港口数据服务的标准规范。

## 核心功能 (Core Functionality)
*   **港口查询**: 提供多种维度的港口信息查询功能
*   **搜索支持**: 支持按港口代码、中英文名称、关键词等多种方式搜索
*   **分页处理**: 所有列表查询都支持分页，提高查询性能
*   **批量操作**: 支持批量获取港口信息，减少网络请求
*   **精确查找**: 提供按港口代码精确查找的功能
*   **统计功能**: 提供港口总数和搜索结果统计
*   **组件支持**: 为前端港口选择器组件提供专用接口
*   **快速搜索**: 提供限制结果数量的快速搜索功能

## 接口说明 (Interface Description)

### 基础查询方法

#### getAllPorts - 获取所有港口（分页）
*   **方法签名**: `List<PortDTO> getAllPorts(Integer page, Integer size)`
*   **参数**: 
    *   `page` (Integer) - 页码，从1开始
    *   `size` (Integer) - 每页大小
*   **返回值**: `List<PortDTO>` - 港口数据传输对象列表
*   **业务场景**: 管理后台港口列表展示，支持分页浏览所有港口
*   **使用说明**: 用于获取系统中所有港口的分页列表

#### getPortById - 根据ID获取港口详情
*   **方法签名**: `PortDTO getPortById(Long portId)`
*   **参数**: `portId` (Long) - 港口ID
*   **返回值**: `PortDTO` - 港口详情对象，如果不存在则返回null
*   **业务场景**: 获取特定港口的详细信息，用于港口详情页面展示
*   **使用说明**: 通过唯一的港口ID获取完整的港口信息

#### getPortByCode - 根据港口代码精确查找港口
*   **方法签名**: `PortDTO getPortByCode(String portCode)`
*   **参数**: `portCode` (String) - 港口代码（如"CNSHA"表示上海港）
*   **返回值**: `PortDTO` - 港口对象，如果不存在则返回null
*   **业务场景**: 通过标准港口代码精确定位港口，常用于物流系统集成
*   **使用说明**: 港口代码通常遵循UN/LOCODE标准，具有唯一性

#### getPortsByIds - 批量获取港口信息
*   **方法签名**: `List<PortDTO> getPortsByIds(List<Long> portIds)`
*   **参数**: `portIds` (List<Long>) - 港口ID列表
*   **返回值**: `List<PortDTO>` - 港口对象列表
*   **业务场景**: 批量获取多个港口信息，减少数据库查询次数
*   **使用说明**: 适用于需要同时获取多个港口信息的场景

### 搜索方法

#### searchPorts - 根据关键词搜索港口
*   **方法签名**: `List<PortDTO> searchPorts(String keyword, Integer page, Integer size)`
*   **参数**: 
    *   `keyword` (String) - 搜索关键词，支持港口代码、中文名、英文名
    *   `page` (Integer) - 页码
    *   `size` (Integer) - 每页大小
*   **返回值**: `List<PortDTO>` - 匹配的港口列表
*   **业务场景**: 用户输入关键词进行港口搜索，支持模糊匹配
*   **使用说明**: 关键词会在港口代码、中文名称、英文名称中进行模糊匹配

#### searchByPortCode - 根据港口代码搜索港口
*   **方法签名**: `List<PortDTO> searchByPortCode(String portCode, Integer page, Integer size)`
*   **参数**: 
    *   `portCode` (String) - 港口代码（支持部分匹配）
    *   `page` (Integer) - 页码
    *   `size` (Integer) - 每页大小
*   **返回值**: `List<PortDTO>` - 匹配的港口列表
*   **业务场景**: 按港口代码进行模糊搜索，如输入"CN"查找所有中国港口
*   **使用说明**: 支持港口代码的前缀匹配和模糊匹配

#### searchByPortName - 根据港口名称搜索港口
*   **方法签名**: `List<PortDTO> searchByPortName(String portName, Integer page, Integer size)`
*   **参数**: 
    *   `portName` (String) - 港口名称（支持中文和英文）
    *   `page` (Integer) - 页码
    *   `size` (Integer) - 每页大小
*   **返回值**: `List<PortDTO>` - 匹配的港口列表
*   **业务场景**: 按港口名称进行搜索，支持中英文名称匹配
*   **使用说明**: 支持港口中文名称和英文名称的模糊匹配

### 统计方法

#### getTotalCount - 获取港口总数
*   **方法签名**: `Long getTotalCount()`
*   **返回值**: `Long` - 系统中港口的总数量
*   **业务场景**: 用于分页计算和统计展示
*   **使用说明**: 返回系统中所有有效港口的数量

#### getSearchCount - 根据关键词搜索港口总数
*   **方法签名**: `Long getSearchCount(String keyword)`
*   **参数**: `keyword` (String) - 搜索关键词
*   **返回值**: `Long` - 匹配关键词的港口数量
*   **业务场景**: 搜索结果的分页计算和结果统计
*   **使用说明**: 配合搜索功能使用，用于计算搜索结果的总页数

### 组件专用方法

#### getPortsForSelector - 港口选择器专用接口（分页）
*   **方法签名**: `PageResult<PortDTO> getPortsForSelector(Integer page, Integer size, String keyword)`
*   **参数**: 
    *   `page` (Integer) - 页码
    *   `size` (Integer) - 每页大小
    *   `keyword` (String) - 搜索关键词，可选
*   **返回值**: `PageResult<PortDTO>` - 包含分页信息的港口数据结果
*   **业务场景**: 为前端港口选择器组件提供数据，支持分页和搜索
*   **使用说明**: 返回的PageResult包含数据列表、总数、当前页等完整分页信息

#### quickSearchPortsForSelector - 港口选择器专用接口（快速搜索）
*   **方法签名**: `List<PortDTO> quickSearchPortsForSelector(String keyword, Integer limit)`
*   **参数**: 
    *   `keyword` (String) - 搜索关键词
    *   `limit` (Integer) - 返回结果数量限制
*   **返回值**: `List<PortDTO>` - 限制数量的港口列表
*   **业务场景**: 为港口选择器提供快速搜索功能，如自动完成、下拉提示等
*   **使用说明**: 通常用于实时搜索场景，限制返回结果数量以提高响应速度

## 使用示例 (Usage Examples)

```java
// 1. 服务实现类示例
@Service
public class PortServiceImpl implements PortService {
    
    @Autowired
    private PortMapper portMapper;
    
    @Override
    public List<PortDTO> getAllPorts(Integer page, Integer size) {
        // 参数验证
        if (page == null || page < 1) page = 1;
        if (size == null || size < 1) size = 20;
        
        // 计算偏移量
        int offset = (page - 1) * size;
        
        // 查询港口数据
        List<Port> ports = portMapper.selectAllPorts(offset, size);
        
        // 转换为DTO
        return ports.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }
    
    @Override
    public PortDTO getPortByCode(String portCode) {
        if (StringUtils.isEmpty(portCode)) {
            return null;
        }
        
        Port port = portMapper.selectByPortCode(portCode.toUpperCase());
        return port != null ? convertToDTO(port) : null;
    }
    
    @Override
    public List<PortDTO> searchPorts(String keyword, Integer page, Integer size) {
        if (StringUtils.isEmpty(keyword)) {
            return getAllPorts(page, size);
        }
        
        // 参数处理
        if (page == null || page < 1) page = 1;
        if (size == null || size < 1) size = 20;
        int offset = (page - 1) * size;
        
        // 执行搜索
        List<Port> ports = portMapper.searchPorts(keyword, offset, size);
        
        return ports.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }
    
    @Override
    public PageResult<PortDTO> getPortsForSelector(Integer page, Integer size, String keyword) {
        // 参数处理
        if (page == null || page < 1) page = 1;
        if (size == null || size < 1) size = 10; // 选择器默认较小的页面大小
        
        List<PortDTO> ports;
        Long total;
        
        if (StringUtils.isEmpty(keyword)) {
            ports = getAllPorts(page, size);
            total = getTotalCount();
        } else {
            ports = searchPorts(keyword, page, size);
            total = getSearchCount(keyword);
        }
        
        return new PageResult<>(ports, page, size, total);
    }
    
    @Override
    public List<PortDTO> quickSearchPortsForSelector(String keyword, Integer limit) {
        if (StringUtils.isEmpty(keyword)) {
            return Collections.emptyList();
        }
        
        if (limit == null || limit < 1) limit = 10;
        
        List<Port> ports = portMapper.quickSearchPorts(keyword, limit);
        
        return ports.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }
    
    // 实体转DTO的私有方法
    private PortDTO convertToDTO(Port port) {
        PortDTO dto = new PortDTO();
        dto.setId(port.getId());
        dto.setPortCode(port.getPortCode());
        dto.setPortNameCn(port.getPortNameCn());
        dto.setPortNameEn(port.getPortNameEn());
        dto.setCountryCode(port.getCountryCode());
        dto.setCountryName(port.getCountryName());
        dto.setRegion(port.getRegion());
        dto.setLatitude(port.getLatitude());
        dto.setLongitude(port.getLongitude());
        dto.setTimeZone(port.getTimeZone());
        return dto;
    }
}

// 2. 控制器层调用示例
@RestController
@RequestMapping("/api/v1/ports")
public class PortController {
    
    @Autowired
    private PortService portService;
    
    // 获取港口列表
    @GetMapping
    public Result<List<PortDTO>> getPorts(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        List<PortDTO> ports = portService.getAllPorts(page, size);
        return Result.success(ports);
    }
    
    // 搜索港口
    @GetMapping("/search")
    public Result<List<PortDTO>> searchPorts(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        List<PortDTO> ports = portService.searchPorts(keyword, page, size);
        Long total = portService.getSearchCount(keyword);
        
        Map<String, Object> result = new HashMap<>();
        result.put("ports", ports);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        
        return Result.success(result);
    }
    
    // 根据港口代码获取港口
    @GetMapping("/code/{portCode}")
    public Result<PortDTO> getPortByCode(@PathVariable String portCode) {
        PortDTO port = portService.getPortByCode(portCode);
        if (port == null) {
            return Result.error("港口不存在");
        }
        return Result.success(port);
    }
    
    // 港口选择器接口
    @GetMapping("/selector")
    public Result<PageResult<PortDTO>> getPortsForSelector(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        
        PageResult<PortDTO> result = portService.getPortsForSelector(page, size, keyword);
        return Result.success(result);
    }
    
    // 快速搜索接口
    @GetMapping("/quick-search")
    public Result<List<PortDTO>> quickSearch(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        List<PortDTO> ports = portService.quickSearchPortsForSelector(keyword, limit);
        return Result.success(ports);
    }
}

// 3. 前端JavaScript调用示例
const PortAPI = {
    // 获取港口列表
    async getPorts(page = 1, size = 20) {
        const response = await fetch(`/api/v1/ports?page=${page}&size=${size}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        return await response.json();
    },

    // 搜索港口
    async searchPorts(keyword, page = 1, size = 20) {
        const response = await fetch(
            `/api/v1/ports/search?keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        return await response.json();
    },

    // 快速搜索（用于自动完成）
    async quickSearch(keyword, limit = 10) {
        const response = await fetch(
            `/api/v1/ports/quick-search?keyword=${encodeURIComponent(keyword)}&limit=${limit}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        return await response.json();
    },

    // 根据港口代码获取港口
    async getPortByCode(portCode) {
        const response = await fetch(`/api/v1/ports/code/${portCode}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        return await response.json();
    }
};

// 港口选择器组件
class PortSelector {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            placeholder: '请选择港口',
            searchPlaceholder: '搜索港口代码或名称',
            pageSize: 10,
            quickSearchLimit: 10,
            ...options
        };
        this.selectedPort = null;
        this.currentPage = 1;
        this.searchKeyword = '';
        this.init();
    }

    init() {
        this.render();
        this.bindEvents();
        this.loadPorts();
    }

    render() {
        this.container.innerHTML = `
            <div class="port-selector">
                <div class="port-selector-input">
                    <input type="text"
                           placeholder="${this.options.placeholder}"
                           readonly
                           class="selected-port-display" />
                    <button class="selector-toggle">▼</button>
                </div>
                <div class="port-selector-dropdown" style="display: none;">
                    <div class="search-box">
                        <input type="text"
                               placeholder="${this.options.searchPlaceholder}"
                               class="port-search-input" />
                    </div>
                    <div class="port-list"></div>
                    <div class="pagination"></div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        const toggle = this.container.querySelector('.selector-toggle');
        const dropdown = this.container.querySelector('.port-selector-dropdown');
        const searchInput = this.container.querySelector('.port-search-input');

        // 切换下拉框显示
        toggle.addEventListener('click', () => {
            const isVisible = dropdown.style.display !== 'none';
            dropdown.style.display = isVisible ? 'none' : 'block';
        });

        // 搜索输入
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.searchKeyword = e.target.value;
                this.currentPage = 1;
                this.loadPorts();
            }, 300);
        });

        // 点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                dropdown.style.display = 'none';
            }
        });
    }

    async loadPorts() {
        try {
            const result = await PortAPI.searchPorts(
                this.searchKeyword,
                this.currentPage,
                this.options.pageSize
            );

            if (result.success) {
                this.renderPortList(result.data.ports);
                this.renderPagination(result.data.total);
            }
        } catch (error) {
            console.error('加载港口数据失败:', error);
        }
    }

    renderPortList(ports) {
        const portList = this.container.querySelector('.port-list');

        if (ports.length === 0) {
            portList.innerHTML = '<div class="no-data">暂无数据</div>';
            return;
        }

        portList.innerHTML = ports.map(port => `
            <div class="port-item" data-port='${JSON.stringify(port)}'>
                <div class="port-code">${port.portCode}</div>
                <div class="port-name">
                    <div class="name-cn">${port.portNameCn}</div>
                    <div class="name-en">${port.portNameEn}</div>
                </div>
                <div class="port-country">${port.countryName}</div>
            </div>
        `).join('');

        // 绑定选择事件
        portList.querySelectorAll('.port-item').forEach(item => {
            item.addEventListener('click', () => {
                const port = JSON.parse(item.dataset.port);
                this.selectPort(port);
            });
        });
    }

    selectPort(port) {
        this.selectedPort = port;

        // 更新显示
        const display = this.container.querySelector('.selected-port-display');
        display.value = `${port.portCode} - ${port.portNameCn}`;

        // 隐藏下拉框
        const dropdown = this.container.querySelector('.port-selector-dropdown');
        dropdown.style.display = 'none';

        // 触发选择事件
        if (this.options.onSelect) {
            this.options.onSelect(port);
        }
    }

    renderPagination(total) {
        const pagination = this.container.querySelector('.pagination');
        const totalPages = Math.ceil(total / this.options.pageSize);

        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // 上一页
        if (this.currentPage > 1) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage - 1}">上一页</button>`;
        }

        // 页码
        for (let i = Math.max(1, this.currentPage - 2);
             i <= Math.min(totalPages, this.currentPage + 2);
             i++) {
            const activeClass = i === this.currentPage ? 'active' : '';
            paginationHTML += `<button class="page-btn ${activeClass}" data-page="${i}">${i}</button>`;
        }

        // 下一页
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage + 1}">下一页</button>`;
        }

        pagination.innerHTML = paginationHTML;

        // 绑定分页事件
        pagination.querySelectorAll('.page-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.currentPage = parseInt(btn.dataset.page);
                this.loadPorts();
            });
        });
    }

    // 获取选中的港口
    getSelectedPort() {
        return this.selectedPort;
    }

    // 设置选中的港口
    setSelectedPort(port) {
        this.selectPort(port);
    }

    // 清空选择
    clear() {
        this.selectedPort = null;
        const display = this.container.querySelector('.selected-port-display');
        display.value = '';
    }
}

// 4. 业务集成示例
@Service
public class ShippingService {

    @Autowired
    private PortService portService;

    // 计算两个港口之间的运输路线
    public ShippingRoute calculateRoute(String originPortCode, String destinationPortCode) {
        // 获取起始港口和目的港口信息
        PortDTO originPort = portService.getPortByCode(originPortCode);
        PortDTO destinationPort = portService.getPortByCode(destinationPortCode);

        if (originPort == null || destinationPort == null) {
            throw new BusinessException("港口信息不存在");
        }

        // 计算距离和预估运输时间
        double distance = calculateDistance(originPort, destinationPort);
        int estimatedDays = calculateEstimatedDays(distance);

        ShippingRoute route = new ShippingRoute();
        route.setOriginPort(originPort);
        route.setDestinationPort(destinationPort);
        route.setDistance(distance);
        route.setEstimatedDays(estimatedDays);

        return route;
    }

    // 获取港口的运输选项
    public List<ShippingOption> getShippingOptions(String portCode) {
        PortDTO port = portService.getPortByCode(portCode);
        if (port == null) {
            return Collections.emptyList();
        }

        // 根据港口特性返回可用的运输选项
        List<ShippingOption> options = new ArrayList<>();

        // 海运选项
        if (isSeaPort(port)) {
            options.add(new ShippingOption("SEA", "海运", "经济实惠，适合大批量货物"));
        }

        // 空运选项（如果港口附近有机场）
        if (hasNearbyAirport(port)) {
            options.add(new ShippingOption("AIR", "空运", "快速到达，适合紧急货物"));
        }

        // 陆运选项
        if (hasLandConnection(port)) {
            options.add(new ShippingOption("LAND", "陆运", "灵活便捷，适合短距离运输"));
        }

        return options;
    }

    // 推荐相关港口
    public List<PortDTO> getRelatedPorts(String portCode, int limit) {
        PortDTO port = portService.getPortByCode(portCode);
        if (port == null) {
            return Collections.emptyList();
        }

        // 查找同一国家的其他港口
        List<PortDTO> relatedPorts = portService.searchByPortCode(port.getCountryCode(), 1, limit * 2)
            .stream()
            .filter(p -> !p.getPortCode().equals(portCode))
            .limit(limit)
            .collect(Collectors.toList());

        return relatedPorts;
    }
}

// 5. 缓存集成示例
@Service
public class CachedPortService implements PortService {

    @Autowired
    private PortService portService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String CACHE_PREFIX = "port:";
    private static final Duration CACHE_TTL = Duration.ofHours(24);

    @Override
    @Cacheable(value = "ports", key = "#portCode")
    public PortDTO getPortByCode(String portCode) {
        return portService.getPortByCode(portCode);
    }

    @Override
    public List<PortDTO> searchPorts(String keyword, Integer page, Integer size) {
        String cacheKey = CACHE_PREFIX + "search:" + keyword + ":" + page + ":" + size;

        @SuppressWarnings("unchecked")
        List<PortDTO> cached = (List<PortDTO>) redisTemplate.opsForValue().get(cacheKey);

        if (cached != null) {
            return cached;
        }

        List<PortDTO> result = portService.searchPorts(keyword, page, size);
        redisTemplate.opsForValue().set(cacheKey, result, CACHE_TTL);

        return result;
    }

    @Override
    public List<PortDTO> quickSearchPortsForSelector(String keyword, Integer limit) {
        String cacheKey = CACHE_PREFIX + "quick:" + keyword + ":" + limit;

        @SuppressWarnings("unchecked")
        List<PortDTO> cached = (List<PortDTO>) redisTemplate.opsForValue().get(cacheKey);

        if (cached != null) {
            return cached;
        }

        List<PortDTO> result = portService.quickSearchPortsForSelector(keyword, limit);
        redisTemplate.opsForValue().set(cacheKey, result, Duration.ofMinutes(30)); // 快速搜索缓存时间较短

        return result;
    }

    // 清除缓存
    public void clearCache() {
        Set<String> keys = redisTemplate.keys(CACHE_PREFIX + "*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }

    // 其他方法委托给原始服务...
}
```

## 注意事项 (Notes)
*   **接口设计**: 作为服务接口，只定义方法签名和业务规范，不包含具体实现
*   **参数验证**: 实现类需要对所有输入参数进行有效性验证，特别是分页参数
*   **分页处理**: 所有列表查询都支持分页，避免大数据量查询导致的性能问题
*   **搜索优化**: 搜索功能需要考虑数据库索引优化，提高查询性能
*   **数据一致性**: 港口数据相对稳定，但需要考虑数据更新时的一致性
*   **缓存策略**: 港口数据适合缓存，可以显著提高查询性能
*   **国际化**: 港口名称涉及多语言，需要考虑字符编码和显示问题
*   **标准化**: 港口代码应遵循国际标准（如UN/LOCODE），确保数据规范性
*   **异常处理**: 实现类需要处理各种异常情况，如数据不存在、网络异常等
*   **性能监控**: 搜索和查询操作需要监控性能，及时发现慢查询
*   **数据完整性**: 确保港口数据的完整性和准确性，定期进行数据校验
*   **组件适配**: 为前端组件提供的专用接口需要考虑组件的特殊需求
*   **批量操作**: 批量查询时需要考虑数据量限制，避免内存溢出
*   **搜索精度**: 搜索功能需要平衡查询精度和性能，提供合适的匹配策略
*   **数据更新**: 港口数据更新时需要考虑缓存失效和数据同步问题
*   **接口版本**: 接口变更时需要考虑向后兼容性，避免影响现有功能
