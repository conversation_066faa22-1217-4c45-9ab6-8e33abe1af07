# PortMapper.md

## 1. 文件概述

`PortMapper.java` 是港口模块中的一个MyBatis-Plus Mapper接口，位于 `com.purchase.ports.mapper` 包中。它继承自MyBatis-Plus的 `BaseMapper<Port>` 接口，并在此基础上定义了多个自定义的查询方法。`Port` 实体代表了系统中的港口信息，包含了港口代码、中文名、英文名等。该Mapper接口是港口服务层与数据库进行交互的桥梁，负责将业务对象的操作转换为SQL语句，实现港口数据的持久化管理和多维度查询。

## 2. 核心功能

*   **基础CRUD操作**: 继承 `BaseMapper`，自动拥有对 `Port` 实体进行插入（`insert`）、根据ID查询（`selectById`）、根据条件查询列表（`selectList`）、更新（`updateById`）和删除（`deleteById`）等基础的增删改查功能。
*   **多维度模糊搜索**: 提供了 `searchByPortCode`（按港口代码）、`searchByPortName`（按中文名或英文名）和 `searchPorts`（综合代码、中英文名）等多种模糊搜索方法，满足不同场景的查询需求。
*   **精确查找**: 提供了 `selectByPortCode` 方法，用于根据港口代码进行精确查找。
*   **分页查询**: 所有搜索和查询方法都支持分页，通过 `offset` 和 `size` 参数控制返回结果的数量。
*   **总数统计**: 提供了 `countAllPorts` 和 `countSearchPorts` 方法，用于获取总记录数和搜索结果的总数，配合分页查询使用。
*   **排序优化**: 搜索方法中的SQL包含了 `ORDER BY CASE ... END` 逻辑，实现了更智能的搜索结果排序，将精确匹配或前缀匹配的结果排在前面。

## 3. 接口说明

`PortMapper` 在继承 `BaseMapper` 的基础上，定义了以下自定义方法：

### 3.1 搜索查询方法

#### searchByPortCode - 根据港口代码搜索港口（模糊查询）
*   **方法签名**: `List<Port> searchByPortCode(@Param("portCode") String portCode, @Param("offset") Integer offset, @Param("size") Integer size)`
*   **描述**: 根据港口代码进行模糊查询，返回匹配的港口列表，并支持分页。
*   **参数**:
    *   `portCode` (String): 港口代码关键词。
    *   `offset` (Integer): 查询起始位置（偏移量）。
    *   `size` (Integer): 返回记录的数量。
*   **返回值**: `List<Port>` - 匹配的港口实体列表。

#### searchByPortName - 根据港口名称搜索港口（模糊查询）
*   **方法签名**: `List<Port> searchByPortName(@Param("keyword") String keyword, @Param("offset") Integer offset, @Param("size") Integer size)`
*   **描述**: 根据港口中文名或英文名进行模糊查询，返回匹配的港口列表，并支持分页。结果会根据匹配度进行排序。
*   **参数**:
    *   `keyword` (String): 港口名称关键词。
    *   `offset` (Integer): 查询起始位置。
    *   `size` (Integer): 返回记录的数量。
*   **返回值**: `List<Port>` - 匹配的港口实体列表。

#### searchPorts - 综合搜索港口
*   **方法签名**: `List<Port> searchPorts(@Param("keyword") String keyword, @Param("offset") Integer offset, @Param("size") Integer size)`
*   **描述**: 综合港口代码、中文名、英文名进行模糊查询，返回匹配的港口列表，并支持分页。结果会根据精确匹配、前缀匹配等优先级进行排序。
*   **参数**:
    *   `keyword` (String): 搜索关键词。
    *   `offset` (Integer): 查询起始位置。
    *   `size` (Integer): 返回记录的数量。
*   **返回值**: `List<Port>` - 匹配的港口实体列表。

### 3.2 精确查询与统计方法

#### selectByPortCode - 根据港口代码精确查找
*   **方法签名**: `Port selectByPortCode(@Param("portCode") String portCode)`
*   **描述**: 根据完整的港口代码精确查找一个港口。
*   **参数**:
    *   `portCode` (String): 完整的港口代码。
*   **返回值**: `Port` - 匹配的港口实体，如果不存在则返回 `null`。

#### countAllPorts - 获取港口总数
*   **方法签名**: `Long countAllPorts()`
*   **描述**: 获取 `ports` 表中所有记录的总数。
*   **参数**: 无。
*   **返回值**: `Long` - 港口总数。

#### countSearchPorts - 根据关键词搜索港口总数
*   **方法签名**: `Long countSearchPorts(@Param("keyword") String keyword)`
*   **描述**: 统计根据关键词进行综合搜索（港口代码、中文名、英文名）后匹配到的港口总数。
*   **参数**:
    *   `keyword` (String): 搜索关键词。
*   **返回值**: `Long` - 匹配的港口总数。

## 4. 业务规则

*   **模糊匹配**: 所有搜索方法都使用 `LIKE CONCAT('%', #{keyword}, '%')` 进行模糊匹配，这意味着关键词可以出现在字段的任何位置。
*   **排序优先级**: `searchByPortName` 和 `searchPorts` 中的 `ORDER BY CASE` 语句定义了搜索结果的优先级，通常是精确匹配 > 前缀匹配 > 包含匹配，以提供更相关的搜索结果。
*   **分页逻辑**: `offset` 和 `size` 参数用于实现数据库层面的分页，`offset` 表示跳过的记录数，`size` 表示返回的记录数。

## 5. 使用示例

```java
// 1. 在 PortService 实现中进行综合搜索
@Service
public class PortServiceImpl implements PortService {
    @Autowired
    private PortMapper portMapper;

    @Override
    public List<PortDTO> searchPorts(String keyword, Integer page, Integer size) {
        Integer offset = (page - 1) * size;
        List<Port> ports = portMapper.searchPorts(keyword, offset, size);
        return ports.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public Long getSearchCount(String keyword) {
        return portMapper.countSearchPorts(keyword);
    }
}

// 2. 在 PortController 中调用 Mapper 方法
@RestController
@RequestMapping("/api/v1/ports")
public class PortController {
    @Autowired
    private PortService portService;

    @GetMapping("/search")
    public Result<Map<String, Object>> searchPorts(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        List<PortDTO> ports = portService.searchPorts(keyword, page, size);
        Long totalCount = portService.getSearchCount(keyword);
        
        Map<String, Object> result = new HashMap<>();
        result.put("ports", ports);
        result.put("totalCount", totalCount);
        // ... 其他分页信息 ...
        return Result.success(result);
    }
}

// 3. 测试示例
@SpringBootTest
class PortMapperTest {
    @Autowired
    private PortMapper portMapper;

    @Test
    @Transactional
    void testSearchPorts() {
        // 插入测试数据
        Port p1 = new Port(); p1.setPortCode("CNSHA"); p1.setPortNameCn("上海港"); p1.setPortNameEn("SHANGHAI"); portMapper.insert(p1);
        Port p2 = new Port(); p2.setPortCode("CNGZ"); p2.setPortNameCn("广州港"); p2.setPortNameEn("GUANGZHOU"); portMapper.insert(p2);
        Port p3 = new Port(); p3.setPortCode("USNYC"); p3.setPortNameCn("纽约港"); p3.setPortNameEn("NEW YORK"); portMapper.insert(p3);

        // 搜索 "SH"，期望上海港排在前面
        List<Port> results = portMapper.searchPorts("SH", 0, 10);
        assertThat(results).isNotEmpty();
        assertThat(results.get(0).getPortCode()).isEqualTo("CNSHA");

        // 精确搜索 "CNGZ"
        Port foundPort = portMapper.selectByPortCode("CNGZ");
        assertThat(foundPort).isNotNull();
        assertThat(foundPort.getPortNameCn()).isEqualTo("广州港");
    }

    @Test
    @Transactional
    void testCountAllPorts() {
        // 插入一些测试数据
        portMapper.insert(new Port());
        portMapper.insert(new Port());
        Long count = portMapper.countAllPorts();
        assertThat(count).isGreaterThanOrEqualTo(2L);
    }
}
```

## 6. 注意事项

*   **MyBatis-Plus集成**: 继承 `BaseMapper` 使得该Mapper自动拥有强大的CRUD能力，减少了重复代码。
*   **注解SQL**: 所有自定义方法都使用了 `@Select` 注解直接编写SQL。对于复杂的SQL，也可以考虑使用XML配置。
*   **SQL注入防护**: 使用 `@Param` 注解和MyBatis的参数绑定机制可以有效防止SQL注入攻击。
*   **性能优化**: 港口数据通常是静态且查询频繁的。应确保数据库中对 `port_code`, `port_name_cn`, `port_name_en` 等字段建立了合适的索引。对于高并发查询，可以考虑在服务层添加缓存。
*   **排序逻辑**: `ORDER BY CASE` 语句虽然实现了智能排序，但其复杂性可能会对性能产生一定影响。在数据量非常大时，需要进行性能测试和优化。
*   **分页参数**: `offset` 和 `size` 是数据库层面的分页参数，与MyBatis-Plus的 `Page` 对象结合使用时，需要注意转换关系（`offset = (page - 1) * size`）。
*   **数据一致性**: 确保港口代码的唯一性，并在业务逻辑层进行校验。
*   **可读性**: 自定义查询方法的命名清晰，准确反映了其查询目的。