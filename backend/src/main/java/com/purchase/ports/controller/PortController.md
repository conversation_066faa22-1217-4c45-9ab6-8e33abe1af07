# PortController.md

## 1. 文件概述

`PortController.java` 是港口信息模块的API入口，位于 `com.purchase.ports.controller` 包中。该控制器作为港口服务的前端接口，负责处理所有与港口数据相关的HTTP请求。它通过依赖注入的 `PortService` 来执行具体的业务逻辑，为前端应用（特别是货代和订单模块）提供了全面、高效的港口信息查询功能，包括分页、多维度搜索、批量获取和精确查找。此外，它还特别为前端选择器组件优化了API，提供了专用的分页和快速搜索接口。

## 2. 核心功能

*   **分页查询**: 提供 `/api/v1/ports` 端点，支持对所有港口数据进行标准化的分页查询。
*   **多维度搜索**: 提供了多种搜索方式，包括一个通用的 `/search` 接口（支持代码、中英文名混合搜索），以及针对特定字段的 `/search/code` 和 `/search/name` 接口。
*   **精确查找**: 支持通过港口ID (`/{portId}`) 或唯一的港口代码 (`/code/{portCode}`) 来精确获取单个港口的信息。
*   **批量获取**: 提供了 `/batch` 接口，允许客户端通过一个请求传递一个ID列表来获取多个港口的信息，减少了HTTP请求次数。
*   **统计功能**: 通过 `/stats` 接口提供港口总数等基础统计数据。
*   **选择器组件优化**: 专门为前端UI组件（如港口选择器）设计了 `/selector` 和 `/selector/quick-search` 两个接口，分别用于支持分页加载和实时搜索建议，提升了用户体验。
*   **统一响应格式**: 所有接口都返回一个标准化的 `Result` 对象，其中包含成功或失败的状态以及相应的数据或错误信息。
*   **日志记录**: 对所有接收到的请求都记录了详细的日志，包括请求参数，便于系统监控和调试。

## 3. 接口说明

### 港口查询接口

#### getAllPorts - 分页获取所有港口
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/ports`
*   **参数**:
    *   `page` (Integer, optional, default=1): 页码。
    *   `size` (Integer, optional, default=20): 每页大小。
*   **返回值**: `Result<Map<String, Object>>` - 包含 `ports` 列表和分页信息（`totalCount`, `currentPage`, `pageSize`, `totalPages`）的Map。
*   **业务逻辑**: 调用 `portService.getAllPorts` 获取数据，并从 `portService.getTotalCount` 获取总数，然后组装成分页结果返回。

#### searchPorts - 通用模糊搜索港口
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/ports/search`
*   **参数**:
    *   `keyword` (String, required): 搜索关键词，匹配港口代码、中文名或英文名。
    *   `page` (Integer, optional, default=1): 页码。
    *   `size` (Integer, optional, default=20): 每页大小。
*   **返回值**: `Result<Map<String, Object>>` - 包含 `ports` 列表、关键词和分页信息的Map。
*   **业务逻辑**: 调用 `portService.searchPorts` 和 `portService.getSearchCount`，并组装结果。

#### getPortByCode - 根据港口代码精确查找
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/ports/code/{portCode}`
*   **参数**:
    *   `portCode` (String, path, required): 完整的港口代码。
*   **返回值**: `Result<PortDTO>` - 匹配的港口DTO。如果未找到，返回包含错误信息的 `Result` 对象。
*   **业务逻辑**: 调用 `portService.getPortByCode`，并处理 `null` 结果。

### 选择器专用接口

#### getPortsForSelector - 港口选择器分页接口
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/ports/selector`
*   **参数**:
    *   `page` (Integer, optional, default=1): 页码。
    *   `size` (Integer, optional, default=20): 每页大小。
    *   `keyword` (String, optional): 搜索关键词。
*   **返回值**: `Result<PageResult<PortDTO>>` - 返回一个标准的分页结果对象 `PageResult`，前端组件可直接使用。
*   **业务逻辑**: 直接调用 `portService.getPortsForSelector`，该服务方法已封装了分页和搜索逻辑。

#### quickSearchForSelector - 港口选择器快速搜索接口
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/ports/selector/quick-search`
*   **参数**:
    *   `keyword` (String, required): 搜索关键词。
    *   `limit` (Integer, optional, default=10): 返回结果的最大数量。
*   **返回值**: `Result<List<PortDTO>>` - 匹配的港口DTO列表。
*   **业务逻辑**: 调用 `portService.quickSearchPortsForSelector`，获取有限数量的顶部匹配结果，用于实现搜索建议功能。

## 4. 业务规则

*   **数据一致性**: 控制器本身不处理业务逻辑，所有数据操作都委托给 `PortService`，确保了业务规则的统一应用。
*   **分页逻辑**: 分页参数 `page` 从1开始，符合通常的用户习惯。总页数的计算采用了 `Math.ceil((double) totalCount / size)`，确保了在有余数时页数计算正确。
*   **错误处理**: 对于未找到资源的请求（如按ID或代码查询），控制器会返回一个包含明确错误信息的 `Result` 对象，而不是抛出异常或返回 `404` 状态码，这是一种特定的API设计风格。

## 5. 使用示例

```java
// 1. 前端 (Vue + Element UI Select) 使用选择器接口
/*
<el-select
  v-model="selectedPort"
  filterable
  remote
  :remote-method="remoteMethod"
  :loading="loading"
  placeholder="请输入港口关键词"
>
  <el-option
    v-for="item in options"
    :key="item.id"
    :label="item.portNameCn"
    :value="item.id"
  />
</el-select>
*/

// a. 实时搜索建议 (quick-search)
async function remoteMethod(query) {
  if (query !== '') {
    this.loading = true;
    const { data } = await axios.get('/api/v1/ports/selector/quick-search', {
      params: { keyword: query, limit: 15 }
    });
    this.loading = false;
    this.options = data.data; // 假设axios返回的结构是 { data: { success: true, data: [...] } }
  }
}

// b. 分页加载 (selector)
// 可以在下拉列表滚动到底部时加载下一页
async function loadMorePorts() {
    const params = { page: currentPage++, size: 20, keyword: currentKeyword };
    const { data } = await axios.get('/api/v1/ports/selector', { params });
    this.options.push(...data.data.list);
}

// 2. Java后端服务间调用 (Feign Client)
@FeignClient(name = "purchase-system", path = "/api/v1/ports")
public interface PortFeignClient {
    @GetMapping("/code/{portCode}")
    Result<PortDTO> getPortByCode(@PathVariable("portCode") String portCode);

    @PostMapping("/batch")
    Result<List<PortDTO>> getPortsByIds(@RequestBody List<Long> portIds);
}

// 在订单服务中使用
@Service
public class OrderServiceImpl {
    @Autowired
    private PortFeignClient portFeignClient;

    public void createOrder(OrderRequest request) {
        Result<PortDTO> portResult = portFeignClient.getPortByCode(request.getDestinationPortCode());
        if (!portResult.isSuccess() || portResult.getData() == null) {
            throw new BusinessException("目的地港口无效");
        }
        // ...
    }
}

// 3. 测试示例 (MockMvc)
@SpringBootTest
@AutoConfigureMockMvc
class PortControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PortService portService;

    @Test
    void testGetPortById_Found() throws Exception {
        PortDTO mockPort = new PortDTO(1L, "CNSHA", "上海港", "SHANGHAI");
        when(portService.getPortById(1L)).thenReturn(mockPort);

        mockMvc.perform(get("/api/v1/ports/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.portCode").value("CNSHA"));
    }

    @Test
    void testGetPortById_NotFound() throws Exception {
        when(portService.getPortById(anyLong())).thenReturn(null);

        mockMvc.perform(get("/api/v1/ports/999"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("港口不存在"));
    }
}
```

## 6. 注意事项

*   **性能**: 搜索接口（特别是通用搜索）的性能高度依赖于 `PortService` 中SQL查询的优化。应确保数据库中对 `port_code`, `port_name_cn`, `port_name_en` 等字段建立了合适的索引（如联合索引或全文索引）。
*   **安全**: 虽然是查询接口，但仍需考虑防范潜在的SQL注入风险（通过使用MyBatis等框架的参数化查询来避免），以及防止恶意的高频次请求（可引入API网关进行速率限制）。
*   **数据格式**: 返回给前端的 `Map<String, Object>` 结构虽然灵活，但不如定义一个专门的 `PortPageResponse` DTO类类型安全，后者更利于API文档的维护和客户端代码的生成。
*   **API文档**: 控制器使用了 `@Tag` 和 `@Operation` 等Swagger注解，这对于自动生成和维护API文档非常有帮助，应确保其内容准确、完整。
*   **参数校验**: 应在控制器层或服务层对`page`和`size`等分页参数进行基本的校验，例如，确保它们是正整数。
*   **职责单一**: 控制器很好地遵循了单一职责原则，仅负责请求的接收和响应的组装，所有业务逻辑都委托给了服务层。
*   **可维护性**: 将不同搜索逻辑拆分为独立的API (`/search`, `/search/code`, `/search/name`)，使得每个接口的职责更清晰，易于理解和维护。
*   **用户体验**: 为选择器设计的专用接口是该控制器的一大亮点，它体现了后端开发对前端用户体验的深入思考，是优秀API设计的典范。