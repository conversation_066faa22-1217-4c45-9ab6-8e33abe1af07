# Port.java

## 文件概述 (File Overview)
`Port.java` 是一个持久化实体类，对应数据库中的 `ports` 表。它定义了港口的基本数据模型，包括港口的唯一标识、港口代码以及中英文名称。该实体类是港口管理功能的核心数据载体，为采购系统中的物流、运输和转发业务提供标准化的港口信息支持。

## 核心功能 (Core Functionality)
*   **数据库表映射:** 通过 `@TableName("ports")` 注解明确映射到数据库表。
*   **主键配置:** `@TableId` 注解配置了主键 `id` 及其自动增长策略。
*   **字段映射:** `@TableField` 注解用于将 POJO 属性映射到数据库列名。
*   **多语言支持:** 包含 `portNameCn`（中文名称）和 `portNameEn`（英文名称），支持国际化。
*   **港口标识:** `portCode` 字段用于存储标准化的港口代码，通常遵循国际港口代码标准。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `Long id`: 港口唯一标识 ID。`@TableId(value = "id", type = IdType.AUTO)`，自动增长。
*   `String portCode`: 港口代码。`@TableField("port_code")`，通常使用国际标准港口代码（如 UN/LOCODE）。
*   `String portNameCn`: 港口中文名称。`@TableField("port_name_cn")`，用于中文环境下的港口显示。
*   `String portNameEn`: 港口英文名称。`@TableField("port_name_en")`，用于英文环境下的港口显示和国际业务。

## 使用示例 (Usage Examples)

```java
// 在Mapper接口中使用Port实体
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.purchase.ports.entity.Port;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PortMapper extends BaseMapper<Port> {
    // BaseMapper提供了基本的CRUD操作

    // 自定义查询示例：根据港口代码查询
    @Select("SELECT * FROM ports WHERE port_code = #{portCode}")
    Port findByPortCode(@Param("portCode") String portCode);

    // 自定义查询示例：模糊搜索港口名称
    @Select("SELECT * FROM ports WHERE port_name_cn LIKE CONCAT('%', #{keyword}, '%') " +
            "OR port_name_en LIKE CONCAT('%', #{keyword}, '%')")
    List<Port> searchByName(@Param("keyword") String keyword);
}

// 在Service层操作Port实体
@Service
public class PortServiceImpl implements PortService {
    @Autowired
    private PortMapper portMapper;

    public Port createPort(String portCode, String portNameCn, String portNameEn) {
        Port port = Port.builder()
                .portCode(portCode)
                .portNameCn(portNameCn)
                .portNameEn(portNameEn)
                .build();
        portMapper.insert(port);
        return port;
    }

    public PortDTO getPortByCode(String portCode) {
        Port port = portMapper.findByPortCode(portCode);
        if (port == null) return null;
        
        PortDTO dto = new PortDTO();
        BeanUtils.copyProperties(port, dto);
        return dto;
    }

    public List<Port> searchPorts(String keyword) {
        return portMapper.searchByName(keyword);
    }
}
```

## 注意事项 (Notes)
*   **Lombok 注解:** 该类使用了 Lombok 的 `@Data`, `@Builder`, `@NoArgsConstructor`, `@AllArgsConstructor` 注解，自动生成了 getter、setter、构造函数、`equals`、`hashCode` 和 `toString` 方法，减少了样板代码。
*   **MyBatis-Plus 集成:** 该实体类专门为 MyBatis-Plus 设计，通过其注解实现了与数据库表的映射和自动化功能（如 ID 生成）。
*   **港口代码标准化:** `portCode` 字段应遵循国际港口代码标准（如 UN/LOCODE），确保与国际物流系统的兼容性。格式通常为 2 位国家代码 + 3 位地点代码（如 "CNSHA" 表示中国上海港）。
*   **多语言支持:** 中英文名称字段的存在表明系统支持多语言环境，在设计 API 响应时应根据用户语言偏好返回相应的名称字段。
*   **数据完整性:** 在业务逻辑中应确保港口代码的唯一性，避免重复的港口代码导致数据混乱。
*   **字符编码:** 由于包含中文字段，应确保数据库和应用程序都使用 UTF-8 编码，避免中文字符乱码问题。
*   **索引优化:** 建议在 `port_code` 字段上建立唯一索引，在 `port_name_cn` 和 `port_name_en` 字段上建立普通索引，以提高查询性能。
