# Ports 港口模块文档

## 模块概述

港口模块是采购系统的基础数据模块，负责管理全球港口信息。该模块提供标准化的港口数据服务，支持国际贸易中的港口选择、运输路线规划等功能，为转发业务和物流管理提供准确的港口信息支持。

## 目录结构概览

```
com.purchase.ports/
├── controller/                  # 控制器层
│   └── PortController.java            # 港口控制器
├── dto/                        # 数据传输对象
│   └── PortDTO.java                   # 港口DTO
├── entity/                     # 实体类
│   └── Port.java                      # 港口实体
├── mapper/                     # 数据访问层
│   └── PortMapper.java                # 港口Mapper
├── service/                    # 服务层
│   ├── PortService.java               # 港口服务接口
│   └── impl/                          # 服务实现
```

## 核心功能详述

### 1. 港口信息管理 (Port)

#### 主要功能
- **港口查询**: 支持多条件港口信息查询
- **港口搜索**: 按名称、代码、国家等搜索港口
- **港口分类**: 按地区、类型等分类管理港口
- **数据维护**: 港口信息的增删改查操作

#### 核心实体
- `Port.java`: 港口实体
  - `portCode`: 港口UN/LOCODE代码
  - `portName`: 港口名称
  - `countryCode`: 国家代码
  - `countryName`: 国家名称
  - `regionName`: 地区名称
  - `latitude`: 纬度
  - `longitude`: 经度
  - `portType`: 港口类型
  - `status`: 港口状态

### 2. 港口数据服务 (PortService)

#### 主要功能
- **标准化查询**: 提供标准化的港口查询接口
- **智能搜索**: 支持模糊搜索和智能匹配
- **数据缓存**: 港口数据缓存提高查询性能
- **数据同步**: 与国际港口数据库同步

#### 查询功能
- 按港口代码查询
- 按港口名称搜索
- 按国家/地区筛选
- 按港口类型筛选
- 地理位置范围查询

## 数据模型说明

### Port 港口实体

#### 核心字段
- `id`: 港口唯一标识
- `portCode`: 港口UN/LOCODE代码（如CNSHA、NLRTM）
- `portName`: 港口名称（如Shanghai、Rotterdam）
- `countryCode`: 国家代码（如CN、NL）
- `countryName`: 国家名称（如China、Netherlands）
- `regionName`: 地区名称（如Asia、Europe）
- `latitude`: 纬度坐标
- `longitude`: 经度坐标
- `portType`: 港口类型（海港、河港、内陆港等）
- `status`: 港口状态（活跃、停用等）

#### 标准规范
- **UN/LOCODE**: 联合国贸易和运输地点代码
- **ISO 3166**: 国家代码标准
- **WGS84**: 地理坐标系统标准

## API 接口概览

### 港口查询接口
- `GET /api/v1/ports` - 获取港口列表
- `GET /api/v1/ports/{code}` - 根据代码获取港口详情
- `GET /api/v1/ports/search` - 搜索港口
- `GET /api/v1/ports/by-country/{countryCode}` - 按国家查询港口

### 港口管理接口
- `POST /api/v1/ports` - 创建港口信息
- `PUT /api/v1/ports/{id}` - 更新港口信息
- `DELETE /api/v1/ports/{id}` - 删除港口信息

### 地理查询接口
- `GET /api/v1/ports/nearby` - 查询附近港口
- `GET /api/v1/ports/route` - 查询航线港口

## 使用示例

### 港口查询
```java
// 按代码查询港口
Port shanghaiPort = portService.getByCode("CNSHA");

// 搜索港口
List<Port> ports = portService.searchPorts("Shanghai");

// 按国家查询港口
List<Port> chinaPorts = portService.getPortsByCountry("CN");

// 查询附近港口
List<Port> nearbyPorts = portService.getNearbyPorts(31.2304, 121.4737, 100);
```

### 前端集成
```javascript
// 港口选择器组件
const PortSelector = () => {
  const [ports, setPorts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  
  const searchPorts = async (term) => {
    const response = await fetch(`/api/v1/ports/search?q=${term}`);
    const data = await response.json();
    setPorts(data.data);
  };
  
  return (
    <Select
      showSearch
      placeholder="选择港口"
      onSearch={searchPorts}
      filterOption={false}
    >
      {ports.map(port => (
        <Option key={port.portCode} value={port.portCode}>
          {port.portName} ({port.portCode})
        </Option>
      ))}
    </Select>
  );
};
```

## 业务规则

### 港口代码规则
1. 使用标准UN/LOCODE代码格式
2. 代码格式：2位国家代码 + 3位地点代码
3. 代码唯一性：每个港口有唯一的代码
4. 代码不可变：港口代码一旦分配不可修改

### 数据质量规则
1. 港口名称必须准确且标准化
2. 地理坐标必须精确到小数点后6位
3. 国家代码必须符合ISO 3166标准
4. 港口类型必须从预定义列表中选择

### 数据维护规则
1. 定期与国际港口数据库同步
2. 新增港口需要验证代码唯一性
3. 停用港口保留历史数据
4. 重要港口信息变更需要审核

## 集成说明

### 与其他模块的关系
- **转发模块**: 提供起运港和目的港选择
- **订单模块**: 订单中的港口信息引用
- **竞价模块**: 竞价中的港口信息
- **结算模块**: 结算单中的港口信息

### 外部数据源
- **UN/LOCODE数据库**: 联合国官方港口代码
- **港口管理局**: 各国港口管理部门数据
- **航运公司**: 航运公司港口信息
- **物流平台**: 第三方物流平台数据

## 注意事项

### 开发注意事项
1. **数据标准**: 严格遵循国际标准
2. **编码格式**: 统一使用UTF-8编码
3. **大小写**: 港口代码统一大写
4. **缓存策略**: 港口数据适合长期缓存

### 性能优化
1. **索引优化**: 为查询字段建立索引
2. **缓存策略**: 使用Redis缓存热点数据
3. **分页查询**: 大量数据查询使用分页
4. **搜索优化**: 使用全文搜索引擎

### 数据质量
1. **数据验证**: 严格的数据格式验证
2. **重复检查**: 防止重复港口数据
3. **数据清洗**: 定期清理无效数据
4. **数据监控**: 监控数据质量指标

### 国际化支持
1. **多语言**: 支持港口名称多语言
2. **时区处理**: 港口时区信息管理
3. **货币支持**: 港口相关费用货币
4. **法规遵循**: 遵循各国港口法规

## 扩展功能

### 未来规划
1. **实时信息**: 港口实时状态信息
2. **费用管理**: 港口相关费用信息
3. **天气集成**: 港口天气信息集成
4. **航线优化**: 基于港口的航线优化

### 数据增强
1. **港口设施**: 详细的港口设施信息
2. **服务能力**: 港口服务能力数据
3. **历史数据**: 港口历史统计数据
4. **评价系统**: 港口服务评价系统
