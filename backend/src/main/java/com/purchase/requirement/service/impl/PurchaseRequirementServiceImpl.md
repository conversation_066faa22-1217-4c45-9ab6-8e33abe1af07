# PurchaseRequirementServiceImpl.java

## 文件概述 (File Overview)
`PurchaseRequirementServiceImpl.java` 是采购需求业务服务的实现类，位于 `com.purchase.requirement.service.impl` 包中。该类实现了 `PurchaseRequirementService` 接口，提供了采购需求的完整业务逻辑处理，包括需求的创建、查询、更新、删除、状态管理等核心功能。通过集成多个相关服务（分类服务、服务等级服务、联系人服务、通知服务等），实现了采购需求的全生命周期管理。该服务类包含19个主要方法，总计1549行代码，是采购系统的核心业务组件。

## 核心功能 (Core Functionality)
*   **需求管理**: 提供采购需求的完整CRUD操作和生命周期管理
*   **搜索查询**: 支持多条件搜索、分页查询、按买家查询等多种查询方式
*   **状态管理**: 管理需求的状态流转（草稿、发布、进行中、已完成、已取消等）
*   **统计分析**: 提供需求统计数据和分析报告
*   **权限控制**: 基于用户角色和所有权的权限验证
*   **通知集成**: 集成通知服务，及时通知相关用户
*   **数据转换**: 实体对象与DTO、VO之间的完整转换
*   **业务验证**: 完善的业务规则验证和数据校验

## 接口说明 (Interface Description)

### 统计查询方法

#### getRequirementStats - 获取需求统计数据
*   **方法签名**: `RequirementStatsDTO getRequirementStats()`
*   **返回值**: `RequirementStatsDTO` - 需求统计数据对象
*   **业务逻辑**: 
    *   统计总需求数量
    *   统计各状态需求数量
    *   统计各分类需求数量
    *   计算统计时间范围
    *   返回完整的统计数据

#### getRequirementStatsByBuyerId - 获取买家需求统计
*   **方法签名**: `RequirementStatsDTO getRequirementStatsByBuyerId(Long buyerId)`
*   **参数**: `buyerId` (Long) - 买家ID
*   **返回值**: `RequirementStatsDTO` - 特定买家的需求统计数据
*   **业务逻辑**: 
    *   验证买家ID有效性
    *   统计该买家的需求数据
    *   按状态和分类进行统计
    *   返回个人统计报告

#### getStatistics - 获取系统统计数据
*   **方法签名**: `PurchaseRequirementStatistics getStatistics()`
*   **返回值**: `PurchaseRequirementStatistics` - 系统级统计数据
*   **业务逻辑**: 
    *   统计系统总体需求数据
    *   计算各种业务指标
    *   提供管理员决策支持数据

### 需求管理方法

#### createRequirement - 创建采购需求
*   **方法签名**: `PurchaseRequirementDTO.CreateResponse createRequirement(PurchaseRequirementDTO.CreateRequest request)`
*   **事务**: `@Transactional`
*   **参数**: `request` (PurchaseRequirementDTO.CreateRequest) - 创建请求对象
*   **返回值**: `PurchaseRequirementDTO.CreateResponse` - 创建响应对象
*   **业务逻辑**: 
    *   验证必填字段和业务规则
    *   处理联系人信息
    *   设置需求状态和时间戳
    *   保存到数据库
    *   发送创建成功通知
    *   返回创建结果

#### updateRequirement - 更新采购需求
*   **方法签名**: `PurchaseRequirementDTO.CreateResponse updateRequirement(Long id, PurchaseRequirementDTO.CreateRequest request)`
*   **事务**: `@Transactional`
*   **参数**: 
    *   `id` (Long) - 需求ID
    *   `request` (PurchaseRequirementDTO.CreateRequest) - 更新请求对象
*   **返回值**: `PurchaseRequirementDTO.CreateResponse` - 更新响应对象
*   **业务逻辑**: 
    *   验证需求存在性和权限
    *   检查需求状态是否允许更新
    *   更新需求信息
    *   记录更新日志
    *   发送更新通知

#### deleteRequirement - 删除采购需求
*   **方法签名**: `void deleteRequirement(Long id, Long buyerId)`
*   **事务**: `@Transactional`
*   **参数**: 
    *   `id` (Long) - 需求ID
    *   `buyerId` (Long) - 买家ID（用于权限验证）
*   **业务逻辑**: 
    *   验证需求存在性和所有权
    *   检查需求状态是否允许删除
    *   执行软删除操作
    *   清理相关数据
    *   记录删除日志

#### cancelRequirement - 取消采购需求
*   **方法签名**: `void cancelRequirement(Long id, Long userId)`
*   **事务**: `@Transactional`
*   **参数**: 
    *   `id` (Long) - 需求ID
    *   `userId` (Long) - 操作用户ID
*   **业务逻辑**: 
    *   验证需求存在性和权限
    *   检查需求状态是否允许取消
    *   更新需求状态为已取消
    *   发送取消通知
    *   记录操作日志

### 查询方法

#### searchRequirements - 搜索采购需求
*   **方法签名**: `PurchaseRequirementDTO.SearchResponse searchRequirements(PurchaseRequirementDTO.SearchRequest request)`
*   **参数**: `request` (PurchaseRequirementDTO.SearchRequest) - 搜索请求对象
*   **返回值**: `PurchaseRequirementDTO.SearchResponse` - 搜索结果对象
*   **业务逻辑**: 
    *   构建动态查询条件
    *   执行分页查询
    *   转换为响应DTO
    *   返回搜索结果

#### getRequirementsByBuyerId - 获取买家的需求列表
*   **方法签名**: `PurchaseRequirementDTO.SearchResponse getRequirementsByBuyerId(Long buyerId, PurchaseRequirementDTO.SearchRequest request)`
*   **参数**: 
    *   `buyerId` (Long) - 买家ID
    *   `request` (PurchaseRequirementDTO.SearchRequest) - 搜索条件
*   **返回值**: `PurchaseRequirementDTO.SearchResponse` - 搜索结果
*   **业务逻辑**: 
    *   添加买家ID过滤条件
    *   执行搜索查询
    *   返回该买家的需求列表

#### getRequirementPage - 分页获取需求列表
*   **方法签名**: `Page<PurchaseRequirement> getRequirementPage(Integer page, Integer size, Long categoryId)`
*   **参数**: 
    *   `page` (Integer) - 页码
    *   `size` (Integer) - 每页大小
    *   `categoryId` (Long) - 分类ID，可选
*   **返回值**: `Page<PurchaseRequirement>` - 分页结果
*   **业务逻辑**: 
    *   构建分页查询条件
    *   执行分页查询
    *   返回分页结果

#### getRequirementById - 根据ID获取需求详情
*   **方法签名**: `RequirementVO getRequirementById(Long id)`
*   **参数**: `id` (Long) - 需求ID
*   **返回值**: `RequirementVO` - 需求详情视图对象
*   **业务逻辑**: 
    *   查询需求基本信息
    *   关联查询相关数据
    *   转换为视图对象
    *   返回完整的需求详情

#### getRequirementsByIds - 批量获取需求信息
*   **方法签名**: `List<RequirementVO> getRequirementsByIds(List<Long> ids)`
*   **参数**: `ids` (List<Long>) - 需求ID列表
*   **返回值**: `List<RequirementVO>` - 需求视图对象列表
*   **业务逻辑**: 
    *   批量查询需求信息
    *   转换为视图对象
    *   返回需求列表

#### getFeaturedRequirements - 获取推荐需求
*   **方法签名**: `List<PurchaseRequirement> getFeaturedRequirements(Integer limit)`
*   **参数**: `limit` (Integer) - 返回数量限制
*   **返回值**: `List<PurchaseRequirement>` - 推荐需求列表
*   **业务逻辑**: 
    *   查询符合推荐条件的需求
    *   按推荐算法排序
    *   返回指定数量的推荐需求

#### getLatestRequirements - 获取最新需求
*   **方法签名**: `List<PurchaseRequirementDTO.LatestRequirementResponse> getLatestRequirements(Integer limit)`
*   **参数**: `limit` (Integer) - 返回数量限制
*   **返回值**: `List<PurchaseRequirementDTO.LatestRequirementResponse>` - 最新需求列表
*   **业务逻辑**: 
    *   按创建时间倒序查询
    *   转换为响应DTO
    *   返回最新需求列表

#### getLatestRequirementsByBuyerId - 获取买家最新需求
*   **方法签名**: `List<PurchaseRequirementDTO.LatestRequirementResponse> getLatestRequirementsByBuyerId(Long buyerId, Integer limit)`
*   **参数**: 
    *   `buyerId` (Long) - 买家ID
    *   `limit` (Integer) - 返回数量限制
*   **返回值**: `List<PurchaseRequirementDTO.LatestRequirementResponse>` - 买家最新需求列表
*   **业务逻辑**: 
    *   查询指定买家的最新需求
    *   按时间排序
    *   返回需求列表

### 管理员方法

#### deleteRequirementByAdmin - 管理员删除需求
*   **方法签名**: `void deleteRequirementByAdmin(Long id)`
*   **事务**: `@Transactional`
*   **参数**: `id` (Long) - 需求ID
*   **业务逻辑**: 
    *   验证需求存在性
    *   执行管理员删除操作
    *   记录管理员操作日志
    *   发送删除通知

#### updateRequirementStatusByAdmin - 管理员更新需求状态
*   **方法签名**: `PurchaseRequirementDTO.CreateResponse updateRequirementStatusByAdmin(Long id, String status)`
*   **参数**: 
    *   `id` (Long) - 需求ID
    *   `status` (String) - 新状态
*   **返回值**: `PurchaseRequirementDTO.CreateResponse` - 更新结果
*   **业务逻辑**: 
    *   验证需求存在性和状态有效性
    *   更新需求状态
    *   记录管理员操作
    *   发送状态变更通知

#### updateRequirementStatus - 更新需求状态
*   **方法签名**: `void updateRequirementStatus(Long id, String status)`
*   **事务**: `@Transactional`
*   **参数**: 
    *   `id` (Long) - 需求ID
    *   `status` (String) - 新状态
*   **业务逻辑**: 
    *   验证状态转换的合法性
    *   更新需求状态
    *   触发状态变更事件
    *   记录状态变更日志

## 使用示例 (Usage Examples)

```java
// 1. 控制器层调用示例
@RestController
@RequestMapping("/api/v1/purchase-requirements")
public class PurchaseRequirementController {
    
    @Autowired
    private PurchaseRequirementServiceImpl requirementService;
    
    // 创建采购需求
    @PostMapping
    public Result<PurchaseRequirementDTO.CreateResponse> createRequirement(
            @RequestBody PurchaseRequirementDTO.CreateRequest request) {
        
        try {
            PurchaseRequirementDTO.CreateResponse response = 
                requirementService.createRequirement(request);
            return Result.success("需求创建成功", response);
        } catch (Exception e) {
            log.error("创建采购需求失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }
    
    // 搜索采购需求
    @PostMapping("/search")
    public Result<PurchaseRequirementDTO.SearchResponse> searchRequirements(
            @RequestBody PurchaseRequirementDTO.SearchRequest request) {
        
        PurchaseRequirementDTO.SearchResponse response = 
            requirementService.searchRequirements(request);
        return Result.success(response);
    }
    
    // 获取需求详情
    @GetMapping("/{id}")
    public Result<RequirementVO> getRequirementDetail(@PathVariable Long id) {
        RequirementVO requirement = requirementService.getRequirementById(id);
        if (requirement == null) {
            return Result.error("需求不存在");
        }
        return Result.success(requirement);
    }
    
    // 更新需求状态
    @PutMapping("/{id}/status")
    public Result<String> updateRequirementStatus(
            @PathVariable Long id,
            @RequestParam String status) {
        
        try {
            requirementService.updateRequirementStatus(id, status);
            return Result.success("状态更新成功");
        } catch (Exception e) {
            log.error("更新需求状态失败: id={}, status={}", id, status, e);
            return Result.error("状态更新失败: " + e.getMessage());
        }
    }
    
    // 获取统计数据
    @GetMapping("/statistics")
    public Result<RequirementStatsDTO> getStatistics() {
        RequirementStatsDTO stats = requirementService.getRequirementStats();
        return Result.success(stats);
    }
}

// 2. 前端JavaScript调用示例
const PurchaseRequirementAPI = {
    // 创建采购需求
    async createRequirement(requirementData) {
        const response = await fetch('/api/v1/purchase-requirements', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify(requirementData)
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 搜索采购需求
    async searchRequirements(searchParams) {
        const response = await fetch('/api/v1/purchase-requirements/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify(searchParams)
        });

        return await response.json();
    },

    // 获取需求详情
    async getRequirementDetail(id) {
        const response = await fetch(`/api/v1/purchase-requirements/${id}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        return await response.json();
    },

    // 渲染需求列表
    renderRequirementList(requirements, containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        requirements.forEach(requirement => {
            const requirementDiv = document.createElement('div');
            requirementDiv.className = 'requirement-item';

            requirementDiv.innerHTML = `
                <div class="requirement-header">
                    <h3>${requirement.title}</h3>
                    <span class="status ${requirement.status.toLowerCase()}">${requirement.statusText}</span>
                </div>
                <div class="requirement-content">
                    <p class="description">${requirement.description}</p>
                    <div class="requirement-meta">
                        <span class="category">${requirement.categoryName}</span>
                        <span class="budget">预算: ¥${requirement.budget?.toLocaleString() || '面议'}</span>
                        <span class="deadline">截止: ${new Date(requirement.deadline).toLocaleDateString()}</span>
                    </div>
                </div>
                <div class="requirement-actions">
                    <button onclick="viewRequirement(${requirement.id})">查看详情</button>
                    <button onclick="contactBuyer(${requirement.buyerId})">联系买家</button>
                </div>
            `;

            container.appendChild(requirementDiv);
        });
    }
};

// 3. 业务流程集成示例
@Service
public class RequirementWorkflowService {

    @Autowired
    private PurchaseRequirementServiceImpl requirementService;

    @Autowired
    private BiddingService biddingService;

    @Autowired
    private OrderService orderService;

    // 需求发布流程
    @Transactional
    public RequirementPublishResult publishRequirement(Long requirementId, Long buyerId) {
        RequirementPublishResult result = new RequirementPublishResult();

        try {
            // 1. 验证需求状态
            RequirementVO requirement = requirementService.getRequirementById(requirementId);
            if (requirement == null) {
                throw new BusinessException("需求不存在");
            }

            if (!"DRAFT".equals(requirement.getStatus())) {
                throw new BusinessException("只有草稿状态的需求才能发布");
            }

            // 2. 更新需求状态为已发布
            requirementService.updateRequirementStatus(requirementId, "PUBLISHED");

            // 3. 创建竞标记录
            biddingService.createBiddingOpportunity(requirementId);

            // 4. 发送发布通知
            sendPublishNotification(requirement);

            result.setSuccess(true);
            result.setRequirementId(requirementId);
            result.setMessage("需求发布成功");

            log.info("需求发布成功: requirementId={}, buyerId={}", requirementId, buyerId);

        } catch (Exception e) {
            log.error("需求发布失败: requirementId={}, buyerId={}", requirementId, buyerId, e);
            result.setSuccess(false);
            result.setMessage("发布失败: " + e.getMessage());
        }

        return result;
    }

    // 需求完成流程
    @Transactional
    public RequirementCompletionResult completeRequirement(Long requirementId, Long selectedBidderId) {
        RequirementCompletionResult result = new RequirementCompletionResult();

        try {
            // 1. 验证需求状态
            RequirementVO requirement = requirementService.getRequirementById(requirementId);
            if (!"IN_PROGRESS".equals(requirement.getStatus())) {
                throw new BusinessException("只有进行中的需求才能完成");
            }

            // 2. 创建订单
            Order order = orderService.createOrderFromRequirement(requirementId, selectedBidderId);

            // 3. 更新需求状态
            requirementService.updateRequirementStatus(requirementId, "COMPLETED");

            // 4. 关闭其他竞标
            biddingService.closeBiddingForRequirement(requirementId, selectedBidderId);

            // 5. 发送完成通知
            sendCompletionNotification(requirement, order);

            result.setSuccess(true);
            result.setOrderId(order.getId());
            result.setMessage("需求完成，订单已创建");

        } catch (Exception e) {
            log.error("需求完成流程失败: requirementId={}", requirementId, e);
            result.setSuccess(false);
            result.setMessage("完成失败: " + e.getMessage());
        }

        return result;
    }
}

// 4. 定时任务示例
@Component
public class RequirementScheduledTasks {

    @Autowired
    private PurchaseRequirementServiceImpl requirementService;

    // 自动过期需求
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void expireOverdueRequirements() {
        log.info("开始检查过期需求");

        try {
            // 查找已过期但状态仍为发布中的需求
            PurchaseRequirementDTO.SearchRequest searchRequest = new PurchaseRequirementDTO.SearchRequest();
            searchRequest.setStatus("PUBLISHED");
            searchRequest.setDeadlineBefore(LocalDateTime.now());

            PurchaseRequirementDTO.SearchResponse searchResponse =
                requirementService.searchRequirements(searchRequest);

            int expiredCount = 0;
            for (RequirementVO requirement : searchResponse.getRequirements()) {
                try {
                    requirementService.updateRequirementStatus(requirement.getId(), "EXPIRED");
                    expiredCount++;
                    log.info("需求已过期: id={}, title={}", requirement.getId(), requirement.getTitle());
                } catch (Exception e) {
                    log.error("更新需求过期状态失败: id={}", requirement.getId(), e);
                }
            }

            log.info("过期需求处理完成，处理数量: {}", expiredCount);

        } catch (Exception e) {
            log.error("过期需求检查任务失败", e);
        }
    }

    // 生成需求统计报告
    @Scheduled(cron = "0 0 8 * * MON") // 每周一早上8点
    public void generateWeeklyReport() {
        log.info("开始生成周度需求统计报告");

        try {
            RequirementStatsDTO stats = requirementService.getRequirementStats();

            WeeklyRequirementReport report = new WeeklyRequirementReport();
            report.setReportPeriod("最近一周");
            report.setTotalRequirements(stats.getTotalCount());
            report.setNewRequirements(stats.getNewCount());
            report.setCompletedRequirements(stats.getCompletedCount());
            report.setActiveRequirements(stats.getActiveCount());
            report.setGeneratedAt(LocalDateTime.now());

            // 发送报告给管理员
            sendWeeklyReport(report);

            log.info("周度需求统计报告生成完成");

        } catch (Exception e) {
            log.error("生成周度需求统计报告失败", e);
        }
    }
}

// 5. 数据分析示例
@Service
public class RequirementAnalyticsService {

    @Autowired
    private PurchaseRequirementServiceImpl requirementService;

    // 分析需求趋势
    public RequirementTrendAnalysis analyzeRequirementTrends(int months) {
        RequirementTrendAnalysis analysis = new RequirementTrendAnalysis();

        // 获取最近几个月的数据
        List<MonthlyRequirementData> monthlyData = new ArrayList<>();

        for (int i = 0; i < months; i++) {
            LocalDateTime startOfMonth = LocalDateTime.now().minusMonths(i).withDayOfMonth(1);
            LocalDateTime endOfMonth = startOfMonth.plusMonths(1).minusDays(1);

            PurchaseRequirementDTO.SearchRequest searchRequest = new PurchaseRequirementDTO.SearchRequest();
            searchRequest.setCreatedAfter(startOfMonth);
            searchRequest.setCreatedBefore(endOfMonth);

            PurchaseRequirementDTO.SearchResponse response =
                requirementService.searchRequirements(searchRequest);

            MonthlyRequirementData data = new MonthlyRequirementData();
            data.setMonth(startOfMonth.getMonth().toString());
            data.setYear(startOfMonth.getYear());
            data.setTotalCount(response.getTotal());
            data.setCompletedCount(calculateCompletedCount(response.getRequirements()));
            data.setAverageBudget(calculateAverageBudget(response.getRequirements()));

            monthlyData.add(data);
        }

        analysis.setMonthlyData(monthlyData);
        analysis.setTrendDirection(calculateTrendDirection(monthlyData));
        analysis.setGrowthRate(calculateGrowthRate(monthlyData));

        return analysis;
    }

    // 分析热门分类
    public List<CategoryPopularity> analyzePopularCategories() {
        RequirementStatsDTO stats = requirementService.getRequirementStats();

        return stats.getCategoryStats().entrySet().stream()
            .map(entry -> new CategoryPopularity(
                entry.getKey(),
                entry.getValue(),
                calculateCategoryGrowth(entry.getKey())
            ))
            .sorted((c1, c2) -> c2.getCount().compareTo(c1.getCount()))
            .collect(Collectors.toList());
    }

    // 预测需求量
    public RequirementForecast forecastRequirements(int futureDays) {
        // 基于历史数据进行简单的线性预测
        RequirementTrendAnalysis trends = analyzeRequirementTrends(6);

        double averageDailyGrowth = calculateAverageDailyGrowth(trends);
        int currentTotal = requirementService.getRequirementStats().getTotalCount();

        RequirementForecast forecast = new RequirementForecast();
        forecast.setForecastDays(futureDays);
        forecast.setCurrentTotal(currentTotal);
        forecast.setPredictedTotal((int) (currentTotal + averageDailyGrowth * futureDays));
        forecast.setConfidenceLevel(calculateConfidenceLevel(trends));

        return forecast;
    }
}
```

## 注意事项 (Notes)
*   **事务管理**: 涉及数据修改的方法都使用@Transactional注解，确保数据一致性
*   **权限验证**: 需求的增删改操作需要验证用户权限，确保只有所有者或管理员可以操作
*   **状态管理**: 需求状态的转换需要遵循业务规则，不能随意跳转状态
*   **数据验证**: 创建和更新需求时需要进行完整的数据验证，包括必填字段和格式验证
*   **软删除**: 使用软删除机制保护重要的需求数据，避免误删除
*   **通知集成**: 重要的业务操作需要及时通知相关用户，提升用户体验
*   **搜索性能**: 复杂的搜索查询需要合理的索引设计和查询优化
*   **分页处理**: 大数据量查询时必须使用分页，避免内存溢出
*   **异常处理**: 完善的异常处理机制，区分业务异常和系统异常
*   **日志记录**: 重要操作都需要详细的日志记录，便于问题排查和审计
*   **数据转换**: 实体对象和DTO之间的转换需要处理所有字段映射
*   **并发控制**: 多用户同时操作同一需求时需要考虑并发控制
*   **缓存策略**: 频繁查询的数据可以考虑使用缓存提高性能
*   **业务规则**: 需求的业务规则可能随时间变化，需要保持代码的灵活性
*   **数据一致性**: 需求相关的数据变更需要保持一致性，避免数据不一致问题
*   **性能监控**: 复杂查询和批量操作需要监控性能，及时发现性能瓶颈
