# RequirementContactService.md

## 1. 文件概述

`RequirementContactService.java` 是需求模块中一个负责处理敏感信息访问控制的服务接口，位于 `com.purchase.requirement.service` 包中。它的核心职责是根据当前用户的身份和其与特定采购需求的关系，来决定是否以及如何展示该需求的联系信息（如联系人姓名、电话、地址等）。该服务通过封装复杂的权限校验逻辑，实现了联系信息的安全展示，有效防止了敏感信息的泄露，是平台保护用户隐私和商业机密的关键一环。

## 2. 核心功能

*   **权限校验**: `checkSellerPermission` 方法提供了核心的权限检查逻辑，它通过判断一个卖家是否在某个采购需求中中标，来决定该卖家是否有权查看该需求的完整联系方式。这是平台“中标后可见”业务规则的直接体现。
*   **信息脱敏与动态展示**: `getContactInfo` 方法是其主要功能入口。它根据调用者传入的用户角色（买家、卖家、管理员）和权限校验结果，动态地返回完整或经过脱敏处理的联系信息。例如，未中标的卖家只能看到 “王**” 和 “138****1234”，而中标的卖家或需求发布者自己则能看到完整信息。
*   **角色支持**: 接口设计考虑了多种用户角色，包括需求的发布者（买家）、参与者（卖家）和系统管理者（管理员），并为他们提供了不同的信息访问权限。
*   **单一职责**: 该服务高度聚焦于“需求联系信息”这一单一业务点，将敏感信息的访问控制逻辑从主需求服务 (`RequirementService`) 中剥离出来，使得主服务更关注业务流程，而此服务更关注安全和权限。

## 3. 接口说明

### 联系信息权限接口

#### checkSellerPermission - 检查卖家是否有权限查看联系信息
*   **方法签名**: `boolean checkSellerPermission(Long requirementId, Long sellerId)`
*   **参数**:
    *   `requirementId` (Long): 需要被访问联系信息的需求ID。
    *   `sellerId` (Long): 尝试访问信息的卖家用户ID。
*   **返回值**: `boolean` - 如果该卖家有权查看，返回 `true`；否则返回 `false`。
*   **业务逻辑**: 服务的实现需要查询与 `requirementId` 相关的竞价或订单记录，检查 `sellerId` 是否是这些记录中的中标者或最终供应商。如果存在中标记录，则认为其有权限。

#### getContactInfo - 根据权限获取需求联系信息
*   **方法签名**: `String getContactInfo(Long requirementId, Long userId, boolean isBuyer)`
*   **参数**:
    *   `requirementId` (Long): 需求ID。
    *   `userId` (Long): 当前请求用户的ID。
    *   `isBuyer` (boolean): 标记当前用户是否为该需求的发布者（买家）。
*   **返回值**: `String` - 包含联系信息的字符串。可能是完整的JSON字符串，也可能是脱敏后的字符串。
*   **业务逻辑**: 实现逻辑如下：
    1.  首先，从数据库获取指定 `requirementId` 的原始联系信息。
    2.  如果 `isBuyer` 为 `true`，直接返回完整信息（因为买家总能看自己的信息）。
    3.  如果 `isBuyer` 为 `false`，则调用 `checkSellerPermission(requirementId, userId)` 进行权限检查。
    4.  如果权限检查通过，返回完整信息。
    5.  如果权限检查不通过，对联系信息进行脱敏处理（例如，将部分数字替换为 `*`）后返回。

#### getContactInfo (重载) - 支持管理员角色的版本
*   **方法签名**: `String getContactInfo(Long requirementId, Long userId, boolean isBuyer, boolean isAdmin)`
*   **参数**:
    *   `isAdmin` (boolean): 标记当前用户是否为管理员。
*   **返回值**: `String` - 联系信息字符串。
*   **业务逻辑**: 在前一个方法逻辑的基础上，增加一条规则：如果 `isAdmin` 为 `true`，则无论其他条件如何，都直接返回完整信息。

## 4. 业务规则

*   **买家权限**: 需求的发布者（买家）拥有对自己发布的需求的全部信息的完全访问权限。
*   **卖家权限**: 卖家只有在成功中标（或被选为供应商）之后，才能获得查看买家完整联系信息的权限。在中标前，信息必须被脱敏。
*   **管理员权限**: 管理员（Admin）拥有最高权限，可以查看平台内所有需求的完整联系信息，通常用于客户支持或争议处理。
*   **信息脱敏规则**: 脱敏规则需要明确定义。例如：
    *   姓名：保留姓氏，隐藏名字，如“张三” -> “张**”。
    *   手机号：保留前三位和后四位，中间用 `*` 代替，如“13812345678” -> “138****5678”。
    *   地址：只显示到城市，隐藏详细街道地址。

## 5. 使用示例

```java
// 1. 服务实现类示例
@Service
public class RequirementContactServiceImpl implements RequirementContactService {
    @Autowired
    private RequirementRepository requirementRepository;
    @Autowired
    private BiddingRepository biddingRepository; // 假设中标记录在bidding表中

    @Override
    public boolean checkSellerPermission(Long requirementId, Long sellerId) {
        // 查询该需求是否有该卖家的中标记录
        return biddingRepository.existsByRequirementIdAndSellerIdAndStatus(requirementId, sellerId, "WON");
    }

    @Override
    public String getContactInfo(Long requirementId, Long userId, boolean isBuyer, boolean isAdmin) {
        Requirement requirement = requirementRepository.findById(requirementId).orElse(null);
        if (requirement == null) return "需求不存在";

        String rawContactInfo = requirement.getContactJson(); // 假设联系信息存为JSON字符串

        // 管理员或买家自己，直接返回完整信息
        if (isAdmin || isBuyer) {
            return rawContactInfo;
        }

        // 检查卖家权限
        if (checkSellerPermission(requirementId, userId)) {
            return rawContactInfo;
        }

        // 无权限，返回脱敏信息
        return desensitizeContactInfo(rawContactInfo);
    }
    
    private String desensitizeContactInfo(String rawJson) {
        // ... 实现JSON解析和字段脱敏的逻辑 ...
        return "脱敏后的信息";
    }
    // ...
}

// 2. 在需求详情Controller中调用
@RestController
@RequestMapping("/api/v1/requirements/{id}")
public class RequirementController {
    @Autowired
    private RequirementService requirementService;
    @Autowired
    private RequirementContactService contactService;

    @GetMapping
    public Result<RequirementDetailVO> getRequirementDetail(@PathVariable Long id) {
        // ... 获取当前用户信息 ...
        Long currentUserId = // ...
        String currentUserRole = // ...

        RequirementDetailVO vo = requirementService.getDetailById(id);
        if (vo == null) return Result.error("Not Found");

        // 调用服务获取可能被脱敏的联系信息
        String contactInfo = contactService.getContactInfo(
            id, 
            currentUserId, 
            vo.getCreatorId().equals(currentUserId), // isBuyer
            "admin".equals(currentUserRole) // isAdmin
        );
        
        vo.setContactInfo(contactInfo);
        return Result.success(vo);
    }
}

// 3. 测试示例
@SpringBootTest
class RequirementContactServiceTest {
    @Autowired
    private RequirementContactService contactService;
    @MockBean
    private RequirementRepository requirementRepository;
    @MockBean
    private BiddingRepository biddingRepository;

    @Test
    void testGetContactInfo_ForWinningSeller() {
        // 准备数据
        Requirement req = new Requirement();
        req.setContactJson("{\"name\":\"张三\",\"phone\":\"13812345678\"}");
        when(requirementRepository.findById(1L)).thenReturn(Optional.of(req));
        when(biddingRepository.existsByRequirementIdAndSellerIdAndStatus(1L, 100L, "WON")).thenReturn(true);

        // 调用服务
        String info = contactService.getContactInfo(1L, 100L, false, false);

        // 断言返回的是完整信息
        assertThat(info).contains("张三");
    }

    @Test
    void testGetContactInfo_ForLosingSeller() {
        // 准备数据
        Requirement req = new Requirement();
        req.setContactJson("{\"name\":\"张三\",\"phone\":\"13812345678\"}");
        when(requirementRepository.findById(1L)).thenReturn(Optional.of(req));
        // 卖家未中标
        when(biddingRepository.existsByRequirementIdAndSellerIdAndStatus(1L, 101L, "WON")).thenReturn(false);

        // 调用服务
        String info = contactService.getContactInfo(1L, 101L, false, false);

        // 断言返回的是脱敏信息 (具体脱敏逻辑需要mock)
        // 假设脱敏服务会将"张三"变为"张**"
        assertThat(info).doesNotContain("张三");
        assertThat(info).contains("张**");
    }
}
```

## 6. 注意事项

*   **安全性是首要目标**: 该服务的核心价值在于安全。在实现时，必须确保权限检查逻辑严密无误，防止任何绕过检查获取到完整信息的可能性。
*   **信息存储**: 联系信息可能以JSON字符串或多个独立字段的形式存储在 `Requirement` 实体中。服务的实现需要能正确地解析和处理这些数据。
*   **脱敏工具**: 脱敏逻辑 (`desensitizeContactInfo`) 应该被封装成一个可重用的工具类，而不是在服务实现中硬编码。可以使用现有的库（如Hutool）或自定义实现。
*   **性能**: `checkSellerPermission` 方法会执行数据库查询。对于高频访问的需求详情页，可以考虑对中标结果进行缓存，以减少数据库压力。
*   **接口设计**: 重载的 `getContactInfo` 方法是处理多种角色权限的一种方式。另一种设计是传入一个包含用户所有信息的 `UserContext` 对象，让服务内部自己判断角色，这样可以使接口更简洁。
*   **可测试性**: 服务的逻辑依赖于用户的角色和与需求的关系，因此在单元测试中需要模拟这些不同的场景（买家、中标卖家、未中标卖家、管理员），确保每种情况下的返回都符合预期。