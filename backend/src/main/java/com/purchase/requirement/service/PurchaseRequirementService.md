## 类用途
采购需求核心服务接口，管理采购需求全生命周期

## 核心方法
### String createRequirement(PurchaseRequirementDTO request)
- 参数：需求创建请求DTO
- 返回：需求ID
- 逻辑：
  1. 验证基础信息
  2. 生成需求编号（PR-YYYYMMDD-XXXX）
  3. 保存需求记录
  4. 初始化工作流

### void updateRequirement(String id, PurchaseRequirementDTO request)
- 参数：需求ID/更新内容
- 逻辑：
  1. 校验需求状态是否可编辑
  2. 记录变更历史
  3. 更新需求信息
  4. 触发变更事件

### PurchaseRequirementDetail getRequirementDetail(String id)
- 参数：需求ID
- 返回：需求详情（聚合基础信息+变更记录）
- 逻辑：
  1. 查询需求主体
  2. 加载关联分类/服务级别
  3. 获取操作日志

## 状态管理
```mermaid
stateDiagram
    [*] --> DRAFT
    DRAFT --> PENDING_APPROVAL
    PENDING_APPROVAL --> APPROVED
    APPROVED --> PROCESSING
    PROCESSING --> COMPLETED
    any --> CANCELLED
```

## 业务规则
- 需求编号需唯一且不可修改
- 重要字段变更需审批
- 状态变更需记录操作人
- 历史版本需保留至少1年

## 使用示例
```java
// 创建需求示例
PurchaseRequirementDTO request = new PurchaseRequirementDTO(
    "办公设备采购",
    "cat123",
    new BigDecimal("10000"),
    "2025-12-31"
);
String reqId = purchaseRequirementService.createRequirement(request);
```

## 注意事项
- 生产环境建议添加防重复提交控制
- 批量操作需考虑性能优化
- 敏感操作需权限验证