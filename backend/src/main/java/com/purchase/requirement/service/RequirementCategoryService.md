## 类用途
需求分类服务接口，管理采购需求的分类体系

## 核心方法
### List<RequirementCategoryTreeDTO> getCategoryTree()
- 返回：分类树形结构
- 逻辑：
  1. 查询所有有效分类
  2. 构建父子关系树
  3. 计算各节点统计指标
  4. 返回前端所需结构

### String createCategory(RequirementCategoryDTO request)
- 参数：分类创建请求
- 返回：分类ID
- 逻辑：
  1. 验证分类名称唯一性
  2. 生成分类编码（自动递增）
  3. 保存分类记录
  4. 更新分类树缓存

### void updateCategory(String id, RequirementCategoryDTO request)
- 参数：分类ID/更新内容
- 逻辑：
  1. 校验分类是否可编辑
  2. 防止循环引用
  3. 更新分类信息
  4. 同步更新关联需求

## 树形结构规则
```mermaid
classDiagram
    class RequirementCategory {
        +String id
        +String name
        +String parentId
        +Integer sortOrder
        +List<RequirementCategory> children
    }
```

## 使用示例
```java
// 获取分类树示例
List<RequirementCategoryTreeDTO> tree = requirementCategoryService.getCategoryTree();

// 创建分类示例
RequirementCategoryDTO request = new RequirementCategoryDTO(
    "IT设备",
    "parent123",
    1
);
String catId = requirementCategoryService.createCategory(request);
```

## 注意事项
- 分类删除需处理关联数据
- 树形结构需支持无限层级
- 生产环境建议添加分类缓存