# RequirementCategoryServiceImpl.java

## 文件概述 (File Overview)
`RequirementCategoryServiceImpl.java` 是需求分类服务的具体实现类，继承了 `ServiceImpl<RequirementCategoryMapper, RequirementCategory>` 并实现了 `RequirementCategoryService` 接口。该类负责处理采购需求分类的所有业务逻辑，包括分类的查询、创建、更新、删除、树形结构构建、统计信息获取等功能。它通过MyBatis-Plus提供的基础功能和自定义的业务方法，为采购需求系统提供完整的分类管理服务。

## 核心功能 (Core Functionality)
*   **分类查询管理:** 提供多种分类查询方式，包括全部分类、启用分类、树形结构分类等。
*   **分类CRUD操作:** 支持分类的创建、更新、删除等基本操作，包含业务验证和数据完整性检查。
*   **树形结构构建:** 将扁平的分类数据转换为层次化的树形结构，支持多级分类展示。
*   **统计信息计算:** 提供分类下需求数量统计、分类使用情况分析等统计功能。
*   **数据转换服务:** 在实体对象、DTO对象和VO对象之间进行数据转换。
*   **缓存管理:** 对频繁查询的分类数据进行缓存优化。
*   **业务验证:** 确保分类操作的业务规则正确性，如删除前检查是否有关联需求。

## 接口说明 (Interface Description)

### 主要方法 (Main Methods)

#### 1. listCategories()
*   **返回值:** `List<RequirementCategoryVO>`
*   **功能:** 获取所有分类列表，按排序字段和ID升序排列
*   **业务逻辑:** 查询所有分类并转换为VO对象

#### 2. listEnabledCategories()
*   **返回值:** `List<RequirementCategoryVO>`
*   **功能:** 获取所有启用状态的分类列表
*   **业务逻辑:** 只返回状态为启用的分类

#### 3. getCategoryTree()
*   **返回值:** `List<RequirementCategoryTreeDTO>`
*   **功能:** 获取分类的树形结构
*   **业务逻辑:** 将扁平的分类数据构建为层次化的树形结构

#### 4. createCategory(RequirementCategoryDTO categoryDTO)
*   **参数:** `categoryDTO` - 分类创建请求对象
*   **返回值:** `RequirementCategoryVO`
*   **功能:** 创建新的需求分类
*   **业务逻辑:**
    1. 验证分类数据的有效性
    2. 检查分类名称是否重复
    3. 设置默认值和创建时间
    4. 保存到数据库并返回创建结果

#### 5. updateCategory(Long id, RequirementCategoryDTO categoryDTO)
*   **参数:**
    *   `id` - 分类ID
    *   `categoryDTO` - 分类更新请求对象
*   **返回值:** `RequirementCategoryVO`
*   **功能:** 更新指定的需求分类
*   **业务逻辑:**
    1. 验证分类是否存在
    2. 检查更新数据的有效性
    3. 更新分类信息
    4. 返回更新后的结果

#### 6. deleteCategory(Long id)
*   **参数:** `id` - 分类ID
*   **返回值:** `boolean`
*   **功能:** 删除指定的需求分类
*   **业务逻辑:**
    1. 检查分类是否存在
    2. 验证是否有关联的需求
    3. 执行逻辑删除或物理删除

#### 7. getCategoryWithStats(Long id)
*   **参数:** `id` - 分类ID
*   **返回值:** `RequirementCategoryVO`
*   **功能:** 获取包含统计信息的分类详情
*   **业务逻辑:** 查询分类基本信息和相关统计数据

### 辅助方法 (Helper Methods)

#### 1. convertToVOList(List<RequirementCategory> categoryList)
*   **功能:** 将实体列表转换为VO列表
*   **业务逻辑:** 批量进行数据转换和字段映射

#### 2. buildCategoryTree(List<RequirementCategory> categories)
*   **功能:** 构建分类树形结构
*   **业务逻辑:** 递归构建父子关系的树形数据结构

#### 3. validateCategoryData(RequirementCategoryDTO categoryDTO)
*   **功能:** 验证分类数据的有效性
*   **业务逻辑:** 检查必填字段、数据格式、业务规则等

## 使用示例 (Usage Examples)

```java
// 在Controller中使用RequirementCategoryServiceImpl
@RestController
@RequestMapping("/api/v1/requirement-categories")
public class RequirementCategoryController {
    @Autowired
    private RequirementCategoryService categoryService;

    @GetMapping
    public Result<List<RequirementCategoryVO>> getAllCategories() {
        List<RequirementCategoryVO> categories = categoryService.listCategories();
        return Result.success(categories);
    }

    @GetMapping("/enabled")
    public Result<List<RequirementCategoryVO>> getEnabledCategories() {
        List<RequirementCategoryVO> categories = categoryService.listEnabledCategories();
        return Result.success(categories);
    }

    @GetMapping("/tree")
    public Result<List<RequirementCategoryTreeDTO>> getCategoryTree() {
        List<RequirementCategoryTreeDTO> tree = categoryService.getCategoryTree();
        return Result.success(tree);
    }

    @PostMapping
    public Result<RequirementCategoryVO> createCategory(@RequestBody RequirementCategoryDTO categoryDTO) {
        RequirementCategoryVO category = categoryService.createCategory(categoryDTO);
        return Result.success(category);
    }

    @PutMapping("/{id}")
    public Result<RequirementCategoryVO> updateCategory(
            @PathVariable Long id,
            @RequestBody RequirementCategoryDTO categoryDTO) {
        RequirementCategoryVO category = categoryService.updateCategory(id, categoryDTO);
        return Result.success(category);
    }

    @DeleteMapping("/{id}")
    public Result<String> deleteCategory(@PathVariable Long id) {
        boolean success = categoryService.deleteCategory(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }
}

// 在其他Service中使用
@Service
public class PurchaseRequirementService {
    @Autowired
    private RequirementCategoryService categoryService;

    public void validateRequirementCategory(Long categoryId) {
        List<RequirementCategoryVO> enabledCategories = categoryService.listEnabledCategories();
        boolean isValidCategory = enabledCategories.stream()
            .anyMatch(category -> category.getId().equals(categoryId));

        if (!isValidCategory) {
            throw new BusinessException("无效的需求分类");
        }
    }

    public Map<String, Object> getRequirementWithCategoryInfo(Long requirementId) {
        // 获取需求信息
        PurchaseRequirement requirement = getRequirementById(requirementId);

        // 获取分类信息
        RequirementCategoryVO category = categoryService.getCategoryWithStats(requirement.getCategoryId());

        Map<String, Object> result = new HashMap<>();
        result.put("requirement", requirement);
        result.put("category", category);
        return result;
    }
}
```

## 注意事项 (Notes)
*   **继承关系:** 该类继承了 `ServiceImpl<RequirementCategoryMapper, RequirementCategory>`，自动获得了MyBatis-Plus提供的基础CRUD功能。
*   **事务管理:** 涉及数据修改的方法使用 `@Transactional` 注解确保数据一致性。
*   **数据验证:** 在创建和更新分类时进行严格的数据验证，确保数据的完整性和业务规则的正确性。
*   **缓存策略:** 分类数据相对稳定，适合使用缓存来提高查询性能，特别是树形结构的构建。
*   **树形结构构建:** 树形结构的构建可能涉及递归操作，需要注意性能优化和循环引用的防护。
*   **关联数据检查:** 在删除分类前需要检查是否有关联的采购需求，避免数据完整性问题。
*   **排序规则:** 分类列表按照 `sortOrder` 和 `id` 字段升序排列，确保展示顺序的一致性。
*   **VO转换:** 使用VO对象向外暴露数据，避免直接暴露实体类，提高系统的安全性和可维护性。
