# ServiceLevelService.md

## 1. 文件概述

`ServiceLevelService.java` 是需求模块中一个用于管理“服务级别”配置的服务接口，位于 `com.purchase.requirement.service` 包中。服务级别（Service Level）是系统中一个重要的配置项，它定义了不同等级的服务标准，例如“标准服务”、“加急服务”、“VIP服务”等。这些级别通常与不同的价格、处理时效或服务内容相关联。该接口提供了对这些服务级别进行增删改查（CRUD）的完整功能，是系统实现差异化服务和定价策略的基础。

## 2. 核心功能

*   **CRUD操作**: 提供了对服务级别进行创建 (`createServiceLevel`)、查询 (`getAllLevels`, `getLevelById`, `getLevelByCode`)、更新 (`updateServiceLevel`) 和删除 (`deleteServiceLevel`) 的全套标准管理功能。
*   **状态查询**: 支持专门查询所有“已启用”的服务级别 (`getAllActiveLevels`)，这通常用于在前端（如发布需求页面）的下拉列表中，只向用户展示当前可用的服务选项。
*   **多方式查询**: 支持通过主键ID (`getLevelById`) 或唯一的业务代码 (`getLevelByCode`) 两种方式来精确查找一个服务级别，提高了接口的灵活性。
*   **数据封装**: 接口统一使用 `ServiceLevelDTO` 作为数据传输对象，用于封装服务级别的所有属性（如ID, 代码, 名称, 描述, 价格乘数, 状态等），实现了内外模型的分离。
*   **后台管理支持**: 该接口的功能主要是为了支持系统的后台管理界面，让运营人员可以灵活地配置和调整服务级别及其相关参数。

## 3. 接口说明

### 服务级别管理接口

#### createServiceLevel - 创建服务级别
*   **方法签名**: `ServiceLevelDTO createServiceLevel(ServiceLevelDTO serviceLevelDTO)`
*   **参数**:
    *   `serviceLevelDTO` (ServiceLevelDTO): 一个包含了新服务级别所有属性的DTO对象。
*   **返回值**: `ServiceLevelDTO` - 创建成功后，包含新生成ID的服务级别DTO。
*   **业务逻辑**: 服务实现需要将传入的DTO转换为实体对象，并持久化到数据库。在持久化前，应校验 `levelCode` 的唯一性。

#### getAllActiveLevels - 获取所有启用的服务级别
*   **方法签名**: `List<ServiceLevelDTO> getAllActiveLevels()`
*   **参数**: 无
*   **返回值**: `List<ServiceLevelDTO>` - 所有状态为“启用”的服务级别列表。
*   **业务逻辑**: 查询数据库中所有 `status` 字段为 `ACTIVE` 或 `1` 的记录，并将其转换为DTO列表返回。

#### getLevelById - 根据ID获取服务级别
*   **方法签名**: `ServiceLevelDTO getLevelById(Integer id)`
*   **参数**:
    *   `id` (Integer): 服务级别的唯一主键ID。
*   **返回值**: `ServiceLevelDTO` - 匹配的服务级别信息，如果不存在则返回 `null`。

#### updateServiceLevel - 更新服务级别
*   **方法签名**: `ServiceLevelDTO updateServiceLevel(Integer id, ServiceLevelDTO serviceLevelDTO)`
*   **参数**:
    *   `id` (Integer): 需要更新的服务级别的主键ID。
    *   `serviceLevelDTO` (ServiceLevelDTO): 包含了待更新字段的新信息的DTO。
*   **返回值**: `ServiceLevelDTO` - 更新成功后的服务级别DTO。
*   **业务逻辑**: 先根据 `id` 查找出实体，然后用DTO中的新值更新实体属性，最后保存回数据库。同样需要校验 `levelCode` 的唯一性（如果允许修改的话）。

#### deleteServiceLevel - 删除服务级别
*   **方法签名**: `boolean deleteServiceLevel(Integer id)`
*   **参数**:
    *   `id` (Integer): 需要删除的服务级别的主键ID。
*   **返回值**: `boolean` - 如果删除成功，返回 `true`。
*   **业务逻辑**: 从数据库中删除指定ID的记录。在删除前，应检查该服务级别是否仍被任何现有的需求或订单所引用，以维护数据完整性。如果已被引用，应阻止删除并抛出业务异常。

## 4. 业务规则

*   **代码唯一性**: `levelCode` 作为一个业务标识，必须在所有服务级别中保持唯一。`create` 和 `update` 操作都需要对此进行校验。
*   **删除约束**: 在删除一个服务级别前，必须检查系统中是否存在引用了该级别的采购需求或订单。如果存在，应禁止删除，或者提供一个“归档”或“禁用”的选项来代替物理删除。
*   **状态管理**: 服务级别应有一个“启用/禁用”的状态。只有“启用”状态的级别才能在创建新需求时被用户选择。
*   **默认级别**: 系统中可能需要指定一个“默认服务级别”。当用户不选择时，系统会自动应用此级别。

## 5. 使用示例

```java
// 1. 服务实现类示例
@Service
@Transactional
public class ServiceLevelServiceImpl implements ServiceLevelService {
    @Autowired
    private ServiceLevelRepository levelRepository;
    @Autowired
    private RequirementRepository requirementRepository;

    @Override
    public ServiceLevelDTO createServiceLevel(ServiceLevelDTO dto) {
        if (levelRepository.existsByLevelCode(dto.getLevelCode())) {
            throw new BusinessException("服务级别代码已存在");
        }
        ServiceLevel entity = convertToEntity(dto);
        ServiceLevel savedEntity = levelRepository.save(entity);
        return convertToDTO(savedEntity);
    }

    @Override
    public boolean deleteServiceLevel(Integer id) {
        if (requirementRepository.existsByServiceLevelId(id)) {
            throw new BusinessException("无法删除：该服务级别已被需求引用");
        }
        levelRepository.deleteById(id);
        return true;
    }
    // ... 其他方法的实现和 DTO/Entity 转换逻辑 ...
}

// 2. 在后台管理Controller中调用
@RestController
@RequestMapping("/api/v1/admin/service-levels")
@PreAuthorize("hasAuthority('admin')")
public class ServiceLevelAdminController {
    @Autowired
    private ServiceLevelService levelService;

    @GetMapping
    public Result<List<ServiceLevelDTO>> getAll() {
        return Result.success(levelService.getAllLevels());
    }

    @PostMapping
    public Result<ServiceLevelDTO> create(@RequestBody ServiceLevelDTO dto) {
        return Result.success(levelService.createServiceLevel(dto));
    }

    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Integer id) {
        return Result.success(levelService.deleteServiceLevel(id));
    }
}

// 3. 在发布需求的页面加载时，前端调用以获取可用级别
// 前端JavaScript (axios)
async function fetchActiveServiceLevels() {
  try {
    const response = await axios.get('/api/v1/public/service-levels/active'); // 假设有一个公开的查询接口
    return response.data.data; // 返回启用的服务级别列表，用于填充下拉框
  } catch (error) {
    console.error('获取服务级别失败', error);
    return [];
  }
}

// 4. 测试示例
@SpringBootTest
class ServiceLevelServiceTest {
    @Autowired
    private ServiceLevelService levelService;
    @MockBean
    private ServiceLevelRepository levelRepository;

    @Test
    void testCreateServiceLevel_CodeExists() {
        ServiceLevelDTO dto = new ServiceLevelDTO();
        dto.setLevelCode("EXISTING_CODE");
        when(levelRepository.existsByLevelCode("EXISTING_CODE")).thenReturn(true);

        // 断言当代码已存在时，会抛出业务异常
        assertThrows(BusinessException.class, () -> {
            levelService.createServiceLevel(dto);
        });
    }
}
```

## 6. 注意事项

*   **事务管理**: 所有写操作（`create`, `update`, `delete`）的实现都必须是事务性的 (`@Transactional`)。
*   **权限控制**: 对服务级别的管理操作（增删改）应严格限制为管理员角色。查询操作（特别是查询启用的级别）可以开放给更广泛的用户。
*   **数据完整性**: 删除操作的校验逻辑至关重要，必须防止因删除了被引用的基础数据而导致系统出现脏数据或运行时错误。
*   **DTO与实体的转换**: 实现类中需要包含 `convertToDTO` 和 `convertToEntity` 的逻辑，确保数据在不同层之间的正确映射。
*   **缓存**: 服务级别是典型的配置数据，不经常变动。为了提升性能，可以对 `getAllActiveLevels` 和 `getLevelById` 等查询结果进行缓存（例如，使用Spring Cache和Redis）。当有更新或删除操作时，需要相应地清除缓存。
*   **命名规范**: 接口和方法命名清晰，符合Java社区的通用规范。
*   **错误处理**: 实现类在遇到业务约束冲突时（如代码重复、删除被引用的数据），应抛出自定义的、具有明确业务含义的异常（如 `DuplicateCodeException`, `DataInUseException`），而不是通用的 `Exception`。