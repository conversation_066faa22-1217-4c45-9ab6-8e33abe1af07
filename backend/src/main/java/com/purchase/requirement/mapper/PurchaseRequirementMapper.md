## 类用途
采购需求映射器，处理PurchaseRequirement实体与DTO之间的转换

## 核心方法
### PurchaseRequirementDTO entityToDto(PurchaseRequirement entity)
- 参数：实体对象
- 返回：DTO对象
- 映射规则：
  - entity.id → dto.requirementId
  - entity.name → dto.requirementName
  - entity.category → dto.categoryDTO
  - 自定义处理：
    - 金额单位转换
    - 日期格式化
    - 状态枚举转换

### PurchaseRequirement dtoToEntity(PurchaseRequirementDTO dto)
- 参数：DTO对象
- 返回：实体对象
- 映射规则：
  - dto.requirementId → entity.id
  - dto.requirementName → entity.name
  - dto.categoryDTO → entity.category
  - 反向处理：
    - 元→分
    - 日期解析
    - 状态枚举反向转换

## 特殊字段处理
```java
// 金额转换示例
default BigDecimal mapAmount(Integer amount) {
    return new BigDecimal(amount).divide(new BigDecimal(100));
}

// 状态枚举转换示例
default String mapStatus(RequirementStatus status) {
    switch(status) {
        case DRAFT: return "草稿";
        case APPROVED: return "已批准";
        default: return status.name();
    }
}
```

## 使用示例
```java
// 实体转DTO示例
PurchaseRequirement entity = purchaseRequirementRepository.findById("req123");
PurchaseRequirementDTO dto = purchaseRequirementMapper.entityToDto(entity);

// DTO转实体示例
PurchaseRequirementDTO dto = new PurchaseRequirementDTO(...);
PurchaseRequirement entity = purchaseRequirementMapper.dtoToEntity(dto);
```

## 注意事项
- 敏感字段需脱敏处理
- 批量转换时需优化性能
- 生产环境建议添加缓存
