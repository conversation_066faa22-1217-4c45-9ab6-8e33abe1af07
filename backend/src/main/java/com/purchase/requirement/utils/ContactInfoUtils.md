# ContactInfoUtils 联系信息工具类文档

## 文件概述

`ContactInfoUtils` 是联系信息处理工具类，位于 `com.purchase.requirement.utils` 包中。该工具类专门用于处理买家联系信息的脱敏和校验，提供了多种格式的联系信息脱敏功能，包括手机号、邮箱、JSON格式联系信息等，确保在展示联系信息时保护用户隐私的同时保持信息的可识别性。

## 核心功能

### 主要职责
- **联系信息脱敏**: 对手机号、邮箱、姓名等敏感信息进行智能脱敏处理
- **格式验证**: 验证联系信息的格式是否合法
- **JSON脱敏**: 对JSON格式的复杂联系信息进行全面脱敏
- **多格式支持**: 支持多种联系信息格式的识别和处理
- **隐私保护**: 在保持信息可识别性的前提下最大化保护用户隐私

### 业务特点
- 智能识别不同类型的联系信息格式
- 采用差异化脱敏策略保持信息可用性
- 支持国际化的电话号码和邮箱格式
- 提供完整的JSON联系信息脱敏方案
- 具备完善的异常处理机制

## 接口说明

### 核心静态方法

#### maskContactInfo(String contactInfo)
```java
public static String maskContactInfo(String contactInfo)
```
- **功能**: 脱敏联系信息
- **参数**: `contactInfo` - 原始联系信息字符串
- **返回值**: 脱敏后的联系信息字符串
- **支持格式**:
  - 手机号: `13812345678` → `138****5678`
  - 邮箱: `<EMAIL>` → `exa****@em****.com`
  - 其他格式: 保留前3个和后3个字符

#### isValidContactInfo(String contactInfo)
```java
public static boolean isValidContactInfo(String contactInfo)
```
- **功能**: 验证联系信息格式是否合法
- **参数**: `contactInfo` - 待验证的联系信息
- **返回值**: `true` 表示格式合法，`false` 表示格式不合法
- **验证规则**:
  - 中国手机号: `^1[3-9]\\d{9}$`
  - 邮箱格式: `^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$`
  - 联系人+电话: `^.+\\s+1[3-9]\\d{9}$`
  - 最小长度: 5个字符

#### maskJsonContactInfo(String jsonContactInfo)
```java
public static String maskJsonContactInfo(String jsonContactInfo)
```
- **功能**: 脱敏JSON格式的联系信息
- **参数**: `jsonContactInfo` - JSON格式的联系信息字符串
- **返回值**: 脱敏后的JSON字符串
- **支持字段**:
  - `companyName`: 公司名称
  - `contactPerson`: 联系人姓名
  - `phone`/`phoneNumber`: 电话号码
  - `email`: 邮箱地址
  - `address`: 地址信息
  - `country`/`province`/`city`: 地理位置
  - `detailedAddress`: 详细地址

## 使用示例

### 基础联系信息脱敏
```java
import com.purchase.requirement.utils.ContactInfoUtils;

public class ContactInfoExample {

    public void demonstrateBasicMasking() {
        // 手机号脱敏
        String phone = "13812345678";
        String maskedPhone = ContactInfoUtils.maskContactInfo(phone);
        System.out.println(maskedPhone); // 输出: 138****5678

        // 邮箱脱敏
        String email = "<EMAIL>";
        String maskedEmail = ContactInfoUtils.maskContactInfo(email);
        System.out.println(maskedEmail); // 输出: joh****@co****.com

        // 其他格式脱敏
        String contact = "张三 13812345678";
        String maskedContact = ContactInfoUtils.maskContactInfo(contact);
        System.out.println(maskedContact); // 输出: 张三****678
    }

    public void demonstrateValidation() {
        // 验证手机号
        boolean isValid1 = ContactInfoUtils.isValidContactInfo("13812345678");
        System.out.println(isValid1); // 输出: true

        // 验证邮箱
        boolean isValid2 = ContactInfoUtils.isValidContactInfo("<EMAIL>");
        System.out.println(isValid2); // 输出: true

        // 验证联系人+电话格式
        boolean isValid3 = ContactInfoUtils.isValidContactInfo("张三 13812345678");
        System.out.println(isValid3); // 输出: true

        // 无效格式
        boolean isValid4 = ContactInfoUtils.isValidContactInfo("123");
        System.out.println(isValid4); // 输出: false
    }
}
```

### JSON联系信息脱敏
```java
import com.purchase.requirement.utils.ContactInfoUtils;

public class JsonContactInfoExample {

    public void demonstrateJsonMasking() {
        // 构建JSON联系信息
        String jsonContactInfo = """
            {
                "companyName": "深圳科技有限公司",
                "contactPerson": "张三",
                "phone": "13812345678",
                "email": "<EMAIL>",
                "country": "中国",
                "province": "广东省",
                "city": "深圳市",
                "detailedAddress": "南山区科技园南区R2-B栋6楼"
            }
            """;

        // 脱敏处理
        String maskedJson = ContactInfoUtils.maskJsonContactInfo(jsonContactInfo);
        System.out.println(maskedJson);

        // 输出示例:
        // {
        //     "companyName": "深圳***公司",
        //     "contactPerson": "张**",
        //     "phone": "138***5678",
        //     "email": "zha***@co****.com",
        //     "country": "中*",
        //     "province": "广*",
        //     "city": "深**",
        //     "detailedAddress": "南山区***楼"
        // }
    }
}
```

### 在需求管理中的应用
```java
@Service
public class RequirementDisplayService {

    public RequirementDTO getRequirementForDisplay(Long requirementId, Long currentUserId) {
        Requirement requirement = requirementRepository.findById(requirementId);
        RequirementDTO dto = convertToDTO(requirement);

        // 如果不是需求发布者，脱敏联系信息
        if (!requirement.getBuyerId().equals(currentUserId)) {
            String originalContact = dto.getBuyerContact();
            String maskedContact = ContactInfoUtils.maskContactInfo(originalContact);
            dto.setBuyerContact(maskedContact);

            // 如果有JSON格式的详细联系信息，也进行脱敏
            if (dto.getDetailedContactInfo() != null) {
                String maskedDetailedContact = ContactInfoUtils.maskJsonContactInfo(
                    dto.getDetailedContactInfo()
                );
                dto.setDetailedContactInfo(maskedDetailedContact);
            }
        }

        return dto;
    }
}
```

## 注意事项

### 隐私保护
1. **脱敏策略**: 不同类型信息采用不同的脱敏策略，平衡隐私保护和信息可用性
2. **敏感信息**: 确保所有敏感信息都经过适当脱敏处理
3. **权限控制**: 结合业务权限控制，只对无权限用户进行脱敏
4. **日志安全**: 避免在日志中记录原始的敏感信息

### 性能考虑
1. **正则表达式**: 合理使用正则表达式，避免复杂模式影响性能
2. **JSON处理**: 大量JSON处理时考虑使用流式解析
3. **缓存策略**: 对频繁脱敏的信息考虑缓存脱敏结果
4. **批量处理**: 大批量数据脱敏时使用批量处理方式

### 扩展性
1. **新格式支持**: 易于扩展支持新的联系信息格式
2. **脱敏规则**: 支持配置化的脱敏规则
3. **国际化**: 支持不同国家和地区的联系信息格式
4. **业务定制**: 支持不同业务场景的定制化脱敏需求
