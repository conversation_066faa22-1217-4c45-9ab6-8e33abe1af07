# PurchaseRequirement - 采购需求实体文档

## 文件概述
采购需求实体类(PurchaseRequirement.java)是系统的核心业务实体，代表买家的采购需求。它映射到数据库中的`purchase_requirement`表，包含需求的基本信息、规格要求、价格区间、交付时间等关键业务数据。

## 核心字段

### 基本信息
- `id`: 需求ID (主键)
- `buyerId`: 买家用户ID
- `categoryId`: 关联的分类ID
- `title`: 需求标题
- `description`: 需求描述
- `specification`: 规格要求

### 价格与数量
- `minPrice`: 最低可接受价格
- `maxPrice`: 最高可接受价格
- `quantity`: 采购数量
- `unit`: 数量单位 (件、千克等)

### 时间相关
- `expectedDeliveryTime`: 期望交付时间
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

### 多媒体
- `images`: 相关图片URL (多个用逗号分隔)
- `videos`: 相关视频URL (多个用逗号分隔)
- `pdfUrl`: PDF文档URL

### 业务状态
- `status`: 需求状态 (in_progress/completed/cancelled)
- `requirementType`: 需求类型 (purchase/sample)
- `serviceLevel`: 服务级别 (basic/standard/premium)
- `serviceFeeRate`: 服务费率

### 国际贸易
- `hsCode`: 海关商品编码
- `deliveryTerms`: 交付条款 (FOB/CIF等)

## 重要方法

### 状态管理
```java
// 验证状态是否有效
Status.isValid(String status)

// 获取所有有效状态
Status.getAllStatuses()

// 获取默认状态
Status.getDefault()
```

### 需求类型管理
```java
// 验证需求类型是否有效
RequirementType.isValid(String type)

// 获取所有有效需求类型  
RequirementType.getAllTypes()

// 获取默认需求类型
RequirementType.getDefault()
```

### 字段验证
```java
// 设置需求类型时的自动验证
public void setRequirementType(String requirementType)
```

## 使用示例

### 创建新需求
```java
PurchaseRequirement requirement = new PurchaseRequirement();
requirement.setBuyerId(1L);
requirement.setCategoryId(10L);
requirement.setTitle("采购不锈钢螺丝");
requirement.setDescription("M4不锈钢十字螺丝，304材质");
requirement.setMinPrice(new BigDecimal("0.5"));
requirement.setMaxPrice(new BigDecimal("0.8"));
requirement.setQuantity(new BigDecimal("10000"));
requirement.setUnit("件");
requirement.setStatus(Status.IN_PROGRESS);
requirement.setRequirementType(RequirementType.PURCHASE);
requirementMapper.insert(requirement);
```

### 查询需求
```java
// 根据ID查询
PurchaseRequirement req = requirementMapper.selectById(1L);

// 根据状态查询
QueryWrapper<PurchaseRequirement> query = new QueryWrapper<>();
query.eq("status", Status.IN_PROGRESS);
List<PurchaseRequirement> inProgressReqs = requirementMapper.selectList(query);
```

## 注意事项
1. **状态管理**:
   - 状态变更需要遵循业务规则
   - 已完成的需求不能修改

2. **数据验证**:
   - 价格区间必须有效 (minPrice ≤ maxPrice)
   - 需求类型必须有效
   - 交付时间不能早于当前时间

3. **权限控制**:
   - 只有买家可以创建需求
   - 只有需求创建者或管理员可以修改

4. **关联数据**:
   - 删除需求时需要处理关联的报价和订单

5. **审计跟踪**:
   - 关键字段变更需要记录日志