# RequirementCategoryVO 需求分类视图对象文档

## 文件概述

`RequirementCategoryVO` 是需求分类视图对象，用于向前端展示需求分类的详细信息。该VO对象包含了分类的基本信息、层级关系、状态信息以及国际化支持，为前端提供完整的分类数据结构。

## 核心功能

### 主要职责
- **数据展示**: 向前端提供格式化的需求分类数据
- **层级结构**: 支持父子分类的树形结构展示
- **国际化支持**: 提供中英文双语分类信息
- **状态管理**: 包含分类的启用/禁用状态信息
- **属性模板**: 支持分类相关的属性模板配置

### 业务特点
- 支持树形结构的分类层级
- 完整的国际化字段支持
- 灵活的属性模板配置（JSON格式）
- 状态文本的友好显示
- 序列化支持便于缓存和传输

## 接口说明

### 基础信息字段

#### id
- **类型**: Long
- **描述**: 分类唯一标识
- **用途**: 分类的主键ID

#### name
- **类型**: String
- **描述**: 分类中文名称
- **用途**: 中文环境下的分类显示名称

#### nameEn
- **类型**: String
- **描述**: 分类英文名称
- **用途**: 英文环境下的分类显示名称

#### description
- **类型**: String
- **描述**: 分类描述信息
- **用途**: 分类的详细说明

#### icon
- **类型**: String
- **描述**: 分类图标
- **用途**: 前端显示分类图标的URL或图标类名

### 层级关系字段

#### parentId
- **类型**: Long
- **描述**: 父分类ID
- **用途**: 标识分类的父级关系，null表示顶级分类

#### parentName
- **类型**: String
- **描述**: 父分类名称
- **用途**: 显示父分类的名称，便于前端展示

#### children
- **类型**: List<RequirementCategoryVO>
- **描述**: 子分类列表
- **用途**: 构建树形结构，包含当前分类的所有子分类

### 状态和排序字段

#### status
- **类型**: Integer
- **描述**: 分类状态（0-禁用，1-启用）
- **用途**: 控制分类的可用性

#### statusText
- **类型**: String
- **描述**: 状态文本描述
- **用途**: 前端友好显示状态信息

#### sortOrder
- **类型**: Integer
- **描述**: 排序顺序
- **用途**: 控制分类在同级中的显示顺序

### 属性模板字段

#### attributesJson
- **类型**: String
- **描述**: 中文属性模板（JSON格式）
- **用途**: 定义该分类下需求的属性结构

#### attributesJsonEn
- **类型**: String
- **描述**: 英文属性模板（JSON格式）
- **用途**: 英文环境下的属性模板

### 时间字段

#### createdAt
- **类型**: LocalDateTime
- **描述**: 创建时间
- **用途**: 记录分类创建的时间

#### updatedAt
- **类型**: LocalDateTime
- **描述**: 更新时间
- **用途**: 记录分类最后更新的时间

## 使用示例

### 构建分类树形结构
```java
public class RequirementCategoryService {
    
    public List<RequirementCategoryVO> getCategoryTree() {
        // 获取所有分类
        List<RequirementCategory> categories = categoryMapper.selectList(
            new QueryWrapper<RequirementCategory>()
                .eq("status", 1)
                .orderByAsc("sort_order")
        );
        
        // 转换为VO对象
        List<RequirementCategoryVO> categoryVOs = categories.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        
        // 构建树形结构
        return buildCategoryTree(categoryVOs);
    }
    
    private RequirementCategoryVO convertToVO(RequirementCategory category) {
        RequirementCategoryVO vo = new RequirementCategoryVO();
        BeanUtils.copyProperties(category, vo);
        
        // 设置状态文本
        vo.setStatusText(category.getStatus() == 1 ? "启用" : "禁用");
        
        // 设置父分类名称
        if (category.getParentId() != null) {
            RequirementCategory parent = categoryMapper.selectById(category.getParentId());
            if (parent != null) {
                vo.setParentName(parent.getName());
            }
        }
        
        return vo;
    }
    
    private List<RequirementCategoryVO> buildCategoryTree(List<RequirementCategoryVO> categories) {
        Map<Long, RequirementCategoryVO> categoryMap = categories.stream()
            .collect(Collectors.toMap(RequirementCategoryVO::getId, Function.identity()));
        
        List<RequirementCategoryVO> rootCategories = new ArrayList<>();
        
        for (RequirementCategoryVO category : categories) {
            if (category.getParentId() == null) {
                // 顶级分类
                rootCategories.add(category);
            } else {
                // 子分类
                RequirementCategoryVO parent = categoryMap.get(category.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(category);
                }
            }
        }
        
        return rootCategories;
    }
}
```

### 前端分类选择器
```java
@RestController
@RequestMapping("/api/requirement-categories")
public class RequirementCategoryController {
    
    @GetMapping("/tree")
    public ApiResponse<List<RequirementCategoryVO>> getCategoryTree(
            @RequestParam(defaultValue = "zh") String lang) {
        
        List<RequirementCategoryVO> categoryTree = categoryService.getCategoryTree();
        
        // 根据语言设置显示名称
        if ("en".equals(lang)) {
            setEnglishNames(categoryTree);
        }
        
        return ApiResponse.success(categoryTree);
    }
    
    private void setEnglishNames(List<RequirementCategoryVO> categories) {
        for (RequirementCategoryVO category : categories) {
            if (category.getNameEn() != null) {
                category.setName(category.getNameEn());
            }
            if (category.getChildren() != null) {
                setEnglishNames(category.getChildren());
            }
        }
    }
}
```

### 属性模板处理
```java
public class CategoryAttributeService {
    
    public List<CategoryAttribute> parseAttributes(RequirementCategoryVO category, String lang) {
        String attributesJson = "en".equals(lang) ? 
            category.getAttributesJsonEn() : category.getAttributesJson();
        
        if (attributesJson == null || attributesJson.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(attributesJson, 
                new TypeReference<List<CategoryAttribute>>() {});
        } catch (Exception e) {
            log.error("解析分类属性失败: {}", attributesJson, e);
            return Collections.emptyList();
        }
    }
    
    public String formatAttributesForDisplay(RequirementCategoryVO category) {
        List<CategoryAttribute> attributes = parseAttributes(category, "zh");
        return attributes.stream()
            .map(attr -> attr.getName() + ": " + attr.getType())
            .collect(Collectors.joining(", "));
    }
}
```

### 前端树形组件数据
```javascript
// 获取分类树数据
const getCategoryTree = async (lang = 'zh') => {
  try {
    const response = await fetch(`/api/requirement-categories/tree?lang=${lang}`);
    const result = await response.json();
    
    if (result.success) {
      return formatTreeData(result.data);
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取分类树失败:', error);
    return [];
  }
};

// 格式化树形数据
const formatTreeData = (categories) => {
  return categories.map(category => ({
    key: category.id,
    title: category.name,
    value: category.id,
    icon: category.icon,
    disabled: category.status === 0,
    children: category.children ? formatTreeData(category.children) : undefined
  }));
};
```

## 注意事项

### 数据完整性
1. **层级关系**: 确保parentId指向有效的父分类
2. **状态一致性**: 父分类禁用时，子分类也应该不可用
3. **排序逻辑**: 同级分类按sortOrder排序
4. **国际化数据**: 确保中英文数据的一致性

### 性能优化
1. **树形构建**: 使用Map优化树形结构构建性能
2. **懒加载**: 大量分类时考虑懒加载子分类
3. **缓存策略**: 分类数据变化不频繁，适合缓存
4. **序列化**: 实现Serializable接口支持缓存

### 前端集成
1. **树形组件**: 适配各种前端树形组件的数据格式
2. **国际化**: 根据语言参数返回对应语言的数据
3. **状态显示**: 提供友好的状态文本显示
4. **图标支持**: 支持图标URL或CSS类名

### 扩展性
1. **属性模板**: JSON格式支持灵活的属性配置
2. **多语言**: 可扩展支持更多语言
3. **自定义字段**: 可根据业务需要添加新字段
4. **权限控制**: 可扩展添加权限相关字段
