# RequirementPageVO 需求分页查询结果VO文档

## 文件概述

`RequirementPageVO` 是需求分页查询结果视图对象，用于封装分页查询的结果数据。该VO对象包含总记录数和需求列表，为前端提供标准化的分页数据结构，支持分页组件的数据展示。

## 核心功能

### 主要职责
- **分页数据封装**: 封装分页查询的总记录数和数据列表
- **标准化结构**: 提供统一的分页数据结构
- **便捷构造**: 提供静态工厂方法简化对象创建
- **前端适配**: 适配前端分页组件的数据格式

### 业务特点
- 简洁的分页数据结构
- 静态工厂方法支持
- 泛型化的数据列表
- 标准化的分页响应格式

## 接口说明

### 核心字段

#### total
- **类型**: Long
- **描述**: 总记录数
- **用途**: 用于前端分页组件计算总页数和显示记录统计

#### items
- **类型**: List<RequirementVO>
- **描述**: 当前页的需求列表
- **用途**: 包含当前页面要显示的需求详情数据

### 静态方法

#### of(Long total, List<RequirementVO> items)
```java
public static RequirementPageVO of(Long total, List<RequirementVO> items)
```
- **功能**: 静态工厂方法，创建RequirementPageVO实例
- **参数**:
  - `total`: 总记录数
  - `items`: 需求列表
- **返回值**: RequirementPageVO实例
- **用途**: 简化对象创建过程

## 使用示例

### 服务层分页查询
```java
@Service
public class RequirementService {
    
    public RequirementPageVO getRequirements(Integer page, Integer size, 
                                           Long categoryId, String status) {
        // 构建查询条件
        QueryWrapper<PurchaseRequirement> wrapper = new QueryWrapper<>();
        if (categoryId != null) {
            wrapper.eq("category_id", categoryId);
        }
        if (status != null) {
            wrapper.eq("status", status);
        }
        wrapper.eq("deleted", "0");
        wrapper.orderByDesc("created_at");
        
        // 分页查询
        Page<PurchaseRequirement> pageParam = new Page<>(page, size);
        IPage<PurchaseRequirement> pageResult = requirementMapper.selectPage(pageParam, wrapper);
        
        // 转换为VO对象
        List<RequirementVO> requirementVOs = pageResult.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        
        // 使用静态工厂方法创建分页VO
        return RequirementPageVO.of(pageResult.getTotal(), requirementVOs);
    }
    
    private RequirementVO convertToVO(PurchaseRequirement requirement) {
        RequirementVO vo = new RequirementVO();
        BeanUtils.copyProperties(requirement, vo);
        
        // 补充分类信息
        if (requirement.getCategoryId() != null) {
            RequirementCategory category = categoryMapper.selectById(requirement.getCategoryId());
            if (category != null) {
                vo.setCategoryName(category.getName());
            }
        }
        
        // 解析媒体资源
        vo.setImages(parseMediaUrls(requirement.getImages()));
        vo.setVideos(parseMediaUrls(requirement.getVideos()));
        
        return vo;
    }
}
```

### 控制器层使用
```java
@RestController
@RequestMapping("/api/requirements")
public class RequirementController {
    
    @GetMapping
    public ApiResponse<RequirementPageVO> getRequirements(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String status) {
        
        RequirementPageVO pageVO = requirementService.getRequirements(
            page, size, categoryId, status
        );
        
        return ApiResponse.success(pageVO);
    }
    
    @GetMapping("/search")
    public ApiResponse<RequirementPageVO> searchRequirements(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        RequirementPageVO pageVO = requirementService.searchRequirements(
            keyword, page, size
        );
        
        return ApiResponse.success(pageVO);
    }
}
```

### 买家需求查询
```java
@Service
public class BuyerRequirementService {
    
    public RequirementPageVO getBuyerRequirements(Long buyerId, Integer page, Integer size) {
        // 查询买家的需求
        Page<PurchaseRequirement> pageParam = new Page<>(page, size);
        IPage<PurchaseRequirement> pageResult = requirementMapper.selectPage(pageParam,
            new QueryWrapper<PurchaseRequirement>()
                .eq("buyer_id", buyerId)
                .eq("deleted", "0")
                .orderByDesc("created_at")
        );
        
        // 转换为VO
        List<RequirementVO> requirementVOs = pageResult.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        
        return RequirementPageVO.of(pageResult.getTotal(), requirementVOs);
    }
}
```

### 前端分页组件集成
```javascript
// 获取需求分页数据
const getRequirements = async (page = 1, size = 10, filters = {}) => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
      ...filters
    });
    
    const response = await fetch(`/api/requirements?${params}`);
    const result = await response.json();
    
    if (result.success) {
      return {
        total: result.data.total,
        items: result.data.items,
        currentPage: page,
        pageSize: size,
        totalPages: Math.ceil(result.data.total / size)
      };
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取需求列表失败:', error);
    return {
      total: 0,
      items: [],
      currentPage: 1,
      pageSize: size,
      totalPages: 0
    };
  }
};

// React组件使用示例
const RequirementList = () => {
  const [pageData, setPageData] = useState({
    total: 0,
    items: [],
    currentPage: 1,
    pageSize: 10
  });
  const [loading, setLoading] = useState(false);
  
  const loadRequirements = async (page = 1, filters = {}) => {
    setLoading(true);
    try {
      const data = await getRequirements(page, pageData.pageSize, filters);
      setPageData(data);
    } catch (error) {
      message.error('加载需求列表失败');
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    loadRequirements();
  }, []);
  
  return (
    <div>
      <List
        loading={loading}
        dataSource={pageData.items}
        renderItem={item => (
          <RequirementCard key={item.id} requirement={item} />
        )}
      />
      <Pagination
        current={pageData.currentPage}
        pageSize={pageData.pageSize}
        total={pageData.total}
        onChange={(page) => loadRequirements(page)}
        showSizeChanger
        showQuickJumper
        showTotal={(total, range) => 
          `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
        }
      />
    </div>
  );
};
```

### 高级查询场景
```java
public class RequirementAdvancedService {
    
    public RequirementPageVO getRequirementsByMultipleConditions(
            RequirementSearchDTO searchDTO) {
        
        // 构建复杂查询条件
        QueryWrapper<PurchaseRequirement> wrapper = new QueryWrapper<>();
        
        // 分类筛选
        if (searchDTO.getCategoryIds() != null && !searchDTO.getCategoryIds().isEmpty()) {
            wrapper.in("category_id", searchDTO.getCategoryIds());
        }
        
        // 价格范围筛选
        if (searchDTO.getMinPrice() != null) {
            wrapper.ge("min_price", searchDTO.getMinPrice());
        }
        if (searchDTO.getMaxPrice() != null) {
            wrapper.le("max_price", searchDTO.getMaxPrice());
        }
        
        // 时间范围筛选
        if (searchDTO.getStartDate() != null) {
            wrapper.ge("created_at", searchDTO.getStartDate());
        }
        if (searchDTO.getEndDate() != null) {
            wrapper.le("created_at", searchDTO.getEndDate());
        }
        
        // 关键词搜索
        if (searchDTO.getKeyword() != null && !searchDTO.getKeyword().trim().isEmpty()) {
            wrapper.and(w -> w.like("title", searchDTO.getKeyword())
                           .or().like("description", searchDTO.getKeyword()));
        }
        
        wrapper.eq("deleted", "0");
        wrapper.orderByDesc("created_at");
        
        // 分页查询
        Page<PurchaseRequirement> pageParam = new Page<>(
            searchDTO.getPage(), searchDTO.getSize()
        );
        IPage<PurchaseRequirement> pageResult = requirementMapper.selectPage(pageParam, wrapper);
        
        // 转换结果
        List<RequirementVO> requirementVOs = pageResult.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        
        return RequirementPageVO.of(pageResult.getTotal(), requirementVOs);
    }
}
```

## 注意事项

### 数据一致性
1. **总数准确性**: 确保total字段反映真实的查询结果总数
2. **数据完整性**: items列表中的数据应该完整且有效
3. **分页逻辑**: 分页参数应该与查询结果保持一致
4. **排序一致性**: 确保分页查询的排序逻辑一致

### 性能优化
1. **查询优化**: 使用合适的索引优化分页查询性能
2. **数据量控制**: 合理设置分页大小，避免单页数据过多
3. **缓存策略**: 对于热门查询可以考虑缓存
4. **懒加载**: 大数据量场景考虑懒加载策略

### 前端集成
1. **分页组件**: 适配各种前端分页组件的数据格式
2. **加载状态**: 提供加载状态的友好提示
3. **错误处理**: 妥善处理查询失败的情况
4. **用户体验**: 保持分页状态，避免用户操作丢失

### 扩展性
1. **泛型支持**: 可以扩展为泛型类支持其他类型的分页
2. **元数据扩展**: 可以添加更多分页相关的元数据
3. **排序支持**: 可以扩展添加排序信息
4. **统计信息**: 可以添加查询统计信息
