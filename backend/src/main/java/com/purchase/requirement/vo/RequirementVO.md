# RequirementVO 需求详情视图对象文档

## 文件概述

`RequirementVO` 是需求详情视图对象，用于向前端展示需求的完整信息。该VO对象包含了需求的基本信息、价格范围、媒体资源、属性配置等详细数据，为前端提供丰富的需求展示内容。

## 核心功能

### 主要职责
- **需求展示**: 向前端提供完整的需求详情数据
- **多媒体支持**: 包含图片、视频、PDF等多种媒体资源
- **价格管理**: 支持价格范围的展示
- **属性配置**: 支持灵活的需求属性配置
- **状态跟踪**: 提供需求状态和时间信息

### 业务特点
- 支持普通采购和样品需求两种类型
- 完整的多媒体资源管理
- 灵活的属性配置（JSON格式）
- 丰富的买家和分类信息
- 详细的时间和状态跟踪

## 接口说明

### 基础信息字段

#### id
- **类型**: Long
- **描述**: 需求唯一标识
- **用途**: 需求的主键ID

#### buyerId
- **类型**: Long
- **描述**: 买家用户ID
- **用途**: 标识需求的发布者

#### categoryId / categoryName
- **类型**: Long / String
- **描述**: 分类ID和分类名称
- **用途**: 需求所属的分类信息

#### title
- **类型**: String
- **描述**: 需求标题
- **用途**: 需求的简短描述标题

#### requirementType
- **类型**: String
- **描述**: 需求类型（purchase-普通采购，sample-样品需求）
- **用途**: 区分不同类型的需求

#### description
- **类型**: String
- **描述**: 需求详细描述
- **用途**: 需求的详细说明

#### specification
- **类型**: String
- **描述**: 需求规格
- **用途**: 产品的技术规格要求

### 价格和时间字段

#### price
- **类型**: PriceRange
- **描述**: 价格范围对象
- **用途**: 包含最小价格、最大价格和货币单位

#### expectedDeliveryTime
- **类型**: LocalDateTime
- **描述**: 期望交付时间
- **用途**: 买家期望的交付时间

#### createdAt / updatedAt
- **类型**: LocalDateTime
- **描述**: 创建时间和更新时间
- **用途**: 记录需求的时间信息

### 状态和媒体字段

#### status
- **类型**: String
- **描述**: 需求状态
- **用途**: 需求当前的处理状态

#### images
- **类型**: List<String>
- **描述**: 图片URL列表
- **用途**: 需求相关的图片资源

#### videos
- **类型**: List<String>
- **描述**: 视频URL列表
- **用途**: 需求相关的视频资源

#### pdfUrl
- **类型**: String
- **描述**: PDF文档URL
- **用途**: 需求相关的PDF文档链接

#### attributesJson
- **类型**: String
- **描述**: 需求属性JSON
- **用途**: 存储需求的自定义属性

## 使用示例

### 需求详情查询
```java
@Service
public class RequirementService {
    
    public RequirementVO getRequirementDetail(Long requirementId) {
        // 查询需求基础信息
        PurchaseRequirement requirement = requirementMapper.selectById(requirementId);
        if (requirement == null) {
            throw new BusinessException("需求不存在");
        }
        
        // 转换为VO对象
        RequirementVO vo = convertToVO(requirement);
        
        // 补充分类信息
        if (requirement.getCategoryId() != null) {
            RequirementCategory category = categoryMapper.selectById(requirement.getCategoryId());
            if (category != null) {
                vo.setCategoryName(category.getName());
            }
        }
        
        // 解析媒体资源
        vo.setImages(parseMediaUrls(requirement.getImages()));
        vo.setVideos(parseMediaUrls(requirement.getVideos()));
        
        return vo;
    }
    
    private RequirementVO convertToVO(PurchaseRequirement requirement) {
        RequirementVO vo = new RequirementVO();
        BeanUtils.copyProperties(requirement, vo);
        
        // 设置价格范围
        if (requirement.getMinPrice() != null || requirement.getMaxPrice() != null) {
            PriceRange priceRange = new PriceRange();
            priceRange.setMinPrice(requirement.getMinPrice());
            priceRange.setMaxPrice(requirement.getMaxPrice());
            priceRange.setCurrency(requirement.getCurrency());
            vo.setPrice(priceRange);
        }
        
        return vo;
    }
    
    private List<String> parseMediaUrls(String mediaJson) {
        if (mediaJson == null || mediaJson.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(mediaJson, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            log.error("解析媒体URL失败: {}", mediaJson, e);
            return Collections.emptyList();
        }
    }
}
```

### 前端需求展示
```java
@RestController
@RequestMapping("/api/requirements")
public class RequirementController {
    
    @GetMapping("/{id}")
    public ApiResponse<RequirementVO> getRequirementDetail(@PathVariable Long id) {
        RequirementVO requirement = requirementService.getRequirementDetail(id);
        return ApiResponse.success(requirement);
    }
    
    @GetMapping
    public ApiResponse<IPage<RequirementVO>> getRequirements(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String requirementType,
            @RequestParam(required = false) String status) {
        
        IPage<RequirementVO> requirements = requirementService.getRequirements(
            page, size, categoryId, requirementType, status
        );
        
        return ApiResponse.success(requirements);
    }
}
```

### 需求属性处理
```java
public class RequirementAttributeService {
    
    public List<RequirementAttribute> parseAttributes(RequirementVO requirement) {
        String attributesJson = requirement.getAttributesJson();
        if (attributesJson == null || attributesJson.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(attributesJson, 
                new TypeReference<List<RequirementAttribute>>() {});
        } catch (Exception e) {
            log.error("解析需求属性失败: {}", attributesJson, e);
            return Collections.emptyList();
        }
    }
    
    public String formatAttributesForDisplay(RequirementVO requirement) {
        List<RequirementAttribute> attributes = parseAttributes(requirement);
        return attributes.stream()
            .map(attr -> attr.getName() + ": " + attr.getValue())
            .collect(Collectors.joining(", "));
    }
}
```

### 前端需求卡片组件
```javascript
// 需求卡片组件数据处理
const formatRequirementData = (requirement) => {
  return {
    id: requirement.id,
    title: requirement.title,
    description: requirement.description,
    categoryName: requirement.categoryName,
    requirementType: requirement.requirementType,
    priceRange: formatPriceRange(requirement.price),
    expectedDelivery: formatDate(requirement.expectedDeliveryTime),
    status: requirement.status,
    images: requirement.images || [],
    videos: requirement.videos || [],
    pdfUrl: requirement.pdfUrl,
    createdAt: formatDate(requirement.createdAt)
  };
};

// 价格范围格式化
const formatPriceRange = (price) => {
  if (!price) return '价格面议';
  
  const { minPrice, maxPrice, currency = 'USD' } = price;
  
  if (minPrice && maxPrice) {
    return `${currency} ${minPrice} - ${maxPrice}`;
  } else if (minPrice) {
    return `${currency} ${minPrice}+`;
  } else if (maxPrice) {
    return `${currency} ≤ ${maxPrice}`;
  }
  
  return '价格面议';
};
```

## 注意事项

### 数据完整性
1. **关联数据**: 确保buyerId和categoryId关联到有效记录
2. **媒体资源**: 图片、视频URL应指向有效资源
3. **价格数据**: 价格范围应符合业务逻辑
4. **属性格式**: attributesJson应为有效JSON格式

### 性能优化
1. **懒加载**: 媒体资源采用懒加载策略
2. **分页查询**: 需求列表查询使用分页
3. **缓存策略**: 热门需求数据可以缓存
4. **索引优化**: 为查询字段建立合适索引

### 前端集成
1. **媒体展示**: 支持图片轮播、视频播放等功能
2. **属性渲染**: 动态渲染需求属性
3. **状态显示**: 提供友好的状态显示
4. **响应式**: 支持移动端和桌面端展示

### 安全考虑
1. **权限控制**: 确保用户只能查看有权限的需求
2. **数据脱敏**: 敏感信息需要权限控制显示
3. **文件安全**: 媒体文件需要安全验证
4. **XSS防护**: 用户输入内容需要防XSS处理
