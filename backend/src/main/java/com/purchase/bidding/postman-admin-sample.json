{"info": {"_postman_id": "b3f6a2e1-88c1-4b1a-85d1-9f2e3d8f76d4", "name": "Purchase System - <PERSON><PERSON> Bidding", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Login successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    pm.expect(jsonData.message).to.eql(\"登录成功\");", "    pm.expect(jsonData.data.userInfo.role).to.eql(\"admin\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"credential\": \"admin\",\n    \"password\": \"123456\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/users/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "login"]}, "description": "管理员登录以获取认证Cookie。请将`credential`和`password`替换为真实的管理员凭据。\n\n**注意**: 登录后，Postman会自动保存和发送后续请求所需的`HttpOnly` Cookie，无需手动设置Token。"}, "response": []}], "description": "认证相关接口"}, {"name": "Ad<PERSON> Sample Bidding Management", "item": [{"name": "Get All Sample Biddings", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings?page=1&size=10&productName=Sample&status=pending&auditStatus=pending_audit&startDate=2024-01-01&endDate=2024-12-31", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}, {"key": "productName", "value": "<PERSON><PERSON>", "description": "按产品名称搜索"}, {"key": "company", "value": "Sample Corp", "description": "按公司名称搜索", "disabled": true}, {"key": "status", "value": "pending", "description": "竞价状态: pending, sample_accepted, rejected, cancelled"}, {"key": "auditStatus", "value": "pending_audit", "description": "审核状态: pending_audit, approved, rejected"}, {"key": "sellerId", "value": "1", "disabled": true}, {"key": "buyerId", "value": "2", "disabled": true}, {"key": "requirementId", "value": "3", "disabled": true}, {"key": "minSamplePrice", "value": "100.00", "disabled": true}, {"key": "maxSamplePrice", "value": "500.00", "disabled": true}, {"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-12-31"}]}, "description": "获取所有样品竞价列表，支持多维度筛选"}, "response": []}, {"name": "Get Pending Audit Biddings", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/pending-audit?page=1&size=5", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "pending-audit"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "5"}]}, "description": "获取待审核的样品竞价列表"}, "response": []}, {"name": "Get Approved Biddings", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/approved?page=1&size=10&status=pending", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "approved"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}, {"key": "status", "value": "pending"}]}, "description": "获取已审核通过的样品竞价列表"}, "response": []}, {"name": "Get Rejected Biddings", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/rejected?page=1&size=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "rejected"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}]}, "description": "获取已拒绝审核的样品竞价列表"}, "response": []}, {"name": "Get Bidding Detail", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/{{biddingId}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "{{biddingId}}"]}, "description": "获取单个样品竞价的详情"}, "response": []}, {"name": "<PERSON><PERSON> Bidding", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"approved\": true,\n    \"auditRemark\": \"This sample bidding looks good. Approved by admin.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/{{biddingId}}/audit", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "{{biddingId}}", "audit"]}, "description": "审核单个样品竞价"}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/batch-audit?biddingIds=1,2,3&approved=true&auditRemark=Batch approved", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "batch-audit"], "query": [{"key": "biddingIds", "value": "1,2,3", "description": "需要批量审核的竞价ID列表，用逗号分隔"}, {"key": "approved", "value": "true"}, {"key": "auditRemark", "value": "Batch approved"}]}, "description": "批量审核样品竞价"}, "response": []}, {"name": "Update Bidding Status", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"status\": \"sample_accepted\",\n    \"statusRemark\": \"Status updated to accepted by admin.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/{{biddingId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "{{biddingId}}", "status"]}, "description": "更新样品竞价状态"}, "response": []}, {"name": "Force Accept Bidding", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/{{biddingId}}/force-accept?reason=Force accepted for business reason", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "{{biddingId}}", "force-accept"], "query": [{"key": "reason", "value": "Force accepted for business reason"}]}, "description": "强制接受样品竞价"}, "response": []}, {"name": "Force Reject Bidding", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/{{biddingId}}/force-reject?reason=Force rejected due to policy change", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "{{biddingId}}", "force-reject"], "query": [{"key": "reason", "value": "Force rejected due to policy change"}]}, "description": "强制拒绝样品竞价"}, "response": []}, {"name": "Delete Bidding", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/{{biddingId}}?reason=Deleted by admin", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "{{biddingId}}"], "query": [{"key": "reason", "value": "Deleted by admin"}]}, "description": "逻辑删除样品竞价"}, "response": []}, {"name": "Batch Delete Biddings", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "[\n    1,\n    2,\n    3\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/batch-delete?reason=Batch deleted by admin", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "batch-delete"], "query": [{"key": "reason", "value": "Batch deleted by admin"}]}, "description": "批量删除样品竞价"}, "response": []}, {"name": "Get Bidding Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/stats?startDate=2024-01-01&endDate=2024-12-31", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "stats"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-12-31"}, {"key": "sellerId", "value": "1", "disabled": true}, {"key": "buyerId", "value": "2", "disabled": true}]}, "description": "获取样品竞价统计信息"}, "response": []}, {"name": "Get Bidding Trends", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/trends?startDate=2024-07-01&endDate=2024-07-31&granularity=day", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "trends"], "query": [{"key": "startDate", "value": "2024-07-01"}, {"key": "endDate", "value": "2024-07-31"}, {"key": "granularity", "value": "day", "description": "day, week, month"}]}, "description": "获取样品竞价趋势数据"}, "response": []}, {"name": "Export Bidding Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/export?status=approved&format=excel", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "export"], "query": [{"key": "status", "value": "approved"}, {"key": "auditStatus", "value": "approved", "disabled": true}, {"key": "startDate", "value": "2024-01-01", "disabled": true}, {"key": "endDate", "value": "2024-12-31", "disabled": true}, {"key": "format", "value": "excel"}]}, "description": "导出样品竞价数据"}, "response": []}, {"name": "Resend Bidding Notification", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/admin/sample-biddings/{{biddingId}}/resend-notification?notificationType=audit_approved", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "sample-biddings", "{{biddingId}}", "resend-notification"], "query": [{"key": "notificationType", "value": "audit_approved", "description": "e.g., audit_approved, status_update"}]}, "description": "重新发送样品竞价通知"}, "response": []}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string", "description": "API服务器的基础URL"}, {"key": "biddingId", "value": "1", "type": "string", "description": "用于测试单个竞价接口的ID"}]}