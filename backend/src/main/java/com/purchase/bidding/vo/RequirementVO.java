package com.purchase.bidding.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 需求详情VO对象
 */
@Data
public class RequirementVO {
    /**
     * 需求ID
     */
    private Long id;

    /**
     * 买家ID
     */
    private Long buyerId;

    /**
     * 需求标题
     */
    private String title;

    /**
     * 需求描述
     */
    private String description;

    /**
     * 价格范围
     */
    private PriceRange price;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 期望交付时间
     */
    private LocalDateTime expectedDeliveryTime;

    /**
     * 需求状态
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 图片列表
     */
    private List<String> images;

    /**
     * 价格范围
     */
    @Data
    public static class PriceRange {
        private BigDecimal min;
        private BigDecimal max;

        public static PriceRange of(BigDecimal min, BigDecimal max) {
            PriceRange range = new PriceRange();
            range.setMin(min);
            range.setMax(max);
            return range;
        }
    }
} 