# SampleBiddingServiceImpl - 样品竞价服务实现文档

## 文件概述
`SampleBiddingServiceImpl`是专门处理样品竞价业务逻辑的实现类，位于`com.purchase.bidding.service.impl`包中。该类实现了`SampleBiddingService`接口，专注于样品竞价的提交、接受、拒绝等核心业务场景，是样品竞价模块的核心服务组件。

## 核心功能

### 1. 样品竞价提交
- **需求验证**: 验证样品需求的有效性和类型匹配
- **重复检查**: 防止同一卖家对同一需求重复提交样品竞价
- **数据构建**: 构建完整的样品竞价记录
- **通知发送**: 提交成功后自动发送通知给需求发布者

### 2. 样品竞价接受
- **权限验证**: 验证买家是否有权限接受该竞价
- **状态检查**: 确保竞价处于可接受状态
- **状态更新**: 将竞价状态更新为"样品已接受"
- **通知反馈**: 向卖家发送竞价被接受的通知

### 3. 样品竞价拒绝
- **权限验证**: 验证买家是否有权限拒绝该竞价
- **状态检查**: 确保竞价处于可拒绝状态
- **状态更新**: 将竞价状态更新为"已拒绝"
- **原因记录**: 记录拒绝原因并通知卖家

### 4. 查询服务
- **需求竞价列表**: 查询指定样品需求的所有竞价记录
- **卖家竞价列表**: 查询指定卖家的所有样品竞价记录
- **状态筛选**: 支持按状态筛选竞价记录

### 5. 通知服务集成
- **提交通知**: 样品竞价提交后的通知
- **接受通知**: 竞价被接受后的通知
- **拒绝通知**: 竞价被拒绝后的通知

## 接口说明

### 竞价提交方法

#### submitSampleBidding
```java
BiddingDetailResponse submitSampleBidding(SampleBiddingSubmitRequest request, Long sellerId)
```
**功能**: 提交新的样品竞价
**业务规则**:
- 需求必须是样品类型且处于进行中状态
- 同一卖家不能对同一需求重复提交样品竞价
- 自动设置竞价类型为"sample"
- 提交成功后发送通知给需求发布者

**参数说明**:
- `request`: 样品竞价提交请求，包含样品价格、数量、规格等信息
- `sellerId`: 提交竞价的卖家用户ID

**验证流程**:
1. 验证需求存在且为样品类型
2. 验证需求状态为"in_progress"
3. 检查是否已提交过样品竞价
4. 验证卖家信息有效性

### 竞价接受方法

#### acceptSampleBidding
```java
BiddingDetailResponse acceptSampleBidding(Long biddingId, Long buyerId)
```
**功能**: 买家接受样品竞价
**业务规则**:
- 竞价必须是样品类型
- 竞价状态必须为"pending"待处理
- 只有需求所有者才能接受竞价
- 接受后状态变为"sample_accepted"

**参数说明**:
- `biddingId`: 要接受的样品竞价ID
- `buyerId`: 执行接受操作的买家用户ID

### 竞价拒绝方法

#### rejectSampleBidding
```java
BiddingDetailResponse rejectSampleBidding(Long biddingId, Long buyerId, String reason)
```
**功能**: 买家拒绝样品竞价
**业务规则**:
- 竞价必须是样品类型
- 竞价状态必须为"pending"或"sample_accepted"
- 只有需求所有者才能拒绝竞价
- 拒绝原因会追加到竞价描述中

**参数说明**:
- `biddingId`: 要拒绝的样品竞价ID
- `buyerId`: 执行拒绝操作的买家用户ID
- `reason`: 拒绝原因说明

### 查询相关方法

#### getSampleBiddingsByRequirement
```java
IPage<BiddingDetailResponse> getSampleBiddingsByRequirement(Long requirementId, Integer page, Integer size, String status)
```
**功能**: 获取指定样品需求的所有竞价记录
**参数说明**:
- `requirementId`: 样品需求ID
- `page`: 页码，从1开始
- `size`: 每页记录数
- `status`: 竞价状态筛选，可为null查询所有

#### getSampleBiddingsBySeller
```java
IPage<BiddingDetailResponse> getSampleBiddingsBySeller(Long sellerId, Integer page, Integer size, String status)
```
**功能**: 获取指定卖家的所有样品竞价记录
**用途**: 卖家查看自己提交的样品竞价历史

## 使用示例

### 提交样品竞价
```java
// 创建服务实例
SampleBiddingService sampleService = new SampleBiddingServiceImpl();

// 构建样品竞价请求
SampleBiddingSubmitRequest request = new SampleBiddingSubmitRequest();
request.setRequirementId(1001L);  // 样品需求ID
request.setSamplePrice(new BigDecimal("25.50"));  // 样品单价
request.setSampleQuantity(10);  // 样品数量
request.setSampleSpecification("标准规格，蓝色，XL号");  // 样品规格
request.setDescription("高质量样品，符合需求规格要求");

// 提交样品竞价
try {
    BiddingDetailResponse response = sampleService.submitSampleBidding(request, 2001L);
    System.out.println("样品竞价提交成功，竞价ID: " + response.getId());
    System.out.println("状态: " + response.getStatus());
} catch (BusinessException e) {
    System.out.println("提交失败: " + e.getMessage());
}
```

### 接受样品竞价
```java
// 买家接受样品竞价
Long biddingId = 12345L;
Long buyerId = 1001L;

try {
    BiddingDetailResponse accepted = sampleService.acceptSampleBidding(biddingId, buyerId);
    System.out.println("样品竞价已接受");
    System.out.println("当前状态: " + accepted.getStatus());
    
    // 获取卖家信息以便后续联系
    System.out.println("卖家: " + accepted.getSellerName());
    System.out.println("联系方式: " + accepted.getSellerPhone());
} catch (BusinessException e) {
    System.out.println("接受失败: " + e.getMessage());
}
```

### 拒绝样品竞价
```java
// 买家拒绝样品竞价并说明原因
Long biddingId = 12346L;
Long buyerId = 1001L;
String rejectReason = "样品价格超出预算，规格不符合要求";

try {
    BiddingDetailResponse rejected = sampleService.rejectSampleBidding(biddingId, buyerId, rejectReason);
    System.out.println("样品竞价已拒绝");
    System.out.println("拒绝原因: " + rejectReason);
    
    // 验证拒绝原因已记录
    System.out.println("更新后的描述: " + rejected.getDescription());
} catch (BusinessException e) {
    System.out.println("拒绝失败: " + e.getMessage());
}
```

### 查询样品竞价列表
```java
// 查询需求的样品竞价
Long requirementId = 1001L;
Integer page = 1;
Integer size = 10;

// 查询所有状态的样品竞价
IPage<BiddingDetailResponse> allBiddings = 
    sampleService.getSampleBiddingsByRequirement(requirementId, page, size, null);

// 查询待处理的样品竞价
IPage<BiddingDetailResponse> pendingBiddings = 
    sampleService.getSampleBiddingsByRequirement(requirementId, page, size, "pending");

// 遍历结果
for (BiddingDetailResponse bidding : allBiddings.getRecords()) {
    System.out.println("竞价ID: " + bidding.getId());
    System.out.println("卖家: " + bidding.getSellerName());
    System.out.println("样品价格: " + bidding.getSamplePrice());
    System.out.println("样品数量: " + bidding.getSampleQuantity());
    System.out.println("状态: " + bidding.getStatus());
    System.out.println("---");
}
```

### 查询卖家的样品竞价历史
```java
// 查询卖家的所有样品竞价
Long sellerId = 2001L;
IPage<BiddingDetailResponse> sellerBiddings = 
    sampleService.getSampleBiddingsBySeller(sellerId, 1, 20, null);

// 统计信息
System.out.println("总记录数: " + sellerBiddings.getTotal());
System.out.println("总页数: " + sellerBiddings.getPages());

// 按状态分组统计
long pendingCount = sellerBiddings.getRecords().stream()
    .filter(b -> "pending".equals(b.getStatus()))
    .count();
long acceptedCount = sellerBiddings.getRecords().stream()
    .filter(b -> "sample_accepted".equals(b.getStatus()))
    .count();

System.out.println("待处理: " + pendingCount);
System.out.println("已接受: " + acceptedCount);
```

## 注意事项

### 权限控制
1. **卖家权限**: 只有卖家才能提交样品竞价
2. **买家权限**: 只有需求所有者才能接受或拒绝竞价
3. **数据隔离**: 用户只能操作自己有权限的竞价数据

### 业务规则
1. **需求类型**: 必须是样品类型需求才能提交样品竞价
2. **需求状态**: 需求必须处于"in_progress"状态
3. **重复提交**: 同一卖家不能对同一需求重复提交
4. **状态流转**: 严格遵循状态流转规则

### 性能优化
1. **分页查询**: 所有查询都支持分页，避免内存溢出
2. **索引优化**: 关键查询字段已建立数据库索引
3. **事务管理**: 关键操作使用@Transactional注解

### 数据安全
1. **输入验证**: 对所有输入参数进行严格验证
2. **权限检查**: 每个操作都进行权限验证
3. **数据一致性**: 确保竞价数据与需求数据保持一致

### 错误处理
1. **参数验证**: 无效参数抛出BusinessException
2. **权限异常**: 无权限操作返回403错误
3. **业务异常**: 违反业务规则返回400错误
4. **数据异常**: 数据不存在返回404错误

### 扩展说明
1. **通知扩展**: 支持扩展通知渠道
2. **查询扩展**: 支持自定义查询条件和排序规则
3. **业务规则扩展**: 支持自定义竞价规则
4. **状态扩展**: 支持添加新的竞价状态

---

**文件路径**: `backend/src/main/java/com/purchase/bidding/service/impl/SampleBiddingServiceImpl.java`

**最后更新**: 2025-01-01
