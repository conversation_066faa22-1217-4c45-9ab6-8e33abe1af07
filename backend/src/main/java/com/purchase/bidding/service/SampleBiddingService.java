package com.purchase.bidding.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.bidding.dto.request.SampleBiddingSubmitRequest;
import com.purchase.bidding.dto.response.BiddingDetailResponse;

import java.util.List;

/**
 * 样品竞价服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface SampleBiddingService {
    
    /**
     * 提交样品竞价
     * 
     * @param request 样品竞价请求
     * @param sellerId 卖家ID
     * @return 竞价详情响应
     */
    BiddingDetailResponse submitSampleBidding(SampleBiddingSubmitRequest request, Long sellerId);
    
    /**
     * 接受样品竞价（买家可以接受多个）
     * 
     * @param biddingId 竞价ID
     * @param buyerId 买家ID
     * @return 竞价详情响应
     */
    BiddingDetailResponse acceptSampleBidding(Long biddingId, Long buyerId);
    
    /**
     * 拒绝样品竞价
     * 
     * @param biddingId 竞价ID
     * @param buyerId 买家ID
     * @param reason 拒绝原因
     * @return 竞价详情响应
     */
    BiddingDetailResponse rejectSampleBidding(Long biddingId, Long buyerId, String reason);
    
    /**
     * 获取样品需求的竞价列表
     * 只返回审核状态为approved的竞价数据，仅买家和卖家可访问
     * 
     * @param requirementId 需求ID，必须大于0
     * @param page 页码，默认为1
     * @param size 每页大小，默认为10
     * @param status 竞价状态（可选）：PENDING、SAMPLE_ACCEPTED、REJECTED、CANCELLED
     * @return 竞价分页列表，包含分页信息和竞价详情
     * @throws BusinessException 当需求ID无效时抛出异常
     */
    IPage<BiddingDetailResponse> getSampleBiddingsByRequirement(Long requirementId, Integer page, Integer size, String status);
    
    /**
     * 获取样品需求的竞价列表（别名方法，保持兼容性）
     * 
     * @param requirementId 需求ID
     * @param page 页码
     * @param size 每页大小
     * @param status 竞价状态（可选）
     * @return 竞价分页列表
     */
    IPage<BiddingDetailResponse> getSampleBiddingList(Long requirementId, Integer page, Integer size, String status);
    
    /**
     * 获取已接受的样品竞价列表
     * 
     * @param requirementId 需求ID
     * @return 已接受的竞价列表
     */
    List<BiddingDetailResponse> getAcceptedSampleBiddings(Long requirementId);
    
    /**
     * 卖家获取自己的样品竞价列表
     * 
     * @param sellerId 卖家ID
     * @param page 页码
     * @param size 每页大小
     * @param status 竞价状态（可选）
     * @return 竞价分页列表
     */
    IPage<BiddingDetailResponse> getSellerSampleBiddings(Long sellerId, Integer page, Integer size, String status);
    
    /**
     * 获取样品竞价详情
     * 
     * @param biddingId 竞价ID
     * @return 竞价详情
     */
    BiddingDetailResponse getSampleBiddingDetail(Long biddingId);
    
    /**
     * 修改样品竞价（仅在pending状态下允许）
     * 
     * @param biddingId 竞价ID
     * @param request 修改请求
     * @param sellerId 卖家ID
     * @return 修改后的竞价详情
     */
    BiddingDetailResponse updateSampleBidding(Long biddingId, SampleBiddingSubmitRequest request, Long sellerId);
    
    /**
     * 取消样品竞价
     * 
     * @param biddingId 竞价ID
     * @param sellerId 卖家ID
     * @param reason 取消原因
     * @return 取消后的竞价详情
     */
    BiddingDetailResponse cancelSampleBidding(Long biddingId, Long sellerId, String reason);
    
    /**
     * 批量接受样品竞价
     * 
     * @param biddingIds 竞价ID列表
     * @param buyerId 买家ID
     * @return 接受的竞价详情列表
     */
    List<BiddingDetailResponse> batchAcceptSampleBiddings(List<Long> biddingIds, Long buyerId);
    
    /**
     * 验证竞价是否属于指定需求且为样品类型
     * 
     * @param biddingId 竞价ID
     * @param requirementId 需求ID
     * @return 是否有效
     */
    boolean validateSampleBidding(Long biddingId, Long requirementId);
} 