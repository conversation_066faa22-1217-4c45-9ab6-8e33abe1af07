package com.purchase.bidding.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.purchase.bidding.dto.response.BiddingDetailResponse;
import com.purchase.bidding.dto.response.AdminSampleBiddingStatsResponse;
import com.purchase.bidding.entity.BiddingRecord;
import com.purchase.bidding.mapper.BiddingRecordMapper;
import com.purchase.bidding.service.AdminSampleBiddingService;
import com.purchase.common.exception.BusinessException;
import com.purchase.common.util.SecurityContextUtil;
import com.purchase.requirement.entity.PurchaseRequirement;
import com.purchase.requirement.mapper.PurchaseRequirementMapper;
import com.purchase.user.entity.User;
import com.purchase.user.mapper.UserMapper;
import com.purchase.message.notification.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 管理员样品竞价服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
public class AdminSampleBiddingServiceImpl implements AdminSampleBiddingService {
    
    @Resource
    private BiddingRecordMapper biddingRecordMapper;
    
    @Resource
    private PurchaseRequirementMapper purchaseRequirementMapper;
    
    @Resource
    private UserMapper userMapper;
    
    @Resource
    private NotificationService notificationService;
    
    @Override
    public IPage<BiddingDetailResponse> getAllSampleBiddings(Integer page, Integer size, String status, 
                                                           String auditStatus, Long sellerId, Long buyerId, 
                                                           Long requirementId, BigDecimal minSamplePrice, 
                                                           BigDecimal maxSamplePrice, String productName, 
                                                           String company, LocalDate startDate, LocalDate endDate) {
        
        // 验证管理员权限
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以查看所有样品竞价");
        }
        
        Page<BiddingRecord> pageObj = new Page<>(page, size);
        LambdaQueryWrapper<BiddingRecord> queryWrapper = new LambdaQueryWrapper<>();
        
        // 基础条件：只查询样品竞价且未删除
        queryWrapper.eq(BiddingRecord::getBiddingType, BiddingRecord.BiddingType.SAMPLE)
                   .eq(BiddingRecord::getDeleted, "0");
        
        // 动态查询条件
        queryWrapper.eq(StringUtils.hasText(status), BiddingRecord::getStatus, status)
                   .eq(StringUtils.hasText(auditStatus), BiddingRecord::getAuditStatus, auditStatus)
                   .eq(sellerId != null, BiddingRecord::getSellerId, sellerId)
                   .eq(requirementId != null, BiddingRecord::getRequirementId, requirementId)
                   .ge(minSamplePrice != null, BiddingRecord::getSamplePrice, minSamplePrice)
                   .le(maxSamplePrice != null, BiddingRecord::getSamplePrice, maxSamplePrice)
                   .like(StringUtils.hasText(productName), BiddingRecord::getProductName, productName)
                   .like(StringUtils.hasText(company), BiddingRecord::getCompany, company);
        
        // 安全地处理日期范围
        if (startDate != null) {
            queryWrapper.ge(BiddingRecord::getCreatedAt, startDate.atStartOfDay());
        }
        if (endDate != null) {
            queryWrapper.le(BiddingRecord::getCreatedAt, endDate.atTime(23, 59, 59));
        }
        
        // 买家ID条件需要通过需求表关联查询
        if (buyerId != null) {
            List<Long> requirementIds = purchaseRequirementMapper.selectList(
                new LambdaQueryWrapper<PurchaseRequirement>()
                    .eq(PurchaseRequirement::getBuyerId, buyerId)
                    .select(PurchaseRequirement::getId)
            ).stream().map(PurchaseRequirement::getId).collect(Collectors.toList());
            
            if (!requirementIds.isEmpty()) {
                queryWrapper.in(BiddingRecord::getRequirementId, requirementIds);
            } else {
                // 如果买家没有任何需求，返回空结果
                return new Page<BiddingDetailResponse>(page, size);
            }
        }
        
        queryWrapper.orderByDesc(BiddingRecord::getCreatedAt);
        
        IPage<BiddingRecord> recordPage = biddingRecordMapper.selectPage(pageObj, queryWrapper);
        
        return recordPage.convert(this::convertToDetailResponse);
    }
    
    @Override
    public IPage<BiddingDetailResponse> getPendingAuditSampleBiddings(Integer page, Integer size) {
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以查看待审核样品竞价");
        }
        
        Page<BiddingRecord> pageObj = new Page<>(page, size);
        LambdaQueryWrapper<BiddingRecord> queryWrapper = new LambdaQueryWrapper<>();
        
        queryWrapper.eq(BiddingRecord::getBiddingType, BiddingRecord.BiddingType.SAMPLE)
                   .eq(BiddingRecord::getAuditStatus, "pending_audit")
                   .eq(BiddingRecord::getDeleted, "0")
                   .orderByAsc(BiddingRecord::getCreatedAt);
        
        IPage<BiddingRecord> recordPage = biddingRecordMapper.selectPage(pageObj, queryWrapper);
        
        return recordPage.convert(this::convertToDetailResponse);
    }
    
    @Override
    public IPage<BiddingDetailResponse> getApprovedSampleBiddings(Integer page, Integer size, String status) {
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以查看已审核通过样品竞价");
        }
        
        Page<BiddingRecord> pageObj = new Page<>(page, size);
        LambdaQueryWrapper<BiddingRecord> queryWrapper = new LambdaQueryWrapper<>();
        
        queryWrapper.eq(BiddingRecord::getBiddingType, BiddingRecord.BiddingType.SAMPLE)
                   .eq(BiddingRecord::getAuditStatus, "approved")
                   .eq(BiddingRecord::getDeleted, "0")
                   .eq(StringUtils.hasText(status), BiddingRecord::getStatus, status)
                   .orderByDesc(BiddingRecord::getCreatedAt);
        
        IPage<BiddingRecord> recordPage = biddingRecordMapper.selectPage(pageObj, queryWrapper);
        
        return recordPage.convert(this::convertToDetailResponse);
    }
    
    @Override
    public IPage<BiddingDetailResponse> getRejectedSampleBiddings(Integer page, Integer size) {
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以查看已拒绝审核样品竞价");
        }
        
        Page<BiddingRecord> pageObj = new Page<>(page, size);
        LambdaQueryWrapper<BiddingRecord> queryWrapper = new LambdaQueryWrapper<>();
        
        queryWrapper.eq(BiddingRecord::getBiddingType, BiddingRecord.BiddingType.SAMPLE)
                   .eq(BiddingRecord::getAuditStatus, "rejected")
                   .eq(BiddingRecord::getDeleted, "0")
                   .orderByDesc(BiddingRecord::getCreatedAt);
        
        IPage<BiddingRecord> recordPage = biddingRecordMapper.selectPage(pageObj, queryWrapper);
        
        return recordPage.convert(this::convertToDetailResponse);
    }
    
    @Override
    public BiddingDetailResponse getSampleBiddingDetail(Long biddingId) {
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以查看样品竞价详情");
        }
        
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(biddingId);
        if (biddingRecord == null) {
            throw new BusinessException("样品竞价记录不存在");
        }
        
        if (!BiddingRecord.BiddingType.SAMPLE.equals(biddingRecord.getBiddingType())) {
            throw new BusinessException("该竞价不是样品竞价");
        }
        
        return convertToDetailResponse(biddingRecord);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BiddingDetailResponse auditSampleBidding(Long biddingId, Boolean approved, String auditRemark) {
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以审核样品竞价");
        }
        
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(biddingId);
        if (biddingRecord == null) {
            throw new BusinessException("样品竞价记录不存在");
        }
        
        if (!BiddingRecord.BiddingType.SAMPLE.equals(biddingRecord.getBiddingType())) {
            throw new BusinessException("该竞价不是样品竞价");
        }
        
        if (!"pending_audit".equals(biddingRecord.getAuditStatus())) {
            throw new BusinessException("只能审核待审核状态的样品竞价");
        }
        
        // 更新审核状态
        biddingRecord.setAuditStatus(approved ? "approved" : "rejected");
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        
        // 设置审核备注，使用规范格式
        if (StringUtils.hasText(auditRemark)) {
            String currentTime = LocalDateTime.now().toString();
            String newRemark = String.format("[%s] 管理员审核: %s, 备注: %s", 
                                currentTime, approved ? "通过" : "拒绝", auditRemark);
            
            // 如果已有备注，则追加新备注，否则直接设置
            if (StringUtils.hasText(biddingRecord.getAuditRemark())) {
                biddingRecord.setAuditRemark(biddingRecord.getAuditRemark() + "\n" + newRemark);
            } else {
                biddingRecord.setAuditRemark(newRemark);
            }
        }
        
        biddingRecordMapper.updateById(biddingRecord);
        
        // 发送审核通知
        sendAuditNotification(biddingRecord, approved);
        
        log.info("管理员审核样品竞价完成，竞价ID: {}, 审核结果: {}", biddingId, approved ? "通过" : "拒绝");
        
        return convertToDetailResponse(biddingRecord);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<BiddingDetailResponse> batchAuditSampleBiddings(List<Long> biddingIds, Boolean approved, String auditRemark) {
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以批量审核样品竞价");
        }
        
        if (biddingIds == null || biddingIds.isEmpty()) {
            throw new BusinessException("请选择要审核的样品竞价");
        }
        
        return biddingIds.stream()
                .map(biddingId -> auditSampleBidding(biddingId, approved, auditRemark))
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BiddingDetailResponse updateSampleBiddingStatus(Long biddingId, String status, String statusRemark) {
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以更新样品竞价状态");
        }
        
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(biddingId);
        if (biddingRecord == null) {
            throw new BusinessException("样品竞价记录不存在");
        }
        
        if (!BiddingRecord.BiddingType.SAMPLE.equals(biddingRecord.getBiddingType())) {
            throw new BusinessException("该竞价不是样品竞价");
        }
        
        // 验证状态值
        if (!isValidBiddingStatus(status)) {
            throw new BusinessException("无效的竞价状态");
        }
        
        String oldStatus = biddingRecord.getStatus();
        biddingRecord.setStatus(status);
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        
        // 使用auditRemark字段存储管理员备注，而不是追加到description中
        if (StringUtils.hasText(statusRemark)) {
            String currentTime = LocalDateTime.now().toString();
            String newRemark = String.format("[%s] 管理员状态更新: %s -> %s, 备注: %s", 
                                currentTime, oldStatus, status, statusRemark);
            
            // 如果已有备注，则追加新备注，否则直接设置
            if (StringUtils.hasText(biddingRecord.getAuditRemark())) {
                biddingRecord.setAuditRemark(biddingRecord.getAuditRemark() + "\n" + newRemark);
            } else {
                biddingRecord.setAuditRemark(newRemark);
            }
        }
        
        biddingRecordMapper.updateById(biddingRecord);
        
        // 发送状态更新通知
        sendStatusUpdateNotification(biddingRecord, oldStatus, status);
        
        log.info("管理员更新样品竞价状态，竞价ID: {}, 旧状态: {}, 新状态: {}", biddingId, oldStatus, status);
        
        return convertToDetailResponse(biddingRecord);
    }
    
    // 其他方法的实现将在下一部分继续...
    
    /**
     * 转换为详情响应DTO
     */
    private BiddingDetailResponse convertToDetailResponse(BiddingRecord biddingRecord) {
        BiddingDetailResponse response = new BiddingDetailResponse();
        BeanUtils.copyProperties(biddingRecord, response);
        
        // 转换图片和视频URL列表
        if (StringUtils.hasText(biddingRecord.getImages())) {
            response.setImages(Arrays.asList(biddingRecord.getImages().split(",")));
        }
        if (StringUtils.hasText(biddingRecord.getVideos())) {
            response.setVideos(Arrays.asList(biddingRecord.getVideos().split(",")));
        }
        if (StringUtils.hasText(biddingRecord.getCompanyEnvironmentImages())) {
            response.setCompanyEnvironmentImages(Arrays.asList(biddingRecord.getCompanyEnvironmentImages().split(",")));
        }
        
        return response;
    }
    
    /**
     * 验证竞价状态是否有效
     */
    private boolean isValidBiddingStatus(String status) {
        return BiddingRecord.BiddingStatus.PENDING.equals(status) ||
               BiddingRecord.BiddingStatus.ACCEPTED.equals(status) ||
               BiddingRecord.BiddingStatus.REJECTED.equals(status) ||
               BiddingRecord.BiddingStatus.CANCELLED.equals(status) ||
               BiddingRecord.BiddingStatus.SAMPLE_ACCEPTED.equals(status);
    }
    
    /**
     * 发送审核通知
     */
    private void sendAuditNotification(BiddingRecord biddingRecord, Boolean approved) {
        // 实现审核通知逻辑
        log.info("发送样品竞价审核通知，竞价ID: {}, 审核结果: {}", biddingRecord.getId(), approved ? "通过" : "拒绝");
    }
    
    /**
     * 发送状态更新通知
     */
    private void sendStatusUpdateNotification(BiddingRecord biddingRecord, String oldStatus, String newStatus) {
        // 实现状态更新通知逻辑
        log.info("发送样品竞价状态更新通知，竞价ID: {}, 旧状态: {}, 新状态: {}", 
                biddingRecord.getId(), oldStatus, newStatus);
    }
    
    // 暂时提供简单实现，其他复杂方法可以后续完善
    @Override
    public BiddingDetailResponse forceAcceptSampleBidding(Long biddingId, String reason) {
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以强制接受样品竞价");
        }
        
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(biddingId);
        if (biddingRecord == null) {
            throw new BusinessException("样品竞价记录不存在");
        }
        
        if (!BiddingRecord.BiddingType.SAMPLE.equals(biddingRecord.getBiddingType())) {
            throw new BusinessException("该竞价不是样品竞价");
        }
        
        String oldStatus = biddingRecord.getStatus();
        biddingRecord.setStatus(BiddingRecord.BiddingStatus.SAMPLE_ACCEPTED);
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        
        // 使用auditRemark字段存储强制接受的原因
        if (StringUtils.hasText(reason)) {
            String currentTime = LocalDateTime.now().toString();
            String newRemark = String.format("[%s] 管理员强制接受: %s", currentTime, reason);
            
            if (StringUtils.hasText(biddingRecord.getAuditRemark())) {
                biddingRecord.setAuditRemark(biddingRecord.getAuditRemark() + "\n" + newRemark);
            } else {
                biddingRecord.setAuditRemark(newRemark);
            }
        }
        
        biddingRecordMapper.updateById(biddingRecord);
        
        // 发送通知
        sendStatusUpdateNotification(biddingRecord, oldStatus, BiddingRecord.BiddingStatus.SAMPLE_ACCEPTED);
        
        log.info("管理员强制接受样品竞价，竞价ID: {}, 原因: {}", biddingId, reason);
        
        return convertToDetailResponse(biddingRecord);
    }
    
    @Override
    public BiddingDetailResponse forceRejectSampleBidding(Long biddingId, String reason) {
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以强制拒绝样品竞价");
        }
        
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(biddingId);
        if (biddingRecord == null) {
            throw new BusinessException("样品竞价记录不存在");
        }
        
        if (!BiddingRecord.BiddingType.SAMPLE.equals(biddingRecord.getBiddingType())) {
            throw new BusinessException("该竞价不是样品竞价");
        }
        
        String oldStatus = biddingRecord.getStatus();
        biddingRecord.setStatus(BiddingRecord.BiddingStatus.REJECTED);
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        
        // 使用auditRemark字段存储强制拒绝的原因
        if (StringUtils.hasText(reason)) {
            String currentTime = LocalDateTime.now().toString();
            String newRemark = String.format("[%s] 管理员强制拒绝: %s", currentTime, reason);
            
            if (StringUtils.hasText(biddingRecord.getAuditRemark())) {
                biddingRecord.setAuditRemark(biddingRecord.getAuditRemark() + "\n" + newRemark);
            } else {
                biddingRecord.setAuditRemark(newRemark);
            }
        }
        
        biddingRecordMapper.updateById(biddingRecord);
        
        // 发送通知
        sendStatusUpdateNotification(biddingRecord, oldStatus, BiddingRecord.BiddingStatus.REJECTED);
        
        log.info("管理员强制拒绝样品竞价，竞价ID: {}, 原因: {}", biddingId, reason);
        
        return convertToDetailResponse(biddingRecord);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSampleBidding(Long biddingId, String reason) {
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以删除样品竞价");
        }
        
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(biddingId);
        if (biddingRecord == null) {
            throw new BusinessException("样品竞价记录不存在");
        }
        
        if (!BiddingRecord.BiddingType.SAMPLE.equals(biddingRecord.getBiddingType())) {
            throw new BusinessException("该竞价不是样品竞价");
        }
        
        // 逻辑删除
        biddingRecord.setDeleted("1");
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        
        // 使用auditRemark字段存储删除原因，而不是追加到description中
        if (StringUtils.hasText(reason)) {
            String currentTime = LocalDateTime.now().toString();
            String newRemark = String.format("[%s] 管理员删除原因: %s", currentTime, reason);
            
            if (StringUtils.hasText(biddingRecord.getAuditRemark())) {
                biddingRecord.setAuditRemark(biddingRecord.getAuditRemark() + "\n" + newRemark);
            } else {
                biddingRecord.setAuditRemark(newRemark);
            }
        }
        
        biddingRecordMapper.updateById(biddingRecord);
        
        log.info("管理员删除样品竞价，竞价ID: {}", biddingId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteSampleBiddings(List<Long> biddingIds, String reason) {
        if (biddingIds == null || biddingIds.isEmpty()) {
            throw new BusinessException("请选择要删除的样品竞价");
        }
        
        biddingIds.forEach(biddingId -> deleteSampleBidding(biddingId, reason));
    }
    
    // 其他复杂方法的简单实现，可以后续完善
    @Override
    public AdminSampleBiddingStatsResponse getSampleBiddingStats(LocalDate startDate, LocalDate endDate, 
                                                               Long sellerId, Long buyerId) {
        // 简单实现，返回基础统计信息
        AdminSampleBiddingStatsResponse stats = new AdminSampleBiddingStatsResponse();
        stats.setTotalCount(0L);
        stats.setPendingAuditCount(0L);
        stats.setApprovedCount(0L);
        stats.setRejectedCount(0L);
        return stats;
    }
    
    @Override
    public List<AdminSampleBiddingStatsResponse.TrendData> getSampleBiddingTrends(LocalDate startDate, 
                                                                                 LocalDate endDate, 
                                                                                 String granularity) {
        // 简单实现，返回空列表
        return List.of();
    }
    
    @Override
    public String exportSampleBiddings(String status, String auditStatus, LocalDate startDate, 
                                      LocalDate endDate, String format) {
        // 简单实现，返回模拟下载链接
        return "/downloads/sample-biddings-export.xlsx";
    }
    
    @Override
    public void resendSampleBiddingNotification(Long biddingId, String notificationType) {
        // 简单实现，记录日志
        log.info("重新发送样品竞价通知，竞价ID: {}, 通知类型: {}", biddingId, notificationType);
    }
} 