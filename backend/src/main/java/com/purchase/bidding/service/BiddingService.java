package com.purchase.bidding.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.purchase.bidding.dto.request.BiddingSubmitRequest;
import com.purchase.bidding.dto.request.BiddingUpdateRequest;
import com.purchase.bidding.dto.response.BiddingDetailResponse;
import com.purchase.bidding.dto.response.BiddingStatsResponse;
import com.purchase.bidding.vo.RequirementVO;
import com.purchase.bidding.entity.BiddingRecord;

import java.math.BigDecimal;

public interface BiddingService {
    /**
     * 提交竞价
     */
    BiddingDetailResponse submitBidding(BiddingSubmitRequest request);
    
    /**
     * 修改竞价
     */
    BiddingDetailResponse updateBidding(Long id, BiddingUpdateRequest request);
    
    /**
     * 取消竞价
     */
    void cancelBidding(Long id, Long sellerId);
    
    /**
     * 获取竞价列表
     */
    IPage<BiddingDetailResponse> getBiddingList(Long requirementId, Long buyerId, Long sellerId, String status,
                                                BigDecimal minPrice, BigDecimal maxPrice, Integer page, Integer size);
    
    /**
     * 选择中标者
     */
    BiddingDetailResponse acceptBidding(Long id, Long buyerId);
    
    /**
     * 拒绝竞价
     */
    void rejectBidding(Long id, Long buyerId, String reason);
    
    /**
     * 获取竞价详情
     */
    BiddingDetailResponse getBiddingDetail(Long id);
    
    /**
     * 获取竞价统计信息
     */
    BiddingStatsResponse getBiddingStats(Long requirementId);

    /**
     * 获取当前卖家参与过竞价的所有需求
     */
    IPage<RequirementVO> getMyBiddingRequirements(Page<BiddingRecord> page);

    /**
     * 管理员更新竞价状态
     */
    BiddingDetailResponse updateBiddingStatus(Long id, String status);

    /**
     * 管理员删除竞价
     */
    void deleteBidding(Long id);

    /**
     * 获取当前用户对特定需求的竞价记录
     * @param requirementId 需求ID
     * @param sellerId 卖家ID
     * @return 竞价详情
     */
    BiddingDetailResponse getSellerBiddingForRequirement(Long requirementId, Long sellerId);

    /**
     * 管理员获取所有竞价记录列表
     * 
     * @param auditStatus 竞价状态筛选(可选)
     * @param sellerId 卖家ID筛选(可选)
     * @param buyerId 买家ID筛选(可选)
     * @param minPrice 最低价格筛选(可选)
     * @param maxPrice 最高价格筛选(可选)
     * @param biddingType 竞价类型筛选(可选)
     * @param page 当前页码
     * @param size 每页大小
     * @return 竞价详情分页列表
     */
    IPage<BiddingDetailResponse> getAllBiddings(String auditStatus, Long sellerId, Long buyerId,
                                              BigDecimal minPrice, BigDecimal maxPrice, String biddingType,
                                              Integer page, Integer size);

    /**
     * 管理员审核竞价
     * 
     * @param biddingId 竞价ID
     * @param approved 是否审核通过
     * @param remark 审核备注
     * @return 审核后的竞价详情
     */
    BiddingDetailResponse auditBidding(Long biddingId, boolean approved, String remark);

    /**
     * 获取待审核的竞价列表
     * 
     * @param page 当前页码
     * @param size 每页大小
     * @return 待审核竞价分页列表
     */
    IPage<BiddingDetailResponse> getPendingAuditBiddings(Integer page, Integer size);

    /**
     * 获取可见的竞价列表（审核通过的竞价和自己的竞价）
     *
     * @param requirementId 需求ID
     * @return 可见的竞价列表
     */
    IPage<BiddingDetailResponse> getVisibleBiddingsByRequirementId(Long requirementId, Integer page, Integer size);

    /**
     * 验证需求归属权限
     * 确保买家只能操作自己的需求
     *
     * @param requirementId 需求ID
     * @param buyerId 买家ID
     * @throws BusinessException 如果需求不属于该买家
     */
    void validateRequirementOwnership(Long requirementId, Long buyerId);

    /**
     * 验证竞价归属权限
     * 确保买家只能操作自己需求下的竞价
     *
     * @param biddingId 竞价ID
     * @param buyerId 买家ID
     * @throws BusinessException 如果竞价不属于该买家的需求
     */
    void validateBiddingOwnership(Long biddingId, Long buyerId);
}