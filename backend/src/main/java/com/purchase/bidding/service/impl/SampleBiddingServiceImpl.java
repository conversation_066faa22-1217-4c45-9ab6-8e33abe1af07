package com.purchase.bidding.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.purchase.bidding.dto.request.SampleBiddingSubmitRequest;
import com.purchase.bidding.dto.response.BiddingDetailResponse;
import com.purchase.bidding.entity.BiddingRecord;
import com.purchase.bidding.mapper.BiddingRecordMapper;
import com.purchase.bidding.service.SampleBiddingService;
import com.purchase.common.exception.BusinessException;
import com.purchase.requirement.entity.PurchaseRequirement;
import com.purchase.requirement.mapper.PurchaseRequirementMapper;
import com.purchase.user.entity.User;
import com.purchase.user.mapper.UserMapper;
import com.purchase.message.notification.service.NotificationService;
import com.purchase.message.notification.enums.NotificationType;
import com.purchase.message.notification.entity.Notification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 样品竞价服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
public class SampleBiddingServiceImpl implements SampleBiddingService {
    
    @Resource
    private BiddingRecordMapper biddingRecordMapper;
    
    @Resource
    private PurchaseRequirementMapper purchaseRequirementMapper;
    
    @Resource
    private UserMapper userMapper;
    
    @Resource
    private NotificationService notificationService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BiddingDetailResponse submitSampleBidding(SampleBiddingSubmitRequest request, Long sellerId) {
        log.info("提交样品竞价，卖家ID: {}, 需求ID: {}", sellerId, request.getRequirementId());
        
        // 验证需求是否存在且为样品类型
        PurchaseRequirement requirement = purchaseRequirementMapper.selectById(request.getRequirementId());
        if (requirement == null) {
            throw new BusinessException("需求不存在");
        }
        if (!PurchaseRequirement.RequirementType.SAMPLE.equals(requirement.getRequirementType())) {
            throw new BusinessException("该需求不是样品需求");
        }
        if (!"in_progress".equals(requirement.getStatus())) {
            throw new BusinessException("需求已关闭，无法竞价");
        }
        
        // 检查卖家是否已经对该需求提交过样品竞价
        LambdaQueryWrapper<BiddingRecord> existingQuery = new LambdaQueryWrapper<>();
        existingQuery.eq(BiddingRecord::getRequirementId, request.getRequirementId())
                    .eq(BiddingRecord::getSellerId, sellerId)
                    .eq(BiddingRecord::getBiddingType, BiddingRecord.BiddingType.SAMPLE)
                    .eq(BiddingRecord::getDeleted, "0");
        
        BiddingRecord existingBidding = biddingRecordMapper.selectOne(existingQuery);
        if (existingBidding != null) {
            throw new BusinessException("您已对该样品需求提交过竞价，请勿重复提交");
        }
        
        // 获取卖家信息
        User seller = userMapper.selectById(sellerId);
        if (seller == null) {
            throw new BusinessException("卖家信息不存在");
        }
        
        // 构建样品竞价记录
        BiddingRecord biddingRecord = buildSampleBiddingRecord(request, sellerId, seller);
        biddingRecordMapper.insert(biddingRecord);
        
        // 发送通知
        sendSampleBiddingSubmitNotifications(biddingRecord, requirement);
        
        log.info("样品竞价提交成功，竞价ID: {}", biddingRecord.getId());
        
        return convertToDetailResponse(biddingRecord, false);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BiddingDetailResponse acceptSampleBidding(Long biddingId, Long buyerId) {
        log.info("接受样品竞价，竞价ID: {}, 买家ID: {}", biddingId, buyerId);
        
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(biddingId);
        if (biddingRecord == null) {
            throw new BusinessException("竞价记录不存在");
        }
        
        // 验证竞价类型
        if (!BiddingRecord.BiddingType.SAMPLE.equals(biddingRecord.getBiddingType())) {
            throw new BusinessException("该竞价不是样品竞价");
        }
        
        // 验证竞价状态
        if (!BiddingRecord.BiddingStatus.PENDING.equals(biddingRecord.getStatus())) {
            throw new BusinessException("竞价状态不允许接受");
        }
        
        // 验证买家权限
        PurchaseRequirement requirement = purchaseRequirementMapper.selectById(biddingRecord.getRequirementId());
        if (requirement == null || !buyerId.equals(requirement.getBuyerId())) {
            throw new BusinessException("无权限操作该竞价");
        }
        
        // 更新竞价状态为已接受
        biddingRecord.setStatus(BiddingRecord.BiddingStatus.SAMPLE_ACCEPTED);
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        biddingRecordMapper.updateById(biddingRecord);
        
        // 发送通知给卖家
        sendSampleBiddingAcceptedNotification(biddingRecord);
        
        log.info("样品竞价接受成功，竞价ID: {}", biddingId);
        
        return convertToDetailResponse(biddingRecord, false);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BiddingDetailResponse rejectSampleBidding(Long biddingId, Long buyerId, String reason) {
        log.info("拒绝样品竞价，竞价ID: {}, 买家ID: {}", biddingId, buyerId);
        
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(biddingId);
        if (biddingRecord == null) {
            throw new BusinessException("竞价记录不存在");
        }
        
        // 验证竞价类型
        if (!BiddingRecord.BiddingType.SAMPLE.equals(biddingRecord.getBiddingType())) {
            throw new BusinessException("该竞价不是样品竞价");
        }
        
        // 验证竞价状态
        if (!BiddingRecord.BiddingStatus.PENDING.equals(biddingRecord.getStatus()) &&
            !BiddingRecord.BiddingStatus.SAMPLE_ACCEPTED.equals(biddingRecord.getStatus())) {
            throw new BusinessException("竞价状态不允许拒绝");
        }
        
        // 验证买家权限
        PurchaseRequirement requirement = purchaseRequirementMapper.selectById(biddingRecord.getRequirementId());
        if (requirement == null || !buyerId.equals(requirement.getBuyerId())) {
            throw new BusinessException("无权限操作该竞价");
        }
        
        // 更新竞价状态为已拒绝
        biddingRecord.setStatus(BiddingRecord.BiddingStatus.REJECTED);
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        
        // 添加拒绝原因到描述中
        if (StringUtils.hasText(reason)) {
            String currentDescription = biddingRecord.getDescription();
            String rejectRemark = "\n拒绝原因：" + reason;
            biddingRecord.setDescription(StringUtils.hasText(currentDescription) 
                ? currentDescription + rejectRemark : rejectRemark);
        }
        
        biddingRecordMapper.updateById(biddingRecord);
        
        // 发送通知给卖家
        sendSampleBiddingRejectedNotification(biddingRecord, reason);
        
        log.info("样品竞价拒绝成功，竞价ID: {}", biddingId);
        
        return convertToDetailResponse(biddingRecord, false);
    }
    
    @Override
    public IPage<BiddingDetailResponse> getSampleBiddingsByRequirement(Long requirementId, Integer page, Integer size, String status) {
        log.info("查询需求样品竞价列表，需求ID: {}, 页码: {}, 大小: {}, 状态: {}", requirementId, page, size, status);
        
        // 参数验证
        if (requirementId == null || requirementId <= 0) {
            throw new BusinessException("需求ID不能为空");
        }
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        Page<BiddingRecord> pageParam = new Page<>(page, size);
        
        // 使用专门的Mapper方法查询已审核通过的样品竞价
        IPage<BiddingRecord> biddingPage = biddingRecordMapper.selectApprovedSampleBiddingsByRequirement(
                pageParam, requirementId, status);
        
        log.info("查询到样品竞价记录数: {}", biddingPage.getTotal());
        
        return biddingPage.convert(bidding -> convertToDetailResponse(bidding, false));
    }
    
    @Override
    public IPage<BiddingDetailResponse> getSampleBiddingList(Long requirementId, Integer page, Integer size, String status) {
        // 为了保持兼容性，直接调用新方法
        return getSampleBiddingsByRequirement(requirementId, page, size, status);
    }
    
    @Override
    public List<BiddingDetailResponse> getAcceptedSampleBiddings(Long requirementId) {
        LambdaQueryWrapper<BiddingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BiddingRecord::getRequirementId, requirementId)
                   .eq(BiddingRecord::getBiddingType, BiddingRecord.BiddingType.SAMPLE)
                   .eq(BiddingRecord::getStatus, BiddingRecord.BiddingStatus.SAMPLE_ACCEPTED)
                   .eq(BiddingRecord::getDeleted, "0")
                   .orderByDesc(BiddingRecord::getCreatedAt);
        
        List<BiddingRecord> acceptedBiddings = biddingRecordMapper.selectList(queryWrapper);
        
        return acceptedBiddings.stream()
                .map(bidding -> convertToDetailResponse(bidding, false))
                .collect(Collectors.toList());
    }
    
    @Override
    public IPage<BiddingDetailResponse> getSellerSampleBiddings(Long sellerId, Integer page, Integer size, String status) {
        Page<BiddingRecord> pageParam = new Page<>(page, size);
        
        LambdaQueryWrapper<BiddingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BiddingRecord::getSellerId, sellerId)
                   .eq(BiddingRecord::getBiddingType, BiddingRecord.BiddingType.SAMPLE)
                   .eq(BiddingRecord::getDeleted, "0");
        
        if (StringUtils.hasText(status)) {
            queryWrapper.eq(BiddingRecord::getStatus, status);
        }
        
        queryWrapper.orderByDesc(BiddingRecord::getCreatedAt);
        
        IPage<BiddingRecord> biddingPage = biddingRecordMapper.selectPage(pageParam, queryWrapper);
        
        return biddingPage.convert(bidding -> convertToDetailResponse(bidding, true));
    }
    
    @Override
    public BiddingDetailResponse getSampleBiddingDetail(Long biddingId) {
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(biddingId);
        if (biddingRecord == null) {
            throw new BusinessException("竞价记录不存在");
        }
        
        if (!BiddingRecord.BiddingType.SAMPLE.equals(biddingRecord.getBiddingType())) {
            throw new BusinessException("该竞价不是样品竞价");
        }
        
        return convertToDetailResponse(biddingRecord, false);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BiddingDetailResponse updateSampleBidding(Long biddingId, SampleBiddingSubmitRequest request, Long sellerId) {
        log.info("修改样品竞价，竞价ID: {}, 卖家ID: {}", biddingId, sellerId);
        
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(biddingId);
        if (biddingRecord == null) {
            throw new BusinessException("竞价记录不存在");
        }
        
        // 验证权限
        if (!sellerId.equals(biddingRecord.getSellerId())) {
            throw new BusinessException("无权限修改该竞价");
        }
        
        // 验证竞价类型
        if (!BiddingRecord.BiddingType.SAMPLE.equals(biddingRecord.getBiddingType())) {
            throw new BusinessException("该竞价不是样品竞价");
        }
        
        // 验证状态（只有pending状态才能修改）
        if (!BiddingRecord.BiddingStatus.PENDING.equals(biddingRecord.getStatus())) {
            throw new BusinessException("竞价状态不允许修改");
        }
        
        // 更新竞价信息
        updateBiddingRecordFromRequest(biddingRecord, request);
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        biddingRecordMapper.updateById(biddingRecord);
        
        log.info("样品竞价修改成功，竞价ID: {}", biddingId);
        
        return convertToDetailResponse(biddingRecord, true);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BiddingDetailResponse cancelSampleBidding(Long biddingId, Long sellerId, String reason) {
        log.info("取消样品竞价，竞价ID: {}, 卖家ID: {}", biddingId, sellerId);
        
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(biddingId);
        if (biddingRecord == null) {
            throw new BusinessException("竞价记录不存在");
        }
        
        // 验证权限
        if (!sellerId.equals(biddingRecord.getSellerId())) {
            throw new BusinessException("无权限取消该竞价");
        }
        
        // 验证竞价类型
        if (!BiddingRecord.BiddingType.SAMPLE.equals(biddingRecord.getBiddingType())) {
            throw new BusinessException("该竞价不是样品竞价");
        }
        
        // 验证状态（已接受的竞价不能取消）
        if (BiddingRecord.BiddingStatus.SAMPLE_ACCEPTED.equals(biddingRecord.getStatus())) {
            throw new BusinessException("已接受的竞价不能取消");
        }
        if (BiddingRecord.BiddingStatus.CANCELLED.equals(biddingRecord.getStatus())) {
            throw new BusinessException("竞价已取消");
        }
        
        // 更新竞价状态为已取消
        biddingRecord.setStatus(BiddingRecord.BiddingStatus.CANCELLED);
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        
        // 添加取消原因到描述中
        if (StringUtils.hasText(reason)) {
            String currentDescription = biddingRecord.getDescription();
            String cancelRemark = "\n取消原因：" + reason;
            biddingRecord.setDescription(StringUtils.hasText(currentDescription) 
                ? currentDescription + cancelRemark : cancelRemark);
        }
        
        biddingRecordMapper.updateById(biddingRecord);
        
        log.info("样品竞价取消成功，竞价ID: {}", biddingId);
        
        return convertToDetailResponse(biddingRecord, true);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<BiddingDetailResponse> batchAcceptSampleBiddings(List<Long> biddingIds, Long buyerId) {
        log.info("批量接受样品竞价，买家ID: {}, 竞价数量: {}", buyerId, biddingIds.size());
        
        if (CollectionUtils.isEmpty(biddingIds)) {
            throw new BusinessException("请选择要接受的竞价");
        }
        
        List<BiddingDetailResponse> results = biddingIds.stream()
                .map(biddingId -> acceptSampleBidding(biddingId, buyerId))
                .collect(Collectors.toList());
        
        log.info("批量接受样品竞价成功，接受数量: {}", results.size());
        
        return results;
    }
    
    @Override
    public boolean validateSampleBidding(Long biddingId, Long requirementId) {
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(biddingId);
        if (biddingRecord == null) {
            return false;
        }
        
        return requirementId.equals(biddingRecord.getRequirementId()) &&
               BiddingRecord.BiddingType.SAMPLE.equals(biddingRecord.getBiddingType()) &&
               "0".equals(biddingRecord.getDeleted());
    }
    
    /**
     * 构建样品竞价记录
     */
    private BiddingRecord buildSampleBiddingRecord(SampleBiddingSubmitRequest request, Long sellerId, User seller) {
        BiddingRecord biddingRecord = new BiddingRecord();
        
        // 基本信息
        biddingRecord.setRequirementId(request.getRequirementId());
        biddingRecord.setSellerId(sellerId);
        biddingRecord.setBiddingType(BiddingRecord.BiddingType.SAMPLE);
        biddingRecord.setStatus(BiddingRecord.BiddingStatus.PENDING);
        
        // 样品信息
        biddingRecord.setSamplePrice(request.getSamplePrice());
        biddingRecord.setSampleQuantity(request.getSampleQuantity());
        biddingRecord.setSampleSpecification(request.getSampleSpecification());
        
        // 产品信息
        biddingRecord.setProductName(request.getProductName());
        biddingRecord.setDescription(request.getDescription());
        biddingRecord.setDeliveryTime(request.getDeliveryTime());
        biddingRecord.setHsCode(request.getHsCode());
        biddingRecord.setUnit(request.getUnit());
        
        // 媒体信息
        biddingRecord.setImages(request.getImages());
        biddingRecord.setVideos(request.getVideos());
        biddingRecord.setCompanyEnvironmentImages(request.getCompanyEnvironmentImages());
        biddingRecord.setCompanyAddress(request.getCompanyAddress());
        
        // 属性信息
        biddingRecord.setAttributesJson(request.getAttributesJson());
        
        // 卖家联系信息
        biddingRecord.setCompany(StringUtils.hasText(request.getCompany()) ? request.getCompany() : seller.getCompany());
        biddingRecord.setPhone(StringUtils.hasText(request.getPhone()) ? request.getPhone() : seller.getPhone());
        biddingRecord.setEmail(StringUtils.hasText(request.getEmail()) ? request.getEmail() : seller.getEmail());
        
        // 审核状态
        biddingRecord.setAuditStatus("pending_audit");
        biddingRecord.setWinner(false);
        
        return biddingRecord;
    }
    
    /**
     * 从请求更新竞价记录
     */
    private void updateBiddingRecordFromRequest(BiddingRecord biddingRecord, SampleBiddingSubmitRequest request) {
        // 样品信息
        biddingRecord.setSamplePrice(request.getSamplePrice());
        biddingRecord.setSampleQuantity(request.getSampleQuantity());
        biddingRecord.setSampleSpecification(request.getSampleSpecification());
        
        // 产品信息
        biddingRecord.setProductName(request.getProductName());
        biddingRecord.setDescription(request.getDescription());
        biddingRecord.setDeliveryTime(request.getDeliveryTime());
        biddingRecord.setHsCode(request.getHsCode());
        biddingRecord.setUnit(request.getUnit());
        
        // 媒体信息
        biddingRecord.setImages(request.getImages());
        biddingRecord.setVideos(request.getVideos());
        biddingRecord.setCompanyEnvironmentImages(request.getCompanyEnvironmentImages());
        biddingRecord.setCompanyAddress(request.getCompanyAddress());
        
        // 属性信息
        biddingRecord.setAttributesJson(request.getAttributesJson());
        
        // 联系信息
        if (StringUtils.hasText(request.getCompany())) {
            biddingRecord.setCompany(request.getCompany());
        }
        if (StringUtils.hasText(request.getPhone())) {
            biddingRecord.setPhone(request.getPhone());
        }
        if (StringUtils.hasText(request.getEmail())) {
            biddingRecord.setEmail(request.getEmail());
        }
    }
    
    /**
     * 转换为详情响应DTO
     */
    private BiddingDetailResponse convertToDetailResponse(BiddingRecord biddingRecord, boolean isOwnBidding) {
        BiddingDetailResponse response = new BiddingDetailResponse();
        BeanUtils.copyProperties(biddingRecord, response);
        
        // 设置是否为自己的竞价
        response.setIsOwnBidding(isOwnBidding);
        
        // 转换图片和视频URL列表
        if (StringUtils.hasText(biddingRecord.getImages())) {
            response.setImages(Arrays.asList(biddingRecord.getImages().split(",")));
        }
        if (StringUtils.hasText(biddingRecord.getVideos())) {
            response.setVideos(Arrays.asList(biddingRecord.getVideos().split(",")));
        }
        if (StringUtils.hasText(biddingRecord.getCompanyEnvironmentImages())) {
            response.setCompanyEnvironmentImages(Arrays.asList(biddingRecord.getCompanyEnvironmentImages().split(",")));
        }
        

        
        return response;
    }
    
    /**
     * 发送样品竞价提交通知
     */
    @Async("taskExecutor")
    private void sendSampleBiddingSubmitNotifications(BiddingRecord biddingRecord, PurchaseRequirement requirement) {
        try {
            // 通知买家有新的样品竞价
            Notification buyerNotification = new Notification();
            buyerNotification.setType(NotificationType.SAMPLE_BIDDING_SUBMIT.getCode());
            buyerNotification.setTitle("您有新的样品竞价");
            buyerNotification.setContent("您发布的样品需求收到了新的竞价");
            buyerNotification.setReceiverId(requirement.getBuyerId());
            buyerNotification.setReceiverRole("buyer");
            buyerNotification.setRelatedId(biddingRecord.getId());
            notificationService.createNotification(buyerNotification);
            
            // 通知所有管理员有新的样品竞价待处理
            List<User> adminUsers = userMapper.selectAllAdmins();
            for (User admin : adminUsers) {
                Notification adminNotification = new Notification();
                adminNotification.setType(NotificationType.SAMPLE_BIDDING_SUBMIT.getCode());
                adminNotification.setTitle("有新的样品竞价待处理");
                adminNotification.setContent("有新的样品竞价需要关注");
                adminNotification.setReceiverId(admin.getId());
                adminNotification.setReceiverRole("admin");
                adminNotification.setRelatedId(biddingRecord.getId());
                notificationService.createNotification(adminNotification);
            }
            
            log.info("样品竞价提交通知发送成功，竞价ID: {}", biddingRecord.getId());
        } catch (Exception e) {
            log.error("发送样品竞价提交通知失败，竞价ID: {}", biddingRecord.getId(), e);
        }
    }
    
    /**
     * 发送样品竞价接受通知
     */
    @Async("taskExecutor")
    private void sendSampleBiddingAcceptedNotification(BiddingRecord biddingRecord) {
        try {
            Notification notification = new Notification();
            notification.setType(NotificationType.SAMPLE_BIDDING_ACCEPTED.getCode());
            notification.setTitle("您的样品竞价已被接受");
            notification.setContent("恭喜！您的样品竞价已被买家接受");
            notification.setReceiverId(biddingRecord.getSellerId());
            notification.setReceiverRole("seller");
            notification.setRelatedId(biddingRecord.getId());
            notificationService.createNotification(notification);
            
            log.info("样品竞价接受通知发送成功，竞价ID: {}", biddingRecord.getId());
        } catch (Exception e) {
            log.error("发送样品竞价接受通知失败，竞价ID: {}", biddingRecord.getId(), e);
        }
    }
    
    /**
     * 发送样品竞价拒绝通知
     */
    @Async("taskExecutor")
    private void sendSampleBiddingRejectedNotification(BiddingRecord biddingRecord, String reason) {
        try {
            Notification notification = new Notification();
            notification.setType(NotificationType.SAMPLE_BIDDING_REJECTED.getCode());
            notification.setTitle("您的样品竞价已被拒绝");
            String content = "很遗憾，您的样品竞价已被买家拒绝";
            if (StringUtils.hasText(reason)) {
                content += "，拒绝原因：" + reason;
            }
            notification.setContent(content);
            notification.setReceiverId(biddingRecord.getSellerId());
            notification.setReceiverRole("seller");
            notification.setRelatedId(biddingRecord.getId());
            notificationService.createNotification(notification);
            
            log.info("样品竞价拒绝通知发送成功，竞价ID: {}", biddingRecord.getId());
        } catch (Exception e) {
            log.error("发送样品竞价拒绝通知失败，竞价ID: {}", biddingRecord.getId(), e);
        }
    }
} 