# AdminSampleBiddingServiceImpl - 管理员样品竞价服务实现文档

## 文件概述
`AdminSampleBiddingServiceImpl`是管理员样品竞价业务逻辑的核心实现类，位于`com.purchase.bidding.service.impl`包中。该类实现了`AdminSampleBiddingService`接口，提供了管理员对样品竞价的全面管理功能，包括查询、审核、状态管理和统计分析等。

## 核心功能

### 1. 样品竞价查询管理
- **全量查询**: 支持多条件分页查询所有样品竞价记录
- **待审核列表**: 获取待审核的样品竞价列表
- **已审核列表**: 获取已审核通过/拒绝的样品竞价
- **详情查询**: 获取单个样品竞价的完整详情信息

### 2. 审核管理功能
- **单个审核**: 对单个样品竞价进行审核操作
- **批量审核**: 支持批量审核多个样品竞价
- **审核记录**: 记录详细的审核历史和操作日志

### 3. 状态管理功能
- **状态更新**: 更新样品竞价的状态
- **强制操作**: 支持强制接受或拒绝竞价
- **状态流转**: 管理竞价状态的完整生命周期

### 4. 统计分析功能
- **基础统计**: 提供样品竞价的数量和金额统计
- **趋势分析**: 分析竞价数据的时间趋势
- **分布统计**: 按不同维度统计竞价分布情况

### 5. 数据导出功能
- **Excel导出**: 支持将竞价数据导出为Excel格式
- **自定义导出**: 支持自定义导出字段和筛选条件

## 接口说明

### 查询相关方法

#### getAllSampleBiddings
```java
IPage<BiddingDetailResponse> getAllSampleBiddings(Integer page, Integer size, String status, 
                                                String auditStatus, Long sellerId, Long buyerId, 
                                                Long requirementId, BigDecimal minSamplePrice, 
                                                BigDecimal maxSamplePrice, String productName, 
                                                String company, LocalDate startDate, LocalDate endDate)
```
**功能**: 分页查询所有样品竞价记录，支持多条件筛选
**权限要求**: 仅管理员可访问
**参数说明**:
- `page`: 页码，从1开始
- `size`: 每页记录数
- `status`: 竞价状态筛选
- `auditStatus`: 审核状态筛选
- `sellerId/buyerId`: 买卖家ID筛选
- `minSamplePrice/maxSamplePrice`: 价格区间筛选
- `productName/company`: 产品名称和公司名称模糊查询
- `startDate/endDate`: 创建时间范围筛选

#### getPendingAuditSampleBiddings
```java
IPage<BiddingDetailResponse> getPendingAuditSampleBiddings(Integer page, Integer size)
```
**功能**: 获取待审核的样品竞价列表
**权限要求**: 仅管理员可访问
**返回**: 分页的待审核竞价记录

#### getSampleBiddingDetail
```java
BiddingDetailResponse getSampleBiddingDetail(Long biddingId)
```
**功能**: 获取单个样品竞价的完整详情
**权限要求**: 仅管理员可访问
**异常处理**: 竞价不存在或类型不符时抛出BusinessException

### 审核相关方法

#### auditSampleBidding
```java
BiddingDetailResponse auditSampleBidding(Long biddingId, Boolean approved, String auditRemark)
```
**功能**: 对单个样品竞价进行审核
**业务规则**:
- 只有待审核状态的竞价才能被审核
- 审核通过后会发送通知给相关用户
- 审核操作会记录操作日志
**参数说明**:
- `biddingId`: 竞价记录ID
- `approved`: true表示通过，false表示拒绝
- `auditRemark`: 审核备注信息

#### batchAuditSampleBiddings
```java
void batchAuditSampleBiddings(List<Long> biddingIds, Boolean approved, String auditRemark)
```
**功能**: 批量审核多个样品竞价
**事务管理**: 使用@Transactional注解保证批量操作的原子性
**异常处理**: 单个竞价审核失败不会影响其他竞价的审核

### 状态管理方法

#### updateSampleBiddingStatus
```java
BiddingDetailResponse updateSampleBiddingStatus(Long biddingId, String status, String statusRemark)
```
**功能**: 更新样品竞价的状态
**状态流转规则**:
- pending → accepted: 接受竞价
- pending → rejected: 拒绝竞价
- accepted → completed: 完成竞价
- 任何状态 → cancelled: 取消竞价

#### forceAcceptSampleBidding
```java
BiddingDetailResponse forceAcceptSampleBidding(Long biddingId, String remark)
```
**功能**: 强制接受样品竞价（绕过正常状态流转）
**使用场景**: 特殊情况下管理员可以强制接受竞价
**权限要求**: 需要超级管理员权限

### 统计分析相关方法

#### getSampleBiddingStats
```java
AdminSampleBiddingStatsResponse getSampleBiddingStats(LocalDate startDate, LocalDate endDate)
```
**功能**: 获取样品竞价的统计数据
**统计维度**:
- 总竞价数量
- 审核通过/拒绝数量
- 各状态竞价分布
- 时间趋势分析
- 买卖家统计信息

## 使用示例

### 查询样品竞价列表
```java
// 创建服务实例
AdminSampleBiddingService adminService = new AdminSampleBiddingServiceImpl();

// 查询待审核的样品竞价
IPage<BiddingDetailResponse> pendingBiddings = 
    adminService.getPendingAuditSampleBiddings(1, 20);

// 条件查询所有样品竞价
IPage<BiddingDetailResponse> allBiddings = 
    adminService.getAllSampleBiddings(1, 10, "pending", null, 
                                    null, null, null, null, 
                                    null, "手机", null, 
                                    LocalDate.now().minusDays(7), LocalDate.now());
```

### 审核样品竞价
```java
// 单个审核
BiddingDetailResponse result = 
    adminService.auditSampleBidding(123L, true, "样品质量符合要求");

// 批量审核
List<Long> biddingIds = Arrays.asList(123L, 124L, 125L);
adminService.batchAuditSampleBiddings(biddingIds, true, "批量审核通过");
```

### 获取统计数据
```java
// 获取本周的统计数据
AdminSampleBiddingStatsResponse stats = 
    adminService.getSampleBiddingStats(
        LocalDate.now().minusDays(7), 
        LocalDate.now()
    );

// 打印统计结果
System.out.println("总竞价数: " + stats.getTotalCount());
System.out.println("待审核数: " + stats.getPendingCount());
System.out.println("已审核数: " + stats.getApprovedCount());
```

### 数据导出示例
```java
// 导出待审核的样品竞价数据
IPage<BiddingDetailResponse> data = 
    adminService.getPendingAuditSampleBiddings(1, 1000);

// 转换为Excel格式并导出
List<BiddingDetailResponse> records = data.getRecords();
// 使用Apache POI或EasyExcel进行数据导出
```

## 注意事项

### 权限控制
1. **管理员权限验证**: 所有方法都会验证当前用户是否具有管理员权限
2. **操作审计**: 所有管理操作都会记录详细的审计日志
3. **敏感数据保护**: 对敏感信息进行脱敏处理

### 性能优化
1. **分页查询**: 所有查询方法都支持分页，避免内存溢出
2. **索引优化**: 关键查询字段已建立数据库索引
3. **缓存策略**: 统计数据支持缓存，减少数据库压力
4. **批量操作**: 批量审核使用事务管理，保证数据一致性

### 安全规范
1. **输入验证**: 对所有输入参数进行严格验证
2. **SQL注入防护**: 使用MyBatis-Plus防止SQL注入
3. **权限检查**: 每个操作都进行权限验证
4. **数据脱敏**: 敏感信息在返回前进行脱敏处理

### 扩展说明
1. **自定义查询**: 支持添加自定义查询条件和排序规则
2. **导出模板**: 支持自定义导出模板和字段选择
3. **通知扩展**: 支持扩展通知渠道（邮件、短信、站内信）
4. **统计维度**: 支持添加新的统计维度和指标

### 错误处理
1. **参数验证**: 无效参数抛出BusinessException
2. **权限异常**: 无权限操作返回403错误
3. **数据异常**: 数据不存在返回404错误
4. **系统异常**: 系统错误记录日志并返回友好提示

---

**文件路径**: `backend/src/main/java/com/purchase/bidding/service/impl/AdminSampleBiddingServiceImpl.java`

**最后更新**: 2025-01-01
