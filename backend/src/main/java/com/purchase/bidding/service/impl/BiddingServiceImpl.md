# BiddingServiceImpl - 竞价服务实现文档

## 文件概述
`BiddingServiceImpl`是竞价业务逻辑的核心实现类，继承自`ServiceImpl`并实现了`BiddingService`接口。该类处理竞价记录的提交、修改、查询、取消等核心业务逻辑，支持普通竞价和样品竞价两种类型，是整个竞价系统的核心服务组件。

## 核心功能

### 1. 竞价提交管理
- **普通竞价提交**: 处理采购需求的竞价提交
- **样品竞价提交**: 处理样品需求的竞价提交
- **重复检查**: 防止同一卖家对同一需求重复提交
- **数据验证**: 验证竞价数据的完整性和有效性

### 2. 竞价修改功能
- **竞价更新**: 允许卖家更新已提交的竞价信息
- **状态限制**: 只允许修改待处理或已取消状态的竞价
- **数据同步**: 更新关联的需求信息和用户数据

### 3. 竞价查询服务
- **需求竞价列表**: 查询指定需求的所有竞价记录
- **卖家竞价列表**: 查询指定卖家的所有竞价记录
- **详情查询**: 获取单个竞价的完整详细信息
- **统计查询**: 提供竞价相关的统计数据

### 4. 竞价状态管理
- **竞价取消**: 允许卖家取消已提交的竞价
- **状态检查**: 验证竞价状态的有效性
- **权限验证**: 确保只有竞价所有者可以操作

### 5. 通知服务集成
- **提交通知**: 竞价提交后自动发送通知
- **状态变更通知**: 竞价状态变更时发送通知
- **多渠道通知**: 支持站内信、邮件等多种通知方式

## 接口说明

### 竞价提交相关方法

#### submitBidding
```java
BiddingDetailResponse submitBidding(BiddingSubmitRequest request)
```
**功能**: 提交新的竞价记录
**业务规则**:
- 同一卖家不能对同一需求重复提交竞价
- 自动设置竞价状态为"pending"待处理
- 根据竞价类型设置相应的样品信息
- 提交成功后发送通知给需求发布者

**参数说明**:
- `request`: 竞价提交请求对象，包含所有竞价信息
- 支持字段：需求ID、卖家ID、价格、数量、产品描述、图片、视频等

#### submitSampleBidding
```java
BiddingDetailResponse submitSampleBidding(SampleBiddingSubmitRequest request, Long sellerId)
```
**功能**: 提交样品竞价（在SampleBiddingServiceImpl中实现）
**特殊处理**:
- 验证需求类型必须为样品需求
- 设置竞价类型为"sample"
- 包含样品价格、数量、规格等特殊字段

### 竞价修改方法

#### updateBidding
```java
BiddingDetailResponse updateBidding(Long id, BiddingUpdateRequest request)
```
**功能**: 更新已提交的竞价信息
**限制条件**:
- 只能修改自己提交的竞价
- 只能修改待处理或已取消状态的竞价
- 修改后状态重置为待处理
**更新字段**: 价格、数量、描述、图片、视频、联系方式等

### 竞价查询方法

#### getBiddingsByRequirement
```java
IPage<BiddingDetailResponse> getBiddingsByRequirement(Long requirementId, Integer page, Integer size, String status, Long buyerId)
```
**功能**: 获取指定需求的所有竞价记录
**权限控制**:
- 需求所有者可以看到所有竞价详情
- 其他用户只能看到基本信息
- 支持按状态筛选和分页查询

#### getBiddingsBySeller
```java
IPage<BiddingDetailResponse> getBiddingsBySeller(Long sellerId, Integer page, Integer size, String status)
```
**功能**: 获取指定卖家的所有竞价记录
**用途**: 主要用于卖家查看自己的竞价历史

#### getBiddingDetail
```java
BiddingDetailResponse getBiddingDetail(Long biddingId, Long currentUserId)
```
**功能**: 获取竞价详情
**权限验证**:
- 竞价所有者可以查看完整信息
- 需求所有者可以查看竞价信息
- 其他用户只能查看公开信息

### 竞价状态管理方法

#### cancelBidding
```java
void cancelBidding(Long id, Long sellerId)
```
**功能**: 取消已提交的竞价
**限制条件**:
- 只能取消自己提交的竞价
- 只能取消待处理状态的竞价
- 取消后状态变为"cancelled"

### 统计分析方法

#### getBiddingStats
```java
BiddingStatsResponse getBiddingStats(Long requirementId)
```
**功能**: 获取需求的竞价统计数据
**统计内容**:
- 总竞价数量
- 各状态竞价数量
- 价格区间分布
- 卖家参与情况

## 使用示例

### 提交竞价
```java
// 创建服务实例
BiddingService biddingService = new BiddingServiceImpl();

// 构建竞价提交请求
BiddingSubmitRequest request = new BiddingSubmitRequest();
request.setRequirementId(1001L);
request.setSellerId(2001L);
request.setPrice(new BigDecimal("150.50"));
request.setQuantity(100);
request.setUnit("件");
request.setProductName("高质量手机壳");
request.setDescription("优质材料制作，多种颜色可选");
request.setImages(Arrays.asList("image1.jpg", "image2.jpg"));
request.setVideos(Arrays.asList("video1.mp4"));
request.setCompanyName("优质供应商有限公司");
request.setContactPhone("13800138000");
request.setEmail("<EMAIL>");
request.setBiddingType("purchase");

// 提交竞价
BiddingDetailResponse response = biddingService.submitBidding(request);
System.out.println("竞价提交成功，竞价ID: " + response.getId());
```

### 提交样品竞价
```java
// 构建样品竞价请求
SampleBiddingSubmitRequest sampleRequest = new SampleBiddingSubmitRequest();
sampleRequest.setRequirementId(1002L);
sampleRequest.setSamplePrice(new BigDecimal("25.00"));
sampleRequest.setSampleQuantity(5);
sampleRequest.setSampleSpecification("标准规格，红色，M号");
sampleRequest.setDescription("样品展示用，质量保证");

// 提交样品竞价
BiddingDetailResponse sampleResponse = 
    sampleBiddingService.submitSampleBidding(sampleRequest, 2001L);
System.out.println("样品竞价提交成功，竞价ID: " + sampleResponse.getId());
```

### 查询竞价列表
```java
// 查询需求的竞价列表
IPage<BiddingDetailResponse> biddings = 
    biddingService.getBiddingsByRequirement(1001L, 1, 10, null, 1001L);

// 遍历竞价记录
for (BiddingDetailResponse bidding : biddings.getRecords()) {
    System.out.println("竞价ID: " + bidding.getId());
    System.out.println("价格: " + bidding.getPrice());
    System.out.println("卖家: " + bidding.getSellerName());
    System.out.println("状态: " + bidding.getStatus());
}
```

### 更新竞价信息
```java
// 构建更新请求
BiddingUpdateRequest updateRequest = new BiddingUpdateRequest();
updateRequest.setSellerId(2001L);
updateRequest.setPrice(new BigDecimal("145.00"));
updateRequest.setDescription("更新描述：价格调整，包含运费");
updateRequest.setImages(Arrays.asList("new_image1.jpg", "new_image2.jpg"));

// 更新竞价
BiddingDetailResponse updated = 
    biddingService.updateBidding(12345L, updateRequest);
System.out.println("竞价更新成功");
```

### 取消竞价
```java
// 取消竞价
try {
    biddingService.cancelBidding(12345L, 2001L);
    System.out.println("竞价取消成功");
} catch (BusinessException e) {
    System.out.println("取消失败: " + e.getMessage());
}
```

### 获取统计数据
```java
// 获取竞价统计
BiddingStatsResponse stats = biddingService.getBiddingStats(1001L);
System.out.println("总竞价数: " + stats.getTotalCount());
System.out.println("平均价格: " + stats.getAveragePrice());
System.out.println("最低价格: " + stats.getMinPrice());
System.out.println("最高价格: " + stats.getMaxPrice());
```

## 注意事项

### 权限控制
1. **身份验证**: 所有操作都需要验证用户身份
2. **权限检查**: 确保只有相关用户可以操作竞价
3. **数据隔离**: 不同用户只能访问自己有权限的数据

### 性能优化
1. **分页查询**: 所有查询都支持分页，避免内存溢出
2. **索引优化**: 关键字段已建立数据库索引
3. **缓存策略**: 统计数据支持缓存，减少数据库压力
4. **批量操作**: 批量更新使用事务管理

### 数据安全
1. **输入验证**: 对所有输入参数进行严格验证
2. **SQL注入防护**: 使用MyBatis-Plus防止SQL注入
3. **敏感信息保护**: 用户敏感信息在返回前进行脱敏
4. **事务管理**: 关键操作使用@Transactional注解

### 业务规则
1. **状态流转**: 严格遵循预定义的状态流转规则
2. **重复提交**: 防止同一卖家对同一需求重复竞价
3. **时效性**: 已关闭的需求不接受新的竞价
4. **数据一致性**: 确保竞价数据与需求数据保持一致

### 扩展说明
1. **通知扩展**: 支持添加新的通知渠道
2. **查询扩展**: 支持自定义查询条件和排序规则
3. **统计扩展**: 支持添加新的统计维度和指标
4. **业务规则扩展**: 支持自定义竞价规则和条件

### 错误处理
1. **参数验证**: 无效参数抛出BusinessException
2. **权限异常**: 无权限操作返回403错误
3. **数据异常**: 数据不存在返回404错误
4. **业务异常**: 违反业务规则返回400错误

---

**文件路径**: `backend/src/main/java/com/purchase/bidding/service/impl/BiddingServiceImpl.java`

**最后更新**: 2025-01-01
