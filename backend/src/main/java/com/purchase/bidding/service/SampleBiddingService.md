## 类用途
样品竞价服务接口，处理样品采购的特殊竞价流程

## 核心方法
### String submitSampleBidding(SampleBiddingSubmitRequest request)
- 参数：样品竞价提交请求
- 返回：竞价单ID
- 逻辑：
  1. 验证样品信息
  2. 创建临时竞价记录
  3. 生成样品编号
  4. 返回竞价单ID

### void confirmSampleDelivery(String biddingId)
- 参数：竞价单ID
- 逻辑：
  1. 确认样品已送达
  2. 更新物流状态
  3. 通知采购方验收

### SampleBiddingDetail getSampleBiddingDetail(String id)
- 参数：竞价单ID
- 返回：样品竞价详情
- 逻辑：
  1. 查询竞价主体信息
  2. 加载样品规格
  3. 获取验收记录

## 业务规则
- 样品竞价需单独审批流程
- 样品编号格式：SMP-YYYYMMDD-XXXX
- 物流状态需拍照确认
- 验收不通过需记录原因

## 使用示例
```java
// 提交样品竞价示例
SampleBiddingSubmitRequest request = new SampleBiddingSubmitRequest(
    "req456",
    "样品A",
    new BigDecimal("100"),
    "2个工作日内送达");
String biddingId = sampleBiddingService.submitSampleBidding(request);
```

## 注意事项
- 样品竞价有效期30天
- 重要操作需双重验证
- 生产环境建议添加防重复提交控制