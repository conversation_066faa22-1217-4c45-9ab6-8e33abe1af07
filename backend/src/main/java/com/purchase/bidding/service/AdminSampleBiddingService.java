package com.purchase.bidding.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.bidding.dto.response.BiddingDetailResponse;
import com.purchase.bidding.dto.response.AdminSampleBiddingStatsResponse;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 管理员样品竞价服务接口
 * 提供管理员对样品竞价的全面管理功能
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface AdminSampleBiddingService {
    
    /**
     * 获取所有样品竞价列表（管理员视图）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param status 竞价状态
     * @param auditStatus 审核状态
     * @param sellerId 卖家ID
     * @param buyerId 买家ID
     * @param requirementId 需求ID
     * @param minSamplePrice 最低样品价格
     * @param maxSamplePrice 最高样品价格
     * @param productName 产品名称
     * @param company 公司名称
     * @param startDate 创建开始时间
     * @param endDate 创建结束时间
     * @return 样品竞价分页列表
     */
    IPage<BiddingDetailResponse> getAllSampleBiddings(Integer page, Integer size, String status, 
                                                     String auditStatus, Long sellerId, Long buyerId, 
                                                     Long requirementId, BigDecimal minSamplePrice, 
                                                     BigDecimal maxSamplePrice, String productName, 
                                                     String company, LocalDate startDate, LocalDate endDate);
    
    /**
     * 获取待审核的样品竞价列表
     * 
     * @param page 页码
     * @param size 每页大小
     * @return 待审核样品竞价分页列表
     */
    IPage<BiddingDetailResponse> getPendingAuditSampleBiddings(Integer page, Integer size);
    
    /**
     * 获取已审核通过的样品竞价列表
     * 
     * @param page 页码
     * @param size 每页大小
     * @param status 竞价状态
     * @return 已审核通过样品竞价分页列表
     */
    IPage<BiddingDetailResponse> getApprovedSampleBiddings(Integer page, Integer size, String status);
    
    /**
     * 获取已拒绝审核的样品竞价列表
     * 
     * @param page 页码
     * @param size 每页大小
     * @return 已拒绝审核样品竞价分页列表
     */
    IPage<BiddingDetailResponse> getRejectedSampleBiddings(Integer page, Integer size);
    
    /**
     * 获取样品竞价详情（管理员视图）
     * 
     * @param biddingId 竞价ID
     * @return 样品竞价详情
     */
    BiddingDetailResponse getSampleBiddingDetail(Long biddingId);
    
    /**
     * 审核样品竞价
     * 
     * @param biddingId 竞价ID
     * @param approved 是否审核通过
     * @param auditRemark 审核备注
     * @return 审核后的竞价详情
     */
    BiddingDetailResponse auditSampleBidding(Long biddingId, Boolean approved, String auditRemark);
    
    /**
     * 批量审核样品竞价
     * 
     * @param biddingIds 竞价ID列表
     * @param approved 是否审核通过
     * @param auditRemark 审核备注
     * @return 审核后的竞价详情列表
     */
    List<BiddingDetailResponse> batchAuditSampleBiddings(List<Long> biddingIds, Boolean approved, String auditRemark);
    
    /**
     * 更新样品竞价状态
     * 
     * @param biddingId 竞价ID
     * @param status 新状态
     * @param statusRemark 状态更改备注
     * @return 更新后的竞价详情
     */
    BiddingDetailResponse updateSampleBiddingStatus(Long biddingId, String status, String statusRemark);
    
    /**
     * 强制接受样品竞价（管理员权限）
     * 
     * @param biddingId 竞价ID
     * @param reason 操作原因
     * @return 接受后的竞价详情
     */
    BiddingDetailResponse forceAcceptSampleBidding(Long biddingId, String reason);
    
    /**
     * 强制拒绝样品竞价（管理员权限）
     * 
     * @param biddingId 竞价ID
     * @param reason 拒绝原因
     * @return 拒绝后的竞价详情
     */
    BiddingDetailResponse forceRejectSampleBidding(Long biddingId, String reason);
    
    /**
     * 删除样品竞价（逻辑删除）
     * 
     * @param biddingId 竞价ID
     * @param reason 删除原因
     */
    void deleteSampleBidding(Long biddingId, String reason);
    
    /**
     * 批量删除样品竞价
     * 
     * @param biddingIds 竞价ID列表
     * @param reason 删除原因
     */
    void batchDeleteSampleBiddings(List<Long> biddingIds, String reason);
    
    /**
     * 获取样品竞价统计信息
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param sellerId 卖家ID
     * @param buyerId 买家ID
     * @return 统计信息
     */
    AdminSampleBiddingStatsResponse getSampleBiddingStats(LocalDate startDate, LocalDate endDate, 
                                                         Long sellerId, Long buyerId);
    
    /**
     * 获取样品竞价趋势数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param granularity 时间粒度（day/week/month）
     * @return 趋势数据列表
     */
    List<AdminSampleBiddingStatsResponse.TrendData> getSampleBiddingTrends(LocalDate startDate, 
                                                                          LocalDate endDate, 
                                                                          String granularity);
    
    /**
     * 导出样品竞价数据
     * 
     * @param status 竞价状态
     * @param auditStatus 审核状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param format 导出格式
     * @return 下载链接
     */
    String exportSampleBiddings(String status, String auditStatus, LocalDate startDate, 
                               LocalDate endDate, String format);
    
    /**
     * 重新发送样品竞价通知
     * 
     * @param biddingId 竞价ID
     * @param notificationType 通知类型
     */
    void resendSampleBiddingNotification(Long biddingId, String notificationType);
} 