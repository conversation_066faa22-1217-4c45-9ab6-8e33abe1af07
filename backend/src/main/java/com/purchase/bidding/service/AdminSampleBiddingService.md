## 类用途
管理员样品竞价服务接口，提供后台管理专用操作

## 核心方法
### void auditBidding(AdminSampleBiddingAuditRequest request)
- 参数：审核请求（含审核结果/备注）
- 逻辑：
  1. 校验投标单状态
  2. 记录审核操作
  3. 更新投标单状态
  4. 触发状态变更事件

### BiddingStatsResponse getBiddingStats()
- 返回：竞价统计数据
- 逻辑：
  1. 查询各状态投标单数量
  2. 计算平均响应时间
  3. 返回聚合结果

### void updateStatus(AdminSampleBiddingStatusUpdateRequest request)
- 参数：状态更新请求
- 逻辑：
  1. 管理员强制状态变更
  2. 记录操作日志
  3. 通知相关用户

## 权限控制
- 仅限ADMIN角色调用
- 敏感操作需记录审计日志
- 状态变更需二次确认

## 使用示例
```java
// 审核投标单示例
AdminSampleBiddingAuditRequest request = new AdminSampleBiddingAuditRequest(
    "bid123",
    AuditStatus.APPROVED,
    "符合采购要求");
adminSampleBiddingService.auditBidding(request);
```

## 注意事项
- 生产环境建议添加操作频率限制
- 重要操作需短信通知申请人
- 需验证数据归属权限