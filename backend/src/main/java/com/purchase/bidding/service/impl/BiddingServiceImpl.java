package com.purchase.bidding.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.purchase.common.exception.BusinessException;
import com.purchase.common.response.Result;
import com.purchase.bidding.dto.request.BiddingSubmitRequest;
import com.purchase.bidding.dto.request.BiddingUpdateRequest;
import com.purchase.bidding.dto.response.BiddingDetailResponse;
import com.purchase.bidding.dto.response.BiddingStatsResponse;
import com.purchase.bidding.entity.BiddingRecord;
import com.purchase.requirement.entity.PurchaseRequirement;
import com.purchase.requirement.mapper.PurchaseRequirementMapper;
import com.purchase.user.entity.User;
import com.purchase.bidding.mapper.BiddingRecordMapper;
import com.purchase.user.mapper.UserMapper;
import com.purchase.bidding.service.BiddingService;
import com.purchase.bidding.vo.RequirementPageVO;
import com.purchase.bidding.vo.RequirementVO;
import com.purchase.common.util.SecurityContextUtil;
import com.purchase.requirement.service.PurchaseRequirementService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.purchase.message.notification.service.NotificationService;
import com.purchase.message.notification.enums.NotificationType;
import com.purchase.message.notification.entity.Notification;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 竞价服务实现类
 * 处理竞价记录的提交、修改、查询等操作
 */
@Service
@RequiredArgsConstructor
public class BiddingServiceImpl extends ServiceImpl<BiddingRecordMapper, BiddingRecord> implements BiddingService {

    private final BiddingRecordMapper biddingRecordMapper;
    private final UserMapper userMapper;
    private final PurchaseRequirementService purchaseRequirementService;
    private final NotificationService notificationService;
    private final PurchaseRequirementMapper purchaseRequirementMapper;

    /**
     * 提交竞价
     * 
     * @param request 竞价提交请求，包含竞价信息
     * @return 竞价详情响应
     * @throws BusinessException 如果已经提交过竞价则抛出异常
     */
    @Override
    @Transactional
    public BiddingDetailResponse submitBidding(BiddingSubmitRequest request) {
        // 检查是否已经提交过竞价
        LambdaQueryWrapper<BiddingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BiddingRecord::getRequirementId, request.getRequirementId())
                .eq(BiddingRecord::getSellerId, request.getSellerId())
                .eq(BiddingRecord::getDeleted, "0");
        if (biddingRecordMapper.selectCount(queryWrapper) > 0) {
            throw new BusinessException(400, "已经提交过竞价");
        }

        BiddingRecord biddingRecord = new BiddingRecord();
        BeanUtils.copyProperties(request, biddingRecord);
        // 安全处理images和videos列表，避免NullPointerException
        biddingRecord.setImages(request.getImages() != null ? String.join(",", request.getImages()) : "");
        biddingRecord.setVideos(request.getVideos() != null ? String.join(",", request.getVideos()) : "");
        // 公司环境图片字段直接设置，因为前端传递的已经是逗号分隔的字符串
        biddingRecord.setCompanyEnvironmentImages(request.getCompanyEnvironmentImages());
        // 设置公司地址
        biddingRecord.setCompanyAddress(request.getCompanyAddress());
        biddingRecord.setAttributesJson(request.getAttributesJson());
        biddingRecord.setUnit(request.getUnit());
        biddingRecord.setStatus("pending");
        biddingRecord.setWinner(false);
        
        // 设置样品相关字段
        biddingRecord.setBiddingType(request.getBiddingType() != null ? request.getBiddingType() : "purchase");
        biddingRecord.setSamplePrice(request.getSamplePrice());
        biddingRecord.setSampleQuantity(request.getSampleQuantity());
        biddingRecord.setSampleSpecification(request.getSampleSpecification());
        
        // 设置审核状态为待审核
        biddingRecord.setAuditStatus("pending_audit");
        
        biddingRecord.setCreatedAt(LocalDateTime.now());
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        
        // 设置联系信息
        biddingRecord.setCompany(request.getCompanyName());
        biddingRecord.setPhone(request.getContactPhone());
        biddingRecord.setEmail(request.getEmail());

        biddingRecordMapper.insert(biddingRecord);

        // 根据竞价类型发送不同的通知
        if ("sample".equals(biddingRecord.getBiddingType())) {
            // 样品竞价通知
            sendSampleBiddingSubmitNotifications(biddingRecord);
        } else {
            // 普通竞价通知
            sendRegularBiddingSubmitNotifications(biddingRecord);
        }

        return convertToDetailResponse(biddingRecord);
    }

    /**
     * 更新竞价信息
     * 
     * @param id 竞价ID
     * @param request 竞价更新请求
     * @return 更新后的竞价详情
     * @throws BusinessException 如果无权修改或状态不允许修改
     */
    @Override
    @Transactional
    public BiddingDetailResponse updateBidding(Long id, BiddingUpdateRequest request) {
        BiddingRecord biddingRecord = getBiddingRecordById(id);
        if (!Objects.equals(biddingRecord.getSellerId(), request.getSellerId())) {
            throw new BusinessException(403, "无权修改此竞价");
        }
        if (!"pending".equals(biddingRecord.getStatus()) && !"cancelled".equals(biddingRecord.getStatus())) {
            throw new BusinessException(400, "只能修改待处理或已取消状态的竞价");
        }

        BeanUtils.copyProperties(request, biddingRecord);
        // 安全处理images和videos列表，避免NullPointerException
        biddingRecord.setImages(request.getImages() != null ? String.join(",", request.getImages()) : "");
        biddingRecord.setVideos(request.getVideos() != null ? String.join(",", request.getVideos()) : "");
        // 公司环境图片字段直接设置，因为前端传递的已经是逗号分隔的字符串
        biddingRecord.setCompanyEnvironmentImages(request.getCompanyEnvironmentImages());
        // 设置公司地址
        biddingRecord.setCompanyAddress(request.getCompanyAddress());
        biddingRecord.setAttributesJson(request.getAttributesJson());
        biddingRecord.setUnit(request.getUnit());
        
        // 设置样品相关字段
        biddingRecord.setBiddingType(request.getBiddingType() != null ? request.getBiddingType() : "purchase");
        biddingRecord.setSamplePrice(request.getSamplePrice());
        biddingRecord.setSampleQuantity(request.getSampleQuantity());
        biddingRecord.setSampleSpecification(request.getSampleSpecification());
        
        // 重置状态为pending
        biddingRecord.setStatus("pending");
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        
        // 设置联系信息
        biddingRecord.setCompany(request.getCompanyName());
        biddingRecord.setPhone(request.getContactPhone());
        biddingRecord.setEmail(request.getEmail());
       
        biddingRecordMapper.updateById(biddingRecord);

        return convertToDetailResponse(biddingRecord);
    }

    /**
     * 取消竞价
     * 
     * @param id 竞价ID
     * @param sellerId 卖家ID
     * @throws BusinessException 如果无权取消或状态不允许取消
     */
    @Override
    @Transactional
    public void cancelBidding(Long id, Long sellerId) {
        BiddingRecord biddingRecord = getBiddingRecordById(id);
        if (!Objects.equals(biddingRecord.getSellerId(), sellerId)) {
            throw new BusinessException(403, "无权取消此竞价");
        }
        if (!"pending".equals(biddingRecord.getStatus())) {
            throw new BusinessException(400, "只能取消待处理状态的竞价");
        }

        biddingRecord.setStatus("cancelled");
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        biddingRecordMapper.updateById(biddingRecord);
    }

    /**
     * 获取竞价列表
     * 根据不同条件筛选竞价记录，并根据用户角色返回不同详细程度的信息
     * 
     * @param requirementId 需求ID
     * @param buyerId 买家ID，用于权限控制
     * @param sellerId 卖家ID，用于权限控制
     * @param status 竞价状态
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @param current 当前页码
     * @param size 每页大小
     * @return 竞价详情分页列表
     */
    @Override
    public IPage<BiddingDetailResponse> getBiddingList(Long requirementId, Long buyerId, Long sellerId, String status,
                                                       BigDecimal minPrice, BigDecimal maxPrice, Integer current, Integer size) {
        // 创建分页对象，current从1开始
        Page<BiddingRecord> page = new Page<>(current, size);

        // 构建查询条件
        LambdaQueryWrapper<BiddingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BiddingRecord::getRequirementId, requirementId)
                .eq(BiddingRecord::getDeleted, "0")
                .eq(status != null, BiddingRecord::getStatus, status)
                .ge(minPrice != null, BiddingRecord::getBiddingPrice, minPrice)
                .le(maxPrice != null, BiddingRecord::getBiddingPrice, maxPrice);

        // 根据用户角色添加审核状态过滤
        // 获取当前用户角色
        boolean isAdmin = SecurityContextUtil.hasRole("admin");
        
        if (!isAdmin) {
            // 非管理员用户只能看到审核通过的竞价和自己的竞价
            if (sellerId != null) {
                // 卖家：可以看到自己的所有竞价 + 其他人审核通过的竞价
                queryWrapper.and(wrapper -> wrapper
                    .eq(BiddingRecord::getAuditStatus, "approved")
                    .or()
                    .eq(BiddingRecord::getSellerId, sellerId)
                );
            } else {
                // 买家：只能看到审核通过的竞价
                queryWrapper.eq(BiddingRecord::getAuditStatus, "approved");
            }
        }
        // 管理员可以看到所有状态的竞价，不需要额外过滤

        queryWrapper.orderByDesc(BiddingRecord::getCreatedAt);

        // 执行分页查询
        IPage<BiddingRecord> recordPage = biddingRecordMapper.selectPage(page, queryWrapper);

        // 转换结果
        return recordPage.convert(record -> {
            BiddingDetailResponse response = new BiddingDetailResponse();

            // 如果是管理员，可以看到所有信息
            if (buyerId == null && sellerId == null) {
                BeanUtils.copyProperties(record, response);
                response.setImages(convertStringToList(record.getImages()));
                response.setVideos(convertStringToList(record.getVideos()));
                response.setCompanyEnvironmentImages(convertStringToList(record.getCompanyEnvironmentImages()));
                response.setIsOwnBidding(false);

                // 管理员显示完整联系信息
                response.setCompany(record.getCompany());
                response.setContactPhone(record.getPhone());
                response.setEmail(record.getEmail());

                return response;
            }

            // 如果是卖家
            if (sellerId != null) {
                // 如果是自己的竞价，显示所有信息
                if (Objects.equals(record.getSellerId(), sellerId)) {
                    BeanUtils.copyProperties(record, response);
                    response.setImages(convertStringToList(record.getImages()));
                    response.setVideos(convertStringToList(record.getVideos()));
                    response.setCompanyEnvironmentImages(convertStringToList(record.getCompanyEnvironmentImages()));
                    response.setIsOwnBidding(true);

                    // 自己的竞价显示完整联系信息
                    response.setCompany(record.getCompany());
                    response.setContactPhone(record.getPhone());
                    response.setEmail(record.getEmail());
                } else {
                    // 如果是其他卖家的竞价，显示基本信息及unit和productName
                    response.setId(record.getId());
                    response.setRequirementId(record.getRequirementId());
                    response.setBiddingPrice(record.getBiddingPrice());
                    response.setStatus(record.getStatus());
                    response.setIsWinner(record.getWinner());
                    response.setCreatedAt(record.getCreatedAt());
                    response.setIsOwnBidding(false);
                    response.setUnit(record.getUnit());
                    response.setProductName(record.getProductName());

                    // 其他卖家的竞价需要脱敏联系信息
                    if (record.getCompany() != null) {
                        response.setCompany(com.purchase.requirement.utils.ContactInfoUtils.maskContactInfo(record.getCompany()));
                    }

                    if (record.getPhone() != null) {
                        response.setContactPhone(com.purchase.requirement.utils.ContactInfoUtils.maskContactInfo(record.getPhone()));
                    }

                    if (record.getEmail() != null) {
                        response.setEmail(com.purchase.requirement.utils.ContactInfoUtils.maskContactInfo(record.getEmail()));
                    }
                }
            }

            // 如果是买家
            if (buyerId != null) {
                // 查询该需求是否属于当前买家
                PurchaseRequirement requirement = purchaseRequirementMapper.selectById(record.getRequirementId());
                boolean isOwnRequirement = requirement != null && Objects.equals(requirement.getBuyerId(), buyerId);

                if (isOwnRequirement) {
                    // 如果是买家自己的需求，显示完整信息
                    BeanUtils.copyProperties(record, response);
                    response.setImages(convertStringToList(record.getImages()));
                    response.setVideos(convertStringToList(record.getVideos()));
                    response.setCompanyEnvironmentImages(convertStringToList(record.getCompanyEnvironmentImages()));
                    response.setIsOwnBidding(false);

                    // 买家自己的需求显示完整联系信息
                    response.setCompany(record.getCompany());
                    response.setContactPhone(record.getPhone());
                    response.setEmail(record.getEmail());
                } else {
                    // 如果不是买家自己的需求，显示基本信息和脱敏联系信息（与卖家看其他竞价的权限相同）
                    response.setId(record.getId());
                    response.setRequirementId(record.getRequirementId());
                    response.setBiddingPrice(record.getBiddingPrice());
                    response.setStatus(record.getStatus());
                    response.setIsWinner(record.getWinner());
                    response.setCreatedAt(record.getCreatedAt());
                    response.setIsOwnBidding(false);
                    response.setUnit(record.getUnit());
                    response.setProductName(record.getProductName());

                    // 其他需求的竞价联系信息需要脱敏
                    if (record.getCompany() != null) {
                        response.setCompany(com.purchase.requirement.utils.ContactInfoUtils.maskContactInfo(record.getCompany()));
                    }

                    if (record.getPhone() != null) {
                        response.setContactPhone(com.purchase.requirement.utils.ContactInfoUtils.maskContactInfo(record.getPhone()));
                    }

                    if (record.getEmail() != null) {
                        response.setEmail(com.purchase.requirement.utils.ContactInfoUtils.maskContactInfo(record.getEmail()));
                    }
                }
            }

            return response;
        });
    }

    /**
     * 将逗号分隔的字符串转换为列表
     * 
     * @param str 逗号分隔的字符串
     * @return 字符串列表，如果输入为null或空则返回空列表
     */
    private List<String> convertStringToList(String str) {
        if (str == null || str.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.asList(str.split(","));
    }

    /**
     * 接受竞价，将其设置为中标，并拒绝其他竞价
     * 
     * @param id 竞价ID
     * @param buyerId 买家ID
     * @return 接受后的竞价详情
     * @throws BusinessException 如果竞价状态不是待处理
     */
    @Override
    @Transactional
    public BiddingDetailResponse acceptBidding(Long id, Long buyerId) {
        BiddingRecord biddingRecord = getBiddingRecordById(id);
        if (!"pending".equals(biddingRecord.getStatus())) {
            throw new BusinessException(400, "只能接受待处理状态的竞价");
        }

        // 更新所有相关竞价的状态
        LambdaUpdateWrapper<BiddingRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BiddingRecord::getRequirementId, biddingRecord.getRequirementId())
                .set(BiddingRecord::getStatus, "rejected");
        biddingRecordMapper.update(null, updateWrapper);

        // 更新中标竞价
        biddingRecord.setStatus("accepted");
        biddingRecord.setWinner(true);
        biddingRecordMapper.updateById(biddingRecord);

        // 更新需求状态
        purchaseRequirementService.updateRequirementStatus(biddingRecord.getRequirementId(), "completed");
        
        // 根据竞价类型发送不同的通知
        if ("sample".equals(biddingRecord.getBiddingType())) {
            // 样品竞价接受通知
            sendSampleBiddingAcceptedNotification(biddingRecord);
        } else {
            // 普通竞价接受通知
            sendRegularBiddingAcceptedNotification(biddingRecord);
        }
        
        // 注意：不再需要授予联系信息权限，
        // 因为现在通过查询竞价状态来判断权限

        return convertToDetailResponse(biddingRecord);
    }

    /**
     * 拒绝竞价
     * 
     * @param id 竞价ID
     * @param buyerId 买家ID
     * @param reason 拒绝原因
     * @throws BusinessException 如果竞价状态不是待处理
     */
    @Override
    @Transactional
    public void rejectBidding(Long id, Long buyerId, String reason) {
        BiddingRecord biddingRecord = getBiddingRecordById(id);
        if (!"pending".equals(biddingRecord.getStatus())) {
            throw new BusinessException(400, "只能拒绝待处理状态的竞价");
        }

        biddingRecord.setStatus("rejected");
        biddingRecordMapper.updateById(biddingRecord);
        
        // 根据竞价类型发送不同的通知
        if ("sample".equals(biddingRecord.getBiddingType())) {
            // 样品竞价拒绝通知
            sendSampleBiddingRejectedNotification(biddingRecord, reason);
        } else {
            // 普通竞价拒绝通知
            sendRegularBiddingRejectedNotification(biddingRecord, reason);
        }
    }

    /**
     * 获取竞价详情
     * 
     * @param id 竞价ID
     * @return 竞价详情响应
     */
    @Override
    public BiddingDetailResponse getBiddingDetail(Long id) {
        return convertToDetailResponse(getBiddingRecordById(id));
    }

    /**
     * 获取竞价统计信息
     * 包括总数、各状态数量、最低/最高/平均价格等
     * 
     * @param requirementId 需求ID
     * @return 竞价统计信息
     */
    @Override
    public BiddingStatsResponse getBiddingStats(Long requirementId) {
        BiddingStatsResponse response = new BiddingStatsResponse();
        response.setTotalCount(biddingRecordMapper.countByRequirementId(requirementId));
        response.setPendingCount(biddingRecordMapper.countByRequirementIdAndStatus(requirementId, "pending"));
        response.setAcceptedCount(biddingRecordMapper.countByRequirementIdAndStatus(requirementId, "accepted"));
        response.setRejectedCount(biddingRecordMapper.countByRequirementIdAndStatus(requirementId, "rejected"));
        response.setCancelledCount(biddingRecordMapper.countByRequirementIdAndStatus(requirementId, "cancelled"));
        response.setMinPrice(biddingRecordMapper.findMinPrice(requirementId));
        response.setMaxPrice(biddingRecordMapper.findMaxPrice(requirementId));
        response.setAvgPrice(biddingRecordMapper.findAvgPrice(requirementId));
        return response;
    }

    /**
     * 根据ID获取竞价记录
     * 
     * @param id 竞价ID
     * @return 竞价记录实体
     * @throws BusinessException 如果竞价记录不存在
     */
    private BiddingRecord getBiddingRecordById(Long id) {
        BiddingRecord biddingRecord = biddingRecordMapper.selectById(id);
        if (biddingRecord == null || "1".equals(biddingRecord.getDeleted())) {
            throw new BusinessException(404, "竞价记录不存在");
        }
        return biddingRecord;
    }

    /**
     * 将竞价记录实体转换为详情响应对象
     * 
     * @param record 竞价记录实体
     * @return 竞价详情响应对象
     */
    private BiddingDetailResponse convertToDetailResponse(BiddingRecord record) {
        if (record == null) {
            return null;
        }
        BiddingDetailResponse response = new BiddingDetailResponse();
        BeanUtils.copyProperties(record, response);
        response.setImages(convertStringToList(record.getImages()));
        response.setVideos(convertStringToList(record.getVideos()));
        response.setCompanyEnvironmentImages(convertStringToList(record.getCompanyEnvironmentImages()));
        
        // 设置联系信息（从竞价记录中获取，而不是从用户表）
        response.setCompany(record.getCompany());
        response.setContactPhone(record.getPhone());
        response.setEmail(record.getEmail());
        
        return response;
    }

    /**
     * 获取当前卖家参与竞价的需求列表
     * 
     * @param page 分页参数
     * @return 需求分页列表
     */
    @Override
    public IPage<RequirementVO> getMyBiddingRequirements(Page<BiddingRecord> page) {
        Long sellerId = SecurityContextUtil.getCurrentUserId();

        // 1. 使用子查询获取每个需求的最新竞价记录
        LambdaQueryWrapper<BiddingRecord> wrapper = Wrappers.lambdaQuery(BiddingRecord.class)
                .eq(BiddingRecord::getSellerId, sellerId)
                .eq(BiddingRecord::getDeleted, "0")
                .inSql(BiddingRecord::getId,
                        "SELECT MAX(id) FROM bidding_record " +
                                "WHERE seller_id = " + sellerId + " AND deleted = '0' " +
                                "GROUP BY requirement_id")
                .orderByDesc(BiddingRecord::getCreatedAt);

        IPage<BiddingRecord> biddings = page(page, wrapper);

        // 2. 提取需求ID
        List<Long> requirementIds = biddings.getRecords().stream()
                .map(BiddingRecord::getRequirementId)
                .collect(Collectors.toList());

        if (requirementIds.isEmpty()) {
            return new Page<RequirementVO>()
                    .setRecords(Collections.emptyList())
                    .setTotal(0)
                    .setCurrent(page.getCurrent())
                    .setSize(page.getSize());
        }

        // 3. 获取需求详情
        List<com.purchase.requirement.vo.RequirementVO> requirementsList = 
            purchaseRequirementService.getRequirementsByIds(requirementIds);
            
        // 4. 构建返回结果
        return new Page<RequirementVO>()
                .setRecords(convertToRequirementVOList(requirementsList))
                .setTotal(biddings.getTotal())
                .setCurrent(biddings.getCurrent())
                .setSize(biddings.getSize());
    }
    
    /**
     * 将需求模块的RequirementVO列表转换为竞价模块的RequirementVO列表
     * 
     * @param sourceList 来源需求列表
     * @return 转换后的需求列表
     */
    private List<RequirementVO> convertToRequirementVOList(List<com.purchase.requirement.vo.RequirementVO> sourceList) {
        if (sourceList == null || sourceList.isEmpty()) {
            return Collections.emptyList();
        }
        
        return sourceList.stream()
            .map(source -> {
                RequirementVO target = new RequirementVO();
                target.setId(source.getId());
                target.setBuyerId(source.getBuyerId());
                target.setTitle(source.getTitle());
                target.setDescription(source.getDescription());
                target.setStatus(source.getStatus());
                target.setExpectedDeliveryTime(source.getExpectedDeliveryTime());
                target.setCreatedAt(source.getCreatedAt());
                target.setUpdatedAt(source.getUpdatedAt());
                target.setImages(source.getImages());
                
                // 转换价格范围
                if (source.getPrice() != null) {
                    RequirementVO.PriceRange priceRange = new RequirementVO.PriceRange();
                    priceRange.setMin(source.getPrice().getMin());
                    priceRange.setMax(source.getPrice().getMax());
                    target.setPrice(priceRange);
                }
                
                return target;
            })
            .collect(Collectors.toList());
    }

    /**
     * 更新竞价状态
     * 
     * @param id 竞价ID
     * @param status 新状态
     * @return 更新后的竞价详情
     * @throws BusinessException 如果状态值无效
     */
    @Override
    @Transactional
    public BiddingDetailResponse updateBiddingStatus(Long id, String status) {
        BiddingRecord biddingRecord = getBiddingRecordById(id);

        // 验证状态值是否合法
        List<String> validStatuses = Arrays.asList("pending", "accepted", "rejected", "cancelled");
        if (!validStatuses.contains(status)) {
            throw new BusinessException(400, "无效的状态值");
        }

        // 更新状态
        biddingRecord.setStatus(status);
        biddingRecord.setUpdatedAt(LocalDateTime.now());

        // 如果状态是accepted，设置为中标
        if ("accepted".equals(status)) {
            biddingRecord.setWinner(true);

            // 将其他竞价设置为rejected
            LambdaUpdateWrapper<BiddingRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(BiddingRecord::getRequirementId, biddingRecord.getRequirementId())
                    .ne(BiddingRecord::getId, id)
                    .set(BiddingRecord::getStatus, "rejected")
                    .set(BiddingRecord::getWinner, false);
            biddingRecordMapper.update(null, updateWrapper);
        } else {
            biddingRecord.setWinner(false);
        }

        biddingRecordMapper.updateById(biddingRecord);
        return convertToDetailResponse(biddingRecord);
    }

    /**
     * 删除竞价记录（逻辑删除）
     * 
     * @param id 竞价ID
     */
    @Override
    @Transactional
    public void deleteBidding(Long id) {
        BiddingRecord biddingRecord = getBiddingRecordById(id);

        // 使用UpdateWrapper进行更新
        LambdaUpdateWrapper<BiddingRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BiddingRecord::getId, id)
                .set(BiddingRecord::getDeleted, "1")
                .set(BiddingRecord::getUpdatedAt, LocalDateTime.now());

        update(updateWrapper);
    }

    /**
     * 获取特定卖家对特定需求的竞价信息
     * 
     * @param requirementId 需求ID
     * @param sellerId 卖家ID
     * @return 竞价详情，如不存在则返回null
     */
    @Override
    public BiddingDetailResponse getSellerBiddingForRequirement(Long requirementId, Long sellerId) {
        // 查询条件：需求ID、卖家ID、未删除
        LambdaQueryWrapper<BiddingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BiddingRecord::getRequirementId, requirementId)
                .eq(BiddingRecord::getSellerId, sellerId)
                .eq(BiddingRecord::getDeleted, "0");
        
        // 查询卖家对此需求的竞价记录
        BiddingRecord biddingRecord = getOne(queryWrapper);
        
        // 如果记录存在，转换为详情响应对象并返回
        if (biddingRecord != null) {
            return convertToDetailResponse(biddingRecord);
        }
        
        // 如果没有记录，返回null
        return null;
    }

    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param company 公司名称
     * @param phone 联系电话
     * @param email 电子邮箱
     */
    private void updateUserInfo(Long userId, String company, String phone, String email) {
        // 查询用户信息
        User user = userMapper.selectById(userId);
        if (user != null) {
            // 更新用户的公司名称、联系电话和邮箱
            System.out.println("更新用户信息：" + company + " " + phone + " " + email);
            user.setCompany(company);
            user.setPhone(phone);
            user.setEmail(email);
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);
        }
    }

    /**
     * 管理员获取所有竞价记录列表
     * 支持多种筛选条件，返回所有竞价的详细信息
     * 
     * @param auditStatus 竞价状态筛选
     * @param sellerId 卖家ID筛选
     * @param buyerId 买家ID筛选(通过需求关联)
     * @param minPrice 最低价格筛选
     * @param maxPrice 最高价格筛选
     * @param biddingType 竞价类型筛选
     * @param page 当前页码
     * @param size 每页大小
     * @return 竞价详情分页列表
     */
    @Override
    public IPage<BiddingDetailResponse> getAllBiddings(String auditStatus, Long sellerId, Long buyerId, 
                                                     BigDecimal minPrice, BigDecimal maxPrice, String biddingType,
                                                     Integer page, Integer size) {
        // 创建分页对象
        Page<BiddingRecord> pageObj = new Page<>(page, size);
        
        // 构建基础查询条件
        LambdaQueryWrapper<BiddingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BiddingRecord::getDeleted, "0")
                   .eq(auditStatus != null && !auditStatus.trim().isEmpty(), BiddingRecord::getAuditStatus, auditStatus)
                   .eq(sellerId != null, BiddingRecord::getSellerId, sellerId)
                   .eq(biddingType != null && !biddingType.trim().isEmpty(), BiddingRecord::getBiddingType, biddingType)
                   .ge(minPrice != null, BiddingRecord::getBiddingPrice, minPrice)
                   .le(maxPrice != null, BiddingRecord::getBiddingPrice, maxPrice)
                   .orderByDesc(BiddingRecord::getCreatedAt);
        
        // 如果指定了买家ID，需要先获取该买家的所有采购需求ID
        if (buyerId != null) {
            List<Long> requirementIds = biddingRecordMapper.findRequirementIdsByBuyerId(buyerId);
            if (!requirementIds.isEmpty()) {
                queryWrapper.in(BiddingRecord::getRequirementId, requirementIds);
            } else {
                // 如果该买家没有需求，则返回空结果
                return new Page<BiddingDetailResponse>().setRecords(Collections.emptyList());
            }
        }
        
        // 执行分页查询
        IPage<BiddingRecord> recordPage = biddingRecordMapper.selectPage(pageObj, queryWrapper);
        
        // 转换为详情响应对象
        return recordPage.convert(this::convertToDetailResponse);
    }

    /**
     * 管理员审核竞价
     * 
     * @param biddingId 竞价ID
     * @param approved 是否审核通过
     * @param remark 审核备注
     * @return 审核后的竞价详情
     */
    @Override
    @Transactional
    public BiddingDetailResponse auditBidding(Long biddingId, boolean approved, String remark) {
        // 验证当前用户是否为管理员
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以审核竞价");
        }
        
        // 获取竞价记录
        BiddingRecord biddingRecord = getBiddingRecordById(biddingId);
        
        // 只能审核待审核状态的竞价
        if (!"pending_audit".equals(biddingRecord.getAuditStatus())) {
            throw new BusinessException(400, "只能审核待审核状态的竞价");
        }
        
        // 更新审核状态
        biddingRecord.setAuditStatus(approved ? "approved" : "rejected");
        biddingRecord.setAuditRemark(remark);
        biddingRecord.setUpdatedAt(LocalDateTime.now());
        
        // 保存更新
        biddingRecordMapper.updateById(biddingRecord);
        
        // 发送审核通知
        sendBiddingAuditNotification(biddingRecord, approved);
        
        // 返回更新后的详情
        return convertToDetailResponse(biddingRecord);
    }

    /**
     * 获取待审核的竞价列表
     * 
     * @param page 当前页码
     * @param size 每页大小
     * @return 待审核竞价分页列表
     */
    @Override
    public IPage<BiddingDetailResponse> getPendingAuditBiddings(Integer page, Integer size) {
        // 验证当前用户是否为管理员
        if (!SecurityContextUtil.hasRole("admin")) {
            throw new BusinessException(403, "只有管理员可以查看待审核竞价");
        }
        
        // 创建分页对象
        Page<BiddingRecord> pageObj = new Page<>(page, size);
        
        // 构建查询条件
        LambdaQueryWrapper<BiddingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BiddingRecord::getAuditStatus, "pending_audit")
                    .eq(BiddingRecord::getDeleted, "0")
                    .orderByDesc(BiddingRecord::getCreatedAt);
        
        // 分页查询
        IPage<BiddingRecord> recordPage = biddingRecordMapper.selectPage(pageObj, queryWrapper);
        
        // 转换为详情响应对象
        return recordPage.convert(this::convertToDetailResponse);
    }

    /**
     * 获取可见的竞价列表（审核通过的竞价和自己的竞价）
     * 
     * @param requirementId 需求ID
     * @param page 当前页码
     * @param size 每页大小
     * @return 可见的竞价列表
     */
    @Override
    public IPage<BiddingDetailResponse> getVisibleBiddingsByRequirementId(Long requirementId, Integer page, Integer size) {
        // 创建分页对象
        Page<BiddingRecord> pageObj = new Page<>(page, size);
        
        // 获取当前用户信息
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        boolean isAdmin = SecurityContextUtil.hasRole("admin");
        
        // 构建查询条件
        LambdaQueryWrapper<BiddingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BiddingRecord::getRequirementId, requirementId)
                    .eq(BiddingRecord::getDeleted, "0");
        
        // 如果是管理员，可以看到所有竞价
        if (isAdmin) {
            // 管理员可以看到所有竞价，不需要额外条件
        } 
        // 如果是卖家，只能看到审核通过的竞价和自己的竞价
        else if (SecurityContextUtil.hasRole("seller")) {
            queryWrapper.and(wrapper -> wrapper
                .eq(BiddingRecord::getAuditStatus, "approved")
                .or()
                .eq(BiddingRecord::getSellerId, currentUserId)
            );
        } 
        // 如果是买家，只能看到审核通过的竞价
        else {
            queryWrapper.eq(BiddingRecord::getAuditStatus, "approved");
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(BiddingRecord::getCreatedAt);
        
        // 分页查询
        IPage<BiddingRecord> recordPage = biddingRecordMapper.selectPage(pageObj, queryWrapper);
        
        // 转换为详情响应对象
        return recordPage.convert(this::convertToDetailResponse);
    }

    // ==================== 通知发送方法 ====================
    
    /**
     * 发送普通竞价提交通知
     */
    @Async("taskExecutor")
    private void sendRegularBiddingSubmitNotifications(BiddingRecord biddingRecord) {
        try {
            // 获取需求信息
            PurchaseRequirement requirement = purchaseRequirementMapper.selectById(biddingRecord.getRequirementId());
            if (requirement == null) {
                return;
            }
            
            // 通知买家有新的竞价
            Notification buyerNotification = new Notification();
            buyerNotification.setType(NotificationType.BIDDING_SUBMIT.getCode());
            buyerNotification.setTitle("您有新的竞价");
            buyerNotification.setContent("您发布的需求收到了新的竞价");
            buyerNotification.setReceiverId(requirement.getBuyerId());
            buyerNotification.setReceiverRole("buyer");
            buyerNotification.setRelatedId(biddingRecord.getId());
            notificationService.createNotification(buyerNotification);
            
            // 通知所有管理员有新的竞价待审核
            List<User> adminUsers = userMapper.selectAllAdmins();
            for (User admin : adminUsers) {
                Notification adminNotification = new Notification();
                adminNotification.setType(NotificationType.BIDDING_SUBMIT.getCode());
                adminNotification.setTitle("有新的竞价待审核");
                adminNotification.setContent("有新的竞价需要审核");
                adminNotification.setReceiverId(admin.getId());
                adminNotification.setReceiverRole("admin");
                adminNotification.setRelatedId(biddingRecord.getId());
                notificationService.createNotification(adminNotification);
            }
        } catch (Exception e) {
            // 记录日志但不影响主流程
            System.err.println("发送普通竞价提交通知失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送样品竞价提交通知
     */
    @Async("taskExecutor")
    private void sendSampleBiddingSubmitNotifications(BiddingRecord biddingRecord) {
        try {
            // 获取需求信息
            PurchaseRequirement requirement = purchaseRequirementMapper.selectById(biddingRecord.getRequirementId());
            if (requirement == null) {
                return;
            }
            
            // 通知买家有新的样品竞价
            Notification buyerNotification = new Notification();
            buyerNotification.setType(NotificationType.SAMPLE_BIDDING_SUBMIT.getCode());
            buyerNotification.setTitle("您有新的样品竞价");
            buyerNotification.setContent("您发布的样品需求收到了新的竞价");
            buyerNotification.setReceiverId(requirement.getBuyerId());
            buyerNotification.setReceiverRole("buyer");
            buyerNotification.setRelatedId(biddingRecord.getId());
            notificationService.createNotification(buyerNotification);
            
            // 通知所有管理员有新的样品竞价待处理
            List<User> adminUsers = userMapper.selectAllAdmins();
            for (User admin : adminUsers) {
                Notification adminNotification = new Notification();
                adminNotification.setType(NotificationType.SAMPLE_BIDDING_SUBMIT.getCode());
                adminNotification.setTitle("有新的样品竞价待处理");
                adminNotification.setContent("有新的样品竞价需要关注");
                adminNotification.setReceiverId(admin.getId());
                adminNotification.setReceiverRole("admin");
                adminNotification.setRelatedId(biddingRecord.getId());
                notificationService.createNotification(adminNotification);
            }
        } catch (Exception e) {
            // 记录日志但不影响主流程
            System.err.println("发送样品竞价提交通知失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送普通竞价接受通知
     */
    @Async("taskExecutor")
    private void sendRegularBiddingAcceptedNotification(BiddingRecord biddingRecord) {
        try {
            Notification notification = new Notification();
            notification.setType(NotificationType.BIDDING_ACCEPTED.getCode());
            notification.setTitle("您的竞价已被接受");
            notification.setContent("恭喜！您的竞价已被买家接受");
            notification.setReceiverId(biddingRecord.getSellerId());
            notification.setReceiverRole("seller");
            notification.setRelatedId(biddingRecord.getId());
            notificationService.createNotification(notification);
        } catch (Exception e) {
            // 记录日志但不影响主流程
            System.err.println("发送普通竞价接受通知失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送样品竞价接受通知
     */
    @Async("taskExecutor")
    private void sendSampleBiddingAcceptedNotification(BiddingRecord biddingRecord) {
        try {
            Notification notification = new Notification();
            notification.setType(NotificationType.SAMPLE_BIDDING_ACCEPTED.getCode());
            notification.setTitle("您的样品竞价已被接受");
            notification.setContent("恭喜！您的样品竞价已被买家接受");
            notification.setReceiverId(biddingRecord.getSellerId());
            notification.setReceiverRole("seller");
            notification.setRelatedId(biddingRecord.getId());
            notificationService.createNotification(notification);
        } catch (Exception e) {
            // 记录日志但不影响主流程
            System.err.println("发送样品竞价接受通知失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送普通竞价拒绝通知
     */
    @Async("taskExecutor")
    private void sendRegularBiddingRejectedNotification(BiddingRecord biddingRecord, String reason) {
        try {
            Notification notification = new Notification();
            notification.setType(NotificationType.BIDDING_REJECTED.getCode());
            notification.setTitle("您的竞价已被拒绝");
            String content = "很遗憾，您的竞价已被买家拒绝";
            if (reason != null && !reason.trim().isEmpty()) {
                content += "，拒绝原因：" + reason;
            }
            notification.setContent(content);
            notification.setReceiverId(biddingRecord.getSellerId());
            notification.setReceiverRole("seller");
            notification.setRelatedId(biddingRecord.getId());
            notificationService.createNotification(notification);
        } catch (Exception e) {
            // 记录日志但不影响主流程
            System.err.println("发送普通竞价拒绝通知失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送样品竞价拒绝通知
     */
    @Async("taskExecutor")
    private void sendSampleBiddingRejectedNotification(BiddingRecord biddingRecord, String reason) {
        try {
            Notification notification = new Notification();
            notification.setType(NotificationType.SAMPLE_BIDDING_REJECTED.getCode());
            notification.setTitle("您的样品竞价已被拒绝");
            String content = "很遗憾，您的样品竞价已被买家拒绝";
            if (reason != null && !reason.trim().isEmpty()) {
                content += "，拒绝原因：" + reason;
            }
            notification.setContent(content);
            notification.setReceiverId(biddingRecord.getSellerId());
            notification.setReceiverRole("seller");
            notification.setRelatedId(biddingRecord.getId());
            notificationService.createNotification(notification);
        } catch (Exception e) {
            // 记录日志但不影响主流程
            System.err.println("发送样品竞价拒绝通知失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送竞价审核通知
     */
    @Async("taskExecutor")
    private void sendBiddingAuditNotification(BiddingRecord biddingRecord, boolean approved) {
        try {
            // 审核通过时通知竞价提交者
            if (approved) {
                Notification notification = new Notification();
                notification.setType(NotificationType.BIDDING_AUDIT.getCode());
                notification.setTitle("您的竞价已通过审核");
                notification.setContent("您的竞价已通过审核");
                notification.setReceiverId(biddingRecord.getSellerId());
                notification.setReceiverRole("seller");
                notification.setRelatedId(biddingRecord.getId());
                notificationService.createNotification(notification);

                // 新增：通知需求发布者（买家）
                com.purchase.requirement.vo.RequirementVO requirementVOOrigin = purchaseRequirementService.getRequirementById(biddingRecord.getRequirementId());
                if (requirementVOOrigin != null && requirementVOOrigin.getBuyerId() != null) {
                    // 转换为bidding模块的VO
                    RequirementVO requirementVO = convertToRequirementVOList(Collections.singletonList(requirementVOOrigin)).get(0);
                    Notification buyerNotification = new Notification();
                    buyerNotification.setType(NotificationType.BIDDING_AUDIT.getCode());
                    buyerNotification.setTitle("您的需求有竞价已通过审核");
                    buyerNotification.setContent("您发布的需求有新的竞价已通过审核");
                    buyerNotification.setReceiverId(requirementVO.getBuyerId());
                    buyerNotification.setReceiverRole("buyer");
                    buyerNotification.setRelatedId(biddingRecord.getId());
                    notificationService.createNotification(buyerNotification);
                }
            }
        } catch (Exception e) {
            // 记录日志但不影响主流程
            System.err.println("发送竞价审核通知失败: " + e.getMessage());
        }
    }

    /**
     * 验证需求归属权限
     * 确保买家只能操作自己的需求
     *
     * @param requirementId 需求ID
     * @param buyerId 买家ID
     * @throws BusinessException 如果需求不属于该买家
     */
    @Override
    public void validateRequirementOwnership(Long requirementId, Long buyerId) {
        PurchaseRequirement requirement = purchaseRequirementMapper.selectById(requirementId);
        if (requirement == null) {
            throw new BusinessException(404, "需求不存在");
        }
        if (!Objects.equals(requirement.getBuyerId(), buyerId)) {
            throw new BusinessException(403, "无权访问该需求，您只能操作自己发布的需求");
        }
    }

    /**
     * 验证竞价归属权限
     * 确保买家只能操作自己需求下的竞价
     *
     * @param biddingId 竞价ID
     * @param buyerId 买家ID
     * @throws BusinessException 如果竞价不属于该买家的需求
     */
    @Override
    public void validateBiddingOwnership(Long biddingId, Long buyerId) {
        BiddingRecord bidding = biddingRecordMapper.selectById(biddingId);
        if (bidding == null) {
            throw new BusinessException(404, "竞价不存在");
        }

        // 验证该竞价对应的需求是否属于当前买家
        validateRequirementOwnership(bidding.getRequirementId(), buyerId);
    }
}