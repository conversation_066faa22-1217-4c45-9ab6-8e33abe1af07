package com.purchase.bidding.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.purchase.bidding.entity.BiddingRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

public interface BiddingRecordMapper extends BaseMapper<BiddingRecord> {
    @Select("SELECT COUNT(*) FROM bidding_record WHERE requirement_id = #{requirementId} AND deleted = '0'")
    Long countByRequirementId(@Param("requirementId") Long requirementId);

    @Select("SELECT COUNT(*) FROM bidding_record WHERE requirement_id = #{requirementId} AND audit_status = 'approved' AND deleted = '0'")
    Long countApprovedByRequirementId(@Param("requirementId") Long requirementId);

    @Select("SELECT COUNT(*) FROM bidding_record WHERE requirement_id = #{requirementId} AND status = #{status} AND deleted = '0'")
    Long countByRequirementIdAndStatus(@Param("requirementId") Long requirementId, @Param("status") String status);
    
    @Select("SELECT MIN(bidding_price) FROM bidding_record WHERE requirement_id = #{requirementId} AND deleted = '0'")
    BigDecimal findMinPrice(@Param("requirementId") Long requirementId);
    
    @Select("SELECT MAX(bidding_price) FROM bidding_record WHERE requirement_id = #{requirementId} AND deleted = '0'")
    BigDecimal findMaxPrice(@Param("requirementId") Long requirementId);
    
    @Select("SELECT AVG(bidding_price) FROM bidding_record WHERE requirement_id = #{requirementId} AND deleted = '0'")
    BigDecimal findAvgPrice(@Param("requirementId") Long requirementId);
    
    @Select("SELECT * FROM bidding_record WHERE requirement_id = #{requirementId} AND deleted = '0'")
    List<BiddingRecord> findByRequirementId(@Param("requirementId") Long requirementId);
    
    /**
     * 根据买家ID查询所有的需求ID
     * 需要联表查询，从purchase_requirement表中获取买家关联的需求
     *
     * @param buyerId 买家ID
     * @return 需求ID列表
     */
    @Select("SELECT id FROM purchase_requirement WHERE buyer_id = #{buyerId} AND deleted = '0'")
    List<Long> findRequirementIdsByBuyerId(@Param("buyerId") Long buyerId);
    
    /**
     * 分页查询需求的已审核通过的样品竞价列表
     * 
     * @param requirementId 需求ID
     * @param status 竞价状态（可选）
     * @return 竞价记录列表
     */
    @Select({
        "<script>",
        "SELECT * FROM bidding_record",
        "WHERE requirement_id = #{requirementId}",
        "AND bidding_type = 'sample'",
        "AND audit_status = 'approved'",
        "AND deleted = '0'",
        "<if test='status != null and status != \"\"'>",
        "AND status = #{status}",
        "</if>",
        "ORDER BY created_at DESC",
        "</script>"
    })
    IPage<BiddingRecord> selectApprovedSampleBiddingsByRequirement(
            Page<BiddingRecord> page,
            @Param("requirementId") Long requirementId,
            @Param("status") String status);
}