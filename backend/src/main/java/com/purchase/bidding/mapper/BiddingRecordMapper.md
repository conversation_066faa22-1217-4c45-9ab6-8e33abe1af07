## 类用途
竞价记录映射器，处理BiddingRecord实体与DTO之间的转换

## 核心方法
### BiddingRecordDTO entityToDto(BiddingRecord entity)
- 参数：实体对象
- 返回：DTO对象
- 映射规则：
  - entity.id → dto.biddingId
  - entity.amount → dto.quotedAmount
  - entity.status → dto.status
  - 自定义字段处理：
    - 金额单位：分→元
    - 状态枚举转换

### BiddingRecord dtoToEntity(BiddingRecordDTO dto)
- 参数：DTO对象
- 返回：实体对象
- 映射规则：
  - dto.biddingId → entity.id
  - dto.quotedAmount → entity.amount
  - dto.status → entity.status
  - 反向处理：
    - 元→分
    - 状态枚举反向转换

## 特殊处理
```java
// 状态枚举转换示例
default String mapStatus(BiddingStatus status) {
    switch(status) {
        case PENDING: return "待处理";
        case APPROVED: return "已批准";
        default: return status.name();
    }
}
```

## 使用示例
```java
// 实体转DTO示例
BiddingRecord entity = biddingRecordRepository.findById("bid123");
BiddingRecordDTO dto = biddingRecordMapper.entityToDto(entity);

// DTO转实体示例
BiddingRecordDTO dto = new BiddingRecordDTO(...);
BiddingRecord entity = biddingRecordMapper.dtoToEntity(dto);
```

## 注意事项
- 敏感字段需脱敏处理
- 生产环境建议添加缓存
- 批量转换时需优化性能