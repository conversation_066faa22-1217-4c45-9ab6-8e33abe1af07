# Bidding 竞价模块文档

## 模块概述

竞价模块是采购系统的核心业务模块之一，负责处理卖家对采购需求的竞价投标业务。该模块支持普通竞价和样品竞价两种模式，提供完整的竞价生命周期管理，包括提交、审核、选择中标者等功能。

## 目录结构概览

```
com.purchase.bidding/
├── controller/                    # 控制器层
│   ├── BiddingController.java           # 竞价主控制器
│   ├── SampleBiddingController.java     # 样品竞价控制器
│   ├── AdminSampleBiddingController.java # 管理员样品竞价控制器
│   └── AdminSampleBiddingTestController.java # 测试控制器
├── dto/                          # 数据传输对象
│   ├── BiddingRecordDTO.java           # 竞价记录DTO
│   ├── request/                        # 请求DTO
│   │   ├── BiddingSubmitRequest.java       # 竞价提交请求
│   │   ├── BiddingUpdateRequest.java       # 竞价更新请求
│   │   ├── BiddingCancelRequest.java       # 竞价取消请求
│   │   ├── SampleBiddingSubmitRequest.java # 样品竞价提交请求
│   │   ├── AdminSampleBiddingAuditRequest.java # 管理员审核请求
│   │   └── AdminSampleBiddingStatusUpdateRequest.java # 状态更新请求
│   └── response/                       # 响应DTO
│       ├── BiddingDetailResponse.java      # 竞价详情响应
│       ├── BiddingStatsResponse.java       # 竞价统计响应
│       └── AdminSampleBiddingStatsResponse.java # 管理员统计响应
├── entity/                       # 实体类
│   └── BiddingRecord.java              # 竞价记录实体
├── mapper/                       # 数据访问层
│   └── BiddingRecordMapper.java        # 竞价记录Mapper
├── service/                      # 服务层
│   ├── BiddingService.java             # 竞价服务接口
│   ├── SampleBiddingService.java       # 样品竞价服务接口
│   ├── AdminSampleBiddingService.java  # 管理员样品竞价服务接口
│   └── impl/                           # 服务实现
│       ├── BiddingServiceImpl.java         # 竞价服务实现
│       ├── SampleBiddingServiceImpl.java   # 样品竞价服务实现
│       └── AdminSampleBiddingServiceImpl.java # 管理员服务实现
└── vo/                          # 视图对象
    ├── RequirementVO.java              # 需求视图对象
    └── RequirementPageVO.java          # 需求分页视图对象
```

## 核心功能详述

### 1. 竞价管理 (BiddingController & BiddingService)

#### 主要功能
- **竞价提交**: 卖家对采购需求提交竞价
- **竞价更新**: 修改已提交的竞价信息
- **竞价取消**: 取消自己的竞价
- **竞价查询**: 多维度查询竞价信息
- **中标管理**: 买家选择中标者、拒绝竞价

#### 核心文件
- `BiddingController.java`: REST API控制器，提供竞价相关接口
- `BiddingService.java`: 竞价业务服务接口
- `BiddingServiceImpl.java`: 竞价业务逻辑实现
- `BiddingRecord.java`: 竞价记录实体，支持普通竞价和样品竞价

#### 业务流程
1. **提交阶段**: 卖家提交竞价 → 系统验证 → 保存记录 → 待审核状态
2. **审核阶段**: 管理员审核 → 审核通过/拒绝 → 状态更新
3. **选择阶段**: 买家查看竞价 → 选择中标者 → 其他竞价自动拒绝
4. **完成阶段**: 生成订单 → 竞价流程结束

### 2. 样品竞价 (SampleBiddingController & SampleBiddingService)

#### 特殊功能
- **样品信息**: 样品价格、数量、规格说明
- **快速交付**: 通常7-14天内交付样品
- **详细规格**: 提供完整的样品技术规格
- **小批量**: 样品数量通常较少

#### 核心文件
- `SampleBiddingController.java`: 样品竞价控制器
- `SampleBiddingService.java`: 样品竞价服务接口
- `SampleBiddingSubmitRequest.java`: 样品竞价提交请求

### 3. 管理员功能 (AdminSampleBiddingController)

#### 管理功能
- **竞价审核**: 审核竞价记录的合规性
- **状态管理**: 更新竞价状态
- **数据统计**: 竞价数据分析和统计
- **系统管理**: 删除、修改竞价记录

#### 核心文件
- `AdminSampleBiddingController.java`: 管理员控制器
- `AdminSampleBiddingService.java`: 管理员服务接口

## 数据模型说明

### BiddingRecord 实体

#### 基础字段
- `id`: 主键ID
- `requirementId`: 关联需求ID
- `sellerId`: 卖家用户ID
- `biddingPrice`: 竞价报价
- `status`: 竞价状态
- `biddingType`: 竞价类型（purchase/sample）

#### 产品信息
- `productName`: 产品名称
- `hsCode`: 海关编码
- `unit`: 报价单位
- `images`: 产品图片
- `videos`: 产品视频
- `description`: 竞价描述

#### 样品特有字段
- `samplePrice`: 样品价格
- `sampleQuantity`: 样品数量
- `sampleSpecification`: 样品规格说明

#### 审核相关
- `auditStatus`: 审核状态
- `auditRemark`: 审核备注
- `winner`: 是否中标

## API 接口概览

### 竞价基础接口
- `POST /api/v1/biddings` - 提交竞价
- `PUT /api/v1/biddings/{id}` - 更新竞价
- `PUT /api/v1/biddings/{id}/cancel` - 取消竞价
- `GET /api/v1/biddings/{id}` - 获取竞价详情

### 竞价查询接口
- `GET /api/v1/requirements/{requirementId}/biddings` - 获取需求的竞价列表
- `GET /api/v1/requirements/{requirementId}/biddings/visible` - 获取可见竞价
- `GET /api/v1/biddings/my-requirements` - 获取我的竞价需求

### 中标管理接口
- `PUT /api/v1/biddings/{id}/accept` - 接受竞价（选择中标者）
- `PUT /api/v1/biddings/{id}/reject` - 拒绝竞价

### 管理员接口
- `GET /api/v1/biddings/admin/all` - 获取所有竞价
- `PUT /api/v1/biddings/admin/{id}/audit` - 审核竞价
- `GET /api/v1/biddings/admin/pending-audit` - 获取待审核竞价
- `GET /api/v1/requirements/{requirementId}/biddings/stats` - 获取统计信息

## 权限控制

### 角色权限
- **seller**: 可以提交、更新、取消自己的竞价
- **buyer**: 可以查看自己需求的竞价，选择中标者
- **admin**: 拥有所有权限，可以审核和管理竞价

### 数据访问控制
- 卖家只能操作自己的竞价
- 买家只能操作自己需求下的竞价
- 管理员可以访问所有数据
- 未审核的竞价对买家不可见

## 业务规则

### 竞价规则
1. 同一卖家对同一需求只能有一个有效竞价
2. 只有pending状态的竞价可以更新
3. 竞价需要审核通过才对买家可见
4. 选择中标者后，其他竞价自动变为rejected状态

### 样品竞价规则
1. 样品数量通常不超过100个
2. 样品交付时间通常在1-2周内
3. 必须提供详细的样品规格说明
4. 样品价格应合理，不能过高

### 审核规则
1. 新提交的竞价默认为待审核状态
2. 管理员可以审核通过或拒绝竞价
3. 审核拒绝需要提供拒绝原因
4. 审核通过的竞价才能参与中标选择

## 集成说明

### 与其他模块的关系
- **需求模块**: 竞价基于采购需求创建
- **用户模块**: 关联买家和卖家用户信息
- **订单模块**: 中标后生成采购订单
- **消息模块**: 发送竞价相关通知
- **佣金模块**: 中标后计算佣金

### 事件发布
- 竞价提交事件
- 竞价审核事件
- 中标选择事件
- 竞价状态变更事件

## 注意事项

### 开发注意事项
1. **数据一致性**: 使用事务确保数据一致性
2. **并发控制**: 防止并发操作导致的数据问题
3. **权限验证**: 每个操作都要进行权限检查
4. **状态管理**: 严格按照状态流转规则

### 性能优化
1. **分页查询**: 所有列表查询都支持分页
2. **索引优化**: 为查询字段建立合适索引
3. **缓存策略**: 对频繁查询的数据进行缓存
4. **异步处理**: 通知等非关键操作异步执行

### 安全考虑
1. **输入验证**: 严格验证所有用户输入
2. **权限控制**: 基于角色的访问控制
3. **数据脱敏**: 根据权限返回不同级别的数据
4. **操作日志**: 记录所有重要操作
