# SampleBiddingController.md

## 1. 文件概述

`SampleBiddingController.java` 是竞价模块中专门处理样品竞价的控制器，位于 `com.purchase.bidding.controller` 包中。它提供了与样品竞价相关的RESTful API接口，涵盖了样品竞价的提交、修改、取消、接受、拒绝以及查询等功能。该控制器通过依赖注入 `SampleBiddingService` 来执行具体的业务逻辑，并利用Spring Security的 `@PreAuthorize` 注解进行严格的权限控制，确保不同角色（买家、卖家、管理员）的用户只能执行其被授权的操作。

## 2. 核心功能

*   **样品竞价提交与修改**: 允许卖家提交新的样品竞价或修改其已提交的竞价。
*   **样品竞价取消**: 允许卖家取消其已提交的样品竞价。
*   **样品竞价接受与拒绝**: 允许买家接受或拒绝其需求下的样品竞价，并触发后续业务流程（如生成样品订单）。
*   **样品竞价列表查询**: 提供多维度查询接口，支持按需求ID、竞价状态、卖家ID等条件，分页获取样品竞价列表。并根据用户角色进行权限控制，确保数据隔离。
*   **样品竞价详情查询**: 支持根据竞价ID获取单个样品竞价的详细信息。
*   **批量接受竞价**: 允许买家批量接受多个样品竞价，提高操作效率。
*   **竞价验证**: 提供接口验证样品竞价的有效性。
*   **统一响应**: 所有接口都返回一个标准化的 `Result` 对象，统一了成功和失败的响应格式。

## 3. 接口说明

### 3.1 卖家操作接口

#### submitSampleBidding - 卖家提交样品竞价
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/sample-biddings/submit`
*   **权限**: `hasAuthority('seller')`
*   **参数**:
    *   `request` (SampleBiddingSubmitRequest, body, required): 包含样品竞价信息的请求体。
*   **返回值**: `Result<BiddingDetailResponse>` - 提交成功的竞价详情。
*   **业务逻辑**: 获取当前卖家ID，然后调用 `sampleBiddingService.submitSampleBidding`。

#### updateSampleBidding - 卖家修改样品竞价
*   **HTTP方法**: `PUT`
*   **路径**: `/api/v1/sample-biddings/{biddingId}`
*   **权限**: `hasAuthority('seller')`
*   **参数**:
    *   `biddingId` (Long, path, required): 竞价ID。
    *   `request` (SampleBiddingSubmitRequest, body, required): 包含更新信息的请求体。
*   **返回值**: `Result<BiddingDetailResponse>` - 修改后的竞价详情。
*   **业务逻辑**: 获取当前卖家ID，然后调用 `sampleBiddingService.updateSampleBidding`。

#### cancelSampleBidding - 卖家取消样品竞价
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/sample-biddings/{biddingId}/cancel`
*   **权限**: `hasAuthority('seller')`
*   **参数**:
    *   `biddingId` (Long, path, required): 竞价ID。
    *   `reason` (String, query, optional): 取消原因。
*   **返回值**: `Result<BiddingDetailResponse>` - 取消后的竞价详情。
*   **业务逻辑**: 获取当前卖家ID，然后调用 `sampleBiddingService.cancelSampleBidding`。

#### getSellerSampleBiddings - 卖家获取自己的样品竞价列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/sample-biddings/seller`
*   **权限**: `hasAuthority('seller')`
*   **参数**:
    *   `page` (Integer, query, optional, default=1): 页码。
    *   `size` (Integer, query, optional, default=10): 每页大小。
    *   `status` (String, query, optional): 竞价状态。
*   **返回值**: `Result<IPage<BiddingDetailResponse>>` - 样品竞价分页列表。
*   **业务逻辑**: 获取当前卖家ID，然后调用 `sampleBiddingService.getSellerSampleBiddings`。

### 3.2 买家操作接口

#### acceptSampleBidding - 买家接受样品竞价
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/sample-biddings/{biddingId}/accept`
*   **权限**: `hasAuthority('buyer')`
*   **参数**:
    *   `biddingId` (Long, path, required): 竞价ID。
*   **返回值**: `Result<BiddingDetailResponse>` - 接受后的竞价详情。
*   **业务逻辑**: 获取当前买家ID，然后调用 `sampleBiddingService.acceptSampleBidding`。

#### rejectSampleBidding - 买家拒绝样品竞价
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/sample-biddings/{biddingId}/reject`
*   **权限**: `hasAuthority('buyer')`
*   **参数**:
    *   `biddingId` (Long, path, required): 竞价ID。
    *   `reason` (String, query, optional): 拒绝原因。
*   **返回值**: `Result<BiddingDetailResponse>` - 拒绝后的竞价详情。
*   **业务逻辑**: 获取当前买家ID，然后调用 `sampleBiddingService.rejectSampleBidding`。

#### batchAcceptSampleBiddings - 买家批量接受样品竞价
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/sample-biddings/batch-accept`
*   **权限**: `hasAuthority('buyer')`
*   **参数**:
    *   `biddingIds` (List<Long>, body, required): 竞价ID列表。
*   **返回值**: `Result<List<BiddingDetailResponse>>` - 批量接受后的竞价详情列表。
*   **业务逻辑**: 获取当前买家ID，然后调用 `sampleBiddingService.batchAcceptSampleBiddings`。

### 3.3 通用查询接口

#### getSampleBiddingsByRequirement - 获取需求的样品竞价列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/sample-biddings/requirement/{requirementId}`
*   **权限**: `hasAnyAuthority('buyer', 'seller')`
*   **参数**:
    *   `requirementId` (Long, path, required): 需求ID。
    *   `page` (Integer, query, optional, default=1): 页码。
    *   `size` (Integer, query, optional, default=10): 每页大小。
    *   `status` (String, query, optional): 竞价状态。
*   **返回值**: `Result<IPage<BiddingDetailResponse>>` - 样品竞价分页列表。
*   **业务逻辑**: 调用 `sampleBiddingService.getSampleBiddingsByRequirement`。

#### getAcceptedSampleBiddings - 获取需求的已接受样品竞价列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/sample-biddings/requirement/{requirementId}/accepted`
*   **权限**: `hasAnyAuthority('buyer', 'admin')`
*   **参数**:
    *   `requirementId` (Long, path, required): 需求ID。
*   **返回值**: `Result<List<BiddingDetailResponse>>` - 已接受的样品竞价列表。
*   **业务逻辑**: 调用 `sampleBiddingService.getAcceptedSampleBiddings`。

#### getSampleBiddingDetail - 获取样品竞价详情
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/sample-biddings/{biddingId}`
*   **权限**: `hasAnyAuthority('buyer', 'seller', 'admin')`
*   **参数**:
    *   `biddingId` (Long, path, required): 竞价ID。
*   **返回值**: `Result<BiddingDetailResponse>` - 样品竞价详情。
*   **业务逻辑**: 调用 `sampleBiddingService.getSampleBiddingDetail`。

#### validateSampleBidding - 验证样品竞价
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/sample-biddings/{biddingId}/validate`
*   **权限**: `hasAnyAuthority('buyer', 'seller', 'admin')`
*   **参数**:
    *   `biddingId` (Long, path, required): 竞价ID。
    *   `requirementId` (Long, query, required): 需求ID。
*   **返回值**: `Result<Boolean>` - 验证结果。
*   **业务逻辑**: 调用 `sampleBiddingService.validateSampleBidding`。

## 4. 业务规则

*   **权限控制**: 所有接口都通过 `@PreAuthorize` 注解进行严格的权限控制，确保不同角色用户只能访问其被授权的资源和操作。
*   **竞价生命周期**: 样品竞价的状态流转（提交、修改、取消、接受、拒绝）应遵循预定义的业务流程和状态机。
*   **数据隔离**: 查询接口会根据用户角色返回不同权限的数据，例如卖家只能查看自己提交的竞价，买家只能查看自己需求下的竞价。
*   **JSR-303校验**: 请求体中的DTO通过 `@Valid` 注解进行JSR-303校验，确保输入数据的合法性。
*   **统一响应**: 所有接口都返回 `Result` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **安全上下文**: 控制器通过 `SecurityContextUtil.getCurrentUserId()` 获取当前登录用户ID，确保操作的归属和权限。

## 5. 使用示例

```java
// 1. 卖家提交样品竞价
async function submitSampleBidding(biddingData) {
  try {
    const response = await axios.post('/api/v1/sample-biddings/submit', biddingData, {
      headers: { Authorization: `Bearer ${sellerToken}` }
    });
    console.log('样品竞价提交成功:', response.data.data);
  } catch (error) {
    console.error('样品竞价提交失败:', error.response.data);
  }
}

// 2. 买家获取其需求下的样品竞价列表
async function fetchSampleBiddingsForRequirement(requirementId) {
  try {
    const response = await axios.get(`/api/v1/sample-biddings/requirement/${requirementId}`, {
      params: { page: 1, size: 10, status: 'PENDING' },
      headers: { Authorization: `Bearer ${buyerToken}` }
    });
    console.log('样品竞价列表:', response.data.data);
  } catch (error) {
    console.error('获取样品竞价列表失败:', error.response.data);
  }
}

// 3. 买家接受某个样品竞价
async function acceptSampleBidding(biddingId) {
  try {
    const response = await axios.post(`/api/v1/sample-biddings/${biddingId}/accept`, null, {
      headers: { Authorization: `Bearer ${buyerToken}` }
    });
    console.log('样品竞价接受成功:', response.data.data);
  } catch (error) {
    console.error('样品竞价接受失败:', error.response.data);
  }
}

// 4. 测试示例 (MockMvc)
@SpringBootTest
@AutoConfigureMockMvc
class SampleBiddingControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SampleBiddingService sampleBiddingService;

    @Test
    @WithMockUser(authorities = "seller", username = "testSeller")
    void testSubmitSampleBidding_Success() throws Exception {
        SampleBiddingSubmitRequest request = new SampleBiddingSubmitRequest();
        request.setRequirementId(1L);
        request.setSamplePrice(new BigDecimal("10.00"));
        // ... set other fields ...

        BiddingDetailResponse mockResponse = new BiddingDetailResponse();
        when(sampleBiddingService.submitSampleBidding(any(SampleBiddingSubmitRequest.class), anyLong()))
            .thenReturn(mockResponse);

        mockMvc.perform(post("/api/v1/sample-biddings/submit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("样品竞价提交成功"));
    }

    @Test
    @WithMockUser(authorities = "buyer", username = "testBuyer")
    void testAcceptSampleBidding_Success() throws Exception {
        Long biddingId = 123L;
        BiddingDetailResponse mockResponse = new BiddingDetailResponse();
        when(sampleBiddingService.acceptSampleBidding(eq(biddingId), anyLong())).thenReturn(mockResponse);

        mockMvc.perform(post("/api/v1/sample-biddings/{biddingId}/accept", biddingId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("样品竞价接受成功"));
    }

    @Test
    @WithMockUser(authorities = "user") // 非授权用户
    void testSubmitSampleBidding_AccessDenied() throws Exception {
        SampleBiddingSubmitRequest request = new SampleBiddingSubmitRequest();
        // ...
        mockMvc.perform(post("/api/v1/sample-biddings/submit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }
}
```

## 6. 注意事项

*   **权限控制**: 控制器中大量使用了 `@PreAuthorize` 进行权限控制，这是系统安全的核心。在实际部署时，需要确保Spring Security配置正确，并且JWT或Session管理机制健全。
*   **业务异常**: 控制器捕获了服务层可能抛出的业务异常，并返回统一的错误响应，这使得前端可以根据错误信息进行相应的提示。
*   **参数校验**: 请求体中的DTO通过 `@Valid` 注解进行JSR-303校验，确保输入数据的合法性。
*   **统一响应**: 所有接口都返回 `Result` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **职责分离**: 控制器仅负责请求的接收、参数的初步校验和转发，具体的业务逻辑和数据处理委托给 `SampleBiddingService`。
*   **安全上下文**: 控制器通过 `SecurityContextUtil.getCurrentUserId()` 获取当前登录用户ID，确保操作的归属和权限。
*   **Swagger集成**: 使用 `@Api`, `@ApiOperation`, `@ApiParam` 等Swagger注解，有助于自动生成和维护API文档，提高了API的可用性。
*   **状态流转**: 竞价的接受、拒绝、取消等操作会触发竞价状态的变更，服务层需要确保这些状态流转符合业务逻辑。
