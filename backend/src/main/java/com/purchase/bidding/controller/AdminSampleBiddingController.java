package com.purchase.bidding.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.common.response.Result;
import com.purchase.bidding.dto.request.AdminSampleBiddingAuditRequest;
import com.purchase.bidding.dto.request.AdminSampleBiddingStatusUpdateRequest;
import com.purchase.bidding.dto.response.BiddingDetailResponse;
import com.purchase.bidding.dto.response.AdminSampleBiddingStatsResponse;
import com.purchase.bidding.service.AdminSampleBiddingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 管理员样品竞价管理控制器
 * 提供管理员对样品竞价的全面管理功能
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/admin/sample-biddings")
@Api(tags = "管理员样品竞价管理")
public class AdminSampleBiddingController {
    
    @Resource
    private AdminSampleBiddingService adminSampleBiddingService;
    
    /**
     * 获取所有样品竞价列表（管理员视图）
     */
    @GetMapping
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("获取所有样品竞价列表")
    public Result<IPage<BiddingDetailResponse>> getAllSampleBiddings(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("竞价状态") @RequestParam(required = false) String status,
            @ApiParam("审核状态") @RequestParam(required = false) String auditStatus,
            @ApiParam("卖家ID") @RequestParam(required = false) Long sellerId,
            @ApiParam("买家ID") @RequestParam(required = false) Long buyerId,
            @ApiParam("需求ID") @RequestParam(required = false) Long requirementId,
            @ApiParam("最低样品价格") @RequestParam(required = false) BigDecimal minSamplePrice,
            @ApiParam("最高样品价格") @RequestParam(required = false) BigDecimal maxSamplePrice,
            @ApiParam("产品名称") @RequestParam(required = false) String productName,
            @ApiParam("公司名称") @RequestParam(required = false) String company,
            @ApiParam("创建开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @ApiParam("创建结束时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        log.info("管理员获取样品竞价列表，页码: {}, 大小: {}, 状态: {}, 审核状态: {}", 
                page, size, status, auditStatus);
        
        IPage<BiddingDetailResponse> biddings = adminSampleBiddingService.getAllSampleBiddings(
            page, size, status, auditStatus, sellerId, buyerId, requirementId,
            minSamplePrice, maxSamplePrice, productName, company, startDate, endDate);
        
        return Result.success(biddings);
    }
    
    /**
     * 获取待审核的样品竞价列表
     */
    @GetMapping("/pending-audit")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("获取待审核的样品竞价列表")
    public Result<IPage<BiddingDetailResponse>> getPendingAuditSampleBiddings(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size) {
        
        log.info("管理员获取待审核样品竞价列表，页码: {}, 大小: {}", page, size);
        
        IPage<BiddingDetailResponse> biddings = adminSampleBiddingService.getPendingAuditSampleBiddings(page, size);
        
        return Result.success(biddings);
    }
    
    /**
     * 获取已审核通过的样品竞价列表
     */
    @GetMapping("/approved")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("获取已审核通过的样品竞价列表")
    public Result<IPage<BiddingDetailResponse>> getApprovedSampleBiddings(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("竞价状态") @RequestParam(required = false) String status) {
        
        log.info("管理员获取已审核通过样品竞价列表，页码: {}, 大小: {}, 状态: {}", page, size, status);
        
        IPage<BiddingDetailResponse> biddings = adminSampleBiddingService.getApprovedSampleBiddings(page, size, status);
        
        return Result.success(biddings);
    }
    
    /**
     * 获取已拒绝审核的样品竞价列表
     */
    @GetMapping("/rejected")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("获取已拒绝审核的样品竞价列表")
    public Result<IPage<BiddingDetailResponse>> getRejectedSampleBiddings(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size) {
        
        log.info("管理员获取已拒绝审核样品竞价列表，页码: {}, 大小: {}", page, size);
        
        IPage<BiddingDetailResponse> biddings = adminSampleBiddingService.getRejectedSampleBiddings(page, size);
        
        return Result.success(biddings);
    }
    
    /**
     * 获取样品竞价详情（管理员视图）
     */
    @GetMapping("/{biddingId}")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("获取样品竞价详情")
    public Result<BiddingDetailResponse> getSampleBiddingDetail(
            @ApiParam("竞价ID") @PathVariable Long biddingId) {
        
        log.info("管理员查询样品竞价详情，竞价ID: {}", biddingId);
        
        BiddingDetailResponse bidding = adminSampleBiddingService.getSampleBiddingDetail(biddingId);
        
        return Result.success(bidding);
    }
    
    /**
     * 审核样品竞价
     */
    @PostMapping("/{biddingId}/audit")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("审核样品竞价")
    public Result<BiddingDetailResponse> auditSampleBidding(
            @ApiParam("竞价ID") @PathVariable Long biddingId,
            @Valid @RequestBody AdminSampleBiddingAuditRequest request) {
        
        log.info("管理员审核样品竞价，竞价ID: {}, 审核结果: {}", biddingId, request.getApproved());
        
        BiddingDetailResponse bidding = adminSampleBiddingService.auditSampleBidding(
            biddingId, request.getApproved(), request.getAuditRemark());
        
        return Result.success("样品竞价审核成功", bidding);
    }
    
    /**
     * 批量审核样品竞价
     */
    @PostMapping("/batch-audit")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("批量审核样品竞价")
    public Result<List<BiddingDetailResponse>> batchAuditSampleBiddings(
            @ApiParam("竞价ID列表") @RequestParam List<Long> biddingIds,
            @ApiParam("是否审核通过") @RequestParam Boolean approved,
            @ApiParam("审核备注") @RequestParam(required = false) String auditRemark) {
        
        log.info("管理员批量审核样品竞价，竞价数量: {}, 审核结果: {}", biddingIds.size(), approved);
        
        List<BiddingDetailResponse> biddings = adminSampleBiddingService.batchAuditSampleBiddings(
            biddingIds, approved, auditRemark);
        
        return Result.success("批量审核样品竞价成功", biddings);
    }
    
    /**
     * 更新样品竞价状态
     */
    @PutMapping("/{biddingId}/status")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("更新样品竞价状态")
    public Result<BiddingDetailResponse> updateSampleBiddingStatus(
            @ApiParam("竞价ID") @PathVariable Long biddingId,
            @Valid @RequestBody AdminSampleBiddingStatusUpdateRequest request) {
        
        log.info("管理员更新样品竞价状态，竞价ID: {}, 新状态: {}", biddingId, request.getStatus());
        
        BiddingDetailResponse bidding = adminSampleBiddingService.updateSampleBiddingStatus(
            biddingId, request.getStatus(), request.getStatusRemark());
        
        return Result.success("样品竞价状态更新成功", bidding);
    }
    
    /**
     * 强制接受样品竞价（管理员权限）
     */
    @PostMapping("/{biddingId}/force-accept")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("强制接受样品竞价")
    public Result<BiddingDetailResponse> forceAcceptSampleBidding(
            @ApiParam("竞价ID") @PathVariable Long biddingId,
            @ApiParam("操作原因") @RequestParam(required = false) String reason) {
        
        log.info("管理员强制接受样品竞价，竞价ID: {}", biddingId);
        
        BiddingDetailResponse bidding = adminSampleBiddingService.forceAcceptSampleBidding(biddingId, reason);
        
        return Result.success("样品竞价强制接受成功", bidding);
    }
    
    /**
     * 强制拒绝样品竞价（管理员权限）
     */
    @PostMapping("/{biddingId}/force-reject")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("强制拒绝样品竞价")
    public Result<BiddingDetailResponse> forceRejectSampleBidding(
            @ApiParam("竞价ID") @PathVariable Long biddingId,
            @ApiParam("拒绝原因") @RequestParam String reason) {
        
        log.info("管理员强制拒绝样品竞价，竞价ID: {}", biddingId);
        
        BiddingDetailResponse bidding = adminSampleBiddingService.forceRejectSampleBidding(biddingId, reason);
        
        return Result.success("样品竞价强制拒绝成功", bidding);
    }
    
    /**
     * 删除样品竞价（逻辑删除）
     */
    @DeleteMapping("/{biddingId}")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("删除样品竞价")
    public Result<Void> deleteSampleBidding(
            @ApiParam("竞价ID") @PathVariable Long biddingId,
            @ApiParam("删除原因") @RequestParam(required = false) String reason) {
        
        log.info("管理员删除样品竞价，竞价ID: {}", biddingId);
        
        adminSampleBiddingService.deleteSampleBidding(biddingId, reason);
        
        return Result.success("样品竞价删除成功", null);
    }
    
    /**
     * 批量删除样品竞价
     */
    @PostMapping("/batch-delete")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("批量删除样品竞价")
    public Result<Void> batchDeleteSampleBiddings(
            @ApiParam("竞价ID列表") @RequestBody List<Long> biddingIds,
            @ApiParam("删除原因") @RequestParam(required = false) String reason) {
        
        log.info("管理员批量删除样品竞价，竞价数量: {}", biddingIds.size());
        
        adminSampleBiddingService.batchDeleteSampleBiddings(biddingIds, reason);
        
        return Result.success("批量删除样品竞价成功", null);
    }
    
    /**
     * 获取样品竞价统计信息
     */
    @GetMapping("/stats")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("获取样品竞价统计信息")
    public Result<AdminSampleBiddingStatsResponse> getSampleBiddingStats(
            @ApiParam("开始日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @ApiParam("结束日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @ApiParam("卖家ID") @RequestParam(required = false) Long sellerId,
            @ApiParam("买家ID") @RequestParam(required = false) Long buyerId) {
        
        log.info("管理员获取样品竞价统计信息");
        
        AdminSampleBiddingStatsResponse stats = adminSampleBiddingService.getSampleBiddingStats(
            startDate, endDate, sellerId, buyerId);
        
        return Result.success(stats);
    }
    
    /**
     * 获取样品竞价趋势数据
     */
    @GetMapping("/trends")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("获取样品竞价趋势数据")
    public Result<List<AdminSampleBiddingStatsResponse.TrendData>> getSampleBiddingTrends(
            @ApiParam("开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @ApiParam("结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @ApiParam("时间粒度") @RequestParam(defaultValue = "day") String granularity) {
        
        log.info("管理员获取样品竞价趋势数据，开始日期: {}, 结束日期: {}, 粒度: {}", 
                startDate, endDate, granularity);
        
        List<AdminSampleBiddingStatsResponse.TrendData> trends = 
            adminSampleBiddingService.getSampleBiddingTrends(startDate, endDate, granularity);
        
        return Result.success(trends);
    }
    
    /**
     * 导出样品竞价数据
     */
    @GetMapping("/export")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("导出样品竞价数据")
    public Result<String> exportSampleBiddings(
            @ApiParam("竞价状态") @RequestParam(required = false) String status,
            @ApiParam("审核状态") @RequestParam(required = false) String auditStatus,
            @ApiParam("开始日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @ApiParam("结束日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @ApiParam("导出格式") @RequestParam(defaultValue = "excel") String format) {
        
        log.info("管理员导出样品竞价数据，格式: {}", format);
        
        String downloadUrl = adminSampleBiddingService.exportSampleBiddings(
            status, auditStatus, startDate, endDate, format);
        
        return Result.success("样品竞价数据导出成功", downloadUrl);
    }
    
    /**
     * 重新发送样品竞价通知
     */
    @PostMapping("/{biddingId}/resend-notification")
    @PreAuthorize("hasAuthority('admin')")
    @ApiOperation("重新发送样品竞价通知")
    public Result<Void> resendSampleBiddingNotification(
            @ApiParam("竞价ID") @PathVariable Long biddingId,
            @ApiParam("通知类型") @RequestParam String notificationType) {
        
        log.info("管理员重新发送样品竞价通知，竞价ID: {}, 通知类型: {}", biddingId, notificationType);
        
        adminSampleBiddingService.resendSampleBiddingNotification(biddingId, notificationType);
        
        return Result.success("样品竞价通知发送成功", null);
    }
} 