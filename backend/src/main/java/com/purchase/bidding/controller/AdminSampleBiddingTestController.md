# AdminSampleBiddingTestController.md

## 1. 文件概述

`AdminSampleBiddingTestController.java` 是竞价模块中一个专门用于测试管理员样品竞价管理功能的控制器，位于 `com.purchase.bidding.controller` 包中。该控制器提供了一系列简单的API接口，旨在验证管理员权限、展示样品竞价管理功能的状态以及提供样品竞价数据结构的基本信息。它主要用于开发和测试环境，帮助开发者快速验证接口连通性和数据模型。

## 2. 核心功能

*   **管理员权限测试**: 提供 `/auth` 接口，用于快速验证当前用户是否具备管理员权限。
*   **功能状态查询**: 提供 `/status` 接口，用于获取样品竞价管理功能的当前状态和支持的操作列表。
*   **数据结构测试**: 提供 `/data-structure` 接口，用于展示样品竞价相关数据结构的基本字段和枚举值，便于前端或集成方理解数据模型。
*   **日志记录**: 对所有接收到的请求都记录了详细的日志，便于测试过程中的调试和追踪。

## 3. 接口说明

### 3.1 测试接口

#### testAdminAuth - 测试管理员权限
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/admin/sample-biddings/test/auth`
*   **权限**: `hasAuthority('admin')`
*   **参数**: 无
*   **返回值**: `Result<Map<String, Object>>` - 包含权限验证结果和时间戳的Map。
*   **业务逻辑**: 仅用于验证调用者是否具有 `admin` 权限。如果权限验证通过，返回成功信息；否则，Spring Security会拦截请求并返回403 Forbidden。

#### getSampleBiddingManagementStatus - 获取样品竞价管理功能状态
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/admin/sample-biddings/test/status`
*   **权限**: `hasAuthority('admin')`
*   **参数**: 无
*   **返回值**: `Result<Map<String, Object>>` - 包含功能名称、版本、是否启用、支持的操作列表和时间戳的Map。
*   **业务逻辑**: 返回一个硬编码的Map，描述了样品竞价管理功能的基本信息和支持的操作。

#### testSampleBiddingDataStructure - 测试样品竞价数据结构
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/admin/sample-biddings/test/data-structure`
*   **权限**: `hasAuthority('admin')`
*   **参数**: 无
*   **返回值**: `Result<Map<String, Object>>` - 包含样品竞价基本字段、样品特有字段和状态枚举的Map。
*   **业务逻辑**: 返回一个硬编码的Map，用于展示样品竞价相关实体和枚举的数据结构概览。

## 4. 业务规则

*   **权限限制**: 所有测试接口都严格限制为 `admin` 角色访问，确保了测试接口的安全性，防止非授权用户探测系统信息。
*   **测试用途**: 该控制器仅用于测试和开发目的，不应在生产环境中使用或对外暴露。
*   **硬编码数据**: 返回的数据（如功能状态、数据结构）是硬编码的，不从数据库或其他服务动态获取，这符合测试控制器的特性。

## 5. 使用示例

```java
// 1. 前端 (Postman/Curl) 调用测试接口

// a. 测试管理员权限
// 请求:
// GET /api/v1/admin/sample-biddings/test/auth
// Headers: Authorization: Bearer <admin_jwt_token>
// 响应示例:
// {
//   "success": true,
//   "message": "权限验证通过",
//   "data": {
//     "message": "管理员权限验证成功",
//     "timestamp": 1678886400000,
//     "feature": "样品竞价管理"
//   }
// }

// b. 获取功能状态
// 请求:
// GET /api/v1/admin/sample-biddings/test/status
// Headers: Authorization: Bearer <admin_jwt_token>
// 响应示例:
// {
//   "success": true,
//   "message": "功能状态获取成功",
//   "data": {
//     "feature": "样品竞价管理",
//     "version": "1.0.0",
//     "enabled": true,
//     "supportedOperations": [
//       "查看所有样品竞价",
//       "审核样品竞价",
//       "更新竞价状态",
//       "强制接受/拒绝",
//       "删除竞价",
//       "批量操作",
//       "统计分析",
//       "数据导出"
//     ],
//     "timestamp": 1678886400000
//   }
// }

// 2. 测试示例 (MockMvc)
@SpringBootTest
@AutoConfigureMockMvc
class AdminSampleBiddingTestControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @Test
    @WithMockUser(authorities = "admin")
    void testAdminAuth_Success() throws Exception {
        mockMvc.perform(get("/api/v1/admin/sample-biddings/test/auth"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.message").value("管理员权限验证成功"));
    }

    @Test
    @WithMockUser(authorities = "user") // 非管理员用户
    void testAdminAuth_AccessDenied() throws Exception {
        mockMvc.perform(get("/api/v1/admin/sample-biddings/test/auth"))
                .andExpect(status().isForbidden()); // 预期403 Forbidden
    }

    @Test
    @WithMockUser(authorities = "admin")
    void testGetSampleBiddingManagementStatus_Success() throws Exception {
        mockMvc.perform(get("/api/v1/admin/sample-biddings/test/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.feature").value("样品竞价管理"));
    }
}
```

## 6. 注意事项

*   **仅限开发/测试环境**: 该控制器及其所有接口仅用于开发、测试和调试目的。在生产环境中，应禁用或移除此类控制器，以避免安全风险和不必要的信息泄露。
*   **权限控制**: 尽管是测试接口，但仍通过 `@PreAuthorize` 进行了权限控制，这是良好的安全实践，即使在测试环境中也应遵循最小权限原则。
*   **信息泄露**: `testSampleBiddingDataStructure` 接口直接暴露了部分数据结构信息。在生产环境中，任何关于内部数据模型的信息都应通过正式的API文档（如Swagger）提供，而不是通过API接口直接返回。
*   **日志记录**: 控制器中使用了 `log.info` 记录请求，这对于测试过程中的调试和问题排查非常有用。
*   **统一响应**: 所有接口都返回 `Result` 对象，统一了成功和失败的响应格式，便于测试工具或前端处理。
*   **Swagger集成**: 使用 `@Api`, `@ApiOperation` 等Swagger注解，有助于自动生成和维护API文档，但对于测试控制器，其主要目的是内部使用。
