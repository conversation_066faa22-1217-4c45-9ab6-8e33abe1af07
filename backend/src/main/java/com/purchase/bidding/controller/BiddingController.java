package com.purchase.bidding.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.purchase.common.exception.BusinessException;
import com.purchase.common.response.Result;
import com.purchase.bidding.dto.request.BiddingSubmitRequest;
import com.purchase.bidding.dto.request.BiddingUpdateRequest;
import com.purchase.bidding.dto.response.BiddingDetailResponse;
import com.purchase.bidding.dto.response.BiddingStatsResponse;
import com.purchase.bidding.service.BiddingService;
import com.purchase.common.util.SecurityContextUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import com.purchase.bidding.dto.request.BiddingCancelRequest;
import com.purchase.bidding.vo.RequirementVO;

/**
 * 竞价控制器
 * 提供竞价相关的API接口
 */
@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
public class BiddingController {

    private final BiddingService biddingService;

    /**
     * 提交竞价
     * 
     * @param request 竞价提交请求，包含竞价信息
     * @return 提交成功的竞价详情
     * @throws BusinessException 如果用户认证失败或已经提交过竞价
     */
    @PostMapping("/biddings")
    @PreAuthorize("hasAuthority('seller')")
    public Result<BiddingDetailResponse> submitBidding(@Valid @RequestBody BiddingSubmitRequest request) {
        Long sellerId = SecurityContextUtil.getCurrentUserId();
        request.setSellerId(sellerId);
        BiddingDetailResponse response = biddingService.submitBidding(request);
        return Result.success(response);
    }

    /**
     * 更新竞价信息
     * 
     * @param id 竞价ID
     * @param request 竞价更新请求
     * @return 更新后的竞价详情
     * @throws BusinessException 如果用户认证失败或无权修改此竞价
     */
    @PutMapping("/biddings/{id}")
    @PreAuthorize("hasAuthority('seller')")
    public Result<BiddingDetailResponse> updateBidding(@PathVariable Long id, @Valid @RequestBody BiddingUpdateRequest request) {
        Long sellerId = SecurityContextUtil.getCurrentUserId();
        request.setSellerId(sellerId);
        BiddingDetailResponse response = biddingService.updateBidding(id, request);
        return Result.success(response);
    }

    /**
     * 取消竞价
     * 
     * @param id 竞价ID
     * @return 操作结果
     * @throws BusinessException 如果用户认证失败或无权取消此竞价
     */
    @PutMapping("/biddings/{id}/cancel")
    @PreAuthorize("hasAuthority('seller')")
    public Result<Void> cancelBidding(@PathVariable Long id) {
        Long sellerId = SecurityContextUtil.getCurrentUserId();
        biddingService.cancelBidding(id, sellerId);
        return Result.success();
    }

    /**
     * 获取特定需求的竞价列表
     * 根据用户角色返回不同权限的竞价信息
     * 严格权限控制：买家只能查看自己需求下的竞价
     *
     * @param requirementId 需求ID
     * @param buyerId 买家ID（可选）
     * @param status 竞价状态（可选）
     * @param minPrice 最低价格（可选）
     * @param maxPrice 最高价格（可选）
     * @param page 当前页码
     * @param size 每页大小
     * @return 竞价详情分页列表
     * @throws BusinessException 如果用户认证失败或无权访问该需求
     */
    @GetMapping("/requirements/{requirementId}/biddings")
    @PreAuthorize("hasAnyAuthority('buyer','seller','admin')")
    public Result<IPage<BiddingDetailResponse>> getBiddingList(
            @PathVariable Long requirementId,
            @RequestParam(required = false) Long buyerId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {

        // 如果是管理员，不需要设置buyerId和sellerId限制
        if (SecurityContextUtil.hasRole("admin")) {
            // admin可以查看所有投标列表
            return Result.success(biddingService.getBiddingList(
                requirementId, null, null, status, minPrice, maxPrice, page, size));
        }

        Long userId = SecurityContextUtil.getCurrentUserId();

        // 如果是买家，使用当前用户ID作为buyerId
        if (SecurityContextUtil.hasRole("buyer")) {
            buyerId = userId;
        }
        // 如果是卖家，使用当前用户ID作为sellerId
        Long sellerId = null;
        if (SecurityContextUtil.hasRole("seller")) {
            sellerId = userId;
        }

        // 调用biddingService的getBiddingList方法，获取投标列表
        IPage<BiddingDetailResponse> response = biddingService.getBiddingList(
                requirementId, buyerId, sellerId, status, minPrice, maxPrice, page, size);
        // 返回结果
        return Result.success(response);
    }

    /**
     * 接受竞价
     * 将竞价设置为中标，并拒绝该需求的其他竞价
     * 严格权限控制：只能接受自己需求下的竞价
     *
     * @param id 竞价ID
     * @return 接受后的竞价详情
     * @throws BusinessException 如果用户认证失败或无权接受此竞价
     */
    @PutMapping("/biddings/{id}/accept")
    @PreAuthorize("hasAuthority('buyer')")
    public Result<BiddingDetailResponse> acceptBidding(@PathVariable Long id) {
        Long buyerId = SecurityContextUtil.getCurrentUserId();
        // 验证竞价归属权限
        biddingService.validateBiddingOwnership(id, buyerId);
        BiddingDetailResponse response = biddingService.acceptBidding(id, buyerId);
        return Result.success(response);
    }

    /**
     * 拒绝竞价
     * 严格权限控制：只能拒绝自己需求下的竞价
     *
     * @param id 竞价ID
     * @param reason 拒绝原因（可选）
     * @return 操作结果
     * @throws BusinessException 如果用户认证失败或无权拒绝此竞价
     */
    @PutMapping("/biddings/{id}/reject")
    @PreAuthorize("hasAuthority('buyer')")
    public Result<Void> rejectBidding(
            @PathVariable Long id,
            @RequestParam(required = false) String reason) {
        Long buyerId = SecurityContextUtil.getCurrentUserId();
        // 验证竞价归属权限
        biddingService.validateBiddingOwnership(id, buyerId);
        biddingService.rejectBidding(id, buyerId, reason);
        return Result.success();
    }

    /**
     * 获取竞价详情
     * 买家可以查看所有竞价详情，但对于不属于自己需求的竞价只能看到脱敏信息
     *
     * @param id 竞价ID
     * @return 竞价详情信息
     */
    @GetMapping("/biddings/{id}")
    @PreAuthorize("hasAnyAuthority('buyer','seller','admin')")
    public Result<BiddingDetailResponse> getBiddingDetail(@PathVariable Long id) {
        BiddingDetailResponse response = biddingService.getBiddingDetail(id);
        return Result.success(response);
    }

    /**
     * 获取竞价统计信息
     * 包括总数、各状态数量、最低/最高/平均价格等
     * 
     * @param requirementId 需求ID
     * @return 竞价统计信息
     */
    @GetMapping("/requirements/{requirementId}/biddings/stats")
    @PreAuthorize("hasAuthority('admin')")
    public Result<BiddingStatsResponse> getBiddingStats(@PathVariable Long requirementId) {
        BiddingStatsResponse response = biddingService.getBiddingStats(requirementId);
        return Result.success(response);
    }

    /**
     * 获取当前卖家参与竞价的需求列表
     * 
     * @param current 当前页码
     * @param size 每页大小
     * @return 需求分页列表
     */
    @GetMapping("/biddings/my-requirements")
    @PreAuthorize("hasAuthority('seller')")
    public Result<IPage<RequirementVO>> getMyBiddingRequirements(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size) {
        return Result.success(biddingService.getMyBiddingRequirements(new Page<>(current, size)));
    }

    /**
     * 管理员更改竞价状态
     * 
     * @param id 竞价ID
     * @param status 新状态
     * @return 更新后的竞价详情
     * @throws BusinessException 如果状态值无效
     */
    @PutMapping("/biddings/admin/{id}/status")
    @PreAuthorize("hasAuthority('admin')")
    public Result<BiddingDetailResponse> updateBiddingStatus(
            @PathVariable Long id,
            @RequestParam String status) {
        BiddingDetailResponse response = biddingService.updateBiddingStatus(id, status);
        return Result.success(response);
    }

    /**
     * 管理员删除竞价（逻辑删除）
     * 
     * @param id 竞价ID
     * @return 操作结果
     */
    @DeleteMapping("/biddings/admin/{id}")
    @PreAuthorize("hasAuthority('admin')")
    public Result<Void> deleteBidding(@PathVariable Long id) {
        biddingService.deleteBidding(id);
        return Result.success();
    }
    
    /**
     * 获取当前卖家对特定需求的竞价记录
     * 
     * @param requirementId 需求ID
     * @return 竞价详情，如不存在则返回null
     * @throws BusinessException 如果用户认证失败
     */
    @GetMapping("/requirements/{requirementId}/biddings/my")
    @PreAuthorize("hasAuthority('seller')")
    public Result<BiddingDetailResponse> getMyBiddingForRequirement(@PathVariable Long requirementId) {
        Long sellerId = SecurityContextUtil.getCurrentUserId();
        BiddingDetailResponse response = biddingService.getSellerBiddingForRequirement(requirementId, sellerId);
        return Result.success(response);
    }

    /**
     * 管理员获取所有竞价记录
     * 提供多种筛选条件，适用于管理后台查看所有竞价情况
     * 
     * @param auditStatus 审核状态筛选(可选)
     * @param sellerId 卖家ID筛选(可选)
     * @param buyerId 买家ID筛选(可选)
     * @param minPrice 最低价格筛选(可选)
     * @param maxPrice 最高价格筛选(可选)
     * @param biddingType 竞价类型筛选(可选, e.g., 'purchase', 'sample')
     * @param page 当前页码(默认1)
     * @param size 每页大小(默认10)
     * @return 竞价记录分页列表
     */
    @GetMapping("/biddings/admin/all")
    @PreAuthorize("hasAuthority('admin')")
    public Result<IPage<BiddingDetailResponse>> getAllBiddings(
            @RequestParam(required = false) String auditStatus,
            @RequestParam(required = false) Long sellerId,
            @RequestParam(required = false) Long buyerId,
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice,
            @RequestParam(required = false) String biddingType,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        IPage<BiddingDetailResponse> response = biddingService.getAllBiddings(
                auditStatus, sellerId, buyerId, minPrice, maxPrice, biddingType, page, size);
        
        return Result.success(response);
    }
    
    /**
     * 管理员审核竞价
     * 
     * @param id 竞价ID
     * @param approved 是否审核通过
     * @param remark 审核备注
     * @return 审核后的竞价详情
     */
    @PutMapping("/biddings/admin/{id}/audit")
    @PreAuthorize("hasAuthority('admin')")
    public Result<BiddingDetailResponse> auditBidding(
            @PathVariable Long id,
            @RequestParam boolean approved,
            @RequestParam(required = false) String remark) {
        BiddingDetailResponse response = biddingService.auditBidding(id, approved, remark);
        return Result.success(response);
    }
    
    /**
     * 获取待审核的竞价列表
     * 
     * @param page 当前页码
     * @param size 每页大小
     * @return 待审核竞价分页列表
     */
    @GetMapping("/biddings/admin/pending-audit")
    @PreAuthorize("hasAuthority('admin')")
    public Result<IPage<BiddingDetailResponse>> getPendingAuditBiddings(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        IPage<BiddingDetailResponse> response = biddingService.getPendingAuditBiddings(page, size);
        return Result.success(response);
    }
    
    /**
     * 获取可见的竞价列表（审核通过的竞价和自己的竞价）
     * 买家可以查看所有需求的竞价，但对于不属于自己的需求只能看到脱敏信息
     *
     * @param requirementId 需求ID
     * @param page 当前页码
     * @param size 每页大小
     * @return 可见的竞价列表
     */
    @GetMapping("/requirements/{requirementId}/biddings/visible")
    @PreAuthorize("hasAnyAuthority('buyer','seller','admin')")
    public Result<IPage<BiddingDetailResponse>> getVisibleBiddingsByRequirementId(
            @PathVariable Long requirementId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {

        IPage<BiddingDetailResponse> response = biddingService.getVisibleBiddingsByRequirementId(requirementId, page, size);
        return Result.success(response);
    }
}