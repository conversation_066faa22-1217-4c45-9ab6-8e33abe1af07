package com.purchase.bidding.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.common.response.Result;
import com.purchase.bidding.dto.request.SampleBiddingSubmitRequest;
import com.purchase.bidding.dto.response.BiddingDetailResponse;
import com.purchase.bidding.service.SampleBiddingService;
import com.purchase.common.util.SecurityContextUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 样品竞价控制器
 * 提供样品竞价的提交、管理和查询功能
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/sample-biddings")
@Api(tags = "样品竞价管理")
public class SampleBiddingController {
    
    @Resource
    private SampleBiddingService sampleBiddingService;
    
    /**
     * 卖家提交样品竞价
     */
    @PostMapping("/submit")
    @PreAuthorize("hasAuthority('seller')")
    @ApiOperation("卖家提交样品竞价")
    public Result<BiddingDetailResponse> submitSampleBidding(
            @Valid @RequestBody SampleBiddingSubmitRequest request) {
        
        // 从Security Context获取卖家ID
        Long sellerId = SecurityContextUtil.getCurrentUserId();
        
        log.info("卖家提交样品竞价，卖家ID: {}, 需求ID: {}", sellerId, request.getRequirementId());
        
        BiddingDetailResponse bidding = sampleBiddingService.submitSampleBidding(request, sellerId);
        
        return Result.success("样品竞价提交成功", bidding);
    }
    
    /**
     * 买家接受样品竞价
     */
    @PostMapping("/{biddingId}/accept")
    @PreAuthorize("hasAuthority('buyer')")
    @ApiOperation("买家接受样品竞价")
    public Result<BiddingDetailResponse> acceptSampleBidding(
            @ApiParam("竞价ID") @PathVariable Long biddingId) {
        
        // 从Security Context获取买家ID
        Long buyerId = SecurityContextUtil.getCurrentUserId();
        
        log.info("买家接受样品竞价，买家ID: {}, 竞价ID: {}", buyerId, biddingId);
        
        BiddingDetailResponse bidding = sampleBiddingService.acceptSampleBidding(biddingId, buyerId);
        
        return Result.success("样品竞价接受成功", bidding);
    }
    
    /**
     * 买家拒绝样品竞价
     */
    @PostMapping("/{biddingId}/reject")
    @PreAuthorize("hasAuthority('buyer')")
    @ApiOperation("买家拒绝样品竞价")
    public Result<BiddingDetailResponse> rejectSampleBidding(
            @ApiParam("竞价ID") @PathVariable Long biddingId,
            @ApiParam("拒绝原因") @RequestParam(required = false) String reason) {
        
        // 从Security Context获取买家ID
        Long buyerId = SecurityContextUtil.getCurrentUserId();
        
        log.info("买家拒绝样品竞价，买家ID: {}, 竞价ID: {}", buyerId, biddingId);
        
        BiddingDetailResponse bidding = sampleBiddingService.rejectSampleBidding(biddingId, buyerId, reason);
        
        return Result.success("样品竞价拒绝成功", bidding);
    }
    
    /**
     * 获取需求的样品竞价列表
     * 只返回审核通过的竞价数据，仅买家和卖家可访问
     */
    @GetMapping("/requirement/{requirementId}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller')")
    @ApiOperation("获取需求的样品竞价列表")
    public Result<IPage<BiddingDetailResponse>> getSampleBiddingsByRequirement(
            @ApiParam("需求ID") @PathVariable Long requirementId,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("竞价状态") @RequestParam(required = false) String status) {
        
        log.info("查询需求的样品竞价列表，需求ID: {}, 页码: {}, 大小: {}", requirementId, page, size);
        
        IPage<BiddingDetailResponse> biddings = sampleBiddingService.getSampleBiddingsByRequirement(
            requirementId, page, size, status);
        
        return Result.success(biddings);
    }
    
    /**
     * 获取需求的已接受样品竞价列表
     */
    @GetMapping("/requirement/{requirementId}/accepted")
    @PreAuthorize("hasAnyAuthority('buyer', 'admin')")
    @ApiOperation("获取需求的已接受样品竞价列表")
    public Result<List<BiddingDetailResponse>> getAcceptedSampleBiddings(
            @ApiParam("需求ID") @PathVariable Long requirementId) {
        
        log.info("查询需求的已接受样品竞价列表，需求ID: {}", requirementId);
        
        List<BiddingDetailResponse> biddings = sampleBiddingService.getAcceptedSampleBiddings(requirementId);
        
        return Result.success(biddings);
    }
    
    /**
     * 卖家获取自己的样品竞价列表
     */
    @GetMapping("/seller")
    @PreAuthorize("hasAuthority('seller')")
    @ApiOperation("卖家获取自己的样品竞价列表")
    public Result<IPage<BiddingDetailResponse>> getSellerSampleBiddings(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("竞价状态") @RequestParam(required = false) String status) {
        
        // 从Security Context获取卖家ID
        Long sellerId = SecurityContextUtil.getCurrentUserId();
        
        log.info("卖家查询自己的样品竞价列表，卖家ID: {}, 页码: {}, 大小: {}", sellerId, page, size);
        
        IPage<BiddingDetailResponse> biddings = sampleBiddingService.getSellerSampleBiddings(
            sellerId, page, size, status);
        
        return Result.success(biddings);
    }
    
    /**
     * 获取样品竞价详情
     */
    @GetMapping("/{biddingId}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")
    @ApiOperation("获取样品竞价详情")
    public Result<BiddingDetailResponse> getSampleBiddingDetail(
            @ApiParam("竞价ID") @PathVariable Long biddingId) {
        
        log.info("查询样品竞价详情，竞价ID: {}", biddingId);
        
        BiddingDetailResponse bidding = sampleBiddingService.getSampleBiddingDetail(biddingId);
        
        return Result.success(bidding);
    }
    
    /**
     * 卖家修改样品竞价
     */
    @PutMapping("/{biddingId}")
    @PreAuthorize("hasAuthority('seller')")
    @ApiOperation("卖家修改样品竞价")
    public Result<BiddingDetailResponse> updateSampleBidding(
            @ApiParam("竞价ID") @PathVariable Long biddingId,
            @Valid @RequestBody SampleBiddingSubmitRequest request) {
        
        // 从Security Context获取卖家ID
        Long sellerId = SecurityContextUtil.getCurrentUserId();
        
        log.info("卖家修改样品竞价，卖家ID: {}, 竞价ID: {}", sellerId, biddingId);
        
        BiddingDetailResponse bidding = sampleBiddingService.updateSampleBidding(biddingId, request, sellerId);
        
        return Result.success("样品竞价修改成功", bidding);
    }
    
    /**
     * 卖家取消样品竞价
     */
    @PostMapping("/{biddingId}/cancel")
    @PreAuthorize("hasAuthority('seller')")
    @ApiOperation("卖家取消样品竞价")
    public Result<BiddingDetailResponse> cancelSampleBidding(
            @ApiParam("竞价ID") @PathVariable Long biddingId,
            @ApiParam("取消原因") @RequestParam(required = false) String reason) {
        
        // 从Security Context获取卖家ID
        Long sellerId = SecurityContextUtil.getCurrentUserId();
        
        log.info("卖家取消样品竞价，卖家ID: {}, 竞价ID: {}", sellerId, biddingId);
        
        BiddingDetailResponse bidding = sampleBiddingService.cancelSampleBidding(biddingId, sellerId, reason);
        
        return Result.success("样品竞价取消成功", bidding);
    }
    
    /**
     * 买家批量接受样品竞价
     */
    @PostMapping("/batch-accept")
    @PreAuthorize("hasAuthority('buyer')")
    @ApiOperation("买家批量接受样品竞价")
    public Result<List<BiddingDetailResponse>> batchAcceptSampleBiddings(
            @ApiParam("竞价ID列表") @RequestBody List<Long> biddingIds) {
        
        // 从Security Context获取买家ID
        Long buyerId = SecurityContextUtil.getCurrentUserId();
        
        log.info("买家批量接受样品竞价，买家ID: {}, 竞价数量: {}", buyerId, biddingIds.size());
        
        List<BiddingDetailResponse> biddings = sampleBiddingService.batchAcceptSampleBiddings(biddingIds, buyerId);
        
        return Result.success("批量接受样品竞价成功", biddings);
    }
    
    /**
     * 验证样品竞价
     */
    @GetMapping("/{biddingId}/validate")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")
    @ApiOperation("验证样品竞价")
    public Result<Boolean> validateSampleBidding(
            @ApiParam("竞价ID") @PathVariable Long biddingId,
            @ApiParam("需求ID") @RequestParam Long requirementId) {
        
        log.info("验证样品竞价，竞价ID: {}, 需求ID: {}", biddingId, requirementId);
        
        boolean isValid = sampleBiddingService.validateSampleBidding(biddingId, requirementId);
        
        return Result.success(isValid);
    }
    

} 