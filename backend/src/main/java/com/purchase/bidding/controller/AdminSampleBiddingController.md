# AdminSampleBiddingController.md

## 1. 文件概述

`AdminSampleBiddingController.java` 是竞价模块中专门为管理员提供样品竞价管理功能的控制器，位于 `com.purchase.bidding.controller` 包中。该控制器作为管理员后台的API入口，负责处理所有与样品竞价的查询、审核、状态更新、强制操作、删除以及统计分析相关的HTTP请求。它通过依赖注入 `AdminSampleBiddingService` 来执行具体的业务逻辑，并利用Spring Security的 `@PreAuthorize` 注解进行严格的权限控制，确保只有管理员才能执行这些敏感操作。

## 2. 核心功能

*   **样品竞价列表查询**: 提供多维度查询接口，支持按竞价状态、审核状态、卖家ID、买家ID、需求ID、样品价格范围、产品名称、公司名称以及创建日期范围等条件，分页获取所有样品竞价列表。
*   **特定状态列表查询**: 提供了快速获取待审核、已审核通过和已拒绝审核的样品竞价列表的接口。
*   **样品竞价详情**: 支持根据竞价ID获取单个样品竞价的详细信息。
*   **竞价审核**: 提供了审核样品竞价的接口，支持单个和批量审核，并可添加审核备注。
*   **状态更新**: 允许管理员更新样品竞价的状态，并可添加状态备注。
*   **强制操作**: 提供了强制接受或强制拒绝样品竞价的功能，赋予管理员对竞价流程的最终控制权。
*   **竞价删除**: 支持逻辑删除单个或批量样品竞价，并可记录删除原因。
*   **统计与趋势**: 提供样品竞价的统计信息和趋势数据查询，帮助管理员了解竞价活动的整体情况。
*   **数据导出**: 支持将样品竞价数据导出为指定格式（如Excel），便于离线分析和归档。
*   **通知重发**: 提供了重新发送样品竞价通知的功能，确保关键信息能够触达相关方。

## 3. 接口说明

### 3.1 样品竞价查询接口

#### getAllSampleBiddings - 获取所有样品竞价列表（管理员视图）
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/admin/sample-biddings`
*   **权限**: `hasAuthority('admin')`
*   **参数**: 支持多种查询参数，包括 `page`, `size`, `status`, `auditStatus`, `sellerId`, `buyerId`, `requirementId`, `minSamplePrice`, `maxSamplePrice`, `productName`, `company`, `startDate`, `endDate`。
*   **返回值**: `Result<IPage<BiddingDetailResponse>>` - 包含样品竞价详情的分页结果。
*   **业务逻辑**: 调用 `adminSampleBiddingService.getAllSampleBiddings`，将所有查询参数传递给服务层进行处理。

#### getPendingAuditSampleBiddings - 获取待审核的样品竞价列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/admin/sample-biddings/pending-audit`
*   **权限**: `hasAuthority('admin')`
*   **参数**: `page`, `size`。
*   **返回值**: `Result<IPage<BiddingDetailResponse>>` - 包含待审核样品竞价详情的分页结果。

#### getSampleBiddingDetail - 获取样品竞价详情（管理员视图）
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/admin/sample-biddings/{biddingId}`
*   **权限**: `hasAuthority('admin')`
*   **参数**: `biddingId` (path, required)。
*   **返回值**: `Result<BiddingDetailResponse>` - 样品竞价的详细信息。

### 3.2 样品竞价操作接口

#### auditSampleBidding - 审核样品竞价
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/admin/sample-biddings/{biddingId}/audit`
*   **权限**: `hasAuthority('admin')`
*   **参数**: `biddingId` (path, required), `request` (AdminSampleBiddingAuditRequest, body, required)。`request` 包含 `approved` (Boolean) 和 `auditRemark` (String)。
*   **返回值**: `Result<BiddingDetailResponse>` - 审核后的样品竞价详情。

#### batchAuditSampleBiddings - 批量审核样品竞价
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/admin/sample-biddings/batch-audit`
*   **权限**: `hasAuthority('admin')`
*   **参数**: `biddingIds` (query, required), `approved` (query, required), `auditRemark` (query, optional)。
*   **返回值**: `Result<List<BiddingDetailResponse>>` - 批量审核后的样品竞价详情列表。

#### updateSampleBiddingStatus - 更新样品竞价状态
*   **HTTP方法**: `PUT`
*   **路径**: `/api/v1/admin/sample-biddings/{biddingId}/status`
*   **权限**: `hasAuthority('admin')`
*   **参数**: `biddingId` (path, required), `request` (AdminSampleBiddingStatusUpdateRequest, body, required)。`request` 包含 `status` (String) 和 `statusRemark` (String)。
*   **返回值**: `Result<BiddingDetailResponse>` - 更新状态后的样品竞价详情。

#### forceAcceptSampleBidding - 强制接受样品竞价
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/admin/sample-biddings/{biddingId}/force-accept`
*   **权限**: `hasAuthority('admin')`
*   **参数**: `biddingId` (path, required), `reason` (query, optional)。
*   **返回值**: `Result<BiddingDetailResponse>` - 强制接受后的样品竞价详情。

#### deleteSampleBidding - 删除样品竞价（逻辑删除）
*   **HTTP方法**: `DELETE`
*   **路径**: `/api/v1/admin/sample-biddings/{biddingId}`
*   **权限**: `hasAuthority('admin')`
*   **参数**: `biddingId` (path, required), `reason` (query, optional)。
*   **返回值**: `Result<Void>` - 表示操作成功。

### 3.3 统计与导出接口

#### getSampleBiddingStats - 获取样品竞价统计信息
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/admin/sample-biddings/stats`
*   **权限**: `hasAuthority('admin')`
*   **参数**: `startDate`, `endDate`, `sellerId`, `buyerId` (均为可选)。
*   **返回值**: `Result<AdminSampleBiddingStatsResponse>` - 样品竞价统计信息。

#### getSampleBiddingTrends - 获取样品竞价趋势数据
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/admin/sample-biddings/trends`
*   **权限**: `hasAuthority('admin')`
*   **参数**: `startDate` (required), `endDate` (required), `granularity` (optional, default="day")。
*   **返回值**: `Result<List<AdminSampleBiddingStatsResponse.TrendData>>` - 样品竞价趋势数据列表。

#### exportSampleBiddings - 导出样品竞价数据
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/admin/sample-biddings/export`
*   **权限**: `hasAuthority('admin')`
*   **参数**: `status`, `auditStatus`, `startDate`, `endDate`, `format` (optional, default="excel")。
*   **返回值**: `Result<String>` - 导出文件下载URL。

## 4. 业务规则

*   **权限控制**: 所有接口都严格限制为 `admin` 角色访问，确保了后台操作的安全性。
*   **审核流程**: 样品竞价的审核（`auditSampleBidding`）是关键业务流程，审核结果会影响竞价的后续状态。
*   **状态流转**: 竞价状态的更新（`updateSampleBiddingStatus`）应遵循预定义的状态机，防止非法状态转换。
*   **强制操作**: `forceAccept` 和 `forceReject` 接口赋予管理员直接干预竞价结果的能力，通常用于处理特殊情况或异常。
*   **逻辑删除**: `deleteSampleBidding` 和 `batchDeleteSampleBiddings` 采用逻辑删除，而非物理删除，便于数据恢复和审计。
*   **数据校验**: 请求体中的DTO（如 `AdminSampleBiddingAuditRequest`）通过 `@Valid` 注解进行JSR-303校验，确保输入数据的合法性。

## 5. 使用示例

```java
// 1. 前端 (React) 管理后台调用示例
// a. 获取待审核竞价列表
async function fetchPendingBiddings() {
  try {
    const response = await axios.get('/api/v1/admin/sample-biddings/pending-audit', {
      params: { page: 1, size: 10 },
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('待审核竞价:', response.data.data);
  } catch (error) {
    console.error('获取失败:', error.response.data);
  }
}

// b. 审核单个竞价
async function auditBidding(biddingId, approved, auditRemark) {
  try {
    const response = await axios.post(`/api/v1/admin/sample-biddings/${biddingId}/audit`, {
      approved,
      auditRemark
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('审核结果:', response.data.data);
  } catch (error) {
    console.error('审核失败:', error.response.data);
  }
}

// c. 导出数据
async function exportBiddings(status, format) {
  try {
    const response = await axios.get('/api/v1/admin/sample-biddings/export', {
      params: { status, format },
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('下载链接:', response.data.data);
  } catch (error) {
    console.error('导出失败:', error.response.data);
  }
}

// 2. Java后端服务间调用 (Feign Client)
@FeignClient(name = "bidding-service", path = "/api/v1/admin/sample-biddings")
public interface AdminSampleBiddingClient {
    @GetMapping
    Result<IPage<BiddingDetailResponse>> getAllSampleBiddings(
            @RequestParam Integer page, @RequestParam Integer size, @RequestParam(required = false) String status);

    @PostMapping("/{biddingId}/audit")
    Result<BiddingDetailResponse> auditSampleBidding(
            @PathVariable Long biddingId, @RequestBody AdminSampleBiddingAuditRequest request);
}

// 3. 测试示例 (MockMvc)
@SpringBootTest
@AutoConfigureMockMvc
class AdminSampleBiddingControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AdminSampleBiddingService adminSampleBiddingService;

    @Test
    @WithMockUser(authorities = "admin")
    void testGetAllSampleBiddings_Success() throws Exception {
        IPage<BiddingDetailResponse> mockPage = new Page<>(); // 模拟分页数据
        when(adminSampleBiddingService.getAllSampleBiddings(anyInt(), anyInt(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(mockPage);

        mockMvc.perform(get("/api/v1/admin/sample-biddings")
                .param("page", "1").param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(authorities = "admin")
    void testAuditSampleBidding_Success() throws Exception {
        AdminSampleBiddingAuditRequest request = new AdminSampleBiddingAuditRequest();
        request.setApproved(true);
        request.setAuditRemark("通过审核");

        BiddingDetailResponse mockResponse = new BiddingDetailResponse(); // 模拟返回的竞价详情
        when(adminSampleBiddingService.auditSampleBidding(anyLong(), anyBoolean(), anyString()))
            .thenReturn(mockResponse);

        mockMvc.perform(post("/api/v1/admin/sample-biddings/123/audit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("样品竞价审核成功"));
    }

    @Test
    @WithMockUser(authorities = "user") // 非管理员用户
    void testGetAllSampleBiddings_AccessDenied() throws Exception {
        mockMvc.perform(get("/api/v1/admin/sample-biddings"))
                .andExpect(status().isForbidden()); // 预期403 Forbidden
    }
}
```

## 6. 注意事项

*   **权限控制**: 所有接口都通过 `@PreAuthorize("hasAuthority('admin')")` 进行了严格的权限控制，确保只有管理员才能访问和操作。这是后台管理系统安全的关键。
*   **日志记录**: 控制器中使用了 `log.info` 记录请求参数，这对于监控和问题排查非常重要。
*   **参数校验**: 请求体中的DTO（如 `AdminSampleBiddingAuditRequest`）通过 `@Valid` 注解进行JSR-303校验，确保输入数据的合法性。日期参数使用了 `@DateTimeFormat` 进行格式化。
*   **统一响应**: 所有接口都返回 `Result` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **Swagger集成**: 使用 `@Api`, `@ApiOperation`, `@ApiParam` 等Swagger注解，有助于自动生成和维护API文档，提高了API的可用性。
*   **职责分离**: 控制器仅负责请求的接收、参数的初步校验和转发，具体的业务逻辑和数据处理委托给 `AdminSampleBiddingService`。
*   **复杂查询**: `getAllSampleBiddings` 接口支持大量查询参数，其服务层实现需要能够灵活地构建动态查询条件。
*   **强制操作的风险**: 强制接受/拒绝等操作具有高权限，应在业务流程中谨慎使用，并确保有详细的操作日志记录。
*   **导出功能**: 导出功能通常是异步的，控制器返回下载URL，实际文件生成可能在后台任务中完成。
