# BiddingController.md

## 1. 文件概述

`BiddingController.java` 是竞价模块的核心控制器，位于 `com.purchase.bidding.controller` 包中。它提供了与竞价相关的RESTful API接口，涵盖了竞价的提交、更新、取消、查询、接受和拒绝等全生命周期管理。该控制器通过依赖注入 `BiddingService` 来执行具体的业务逻辑，并利用Spring Security的 `@PreAuthorize` 注解进行严格的权限控制，确保不同角色（买家、卖家、管理员）的用户只能执行其被授权的操作。

## 2. 核心功能

*   **竞价提交与更新**: 允许卖家提交新的竞价或更新其已提交的竞价。
*   **竞价取消**: 允许卖家取消其已提交的竞价。
*   **竞价列表查询**: 提供多维度查询接口，支持按需求ID、买家ID、卖家ID、竞价状态、价格范围等条件，分页获取竞价列表。并根据用户角色进行权限控制，确保数据隔离。
*   **竞价接受与拒绝**: 允许买家接受或拒绝其需求下的竞价，并触发后续业务流程（如生成订单）。
*   **竞价详情查询**: 支持根据竞价ID获取单个竞价的详细信息，并根据用户权限返回脱敏或完整数据。
*   **竞价统计**: 提供特定需求下的竞价统计信息，如总数、各状态数量、价格分布等。
*   **卖家竞价需求**: 允许卖家查看其参与竞价的需求列表。
*   **管理员操作**: 提供管理员专属接口，用于强制更改竞价状态、删除竞价、批量查询和审核竞价。
*   **统一响应**: 所有接口都返回一个标准化的 `Result` 对象，统一了成功和失败的响应格式。

## 3. 接口说明

### 3.1 卖家操作接口

#### submitBidding - 提交竞价
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/biddings`
*   **权限**: `hasAuthority('seller')`
*   **参数**:
    *   `request` (BiddingSubmitRequest, body, required): 包含竞价信息的请求体。
*   **返回值**: `Result<BiddingDetailResponse>` - 提交成功的竞价详情。
*   **业务逻辑**: 获取当前卖家ID并设置到请求中，然后调用 `biddingService.submitBidding`。

#### updateBidding - 更新竞价信息
*   **HTTP方法**: `PUT`
*   **路径**: `/api/v1/biddings/{id}`
*   **权限**: `hasAuthority('seller')`
*   **参数**:
    *   `id` (Long, path, required): 竞价ID。
    *   `request` (BiddingUpdateRequest, body, required): 包含更新信息的请求体。
*   **返回值**: `Result<BiddingDetailResponse>` - 更新后的竞价详情。
*   **业务逻辑**: 获取当前卖家ID并设置到请求中，然后调用 `biddingService.updateBidding`。

#### cancelBidding - 取消竞价
*   **HTTP方法**: `PUT`
*   **路径**: `/api/v1/biddings/{id}/cancel`
*   **权限**: `hasAuthority('seller')`
*   **参数**:
    *   `id` (Long, path, required): 竞价ID。
*   **返回值**: `Result<Void>` - 操作结果。
*   **业务逻辑**: 获取当前卖家ID，然后调用 `biddingService.cancelBidding`。

#### getMyBiddingForRequirement - 获取当前卖家对特定需求的竞价记录
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/requirements/{requirementId}/biddings/my`
*   **权限**: `hasAuthority('seller')`
*   **参数**:
    *   `requirementId` (Long, path, required): 需求ID。
*   **返回值**: `Result<BiddingDetailResponse>` - 竞价详情，如不存在则返回 `null`。
*   **业务逻辑**: 获取当前卖家ID，然后调用 `biddingService.getSellerBiddingForRequirement`。

#### getMyBiddingRequirements - 获取当前卖家参与竞价的需求列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/biddings/my-requirements`
*   **权限**: `hasAuthority('seller')`
*   **参数**:
    *   `current` (Long, query, optional, default=1): 当前页码。
    *   `size` (Long, query, optional, default=10): 每页大小。
*   **返回值**: `Result<IPage<RequirementVO>>` - 需求分页列表。
*   **业务逻辑**: 调用 `biddingService.getMyBiddingRequirements`。

### 3.2 买家操作接口

#### getBiddingList - 获取特定需求的竞价列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/requirements/{requirementId}/biddings`
*   **权限**: `hasAnyAuthority('buyer','seller','admin')`
*   **参数**:
    *   `requirementId` (Long, path, required): 需求ID。
    *   `buyerId` (Long, query, optional): 买家ID（仅管理员或买家自身使用）。
    *   `status` (String, query, optional): 竞价状态。
    *   `minPrice` (BigDecimal, query, optional): 最低价格。
    *   `maxPrice` (BigDecimal, query, optional): 最高价格。
    *   `page` (Integer, query, optional, default=1): 当前页码。
    *   `size` (Integer, query, optional, default=10): 每页大小。
*   **返回值**: `Result<IPage<BiddingDetailResponse>>` - 竞价详情分页列表。
*   **业务逻辑**: 根据当前用户角色（管理员、买家、卖家）动态调整查询参数，然后调用 `biddingService.getBiddingList`。

#### acceptBidding - 接受竞价
*   **HTTP方法**: `PUT`
*   **路径**: `/api/v1/biddings/{id}/accept`
*   **权限**: `hasAuthority('buyer')`
*   **参数**:
    *   `id` (Long, path, required): 竞价ID。
*   **返回值**: `Result<BiddingDetailResponse>` - 接受后的竞价详情。
*   **业务逻辑**: 获取当前买家ID，验证竞价归属权限，然后调用 `biddingService.acceptBidding`。

#### rejectBidding - 拒绝竞价
*   **HTTP方法**: `PUT`
*   **路径**: `/api/v1/biddings/{id}/reject`
*   **权限**: `hasAuthority('buyer')`
*   **参数**:
    *   `id` (Long, path, required): 竞价ID。
    *   `reason` (String, query, optional): 拒绝原因。
*   **返回值**: `Result<Void>` - 操作结果。
*   **业务逻辑**: 获取当前买家ID，验证竞价归属权限，然后调用 `biddingService.rejectBidding`。

#### getBiddingDetail - 获取竞价详情
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/biddings/{id}`
*   **权限**: `hasAnyAuthority('buyer','seller','admin')`
*   **参数**:
    *   `id` (Long, path, required): 竞价ID。
*   **返回值**: `Result<BiddingDetailResponse>` - 竞价详情信息。
*   **业务逻辑**: 调用 `biddingService.getBiddingDetail`。服务层会根据用户权限返回脱敏或完整信息。

#### getBiddingStats - 获取竞价统计信息
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/requirements/{requirementId}/biddings/stats`
*   **权限**: `hasAuthority('admin')`
*   **参数**:
    *   `requirementId` (Long, path, required): 需求ID。
*   **返回值**: `Result<BiddingStatsResponse>` - 竞价统计信息。
*   **业务逻辑**: 调用 `biddingService.getBiddingStats`。

#### getVisibleBiddingsByRequirementId - 获取可见的竞价列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/requirements/{requirementId}/biddings/visible`
*   **权限**: `hasAnyAuthority('buyer','seller','admin')`
*   **参数**:
    *   `requirementId` (Long, path, required): 需求ID。
    *   `page` (Integer, query, optional, default=1): 当前页码。
    *   `size` (Integer, query, optional, default=10): 每页大小。
*   **返回值**: `Result<IPage<BiddingDetailResponse>>` - 可见的竞价列表。
*   **业务逻辑**: 调用 `biddingService.getVisibleBiddingsByRequirementId`。

### 3.3 管理员操作接口

#### updateBiddingStatus - 管理员更改竞价状态
*   **HTTP方法**: `PUT`
*   **路径**: `/api/v1/biddings/admin/{id}/status`
*   **权限**: `hasAuthority('admin')`
*   **参数**:
    *   `id` (Long, path, required): 竞价ID。
    *   `status` (String, query, required): 新状态。
*   **返回值**: `Result<BiddingDetailResponse>` - 更新后的竞价详情。
*   **业务逻辑**: 调用 `biddingService.updateBiddingStatus`。

#### deleteBidding - 管理员删除竞价（逻辑删除）
*   **HTTP方法**: `DELETE`
*   **路径**: `/api/v1/biddings/admin/{id}`
*   **权限**: `hasAuthority('admin')`
*   **参数**:
    *   `id` (Long, path, required): 竞价ID。
*   **返回值**: `Result<Void>` - 操作结果。
*   **业务逻辑**: 调用 `biddingService.deleteBidding`。

#### getAllBiddings - 管理员获取所有竞价记录
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/biddings/admin/all`
*   **权限**: `hasAuthority('admin')`
*   **参数**: `auditStatus`, `sellerId`, `buyerId`, `minPrice`, `maxPrice`, `biddingType`, `page`, `size` (均为可选)。
*   **返回值**: `Result<IPage<BiddingDetailResponse>>` - 竞价记录分页列表。
*   **业务逻辑**: 调用 `biddingService.getAllBiddings`。

#### auditBidding - 管理员审核竞价
*   **HTTP方法**: `PUT`
*   **路径**: `/api/v1/biddings/admin/{id}/audit`
*   **权限**: `hasAuthority('admin')`
*   **参数**:
    *   `id` (Long, path, required): 竞价ID。
    *   `approved` (boolean, query, required): 是否审核通过。
    *   `remark` (String, query, optional): 审核备注。
*   **返回值**: `Result<BiddingDetailResponse>` - 审核后的竞价详情。
*   **业务逻辑**: 调用 `biddingService.auditBidding`。

#### getPendingAuditBiddings - 获取待审核的竞价列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/biddings/admin/pending-audit`
*   **权限**: `hasAuthority('admin')`
*   **参数**: `page`, `size`。
*   **返回值**: `Result<IPage<BiddingDetailResponse>>` - 待审核竞价分页列表。
*   **业务逻辑**: 调用 `biddingService.getPendingAuditBiddings`。

## 4. 业务规则

*   **权限控制**: 所有接口都通过 `@PreAuthorize` 注解进行严格的权限控制，确保不同角色用户只能访问其被授权的资源和操作。
*   **竞价生命周期**: 竞价的状态流转（提交、更新、取消、接受、拒绝）应遵循预定义的业务流程和状态机。
*   **数据隔离**: `getBiddingList` 和 `getBiddingDetail` 等查询接口会根据用户角色（买家、卖家、管理员）返回不同权限的数据，例如买家只能查看自己需求下的竞价，卖家只能查看自己提交的竞价。
*   **脱敏处理**: 对于非授权用户，敏感信息（如联系方式、具体报价）应进行脱敏处理。
*   **JSR-303校验**: 请求体中的DTO通过 `@Valid` 注解进行JSR-303校验，确保输入数据的合法性。
*   **统一响应**: 所有接口都返回 `Result` 对象，统一了成功和失败的响应格式，便于前端处理。

## 5. 使用示例

```java
// 1. 卖家提交竞价
async function submitNewBidding(biddingData) {
  try {
    const response = await axios.post('/api/v1/biddings', biddingData, {
      headers: { Authorization: `Bearer ${sellerToken}` }
    });
    console.log('竞价提交成功:', response.data.data);
  } catch (error) {
    console.error('竞价提交失败:', error.response.data);
  }
}

// 2. 买家获取其需求下的竞价列表
async function fetchBiddingsForRequirement(requirementId) {
  try {
    const response = await axios.get(`/api/v1/requirements/${requirementId}/biddings`, {
      params: { page: 1, size: 10 },
      headers: { Authorization: `Bearer ${buyerToken}` }
    });
    console.log('竞价列表:', response.data.data);
  } catch (error) {
    console.error('获取竞价列表失败:', error.response.data);
  }
}

// 3. 买家接受某个竞价
async function acceptBidding(biddingId) {
  try {
    const response = await axios.put(`/api/v1/biddings/${biddingId}/accept`, null, {
      headers: { Authorization: `Bearer ${buyerToken}` }
    });
    console.log('竞价接受成功:', response.data.data);
  } catch (error) {
    console.error('竞价接受失败:', error.response.data);
  }
}

// 4. 管理员获取所有待审核竞价
async function getPendingAuditBiddings() {
  try {
    const response = await axios.get('/api/v1/biddings/admin/pending-audit', {
      params: { page: 1, size: 10 },
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('待审核竞价:', response.data.data);
  } catch (error) {
    console.error('获取失败:', error.response.data);
  }
}

// 5. 测试示例 (MockMvc)
@SpringBootTest
@AutoConfigureMockMvc
class BiddingControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private BiddingService biddingService;

    @Test
    @WithMockUser(authorities = "seller")
    void testSubmitBidding_Success() throws Exception {
        BiddingSubmitRequest request = new BiddingSubmitRequest();
        request.setRequirementId(1L);
        request.setPrice(new BigDecimal("100.00"));
        // ... set other fields ...

        BiddingDetailResponse mockResponse = new BiddingDetailResponse();
        when(biddingService.submitBidding(any(BiddingSubmitRequest.class))).thenReturn(mockResponse);

        mockMvc.perform(post("/api/v1/biddings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(authorities = "buyer")
    void testAcceptBidding_Success() throws Exception {
        Long biddingId = 123L;
        BiddingDetailResponse mockResponse = new BiddingDetailResponse();
        doNothing().when(biddingService).validateBiddingOwnership(eq(biddingId), anyLong());
        when(biddingService.acceptBidding(eq(biddingId), anyLong())).thenReturn(mockResponse);

        mockMvc.perform(put("/api/v1/biddings/{id}/accept", biddingId)
                .with(SecurityMockMvcRequestPostProcessors.csrf())) // Spring Security CSRF protection
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(authorities = "user") // 非授权用户
    void testSubmitBidding_AccessDenied() throws Exception {
        BiddingSubmitRequest request = new BiddingSubmitRequest();
        // ...
        mockMvc.perform(post("/api/v1/biddings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }
}
```

## 6. 注意事项

*   **权限控制**: 控制器中大量使用了 `@PreAuthorize` 进行权限控制，这是系统安全的核心。在实际部署时，需要确保Spring Security配置正确，并且JWT或Session管理机制健全。
*   **业务异常**: 控制器捕获了 `BusinessException` 并返回统一的错误响应，这使得前端可以根据错误信息进行相应的提示。
*   **参数校验**: 请求体中的DTO通过 `@Valid` 注解进行JSR-303校验，确保输入数据的合法性。对于路径变量和查询参数，也应在服务层进行必要的业务校验。
*   **统一响应**: 所有接口都返回 `Result` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **职责分离**: 控制器仅负责请求的接收、参数的初步校验和转发，具体的业务逻辑和数据处理委托给 `BiddingService`。
*   **复杂查询**: `getBiddingList` 方法根据用户角色动态调整查询逻辑，这增加了代码的复杂性，但提高了灵活性。在服务层实现时，需要确保查询条件的正确组合。
*   **敏感信息处理**: `getBiddingDetail` 方法在服务层会根据用户权限进行脱敏处理，这是保护用户隐私的重要措施。
*   **事务管理**: 提交、更新、接受、拒绝等写操作的服务层方法应声明为事务性的 (`@Transactional`)，以保证数据操作的原子性。