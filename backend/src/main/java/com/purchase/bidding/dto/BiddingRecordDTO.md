# BiddingRecordDTO 数据传输对象文档

## 文件概述

`BiddingRecordDTO` 是竞价记录的数据传输对象，用于在不同层之间传递竞价相关数据。该类包含了主DTO类和多个内部静态类，分别用于不同的业务场景，如创建、更新、查询和统计等操作。

## 核心功能

### 主要组件
- **主DTO类**: 包含完整的竞价记录信息
- **SellerInfo**: 卖家详细信息的内嵌类
- **CreateRequest**: 创建竞价的请求DTO
- **UpdateRequest**: 更新竞价的请求DTO
- **StatusRequest**: 状态变更请求DTO
- **QueryRequest**: 查询请求DTO
- **StatsResponse**: 统计响应DTO

### 数据转换功能
- 实体对象与DTO之间的数据转换
- 列表字段与字符串字段的转换处理
- 业务逻辑相关的计算字段处理

## 接口说明

### 主DTO类字段

#### 基础信息
- `id`: 竞价记录ID
- `requirementId`: 关联需求ID
- `sellerId`: 卖家用户ID
- `biddingPrice`: 竞价报价
- `unit`: 报价单位
- `productName`: 产品名称
- `hsCode`: 海关编码

#### 媒体资源
- `images`: 产品图片列表（前端使用）
- `videos`: 产品视频列表（前端使用）
- `companyEnvironmentImages`: 公司环境照片（数据库存储格式）

#### 业务字段
- `status`: 竞价状态
- `isWinner`: 是否中标
- `isOwnBidding`: 是否为当前用户的竞价
- `attributesJson`: 扩展属性JSON

### 内部类说明

#### SellerInfo 卖家信息类
```java
public static class SellerInfo {
    private Long id;                              // 卖家ID
    private String username;                      // 用户名
    private String company;                       // 公司名称
    private String phone;                         // 联系电话
    private String email;                         // 邮箱
    private String companyEnvironmentImages;      // 公司环境照片
    private String companyAddress;                // 公司地址
    private String businessLicenseImage;          // 营业执照
    private String qualificationCertificateImage; // 资质证书
    private LocalDateTime createdAt;              // 注册时间
}
```

#### CreateRequest 创建请求类
```java
public static class CreateRequest {
    private Long requirementId;        // 需求ID（必填）
    private Long sellerId;            // 卖家ID（必填）
    private BigDecimal biddingPrice;  // 竞价报价（必填）
    private String unit;              // 单位
    private String productName;       // 产品名称（必填）
    private String hsCode;            // 海关编码
    private List<String> images;      // 产品图片
    private List<String> videos;      // 产品视频
    private String companyEnvironmentImages; // 公司环境照片
    private String description;       // 描述
    private LocalDate deliveryTime;   // 交货时间
}
```

#### QueryRequest 查询请求类
```java
public static class QueryRequest {
    private Long buyerId;           // 买家ID
    private String status;          // 状态筛选
    private BigDecimal minPrice;    // 最低价格
    private BigDecimal maxPrice;    // 最高价格
    private Integer page = 1;       // 页码（默认1）
    private Integer size = 10;      // 页大小（默认10）
}
```

#### StatsResponse 统计响应类
```java
public static class StatsResponse {
    private Long totalCount;      // 总数量
    private Long pendingCount;    // 待处理数量
    private Long acceptedCount;   // 已接受数量
    private Long rejectedCount;   // 已拒绝数量
    private Long cancelledCount;  // 已取消数量
    private BigDecimal minPrice;  // 最低价格
    private BigDecimal maxPrice;  // 最高价格
    private BigDecimal avgPrice;  // 平均价格
}
```

## 使用示例

### 创建竞价请求
```java
BiddingRecordDTO.CreateRequest request = new BiddingRecordDTO.CreateRequest();
request.setRequirementId(1001L);
request.setSellerId(2001L);
request.setBiddingPrice(new BigDecimal("150.00"));
request.setUnit("个");
request.setProductName("工业零件");
request.setHsCode("8421999090");
request.setImages(Arrays.asList("image1.jpg", "image2.jpg"));
request.setDescription("高质量工业零件，符合国际标准");
request.setDeliveryTime(LocalDate.now().plusDays(30));
```

### 查询竞价记录
```java
BiddingRecordDTO.QueryRequest queryRequest = new BiddingRecordDTO.QueryRequest();
queryRequest.setBuyerId(1001L);
queryRequest.setStatus("pending");
queryRequest.setMinPrice(new BigDecimal("100.00"));
queryRequest.setMaxPrice(new BigDecimal("500.00"));
queryRequest.setPage(1);
queryRequest.setSize(20);
```

### 状态变更请求
```java
BiddingRecordDTO.StatusRequest statusRequest = new BiddingRecordDTO.StatusRequest();
statusRequest.setBuyerId(1001L);
statusRequest.setReason("价格不符合预期");
```

### 数据转换示例
```java
// Entity转DTO
public BiddingRecordDTO convertToDTO(BiddingRecord entity) {
    BiddingRecordDTO dto = new BiddingRecordDTO();
    dto.setId(entity.getId());
    dto.setRequirementId(entity.getRequirementId());
    dto.setSellerId(entity.getSellerId());
    dto.setBiddingPrice(entity.getBiddingPrice());
    
    // 处理图片列表转换
    if (entity.getImages() != null) {
        dto.setImages(Arrays.asList(entity.getImages().split(",")));
    }
    
    // 处理视频列表转换
    if (entity.getVideos() != null) {
        dto.setVideos(Arrays.asList(entity.getVideos().split(",")));
    }
    
    return dto;
}
```

## 注意事项

### 数据转换
1. **图片/视频处理**: 数据库存储为逗号分隔字符串，DTO中使用List<String>便于前端处理
2. **空值处理**: 转换时需要检查null值，避免空指针异常
3. **日期格式**: 确保日期字段的格式转换正确

### 验证规则
1. **必填字段**: CreateRequest中的核心字段必须验证非空
2. **价格验证**: 价格字段应验证为正数
3. **日期验证**: 交货时间应不早于当前日期

### 性能考虑
1. **分页查询**: QueryRequest默认分页大小为10，避免大量数据传输
2. **字段选择**: 根据业务需要选择性返回字段，减少数据传输量
3. **缓存策略**: 对于频繁查询的统计数据可考虑缓存

### 安全性
1. **权限控制**: isOwnBidding字段用于前端权限控制显示
2. **敏感信息**: 卖家联系方式等敏感信息需要权限控制
3. **数据脱敏**: 在某些场景下可能需要对敏感数据进行脱敏处理
