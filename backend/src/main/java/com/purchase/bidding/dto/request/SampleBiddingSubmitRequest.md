# SampleBiddingSubmitRequest 样品竞价提交请求文档

## 文件概述

`SampleBiddingSubmitRequest` 是专门用于样品竞价提交的请求数据传输对象。该类专注于样品竞价业务场景，包含了样品竞价特有的字段和验证规则，与普通竞价相比更加注重样品的价格、数量和规格说明。

## 核心功能

### 主要职责
- 封装样品竞价提交的专用请求参数
- 提供样品竞价特有的数据验证规则
- 简化样品竞价的数据结构，专注核心业务
- 确保样品竞价数据的完整性和有效性

### 业务特点
- 专门针对样品竞价场景设计
- 强调样品价格、数量和规格的重要性
- 支持完整的产品信息和公司信息
- 提供灵活的媒体资源上传

## 接口说明

### 必填字段

#### 核心业务字段
- `requirementId`: 需求ID，关联具体的采购需求
- `samplePrice`: 样品价格，必须大于0.01
- `sampleQuantity`: 样品数量，必须大于0
- `productName`: 产品名称，标识样品产品
- `description`: 竞价描述，详细说明样品特点
- `deliveryTime`: 预计交付时间，样品发送时间

### 可选字段

#### 样品详情
- `sampleSpecification`: 样品规格说明，详细描述样品特性
- `unit`: 销售单位（如：个、套、包等）
- `hsCode`: 海关商品编码，用于国际贸易

#### 媒体资源
- `images`: 产品图片URL列表（逗号分隔字符串）
- `videos`: 产品视频URL列表（逗号分隔字符串）
- `companyEnvironmentImages`: 公司环境照片（逗号分隔字符串）

#### 公司信息
- `company`: 公司名称
- `phone`: 联系电话
- `email`: 电子邮箱
- `companyAddress`: 公司地址

#### 扩展信息
- `attributesJson`: 需求属性JSON，存储产品的特定属性

## 使用示例

### 基础样品竞价提交
```java
SampleBiddingSubmitRequest request = new SampleBiddingSubmitRequest();

// 必填字段
request.setRequirementId(1001L);
request.setSamplePrice(new BigDecimal("25.50"));
request.setSampleQuantity(5);
request.setProductName("工业传感器样品");
request.setDescription("高精度工业传感器样品，包含完整技术文档和测试报告");
request.setDeliveryTime(LocalDate.now().plusDays(7));

// 样品规格
request.setSampleSpecification("尺寸：50x30x20mm，精度：±0.1%，工作温度：-40°C至+85°C");
request.setUnit("个");
request.setHsCode("9026209090");

// 媒体资源
request.setImages("sensor1.jpg,sensor2.jpg,sensor_detail.jpg");
request.setVideos("sensor_demo.mp4");
request.setCompanyEnvironmentImages("factory_overview.jpg,production_line.jpg");

// 公司信息
request.setCompany("精密传感器科技有限公司");
request.setPhone("+86 755-12345678");
request.setEmail("<EMAIL>");
request.setCompanyAddress("深圳市南山区高新技术产业园区");

// 产品属性
request.setAttributesJson("{\"type\":\"压力传感器\",\"range\":\"0-100bar\",\"output\":\"4-20mA\"}");
```

### 控制器中的使用
```java
@PostMapping("/sample/submit")
public Result<Long> submitSampleBidding(@Valid @RequestBody SampleBiddingSubmitRequest request) {
    // 业务逻辑验证
    validateSampleBiddingRequest(request);
    
    // 调用服务层处理
    Long biddingId = sampleBiddingService.submitSampleBidding(request);
    return Result.success(biddingId);
}

private void validateSampleBiddingRequest(SampleBiddingSubmitRequest request) {
    // 验证交付时间
    if (request.getDeliveryTime().isBefore(LocalDate.now())) {
        throw new BusinessException("交付时间不能早于当前日期");
    }
    
    // 验证样品数量合理性
    if (request.getSampleQuantity() > 100) {
        throw new BusinessException("样品数量不能超过100个");
    }
    
    // 验证样品价格合理性
    if (request.getSamplePrice().compareTo(new BigDecimal("10000")) > 0) {
        throw new BusinessException("样品价格不能超过10000元");
    }
}
```

### 数据转换示例
```java
// Request转Entity
public BiddingRecord convertToEntity(SampleBiddingSubmitRequest request, Long sellerId) {
    BiddingRecord record = new BiddingRecord();
    
    // 基础字段
    record.setRequirementId(request.getRequirementId());
    record.setSellerId(sellerId);
    record.setProductName(request.getProductName());
    record.setDescription(request.getDescription());
    record.setDeliveryTime(request.getDeliveryTime());
    
    // 样品特有字段
    record.setBiddingType("sample");
    record.setSamplePrice(request.getSamplePrice());
    record.setSampleQuantity(request.getSampleQuantity());
    record.setSampleSpecification(request.getSampleSpecification());
    
    // 产品信息
    record.setUnit(request.getUnit());
    record.setHsCode(request.getHsCode());
    record.setImages(request.getImages());
    record.setVideos(request.getVideos());
    record.setCompanyEnvironmentImages(request.getCompanyEnvironmentImages());
    
    // 公司信息
    record.setCompany(request.getCompany());
    record.setPhone(request.getPhone());
    record.setEmail(request.getEmail());
    record.setCompanyAddress(request.getCompanyAddress());
    
    // 扩展属性
    record.setAttributesJson(request.getAttributesJson());
    
    // 设置默认状态
    record.setStatus("pending");
    record.setAuditStatus("pending_audit");
    record.setWinner(false);
    
    return record;
}
```

### 服务层处理示例
```java
@Service
public class SampleBiddingServiceImpl implements SampleBiddingService {
    
    @Override
    @Transactional
    public Long submitSampleBidding(SampleBiddingSubmitRequest request) {
        // 1. 验证需求是否存在且支持样品竞价
        validateRequirement(request.getRequirementId());
        
        // 2. 检查是否已经提交过样品竞价
        checkDuplicateSubmission(request.getRequirementId(), getCurrentUserId());
        
        // 3. 转换并保存竞价记录
        BiddingRecord record = convertToEntity(request, getCurrentUserId());
        biddingRecordMapper.insert(record);
        
        // 4. 发送通知
        notificationService.notifySampleBiddingSubmitted(record);
        
        return record.getId();
    }
    
    private void validateRequirement(Long requirementId) {
        // 验证需求是否存在
        // 验证需求是否支持样品竞价
        // 验证需求是否在有效期内
    }
    
    private void checkDuplicateSubmission(Long requirementId, Long sellerId) {
        // 检查是否已经提交过样品竞价
    }
}
```

## 注意事项

### 数据验证
1. **价格验证**: 样品价格使用@DecimalMin确保大于0.01
2. **数量验证**: 样品数量使用@Min确保大于0
3. **日期验证**: 交付时间应在合理范围内
4. **业务验证**: 需要额外的业务逻辑验证

### 样品竞价特点
1. **小批量**: 样品数量通常较小，一般不超过100个
2. **快速交付**: 样品交付时间通常较短，一般在1-2周内
3. **详细规格**: 样品规格说明应该详细，便于买家评估
4. **完整信息**: 需要提供完整的产品和公司信息

### 安全考虑
1. **权限验证**: 确保只有合法卖家可以提交样品竞价
2. **重复提交**: 防止同一卖家对同一需求重复提交
3. **数据验证**: 对所有输入数据进行严格验证
4. **文件安全**: 上传的图片和视频需要安全检查

### 性能优化
1. **异步处理**: 文件上传和通知发送可以异步处理
2. **缓存策略**: 频繁查询的需求信息可以缓存
3. **批量操作**: 多个样品竞价可以考虑批量处理
4. **索引优化**: 为查询字段建立合适的数据库索引
