package com.purchase.bidding.dto.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class BiddingDetailResponse {
    private Long id;
    private Long requirementId;
    private Long sellerId;
    private BigDecimal biddingPrice;
    private String unit;
    private String productName;
    private String hsCode;
    private String description;
    private LocalDate deliveryTime;
    private List<String> images;
    private List<String> videos;
    // 公司/工厂环境照片和视频，在convertToDetailResponse方法中会转换为List<String>返回给前端
    private List<String> companyEnvironmentImages;
    // 公司详细地址
    private String companyAddress;
    private String status;
    private Boolean isWinner;
    
    /**
     * 审核状态：
     * pending_audit - 待审核
     * approved - 审核通过
     * rejected - 审核拒绝
     */
    private String auditStatus;
    
    /**
     * 审核备注
     */
    private String auditRemark;
    
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean isOwnBidding;
    
    // 新增字段: 属性JSON数据，保持字符串格式
    private String attributesJson;
    
    // 用户联系信息字段
    private String company;
    private String contactPhone; // 包含区号的完整电话号码，如："+86 13800138000"
    private String email;
    
    // 样品相关字段
    /**
     * 竞价类型：purchase-普通竞价，sample-样品竞价
     */
    private String biddingType;
    
    /**
     * 样品价格（仅样品竞价时使用）
     */
    private BigDecimal samplePrice;
    
    /**
     * 样品数量（仅样品竞价时使用）
     */
    private Integer sampleQuantity;
    
    /**
     * 样品规格说明（仅样品竞价时使用）
     */
    private String sampleSpecification;
}