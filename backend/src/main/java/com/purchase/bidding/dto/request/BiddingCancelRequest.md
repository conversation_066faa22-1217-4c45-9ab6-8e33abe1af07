# BiddingCancelRequest 投标取消请求DTO文档

## 文件概述

`BiddingCancelRequest` 是投标取消请求数据传输对象，用于封装投标取消操作所需的参数。该DTO设计简洁，包含必要的验证注解，确保投标取消操作的数据完整性和业务安全性，为投标管理提供标准化的取消操作接口。

## 核心功能

### 主要职责
- **取消参数封装**: 封装投标取消操作所需的参数
- **数据验证**: 通过验证注解确保参数的有效性
- **业务安全**: 确保只有合法的卖家可以取消投标
- **接口标准化**: 为投标取消提供标准化的请求格式
- **操作审计**: 支持投标取消操作的审计和追踪

### 业务特点
- 简洁的请求参数设计
- 强制的参数验证
- 基于卖家身份的权限控制
- 支持业务操作审计
- 标准化的取消流程

## 接口说明

### 核心字段

#### sellerId
- **类型**: Long
- **描述**: 卖家ID
- **验证**: @NotNull(message = "卖家ID不能为空")
- **用途**: 标识执行取消操作的卖家身份
- **业务规则**: 只有投标的创建者（卖家）才能取消自己的投标

## 使用示例

### 基础使用示例
```java
@Service
public class BiddingCancelService {
    
    @Autowired
    private BiddingService biddingService;
    
    @Autowired
    private BiddingValidator biddingValidator;
    
    public ApiResponse<Void> cancelBidding(Long biddingId, BiddingCancelRequest request) {
        try {
            // 验证请求参数
            validateCancelRequest(request);
            
            // 获取投标信息
            Bidding bidding = biddingService.getById(biddingId);
            if (bidding == null) {
                return ApiResponse.error("投标不存在");
            }
            
            // 验证权限：只有投标创建者可以取消
            if (!bidding.getSellerId().equals(request.getSellerId())) {
                return ApiResponse.error("无权限取消此投标");
            }
            
            // 验证投标状态：只有待处理状态的投标可以取消
            if (!BiddingStatus.PENDING.equals(bidding.getStatus())) {
                return ApiResponse.error("当前状态的投标无法取消");
            }
            
            // 执行取消操作
            biddingService.cancelBidding(biddingId, request.getSellerId());
            
            return ApiResponse.success("投标取消成功");
            
        } catch (Exception e) {
            log.error("取消投标失败: biddingId={}, sellerId={}", biddingId, request.getSellerId(), e);
            return ApiResponse.error("取消投标失败: " + e.getMessage());
        }
    }
    
    private void validateCancelRequest(BiddingCancelRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("取消请求不能为空");
        }
        
        if (request.getSellerId() == null) {
            throw new IllegalArgumentException("卖家ID不能为空");
        }
        
        // 验证卖家是否存在
        if (!userService.existsById(request.getSellerId())) {
            throw new IllegalArgumentException("卖家不存在");
        }
    }
}
```

### 控制器中使用
```java
@RestController
@RequestMapping("/api/bidding")
public class BiddingController {
    
    @Autowired
    private BiddingCancelService biddingCancelService;
    
    @PostMapping("/{biddingId}/cancel")
    public ApiResponse<Void> cancelBidding(
            @PathVariable Long biddingId,
            @RequestBody @Valid BiddingCancelRequest request) {
        
        return biddingCancelService.cancelBidding(biddingId, request);
    }
    
    @PostMapping("/batch-cancel")
    public ApiResponse<BatchOperationResult> batchCancelBiddings(
            @RequestBody @Valid BatchBiddingCancelRequest request) {
        
        BatchOperationResult result = new BatchOperationResult();
        
        for (Long biddingId : request.getBiddingIds()) {
            try {
                BiddingCancelRequest cancelRequest = new BiddingCancelRequest();
                cancelRequest.setSellerId(request.getSellerId());
                
                ApiResponse<Void> response = biddingCancelService.cancelBidding(biddingId, cancelRequest);
                
                if (response.isSuccess()) {
                    result.addSuccess(biddingId);
                } else {
                    result.addFailure(biddingId, response.getMessage());
                }
            } catch (Exception e) {
                result.addFailure(biddingId, e.getMessage());
            }
        }
        
        return ApiResponse.success(result);
    }
}
```

### 前端使用示例
```javascript
// 取消单个投标
const cancelBidding = async (biddingId, sellerId) => {
  try {
    const response = await fetch(`/api/bidding/${biddingId}/cancel`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sellerId: sellerId
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      return result;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('取消投标失败:', error);
    throw error;
  }
};

// 批量取消投标
const batchCancelBiddings = async (biddingIds, sellerId) => {
  try {
    const response = await fetch('/api/bidding/batch-cancel', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        biddingIds: biddingIds,
        sellerId: sellerId
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('批量取消投标失败:', error);
    throw error;
  }
};

// React投标取消组件
const BiddingCancelButton = ({ bidding, onCancelSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  
  const handleCancel = async () => {
    setLoading(true);
    try {
      await cancelBidding(bidding.id, bidding.sellerId);
      
      // 显示成功消息
      toast.success('投标取消成功');
      
      // 调用成功回调
      if (onCancelSuccess) {
        onCancelSuccess(bidding.id);
      }
      
      setShowConfirm(false);
    } catch (error) {
      toast.error(`取消失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  const canCancel = bidding.status === 'PENDING' && bidding.sellerId === getCurrentUserId();
  
  if (!canCancel) {
    return null;
  }
  
  return (
    <>
      <button
        className="btn btn-outline-danger"
        onClick={() => setShowConfirm(true)}
        disabled={loading}
      >
        {loading ? '取消中...' : '取消投标'}
      </button>
      
      {showConfirm && (
        <ConfirmDialog
          title="确认取消投标"
          message="确定要取消这个投标吗？取消后无法恢复。"
          onConfirm={handleCancel}
          onCancel={() => setShowConfirm(false)}
          loading={loading}
        />
      )}
    </>
  );
};

// 批量取消组件
const BatchCancelBiddings = ({ selectedBiddings, onCancelSuccess }) => {
  const [loading, setLoading] = useState(false);
  
  const handleBatchCancel = async () => {
    setLoading(true);
    try {
      const biddingIds = selectedBiddings.map(b => b.id);
      const sellerId = getCurrentUserId();
      
      const result = await batchCancelBiddings(biddingIds, sellerId);
      
      // 显示结果
      if (result.successCount > 0) {
        toast.success(`成功取消 ${result.successCount} 个投标`);
      }
      
      if (result.failureCount > 0) {
        toast.warning(`${result.failureCount} 个投标取消失败`);
      }
      
      // 调用成功回调
      if (onCancelSuccess) {
        onCancelSuccess(result.successIds);
      }
      
    } catch (error) {
      toast.error(`批量取消失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  const canCancelBiddings = selectedBiddings.filter(
    b => b.status === 'PENDING' && b.sellerId === getCurrentUserId()
  );
  
  if (canCancelBiddings.length === 0) {
    return null;
  }
  
  return (
    <button
      className="btn btn-outline-danger"
      onClick={handleBatchCancel}
      disabled={loading}
    >
      {loading ? '取消中...' : `批量取消 (${canCancelBiddings.length})`}
    </button>
  );
};
```

### 扩展请求示例
```java
// 扩展版本的取消请求
@Data
public class EnhancedBiddingCancelRequest {
    
    @NotNull(message = "卖家ID不能为空")
    private Long sellerId;
    
    @Size(max = 500, message = "取消原因不能超过500字符")
    private String cancelReason;
    
    private Boolean notifyBuyer = true;
    
    private Boolean refundDeposit = false;
    
    // 验证方法
    public void validate() {
        if (sellerId == null) {
            throw new IllegalArgumentException("卖家ID不能为空");
        }
        
        if (cancelReason != null && cancelReason.trim().length() > 500) {
            throw new IllegalArgumentException("取消原因不能超过500字符");
        }
    }
    
    // 构建器模式
    public static class Builder {
        private EnhancedBiddingCancelRequest request = new EnhancedBiddingCancelRequest();
        
        public Builder sellerId(Long sellerId) {
            request.setSellerId(sellerId);
            return this;
        }
        
        public Builder cancelReason(String reason) {
            request.setCancelReason(reason);
            return this;
        }
        
        public Builder notifyBuyer(boolean notify) {
            request.setNotifyBuyer(notify);
            return this;
        }
        
        public Builder refundDeposit(boolean refund) {
            request.setRefundDeposit(refund);
            return this;
        }
        
        public EnhancedBiddingCancelRequest build() {
            request.validate();
            return request;
        }
    }
    
    public static Builder builder() {
        return new Builder();
    }
}

// 使用示例
EnhancedBiddingCancelRequest request = EnhancedBiddingCancelRequest.builder()
    .sellerId(123L)
    .cancelReason("价格变动，无法履行")
    .notifyBuyer(true)
    .refundDeposit(true)
    .build();
```

### 业务规则验证
```java
@Component
public class BiddingCancelValidator {
    
    @Autowired
    private BiddingService biddingService;
    
    @Autowired
    private UserService userService;
    
    public void validateCancelRequest(Long biddingId, BiddingCancelRequest request) {
        // 基础参数验证
        if (biddingId == null) {
            throw new IllegalArgumentException("投标ID不能为空");
        }
        
        if (request == null || request.getSellerId() == null) {
            throw new IllegalArgumentException("卖家ID不能为空");
        }
        
        // 投标存在性验证
        Bidding bidding = biddingService.getById(biddingId);
        if (bidding == null) {
            throw new BusinessException("投标不存在");
        }
        
        // 权限验证
        if (!bidding.getSellerId().equals(request.getSellerId())) {
            throw new BusinessException("无权限取消此投标");
        }
        
        // 状态验证
        if (!canCancelBidding(bidding)) {
            throw new BusinessException("当前状态的投标无法取消");
        }
        
        // 时间限制验证
        if (!isWithinCancelTimeLimit(bidding)) {
            throw new BusinessException("超过取消时间限制");
        }
    }
    
    private boolean canCancelBidding(Bidding bidding) {
        return BiddingStatus.PENDING.equals(bidding.getStatus());
    }
    
    private boolean isWithinCancelTimeLimit(Bidding bidding) {
        // 投标后24小时内可以取消
        LocalDateTime cancelDeadline = bidding.getCreatedAt().plusHours(24);
        return LocalDateTime.now().isBefore(cancelDeadline);
    }
}
```

## 注意事项

### 业务规则
1. **权限控制**: 只有投标创建者可以取消自己的投标
2. **状态限制**: 只有特定状态的投标可以取消
3. **时间限制**: 可能存在取消时间窗口限制
4. **业务影响**: 考虑取消对其他业务流程的影响

### 数据安全
1. **身份验证**: 确保请求者身份的真实性
2. **权限验证**: 严格验证操作权限
3. **操作审计**: 记录取消操作的审计日志
4. **数据完整性**: 确保取消操作的数据一致性

### 用户体验
1. **确认机制**: 提供取消确认机制防止误操作
2. **状态反馈**: 及时反馈取消操作的结果
3. **错误提示**: 提供清晰的错误信息和处理建议
4. **批量操作**: 支持批量取消提高操作效率

### 系统设计
1. **幂等性**: 确保取消操作的幂等性
2. **事务管理**: 使用事务确保操作的原子性
3. **异常处理**: 完善的异常处理机制
4. **性能优化**: 优化批量取消的性能
