package com.purchase.bidding.dto.request;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 样品竞价提交请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class SampleBiddingSubmitRequest {
    
    /**
     * 需求ID
     */
    @NotNull(message = "需求ID不能为空")
    private Long requirementId;
    
    /**
     * 样品价格
     */
    @NotNull(message = "样品价格不能为空")
    @DecimalMin(value = "0.01", message = "样品价格必须大于0")
    private BigDecimal samplePrice;
    
    /**
     * 样品数量
     */
    @NotNull(message = "样品数量不能为空")
    @Min(value = 1, message = "样品数量必须大于0")
    private Integer sampleQuantity;
    
    /**
     * 样品规格说明
     */
    private String sampleSpecification;
    
    /**
     * 产品名称
     */
    @NotNull(message = "产品名称不能为空")
    private String productName;
    
    /**
     * 竞价描述
     */
    @NotNull(message = "竞价描述不能为空")
    private String description;
    
    /**
     * 预计交付时间
     */
    @NotNull(message = "预计交付时间不能为空")
    private LocalDate deliveryTime;
    
    /**
     * 产品图片URL列表
     */
    private String images;
    
    /**
     * 产品视频URL列表
     */
    private String videos;
    
    /**
     * 公司环境照片
     */
    private String companyEnvironmentImages;
    
    /**
     * 公司地址
     */
    private String companyAddress;
    
    /**
     * 海关商品编码
     */
    private String hsCode;
    
    /**
     * 需求属性JSON
     */
    private String attributesJson;
    
    /**
     * 销售单位
     */
    private String unit;
    
    /**
     * 公司名称
     */
    private String company;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 电子邮箱
     */
    private String email;
} 