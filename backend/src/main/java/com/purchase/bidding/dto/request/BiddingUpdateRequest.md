# BiddingUpdateRequest 竞价更新请求文档

## 文件概述

`BiddingUpdateRequest` 是用于更新已提交竞价信息的请求数据传输对象。该类允许卖家在竞价状态允许的情况下修改竞价内容，支持普通竞价和样品竞价的更新操作。

## 核心功能

### 主要职责
- 封装竞价更新时的请求参数
- 提供与提交竞价相同的验证规则
- 支持部分字段更新或全量更新
- 维护竞价数据的一致性

### 更新场景
- 价格调整：根据市场情况调整竞价
- 信息完善：补充或修正产品信息
- 交期调整：修改预计交付时间
- 媒体更新：更新产品图片或视频

## 接口说明

### 必填字段
- `sellerId`: 卖家用户ID，确保更新权限
- `biddingPrice`: 竞价金额，必须大于0
- `description`: 竞价描述，不能为空
- `deliveryTime`: 预计交付时间，不能为空

### 可选字段

#### 产品信息
- `unit`: 报价单位
- `productName`: 产品名称
- `hsCode`: 海关编码

#### 媒体资源
- `images`: 产品图片列表
- `videos`: 产品视频列表
- `companyEnvironmentImages`: 公司环境照片

#### 联系信息
- `companyName`: 公司名称
- `contactPhone`: 联系电话
- `email`: 联系邮箱
- `companyAddress`: 公司地址

#### 样品竞价字段
- `biddingType`: 竞价类型
- `samplePrice`: 样品价格
- `sampleQuantity`: 样品数量
- `sampleSpecification`: 样品规格说明

## 使用示例

### 基础竞价更新
```java
BiddingUpdateRequest request = new BiddingUpdateRequest();

// 必填字段
request.setSellerId(2001L);
request.setBiddingPrice(new BigDecimal("145.00")); // 价格调整
request.setDescription("高质量工业零件，符合国际标准，现提供更优惠价格");
request.setDeliveryTime(LocalDate.now().plusDays(25)); // 交期提前

// 更新产品信息
request.setProductName("精密工业零件（升级版）");
request.setUnit("个");
request.setHsCode("8421999090");

// 更新媒体资源
request.setImages(Arrays.asList("product1_new.jpg", "product2_new.jpg", "certificate.jpg"));
request.setVideos(Arrays.asList("demo_updated.mp4"));
request.setCompanyEnvironmentImages("factory_new.jpg,workshop_updated.jpg");

// 更新联系信息
request.setCompanyName("ABC精密制造有限公司");
request.setContactPhone("+86 13800138001"); // 更新联系方式
request.setEmail("<EMAIL>");
request.setCompanyAddress("广东省深圳市南山区科技园南区A座");

// 更新属性
request.setAttributesJson("{\"material\":\"316不锈钢\",\"precision\":\"±0.005mm\",\"certification\":\"ISO9001,CE\"}");
```

### 样品竞价更新
```java
BiddingUpdateRequest sampleUpdate = new BiddingUpdateRequest();

// 基础信息更新
request.setSellerId(2001L);
request.setBiddingPrice(new BigDecimal("120.00"));
request.setDescription("样品竞价更新，提供更详细的技术规格");
request.setDeliveryTime(LocalDate.now().plusDays(10));

// 样品信息更新
request.setBiddingType("sample");
request.setSamplePrice(new BigDecimal("45.00")); // 样品价格调整
request.setSampleQuantity(5); // 样品数量调整
request.setSampleSpecification("提供5个标准规格样品，包含完整功能演示、质量检测报告和技术文档");
```

### 控制器中的使用
```java
@PutMapping("/{biddingId}")
public Result<Void> updateBidding(
    @PathVariable Long biddingId,
    @Valid @RequestBody BiddingUpdateRequest request) {
    
    // 验证更新权限
    validateUpdatePermission(biddingId, request.getSellerId());
    
    // 验证竞价状态是否允许更新
    validateBiddingStatus(biddingId);
    
    // 样品竞价特殊验证
    if ("sample".equals(request.getBiddingType())) {
        validateSampleFields(request);
    }
    
    // 执行更新
    biddingService.updateBidding(biddingId, request);
    
    return Result.success();
}

private void validateUpdatePermission(Long biddingId, Long sellerId) {
    BiddingRecord existing = biddingService.getById(biddingId);
    if (!existing.getSellerId().equals(sellerId)) {
        throw new BusinessException("无权限更新此竞价");
    }
}

private void validateBiddingStatus(Long biddingId) {
    BiddingRecord existing = biddingService.getById(biddingId);
    if (!"pending".equals(existing.getStatus())) {
        throw new BusinessException("当前状态不允许更新竞价");
    }
}
```

### 服务层更新逻辑
```java
@Service
public class BiddingServiceImpl implements BiddingService {
    
    @Override
    @Transactional
    public void updateBidding(Long biddingId, BiddingUpdateRequest request) {
        // 1. 获取现有竞价记录
        BiddingRecord existing = getById(biddingId);
        
        // 2. 验证更新权限和状态
        validateUpdateConditions(existing, request);
        
        // 3. 更新字段
        updateBiddingFields(existing, request);
        
        // 4. 保存更新
        biddingRecordMapper.updateById(existing);
        
        // 5. 记录更新日志
        logBiddingUpdate(biddingId, request);
        
        // 6. 发送通知
        notificationService.notifyBiddingUpdated(existing);
    }
    
    private void updateBiddingFields(BiddingRecord existing, BiddingUpdateRequest request) {
        // 更新基础字段
        existing.setBiddingPrice(request.getBiddingPrice());
        existing.setDescription(request.getDescription());
        existing.setDeliveryTime(request.getDeliveryTime());
        
        // 更新产品信息
        if (request.getProductName() != null) {
            existing.setProductName(request.getProductName());
        }
        if (request.getUnit() != null) {
            existing.setUnit(request.getUnit());
        }
        if (request.getHsCode() != null) {
            existing.setHsCode(request.getHsCode());
        }
        
        // 更新媒体资源
        if (request.getImages() != null) {
            existing.setImages(String.join(",", request.getImages()));
        }
        if (request.getVideos() != null) {
            existing.setVideos(String.join(",", request.getVideos()));
        }
        if (request.getCompanyEnvironmentImages() != null) {
            existing.setCompanyEnvironmentImages(request.getCompanyEnvironmentImages());
        }
        
        // 更新联系信息
        if (request.getCompanyName() != null) {
            existing.setCompany(request.getCompanyName());
        }
        if (request.getContactPhone() != null) {
            existing.setPhone(request.getContactPhone());
        }
        if (request.getEmail() != null) {
            existing.setEmail(request.getEmail());
        }
        if (request.getCompanyAddress() != null) {
            existing.setCompanyAddress(request.getCompanyAddress());
        }
        
        // 更新样品信息
        if ("sample".equals(request.getBiddingType())) {
            existing.setSamplePrice(request.getSamplePrice());
            existing.setSampleQuantity(request.getSampleQuantity());
            existing.setSampleSpecification(request.getSampleSpecification());
        }
        
        // 更新属性
        if (request.getAttributesJson() != null) {
            existing.setAttributesJson(request.getAttributesJson());
        }
    }
}
```

## 注意事项

### 更新权限
1. **身份验证**: 只有竞价的提交者可以更新
2. **状态限制**: 只有"pending"状态的竞价可以更新
3. **时间限制**: 可以设置更新的时间窗口限制
4. **次数限制**: 可以限制更新次数防止频繁修改

### 数据一致性
1. **原子操作**: 更新操作应该是原子性的
2. **版本控制**: 考虑使用乐观锁防止并发更新
3. **审计日志**: 记录所有更新操作便于追踪
4. **数据备份**: 保留更新前的数据快照

### 业务规则
1. **价格变动**: 价格调整可能需要重新审核
2. **重要信息**: 关键信息变更可能影响竞价有效性
3. **通知机制**: 更新后需要通知相关方
4. **状态重置**: 某些更新可能需要重置审核状态

### 性能考虑
1. **部分更新**: 只更新变更的字段
2. **批量操作**: 多个字段更新合并为一次操作
3. **缓存更新**: 及时更新相关缓存
4. **异步通知**: 通知发送可以异步处理
