package com.purchase.bidding.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 管理员样品竞价状态更新请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class AdminSampleBiddingStatusUpdateRequest {
    
    /**
     * 新的竞价状态
     * pending - 待处理
     * accepted - 已中标
     * rejected - 已拒绝
     * cancelled - 已取消
     * sample_accepted - 样品已接受
     */
    @NotBlank(message = "竞价状态不能为空")
    private String status;
    
    /**
     * 状态更改备注
     */
    private String statusRemark;
} 