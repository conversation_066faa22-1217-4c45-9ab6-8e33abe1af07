## 类用途
管理员样品竞价审核请求DTO

## 字段说明
| 字段名 | 类型 | 必填 | 校验规则 | 描述 |
|-------|------|-----|---------|-----|
| biddingId | String | 是 | 长度≤32 | 竞价单ID |
| status | AuditStatus | 是 | 非空 | 审核状态（APPROVED/REJECTED） |
| comment | String | 否 | 长度≤200 | 审核备注 |
| operator | String | 是 | 长度≤50 | 操作人姓名 |
| attachments | List<String> | 否 | 最大5个 | 审核附件URL |

## 枚举定义
### AuditStatus
| 值 | 描述 |
|---|-----|
| APPROVED | 审核通过 |
| REJECTED | 审核驳回 |
| PENDING | 待补充材料 |

## 业务规则
- 驳回时必须填写comment
- 附件仅支持PDF/图片格式
- 操作人信息需记录操作日志
- 状态变更需触发工作流通知

## 使用示例
```java
// 创建审核请求示例
AdminSampleBiddingAuditRequest request = new AdminSampleBiddingAuditRequest(
    "bid123",
    AuditStatus.APPROVED,
    "样品质量符合要求",
    "admin01",
    List.of("http://.../attachment1.pdf")
);
```

## 注意事项
- 生产环境需加密敏感字段
- 重要操作需二次确认
- 需验证操作人权限