package com.purchase.bidding.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class BiddingRecordDTO {
    private Long id;
    private Long requirementId;
    private Long sellerId;
    private BigDecimal biddingPrice;
    private String unit;
    private String productName;
    private String hsCode;
    private List<String> images;
    private List<String> videos;
    // 公司/工厂环境照片和视频，数据库存储为逗号分隔的字符串
    private String companyEnvironmentImages;
    // 公司详细地址
    private String companyAddress;
    private String description;
    private LocalDate deliveryTime;
    private String status;
    private Boolean isWinner;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 卖家信息
    private String company;
    private String phone;
    private String email;
    
    // 属性JSON
    private String attributesJson;
    
    // 是否为当前用户的竞价
    private Boolean isOwnBidding;
    
    @Data
    public static class SellerInfo {
        private Long id;
        private String username;
        private String company;
        private String phone;
        private String email;
        // 公司/工厂环境照片和视频，数据库存储为逗号分隔的字符串
        private String companyEnvironmentImages;
        // 公司详细地址
        private String companyAddress;
        private String businessLicenseImage;
        private String qualificationCertificateImage;
        private LocalDateTime createdAt;
    }
    
    // 用于请求的构造函数
    public BiddingRecordDTO() {}
    
    // 用于创建竞价的请求DTO
    @Data
    public static class CreateRequest {
        private Long requirementId;
        private Long sellerId;
        private BigDecimal biddingPrice;
        private String unit;
        private String productName;
        private String hsCode;
        private List<String> images;
        private List<String> videos;
        // 公司/工厂环境照片和视频，前端传递的是逗号分隔的字符串
        private String companyEnvironmentImages;
        private String description;
        private LocalDate deliveryTime;
    }
    
    // 用于更新竞价的请求DTO
    @Data
    public static class UpdateRequest {
        private Long sellerId;
        private BigDecimal biddingPrice;
        private String unit;
        private String productName;
        private String hsCode;
        private List<String> images;
        private List<String> videos;
        // 公司/工厂环境照片和视频，前端传递的是逗号分隔的字符串
        private String companyEnvironmentImages;
        private String description;
        private LocalDate deliveryTime;
    }
    
    // 用于竞价状态变更的请求DTO
    @Data
    public static class StatusRequest {
        private Long buyerId;
        private String reason;  // 用于拒绝竞价时的原因
    }
    
    // 用于竞价查询的请求DTO
    @Data
    public static class QueryRequest {
        private Long buyerId;
        private String status;
        private BigDecimal minPrice;
        private BigDecimal maxPrice;
        private Integer page = 1;
        private Integer size = 10;
    }
    
    // 用于竞价统计的响应DTO
    @Data
    public static class StatsResponse {
        private Long totalCount;
        private Long pendingCount;
        private Long acceptedCount;
        private Long rejectedCount;
        private Long cancelledCount;
        private BigDecimal minPrice;
        private BigDecimal maxPrice;
        private BigDecimal avgPrice;
    }
}