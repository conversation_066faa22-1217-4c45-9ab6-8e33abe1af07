# BiddingDetailResponse 竞价详情响应DTO文档

## 文件概述

`BiddingDetailResponse` 是竞价详情响应数据传输对象，用于向前端返回竞价的完整详细信息。该DTO包含了竞价的基本信息、产品详情、多媒体资源、审核状态、联系信息以及样品相关信息，为前端提供丰富的竞价展示数据。

## 核心功能

### 主要职责
- **竞价详情展示**: 提供完整的竞价详细信息
- **多媒体支持**: 包含产品图片、视频、公司环境照片等
- **审核状态管理**: 显示竞价的审核状态和备注
- **联系信息**: 提供卖家的联系方式
- **样品竞价支持**: 支持样品竞价的特殊字段

### 业务特点
- 支持普通竞价和样品竞价两种类型
- 完整的多媒体资源展示
- 详细的审核流程信息
- 丰富的卖家和公司信息
- 灵活的属性配置（JSON格式）

## 接口说明

### 基础竞价信息

#### id
- **类型**: Long
- **描述**: 竞价记录唯一标识
- **用途**: 竞价的主键ID

#### requirementId
- **类型**: Long
- **描述**: 关联的需求ID
- **用途**: 标识竞价对应的采购需求

#### sellerId
- **类型**: Long
- **描述**: 卖家用户ID
- **用途**: 标识竞价的提交者

#### biddingPrice
- **类型**: BigDecimal
- **描述**: 竞价价格
- **用途**: 卖家报价的单价

#### unit
- **类型**: String
- **描述**: 价格单位
- **用途**: 价格的计量单位（如：件、公斤等）

### 产品信息

#### productName
- **类型**: String
- **描述**: 产品名称
- **用途**: 竞价产品的名称

#### hsCode
- **类型**: String
- **描述**: 海关编码
- **用途**: 产品的国际贸易分类编码

#### description
- **类型**: String
- **描述**: 产品描述
- **用途**: 产品的详细描述信息

#### deliveryTime
- **类型**: LocalDate
- **描述**: 交付时间
- **用途**: 卖家承诺的交付日期

#### attributesJson
- **类型**: String
- **描述**: 产品属性JSON数据
- **用途**: 存储产品的自定义属性信息

### 多媒体资源

#### images
- **类型**: List<String>
- **描述**: 产品图片URL列表
- **用途**: 产品展示图片

#### videos
- **类型**: List<String>
- **描述**: 产品视频URL列表
- **用途**: 产品展示视频

#### companyEnvironmentImages
- **类型**: List<String>
- **描述**: 公司/工厂环境照片URL列表
- **用途**: 展示卖家的生产环境和实力

#### companyAddress
- **类型**: String
- **描述**: 公司详细地址
- **用途**: 卖家公司的具体地址

### 状态信息

#### status
- **类型**: String
- **描述**: 竞价状态
- **用途**: 竞价当前的处理状态

#### isWinner
- **类型**: Boolean
- **描述**: 是否中标
- **用途**: 标识该竞价是否获胜

#### auditStatus
- **类型**: String
- **描述**: 审核状态（pending_audit/approved/rejected）
- **用途**: 竞价的审核状态

#### auditRemark
- **类型**: String
- **描述**: 审核备注
- **用途**: 管理员的审核意见

#### isOwnBidding
- **类型**: Boolean
- **描述**: 是否为当前用户的竞价
- **用途**: 前端权限控制和显示逻辑

### 联系信息

#### company
- **类型**: String
- **描述**: 公司名称
- **用途**: 卖家的公司名称

#### contactPhone
- **类型**: String
- **描述**: 联系电话（包含区号）
- **用途**: 卖家的联系电话，格式如"+86 13800138000"

#### email
- **类型**: String
- **描述**: 邮箱地址
- **用途**: 卖家的联系邮箱

### 样品竞价字段

#### biddingType
- **类型**: String
- **描述**: 竞价类型（purchase-普通竞价，sample-样品竞价）
- **用途**: 区分不同类型的竞价

#### samplePrice
- **类型**: BigDecimal
- **描述**: 样品价格（仅样品竞价时使用）
- **用途**: 样品的单价

#### sampleQuantity
- **类型**: Integer
- **描述**: 样品数量（仅样品竞价时使用）
- **用途**: 提供的样品数量

#### sampleSpecification
- **类型**: String
- **描述**: 样品规格说明（仅样品竞价时使用）
- **用途**: 样品的详细规格描述

### 时间信息

#### createdAt
- **类型**: LocalDateTime
- **描述**: 创建时间
- **用途**: 竞价提交的时间

#### updatedAt
- **类型**: LocalDateTime
- **描述**: 更新时间
- **用途**: 竞价最后更新的时间

## 使用示例

### 服务层转换
```java
@Service
public class BiddingService {
    
    public BiddingDetailResponse getBiddingDetail(Long biddingId, Long currentUserId) {
        // 查询竞价记录
        BiddingRecord bidding = biddingMapper.selectById(biddingId);
        if (bidding == null) {
            throw new BusinessException("竞价记录不存在");
        }
        
        // 转换为响应DTO
        BiddingDetailResponse response = convertToDetailResponse(bidding, currentUserId);
        
        return response;
    }
    
    private BiddingDetailResponse convertToDetailResponse(BiddingRecord bidding, Long currentUserId) {
        BiddingDetailResponse response = new BiddingDetailResponse();
        BeanUtils.copyProperties(bidding, response);
        
        // 设置是否为当前用户的竞价
        response.setIsOwnBidding(Objects.equals(bidding.getSellerId(), currentUserId));
        
        // 解析多媒体资源
        response.setImages(parseMediaUrls(bidding.getImages()));
        response.setVideos(parseMediaUrls(bidding.getVideos()));
        response.setCompanyEnvironmentImages(parseMediaUrls(bidding.getCompanyEnvironmentImages()));
        
        // 获取卖家信息
        User seller = userService.getById(bidding.getSellerId());
        if (seller != null) {
            response.setCompany(seller.getCompany());
            response.setContactPhone(seller.getPhone());
            response.setEmail(seller.getEmail());
        }
        
        return response;
    }
    
    private List<String> parseMediaUrls(String mediaJson) {
        if (mediaJson == null || mediaJson.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(mediaJson, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            log.error("解析媒体URL失败: {}", mediaJson, e);
            return Collections.emptyList();
        }
    }
}
```

### 控制器使用
```java
@RestController
@RequestMapping("/api/biddings")
public class BiddingController {
    
    @GetMapping("/{id}")
    public ApiResponse<BiddingDetailResponse> getBiddingDetail(
            @PathVariable Long id,
            @RequestHeader("User-Id") Long currentUserId) {
        
        BiddingDetailResponse response = biddingService.getBiddingDetail(id, currentUserId);
        return ApiResponse.success(response);
    }
    
    @GetMapping("/requirement/{requirementId}")
    public ApiResponse<List<BiddingDetailResponse>> getRequirementBiddings(
            @PathVariable Long requirementId,
            @RequestHeader("User-Id") Long currentUserId) {
        
        List<BiddingDetailResponse> biddings = biddingService.getRequirementBiddings(
            requirementId, currentUserId
        );
        
        return ApiResponse.success(biddings);
    }
}
```

### 前端竞价详情展示
```javascript
// 获取竞价详情
const getBiddingDetail = async (biddingId) => {
  try {
    const response = await fetch(`/api/biddings/${biddingId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'User-Id': currentUserId
      }
    });
    
    const result = await response.json();
    if (result.success) {
      return formatBiddingDetail(result.data);
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取竞价详情失败:', error);
    throw error;
  }
};

// 格式化竞价详情数据
const formatBiddingDetail = (bidding) => {
  return {
    ...bidding,
    formattedPrice: `${bidding.biddingPrice} ${bidding.unit}`,
    formattedDeliveryTime: formatDate(bidding.deliveryTime),
    canContact: !bidding.isOwnBidding && bidding.auditStatus === 'approved',
    canEdit: bidding.isOwnBidding && bidding.status === 'pending',
    statusText: getStatusText(bidding.status),
    auditStatusText: getAuditStatusText(bidding.auditStatus)
  };
};

// 竞价详情组件
const BiddingDetailCard = ({ bidding }) => {
  return (
    <Card>
      <div className="bidding-header">
        <h3>{bidding.productName}</h3>
        <div className="price">{bidding.formattedPrice}</div>
        <div className="status">
          <Tag color={getStatusColor(bidding.status)}>
            {bidding.statusText}
          </Tag>
          {bidding.auditStatus && (
            <Tag color={getAuditStatusColor(bidding.auditStatus)}>
              {bidding.auditStatusText}
            </Tag>
          )}
        </div>
      </div>
      
      <div className="bidding-content">
        <p>{bidding.description}</p>
        
        {bidding.images && bidding.images.length > 0 && (
          <ImageGallery images={bidding.images} />
        )}
        
        {bidding.videos && bidding.videos.length > 0 && (
          <VideoPlayer videos={bidding.videos} />
        )}
        
        {bidding.companyEnvironmentImages && bidding.companyEnvironmentImages.length > 0 && (
          <div className="company-environment">
            <h4>公司环境</h4>
            <ImageGallery images={bidding.companyEnvironmentImages} />
          </div>
        )}
      </div>
      
      <div className="bidding-footer">
        <div className="company-info">
          <p><strong>公司:</strong> {bidding.company}</p>
          <p><strong>地址:</strong> {bidding.companyAddress}</p>
          {bidding.canContact && (
            <div className="contact-info">
              <p><strong>电话:</strong> {bidding.contactPhone}</p>
              <p><strong>邮箱:</strong> {bidding.email}</p>
            </div>
          )}
        </div>
        
        <div className="delivery-info">
          <p><strong>交付时间:</strong> {bidding.formattedDeliveryTime}</p>
        </div>
      </div>
    </Card>
  );
};
```

## 注意事项

### 数据安全
1. **联系信息保护**: 只有在特定条件下才显示卖家联系信息
2. **权限控制**: 根据用户角色和竞价状态控制数据显示
3. **敏感信息**: 某些字段需要权限验证后才能访问
4. **数据脱敏**: 必要时对敏感信息进行脱敏处理

### 性能优化
1. **懒加载**: 多媒体资源采用懒加载策略
2. **数据缓存**: 频繁访问的竞价详情可以缓存
3. **图片压缩**: 图片资源应该提供多种尺寸
4. **分页加载**: 大量竞价列表使用分页

### 前端集成
1. **响应式设计**: 支持移动端和桌面端展示
2. **媒体播放**: 提供图片轮播和视频播放功能
3. **状态显示**: 提供友好的状态和审核状态显示
4. **交互功能**: 根据权限提供相应的操作按钮

### 业务规则
1. **审核状态**: 只有审核通过的竞价才能显示联系信息
2. **竞价类型**: 根据竞价类型显示不同的字段
3. **权限验证**: 确保用户只能查看有权限的竞价详情
4. **状态一致性**: 竞价状态和审核状态应保持逻辑一致
