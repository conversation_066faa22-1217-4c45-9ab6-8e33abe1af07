package com.purchase.bidding.dto.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
public class BiddingSubmitRequest {
    @NotNull(message = "采购需求ID不能为空")
    private Long requirementId;
    
    @NotNull(message = "卖家ID不能为空")
    private Long sellerId;
    
    @NotNull(message = "竞价金额不能为空")
    @Positive(message = "竞价金额必须大于0")
    private BigDecimal biddingPrice;
    
    private String unit;
    
    private String productName;
    
    private String hsCode;
    
    @NotNull(message = "竞价描述不能为空")
    private String description;
    
    @NotNull(message = "预计交付时间不能为空")
    private LocalDate deliveryTime;
    
    private List<String> images;
    
    private List<String> videos;
    
    // 公司/工厂环境照片和视频，前端传递的是逗号分隔的字符串
    private String companyEnvironmentImages;
    
    // 公司详细地址
    private String companyAddress;
    
    // 属性JSON字符串
    private String attributesJson;
    
    // 联系信息
    private String companyName;
    private String contactPhone; // 包含区号的完整电话号码，如："+86 13800138000"
    private String email;
    
    // 样品相关字段
    /**
     * 竞价类型：purchase-普通竞价，sample-样品竞价
     */
    private String biddingType;
    
    /**
     * 样品价格（仅样品竞价时使用）
     */
    private BigDecimal samplePrice;
    
    /**
     * 样品数量（仅样品竞价时使用）
     */
    private Integer sampleQuantity;
    
    /**
     * 样品规格说明（仅样品竞价时使用）
     */
    private String sampleSpecification;
}