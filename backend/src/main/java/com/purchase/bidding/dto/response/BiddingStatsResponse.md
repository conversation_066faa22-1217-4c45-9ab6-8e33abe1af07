# BiddingStatsResponse 投标统计响应DTO文档

## 文件概述

`BiddingStatsResponse` 是投标统计响应数据传输对象，用于封装和传输投标相关的统计数据。该DTO包含投标数量统计、状态分布、价格统计等关键指标，为投标管理和分析提供标准化的数据结构，支持管理后台的投标数据可视化和业务决策。

## 核心功能

### 主要职责
- **统计数据封装**: 封装投标相关的各种统计指标
- **状态分布**: 提供不同投标状态的数量分布
- **价格分析**: 包含投标价格的统计分析数据
- **数据传输**: 为前端提供标准化的统计数据格式
- **业务洞察**: 支持投标业务的数据分析和决策

### 业务特点
- 完整的投标状态统计
- 详细的价格分析指标
- 适用于管理后台展示
- 支持业务趋势分析
- 标准化的数据格式

## 接口说明

### 数量统计字段

#### totalCount
- **类型**: Long
- **描述**: 投标总数量
- **用途**: 统计系统中所有投标的总数
- **计算**: 包含所有状态的投标数量

#### pendingCount
- **类型**: Long
- **描述**: 待处理投标数量
- **用途**: 统计状态为"待处理"的投标数量
- **业务意义**: 反映当前需要处理的投标工作量

#### acceptedCount
- **类型**: Long
- **描述**: 已接受投标数量
- **用途**: 统计状态为"已接受"的投标数量
- **业务意义**: 反映投标成功率和业务成交情况

#### rejectedCount
- **类型**: Long
- **描述**: 已拒绝投标数量
- **用途**: 统计状态为"已拒绝"的投标数量
- **业务意义**: 分析投标失败原因和改进方向

#### cancelledCount
- **类型**: Long
- **描述**: 已取消投标数量
- **用途**: 统计状态为"已取消"的投标数量
- **业务意义**: 分析投标取消率和原因

### 价格统计字段

#### minPrice
- **类型**: BigDecimal
- **描述**: 最低投标价格
- **用途**: 统计所有投标中的最低价格
- **业务意义**: 了解市场价格下限

#### maxPrice
- **类型**: BigDecimal
- **描述**: 最高投标价格
- **用途**: 统计所有投标中的最高价格
- **业务意义**: 了解市场价格上限

#### avgPrice
- **类型**: BigDecimal
- **描述**: 平均投标价格
- **用途**: 计算所有投标的平均价格
- **业务意义**: 了解市场平均价格水平

## 使用示例

### 服务层中构建统计响应
```java
@Service
public class BiddingStatsService {
    
    @Autowired
    private BiddingMapper biddingMapper;
    
    public BiddingStatsResponse getBiddingStats(BiddingStatsQuery query) {
        BiddingStatsResponse response = new BiddingStatsResponse();
        
        // 查询总数量
        Long totalCount = biddingMapper.countByQuery(query);
        response.setTotalCount(totalCount);
        
        // 查询各状态数量
        response.setPendingCount(biddingMapper.countByStatus("PENDING"));
        response.setAcceptedCount(biddingMapper.countByStatus("ACCEPTED"));
        response.setRejectedCount(biddingMapper.countByStatus("REJECTED"));
        response.setCancelledCount(biddingMapper.countByStatus("CANCELLED"));
        
        // 查询价格统计
        PriceStats priceStats = biddingMapper.getPriceStats(query);
        if (priceStats != null) {
            response.setMinPrice(priceStats.getMinPrice());
            response.setMaxPrice(priceStats.getMaxPrice());
            response.setAvgPrice(priceStats.getAvgPrice());
        } else {
            response.setMinPrice(BigDecimal.ZERO);
            response.setMaxPrice(BigDecimal.ZERO);
            response.setAvgPrice(BigDecimal.ZERO);
        }
        
        return response;
    }
    
    public BiddingStatsResponse getBiddingStatsByDateRange(LocalDate startDate, LocalDate endDate) {
        BiddingStatsQuery query = BiddingStatsQuery.builder()
            .startDate(startDate)
            .endDate(endDate)
            .build();
        
        return getBiddingStats(query);
    }
    
    public BiddingStatsResponse getBiddingStatsByRequirement(Long requirementId) {
        BiddingStatsQuery query = BiddingStatsQuery.builder()
            .requirementId(requirementId)
            .build();
        
        return getBiddingStats(query);
    }
}
```

### 控制器中使用
```java
@RestController
@RequestMapping("/api/bidding/stats")
public class BiddingStatsController {
    
    @Autowired
    private BiddingStatsService biddingStatsService;
    
    @GetMapping("/overview")
    public ApiResponse<BiddingStatsResponse> getBiddingOverview() {
        BiddingStatsResponse stats = biddingStatsService.getBiddingStats(new BiddingStatsQuery());
        return ApiResponse.success(stats);
    }
    
    @GetMapping("/by-date-range")
    public ApiResponse<BiddingStatsResponse> getBiddingStatsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        BiddingStatsResponse stats = biddingStatsService.getBiddingStatsByDateRange(startDate, endDate);
        return ApiResponse.success(stats);
    }
    
    @GetMapping("/by-requirement/{requirementId}")
    public ApiResponse<BiddingStatsResponse> getBiddingStatsByRequirement(@PathVariable Long requirementId) {
        BiddingStatsResponse stats = biddingStatsService.getBiddingStatsByRequirement(requirementId);
        return ApiResponse.success(stats);
    }
}
```

### 前端使用示例
```javascript
// 获取投标统计数据
const fetchBiddingStats = async (params = {}) => {
  try {
    const queryParams = new URLSearchParams(params);
    const response = await fetch(`/api/bidding/stats/overview?${queryParams}`, {
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`
      }
    });
    
    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取投标统计数据失败:', error);
    throw error;
  }
};

// 按日期范围获取统计
const fetchBiddingStatsByDateRange = async (startDate, endDate) => {
  try {
    const response = await fetch(
      `/api/bidding/stats/by-date-range?startDate=${startDate}&endDate=${endDate}`,
      {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      }
    );
    
    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取日期范围投标统计失败:', error);
    throw error;
  }
};

// React投标统计组件
const BiddingStatsPanel = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  
  useEffect(() => {
    loadBiddingStats();
  }, [dateRange]);
  
  const loadBiddingStats = async () => {
    setLoading(true);
    try {
      const data = await fetchBiddingStatsByDateRange(dateRange.startDate, dateRange.endDate);
      setStats(data);
    } catch (error) {
      console.error('加载投标统计失败:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const calculateSuccessRate = () => {
    if (!stats || stats.totalCount === 0) return 0;
    return ((stats.acceptedCount / stats.totalCount) * 100).toFixed(1);
  };
  
  const calculateRejectionRate = () => {
    if (!stats || stats.totalCount === 0) return 0;
    return ((stats.rejectedCount / stats.totalCount) * 100).toFixed(1);
  };
  
  if (loading) {
    return <div className="loading">加载中...</div>;
  }
  
  if (!stats) {
    return <div className="no-data">暂无数据</div>;
  }
  
  return (
    <div className="bidding-stats-panel">
      <div className="date-range-selector">
        <input
          type="date"
          value={dateRange.startDate}
          onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
        />
        <span>至</span>
        <input
          type="date"
          value={dateRange.endDate}
          onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
        />
      </div>
      
      <div className="stats-cards">
        <div className="stat-card total">
          <h3>投标总数</h3>
          <div className="value">{stats.totalCount}</div>
        </div>
        
        <div className="stat-card pending">
          <h3>待处理</h3>
          <div className="value">{stats.pendingCount}</div>
        </div>
        
        <div className="stat-card accepted">
          <h3>已接受</h3>
          <div className="value">{stats.acceptedCount}</div>
          <div className="rate">成功率: {calculateSuccessRate()}%</div>
        </div>
        
        <div className="stat-card rejected">
          <h3>已拒绝</h3>
          <div className="value">{stats.rejectedCount}</div>
          <div className="rate">拒绝率: {calculateRejectionRate()}%</div>
        </div>
        
        <div className="stat-card cancelled">
          <h3>已取消</h3>
          <div className="value">{stats.cancelledCount}</div>
        </div>
      </div>
      
      <div className="price-stats">
        <h3>价格统计</h3>
        <div className="price-info">
          <div className="price-item">
            <label>最低价:</label>
            <span>¥{stats.minPrice?.toLocaleString() || '0'}</span>
          </div>
          <div className="price-item">
            <label>最高价:</label>
            <span>¥{stats.maxPrice?.toLocaleString() || '0'}</span>
          </div>
          <div className="price-item">
            <label>平均价:</label>
            <span>¥{stats.avgPrice?.toLocaleString() || '0'}</span>
          </div>
        </div>
      </div>
      
      <div className="status-distribution">
        <h3>状态分布</h3>
        <PieChart
          data={[
            { name: '待处理', value: stats.pendingCount, color: '#ffa500' },
            { name: '已接受', value: stats.acceptedCount, color: '#28a745' },
            { name: '已拒绝', value: stats.rejectedCount, color: '#dc3545' },
            { name: '已取消', value: stats.cancelledCount, color: '#6c757d' }
          ]}
        />
      </div>
    </div>
  );
};
```

### 数据验证和处理
```java
@Component
public class BiddingStatsValidator {
    
    public void validateStatsResponse(BiddingStatsResponse response) {
        if (response == null) {
            throw new IllegalArgumentException("统计响应不能为空");
        }
        
        // 验证数量统计的一致性
        Long calculatedTotal = (response.getPendingCount() != null ? response.getPendingCount() : 0L) +
                              (response.getAcceptedCount() != null ? response.getAcceptedCount() : 0L) +
                              (response.getRejectedCount() != null ? response.getRejectedCount() : 0L) +
                              (response.getCancelledCount() != null ? response.getCancelledCount() : 0L);
        
        if (response.getTotalCount() != null && !response.getTotalCount().equals(calculatedTotal)) {
            log.warn("投标总数与各状态数量之和不一致: total={}, calculated={}", 
                    response.getTotalCount(), calculatedTotal);
        }
        
        // 验证价格统计的合理性
        if (response.getMinPrice() != null && response.getMaxPrice() != null) {
            if (response.getMinPrice().compareTo(response.getMaxPrice()) > 0) {
                throw new IllegalStateException("最低价格不能大于最高价格");
            }
        }
        
        if (response.getAvgPrice() != null && response.getMinPrice() != null && response.getMaxPrice() != null) {
            if (response.getAvgPrice().compareTo(response.getMinPrice()) < 0 || 
                response.getAvgPrice().compareTo(response.getMaxPrice()) > 0) {
                log.warn("平均价格超出最低价和最高价范围");
            }
        }
    }
}
```

## 注意事项

### 数据一致性
1. **状态统计**: 确保各状态数量之和等于总数量
2. **价格统计**: 验证最低价、最高价、平均价的合理性
3. **时间范围**: 确保统计数据的时间范围一致
4. **数据完整性**: 处理空值和异常数据

### 性能优化
1. **查询优化**: 优化统计查询的SQL性能
2. **缓存策略**: 对频繁查询的统计数据进行缓存
3. **异步计算**: 复杂统计计算使用异步处理
4. **分页处理**: 大数据量统计使用分页或分批处理

### 业务规则
1. **状态定义**: 明确各种投标状态的定义和流转规则
2. **价格计算**: 确保价格统计的计算逻辑正确
3. **权限控制**: 根据用户角色控制统计数据的可见性
4. **数据范围**: 明确统计数据的范围和边界

### 扩展性
1. **指标扩展**: 支持新增统计指标
2. **维度扩展**: 支持按不同维度进行统计
3. **时间粒度**: 支持不同时间粒度的统计
4. **格式兼容**: 保持数据格式的向后兼容性
