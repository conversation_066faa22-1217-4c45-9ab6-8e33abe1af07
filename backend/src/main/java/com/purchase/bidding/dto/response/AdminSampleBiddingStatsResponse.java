package com.purchase.bidding.dto.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 管理员样品竞价统计响应DTO
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class AdminSampleBiddingStatsResponse {
    
    /**
     * 总竞价数量
     */
    private Long totalCount;
    
    /**
     * 待审核数量
     */
    private Long pendingAuditCount;
    
    /**
     * 审核通过数量
     */
    private Long approvedCount;
    
    /**
     * 审核拒绝数量
     */
    private Long rejectedCount;
    
    /**
     * 待处理数量
     */
    private Long pendingCount;
    
    /**
     * 已接受数量
     */
    private Long acceptedCount;
    
    /**
     * 已拒绝数量
     */
    private Long rejectedBiddingCount;
    
    /**
     * 已取消数量
     */
    private Long cancelledCount;
    
    /**
     * 样品已接受数量
     */
    private Long sampleAcceptedCount;
    
    /**
     * 最低样品价格
     */
    private BigDecimal minSamplePrice;
    
    /**
     * 最高样品价格
     */
    private BigDecimal maxSamplePrice;
    
    /**
     * 平均样品价格
     */
    private BigDecimal avgSamplePrice;
    
    /**
     * 总样品价值
     */
    private BigDecimal totalSampleValue;
    
    /**
     * 活跃卖家数量
     */
    private Long activeSellerCount;
    
    /**
     * 活跃买家数量
     */
    private Long activeBuyerCount;
    
    /**
     * 平均样品数量
     */
    private Double avgSampleQuantity;
    
    /**
     * 审核通过率
     */
    private Double auditApprovalRate;
    
    /**
     * 竞价接受率
     */
    private Double biddingAcceptanceRate;
    
    /**
     * 今日新增竞价数量
     */
    private Long todayNewCount;
    
    /**
     * 本周新增竞价数量
     */
    private Long thisWeekNewCount;
    
    /**
     * 本月新增竞价数量
     */
    private Long thisMonthNewCount;
    
    /**
     * 统计时间范围
     */
    private LocalDate startDate;
    private LocalDate endDate;
    
    /**
     * 按状态分组的统计数据
     */
    private List<StatusStats> statusStats;
    
    /**
     * 按审核状态分组的统计数据
     */
    private List<AuditStatusStats> auditStatusStats;
    
    /**
     * 热门产品类别统计
     */
    private List<ProductCategoryStats> productCategoryStats;
    
    /**
     * 卖家排行榜
     */
    private List<SellerRankingStats> sellerRankingStats;
    
    /**
     * 买家排行榜
     */
    private List<BuyerRankingStats> buyerRankingStats;
    
    /**
     * 趋势数据
     */
    private List<TrendData> trends;
    
    /**
     * 状态统计数据
     */
    @Data
    public static class StatusStats {
        private String status;
        private String statusName;
        private Long count;
        private Double percentage;
    }
    
    /**
     * 审核状态统计数据
     */
    @Data
    public static class AuditStatusStats {
        private String auditStatus;
        private String auditStatusName;
        private Long count;
        private Double percentage;
    }
    
    /**
     * 产品类别统计数据
     */
    @Data
    public static class ProductCategoryStats {
        private String categoryName;
        private Long count;
        private BigDecimal totalValue;
        private Double percentage;
    }
    
    /**
     * 卖家排行统计数据
     */
    @Data
    public static class SellerRankingStats {
        private Long sellerId;
        private String sellerName;
        private String company;
        private Long biddingCount;
        private Long acceptedCount;
        private BigDecimal totalValue;
        private Double acceptanceRate;
    }
    
    /**
     * 买家排行统计数据
     */
    @Data
    public static class BuyerRankingStats {
        private Long buyerId;
        private String buyerName;
        private String company;
        private Long requirementCount;
        private Long acceptedBiddingCount;
        private BigDecimal totalValue;
        private Double avgBiddingPerRequirement;
    }
    
    /**
     * 趋势数据
     */
    @Data
    public static class TrendData {
        private LocalDate date;
        private String period;
        private Long totalCount;
        private Long submittedCount;
        private Long auditedCount;
        private Long acceptedCount;
        private BigDecimal totalValue;
        private Double avgPrice;
    }
} 