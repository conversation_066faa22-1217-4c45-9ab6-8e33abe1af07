# BiddingSubmitRequest 竞价提交请求文档

## 文件概述

`BiddingSubmitRequest` 是用于提交竞价的请求数据传输对象。该类定义了提交竞价时需要的所有必要信息，包括基础竞价信息、产品详情、联系方式以及样品竞价的特殊字段。支持普通竞价和样品竞价两种类型。

## 核心功能

### 主要职责
- 封装竞价提交时的请求参数
- 提供数据验证注解确保数据完整性
- 支持普通竞价和样品竞价两种业务场景
- 统一竞价提交的数据格式

### 验证规则
- 使用JSR-303验证注解确保关键字段非空
- 价格字段验证为正数
- 支持自定义验证消息

## 接口说明

### 必填字段
- `requirementId`: 采购需求ID，关联具体的采购需求
- `sellerId`: 卖家用户ID，标识竞价提交者
- `biddingPrice`: 竞价金额，必须大于0
- `description`: 竞价描述，详细说明产品和服务
- `deliveryTime`: 预计交付时间，不能为空

### 可选字段

#### 产品信息
- `unit`: 报价单位（如：个、吨、米等）
- `productName`: 产品名称
- `hsCode`: 海关编码，用于国际贸易

#### 媒体资源
- `images`: 产品图片列表
- `videos`: 产品视频列表
- `companyEnvironmentImages`: 公司/工厂环境照片（逗号分隔字符串）

#### 联系信息
- `companyName`: 公司名称
- `contactPhone`: 联系电话（包含区号，如："+86 13800138000"）
- `email`: 联系邮箱
- `companyAddress`: 公司详细地址

#### 扩展信息
- `attributesJson`: 产品属性JSON字符串，存储动态属性

### 样品竞价专用字段
- `biddingType`: 竞价类型（"purchase"或"sample"）
- `samplePrice`: 样品价格（仅样品竞价时使用）
- `sampleQuantity`: 样品数量（仅样品竞价时使用）
- `sampleSpecification`: 样品规格说明（仅样品竞价时使用）

## 使用示例

### 普通竞价提交
```java
BiddingSubmitRequest request = new BiddingSubmitRequest();

// 必填字段
request.setRequirementId(1001L);
request.setSellerId(2001L);
request.setBiddingPrice(new BigDecimal("150.00"));
request.setDescription("高质量工业零件，符合国际标准，具有10年生产经验");
request.setDeliveryTime(LocalDate.now().plusDays(30));

// 产品信息
request.setUnit("个");
request.setProductName("精密工业零件");
request.setHsCode("8421999090");

// 媒体资源
request.setImages(Arrays.asList("product1.jpg", "product2.jpg", "product3.jpg"));
request.setVideos(Arrays.asList("demo.mp4"));
request.setCompanyEnvironmentImages("factory1.jpg,factory2.jpg,workshop.jpg");

// 联系信息
request.setCompanyName("ABC精密制造有限公司");
request.setContactPhone("+86 13800138000");
request.setEmail("<EMAIL>");
request.setCompanyAddress("广东省深圳市南山区科技园南区");

// 产品属性
request.setAttributesJson("{\"material\":\"不锈钢\",\"precision\":\"±0.01mm\",\"certification\":\"ISO9001\"}");

// 竞价类型
request.setBiddingType("purchase");
```

### 样品竞价提交
```java
BiddingSubmitRequest sampleRequest = new BiddingSubmitRequest();

// 设置基础信息（同普通竞价）
// ... 基础字段设置

// 样品竞价特有字段
sampleRequest.setBiddingType("sample");
sampleRequest.setSamplePrice(new BigDecimal("50.00"));
sampleRequest.setSampleQuantity(3);
sampleRequest.setSampleSpecification("提供3个标准规格样品，包含完整功能演示和质量检测报告");
```

### 控制器中的使用
```java
@PostMapping("/submit")
public Result<Long> submitBidding(@Valid @RequestBody BiddingSubmitRequest request) {
    // 验证样品竞价字段
    if ("sample".equals(request.getBiddingType())) {
        if (request.getSamplePrice() == null || request.getSampleQuantity() == null) {
            throw new BusinessException("样品竞价必须提供样品价格和数量");
        }
    }
    
    // 调用服务层处理
    Long biddingId = biddingService.submitBidding(request);
    return Result.success(biddingId);
}
```

### 数据转换示例
```java
// Request转Entity
public BiddingRecord convertToEntity(BiddingSubmitRequest request) {
    BiddingRecord record = new BiddingRecord();
    
    // 基础字段转换
    record.setRequirementId(request.getRequirementId());
    record.setSellerId(request.getSellerId());
    record.setBiddingPrice(request.getBiddingPrice());
    record.setUnit(request.getUnit());
    record.setProductName(request.getProductName());
    record.setHsCode(request.getHsCode());
    record.setDescription(request.getDescription());
    record.setDeliveryTime(request.getDeliveryTime());
    
    // 列表转字符串
    if (request.getImages() != null && !request.getImages().isEmpty()) {
        record.setImages(String.join(",", request.getImages()));
    }
    if (request.getVideos() != null && !request.getVideos().isEmpty()) {
        record.setVideos(String.join(",", request.getVideos()));
    }
    
    // 联系信息
    record.setCompany(request.getCompanyName());
    record.setPhone(request.getContactPhone());
    record.setEmail(request.getEmail());
    record.setCompanyAddress(request.getCompanyAddress());
    record.setCompanyEnvironmentImages(request.getCompanyEnvironmentImages());
    
    // 扩展属性
    record.setAttributesJson(request.getAttributesJson());
    
    // 样品竞价字段
    record.setBiddingType(request.getBiddingType());
    if ("sample".equals(request.getBiddingType())) {
        record.setSamplePrice(request.getSamplePrice());
        record.setSampleQuantity(request.getSampleQuantity());
        record.setSampleSpecification(request.getSampleSpecification());
    }
    
    // 设置默认状态
    record.setStatus("pending");
    record.setAuditStatus("pending_audit");
    record.setWinner(false);
    
    return record;
}
```

## 注意事项

### 数据验证
1. **必填字段验证**: 使用@NotNull和@Positive注解确保关键字段的有效性
2. **业务逻辑验证**: 样品竞价时需要额外验证样品相关字段
3. **日期验证**: 交付时间应不早于当前日期
4. **价格验证**: 所有价格字段应为正数

### 数据格式
1. **电话号码**: 建议包含国际区号，格式如"+86 13800138000"
2. **图片/视频**: 前端传递List<String>，后端转换为逗号分隔字符串存储
3. **JSON属性**: attributesJson应为有效的JSON格式字符串

### 安全考虑
1. **输入验证**: 对所有用户输入进行验证和过滤
2. **文件上传**: 图片和视频路径应经过安全验证
3. **SQL注入**: 使用参数化查询避免SQL注入

### 性能优化
1. **文件处理**: 大量图片/视频上传时考虑异步处理
2. **数据压缩**: 对于大型JSON属性考虑压缩存储
3. **缓存策略**: 频繁访问的数据可考虑缓存
