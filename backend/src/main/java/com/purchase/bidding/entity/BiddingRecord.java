package com.purchase.bidding.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("bidding_record")
public class BiddingRecord {
    
    /**
     * 竞价类型常量
     */
    public interface BiddingType {
        String PURCHASE = "purchase"; // 普通竞价
        String SAMPLE = "sample"; // 样品竞价
    }
    
    /**
     * 竞价状态常量
     */
    public interface BiddingStatus {
        String PENDING = "pending"; // 待处理
        String ACCEPTED = "accepted"; // 已中标
        String REJECTED = "rejected"; // 已拒绝
        String CANCELLED = "cancelled"; // 已取消
        String SAMPLE_ACCEPTED = "sample_accepted"; // 样品已接受
    }
    
    /**
     * 审核状态常量
     */
    public interface AuditStatus {
        String PENDING_AUDIT = "pending_audit"; // 待审核
        String APPROVED = "approved"; // 审核通过
        String REJECTED = "rejected"; // 审核拒绝
    }
    
    /**
     * 竞价记录主键ID，自增长
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联的需求ID，对应requirement表的主键
     */
    private Long requirementId;
    
    /**
     * 卖家用户ID，对应user表的主键
     */
    private Long sellerId;

    /**
     * 卖家公司名称
     */
    private String company;
    
    /**
     * 卖家联系电话
     */
    private String phone;
    
    /**
     * 卖家联系邮箱
     */
    private String email;
    
    /**
     * 竞价报价，单价
     */
    private BigDecimal biddingPrice;
    
    /**
     * 报价单位（如：个、吨、米等）
     */
    private String unit;
    
    /**
     * 竞价的产品名称
     */
    private String productName;
    
    /**
     * 产品海关编码
     */
    private String hsCode;
    
    /**
     * 产品图片路径，多张图片用逗号分隔
     */
    private String images;
    
    /**
     * 产品视频路径，多个视频用逗号分隔
     */
    private String videos;
    
    /**
     * 公司/工厂环境照片路径，多张图片和视频用逗号分隔
     */
    private String companyEnvironmentImages;
    
    /**
     * 公司详细地址
     */
    private String companyAddress;
    
    /**
     * 竞价说明描述，包括产品详情、优势等
     */
    private String description;
    
    /**
     * 预计交货时间
     */
    private LocalDate deliveryTime;
    
    /**
     * 竞价状态：pending-待处理，accepted-已中标，rejected-已拒绝，cancelled-已取消
     */
    private String status;
    
    /**
     * 竞价类型：purchase-普通竞价，sample-样品竞价
     */
    private String biddingType;
    
    /**
     * 样品价格（仅样品竞价时使用）
     */
    private BigDecimal samplePrice;
    
    /**
     * 样品数量（仅样品竞价时使用）
     */
    private Integer sampleQuantity;
    
    /**
     * 样品规格说明（仅样品竞价时使用）
     */
    private String sampleSpecification;
    
    /**
     * 审核状态：
     * pending_audit - 待审核
     * approved - 审核通过
     * rejected - 审核拒绝
     */
    private String auditStatus;
    
    /**
     * 审核备注，记录审核时的意见或原因
     */
    private String auditRemark;
    
    /**
     * 是否中标，true-中标，false-未中标
     */
    @TableField("is_winner")
    private Boolean winner;
    
    /**
     * 根据需求类别获取的属性JSON数据，存储产品的特定属性信息
     */
    private String attributesJson;
    
    /**
     * 逻辑删除标记，0-未删除，1-已删除
     */
    @TableLogic(value = "0", delval = "1")
    private String deleted;
    
    /**
     * 记录创建时间，插入时自动填充
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 记录更新时间，插入和更新时自动填充
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}