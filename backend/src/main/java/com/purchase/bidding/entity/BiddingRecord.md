# BiddingRecord.md

## 1. 文件概述

`BiddingRecord` 是竞价模块中的一个实体（Entity），位于 `com.purchase.bidding.entity` 包中。它直接映射到数据库的 `bidding_record` 表，用于持久化存储每一次竞价记录。这个实体包含了竞价的所有核心属性，如关联的需求ID、卖家信息、报价、产品详情、预计交货时间、竞价状态、竞价类型（普通竞价或样品竞价）、审核状态以及是否中标等。它是竞价系统核心业务流程的数据载体，通过MyBatis-Plus的注解实现了与数据库表的自动化ORM映射。

## 2. 核心功能

*   **数据持久化**: 定义了竞价记录在数据库中的存储结构，是竞价领域模型在持久化层的具体表现。
*   **多类型竞价支持**: 通过 `biddingType` 字段区分普通竞价和样品竞价，并为样品竞价提供了特有的字段（`samplePrice`, `sampleQuantity`, `sampleSpecification`）。
*   **状态管理**: 包含了 `status`（竞价状态）、`auditStatus`（审核状态）和 `winner`（是否中标）等字段，支持竞价的完整生命周期管理和业务流程控制。
*   **MyBatis-Plus集成**: 通过 `@TableName`, `@TableId`, `@TableField`, `@TableLogic`, `@FieldFill` 等注解与MyBatis-Plus框架深度集成，实现了主键自增长、字段映射、逻辑删除和自动填充时间戳等功能，极大地简化了数据库操作。
*   **详细记录**: 包含了丰富的字段，能够全面记录竞价的上下文信息，为后续的订单生成、统计分析和问题追溯提供详尽的原始数据。
*   **逻辑删除**: `deleted` 字段配合 `@TableLogic` 注解实现了逻辑删除功能，确保数据不会被物理删除，便于数据恢复和审计。

## 3. 属性说明

### 3.1 核心属性

- **`id` (Long)**: 竞价记录主键ID，自增长。
- **`requirementId` (Long)**: 关联的需求ID，对应 `requirement` 表的主键。
- **`sellerId` (Long)**: 卖家用户ID，对应 `user` 表的主键。
- **`company` (String)**: 卖家公司名称。
- **`phone` (String)**: 卖家联系电话。
- **`email` (String)**: 卖家联系邮箱。
- **`biddingPrice` (BigDecimal)**: 竞价报价，单价。
- **`unit` (String)**: 报价单位（如：个、吨、米等）。
- **`productName` (String)**: 竞价的产品名称。
- **`hsCode` (String)**: 产品海关编码。
- **`images` (String)**: 产品图片路径，多张图片用逗号分隔。
- **`videos` (String)**: 产品视频路径，多个视频用逗号分隔。
- **`companyEnvironmentImages` (String)**: 公司/工厂环境照片路径，多张图片和视频用逗号分隔。
- **`companyAddress` (String)**: 公司详细地址。
- **`description` (String)**: 竞价说明描述，包括产品详情、优势等。
- **`deliveryTime` (LocalDate)**: 预计交货时间。
- **`status` (String)**: 竞价状态（`pending`, `accepted`, `rejected`, `cancelled`, `sample_accepted`）。
- **`biddingType` (String)**: 竞价类型（`purchase`-普通竞价，`sample`-样品竞价）。
- **`samplePrice` (BigDecimal)**: 样品价格（仅样品竞价时使用）。
- **`sampleQuantity` (Integer)**: 样品数量（仅样品竞价时使用）。
- **`sampleSpecification` (String)**: 样品规格说明（仅样品竞价时使用）。
- **`auditStatus` (String)**: 审核状态（`pending_audit`-待审核，`approved`-审核通过，`rejected`-审核拒绝）。
- **`auditRemark` (String)**: 审核备注，记录审核时的意见或原因。
- **`winner` (Boolean)**: 是否中标（`true`-中标，`false`-未中标）。
- **`attributesJson` (String)**: 根据需求类别获取的属性JSON数据，存储产品的特定属性信息。
- **`deleted` (String)**: 逻辑删除标记（`0`-未删除，`1`-已删除）。
- **`createdAt` (LocalDateTime)**: 记录创建时间，插入时自动填充。
- **`updatedAt` (LocalDateTime)**: 记录更新时间，插入和更新时自动填充。

### 3.2 内部常量接口

- **`BiddingType`**: 定义了竞价类型常量。
- **`BiddingStatus`**: 定义了竞价状态常量。
- **`AuditStatus`**: 定义了审核状态常量。

## 4. 业务规则

*   **状态流转**: `status` 和 `auditStatus` 字段的更新应遵循预定义的状态机规则，例如，只有 `pending_audit` 状态的竞价才能被审核。
*   **类型特定字段**: `samplePrice`, `sampleQuantity`, `sampleSpecification` 仅在 `biddingType` 为 `sample` 时有效，业务逻辑层应确保这些字段的正确使用。
*   **逻辑删除**: `deleted` 字段配合MyBatis-Plus的 `@TableLogic` 注解，实现了数据的逻辑删除，查询时会自动过滤已删除的记录。
*   **唯一性**: 竞价记录通常与 `requirementId` 和 `sellerId` 存在唯一性约束，即一个卖家对一个需求只能提交一个竞价。
*   **数据完整性**: 关键字段（如 `requirementId`, `sellerId`, `biddingPrice`, `status`, `biddingType`）在创建时必须提供。

## 5. 使用示例

```java
// 1. 在 BiddingRecordMapper 接口中定义对 BiddingRecord 的操作
@Mapper
public interface BiddingRecordMapper extends BaseMapper<BiddingRecord> {
    // 继承 BaseMapper 提供了基本的 CRUD 方法
    // 也可以在此定义自定义查询方法，例如：
    @Select("SELECT * FROM bidding_record WHERE requirement_id = #{requirementId} AND seller_id = #{sellerId} AND deleted = '0'")
    BiddingRecord findByRequirementIdAndSellerId(@Param("requirementId") Long requirementId, @Param("sellerId") Long sellerId);

    @Select("SELECT * FROM bidding_record WHERE status = #{status} AND deleted = '0'")
    List<BiddingRecord> findByStatus(@Param("status") String status);
}

// 2. 在 BiddingService 实现中创建和更新 BiddingRecord
@Service
public class BiddingServiceImpl implements BiddingService {
    @Autowired
    private BiddingRecordMapper biddingRecordMapper;

    @Transactional
    public BiddingDetailResponse submitBidding(BiddingSubmitRequest request) {
        // 业务校验：检查是否已提交过竞价
        BiddingRecord existingBidding = biddingRecordMapper.findByRequirementIdAndSellerId(request.getRequirementId(), request.getSellerId());
        if (existingBidding != null) {
            throw new BusinessException("您已对该需求提交过竞价");
        }

        BiddingRecord biddingRecord = new BiddingRecord();
        // ... 将 request 中的数据复制到 biddingRecord ...
        biddingRecord.setRequirementId(request.getRequirementId());
        biddingRecord.setSellerId(request.getSellerId());
        biddingRecord.setBiddingPrice(request.getPrice());
        biddingRecord.setProductName(request.getProductName());
        biddingRecord.setStatus(BiddingRecord.BiddingStatus.PENDING); // 默认待处理
        biddingRecord.setBiddingType(BiddingRecord.BiddingType.PURCHASE); // 默认普通竞价
        biddingRecord.setAuditStatus(BiddingRecord.AuditStatus.PENDING_AUDIT); // 默认待审核
        biddingRecord.setWinner(false);
        biddingRecord.setDeleted("0");

        biddingRecordMapper.insert(biddingRecord);
        return convertToDetailResponse(biddingRecord);
    }

    @Transactional
    public BiddingDetailResponse acceptBidding(Long biddingId, Long buyerId) {
        BiddingRecord bidding = biddingRecordMapper.selectById(biddingId);
        if (bidding == null || !bidding.getStatus().equals(BiddingRecord.BiddingStatus.PENDING)) {
            throw new BusinessException("竞价不存在或状态不允许接受");
        }
        // ... 业务逻辑：验证买家权限，更新竞价状态，设置winner为true，拒绝其他竞价 ...
        bidding.setStatus(BiddingRecord.BiddingStatus.ACCEPTED);
        bidding.setWinner(true);
        biddingRecordMapper.updateById(bidding);
        return convertToDetailResponse(bidding);
    }
}

// 3. 测试示例
@SpringBootTest
class BiddingRecordTest {
    @Autowired
    private BiddingRecordMapper biddingRecordMapper;

    @Test
    @Transactional
    void testInsertAndRetrieve() {
        BiddingRecord record = new BiddingRecord();
        record.setRequirementId(1L);
        record.setSellerId(10L);
        record.setCompany("Test Company");
        record.setBiddingPrice(new BigDecimal("100.00"));
        record.setUnit("个");
        record.setProductName("测试产品");
        record.setStatus(BiddingRecord.BiddingStatus.PENDING);
        record.setBiddingType(BiddingRecord.BiddingType.PURCHASE);
        record.setAuditStatus(BiddingRecord.AuditStatus.PENDING_AUDIT);
        record.setWinner(false);
        record.setDeleted("0");
        record.setDeliveryTime(LocalDate.now().plusDays(7));

        int result = biddingRecordMapper.insert(record);
        assertThat(result).isEqualTo(1);
        assertThat(record.getId()).isNotNull();
        assertThat(record.getCreatedAt()).isNotNull();
        assertThat(record.getUpdatedAt()).isNotNull();

        BiddingRecord retrievedRecord = biddingRecordMapper.selectById(record.getId());
        assertThat(retrievedRecord).isNotNull();
        assertThat(retrievedRecord.getProductName()).isEqualTo("测试产品");
        assertThat(retrievedRecord.getStatus()).isEqualTo(BiddingRecord.BiddingStatus.PENDING);
    }

    @Test
    @Transactional
    void testLogicalDelete() {
        BiddingRecord record = new BiddingRecord();
        record.setRequirementId(2L);
        record.setSellerId(20L);
        record.setBiddingPrice(new BigDecimal("50.00"));
        record.setStatus(BiddingRecord.BiddingStatus.PENDING);
        record.setBiddingType(BiddingRecord.BiddingType.PURCHASE);
        record.setAuditStatus(BiddingRecord.AuditStatus.PENDING_AUDIT);
        record.setWinner(false);
        record.setDeleted("0");
        biddingRecordMapper.insert(record);

        // 执行逻辑删除
        biddingRecordMapper.deleteById(record.getId());

        // 再次查询，应该找不到（因为逻辑删除）
        BiddingRecord deletedRecord = biddingRecordMapper.selectById(record.getId());
        assertThat(deletedRecord).isNull();

        // 验证数据库中的deleted字段是否变为'1'
        // 需要直接查询数据库或使用MyBatis-Plus的selectById(id, true)来验证
    }
}
```

## 6. 注意事项

*   **领域实体**: `BiddingRecord` 是一个典型的领域实体，它包含了数据和行为，并且其行为（如状态变更）直接反映了业务概念。
*   **MyBatis-Plus集成**: 充分利用MyBatis-Plus的注解和功能，可以大大简化数据访问层的开发。特别是 `@TableLogic` 对于逻辑删除的实现非常方便。
*   **状态管理**: 实体内部定义了多个常量接口来表示竞价类型、竞价状态和审核状态，这是一种良好的实践，可以避免使用魔法字符串，提高代码可读性和可维护性。
*   **时间戳**: `createdAt` 和 `updatedAt` 的自动填充依赖于MyBatis-Plus的配置（如 `MyMetaObjectHandler`），确保了记录的创建和更新时间被准确捕获。
*   **唯一性**: 竞价记录通常与 `requirementId` 和 `sellerId` 存在唯一性约束，在数据库层面应建立相应的唯一索引，并在服务层进行业务校验。
*   **数据类型**: `BigDecimal` 用于金额计算，以避免浮点数精度问题。
*   **图片/视频路径**: `images` 和 `videos` 字段存储的是路径字符串，如果有多张/个，则使用逗号分隔。在业务逻辑中需要进行字符串解析和处理。
*   **可扩展性**: 如果未来需要增加更多竞价相关的属性，可以在实体中添加相应的字段。