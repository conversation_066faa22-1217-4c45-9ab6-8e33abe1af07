# Commission 佣金模块文档

## 模块概述

佣金模块是采购系统的重要业务模块，采用领域驱动设计（DDD）架构模式实现。该模块负责管理邀请推荐机制产生的佣金计算、状态管理和结算功能。系统支持阶梯式提成机制、月度奖金系统，并严格按照业务规则确保佣金计算的准确性。

## 系统特性

### 核心业务功能
- **阶梯式提成机制** - 基于月度成交金额计算不同比例的佣金
- **月度奖金系统** - 达到特定金额阶梯时发放额外奖金
- **严格业务规则** - 只有被邀请者作为买家的订单才产生佣金
- **样品订单排除** - 自动排除样品订单，确保佣金计算准确性
- **多订单类型支持** - 支持采购订单和货代订单
- **完整状态管理** - 支持待确认、已确认、已支付、已取消等状态流转

### 技术架构特性
- **DDD分层架构** - 清晰的领域驱动设计分层
- **事件驱动设计** - 通过领域事件实现系统解耦
- **异步处理机制** - 不影响主业务流程的性能
- **完整测试覆盖** - 支持单元测试和集成测试
- **详细技术文档** - 每个组件都有完整的使用说明

## 架构设计

### DDD分层架构

```
├── domain/                    # 领域层
│   ├── entity/               # 实体
│   │   ├── CommissionRecord              # 佣金记录实体
│   │   └── MonthlyCommissionSummary     # 月度佣金汇总实体
│   ├── valueobject/          # 值对象
│   │   ├── Money                        # 金额值对象
│   │   ├── MonthKey                     # 月份键值对象
│   │   └── CommissionRate               # 佣金比例值对象
│   ├── service/              # 领域服务
│   │   └── CommissionCalculationService # 佣金计算服务
│   ├── repository/           # 仓储接口
│   │   ├── CommissionRecordRepository   # 佣金记录仓储接口
│   │   └── MonthlyCommissionSummaryRepository # 月度汇总仓储接口
│   └── event/                # 领域事件
│       └── OrderCompletedEvent          # 订单完成事件
├── application/              # 应用层
│   └── service/              # 应用服务
│       └── CommissionApplicationService # 佣金应用服务
├── infrastructure/           # 基础设施层
│   ├── po/                   # 持久化对象
│   │   ├── CommissionRecordPO           # 佣金记录PO
│   │   ├── InviterCouponPO              # 邀请人优惠券PO
│   │   └── MonthlyCommissionSummaryPO   # 月度汇总PO
│   ├── mapper/               # 数据访问层
│   │   ├── CommissionRecordMapper       # 佣金记录Mapper
│   │   ├── InviterCouponMapper          # 邀请人优惠券Mapper
│   │   └── MonthlyCommissionSummaryMapper # 月度汇总Mapper
│   ├── repository/           # 仓储实现
│   │   ├── CommissionRecordRepositoryImpl # 佣金记录仓储实现
│   │   └── MonthlyCommissionSummaryRepositoryImpl # 月度汇总仓储实现
│   └── config/               # 配置类
│       └── CommissionConfig             # 佣金模块配置
└── interfaces/               # 接口层
    ├── web/                  # Web接口
    │   ├── controller/       # 控制器
    │   │   └── CommissionController     # 佣金控制器
    │   └── dto/              # 数据传输对象
    │       └── CommissionRecordDTO      # 佣金记录DTO
    └── event/                # 事件监听器
        └── OrderCompletedEventListener  # 订单完成事件监听器
```

### 核心组件说明

#### 领域层（Domain Layer）
- **实体（Entities）**: 封装业务逻辑和状态管理
- **值对象（Value Objects）**: 不可变对象，封装业务概念
- **领域服务（Domain Services）**: 处理跨实体的业务逻辑
- **仓储接口（Repository Interfaces）**: 定义数据访问规范
- **领域事件（Domain Events）**: 表示业务事件

#### 应用层（Application Layer）
- **应用服务（Application Services）**: 协调领域服务和仓储，处理业务用例

#### 基础设施层（Infrastructure Layer）
- **持久化对象（PO）**: 数据库表映射对象
- **数据访问层（Mapper）**: MyBatis数据访问接口
- **仓储实现（Repository Implementations）**: 实现领域层定义的仓储接口

#### 接口层（Interfaces Layer）
- **控制器（Controllers）**: REST API接口
- **DTO（Data Transfer Objects）**: 数据传输对象
- **事件监听器（Event Listeners）**: 处理领域事件

## 佣金计算规则

### 阶梯式提成比例
| 月度成交金额范围 | 佣金比例 |
|---------------|---------|
| 0 - 5万元      | 0.9%   |
| 5万 - 15万元   | 1.1%   |
| 15万 - 30万元  | 1.2%   |
| 30万 - 50万元  | 1.3%   |
| 50万元以上     | 1.4%   |

### 月度奖金阶梯
| 月度成交金额 | 奖金金额 |
|------------|---------|
| 5万元      | 300元   |
| 10万元     | 800元   |
| 20万元     | 1500元  |
| 30万元     | 2500元  |
| 50万元     | 4000元  |
| 80万元     | 6000元  |
| 100万元    | 8000元  |

### 业务规则
1. **买家身份验证**: 只有被邀请者作为买家的订单才产生佣金
2. **订单类型限制**: 支持采购订单和货代订单
3. **样品订单排除**: 样品订单不计入佣金计算
4. **月度计算周期**: 按自然月进行佣金计算和汇总
5. **状态流转管理**: 严格的佣金状态流转控制

## 数据库设计

### 核心表结构

#### commission_record - 佣金记录表
```sql
CREATE TABLE commission_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    inviter_id BIGINT NOT NULL,
    invitee_id BIGINT NOT NULL,
    order_id BIGINT NOT NULL,
    order_type VARCHAR(50) NOT NULL,
    order_amount DECIMAL(19,2) NOT NULL,
    commission_rate DECIMAL(5,4) NOT NULL,
    commission_amount DECIMAL(19,2) NOT NULL,
    status VARCHAR(20) NOT NULL,
    month_key VARCHAR(7) NOT NULL,
    buyer_role_verified BOOLEAN DEFAULT TRUE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    deleted BOOLEAN DEFAULT FALSE
);
```

#### monthly_commission_summary - 月度佣金汇总表
```sql
CREATE TABLE monthly_commission_summary (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    inviter_id BIGINT NOT NULL,
    month_key VARCHAR(7) NOT NULL,
    order_count INT DEFAULT 0,
    total_order_amount DECIMAL(19,2) NOT NULL,
    commission_rate DECIMAL(5,4) NOT NULL DEFAULT '0.0000',
    commission_amount DECIMAL(19,2) NOT NULL,
    bonus_amount DECIMAL(19,2) NOT NULL,
    total_amount DECIMAL(19,2) NOT NULL,
    status VARCHAR(20) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    settled_at DATETIME NULL
);
```

## API接口

### 佣金记录管理
- `GET /api/commission/records/{id}` - 查询佣金记录
- `GET /api/commission/records/order/{orderId}` - 根据订单查询佣金记录
- `GET /api/commission/records/inviter/{inviterId}` - 查询邀请者佣金记录
- `GET /api/commission/records/inviter/{inviterId}/month/{monthKey}` - 查询月度佣金记录
- `POST /api/commission/records/{id}/cancel` - 取消佣金记录
- `POST /api/commission/records/batch/cancel` - 批量取消佣金记录

### 月度处理管理
- `POST /api/v1/commission/monthly/process/{monthKey}` - 手动触发月度处理
- `GET /api/v1/commission/monthly/logs` - 查询处理日志
- `POST /api/v1/commission/monthly/validate/{monthKey}` - 验证月度数据

## 使用指南

### 1. 环境准备
```bash
# 数据库初始化
mysql -u root -p < commission_v2_migration.sql

# 启动应用
mvn spring-boot:run
```

### 2. 基本使用流程

#### 订单完成触发佣金计算
```java
// 发布订单完成事件
OrderCompletedEvent event = new OrderCompletedEvent(
    orderId, buyerId, orderAmount, orderType, completedAt
);
applicationEventPublisher.publishEvent(event);
```

#### 查询佣金记录
```java
// 查询邀请者佣金记录
List<CommissionRecord> records = commissionApplicationService
    .getCommissionRecords(inviterId, 20, 0);
```

#### 月度处理
```java
// 手动触发月度处理
monthlyCommissionProcessor.manualProcessMonth(monthKey);

// 查询处理结果
List<CommissionProcessingLog> logs = processingLogRepository.findByMonthKey(monthKey);
```

#### 月度结算
```java
// 获取月度汇总
Optional<MonthlyCommissionSummary> summary = commissionApplicationService
    .getMonthlySummary(inviterId, monthKey);

// 开始结算
commissionApplicationService.startMonthlySettlement(inviterId, monthKey);
```

## 测试策略

### 单元测试
- **值对象测试**: 测试Money、MonthKey、CommissionRate等值对象
- **实体测试**: 测试CommissionRecord、MonthlyCommissionSummary实体逻辑
- **服务测试**: 测试CommissionCalculationService计算逻辑

### 集成测试
- **端到端测试**: 测试完整的佣金计算和管理流程
- **API测试**: 测试REST接口的正确性
- **数据库测试**: 测试数据持久化的正确性

### 测试覆盖
- 已实现：MoneyTest、CommissionCalculationServiceTest
- 已实现：CommissionSystemIntegrationTest（集成测试）

## 扩展点

### 1. 佣金规则扩展
- 可通过commission_config表配置不同的阶梯规则
- 支持按产品类别定制佣金比例
- 支持季度、年度等不同周期的奖金机制

### 2. 结算方式扩展
- 支持自动结算和手动结算
- 支持多种支付方式
- 支持结算审批流程

### 3. 性能优化扩展
- 支持读写分离
- 支持Redis缓存
- 支持分库分表

### 4. 监控和告警
- 佣金计算异常监控
- 结算流程监控
- 性能指标监控

## 已知限制

### 当前版本限制
1. **用户服务集成**: getInviterByUserId方法需要集成用户服务
2. **月度汇总仓储**: 部分复杂查询方法暂未完整实现
3. **Controller功能**: 部分API方法需要应用服务支持
4. **错误处理**: 可以进一步完善异常处理机制

### 后续改进计划
1. 完善用户服务集成
2. 完整实现所有仓储方法
3. 添加更多的业务验证规则
4. 完善监控和告警机制
5. 添加更多的测试用例

## 相关文档

### 技术文档
- [CommissionRecordRepositoryImpl.md](infrastructure/repository/CommissionRecordRepositoryImpl.md) - 佣金记录仓储实现文档
- [CommissionRecordDTO.md](interfaces/web/dto/CommissionRecordDTO.md) - 佣金记录DTO文档
- [MonthlyCommissionSummaryPO.md](infrastructure/po/MonthlyCommissionSummaryPO.md) - 月度汇总PO文档
- [OrderCompletedEventListener.md](interfaces/event/OrderCompletedEventListener.md) - 事件监听器文档

### 业务文档
- [NEW_COMMISSION_MECHANISM.md](../../../../../../../NEW_COMMISSION_MECHANISM.md) - 业务需求文档

### 数据库文档
- [commission_v2_migration.sql](../../../../../../resources/db/commission_v2_migration.sql) - 数据库迁移脚本

## 技术支持

如有问题，请参考：
1. 各组件的详细文档
2. 单元测试和集成测试用例
3. 业务需求文档
4. 数据库设计文档

系统采用了完整的DDD架构设计，每个组件都有清晰的职责分工，便于维护和扩展。 