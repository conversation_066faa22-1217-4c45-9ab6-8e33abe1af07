package com.purchase.commission.infrastructure.po;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 佣金配置持久化对象
 * 对应数据库表 commission_config
 */
public class CommissionConfigPO {
    
    private Long id;
    private String configType;
    private BigDecimal minAmount;
    private BigDecimal maxAmount;
    private BigDecimal rateValue;
    private BigDecimal bonusAmount;
    private String description;
    private Integer status;
    private Integer sortOrder;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 构造函数
    public CommissionConfigPO() {}
    
    public CommissionConfigPO(Long id, String configType, BigDecimal minAmount, BigDecimal maxAmount,
                             BigDecimal rateValue, BigDecimal bonusAmount, String description,
                             Integer status, Integer sortOrder, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.configType = configType;
        this.minAmount = minAmount;
        this.maxAmount = maxAmount;
        this.rateValue = rateValue;
        this.bonusAmount = bonusAmount;
        this.description = description;
        this.status = status;
        this.sortOrder = sortOrder;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getConfigType() {
        return configType;
    }
    
    public void setConfigType(String configType) {
        this.configType = configType;
    }
    
    public BigDecimal getMinAmount() {
        return minAmount;
    }
    
    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }
    
    public BigDecimal getMaxAmount() {
        return maxAmount;
    }
    
    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }
    
    public BigDecimal getRateValue() {
        return rateValue;
    }
    
    public void setRateValue(BigDecimal rateValue) {
        this.rateValue = rateValue;
    }
    
    public BigDecimal getBonusAmount() {
        return bonusAmount;
    }
    
    public void setBonusAmount(BigDecimal bonusAmount) {
        this.bonusAmount = bonusAmount;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "CommissionConfigPO{" +
                "id=" + id +
                ", configType='" + configType + '\'' +
                ", minAmount=" + minAmount +
                ", maxAmount=" + maxAmount +
                ", rateValue=" + rateValue +
                ", bonusAmount=" + bonusAmount +
                ", description='" + description + '\'' +
                ", status=" + status +
                ", sortOrder=" + sortOrder +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
