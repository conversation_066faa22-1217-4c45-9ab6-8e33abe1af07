package com.purchase.commission.infrastructure.repository;

import com.purchase.commission.domain.entity.InviterCoupon;
import com.purchase.commission.domain.repository.InviterCouponRepository;
import com.purchase.commission.domain.valueobject.Money;
import com.purchase.commission.domain.valueobject.MonthKey;
import com.purchase.commission.infrastructure.mapper.InviterCouponMapper;
import com.purchase.commission.infrastructure.po.InviterCouponPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 邀请人优惠券仓储实现
 * 实现领域层定义的仓储接口
 */
@Repository
public class InviterCouponRepositoryImpl implements InviterCouponRepository {
    
    @Autowired
    private InviterCouponMapper inviterCouponMapper;
    
    @Override
    public InviterCoupon save(InviterCoupon coupon) {
        if (coupon == null) {
            throw new IllegalArgumentException("优惠券不能为null");
        }
        
        InviterCouponPO po = convertToePO(coupon);
        
        if (po.getId() == null) {
            // 新增
            po.setCreatedAt(LocalDateTime.now());
            po.setUpdatedAt(LocalDateTime.now());
            inviterCouponMapper.insert(po);
            coupon.setId(po.getId());
        } else {
            // 更新
            po.setUpdatedAt(LocalDateTime.now());
            inviterCouponMapper.updateById(po);
        }
        
        return coupon;
    }
    
    @Override
    public Optional<InviterCoupon> findById(Long id) {
        if (id == null) {
            return Optional.empty();
        }
        
        InviterCouponPO po = inviterCouponMapper.selectById(id);
        return po != null ? Optional.of(convertToEntity(po)) : Optional.empty();
    }
    
    @Override
    public Optional<InviterCoupon> findByCouponCode(String couponCode) {
        if (couponCode == null || couponCode.trim().isEmpty()) {
            return Optional.empty();
        }
        
        InviterCouponPO po = inviterCouponMapper.selectByCouponCode(couponCode);
        return po != null ? Optional.of(convertToEntity(po)) : Optional.empty();
    }
    
    @Override
    public List<InviterCoupon> findByInviterId(Long inviterId, Integer limit, Integer offset) {
        if (inviterId == null) {
            return List.of();
        }
        
        int actualLimit = limit != null ? limit : 20;
        int actualOffset = offset != null ? offset : 0;
        
        List<InviterCouponPO> pos = inviterCouponMapper.selectByInviterId(inviterId, actualLimit, actualOffset);
        return pos.stream().map(this::convertToEntity).collect(Collectors.toList());
    }
    
    @Override
    public List<InviterCoupon> findByInviterIdAndMonthKey(Long inviterId, MonthKey monthKey) {
        if (inviterId == null || monthKey == null) {
            return List.of();
        }
        
        List<InviterCouponPO> pos = inviterCouponMapper.selectByInviterIdAndMonthKey(inviterId, monthKey.getValue());
        return pos.stream().map(this::convertToEntity).collect(Collectors.toList());
    }
    
    @Override
    public Optional<InviterCoupon> findByTriggerOrderId(Long triggerOrderId) {
        if (triggerOrderId == null) {
            return Optional.empty();
        }
        
        InviterCouponPO po = inviterCouponMapper.selectByTriggerOrderId(triggerOrderId);
        return po != null ? Optional.of(convertToEntity(po)) : Optional.empty();
    }
    
    @Override
    public List<InviterCoupon> findActiveByInviterId(Long inviterId, LocalDateTime currentTime) {
        if (inviterId == null || currentTime == null) {
            return List.of();
        }
        
        List<InviterCouponPO> pos = inviterCouponMapper.selectActiveByInviterId(inviterId, currentTime);
        return pos.stream().map(this::convertToEntity).collect(Collectors.toList());
    }
    
    @Override
    public List<InviterCoupon> findExpiredCoupons(LocalDateTime currentTime, Integer limit) {
        if (currentTime == null) {
            return List.of();
        }
        
        int actualLimit = limit != null ? limit : 100;
        List<InviterCouponPO> pos = inviterCouponMapper.selectExpiredCoupons(currentTime, actualLimit);
        return pos.stream().map(this::convertToEntity).collect(Collectors.toList());
    }
    
    @Override
    public int batchUpdateStatus(List<Long> couponIds, InviterCoupon.Status status) {
        if (couponIds == null || couponIds.isEmpty() || status == null) {
            return 0;
        }
        
        return inviterCouponMapper.batchUpdateStatus(couponIds, status.name(), LocalDateTime.now());
    }
    
    @Override
    public long countByInviterIdAndStatus(Long inviterId, InviterCoupon.Status status) {
        if (inviterId == null) {
            return 0;
        }
        
        String statusStr = status != null ? status.name() : null;
        return inviterCouponMapper.countByInviterIdAndStatus(inviterId, statusStr);
    }
    
    @Override
    public void deleteById(Long id) {
        if (id != null) {
            inviterCouponMapper.deleteById(id);
        }
    }
    
    /**
     * 将领域实体转换为PO对象
     */
    private InviterCouponPO convertToePO(InviterCoupon coupon) {
        InviterCouponPO po = new InviterCouponPO();
        po.setId(coupon.getId());
        po.setInviterId(coupon.getInviterId());
        po.setInviteeId(coupon.getInviteeId());
        po.setTriggerOrderId(coupon.getTriggerOrderId());
        po.setCouponCode(coupon.getCouponCode());
        po.setCouponType(coupon.getCouponType().name());
        po.setDiscountValue(coupon.getDiscountValue().getAmount());
        po.setMinimumAmount(coupon.getMinimumAmount() != null ? coupon.getMinimumAmount().getAmount() : null);
        po.setMaximumDiscount(coupon.getMaximumDiscount() != null ? coupon.getMaximumDiscount().getAmount() : null);
        po.setStatus(coupon.getStatus().name());
        po.setMonthKey(coupon.getMonthKey().getValue());
        po.setValidFrom(coupon.getValidFrom());
        po.setValidUntil(coupon.getValidUntil());
        po.setUsedAt(coupon.getUsedAt());
        po.setUsedOrderId(coupon.getUsedOrderId());
        po.setCreatedAt(coupon.getCreatedAt());
        po.setUpdatedAt(coupon.getUpdatedAt());
        return po;
    }
    
    /**
     * 将PO对象转换为领域实体
     */
    private InviterCoupon convertToEntity(InviterCouponPO po) {
        InviterCoupon coupon;
        
        Money discountValue = Money.of(po.getDiscountValue());
        Money minimumAmount = po.getMinimumAmount() != null ? Money.of(po.getMinimumAmount()) : null;
        Money maximumDiscount = po.getMaximumDiscount() != null ? Money.of(po.getMaximumDiscount()) : null;
        MonthKey monthKey = MonthKey.of(po.getMonthKey());
        
        if ("FIXED_AMOUNT".equals(po.getCouponType())) {
            coupon = InviterCoupon.createFixedAmountCoupon(
                po.getInviterId(), po.getInviteeId(), po.getTriggerOrderId(),
                po.getCouponCode(), discountValue, minimumAmount,
                monthKey, po.getValidFrom(), po.getValidUntil()
            );
        } else {
            coupon = InviterCoupon.createPercentageCoupon(
                po.getInviterId(), po.getInviteeId(), po.getTriggerOrderId(),
                po.getCouponCode(), discountValue, minimumAmount, maximumDiscount,
                monthKey, po.getValidFrom(), po.getValidUntil()
            );
        }
        
        // 设置其他属性
        coupon.setId(po.getId());
        if (po.getUsedAt() != null && po.getUsedOrderId() != null) {
            coupon.use(po.getUsedOrderId());
        } else if ("CANCELLED".equals(po.getStatus())) {
            coupon.cancel();
        } else if ("EXPIRED".equals(po.getStatus())) {
            coupon.checkExpiry();
        }
        
        coupon.setCreatedAt(po.getCreatedAt());
        coupon.setUpdatedAt(po.getUpdatedAt());
        
        return coupon;
    }
}
