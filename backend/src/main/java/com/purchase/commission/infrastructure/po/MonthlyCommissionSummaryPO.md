# MonthlyCommissionSummaryPO

## 文件概述

该持久化对象（PO）类对应数据库中的`monthly_commission_summary`表，用于存储月度佣金汇总数据。作为基础设施层的数据模型，它使用MyBatis-Plus注解进行ORM映射，支持自动填充时间字段和主键自增功能。

### 设计思路
- 遵循PO设计模式，专用于数据持久化
- 使用MyBatis-Plus注解简化ORM配置
- 支持自动填充创建和更新时间
- 提供完整的字段映射和约束
- 确保与数据库表结构的一致性

### 架构位置
- 位于基础设施层（Infrastructure Layer）
- 对应数据库表结构
- 与Mapper接口协作进行数据访问
- 通过Repository实现类进行实体转换

## 核心功能

### 主要字段

| 字段名 | 数据库字段 | 类型 | 说明 | 约束 |
|-----|---------|---|---|----|
| id | id | Long | 汇总ID | 主键，自增 |
| inviterId | inviter_id | Long | 邀请者ID | 非空，外键 |
| monthKey | month_key | String | 月份键(YYYY-MM) | 非空，索引 |
| orderCount | order_count | Integer | 订单数量 | 默认0 |
| totalOrderAmount | total_order_amount | BigDecimal | 订单总金额 | 精度19,2 |
| commissionAmount | commission_amount | BigDecimal | 佣金金额 | 精度19,2 |
| bonusAmount | bonus_amount | BigDecimal | 奖金金额 | 精度19,2 |
| totalAmount | total_amount | BigDecimal | 总金额 | 精度19,2 |
| status | status | String | 结算状态 | 枚举值 |
| createdAt | created_at | LocalDateTime | 创建时间 | 自动填充 |
| updatedAt | updated_at | LocalDateTime | 更新时间 | 自动填充 |
| settledAt | settled_at | LocalDateTime | 结算时间 | 可空 |

### MyBatis-Plus注解说明

#### @TableName("monthly_commission_summary")
- **功能**: 指定对应的数据库表名
- **作用**: 建立PO类与数据库表的映射关系
- **必要性**: 确保ORM框架正确识别表结构

#### @TableId(value = "id", type = IdType.AUTO)
- **功能**: 指定主键字段和生成策略
- **策略**: 使用数据库自增ID
- **优势**: 简化ID管理，避免ID冲突

#### @TableField注解
- **value属性**: 指定数据库字段名
- **fill属性**: 指定自动填充策略
- **用途**: 处理Java字段名与数据库字段名的映射

#### 自动填充策略
- **FieldFill.INSERT**: 插入时自动填充
- **FieldFill.INSERT_UPDATE**: 插入和更新时都自动填充
- **实现**: 需要配置MetaObjectHandler处理器

## 使用示例

### 基本CRUD操作
```java
@Autowired
private MonthlyCommissionSummaryMapper mapper;

// 插入记录
MonthlyCommissionSummaryPO po = new MonthlyCommissionSummaryPO();
po.setInviterId(1L);
po.setMonthKey("2024-01");
po.setOrderCount(5);
po.setTotalOrderAmount(new BigDecimal("50000.00"));
po.setCommissionAmount(new BigDecimal("550.00"));
po.setBonusAmount(new BigDecimal("300.00"));
po.setTotalAmount(new BigDecimal("850.00"));
po.setStatus("PENDING");
mapper.insert(po);

// 查询记录
MonthlyCommissionSummaryPO result = mapper.selectById(po.getId());

// 更新记录
po.setStatus("COMPLETED");
po.setSettledAt(LocalDateTime.now());
mapper.updateById(po);

// 删除记录
mapper.deleteById(po.getId());
```

### 条件查询
```java
// 根据邀请者ID查询
LambdaQueryWrapper<MonthlyCommissionSummaryPO> wrapper = new LambdaQueryWrapper<>();
wrapper.eq(MonthlyCommissionSummaryPO::getInviterId, 1L)
       .orderByDesc(MonthlyCommissionSummaryPO::getMonthKey);
List<MonthlyCommissionSummaryPO> summaries = mapper.selectList(wrapper);

// 根据月份和状态查询
wrapper.clear();
wrapper.eq(MonthlyCommissionSummaryPO::getMonthKey, "2024-01")
       .eq(MonthlyCommissionSummaryPO::getStatus, "PENDING");
List<MonthlyCommissionSummaryPO> pendingSummaries = mapper.selectList(wrapper);

// 分页查询
IPage<MonthlyCommissionSummaryPO> page = new Page<>(1, 10);
IPage<MonthlyCommissionSummaryPO> result = mapper.selectPage(page, wrapper);
```

### 统计查询
```java
// 统计记录数量
Integer count = mapper.selectCount(
    new LambdaQueryWrapper<MonthlyCommissionSummaryPO>()
        .eq(MonthlyCommissionSummaryPO::getInviterId, 1L)
);

// 自定义统计查询（需要在Mapper中定义）
@Select("SELECT SUM(total_amount) FROM monthly_commission_summary WHERE inviter_id = #{inviterId}")
BigDecimal getTotalAmountByInviterId(@Param("inviterId") Long inviterId);
```

### 批量操作
```java
// 批量插入
List<MonthlyCommissionSummaryPO> poList = Arrays.asList(po1, po2, po3);
// 需要自定义批量插入方法或使用第三方插件

// 批量更新
LambdaUpdateWrapper<MonthlyCommissionSummaryPO> updateWrapper = new LambdaUpdateWrapper<>();
updateWrapper.set(MonthlyCommissionSummaryPO::getStatus, "COMPLETED")
             .in(MonthlyCommissionSummaryPO::getId, Arrays.asList(1L, 2L, 3L));
mapper.update(null, updateWrapper);
```

## 注意事项

### 数据类型精度
- **BigDecimal字段**: 确保精度设置与数据库一致
- **金额计算**: 避免使用Float或Double，防止精度丢失
- **时间字段**: 注意时区问题，建议使用UTC时间
- **字符串长度**: 确保varchar字段长度足够

### 自动填充配置
```java
@Component
public class MetaObjectHandlerImpl implements MetaObjectHandler {
    
    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
    }
    
    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
    }
}
```

### 索引优化
- **复合索引**: (inviter_id, month_key) 提高查询效率
- **状态索引**: status字段建立索引，支持状态查询
- **时间索引**: created_at, updated_at支持时间范围查询
- **避免过多索引**: 平衡查询性能和插入性能

### 并发控制
- **乐观锁**: 可添加version字段支持乐观锁
- **悲观锁**: 关键操作使用SELECT FOR UPDATE
- **事务隔离**: 合理设置事务隔离级别
- **死锁避免**: 保持操作顺序一致性

### 数据迁移
- **历史数据**: 考虑历史数据的迁移策略
- **字段变更**: 新增字段要有默认值
- **索引添加**: 大表添加索引要在低峰期进行
- **数据校验**: 迁移后进行数据完整性校验

### 性能优化
- **查询优化**: 避免SELECT *，只查询需要的字段
- **分页处理**: 大数据量查询使用分页
- **缓存策略**: 热点数据考虑Redis缓存
- **读写分离**: 查询操作使用读库，写操作使用主库

### 测试策略
- **单元测试**: 测试PO类的序列化和反序列化
- **集成测试**: 测试与数据库的实际交互
- **性能测试**: 测试大数据量下的CRUD性能
- **并发测试**: 测试高并发场景下的数据一致性 