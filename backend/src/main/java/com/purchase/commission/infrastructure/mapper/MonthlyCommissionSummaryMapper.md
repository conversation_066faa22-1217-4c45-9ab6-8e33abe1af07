# MonthlyCommissionSummaryMapper.md

## 1. 文件概述

`MonthlyCommissionSummaryMapper.java` 是佣金模块中的一个MyBatis-Plus Mapper接口，位于 `com.purchase.commission.infrastructure.mapper` 包中。它继承自MyBatis-Plus的 `BaseMapper<MonthlyCommissionSummaryPO>` 接口，为 `MonthlyCommissionSummaryPO` 实体（持久化对象）提供了基本的数据库操作能力。`MonthlyCommissionSummaryPO` 用于存储每个用户在每个月的佣金汇总数据，是佣金结算流程的最终结果之一。该Mapper接口是佣金服务层与数据库进行交互的桥梁，负责将业务对象的操作转换为SQL语句，实现月度佣金汇总数据的持久化管理。

## 2. 核心功能

*   **基础CRUD操作**: 继承 `BaseMapper`，自动拥有对 `MonthlyCommissionSummaryPO` 实体进行插入（`insert`）、根据ID查询（`selectById`）、根据条件查询列表（`selectList`）、更新（`updateById`）和删除（`deleteById`）等基础的增删改查功能。
*   **简化开发**: 通过MyBatis-Plus的自动化特性，无需编写SQL语句即可完成常见的月度佣金汇总数据数据库操作，极大地提高了开发效率。
*   **类型安全**: 针对 `MonthlyCommissionSummaryPO` 实体进行操作，提供了编译时类型检查，减少了运行时错误。
*   **数据持久化**: 负责将 `MonthlyCommissionSummaryPO` 领域对象的状态同步到数据库，或从数据库加载数据到 `MonthlyCommissionSummaryPO` 对象。

## 3. 接口说明

`MonthlyCommissionSummaryMapper` 接口本身没有定义任何自定义方法，其所有功能都继承自 `BaseMapper<MonthlyCommissionSummaryPO>`。以下是 `BaseMapper` 提供的一些常用方法：

### 3.1 常用继承方法

#### insert - 插入一条记录
*   **方法签名**: `int insert(MonthlyCommissionSummaryPO entity)`
*   **描述**: 插入一条 `MonthlyCommissionSummaryPO` 记录。在插入成功后，实体对象的ID会被自动填充（如果配置了自增长ID）。
*   **参数**:
    *   `entity` (MonthlyCommissionSummaryPO): 待插入的月度佣金汇总实体对象。
*   **返回值**: `int` - 影响的行数。

#### selectById - 根据ID查询
*   **方法签名**: `MonthlyCommissionSummaryPO selectById(Serializable id)`
*   **描述**: 根据主键ID查询一条 `MonthlyCommissionSummaryPO` 记录。
*   **参数**:
    *   `id` (Serializable): 月度佣金汇总的主键ID。
*   **返回值**: `MonthlyCommissionSummaryPO` - 匹配的实体，如果不存在则返回 `null`。

#### selectList - 查询列表
*   **方法签名**: `List<MonthlyCommissionSummaryPO> selectList(@Param("ew") Wrapper<MonthlyCommissionSummaryPO> queryWrapper)`
*   **描述**: 根据条件查询 `MonthlyCommissionSummaryPO` 列表。`queryWrapper` 可以用于构建复杂的查询条件，如 `eq`, `like`, `in` 等。
*   **参数**:
    *   `queryWrapper` (Wrapper<MonthlyCommissionSummaryPO>): 查询条件构造器。
*   **返回值**: `List<MonthlyCommissionSummaryPO>` - 匹配的实体列表。

#### updateById - 根据ID更新
*   **方法签名**: `int updateById(MonthlyCommissionSummaryPO entity)`
*   **描述**: 根据主键ID更新 `MonthlyCommissionSummaryPO` 记录。只会更新实体中非空的字段。
*   **参数**:
    *   `entity` (MonthlyCommissionSummaryPO): 包含待更新字段和主键ID的实体对象。
*   **返回值**: `int` - 影响的行数。

#### deleteById - 根据ID删除
*   **方法签名**: `int deleteById(Serializable id)`
*   **描述**: 根据主键ID删除一条 `MonthlyCommissionSummaryPO` 记录。
*   **参数**:
    *   `id` (Serializable): 月度佣金汇总的主键ID。
*   **返回值**: `int` - 影响的行数。

## 4. 业务规则

*   **唯一性**: `MonthlyCommissionSummaryPO` 通常会有一个联合唯一索引，例如 `(user_id, month_key)`，确保每个用户每个月只有一条佣金汇总记录。
*   **数据来源**: 佣金汇总数据来源于当月所有已结算的佣金记录，需要通过复杂的聚合计算生成。
*   **不可变性**: 一旦月度佣金汇总数据生成并确认，通常应视为不可变。任何修正都应通过冲销或调整记录来完成，而不是直接修改汇总数据。

## 5. 使用示例

```java
// 1. 在 MonthlyCommissionProcessor 服务中保存或更新月度汇总
@Service
public class MonthlyCommissionProcessor {
    @Autowired
    private MonthlyCommissionSummaryMapper summaryMapper;

    @Transactional
    public void processMonth(MonthKey monthKey) {
        // ... 计算当月每个用户的佣金汇总数据 ...
        Map<Long, BigDecimal> userMonthlyCommissions = calculateUserMonthlyCommissions(monthKey);

        for (Map.Entry<Long, BigDecimal> entry : userMonthlyCommissions.entrySet()) {
            Long userId = entry.getKey();
            BigDecimal totalCommission = entry.getValue();

            // 尝试查找现有汇总记录
            QueryWrapper<MonthlyCommissionSummaryPO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId).eq("month_key", monthKey.getValue());
            MonthlyCommissionSummaryPO existingSummary = summaryMapper.selectOne(queryWrapper);

            if (existingSummary != null) {
                // 更新现有记录
                existingSummary.setTotalCommission(totalCommission);
                existingSummary.setUpdatedAt(LocalDateTime.now());
                summaryMapper.updateById(existingSummary);
            } else {
                // 创建新记录
                MonthlyCommissionSummaryPO newSummary = new MonthlyCommissionSummaryPO();
                newSummary.setUserId(userId);
                newSummary.setMonthKey(monthKey.getValue());
                newSummary.setTotalCommission(totalCommission);
                newSummary.setCreatedAt(LocalDateTime.now());
                newSummary.setUpdatedAt(LocalDateTime.now());
                summaryMapper.insert(newSummary);
            }
        }
    }
}

// 2. 在佣金查询服务中获取月度汇总数据
@Service
public class CommissionQueryService {
    @Autowired
    private MonthlyCommissionSummaryMapper summaryMapper;

    public MonthlyCommissionSummaryPO getMonthlySummary(Long userId, MonthKey monthKey) {
        QueryWrapper<MonthlyCommissionSummaryPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).eq("month_key", monthKey.getValue());
        return summaryMapper.selectOne(queryWrapper);
    }
}

// 3. 测试示例
@SpringBootTest
class MonthlyCommissionSummaryMapperTest {
    @Autowired
    private MonthlyCommissionSummaryMapper summaryMapper;

    @Test
    @Transactional
    void testInsertAndSelect() {
        MonthlyCommissionSummaryPO summary = new MonthlyCommissionSummaryPO();
        summary.setUserId(1L);
        summary.setMonthKey("2024-07");
        summary.setTotalCommission(new BigDecimal("1234.56"));
        summary.setCreatedAt(LocalDateTime.now());
        summary.setUpdatedAt(LocalDateTime.now());

        int result = summaryMapper.insert(summary);
        assertThat(result).isEqualTo(1);
        assertThat(summary.getId()).isNotNull();

        QueryWrapper<MonthlyCommissionSummaryPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", 1L).eq("month_key", "2024-07");
        MonthlyCommissionSummaryPO retrievedSummary = summaryMapper.selectOne(queryWrapper);
        assertThat(retrievedSummary).isNotNull();
        assertThat(retrievedSummary.getTotalCommission()).isEqualByComparingTo(new BigDecimal("1234.56"));
    }
}
```

## 6. 注意事项

*   **MyBatis-Plus集成**: 继承 `BaseMapper` 使得该Mapper自动拥有强大的CRUD能力，减少了重复代码。
*   **PO与领域实体**: `MonthlyCommissionSummaryPO` 是一个持久化对象（PO），它可能与领域模型中的 `MonthlyCommissionSummary` 实体有所不同。Mapper负责PO的持久化，服务层负责PO与领域实体之间的转换。
*   **事务管理**: 在服务层调用Mapper方法时，必须确保在事务中执行，以保证数据操作的原子性和一致性。
*   **SQL注入防护**: MyBatis-Plus的 `Wrapper` 机制和参数绑定机制可以有效防止SQL注入攻击。
*   **性能优化**: 对于月度佣金汇总这种聚合数据，查询性能至关重要。应确保数据库中对 `user_id` 和 `month_key` 字段建立了联合唯一索引，以优化查询和防止重复数据。
*   **数据类型映射**: 确保Java实体中的数据类型与数据库表中的字段类型正确映射，特别是 `BigDecimal` 用于金额计算，以避免精度问题。
*   **可扩展性**: 如果未来需要记录更多维度的月度佣金汇总数据（如不同佣金类型的汇总），可以在 `MonthlyCommissionSummaryPO` 中添加相应的字段。
*   **日志记录**: 在服务层或Mapper层配置SQL日志，可以方便地查看实际执行的SQL语句，便于调试和性能调优。