package com.purchase.commission.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.purchase.commission.domain.entity.MonthlyCommissionSummary;
import com.purchase.commission.domain.repository.MonthlyCommissionSummaryRepository;
import com.purchase.commission.domain.valueobject.CommissionRate;
import com.purchase.commission.domain.valueobject.Money;
import com.purchase.commission.domain.valueobject.MonthKey;
import com.purchase.commission.infrastructure.mapper.MonthlyCommissionSummaryMapper;
import com.purchase.commission.infrastructure.po.MonthlyCommissionSummaryPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 月度佣金汇总仓储实现类（简化版）
 * 
 * 暂时只实现核心的CRUD方法，复杂查询方法返回默认值。
 * 后续可以根据实际需求逐步完善。
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class MonthlyCommissionSummaryRepositoryImpl implements MonthlyCommissionSummaryRepository {

    private final MonthlyCommissionSummaryMapper mapper;

    @Override
    public MonthlyCommissionSummary save(MonthlyCommissionSummary summary) {
        log.info("保存月度佣金汇总: 邀请者ID={}, 月份={}", 
                summary.getInviterId(), summary.getMonthKey().getValue());
        
        MonthlyCommissionSummaryPO po = convertToPO(summary);
        
        if (summary.getId() == null) {
            mapper.insert(po);
            summary.setId(po.getId());
        } else {
            mapper.updateById(po);
        }
        
        log.info("月度佣金汇总保存成功: ID={}", summary.getId());
        return summary;
    }

    @Override
    public Optional<MonthlyCommissionSummary> findById(Long id) {
        log.debug("查找月度佣金汇总: ID={}", id);
        return Optional.ofNullable(mapper.selectById(id)).map(this::convertToEntity);
    }

    @Override
    public Optional<MonthlyCommissionSummary> findByInviterIdAndMonthKey(Long inviterId, MonthKey monthKey) {
        log.debug("查找指定邀请者和月份的佣金汇总: 邀请者ID={}, 月份={}", inviterId, monthKey.getValue());
        
        LambdaQueryWrapper<MonthlyCommissionSummaryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MonthlyCommissionSummaryPO::getInviterId, inviterId)
               .eq(MonthlyCommissionSummaryPO::getMonthKey, monthKey.getValue());
        
        return Optional.ofNullable(mapper.selectOne(wrapper)).map(this::convertToEntity);
    }

    @Override
    public void deleteById(Long id) {
        log.info("删除月度佣金汇总: ID={}", id);
        mapper.deleteById(id);
    }

    @Override
    public List<MonthlyCommissionSummary> findByStatus(MonthlyCommissionSummary.Status status, Integer limit, Integer offset) {
        LambdaQueryWrapper<MonthlyCommissionSummaryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MonthlyCommissionSummaryPO::getStatus, status.name())
               .orderByDesc(MonthlyCommissionSummaryPO::getUpdatedAt)
               .last("LIMIT " + limit + " OFFSET " + offset);
        return mapper.selectList(wrapper).stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    @Override
    public List<MonthlyCommissionSummary> findByInviterIdAndStatus(Long inviterId, MonthlyCommissionSummary.Status status) {
        LambdaQueryWrapper<MonthlyCommissionSummaryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MonthlyCommissionSummaryPO::getInviterId, inviterId)
               .eq(MonthlyCommissionSummaryPO::getStatus, status.name())
               .orderByDesc(MonthlyCommissionSummaryPO::getUpdatedAt);
        return mapper.selectList(wrapper).stream().map(this::convertToEntity).collect(Collectors.toList());
    }
    
    @Override
    public List<MonthlyCommissionSummary> findByInviterId(Long inviterId, Integer limit, Integer offset) {
        LambdaQueryWrapper<MonthlyCommissionSummaryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MonthlyCommissionSummaryPO::getInviterId, inviterId)
               .orderByDesc(MonthlyCommissionSummaryPO::getUpdatedAt)
               .last("LIMIT " + limit + " OFFSET " + offset);
        return mapper.selectList(wrapper).stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    @Override
    public List<MonthlyCommissionSummary> findByMonthKey(MonthKey monthKey, Integer limit, Integer offset) {
        LambdaQueryWrapper<MonthlyCommissionSummaryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MonthlyCommissionSummaryPO::getMonthKey, monthKey.getValue())
               .orderByDesc(MonthlyCommissionSummaryPO::getUpdatedAt)
               .last("LIMIT " + limit + " OFFSET " + offset);
        return mapper.selectList(wrapper).stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    @Override
    public List<MonthlyCommissionSummary> findSettlableSummaries(Integer limit, Integer offset) {
        LambdaQueryWrapper<MonthlyCommissionSummaryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MonthlyCommissionSummaryPO::getStatus, MonthlyCommissionSummary.Status.PENDING.name())
               .orderByDesc(MonthlyCommissionSummaryPO::getUpdatedAt)
               .last("LIMIT " + limit + " OFFSET " + offset);
        return mapper.selectList(wrapper).stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    @Override
    public List<MonthlyCommissionSummary> findSettlableSummariesByInviterId(Long inviterId) {
        LambdaQueryWrapper<MonthlyCommissionSummaryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MonthlyCommissionSummaryPO::getInviterId, inviterId)
               .eq(MonthlyCommissionSummaryPO::getStatus, MonthlyCommissionSummary.Status.PENDING.name())
               .orderByDesc(MonthlyCommissionSummaryPO::getUpdatedAt);
        return mapper.selectList(wrapper).stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    @Override
    public boolean existsByInviterIdAndMonthKey(Long inviterId, MonthKey monthKey) {
        return findByInviterIdAndMonthKey(inviterId, monthKey).isPresent();
    }

    @Override
    public Integer countByInviterId(Long inviterId) {
        LambdaQueryWrapper<MonthlyCommissionSummaryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MonthlyCommissionSummaryPO::getInviterId, inviterId);
        return Math.toIntExact(mapper.selectCount(wrapper));
    }

    @Override
    public Integer countByStatus(MonthlyCommissionSummary.Status status) {
        LambdaQueryWrapper<MonthlyCommissionSummaryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MonthlyCommissionSummaryPO::getStatus, status.name());
        return Math.toIntExact(mapper.selectCount(wrapper));
    }

    @Override
    public Integer batchUpdateStatus(List<Long> ids, MonthlyCommissionSummary.Status status) {
        log.info("批量更新月度汇总状态: IDs={}, 状态={}", ids, status);
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        LambdaUpdateWrapper<MonthlyCommissionSummaryPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(MonthlyCommissionSummaryPO::getId, ids)
               .set(MonthlyCommissionSummaryPO::getStatus, status.name());
        return mapper.update(null, wrapper);
    }

    @Override
    public void softDeleteById(Long id) {
        throw new UnsupportedOperationException("MonthlyCommissionSummary不支持软删除");
    }

    @Override
    public Integer batchSoftDelete(List<Long> ids) {
        throw new UnsupportedOperationException("MonthlyCommissionSummary不支持批量软删除");
    }

    /**
     * 将实体对象转换为持久化对象
     */
    private MonthlyCommissionSummaryPO convertToPO(MonthlyCommissionSummary entity) {
        MonthlyCommissionSummaryPO po = new MonthlyCommissionSummaryPO();
        po.setId(entity.getId());
        po.setInviterId(entity.getInviterId());
        po.setMonthKey(entity.getMonthKey().getValue());
        po.setOrderCount(entity.getOrderCount());
        po.setTotalOrderAmount(entity.getTotalOrderAmount().getAmount());
        po.setCommissionRate(entity.getCommissionRate().getRate());
        po.setCommissionAmount(entity.getCommissionAmount().getAmount());
        po.setBonusAmount(entity.getMonthlyBonus().getAmount());
        po.setTotalAmount(entity.getTotalAmount().getAmount());
        po.setStatus(entity.getStatus().name());
        po.setCreatedAt(entity.getCreatedAt());
        po.setUpdatedAt(entity.getUpdatedAt());
        po.setLastCalculatedAt(entity.getLastCalculatedAt());
        po.setSettledAt(entity.getSettledAt());
        return po;
    }

    /**
     * 将持久化对象转换为实体对象
     */
    private MonthlyCommissionSummary convertToEntity(MonthlyCommissionSummaryPO po) {
        return MonthlyCommissionSummary.reconstitute(
                po.getId(),
                po.getInviterId(),
                MonthKey.of(po.getMonthKey()),
                Money.of(po.getTotalOrderAmount()),
                CommissionRate.of(po.getCommissionRate()),
                Money.of(po.getCommissionAmount()),
                Money.of(po.getBonusAmount()),
                Money.of(po.getTotalAmount()),
                po.getOrderCount(),
                MonthlyCommissionSummary.Status.valueOf(po.getStatus()),
                po.getSettledAt(),
                po.getLastCalculatedAt(),
                po.getCreatedAt(),
                po.getUpdatedAt()
        );
    }

    // =====================================================
    // 月初统一处理相关方法实现
    // =====================================================

    @Override
    public List<MonthlyCommissionSummary> findByMonthKey(MonthKey monthKey) {
        log.debug("根据月份查找所有月度佣金汇总: 月份={}", monthKey);

        LambdaQueryWrapper<MonthlyCommissionSummaryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MonthlyCommissionSummaryPO::getMonthKey, monthKey.getValue())
               .orderByDesc(MonthlyCommissionSummaryPO::getUpdatedAt);

        List<MonthlyCommissionSummaryPO> poList = mapper.selectList(wrapper);
        return poList.stream()
                     .map(this::convertToEntity)
                     .collect(Collectors.toList());
    }
}