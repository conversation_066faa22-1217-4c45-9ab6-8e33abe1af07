package com.purchase.commission.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.purchase.commission.infrastructure.po.InviterCouponPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 邀请人优惠券数据访问层
 * 基于MyBatis-Plus的Mapper接口
 */
@Mapper
public interface InviterCouponMapper extends BaseMapper<InviterCouponPO> {
    
    /**
     * 根据优惠券代码查找优惠券
     * 
     * @param couponCode 优惠券代码
     * @return 优惠券PO
     */
    @Select("SELECT * FROM inviter_coupon WHERE coupon_code = #{couponCode}")
    InviterCouponPO selectByCouponCode(@Param("couponCode") String couponCode);
    
    /**
     * 根据邀请人ID查找优惠券列表
     * 
     * @param inviterId 邀请人ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 优惠券列表
     */
    @Select("SELECT * FROM inviter_coupon WHERE inviter_id = #{inviterId} " +
            "ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<InviterCouponPO> selectByInviterId(@Param("inviterId") Long inviterId, 
                                           @Param("limit") Integer limit, 
                                           @Param("offset") Integer offset);
    
    /**
     * 根据邀请人ID和月份查找优惠券列表
     * 
     * @param inviterId 邀请人ID
     * @param monthKey 月份键
     * @return 优惠券列表
     */
    @Select("SELECT * FROM inviter_coupon WHERE inviter_id = #{inviterId} AND month_key = #{monthKey} " +
            "ORDER BY created_at DESC")
    List<InviterCouponPO> selectByInviterIdAndMonthKey(@Param("inviterId") Long inviterId, 
                                                      @Param("monthKey") String monthKey);
    
    /**
     * 根据触发订单ID查找优惠券
     * 
     * @param triggerOrderId 触发订单ID
     * @return 优惠券PO
     */
    @Select("SELECT * FROM inviter_coupon WHERE trigger_order_id = #{triggerOrderId}")
    InviterCouponPO selectByTriggerOrderId(@Param("triggerOrderId") Long triggerOrderId);
    
    /**
     * 查找有效的优惠券
     * 
     * @param inviterId 邀请人ID
     * @param currentTime 当前时间
     * @return 有效优惠券列表
     */
    @Select("SELECT * FROM inviter_coupon WHERE inviter_id = #{inviterId} " +
            "AND status = 'ACTIVE' " +
            "AND valid_from <= #{currentTime} " +
            "AND valid_until > #{currentTime} " +
            "ORDER BY created_at DESC")
    List<InviterCouponPO> selectActiveByInviterId(@Param("inviterId") Long inviterId, 
                                                 @Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 查找过期的优惠券
     * 
     * @param currentTime 当前时间
     * @param limit 限制数量
     * @return 过期优惠券列表
     */
    @Select("SELECT * FROM inviter_coupon WHERE status = 'ACTIVE' " +
            "AND valid_until <= #{currentTime} " +
            "ORDER BY valid_until ASC LIMIT #{limit}")
    List<InviterCouponPO> selectExpiredCoupons(@Param("currentTime") LocalDateTime currentTime, 
                                              @Param("limit") Integer limit);
    
    /**
     * 批量更新优惠券状态
     * 
     * @param couponIds 优惠券ID列表
     * @param status 新状态
     * @param updatedAt 更新时间
     * @return 更新数量
     */
    @Update("<script>" +
            "UPDATE inviter_coupon SET status = #{status}, updated_at = #{updatedAt} " +
            "WHERE id IN " +
            "<foreach collection='couponIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("couponIds") List<Long> couponIds, 
                         @Param("status") String status, 
                         @Param("updatedAt") LocalDateTime updatedAt);
    
    /**
     * 统计邀请人的优惠券数量
     * 
     * @param inviterId 邀请人ID
     * @param status 状态（可选）
     * @return 优惠券数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM inviter_coupon WHERE inviter_id = #{inviterId} " +
            "<if test='status != null'>" +
            "AND status = #{status}" +
            "</if>" +
            "</script>")
    long countByInviterIdAndStatus(@Param("inviterId") Long inviterId, 
                                  @Param("status") String status);
    
    /**
     * 根据状态统计优惠券数量
     * 
     * @param status 状态
     * @return 优惠券数量
     */
    @Select("SELECT COUNT(*) FROM inviter_coupon WHERE status = #{status}")
    long countByStatus(@Param("status") String status);
    
    /**
     * 查找即将过期的优惠券
     * 
     * @param fromTime 开始时间
     * @param toTime 结束时间
     * @param limit 限制数量
     * @return 即将过期的优惠券列表
     */
    @Select("SELECT * FROM inviter_coupon WHERE status = 'ACTIVE' " +
            "AND valid_until > #{fromTime} " +
            "AND valid_until <= #{toTime} " +
            "ORDER BY valid_until ASC LIMIT #{limit}")
    List<InviterCouponPO> selectExpiringSoon(@Param("fromTime") LocalDateTime fromTime,
                                            @Param("toTime") LocalDateTime toTime,
                                            @Param("limit") Integer limit);
    
    /**
     * 根据邀请人ID和状态查找优惠券
     * 
     * @param inviterId 邀请人ID
     * @param status 状态
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 优惠券列表
     */
    @Select("SELECT * FROM inviter_coupon WHERE inviter_id = #{inviterId} AND status = #{status} " +
            "ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<InviterCouponPO> selectByInviterIdAndStatus(@Param("inviterId") Long inviterId,
                                                    @Param("status") String status,
                                                    @Param("limit") Integer limit,
                                                    @Param("offset") Integer offset);
    
    /**
     * 更新优惠券使用信息
     * 
     * @param id 优惠券ID
     * @param usedOrderId 使用订单ID
     * @param usedAt 使用时间
     * @param updatedAt 更新时间
     * @return 更新数量
     */
    @Update("UPDATE inviter_coupon SET " +
            "status = 'USED', " +
            "used_order_id = #{usedOrderId}, " +
            "used_at = #{usedAt}, " +
            "updated_at = #{updatedAt} " +
            "WHERE id = #{id}")
    int updateUsageInfo(@Param("id") Long id,
                       @Param("usedOrderId") Long usedOrderId,
                       @Param("usedAt") LocalDateTime usedAt,
                       @Param("updatedAt") LocalDateTime updatedAt);
    
    /**
     * 根据月份键统计优惠券数量
     * 
     * @param monthKey 月份键
     * @return 优惠券数量
     */
    @Select("SELECT COUNT(*) FROM inviter_coupon WHERE month_key = #{monthKey}")
    long countByMonthKey(@Param("monthKey") String monthKey);
    
    /**
     * 根据月份键和状态统计优惠券数量
     * 
     * @param monthKey 月份键
     * @param status 状态
     * @return 优惠券数量
     */
    @Select("SELECT COUNT(*) FROM inviter_coupon WHERE month_key = #{monthKey} AND status = #{status}")
    long countByMonthKeyAndStatus(@Param("monthKey") String monthKey, @Param("status") String status);
}
