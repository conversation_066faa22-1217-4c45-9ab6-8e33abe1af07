package com.purchase.commission.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.purchase.commission.infrastructure.po.CommissionProcessingLogPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 佣金处理日志数据访问层
 */
@Mapper
public interface CommissionProcessingLogMapper extends BaseMapper<CommissionProcessingLogPO> {
    
    /**
     * 根据月份键查找处理日志
     */
    @Select("SELECT * FROM commission_processing_log WHERE month_key = #{monthKey}")
    CommissionProcessingLogPO findByMonthKey(@Param("monthKey") String monthKey);
    
    /**
     * 检查月份是否已处理
     */
    @Select("SELECT COUNT(*) > 0 FROM commission_processing_log WHERE month_key = #{monthKey}")
    boolean existsByMonthKey(@Param("monthKey") String monthKey);
    
    /**
     * 检查月份是否已处理（包含指定状态）
     */
    @Select("SELECT COUNT(*) > 0 FROM commission_processing_log WHERE month_key = #{monthKey} AND status IN (${statuses})")
    boolean existsByMonthKeyAndStatusIn(@Param("monthKey") String monthKey, @Param("statuses") String statuses);
    
    /**
     * 查找最近的处理日志
     */
    @Select("SELECT * FROM commission_processing_log ORDER BY created_at DESC LIMIT #{limit}")
    List<CommissionProcessingLogPO> findRecentLogs(@Param("limit") int limit);
    
    /**
     * 查找指定状态的处理日志
     */
    @Select("SELECT * FROM commission_processing_log WHERE status = #{status} ORDER BY created_at DESC")
    List<CommissionProcessingLogPO> findByStatus(@Param("status") String status);
    
    /**
     * 查找指定月份范围的处理日志
     */
    @Select("SELECT * FROM commission_processing_log WHERE month_key BETWEEN #{startMonth} AND #{endMonth} ORDER BY month_key DESC")
    List<CommissionProcessingLogPO> findByMonthKeyBetween(@Param("startMonth") String startMonth, @Param("endMonth") String endMonth);
    
    /**
     * 统计指定状态的处理日志数量
     */
    @Select("SELECT COUNT(*) FROM commission_processing_log WHERE status = #{status}")
    long countByStatus(@Param("status") String status);
}
