# CommissionRecordMapper.java

## 文件概述 (File Overview)
`CommissionRecordMapper.java` 是佣金记录的数据访问层接口，位于 `com.purchase.commission.infrastructure.mapper` 包中。该接口继承了MyBatis-Plus的 `BaseMapper<CommissionRecordPO>` 接口，提供了佣金记录的完整数据访问功能。通过自定义的SQL查询方法，支持按订单、邀请者、被邀请者、状态、月份等多个维度的数据查询，以及批量操作、统计计算等高级功能。该Mapper接口是佣金系统数据持久化的核心组件，为上层业务服务提供了丰富的数据访问能力。

## 核心功能 (Core Functionality)
*   **基础CRUD**: 继承BaseMapper提供的标准增删改查功能
*   **多维查询**: 支持按订单、邀请者、被邀请者、状态、月份等维度查询
*   **分页查询**: 提供多种场景的分页查询支持
*   **统计计算**: 支持数量统计、金额汇总等聚合计算
*   **批量操作**: 支持批量状态更新和软删除操作
*   **条件组合**: 支持多条件组合查询，满足复杂业务需求
*   **性能优化**: 通过合理的索引设计和SQL优化提高查询性能
*   **数据安全**: 使用软删除机制保护重要的佣金数据

## 接口说明 (Interface Description)

### 基础查询方法

#### selectByOrderId - 根据订单ID查找佣金记录
*   **方法签名**: `List<CommissionRecordPO> selectByOrderId(@Param("orderId") Long orderId)`
*   **参数**: `orderId` (Long) - 订单ID
*   **返回值**: `List<CommissionRecordPO>` - 该订单相关的所有佣金记录
*   **业务场景**: 查看特定订单产生的所有佣金记录，用于订单详情页面展示
*   **SQL逻辑**: 根据order_id字段查询，包含软删除过滤

#### selectByInviterIdAndMonthKey - 根据邀请者ID和月份查找佣金记录
*   **方法签名**: `List<CommissionRecordPO> selectByInviterIdAndMonthKey(@Param("inviterId") Long inviterId, @Param("monthKey") String monthKey)`
*   **参数**: 
    *   `inviterId` (Long) - 邀请者ID
    *   `monthKey` (String) - 月份键值，格式如"2024-01"
*   **返回值**: `List<CommissionRecordPO>` - 指定邀请者在指定月份的佣金记录
*   **业务场景**: 月度佣金结算时查询特定邀请者的佣金记录
*   **SQL逻辑**: 联合查询inviter_id和month_key字段

#### selectByInviteeId - 根据被邀请者ID查找佣金记录
*   **方法签名**: `List<CommissionRecordPO> selectByInviteeId(@Param("inviteeId") Long inviteeId)`
*   **参数**: `inviteeId` (Long) - 被邀请者ID
*   **返回值**: `List<CommissionRecordPO>` - 该被邀请者产生的所有佣金记录
*   **业务场景**: 查看某个用户为其邀请者产生的佣金记录
*   **SQL逻辑**: 根据invitee_id字段查询

#### selectByInviterIdAndStatus - 根据邀请者ID和状态查找佣金记录
*   **方法签名**: `List<CommissionRecordPO> selectByInviterIdAndStatus(@Param("inviterId") Long inviterId, @Param("status") String status)`
*   **参数**: 
    *   `inviterId` (Long) - 邀请者ID
    *   `status` (String) - 佣金状态（PENDING、CONFIRMED、SETTLED等）
*   **返回值**: `List<CommissionRecordPO>` - 符合条件的佣金记录列表
*   **业务场景**: 查询特定邀请者的特定状态佣金记录
*   **SQL逻辑**: 联合查询inviter_id和status字段

#### selectSettlableRecords - 查找可结算的佣金记录
*   **方法签名**: `List<CommissionRecordPO> selectSettlableRecords(@Param("inviterId") Long inviterId, @Param("monthKey") String monthKey)`
*   **参数**: 
    *   `inviterId` (Long) - 邀请者ID
    *   `monthKey` (String) - 月份键值
*   **返回值**: `List<CommissionRecordPO>` - 可结算的佣金记录列表
*   **业务场景**: 月度结算时查找符合结算条件的佣金记录
*   **SQL逻辑**: 查询状态为CONFIRMED且满足结算条件的记录

### 分页查询方法

#### selectByInviterIdWithPage - 根据邀请者ID分页查找佣金记录
*   **方法签名**: `List<CommissionRecordPO> selectByInviterIdWithPage(@Param("inviterId") Long inviterId, @Param("limit") Integer limit, @Param("offset") Integer offset)`
*   **参数**: 
    *   `inviterId` (Long) - 邀请者ID
    *   `limit` (Integer) - 每页记录数
    *   `offset` (Integer) - 偏移量
*   **返回值**: `List<CommissionRecordPO>` - 分页的佣金记录列表
*   **业务场景**: 邀请者查看自己的佣金记录列表，支持分页展示
*   **SQL逻辑**: 使用LIMIT和OFFSET实现分页

#### selectByStatusWithPage - 根据状态分页查找佣金记录
*   **方法签名**: `List<CommissionRecordPO> selectByStatusWithPage(@Param("status") String status, @Param("limit") Integer limit, @Param("offset") Integer offset)`
*   **参数**: 
    *   `status` (String) - 佣金状态
    *   `limit` (Integer) - 每页记录数
    *   `offset` (Integer) - 偏移量
*   **返回值**: `List<CommissionRecordPO>` - 分页的佣金记录列表
*   **业务场景**: 管理员按状态查看佣金记录，如查看所有待处理的佣金
*   **SQL逻辑**: 根据状态过滤并分页

#### selectByMonthKeyWithPage - 根据月份分页查找佣金记录
*   **方法签名**: `List<CommissionRecordPO> selectByMonthKeyWithPage(@Param("monthKey") String monthKey, @Param("limit") Integer limit, @Param("offset") Integer offset)`
*   **参数**: 
    *   `monthKey` (String) - 月份键值
    *   `limit` (Integer) - 每页记录数
    *   `offset` (Integer) - 偏移量
*   **返回值**: `List<CommissionRecordPO>` - 分页的佣金记录列表
*   **业务场景**: 按月份查看佣金记录，用于月度报表和分析
*   **SQL逻辑**: 根据月份过滤并分页

### 统计计算方法

#### countByOrderIdAndInviterId - 统计特定订单和邀请者的佣金记录数量
*   **方法签名**: `Integer countByOrderIdAndInviterId(@Param("orderId") Long orderId, @Param("inviterId") Long inviterId)`
*   **参数**: 
    *   `orderId` (Long) - 订单ID
    *   `inviterId` (Long) - 邀请者ID
*   **返回值**: `Integer` - 记录数量
*   **业务场景**: 检查特定订单是否已为特定邀请者生成佣金记录
*   **SQL逻辑**: COUNT查询，防止重复生成佣金记录

#### countByInviterIdAndMonthKey - 统计邀请者月度佣金记录数量
*   **方法签名**: `Integer countByInviterIdAndMonthKey(@Param("inviterId") Long inviterId, @Param("monthKey") String monthKey)`
*   **参数**: 
    *   `inviterId` (Long) - 邀请者ID
    *   `monthKey` (String) - 月份键值
*   **返回值**: `Integer` - 记录数量
*   **业务场景**: 统计邀请者月度订单数量，用于月度汇总
*   **SQL逻辑**: COUNT查询，按邀请者和月份分组

#### countByInviterId - 统计邀请者的总佣金记录数量
*   **方法签名**: `Integer countByInviterId(@Param("inviterId") Long inviterId)`
*   **参数**: `inviterId` (Long) - 邀请者ID
*   **返回值**: `Integer` - 记录数量
*   **业务场景**: 统计邀请者的历史总订单数量
*   **SQL逻辑**: COUNT查询，按邀请者统计

#### sumOrderAmountByInviterIdAndMonthKey - 计算邀请者月度订单金额总和
*   **方法签名**: `BigDecimal sumOrderAmountByInviterIdAndMonthKey(@Param("inviterId") Long inviterId, @Param("monthKey") String monthKey)`
*   **参数**: 
    *   `inviterId` (Long) - 邀请者ID
    *   `monthKey` (String) - 月份键值
*   **返回值**: `BigDecimal` - 订单金额总和
*   **业务场景**: 计算邀请者月度带来的订单总金额
*   **SQL逻辑**: SUM查询order_amount字段

#### sumCommissionAmountByInviterIdAndMonthKey - 计算邀请者月度佣金总和
*   **方法签名**: `BigDecimal sumCommissionAmountByInviterIdAndMonthKey(@Param("inviterId") Long inviterId, @Param("monthKey") String monthKey)`
*   **参数**: 
    *   `inviterId` (Long) - 邀请者ID
    *   `monthKey` (String) - 月份键值
*   **返回值**: `BigDecimal` - 佣金金额总和
*   **业务场景**: 计算邀请者月度应得佣金总额
*   **SQL逻辑**: SUM查询commission_amount字段

### 批量操作方法

#### batchUpdateStatus - 批量更新佣金记录状态
*   **方法签名**: `Integer batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") String status)`
*   **参数**: 
    *   `ids` (List<Long>) - 要更新的记录ID列表
    *   `status` (String) - 新的状态值
*   **返回值**: `Integer` - 更新的记录数量
*   **业务场景**: 批量确认或结算佣金记录
*   **SQL逻辑**: 使用IN条件批量更新status字段

#### softDeleteById - 软删除单个佣金记录
*   **方法签名**: `Integer softDeleteById(@Param("id") Long id)`
*   **参数**: `id` (Long) - 要删除的记录ID
*   **返回值**: `Integer` - 删除的记录数量（0或1）
*   **业务场景**: 删除错误的佣金记录，但保留数据用于审计
*   **SQL逻辑**: 更新deleted字段为true，而不是物理删除

#### batchSoftDelete - 批量软删除佣金记录
*   **方法签名**: `Integer batchSoftDelete(@Param("ids") List<Long> ids)`
*   **参数**: `ids` (List<Long>) - 要删除的记录ID列表
*   **返回值**: `Integer` - 删除的记录数量
*   **业务场景**: 批量删除错误的佣金记录
*   **SQL逻辑**: 批量更新deleted字段为true

## 使用示例 (Usage Examples)

```java
// 1. 在Repository实现类中的使用示例
@Repository
public class CommissionRecordRepositoryImpl implements CommissionRecordRepository {
    
    @Autowired
    private CommissionRecordMapper commissionRecordMapper;
    
    // 查询邀请者的月度佣金记录
    @Override
    public List<CommissionRecord> findByInviterAndMonth(Long inviterId, MonthKey monthKey) {
        List<CommissionRecordPO> pos = commissionRecordMapper
            .selectByInviterIdAndMonthKey(inviterId, monthKey.toString());
        
        return pos.stream()
            .map(this::convertToDomain)
            .collect(Collectors.toList());
    }
    
    // 分页查询邀请者的佣金记录
    @Override
    public Page<CommissionRecord> findByInviterWithPage(Long inviterId, int pageNum, int pageSize) {
        int offset = (pageNum - 1) * pageSize;
        
        List<CommissionRecordPO> pos = commissionRecordMapper
            .selectByInviterIdWithPage(inviterId, pageSize, offset);
        
        int total = commissionRecordMapper.countByInviterId(inviterId);
        
        List<CommissionRecord> records = pos.stream()
            .map(this::convertToDomain)
            .collect(Collectors.toList());
        
        return new Page<>(records, pageNum, pageSize, total);
    }
    
    // 计算邀请者月度佣金汇总
    @Override
    public MonthlyCommissionSummary calculateMonthlySummary(Long inviterId, MonthKey monthKey) {
        String monthKeyStr = monthKey.toString();
        
        Integer orderCount = commissionRecordMapper
            .countByInviterIdAndMonthKey(inviterId, monthKeyStr);
        
        BigDecimal totalOrderAmount = commissionRecordMapper
            .sumOrderAmountByInviterIdAndMonthKey(inviterId, monthKeyStr);
        
        BigDecimal totalCommissionAmount = commissionRecordMapper
            .sumCommissionAmountByInviterIdAndMonthKey(inviterId, monthKeyStr);
        
        return MonthlyCommissionSummary.builder()
            .inviterId(inviterId)
            .monthKey(monthKey)
            .orderCount(orderCount)
            .totalOrderAmount(Money.of(totalOrderAmount))
            .commissionAmount(Money.of(totalCommissionAmount))
            .build();
    }
    
    // 批量确认佣金记录
    @Override
    @Transactional
    public int batchConfirmCommissions(List<Long> recordIds) {
        return commissionRecordMapper.batchUpdateStatus(recordIds, "CONFIRMED");
    }
}

// 2. 在Service层中的使用示例
@Service
public class CommissionRecordService {

    @Autowired
    private CommissionRecordMapper commissionRecordMapper;

    // 查询订单的佣金分配情况
    public List<CommissionAllocation> getOrderCommissionAllocations(Long orderId) {
        List<CommissionRecordPO> records = commissionRecordMapper.selectByOrderId(orderId);

        return records.stream()
            .map(record -> {
                CommissionAllocation allocation = new CommissionAllocation();
                allocation.setInviterId(record.getInviterId());
                allocation.setInviterName(getUserName(record.getInviterId()));
                allocation.setCommissionAmount(record.getCommissionAmount());
                allocation.setCommissionRate(record.getCommissionRate());
                allocation.setStatus(record.getStatus());
                return allocation;
            })
            .collect(Collectors.toList());
    }

    // 检查佣金记录是否已存在
    public boolean isCommissionRecordExists(Long orderId, Long inviterId) {
        Integer count = commissionRecordMapper.countByOrderIdAndInviterId(orderId, inviterId);
        return count != null && count > 0;
    }

    // 获取邀请者的佣金统计
    public InviterCommissionStats getInviterStats(Long inviterId, MonthKey monthKey) {
        String monthKeyStr = monthKey.toString();

        Integer orderCount = commissionRecordMapper
            .countByInviterIdAndMonthKey(inviterId, monthKeyStr);

        BigDecimal totalOrderAmount = commissionRecordMapper
            .sumOrderAmountByInviterIdAndMonthKey(inviterId, monthKeyStr);

        BigDecimal totalCommissionAmount = commissionRecordMapper
            .sumCommissionAmountByInviterIdAndMonthKey(inviterId, monthKeyStr);

        // 查询不同状态的记录数量
        List<CommissionRecordPO> allRecords = commissionRecordMapper
            .selectByInviterIdAndMonthKey(inviterId, monthKeyStr);

        Map<String, Long> statusCounts = allRecords.stream()
            .collect(Collectors.groupingBy(
                CommissionRecordPO::getStatus,
                Collectors.counting()
            ));

        return InviterCommissionStats.builder()
            .inviterId(inviterId)
            .monthKey(monthKey)
            .totalOrders(orderCount)
            .totalOrderAmount(totalOrderAmount)
            .totalCommissionAmount(totalCommissionAmount)
            .pendingCount(statusCounts.getOrDefault("PENDING", 0L))
            .confirmedCount(statusCounts.getOrDefault("CONFIRMED", 0L))
            .settledCount(statusCounts.getOrDefault("SETTLED", 0L))
            .build();
    }

    // 批量处理月度佣金
    @Transactional
    public BatchProcessResult batchProcessMonthlyCommissions(MonthKey monthKey) {
        BatchProcessResult result = new BatchProcessResult();

        // 查询所有待处理的记录
        List<CommissionRecordPO> pendingRecords = commissionRecordMapper
            .selectByStatusWithPage("PENDING", 1000, 0);

        // 按邀请者分组
        Map<Long, List<CommissionRecordPO>> groupedRecords = pendingRecords.stream()
            .collect(Collectors.groupingBy(CommissionRecordPO::getInviterId));

        for (Map.Entry<Long, List<CommissionRecordPO>> entry : groupedRecords.entrySet()) {
            Long inviterId = entry.getKey();
            List<Long> recordIds = entry.getValue().stream()
                .map(CommissionRecordPO::getId)
                .collect(Collectors.toList());

            try {
                // 批量更新状态
                int updatedCount = commissionRecordMapper.batchUpdateStatus(recordIds, "CONFIRMED");
                result.addSuccess(inviterId, updatedCount);

                log.info("批量处理佣金成功: inviterId={}, count={}", inviterId, updatedCount);

            } catch (Exception e) {
                log.error("批量处理佣金失败: inviterId={}", inviterId, e);
                result.addFailure(inviterId, e.getMessage());
            }
        }

        return result;
    }
}

// 3. 在Controller层中的使用示例
@RestController
@RequestMapping("/api/v1/commission/records")
public class CommissionRecordController {

    @Autowired
    private CommissionRecordMapper commissionRecordMapper;

    // 查询邀请者的佣金记录
    @GetMapping("/inviter/{inviterId}")
    public Result<PageResult<CommissionRecordVO>> getInviterRecords(
            @PathVariable Long inviterId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {

        int offset = (page - 1) * size;

        List<CommissionRecordPO> records = commissionRecordMapper
            .selectByInviterIdWithPage(inviterId, size, offset);

        Integer total = commissionRecordMapper.countByInviterId(inviterId);

        List<CommissionRecordVO> vos = records.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());

        PageResult<CommissionRecordVO> pageResult = new PageResult<>(vos, page, size, total);

        return Result.success(pageResult);
    }

    // 查询订单的佣金记录
    @GetMapping("/order/{orderId}")
    public Result<List<CommissionRecordVO>> getOrderRecords(@PathVariable Long orderId) {
        List<CommissionRecordPO> records = commissionRecordMapper.selectByOrderId(orderId);

        List<CommissionRecordVO> vos = records.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());

        return Result.success(vos);
    }

    // 管理员批量确认佣金
    @PostMapping("/batch-confirm")
    @PreAuthorize("hasAuthority('admin')")
    public Result<Integer> batchConfirmCommissions(@RequestBody List<Long> recordIds) {
        try {
            int updatedCount = commissionRecordMapper.batchUpdateStatus(recordIds, "CONFIRMED");
            return Result.success("批量确认成功", updatedCount);
        } catch (Exception e) {
            log.error("批量确认佣金失败", e);
            return Result.error("批量确认失败: " + e.getMessage());
        }
    }

    // 软删除佣金记录
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('admin')")
    public Result<String> deleteRecord(@PathVariable Long id) {
        try {
            int deletedCount = commissionRecordMapper.softDeleteById(id);
            if (deletedCount > 0) {
                return Result.success("删除成功");
            } else {
                return Result.error("记录不存在或已删除");
            }
        } catch (Exception e) {
            log.error("删除佣金记录失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }
}

// 4. MyBatis XML配置示例
/*
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.purchase.commission.infrastructure.mapper.CommissionRecordMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.purchase.commission.infrastructure.po.CommissionRecordPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="inviter_id" property="inviterId" jdbcType="BIGINT"/>
        <result column="invitee_id" property="inviteeId" jdbcType="BIGINT"/>
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="commission_rate" property="commissionRate" jdbcType="DECIMAL"/>
        <result column="commission_amount" property="commissionAmount" jdbcType="DECIMAL"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="month_key" property="monthKey" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 基础查询条件 -->
    <sql id="Base_Where_Clause">
        WHERE deleted = false
    </sql>

    <!-- 根据订单ID查询 -->
    <select id="selectByOrderId" resultMap="BaseResultMap">
        SELECT * FROM commission_record
        <include refid="Base_Where_Clause"/>
        AND order_id = #{orderId}
        ORDER BY created_at DESC
    </select>

    <!-- 根据邀请者ID和月份查询 -->
    <select id="selectByInviterIdAndMonthKey" resultMap="BaseResultMap">
        SELECT * FROM commission_record
        <include refid="Base_Where_Clause"/>
        AND inviter_id = #{inviterId}
        AND month_key = #{monthKey}
        ORDER BY created_at DESC
    </select>

    <!-- 分页查询邀请者记录 -->
    <select id="selectByInviterIdWithPage" resultMap="BaseResultMap">
        SELECT * FROM commission_record
        <include refid="Base_Where_Clause"/>
        AND inviter_id = #{inviterId}
        ORDER BY created_at DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 统计邀请者月度记录数量 -->
    <select id="countByInviterIdAndMonthKey" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM commission_record
        <include refid="Base_Where_Clause"/>
        AND inviter_id = #{inviterId}
        AND month_key = #{monthKey}
    </select>

    <!-- 计算邀请者月度佣金总额 -->
    <select id="sumCommissionAmountByInviterIdAndMonthKey" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(commission_amount), 0) FROM commission_record
        <include refid="Base_Where_Clause"/>
        AND inviter_id = #{inviterId}
        AND month_key = #{monthKey}
        AND status IN ('CONFIRMED', 'SETTLED')
    </select>

    <!-- 批量更新状态 -->
    <update id="batchUpdateStatus">
        UPDATE commission_record
        SET status = #{status}, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = false
    </update>

    <!-- 软删除 -->
    <update id="softDeleteById">
        UPDATE commission_record
        SET deleted = true, updated_at = NOW()
        WHERE id = #{id} AND deleted = false
    </update>

</mapper>
*/

// 5. 数据库索引优化建议
/*
-- 基础索引
CREATE INDEX idx_commission_record_order_id ON commission_record(order_id);
CREATE INDEX idx_commission_record_inviter_id ON commission_record(inviter_id);
CREATE INDEX idx_commission_record_invitee_id ON commission_record(invitee_id);
CREATE INDEX idx_commission_record_status ON commission_record(status);
CREATE INDEX idx_commission_record_month_key ON commission_record(month_key);

-- 复合索引
CREATE INDEX idx_commission_record_inviter_month ON commission_record(inviter_id, month_key);
CREATE INDEX idx_commission_record_inviter_status ON commission_record(inviter_id, status);
CREATE INDEX idx_commission_record_order_inviter ON commission_record(order_id, inviter_id);
CREATE INDEX idx_commission_record_status_month ON commission_record(status, month_key);

-- 覆盖索引（包含常用查询字段）
CREATE INDEX idx_commission_record_cover_inviter ON commission_record(inviter_id, month_key, status, commission_amount, order_amount);

-- 软删除过滤索引
CREATE INDEX idx_commission_record_not_deleted ON commission_record(deleted) WHERE deleted = false;
*/
```

## 注意事项 (Notes)
*   **软删除机制**: 所有查询都需要过滤deleted=false的记录，确保数据安全性
*   **索引优化**: 根据查询模式建立合适的单列和复合索引，提高查询性能
*   **分页性能**: 大数据量分页时建议使用游标分页或延迟关联优化性能
*   **批量操作**: 批量更新和删除时注意事务控制和性能影响
*   **数据一致性**: 统计查询时注意数据的一致性，避免脏读和幻读
*   **参数校验**: Mapper层虽然不做业务校验，但需要注意SQL注入防护
*   **异常处理**: 数据库操作异常需要在上层进行适当的处理和转换
*   **连接池管理**: 合理配置数据库连接池，避免连接泄露
*   **SQL优化**: 复杂查询需要分析执行计划，优化SQL性能
*   **事务边界**: Mapper方法本身不控制事务，事务边界在Service层管理
*   **缓存策略**: 对于频繁查询的数据可以考虑添加缓存层
*   **数据类型**: 金额字段使用BigDecimal类型，避免精度丢失
*   **时间字段**: 创建时间和更新时间字段需要正确设置默认值和更新触发器
*   **字段长度**: 字符串字段需要设置合适的长度限制
*   **外键约束**: 考虑是否需要外键约束来保证数据完整性
*   **分库分表**: 大数据量情况下需要考虑分库分表策略
