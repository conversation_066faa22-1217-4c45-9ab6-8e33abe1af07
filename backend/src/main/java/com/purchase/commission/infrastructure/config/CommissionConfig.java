package com.purchase.commission.infrastructure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.concurrent.Executor;

/**
 * 佣金模块Spring配置类
 * 
 * 配置佣金模块相关的Bean和功能特性，包括：
 * - 异步任务执行器
 * - 事务管理
 * - AOP代理
 */
@Configuration
@EnableAsync
@EnableTransactionManagement
@EnableAspectJAutoProxy
public class CommissionConfig {
    
    /**
     * 配置佣金模块专用的异步任务执行器
     * 用于处理订单完成事件等异步操作
     * 
     * 注意：使用独立的执行器名称以避免与全局taskExecutor冲突
     * 
     * @return 佣金模块任务执行器
     */
    @Bean("commissionTaskExecutor")
    public Executor commissionTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(2);
        
        // 最大线程数
        executor.setMaxPoolSize(5);
        
        // 队列容量
        executor.setQueueCapacity(100);
        
        // 线程名前缀
        executor.setThreadNamePrefix("Commission-Async-");
        
        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 设置等待时间
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }
} 