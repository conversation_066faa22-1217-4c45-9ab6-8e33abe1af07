# CommissionRecordRepositoryImpl

## 文件概述

该仓储实现类负责佣金记录的数据持久化操作，实现了领域层定义的`CommissionRecordRepository`接口。作为基础设施层的核心组件，它使用MyBatis-Plus作为ORM框架，处理实体对象与持久化对象之间的转换，确保数据的完整性和一致性。

### 设计思路
- 遵循DDD分层架构，实现领域层定义的仓储接口
- 使用MyBatis-Plus简化数据库操作，提高开发效率
- 实现实体对象与PO对象的双向转换
- 提供完整的CRUD操作和复杂查询功能
- 支持软删除机制，保证数据安全

### 架构位置
- 位于基础设施层（Infrastructure Layer）
- 实现领域层（Domain Layer）定义的仓储接口
- 与数据访问层（Mapper）协作完成数据操作
- 为应用层（Application Layer）提供数据持久化服务

## 核心功能

### 主要方法

#### 1. save(CommissionRecord commissionRecord)
- **功能**: 保存或更新佣金记录
- **逻辑**: 根据实体ID判断是新增还是更新操作
- **返回**: 保存后的佣金记录实体
- **事务**: 支持事务管理

#### 2. findById(Long id)
- **功能**: 根据ID查找佣金记录
- **返回**: Optional<CommissionRecord>
- **性能**: 使用主键查询，性能最优

#### 3. findByInviterIdAndMonthKey(Long inviterId, MonthKey monthKey)
- **功能**: 查找指定邀请者在指定月份的所有佣金记录
- **参数**: 
  - `inviterId`: 邀请者ID
  - `monthKey`: 月份键值对象
- **排序**: 按创建时间倒序
- **过滤**: 自动过滤已删除记录

#### 4. findByInviteeId(Long inviteeId)
- **功能**: 查找被邀请者相关的所有佣金记录
- **用途**: 用于买家查看自己产生的佣金记录

#### 5. existsByOrderIdAndInviterId(Long orderId, Long inviterId)
- **功能**: 检查指定订单和邀请者是否已存在佣金记录
- **用途**: 避免重复创建佣金记录
- **返回**: boolean值

#### 6. countByInviterIdAndMonthKey(Long inviterId, MonthKey monthKey)
- **功能**: 统计指定邀请者在指定月份的佣金记录数量
- **用途**: 用于月度汇总统计

#### 7. batchUpdateStatus(List<Long> ids, CommissionRecord.Status status)
- **功能**: 批量更新佣金记录状态
- **参数**:
  - `ids`: 佣金记录ID列表
  - `status`: 新状态
- **返回**: 更新的记录数量
- **性能**: 批量操作，提高效率

#### 8. softDeleteById(Long id)
- **功能**: 软删除佣金记录
- **实现**: 设置deleted字段为true，不物理删除
- **安全**: 保证数据可恢复性

### 对象转换方法

#### convertToPO(CommissionRecord entity)
- **功能**: 将领域实体转换为持久化对象
- **映射关系**:
  - 实体的`inviteeId` → PO的`inviteeId`
  - 实体的`monthKey` → PO的`monthKey`
  - 实体的`createdAt` → PO的`createdAt`
- **字段处理**: 正确处理值对象到基本类型的转换

#### convertToEntity(CommissionRecordPO po)
- **功能**: 将持久化对象转换为领域实体
- **创建方式**: 使用实体的静态工厂方法`create()`
- **后处理**: 设置ID和时间戳字段

## 使用示例

### 保存佣金记录
```java
@Autowired
private CommissionRecordRepository repository;

// 创建佣金记录
CommissionRecord record = CommissionRecord.create(
    inviterId, inviteeId, orderId, orderType, 
    orderAmount, commissionRate, monthKey
);

// 保存到数据库
CommissionRecord savedRecord = repository.save(record);
```

### 查询佣金记录
```java
// 根据ID查询
Optional<CommissionRecord> record = repository.findById(1L);

// 查询指定邀请者的月度佣金记录
List<CommissionRecord> monthlyRecords = repository
    .findByInviterIdAndMonthKey(inviterId, MonthKey.of("2024-01"));

// 分页查询邀请者记录
List<CommissionRecord> pageRecords = repository
    .findByInviterId(inviterId, 20, 0);
```

### 批量操作
```java
// 批量确认佣金记录
List<Long> ids = Arrays.asList(1L, 2L, 3L);
Integer updatedCount = repository.batchUpdateStatus(ids, Status.CONFIRMED);

// 批量软删除
Integer deletedCount = repository.batchSoftDelete(ids);
```

### 统计查询
```java
// 检查重复
boolean exists = repository.existsByOrderIdAndInviterId(orderId, inviterId);

// 统计数量
Integer count = repository.countByInviterIdAndMonthKey(inviterId, monthKey);

// 计算月度总额
Money totalAmount = repository.sumAmountByInviterIdAndMonth(inviterId, monthKey);
```

## 注意事项

### 性能考虑
- **索引优化**: 确保`inviter_id`、`month_key`、`order_id`等字段有适当索引
- **分页查询**: 使用LIMIT和OFFSET进行分页，避免全表扫描
- **批量操作**: 优先使用批量更新方法，减少数据库交互次数
- **软删除**: 查询时自动过滤deleted=true的记录

### 数据一致性
- **字段映射**: 确保实体字段与PO字段正确对应
- **空值处理**: 正确处理可选字段的null值
- **时区处理**: 注意LocalDateTime的时区一致性
- **精度保持**: BigDecimal字段保持精度不丢失

### 异常处理
- **数据库约束**: 处理唯一约束、外键约束等异常
- **并发冲突**: 处理乐观锁冲突异常
- **连接超时**: 处理数据库连接异常
- **事务回滚**: 确保异常时事务正确回滚

### 扩展性考虑
- **查询优化**: 根据业务需求添加新的查询方法
- **缓存集成**: 可考虑集成Redis缓存提高查询性能
- **读写分离**: 支持主从数据库的读写分离
- **分库分表**: 为大数据量场景预留分库分表接口

### 测试建议
- **单元测试**: 测试所有公共方法的正确性
- **集成测试**: 测试与数据库的实际交互
- **性能测试**: 测试大数据量下的查询性能
- **边界测试**: 测试极值和异常情况的处理 