package com.purchase.commission.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 邀请人优惠券持久化对象
 * 对应数据库表：inviter_coupon
 */
@TableName("inviter_coupon")
public class InviterCouponPO {
    
    /**
     * 优惠券ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 邀请人ID（买家）
     */
    @TableField("inviter_id")
    private Long inviterId;
    
    /**
     * 被邀请者ID（订单创建者）
     */
    @TableField("invitee_id")
    private Long inviteeId;
    
    /**
     * 触发订单ID
     */
    @TableField("trigger_order_id")
    private Long triggerOrderId;
    
    /**
     * 优惠券代码
     */
    @TableField("coupon_code")
    private String couponCode;
    
    /**
     * 优惠券类型（FIXED_AMOUNT, PERCENTAGE）
     */
    @TableField("coupon_type")
    private String couponType;
    
    /**
     * 优惠金额或折扣比例
     */
    @TableField("discount_value")
    private BigDecimal discountValue;
    
    /**
     * 最低使用金额
     */
    @TableField("minimum_amount")
    private BigDecimal minimumAmount;
    
    /**
     * 最大优惠金额（用于百分比折扣）
     */
    @TableField("maximum_discount")
    private BigDecimal maximumDiscount;
    
    /**
     * 状态（ACTIVE, USED, EXPIRED, CANCELLED）
     */
    @TableField("status")
    private String status;
    
    /**
     * 月份键（格式：YYYY-MM）
     */
    @TableField("month_key")
    private String monthKey;
    
    /**
     * 有效期开始时间
     */
    @TableField("valid_from")
    private LocalDateTime validFrom;
    
    /**
     * 有效期结束时间
     */
    @TableField("valid_until")
    private LocalDateTime validUntil;
    
    /**
     * 使用时间
     */
    @TableField("used_at")
    private LocalDateTime usedAt;
    
    /**
     * 使用订单ID
     */
    @TableField("used_order_id")
    private Long usedOrderId;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
    
    // 默认构造函数
    public InviterCouponPO() {
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getInviterId() {
        return inviterId;
    }
    
    public void setInviterId(Long inviterId) {
        this.inviterId = inviterId;
    }
    
    public Long getInviteeId() {
        return inviteeId;
    }
    
    public void setInviteeId(Long inviteeId) {
        this.inviteeId = inviteeId;
    }
    
    public Long getTriggerOrderId() {
        return triggerOrderId;
    }
    
    public void setTriggerOrderId(Long triggerOrderId) {
        this.triggerOrderId = triggerOrderId;
    }
    
    public String getCouponCode() {
        return couponCode;
    }
    
    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }
    
    public String getCouponType() {
        return couponType;
    }
    
    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }
    
    public BigDecimal getDiscountValue() {
        return discountValue;
    }
    
    public void setDiscountValue(BigDecimal discountValue) {
        this.discountValue = discountValue;
    }
    
    public BigDecimal getMinimumAmount() {
        return minimumAmount;
    }
    
    public void setMinimumAmount(BigDecimal minimumAmount) {
        this.minimumAmount = minimumAmount;
    }
    
    public BigDecimal getMaximumDiscount() {
        return maximumDiscount;
    }
    
    public void setMaximumDiscount(BigDecimal maximumDiscount) {
        this.maximumDiscount = maximumDiscount;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getMonthKey() {
        return monthKey;
    }
    
    public void setMonthKey(String monthKey) {
        this.monthKey = monthKey;
    }
    
    public LocalDateTime getValidFrom() {
        return validFrom;
    }
    
    public void setValidFrom(LocalDateTime validFrom) {
        this.validFrom = validFrom;
    }
    
    public LocalDateTime getValidUntil() {
        return validUntil;
    }
    
    public void setValidUntil(LocalDateTime validUntil) {
        this.validUntil = validUntil;
    }
    
    public LocalDateTime getUsedAt() {
        return usedAt;
    }
    
    public void setUsedAt(LocalDateTime usedAt) {
        this.usedAt = usedAt;
    }
    
    public Long getUsedOrderId() {
        return usedOrderId;
    }
    
    public void setUsedOrderId(Long usedOrderId) {
        this.usedOrderId = usedOrderId;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "InviterCouponPO{" +
                "id=" + id +
                ", inviterId=" + inviterId +
                ", inviteeId=" + inviteeId +
                ", triggerOrderId=" + triggerOrderId +
                ", couponCode='" + couponCode + '\'' +
                ", couponType='" + couponType + '\'' +
                ", discountValue=" + discountValue +
                ", minimumAmount=" + minimumAmount +
                ", maximumDiscount=" + maximumDiscount +
                ", status='" + status + '\'' +
                ", monthKey='" + monthKey + '\'' +
                ", validFrom=" + validFrom +
                ", validUntil=" + validUntil +
                ", usedAt=" + usedAt +
                ", usedOrderId=" + usedOrderId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
