package com.purchase.commission.infrastructure.repository;

import com.purchase.commission.domain.entity.CommissionConfig;
import com.purchase.commission.domain.repository.CommissionConfigRepository;
import com.purchase.commission.domain.valueobject.CommissionRate;
import com.purchase.commission.domain.valueobject.Money;
import com.purchase.commission.infrastructure.mapper.CommissionConfigMapper;
import com.purchase.commission.infrastructure.po.CommissionConfigPO;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 佣金配置仓储实现
 */
@Repository
public class CommissionConfigRepositoryImpl implements CommissionConfigRepository {
    
    private final CommissionConfigMapper commissionConfigMapper;
    
    public CommissionConfigRepositoryImpl(CommissionConfigMapper commissionConfigMapper) {
        this.commissionConfigMapper = commissionConfigMapper;
    }
    
    @Override
    public CommissionConfig save(CommissionConfig commissionConfig) {
        CommissionConfigPO po = toCommissionConfigPO(commissionConfig);
        
        if (commissionConfig.getId() == null) {
            // 新增
            po.setCreatedAt(LocalDateTime.now());
            po.setUpdatedAt(LocalDateTime.now());
            commissionConfigMapper.insert(po);
            commissionConfig.setId(po.getId());
        } else {
            // 更新
            po.setUpdatedAt(LocalDateTime.now());
            commissionConfigMapper.update(po);
        }
        
        return commissionConfig;
    }
    
    @Override
    public Optional<CommissionConfig> findById(Long id) {
        CommissionConfigPO po = commissionConfigMapper.selectById(id);
        return po != null ? Optional.of(toCommissionConfig(po)) : Optional.empty();
    }
    
    @Override
    public List<CommissionConfig> findEnabledCommissionRates() {
        List<CommissionConfigPO> pos = commissionConfigMapper.selectEnabledCommissionRates();
        return pos.stream()
                .map(this::toCommissionConfig)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CommissionConfig> findEnabledMonthlyBonuses() {
        List<CommissionConfigPO> pos = commissionConfigMapper.selectEnabledMonthlyBonuses();
        return pos.stream()
                .map(this::toCommissionConfig)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CommissionConfig> findEnabledByConfigType(CommissionConfig.ConfigType configType) {
        List<CommissionConfigPO> pos = commissionConfigMapper.selectEnabledByConfigType(configType.getCode());
        return pos.stream()
                .map(this::toCommissionConfig)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CommissionConfig> findAll() {
        List<CommissionConfigPO> pos = commissionConfigMapper.selectAll();
        return pos.stream()
                .map(this::toCommissionConfig)
                .collect(Collectors.toList());
    }
    
    @Override
    public void deleteById(Long id) {
        commissionConfigMapper.deleteById(id);
    }
    
    @Override
    public boolean existsById(Long id) {
        return commissionConfigMapper.existsById(id) > 0;
    }
    
    /**
     * 将领域实体转换为持久化对象
     */
    private CommissionConfigPO toCommissionConfigPO(CommissionConfig commissionConfig) {
        CommissionConfigPO po = new CommissionConfigPO();
        po.setId(commissionConfig.getId());
        po.setConfigType(commissionConfig.getConfigType() != null ? commissionConfig.getConfigType().getCode() : null);
        po.setMinAmount(commissionConfig.getMinAmount() != null ? commissionConfig.getMinAmount().getAmount() : null);
        po.setMaxAmount(commissionConfig.getMaxAmount() != null ? commissionConfig.getMaxAmount().getAmount() : null);
        po.setRateValue(commissionConfig.getRateValue() != null ? commissionConfig.getRateValue().getRate() : null);
        po.setBonusAmount(commissionConfig.getBonusAmount() != null ? commissionConfig.getBonusAmount().getAmount() : null);
        po.setDescription(commissionConfig.getDescription());
        po.setStatus(commissionConfig.getStatus() != null ? commissionConfig.getStatus().getCode() : null);
        po.setSortOrder(commissionConfig.getSortOrder());
        po.setCreatedAt(commissionConfig.getCreatedAt());
        po.setUpdatedAt(commissionConfig.getUpdatedAt());
        return po;
    }
    
    /**
     * 将持久化对象转换为领域实体
     */
    private CommissionConfig toCommissionConfig(CommissionConfigPO po) {
        CommissionConfig config = new CommissionConfig();
        config.setId(po.getId());
        config.setConfigType(po.getConfigType() != null ? CommissionConfig.ConfigType.fromCode(po.getConfigType()) : null);
        config.setMinAmount(po.getMinAmount() != null ? Money.of(po.getMinAmount()) : null);
        config.setMaxAmount(po.getMaxAmount() != null ? Money.of(po.getMaxAmount()) : null);
        config.setRateValue(po.getRateValue() != null ? CommissionRate.of(po.getRateValue()) : null);
        config.setBonusAmount(po.getBonusAmount() != null ? Money.of(po.getBonusAmount()) : null);
        config.setDescription(po.getDescription());
        config.setStatus(po.getStatus() != null ? CommissionConfig.Status.fromCode(po.getStatus()) : null);
        config.setSortOrder(po.getSortOrder());
        config.setCreatedAt(po.getCreatedAt());
        config.setUpdatedAt(po.getUpdatedAt());
        return config;
    }
}
