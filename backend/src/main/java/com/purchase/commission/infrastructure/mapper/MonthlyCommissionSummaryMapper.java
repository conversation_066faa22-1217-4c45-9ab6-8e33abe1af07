package com.purchase.commission.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.purchase.commission.infrastructure.po.MonthlyCommissionSummaryPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 月度佣金汇总Mapper接口
 * 
 * 基于MyBatis-Plus的BaseMapper，提供基础的CRUD操作。
 * 复杂查询可以通过XML配置或注解实现。
 */
@Mapper
public interface MonthlyCommissionSummaryMapper extends BaseMapper<MonthlyCommissionSummaryPO> {
    
    // 继承BaseMapper已提供基本的CRUD操作
    // 如需自定义查询，可在此添加方法声明
} 