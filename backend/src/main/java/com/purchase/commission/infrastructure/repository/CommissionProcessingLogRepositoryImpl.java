package com.purchase.commission.infrastructure.repository;

import com.purchase.commission.domain.entity.CommissionProcessingLog;
import com.purchase.commission.domain.repository.CommissionProcessingLogRepository;
import com.purchase.commission.domain.valueobject.MonthKey;
import com.purchase.commission.infrastructure.mapper.CommissionProcessingLogMapper;
import com.purchase.commission.infrastructure.po.CommissionProcessingLogPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 佣金处理日志仓储实现
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class CommissionProcessingLogRepositoryImpl implements CommissionProcessingLogRepository {
    
    private final CommissionProcessingLogMapper mapper;
    
    @Override
    public CommissionProcessingLog save(CommissionProcessingLog log) {
        CommissionProcessingLogPO po = convertToPO(log);
        
        if (po.getId() == null) {
            // 新增
            mapper.insert(po);
            log.setId(po.getId());
        } else {
            // 更新
            mapper.updateById(po);
        }
        
        return log;
    }
    
    @Override
    public Optional<CommissionProcessingLog> findById(Long id) {
        CommissionProcessingLogPO po = mapper.selectById(id);
        return po != null ? Optional.of(convertToEntity(po)) : Optional.empty();
    }
    
    @Override
    public Optional<CommissionProcessingLog> findByMonthKey(MonthKey monthKey) {
        CommissionProcessingLogPO po = mapper.findByMonthKey(monthKey.toString());
        return po != null ? Optional.of(convertToEntity(po)) : Optional.empty();
    }
    
    @Override
    public boolean existsByMonthKey(MonthKey monthKey) {
        return mapper.existsByMonthKey(monthKey.toString());
    }
    
    @Override
    public boolean existsByMonthKeyAndStatusIn(MonthKey monthKey, List<CommissionProcessingLog.Status> statuses) {
        String statusStr = statuses.stream()
            .map(status -> "'" + status.name() + "'")
            .collect(Collectors.joining(","));
        return mapper.existsByMonthKeyAndStatusIn(monthKey.toString(), statusStr);
    }
    
    @Override
    public List<CommissionProcessingLog> findRecentLogs(int limit) {
        List<CommissionProcessingLogPO> pos = mapper.findRecentLogs(limit);
        return pos.stream()
            .map(this::convertToEntity)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<CommissionProcessingLog> findByStatus(CommissionProcessingLog.Status status) {
        List<CommissionProcessingLogPO> pos = mapper.findByStatus(status.name());
        return pos.stream()
            .map(this::convertToEntity)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<CommissionProcessingLog> findByMonthKeyBetween(MonthKey startMonth, MonthKey endMonth) {
        List<CommissionProcessingLogPO> pos = mapper.findByMonthKeyBetween(
            startMonth.toString(), endMonth.toString());
        return pos.stream()
            .map(this::convertToEntity)
            .collect(Collectors.toList());
    }
    
    @Override
    public void deleteById(Long id) {
        mapper.deleteById(id);
    }
    
    @Override
    public long count() {
        return mapper.selectCount(null);
    }
    
    @Override
    public long countByStatus(CommissionProcessingLog.Status status) {
        return mapper.countByStatus(status.name());
    }
    
    /**
     * 将实体转换为持久化对象
     */
    private CommissionProcessingLogPO convertToPO(CommissionProcessingLog entity) {
        CommissionProcessingLogPO po = new CommissionProcessingLogPO();
        po.setId(entity.getId());
        po.setMonthKey(entity.getMonthKey().toString());
        po.setProcessingDate(entity.getProcessingDate());
        po.setTotalRecords(entity.getTotalRecords());
        po.setSuccessCount(entity.getSuccessCount());
        po.setFailureCount(entity.getFailureCount());
        po.setStatus(entity.getStatus().name());
        po.setErrorMessage(entity.getErrorMessage());
        po.setStartedAt(entity.getStartedAt());
        po.setCompletedAt(entity.getCompletedAt());
        po.setCreatedAt(entity.getCreatedAt());
        po.setUpdatedAt(entity.getUpdatedAt());
        return po;
    }
    
    /**
     * 将持久化对象转换为实体
     */
    private CommissionProcessingLog convertToEntity(CommissionProcessingLogPO po) {
        CommissionProcessingLog entity = new CommissionProcessingLog();
        entity.setId(po.getId());
        entity.setMonthKey(MonthKey.of(po.getMonthKey()));
        entity.setProcessingDate(po.getProcessingDate());
        entity.setTotalRecords(po.getTotalRecords());
        entity.setSuccessCount(po.getSuccessCount());
        entity.setFailureCount(po.getFailureCount());
        entity.setStatus(CommissionProcessingLog.Status.valueOf(po.getStatus()));
        entity.setErrorMessage(po.getErrorMessage());
        entity.setStartedAt(po.getStartedAt());
        entity.setCompletedAt(po.getCompletedAt());
        entity.setCreatedAt(po.getCreatedAt());
        entity.setUpdatedAt(po.getUpdatedAt());
        return entity;
    }
}
