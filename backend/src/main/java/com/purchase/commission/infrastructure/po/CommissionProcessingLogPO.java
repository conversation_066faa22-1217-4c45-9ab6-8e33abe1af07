package com.purchase.commission.infrastructure.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 佣金处理日志持久化对象
 */
@Data
@TableName("commission_processing_log")
public class CommissionProcessingLogPO {
    
    /**
     * 处理日志ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 处理月份
     */
    @TableField("month_key")
    private String monthKey;
    
    /**
     * 处理日期
     */
    @TableField("processing_date")
    private LocalDate processingDate;
    
    /**
     * 总记录数
     */
    @TableField("total_records")
    private Integer totalRecords;
    
    /**
     * 成功处理数
     */
    @TableField("success_count")
    private Integer successCount;
    
    /**
     * 失败处理数
     */
    @TableField("failure_count")
    private Integer failureCount;
    
    /**
     * 处理状态
     */
    @TableField("status")
    private String status;
    
    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;
    
    /**
     * 开始时间
     */
    @TableField("started_at")
    private LocalDateTime startedAt;
    
    /**
     * 完成时间
     */
    @TableField("completed_at")
    private LocalDateTime completedAt;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
