package com.purchase.commission.infrastructure.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 月度佣金汇总持久化对象
 * 对应数据库表：monthly_commission_summary
 */
@TableName("monthly_commission_summary")
@Data
public class MonthlyCommissionSummaryPO {
    
    /**
     * 汇总ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 邀请者ID
     */
    @TableField("inviter_id")
    private Long inviterId;
    
    /**
     * 月份键（YYYY-MM格式）
     */
    @TableField("month_key")
    private String monthKey;
    
    /**
     * 订单数量
     */
    @TableField("order_count")
    private Integer orderCount;
    
    /**
     * 订单总金额
     */
    @TableField("total_order_amount")
    private BigDecimal totalOrderAmount;
    
    /**
     * 月度适用佣金比例
     */
    @TableField("commission_rate")
    private BigDecimal commissionRate;
    
    /**
     * 佣金金额
     */
    @TableField("commission_amount")
    private BigDecimal commissionAmount;
    
    /**
     * 奖金金额
     */
    @TableField("bonus_amount")
    private BigDecimal bonusAmount;
    
    /**
     * 总金额（佣金+奖金）
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;
    
    /**
     * 结算状态
     */
    @TableField("status")
    private String status;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 最后计算时间
     */
    @TableField("last_calculated_at")
    private LocalDateTime lastCalculatedAt;

    /**
     * 结算时间
     */
    @TableField("settled_at")
    private LocalDateTime settledAt;
} 