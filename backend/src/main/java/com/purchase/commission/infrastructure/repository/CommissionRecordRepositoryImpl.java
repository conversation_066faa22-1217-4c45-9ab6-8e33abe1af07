package com.purchase.commission.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.purchase.commission.domain.entity.CommissionRecord;
import com.purchase.commission.domain.repository.CommissionRecordRepository;
import com.purchase.commission.domain.valueobject.CommissionRate;
import com.purchase.commission.domain.valueobject.Money;
import com.purchase.commission.domain.valueobject.MonthKey;
import com.purchase.commission.infrastructure.mapper.CommissionRecordMapper;
import com.purchase.commission.infrastructure.po.CommissionRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 佣金记录仓储实现类
 * 
 * 负责佣金记录的数据持久化操作，实现领域层定义的仓储接口。
 * 使用MyBatis-Plus作为ORM框架，处理实体对象与持久化对象之间的转换。
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class CommissionRecordRepositoryImpl implements CommissionRecordRepository {

    private final CommissionRecordMapper mapper;

    @Override
    public CommissionRecord save(CommissionRecord commissionRecord) {
        log.info("保存佣金记录: 邀请者ID={}, 订单ID={}", 
                commissionRecord.getInviterId(), commissionRecord.getOrderId());
        
        CommissionRecordPO po;
        if (commissionRecord.getId() == null) {
            // 新增
            po = convertToPO(commissionRecord);
            mapper.insert(po);
            // 回填ID到领域实体
            commissionRecord.setId(po.getId());
        } else {
            // 更新
            // 1. 先从数据库加载旧的PO
            po = mapper.selectById(commissionRecord.getId());
            if (po == null) {
                // 如果记录不存在，可以根据业务需求选择抛出异常或转为新增
                // 这里选择抛出异常，因为更新一个不存在的记录通常是错误的
                throw new IllegalStateException("尝试更新的佣金记录不存在, ID: " + commissionRecord.getId());
            }
            // 2. 将领域实体的变更同步到PO上
            updatePOFromEntity(po, commissionRecord);
            // 3. 执行更新，MyBatis-Plus会自动填充updated_at字段
            mapper.updateById(po);
        }
        
        log.info("佣金记录保存成功: ID={}", commissionRecord.getId());
        return commissionRecord;
    }

    @Override
    public Optional<CommissionRecord> findById(Long id) {
        log.debug("查找佣金记录: ID={}", id);
        
        CommissionRecordPO po = mapper.selectById(id);
        if (po == null) {
            log.debug("佣金记录不存在: ID={}", id);
            return Optional.empty();
        }

        return Optional.of(convertToEntity(po));
    }

    @Override
    public List<CommissionRecord> findByOrderId(Long orderId) {
        log.debug("查找订单的佣金记录: 订单ID={}", orderId);
        
        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getOrderId, orderId)
               .eq(CommissionRecordPO::getDeleted, false)
               .orderByDesc(CommissionRecordPO::getCreatedAt);

        List<CommissionRecordPO> poList = mapper.selectList(wrapper);
        return poList.stream()
                     .map(this::convertToEntity)
                     .collect(Collectors.toList());
    }

    @Override
    public List<CommissionRecord> findByInviterIdAndMonthKey(Long inviterId, MonthKey monthKey) {
        log.debug("查找指定邀请者和月份的佣金记录: 邀请者ID={}, 月份={}", inviterId, monthKey.getValue());

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getInviterId, inviterId)
               .eq(CommissionRecordPO::getMonthKey, monthKey.getValue())
               .eq(CommissionRecordPO::getDeleted, false)
               .orderByDesc(CommissionRecordPO::getCreatedAt);

        List<CommissionRecordPO> poList = mapper.selectList(wrapper);
        return poList.stream()
                     .map(this::convertToEntity)
                     .collect(Collectors.toList());
    }

    @Override
    public List<CommissionRecord> findByInviteeId(Long inviteeId) {
        log.debug("查找被邀请者的佣金记录: 被邀请者ID={}", inviteeId);

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getInviteeId, inviteeId)
               .eq(CommissionRecordPO::getDeleted, false)
               .orderByDesc(CommissionRecordPO::getCreatedAt);

        List<CommissionRecordPO> poList = mapper.selectList(wrapper);
        return poList.stream()
                     .map(this::convertToEntity)
                     .collect(Collectors.toList());
    }

    @Override
    public List<CommissionRecord> findByInviterId(Long inviterId, Integer limit, Integer offset) {
        log.debug("分页查找邀请者的佣金记录: 邀请者ID={}, 限制={}, 偏移={}", inviterId, limit, offset);

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getInviterId, inviterId)
               .eq(CommissionRecordPO::getDeleted, false)
               .orderByDesc(CommissionRecordPO::getCreatedAt)
               .last("LIMIT " + limit + " OFFSET " + offset);

        List<CommissionRecordPO> poList = mapper.selectList(wrapper);
        return poList.stream()
                     .map(this::convertToEntity)
                     .collect(Collectors.toList());
    }

    @Override
    public List<CommissionRecord> findByStatus(CommissionRecord.Status status, Integer limit, Integer offset) {
        log.debug("分页查找指定状态的佣金记录: 状态={}, 限制={}, 偏移={}", status, limit, offset);

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getStatus, status.name())
               .eq(CommissionRecordPO::getDeleted, false)
               .orderByDesc(CommissionRecordPO::getCreatedAt)
               .last("LIMIT " + limit + " OFFSET " + offset);

        List<CommissionRecordPO> poList = mapper.selectList(wrapper);
        return poList.stream()
                     .map(this::convertToEntity)
                     .collect(Collectors.toList());
    }

    @Override
    public List<CommissionRecord> findByInviterIdAndStatus(Long inviterId, CommissionRecord.Status status) {
        log.debug("查找指定邀请者和状态的佣金记录: 邀请者ID={}, 状态={}", inviterId, status);

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getInviterId, inviterId)
               .eq(CommissionRecordPO::getStatus, status.name())
               .eq(CommissionRecordPO::getDeleted, false)
               .orderByDesc(CommissionRecordPO::getCreatedAt);

        List<CommissionRecordPO> poList = mapper.selectList(wrapper);
        return poList.stream()
                     .map(this::convertToEntity)
                     .collect(Collectors.toList());
    }

    @Override
    public List<CommissionRecord> findByMonthKey(MonthKey monthKey, Integer limit, Integer offset) {
        log.debug("分页查找指定月份的佣金记录: 月份={}, 限制={}, 偏移={}", monthKey.getValue(), limit, offset);

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getMonthKey, monthKey.getValue())
               .eq(CommissionRecordPO::getDeleted, false)
               .orderByDesc(CommissionRecordPO::getCreatedAt)
               .last("LIMIT " + limit + " OFFSET " + offset);

        List<CommissionRecordPO> poList = mapper.selectList(wrapper);
        return poList.stream()
                     .map(this::convertToEntity)
                     .collect(Collectors.toList());
    }

    @Override
    public List<CommissionRecord> findSettlableRecords(Long inviterId, MonthKey monthKey) {
        log.debug("查找可结算的佣金记录: 邀请者ID={}, 月份={}", inviterId, monthKey.getValue());

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getInviterId, inviterId)
               .eq(CommissionRecordPO::getMonthKey, monthKey.getValue())
               .eq(CommissionRecordPO::getStatus, CommissionRecord.Status.CONFIRMED.name())
               .eq(CommissionRecordPO::getDeleted, false)
               .orderByDesc(CommissionRecordPO::getCreatedAt);

        List<CommissionRecordPO> poList = mapper.selectList(wrapper);
        return poList.stream()
                     .map(this::convertToEntity)
                     .collect(Collectors.toList());
    }

    @Override
    public boolean existsByOrderIdAndInviterId(Long orderId, Long inviterId) {
        log.debug("检查订单是否已存在佣金记录: 订单ID={}, 邀请者ID={}", orderId, inviterId);

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getOrderId, orderId)
               .eq(CommissionRecordPO::getInviterId, inviterId)
               .eq(CommissionRecordPO::getDeleted, false);

        return mapper.selectCount(wrapper) > 0;
    }

    @Override
    public Integer countByInviterIdAndMonthKey(Long inviterId, MonthKey monthKey) {
        log.debug("统计指定邀请者和月份的佣金记录数量: 邀请者ID={}, 月份={}", inviterId, monthKey.getValue());

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getInviterId, inviterId)
               .eq(CommissionRecordPO::getMonthKey, monthKey.getValue())
               .eq(CommissionRecordPO::getDeleted, false);

        return Math.toIntExact(mapper.selectCount(wrapper));
    }

    @Override
    public Integer countByInviterId(Long inviterId) {
        log.debug("统计邀请者的总佣金记录数量: 邀请者ID={}", inviterId);

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getInviterId, inviterId)
               .eq(CommissionRecordPO::getDeleted, false);

        return Math.toIntExact(mapper.selectCount(wrapper));
    }

    @Override
    public Integer batchUpdateStatus(List<Long> ids, CommissionRecord.Status status) {
        log.info("批量更新佣金记录状态: IDs={}, 状态={}", ids, status);
        
        LambdaUpdateWrapper<CommissionRecordPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CommissionRecordPO::getId, ids)
               .set(CommissionRecordPO::getStatus, status.name());
        
        return mapper.update(null, wrapper);
    }

    @Override
    public void deleteById(Long id) {
        log.info("删除佣金记录: ID={}", id);
        mapper.deleteById(id);
    }

    @Override
    public void softDeleteById(Long id) {
        log.info("软删除佣金记录: ID={}", id);
        
        LambdaUpdateWrapper<CommissionRecordPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CommissionRecordPO::getId, id)
               .set(CommissionRecordPO::getDeleted, true)
               .set(CommissionRecordPO::getUpdatedAt, LocalDateTime.now());
        
        mapper.update(null, wrapper);
    }

    @Override
    public Integer batchSoftDelete(List<Long> ids) {
        log.info("批量软删除佣金记录: IDs={}", ids);
        
        LambdaUpdateWrapper<CommissionRecordPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CommissionRecordPO::getId, ids)
               .set(CommissionRecordPO::getDeleted, true)
               .set(CommissionRecordPO::getUpdatedAt, LocalDateTime.now());
        
        return mapper.update(null, wrapper);
    }

    /**
     * 将实体对象转换为持久化对象
     */
    private CommissionRecordPO convertToPO(CommissionRecord entity) {
        CommissionRecordPO po = new CommissionRecordPO();
        po.setId(entity.getId());
        po.setInviterId(entity.getInviterId());
        po.setInviteeId(entity.getInviteeId());
        po.setOrderId(entity.getOrderId());
        po.setOrderType(entity.getOrderType().name());
        po.setOrderAmount(entity.getOrderAmount().getAmount());
        po.setCommissionRate(entity.getCommissionRate().getRate());
        po.setCommissionAmount(entity.getCommissionAmount().getAmount());
        po.setStatus(entity.getStatus().name());
        po.setMonthKey(entity.getMonthKey().getValue());
        po.setBuyerRoleVerified(entity.getBuyerRoleVerified());
        po.setDeleted(entity.getDeleted());
        return po;
    }

    /**
     * 将持久化对象转换为实体对象
     */
    private CommissionRecord convertToEntity(CommissionRecordPO po) {
        CommissionRecord record = CommissionRecord.create(
                po.getInviterId(),
                po.getInviteeId(),
                po.getOrderId(),
                CommissionRecord.OrderType.valueOf(po.getOrderType()),
                Money.of(po.getOrderAmount()),
                CommissionRate.of(po.getCommissionRate()),
                MonthKey.of(po.getMonthKey())
        );
        
        // 设置ID和时间戳
        record.setId(po.getId());
        record.setCreatedAt(po.getCreatedAt());
        record.setUpdatedAt(po.getUpdatedAt());
        
        return record;
    }

    // =====================================================
    // 月初统一处理相关方法实现
    // =====================================================

    @Override
    public List<CommissionRecord> findByMonthKeyAndStatusAndDeleted(MonthKey monthKey, CommissionRecord.Status status, Boolean deleted) {
        log.debug("根据月份、状态和删除标志查找佣金记录: 月份={}, 状态={}, 删除={}", monthKey, status, deleted);

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getMonthKey, monthKey.getValue())
               .eq(CommissionRecordPO::getStatus, status.name())
               .eq(CommissionRecordPO::getDeleted, deleted)
               .orderByDesc(CommissionRecordPO::getCreatedAt);

        List<CommissionRecordPO> poList = mapper.selectList(wrapper);
        return poList.stream()
                     .map(this::convertToEntity)
                     .collect(Collectors.toList());
    }

    @Override
    public List<CommissionRecord> findByInviterIdAndMonthKeyAndStatusAndDeleted(Long inviterId, MonthKey monthKey, CommissionRecord.Status status, Boolean deleted) {
        log.debug("根据邀请者ID、月份、状态和删除标志查找佣金记录: 邀请者ID={}, 月份={}, 状态={}, 删除={}", inviterId, monthKey, status, deleted);

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getInviterId, inviterId)
               .eq(CommissionRecordPO::getMonthKey, monthKey.getValue())
               .eq(CommissionRecordPO::getStatus, status.name())
               .eq(CommissionRecordPO::getDeleted, deleted)
               .orderByDesc(CommissionRecordPO::getCreatedAt);

        List<CommissionRecordPO> poList = mapper.selectList(wrapper);
        return poList.stream()
                     .map(this::convertToEntity)
                     .collect(Collectors.toList());
    }

    @Override
    public Integer batchUpdateStatusToConfirmed(List<Long> ids, java.time.LocalDateTime confirmedAt) {
        log.info("批量更新记录状态为CONFIRMED: IDs={}, 确认时间={}", ids, confirmedAt);

        if (ids == null || ids.isEmpty()) {
            return 0;
        }

        LambdaUpdateWrapper<CommissionRecordPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CommissionRecordPO::getId, ids)
               .set(CommissionRecordPO::getStatus, CommissionRecord.Status.CONFIRMED.name())
               .set(CommissionRecordPO::getConfirmedAt, confirmedAt)
               .set(CommissionRecordPO::getProcessedAt, confirmedAt);

        int updatedCount = mapper.update(null, wrapper);
        log.info("批量更新完成: 期望更新={}, 实际更新={}", ids.size(), updatedCount);

        return updatedCount;
    }

    @Override
    public List<Long> findDistinctInviterIdsByMonthKeyAndStatusAndDeleted(MonthKey monthKey, CommissionRecord.Status status, Boolean deleted) {
        log.debug("查找指定月份中不同邀请者的ID列表: 月份={}, 状态={}, 删除={}", monthKey, status, deleted);

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(CommissionRecordPO::getInviterId)
               .eq(CommissionRecordPO::getMonthKey, monthKey.getValue())
               .eq(CommissionRecordPO::getStatus, status.name())
               .eq(CommissionRecordPO::getDeleted, deleted)
               .groupBy(CommissionRecordPO::getInviterId);

        List<CommissionRecordPO> poList = mapper.selectList(wrapper);
        return poList.stream()
                     .map(CommissionRecordPO::getInviterId)
                     .distinct()
                     .collect(Collectors.toList());
    }

    @Override
    public Integer countByMonthKeyAndStatusAndDeleted(MonthKey monthKey, CommissionRecord.Status status, Boolean deleted) {
        log.debug("统计指定月份和状态的记录数量: 月份={}, 状态={}, 删除={}", monthKey, status, deleted);

        LambdaQueryWrapper<CommissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecordPO::getMonthKey, monthKey.getValue())
               .eq(CommissionRecordPO::getStatus, status.name())
               .eq(CommissionRecordPO::getDeleted, deleted);

        return Math.toIntExact(mapper.selectCount(wrapper));
    }

    /**
     * 将实体对象转换为持久化对象
     */
    private void updatePOFromEntity(CommissionRecordPO po, CommissionRecord entity) {
        // 只更新可变的字段
        po.setStatus(entity.getStatus().name());
        po.setOrderAmount(entity.getOrderAmount().getAmount());
        po.setCommissionRate(entity.getCommissionRate().getRate());
        po.setCommissionAmount(entity.getCommissionAmount().getAmount());
        po.setBuyerRoleVerified(entity.getBuyerRoleVerified());
        po.setDeleted(entity.getDeleted());
    }
}