package com.purchase.commission.infrastructure.mapper;

import com.purchase.commission.infrastructure.po.CommissionRecordPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 佣金记录 Mapper接口
 * 基于MyBatis-Plus的数据访问层
 */
@Mapper
public interface CommissionRecordMapper extends BaseMapper<CommissionRecordPO> {
    
    /**
     * 根据订单ID查找佣金记录
     * 
     * @param orderId 订单ID
     * @return 佣金记录列表
     */
    List<CommissionRecordPO> selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据邀请者ID和月份查找佣金记录
     *
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 佣金记录列表
     */
    List<CommissionRecordPO> selectByInviterIdAndMonthKey(@Param("inviterId") Long inviterId,
                                                           @Param("monthKey") String monthKey);

    /**
     * 根据被邀请者ID查找佣金记录
     *
     * @param inviteeId 被邀请者ID
     * @return 佣金记录列表
     */
    List<CommissionRecordPO> selectByInviteeId(@Param("inviteeId") Long inviteeId);

    /**
     * 根据邀请者ID分页查找佣金记录
     *
     * @param inviterId 邀请者ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 佣金记录列表
     */
    List<CommissionRecordPO> selectByInviterIdWithPage(@Param("inviterId") Long inviterId,
                                                        @Param("limit") Integer limit,
                                                        @Param("offset") Integer offset);

    /**
     * 根据状态分页查找佣金记录
     *
     * @param status 状态
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 佣金记录列表
     */
    List<CommissionRecordPO> selectByStatusWithPage(@Param("status") String status,
                                                     @Param("limit") Integer limit,
                                                     @Param("offset") Integer offset);

    /**
     * 根据邀请者ID和状态查找佣金记录
     *
     * @param inviterId 邀请者ID
     * @param status 状态
     * @return 佣金记录列表
     */
    List<CommissionRecordPO> selectByInviterIdAndStatus(@Param("inviterId") Long inviterId,
                                                         @Param("status") String status);

    /**
     * 根据月份分页查找佣金记录
     *
     * @param monthKey 月份键
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 佣金记录列表
     */
    List<CommissionRecordPO> selectByMonthKeyWithPage(@Param("monthKey") String monthKey,
                                                       @Param("limit") Integer limit,
                                                       @Param("offset") Integer offset);

    /**
     * 查找可结算的佣金记录
     *
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 可结算的佣金记录列表
     */
    List<CommissionRecordPO> selectSettlableRecords(@Param("inviterId") Long inviterId,
                                                     @Param("monthKey") String monthKey);
    
    /**
     * 检查订单是否已存在佣金记录
     * 
     * @param orderId 订单ID
     * @param inviterId 邀请者ID
     * @return 记录数量
     */
    Integer countByOrderIdAndInviterId(@Param("orderId") Long orderId,
                                     @Param("inviterId") Long inviterId);
    
    /**
     * 统计邀请者在指定月份的佣金记录数量
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 记录数量
     */
    Integer countByInviterIdAndMonthKey(@Param("inviterId") Long inviterId,
                                      @Param("monthKey") String monthKey);
    
    /**
     * 统计邀请者的总佣金记录数量
     * 
     * @param inviterId 邀请者ID
     * @return 记录数量
     */
    Integer countByInviterId(@Param("inviterId") Long inviterId);
    
    /**
     * 计算邀请者指定月份的订单总金额
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 订单总金额
     */
    BigDecimal sumOrderAmountByInviterIdAndMonthKey(@Param("inviterId") Long inviterId,
                                                   @Param("monthKey") String monthKey);
    
    /**
     * 计算邀请者指定月份的佣金总额
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 佣金总额
     */
    BigDecimal sumCommissionAmountByInviterIdAndMonthKey(@Param("inviterId") Long inviterId,
                                                        @Param("monthKey") String monthKey);
    
    /**
     * 批量更新佣金记录状态
     * 
     * @param ids 佣金记录ID列表
     * @param status 新状态
     * @return 更新数量
     */
    Integer batchUpdateStatus(@Param("ids") List<Long> ids,
                            @Param("status") String status);
    
    /**
     * 软删除佣金记录
     * 
     * @param id 佣金记录ID
     * @return 更新数量
     */
    Integer softDeleteById(@Param("id") Long id);
    
    /**
     * 批量软删除佣金记录
     * 
     * @param ids 佣金记录ID列表
     * @return 删除数量
     */
    Integer batchSoftDelete(@Param("ids") List<Long> ids);
} 