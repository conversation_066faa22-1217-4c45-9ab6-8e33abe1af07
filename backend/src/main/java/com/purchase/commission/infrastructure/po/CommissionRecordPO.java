package com.purchase.commission.infrastructure.po;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 佣金记录持久化对象
 * 对应数据库表：commission_record
 */
@TableName("commission_record")
public class CommissionRecordPO {
    
    /**
     * 佣金记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 邀请者ID
     */
    @TableField("inviter_id")
    private Long inviterId;
    
    /**
     * 被邀请者ID（订单买家）
     */
    @TableField("invitee_id")
    private Long inviteeId;
    
    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;
    
    /**
     * 订单类型
     */
    @TableField("order_type")
    private String orderType;
    
    /**
     * 订单金额
     */
    @TableField("order_amount")
    private BigDecimal orderAmount;
    
    /**
     * 佣金比例
     */
    @TableField("commission_rate")
    private BigDecimal commissionRate;
    
    /**
     * 佣金金额
     */
    @TableField("commission_amount")
    private BigDecimal commissionAmount;
    
    /**
     * 状态
     */
    @TableField("status")
    private String status;
    
    /**
     * 月份标识
     */
    @TableField("month_key")
    private String monthKey;
    
    /**
     * 被邀请者买家角色验证
     */
    @TableField("buyer_role_verified")
    private Boolean buyerRoleVerified;
    
    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 确认时间
     */
    @TableField("confirmed_at")
    private LocalDateTime confirmedAt;

    /**
     * 处理时间
     */
    @TableField("processed_at")
    private LocalDateTime processedAt;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @TableLogic
    private Boolean deleted;
    
    // 默认构造函数
    public CommissionRecordPO() {
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getInviterId() {
        return inviterId;
    }
    
    public void setInviterId(Long inviterId) {
        this.inviterId = inviterId;
    }
    
    public Long getInviteeId() {
        return inviteeId;
    }
    
    public void setInviteeId(Long inviteeId) {
        this.inviteeId = inviteeId;
    }
    
    public Long getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
    
    public String getOrderType() {
        return orderType;
    }
    
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }
    
    public BigDecimal getOrderAmount() {
        return orderAmount;
    }
    
    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }
    
    public BigDecimal getCommissionRate() {
        return commissionRate;
    }
    
    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }
    
    public BigDecimal getCommissionAmount() {
        return commissionAmount;
    }
    
    public void setCommissionAmount(BigDecimal commissionAmount) {
        this.commissionAmount = commissionAmount;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getMonthKey() {
        return monthKey;
    }
    
    public void setMonthKey(String monthKey) {
        this.monthKey = monthKey;
    }
    
    public Boolean getBuyerRoleVerified() {
        return buyerRoleVerified;
    }
    
    public void setBuyerRoleVerified(Boolean buyerRoleVerified) {
        this.buyerRoleVerified = buyerRoleVerified;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Boolean getDeleted() {
        return deleted;
    }
    
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getConfirmedAt() {
        return confirmedAt;
    }

    public void setConfirmedAt(LocalDateTime confirmedAt) {
        this.confirmedAt = confirmedAt;
    }

    public LocalDateTime getProcessedAt() {
        return processedAt;
    }

    public void setProcessedAt(LocalDateTime processedAt) {
        this.processedAt = processedAt;
    }

    @Override
    public String toString() {
        return "CommissionRecordPO{" +
                "id=" + id +
                ", inviterId=" + inviterId +
                ", inviteeId=" + inviteeId +
                ", orderId=" + orderId +
                ", orderType='" + orderType + '\'' +
                ", orderAmount=" + orderAmount +
                ", commissionRate=" + commissionRate +
                ", commissionAmount=" + commissionAmount +
                ", status='" + status + '\'' +
                ", monthKey='" + monthKey + '\'' +
                ", buyerRoleVerified=" + buyerRoleVerified +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", deleted=" + deleted +
                '}';
    }
}
