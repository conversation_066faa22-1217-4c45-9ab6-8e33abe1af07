package com.purchase.commission.infrastructure.mapper;

import com.purchase.commission.infrastructure.po.CommissionConfigPO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 佣金配置数据访问映射器
 */
@Mapper
public interface CommissionConfigMapper {
    
    /**
     * 插入佣金配置
     */
    @Insert("""
        INSERT INTO commission_config (
            config_type, min_amount, max_amount, rate_value, bonus_amount,
            description, status, sort_order, created_at, updated_at
        ) VALUES (
            #{configType}, #{minAmount}, #{maxAmount}, #{rateValue}, #{bonusAmount},
            #{description}, #{status}, #{sortOrder}, #{createdAt}, #{updatedAt}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(CommissionConfigPO commissionConfigPO);
    
    /**
     * 根据ID查询佣金配置
     */
    @Select("""
        SELECT id, config_type, min_amount, max_amount, rate_value, bonus_amount,
               description, status, sort_order, created_at, updated_at
        FROM commission_config
        WHERE id = #{id}
        """)
    CommissionConfigPO selectById(Long id);
    
    /**
     * 根据配置类型查询启用的配置
     */
    @Select("""
        SELECT id, config_type, min_amount, max_amount, rate_value, bonus_amount,
               description, status, sort_order, created_at, updated_at
        FROM commission_config
        WHERE config_type = #{configType} AND status = 1
        ORDER BY sort_order ASC
        """)
    List<CommissionConfigPO> selectEnabledByConfigType(String configType);
    
    /**
     * 查询所有启用的佣金比例配置
     */
    @Select("""
        SELECT id, config_type, min_amount, max_amount, rate_value, bonus_amount,
               description, status, sort_order, created_at, updated_at
        FROM commission_config
        WHERE config_type = 'commission_rate' AND status = 1
        ORDER BY sort_order ASC
        """)
    List<CommissionConfigPO> selectEnabledCommissionRates();
    
    /**
     * 查询所有启用的月度奖金配置
     */
    @Select("""
        SELECT id, config_type, min_amount, max_amount, rate_value, bonus_amount,
               description, status, sort_order, created_at, updated_at
        FROM commission_config
        WHERE config_type = 'monthly_bonus' AND status = 1
        ORDER BY sort_order ASC
        """)
    List<CommissionConfigPO> selectEnabledMonthlyBonuses();
    
    /**
     * 查询所有配置
     */
    @Select("""
        SELECT id, config_type, min_amount, max_amount, rate_value, bonus_amount,
               description, status, sort_order, created_at, updated_at
        FROM commission_config
        ORDER BY config_type ASC, sort_order ASC
        """)
    List<CommissionConfigPO> selectAll();
    
    /**
     * 更新佣金配置
     */
    @Update("""
        UPDATE commission_config SET
            config_type = #{configType},
            min_amount = #{minAmount},
            max_amount = #{maxAmount},
            rate_value = #{rateValue},
            bonus_amount = #{bonusAmount},
            description = #{description},
            status = #{status},
            sort_order = #{sortOrder},
            updated_at = #{updatedAt}
        WHERE id = #{id}
        """)
    int update(CommissionConfigPO commissionConfigPO);
    
    /**
     * 根据ID删除配置
     */
    @Delete("DELETE FROM commission_config WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 检查配置是否存在
     */
    @Select("SELECT COUNT(1) FROM commission_config WHERE id = #{id}")
    int existsById(Long id);
}
