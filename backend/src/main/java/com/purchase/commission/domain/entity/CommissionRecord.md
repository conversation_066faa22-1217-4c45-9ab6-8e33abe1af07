# CommissionRecord.java

## 文件概述 (File Overview)
`CommissionRecord.java` 是佣金记录实体类，位于 `com.purchase.commission.domain.entity` 包中，使用MyBatis-Plus注解进行ORM映射。该实体类定义了佣金记录的完整数据模型，是佣金业务领域的核心聚合根，包含佣金基本信息、计算依据、发放状态、审核信息等核心属性。通过完善的字段设计和业务逻辑，支持佣金记录的全生命周期管理，实现了佣金数据的标准化存储和高效查询，并提供了完善的佣金计算和发放机制。

## 核心功能 (Core Functionality)
*   **佣金记录管理**: 存储佣金记录的完整信息，包括金额、类型、计算依据、发放状态等
*   **状态流转控制**: 管理佣金状态的流转，包括待审核、已审核、已发放、已拒绝等状态
*   **计算依据存储**: 详细记录佣金计算的依据，包括订单信息、费率、计算公式等
*   **发放管理**: 管理佣金的发放流程，包括发放时间、发放方式、发放账户等
*   **受益人信息**: 记录佣金受益人的身份信息、账户信息、税务信息等
*   **审核流程**: 支持佣金审核流程，包括审核人、审核时间、审核意见等
*   **类型分类**: 支持多种佣金类型，如邀请佣金、交易佣金、推广佣金等
*   **税务处理**: 处理佣金相关的税务计算和申报
*   **风险控制**: 提供佣金风险控制和异常检测功能
*   **关联关系维护**: 与订单、用户、邀请关系等建立关联关系
*   **统计分析支持**: 提供佣金统计分析所需的关键数据
*   **合规性检查**: 确保佣金处理符合相关法规要求

## DDD分析 (DDD Analysis)

### 限界上下文识别
*   **所属上下文**: 佣金管理上下文（Commission Management Context）
*   **上下文边界**: 与订单上下文、用户上下文、支付上下文、财务上下文的交互边界
*   **领域语言**: 佣金记录、佣金计算、佣金发放、邀请佣金、交易佣金、佣金审核等核心业务术语

### 聚合设计分析
*   **聚合根判断**: ✅ 是聚合根 - 佣金记录是独立的业务概念，具有完整的生命周期
*   **聚合边界**: 包含佣金基本信息、计算依据、发放信息、审核记录等
*   **业务不变量**: 
    *   佣金状态流转必须符合业务规则（待审核→已审核→已发放）
    *   佣金金额必须基于有效的计算依据和费率
    *   佣金发放必须在审核通过后进行
    *   同一业务事件只能产生一条佣金记录
    *   佣金金额必须大于0且不超过合理上限
*   **状态一致性**: 聚合内部状态变更必须保持一致性，状态变更需要记录操作日志

### 领域概念分析
*   **核心领域概念**: 佣金记录是对业务贡献的奖励机制，体现了价值分配
*   **业务价值**: 激励用户参与业务，促进平台生态发展，实现价值共享
*   **生命周期**: 产生→计算→审核→发放→完成的完整业务流程
*   **状态机**: 
    *   PENDING（待审核）→ APPROVED（已审核）
    *   APPROVED → PAID（已发放）
    *   PENDING → REJECTED（已拒绝）
    *   任意状态 → CANCELLED（已取消）

### 值对象候选
*   **佣金金额**: CommissionAmount（金额、货币类型、税前税后）
*   **计算依据**: CalculationBasis（订单金额、费率、计算公式、计算时间）
*   **受益人信息**: Beneficiary（用户ID、姓名、账户信息、税务信息）
*   **发放信息**: PaymentInfo（发放时间、发放方式、交易流水号）
*   **审核记录**: AuditRecord（审核人、审核时间、审核意见、审核结果）

## 业务规则 (Business Rules)
*   **计算规则**: 佣金计算必须基于预定义的费率和计算公式
*   **审核规则**: 超过一定金额的佣金必须经过人工审核
*   **发放规则**: 佣金发放必须在审核通过后的指定时间内完成
*   **重复检查**: 防止同一业务事件重复产生佣金记录
*   **时效规则**: 佣金记录有时效性，超期未发放的需要特殊处理
*   **税务规则**: 佣金发放需要考虑税务处理和申报要求
*   **最低金额**: 佣金金额必须达到最低发放标准
*   **账户验证**: 佣金发放前必须验证受益人账户的有效性

## 注意事项 (Notes)
*   **聚合根设计**: 作为聚合根，需要控制对聚合内部对象的访问
*   **状态一致性**: 状态变更需要通过聚合根的方法进行，确保业务规则得到执行
*   **领域事件**: 状态变更时应该发布相应的领域事件，如佣金发放事件、审核完成事件等
*   **数据精度**: 使用BigDecimal处理佣金金额，确保计算精度
*   **索引设计**: 合理设计数据库索引，支持高效的佣金查询和统计
*   **并发控制**: 佣金状态更新需要考虑并发控制，防止重复发放
*   **软删除**: 建议使用软删除机制，保留佣金历史数据
*   **审计追踪**: 记录佣金数据的完整变更历史，满足审计要求
*   **性能优化**: 大量佣金数据需要考虑分库分表等性能优化策略
*   **安全防护**: 防止佣金欺诈和恶意刷取行为
