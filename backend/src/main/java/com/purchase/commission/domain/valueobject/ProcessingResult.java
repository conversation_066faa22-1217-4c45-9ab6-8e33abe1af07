package com.purchase.commission.domain.valueobject;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 处理结果值对象
 * 封装批量处理的结果信息
 */
@Data
@NoArgsConstructor
public class ProcessingResult {
    
    /**
     * 邀请者处理结果
     */
    @Data
    @NoArgsConstructor
    public static class InviterResult {
        private Long inviterId;
        private Integer recordCount;
        private BigDecimal totalAmount;
        private BigDecimal totalCommission;
        private BigDecimal commissionRate;
        private BigDecimal monthlyBonus;
        private boolean success;
        private String errorMessage;
        
        public InviterResult(Long inviterId, Integer recordCount, boolean success) {
            this.inviterId = inviterId;
            this.recordCount = recordCount;
            this.success = success;
        }
        
        public InviterResult(Long inviterId, String errorMessage) {
            this.inviterId = inviterId;
            this.success = false;
            this.errorMessage = errorMessage;
            this.recordCount = 0;
        }
    }
    
    /**
     * 邀请者处理结果列表
     */
    private List<InviterResult> inviterResults = new ArrayList<>();
    
    /**
     * 处理开始时间
     */
    private long startTime;
    
    /**
     * 处理结束时间
     */
    private long endTime;
    
    /**
     * 开始处理
     */
    public void start() {
        this.startTime = System.currentTimeMillis();
    }
    
    /**
     * 结束处理
     */
    public void end() {
        this.endTime = System.currentTimeMillis();
    }
    
    /**
     * 添加成功结果
     */
    public void addSuccess(Long inviterId, Integer recordCount) {
        inviterResults.add(new InviterResult(inviterId, recordCount, true));
    }
    
    /**
     * 添加失败结果
     */
    public void addFailure(Long inviterId, String errorMessage) {
        inviterResults.add(new InviterResult(inviterId, errorMessage));
    }
    
    /**
     * 获取总记录数
     */
    public Integer getTotalRecords() {
        return inviterResults.stream()
                .mapToInt(result -> result.getRecordCount() != null ? result.getRecordCount() : 0)
                .sum();
    }
    
    /**
     * 获取成功处理数
     */
    public Integer getSuccessCount() {
        return (int) inviterResults.stream()
                .filter(InviterResult::isSuccess)
                .mapToInt(result -> result.getRecordCount() != null ? result.getRecordCount() : 0)
                .sum();
    }
    
    /**
     * 获取失败处理数
     */
    public Integer getFailureCount() {
        return getTotalRecords() - getSuccessCount();
    }
    
    /**
     * 获取成功的邀请者数量
     */
    public Integer getSuccessInviterCount() {
        return (int) inviterResults.stream()
                .filter(InviterResult::isSuccess)
                .count();
    }
    
    /**
     * 获取失败的邀请者数量
     */
    public Integer getFailureInviterCount() {
        return (int) inviterResults.stream()
                .filter(result -> !result.isSuccess())
                .count();
    }
    
    /**
     * 获取处理耗时（毫秒）
     */
    public Long getProcessingTimeMs() {
        if (startTime == 0 || endTime == 0) {
            return 0L;
        }
        return endTime - startTime;
    }
    
    /**
     * 获取成功率
     */
    public Double getSuccessRate() {
        int total = getTotalRecords();
        if (total == 0) {
            return 100.0;
        }
        return (double) getSuccessCount() / total * 100;
    }
    
    /**
     * 是否有失败
     */
    public boolean hasFailures() {
        return getFailureCount() > 0;
    }
    
    /**
     * 是否全部成功
     */
    public boolean isAllSuccess() {
        return !hasFailures() && getTotalRecords() > 0;
    }
    
    /**
     * 获取错误摘要
     */
    public String getErrorSummary() {
        List<String> errors = inviterResults.stream()
                .filter(result -> !result.isSuccess())
                .map(result -> String.format("邀请者%d: %s", result.getInviterId(), result.getErrorMessage()))
                .collect(Collectors.toList());
        
        if (errors.isEmpty()) {
            return null;
        }
        
        return String.join("; ", errors);
    }
    
    /**
     * 获取处理摘要
     */
    public String getSummary() {
        return String.format("总计: %d条, 成功: %d条, 失败: %d条, 耗时: %dms, 成功率: %.1f%%",
                getTotalRecords(),
                getSuccessCount(),
                getFailureCount(),
                getProcessingTimeMs(),
                getSuccessRate());
    }
    
    /**
     * 获取成功的邀请者结果
     */
    public List<InviterResult> getSuccessResults() {
        return inviterResults.stream()
                .filter(InviterResult::isSuccess)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取失败的邀请者结果
     */
    public List<InviterResult> getFailureResults() {
        return inviterResults.stream()
                .filter(result -> !result.isSuccess())
                .collect(Collectors.toList());
    }
}
