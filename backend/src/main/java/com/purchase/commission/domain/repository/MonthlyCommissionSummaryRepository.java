package com.purchase.commission.domain.repository;

import com.purchase.commission.domain.entity.MonthlyCommissionSummary;
import com.purchase.commission.domain.valueobject.MonthKey;

import java.util.List;
import java.util.Optional;

/**
 * 月度佣金汇总仓储接口
 * 定义月度佣金汇总的数据访问操作
 */
public interface MonthlyCommissionSummaryRepository {
    
    /**
     * 保存月度佣金汇总
     * 
     * @param summary 月度佣金汇总
     * @return 保存后的月度佣金汇总
     */
    MonthlyCommissionSummary save(MonthlyCommissionSummary summary);
    
    /**
     * 根据ID查找月度佣金汇总
     * 
     * @param id 汇总ID
     * @return 月度佣金汇总
     */
    Optional<MonthlyCommissionSummary> findById(Long id);
    
    /**
     * 根据邀请者ID和月份查找月度佣金汇总
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 月度佣金汇总
     */
    Optional<MonthlyCommissionSummary> findByInviterIdAndMonthKey(Long inviterId, MonthKey monthKey);
    
    /**
     * 根据邀请者ID查找月度佣金汇总列表
     * 
     * @param inviterId 邀请者ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 月度佣金汇总列表
     */
    List<MonthlyCommissionSummary> findByInviterId(Long inviterId, Integer limit, Integer offset);
    
    /**
     * 根据月份查找月度佣金汇总列表
     * 
     * @param monthKey 月份键
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 月度佣金汇总列表
     */
    List<MonthlyCommissionSummary> findByMonthKey(MonthKey monthKey, Integer limit, Integer offset);
    
    /**
     * 根据结算状态查找月度佣金汇总列表
     * 
     * @param status 结算状态
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 月度佣金汇总列表
     */
    List<MonthlyCommissionSummary> findByStatus(MonthlyCommissionSummary.Status status, 
                                                         Integer limit, Integer offset);
    
    /**
     * 根据邀请者ID和结算状态查找月度佣金汇总列表
     * 
     * @param inviterId 邀请者ID
     * @param status 结算状态
     * @return 月度佣金汇总列表
     */
    List<MonthlyCommissionSummary> findByInviterIdAndStatus(Long inviterId, 
                                                                     MonthlyCommissionSummary.Status status);
    
    /**
     * 查找可结算的月度佣金汇总
     * 
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 可结算的月度佣金汇总列表
     */
    List<MonthlyCommissionSummary> findSettlableSummaries(Integer limit, Integer offset);
    
    /**
     * 查找邀请者的可结算月度佣金汇总
     * 
     * @param inviterId 邀请者ID
     * @return 可结算的月度佣金汇总列表
     */
    List<MonthlyCommissionSummary> findSettlableSummariesByInviterId(Long inviterId);
    
    /**
     * 检查是否存在指定邀请者和月份的汇总记录
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 是否存在
     */
    boolean existsByInviterIdAndMonthKey(Long inviterId, MonthKey monthKey);
    
    /**
     * 统计邀请者的月度汇总记录数量
     * 
     * @param inviterId 邀请者ID
     * @return 记录数量
     */
    Integer countByInviterId(Long inviterId);
    
    /**
     * 统计指定结算状态的汇总记录数量
     * 
     * @param status 结算状态
     * @return 记录数量
     */
    Integer countByStatus(MonthlyCommissionSummary.Status status);
    
    /**
     * 批量更新结算状态
     * 
     * @param ids 汇总记录ID列表
     * @param status 新的结算状态
     * @return 更新数量
     */
    Integer batchUpdateStatus(List<Long> ids, MonthlyCommissionSummary.Status status);
    
    /**
     * 删除月度佣金汇总
     * 
     * @param id 汇总记录ID
     */
    void deleteById(Long id);
    
    /**
     * 软删除月度佣金汇总
     * 
     * @param id 汇总记录ID
     */
    void softDeleteById(Long id);
    
    /**
     * 批量软删除月度佣金汇总
     *
     * @param ids 汇总记录ID列表
     * @return 删除数量
     */
    Integer batchSoftDelete(List<Long> ids);

    // =====================================================
    // 月初统一处理相关方法
    // =====================================================

    /**
     * 根据月份查找所有月度佣金汇总（不分页）
     *
     * @param monthKey 月份键
     * @return 月度佣金汇总列表
     */
    List<MonthlyCommissionSummary> findByMonthKey(MonthKey monthKey);
}