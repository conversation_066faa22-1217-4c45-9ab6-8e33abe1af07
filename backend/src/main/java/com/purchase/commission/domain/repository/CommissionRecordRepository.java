package com.purchase.commission.domain.repository;

import com.purchase.commission.domain.entity.CommissionRecord;
import com.purchase.commission.domain.valueobject.MonthKey;

import java.util.List;
import java.util.Optional;

/**
 * 佣金记录仓储接口
 * 定义佣金记录的数据访问操作
 */
public interface CommissionRecordRepository {
    
    /**
     * 保存佣金记录
     * 
     * @param commissionRecord 佣金记录
     * @return 保存后的佣金记录
     */
    CommissionRecord save(CommissionRecord commissionRecord);
    
    /**
     * 根据ID查找佣金记录
     * 
     * @param id 佣金记录ID
     * @return 佣金记录
     */
    Optional<CommissionRecord> findById(Long id);
    
    /**
     * 根据订单ID查找佣金记录
     * 
     * @param orderId 订单ID
     * @return 佣金记录列表
     */
    List<CommissionRecord> findByOrderId(Long orderId);
    
    /**
     * 根据邀请者ID和月份查找佣金记录
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 佣金记录列表
     */
    List<CommissionRecord> findByInviterIdAndMonthKey(Long inviterId, MonthKey monthKey);
    
    /**
     * 根据被邀请者ID查找佣金记录
     * 
     * @param inviteeId 被邀请者ID
     * @return 佣金记录列表
     */
    List<CommissionRecord> findByInviteeId(Long inviteeId);
    
    /**
     * 根据邀请者ID查找佣金记录
     * 
     * @param inviterId 邀请者ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 佣金记录列表
     */
    List<CommissionRecord> findByInviterId(Long inviterId, Integer limit, Integer offset);
    
    /**
     * 根据状态查找佣金记录
     * 
     * @param status 状态
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 佣金记录列表
     */
    List<CommissionRecord> findByStatus(CommissionRecord.Status status, Integer limit, Integer offset);
    
    /**
     * 根据邀请者ID和状态查找佣金记录
     * 
     * @param inviterId 邀请者ID
     * @param status 状态
     * @return 佣金记录列表
     */
    List<CommissionRecord> findByInviterIdAndStatus(Long inviterId, CommissionRecord.Status status);
    
    /**
     * 根据月份查找佣金记录
     * 
     * @param monthKey 月份键
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 佣金记录列表
     */
    List<CommissionRecord> findByMonthKey(MonthKey monthKey, Integer limit, Integer offset);
    
    /**
     * 查找可结算的佣金记录
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 可结算的佣金记录列表
     */
    List<CommissionRecord> findSettlableRecords(Long inviterId, MonthKey monthKey);
    
    /**
     * 检查订单是否已存在佣金记录
     * 
     * @param orderId 订单ID
     * @param inviterId 邀请者ID
     * @return 是否存在
     */
    boolean existsByOrderIdAndInviterId(Long orderId, Long inviterId);
    
    /**
     * 统计邀请者在指定月份的佣金记录数量
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 记录数量
     */
    Integer countByInviterIdAndMonthKey(Long inviterId, MonthKey monthKey);
    
    /**
     * 统计邀请者的总佣金记录数量
     * 
     * @param inviterId 邀请者ID
     * @return 记录数量
     */
    Integer countByInviterId(Long inviterId);
    
    /**
     * 批量更新佣金记录状态
     * 
     * @param ids 佣金记录ID列表
     * @param status 新状态
     * @return 更新数量
     */
    Integer batchUpdateStatus(List<Long> ids, CommissionRecord.Status status);
    
    /**
     * 删除佣金记录
     * 
     * @param id 佣金记录ID
     */
    void deleteById(Long id);
    
    /**
     * 软删除佣金记录
     * 
     * @param id 佣金记录ID
     */
    void softDeleteById(Long id);
    
    /**
     * 批量软删除佣金记录
     *
     * @param ids 佣金记录ID列表
     * @return 删除数量
     */
    Integer batchSoftDelete(List<Long> ids);

    // =====================================================
    // 月初统一处理相关方法
    // =====================================================

    /**
     * 根据月份、状态和删除标志查找佣金记录
     *
     * @param monthKey 月份键
     * @param status 状态
     * @param deleted 是否删除
     * @return 佣金记录列表
     */
    List<CommissionRecord> findByMonthKeyAndStatusAndDeleted(MonthKey monthKey, CommissionRecord.Status status, Boolean deleted);

    /**
     * 根据邀请者ID、月份、状态和删除标志查找佣金记录
     *
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @param status 状态
     * @param deleted 是否删除
     * @return 佣金记录列表
     */
    List<CommissionRecord> findByInviterIdAndMonthKeyAndStatusAndDeleted(Long inviterId, MonthKey monthKey, CommissionRecord.Status status, Boolean deleted);

    /**
     * 批量更新记录状态为CONFIRMED并设置确认时间
     *
     * @param ids 佣金记录ID列表
     * @param confirmedAt 确认时间
     * @return 更新数量
     */
    Integer batchUpdateStatusToConfirmed(List<Long> ids, java.time.LocalDateTime confirmedAt);

    /**
     * 批量更新记录状态为CONFIRMED（使用当前时间作为确认时间）
     *
     * @param ids 佣金记录ID列表
     * @return 更新数量
     */
    default Integer batchUpdateStatusToConfirmed(List<Long> ids) {
        return batchUpdateStatusToConfirmed(ids, java.time.LocalDateTime.now());
    }

    /**
     * 查找指定月份中不同邀请者的ID列表
     *
     * @param monthKey 月份键
     * @param status 状态
     * @param deleted 是否删除
     * @return 邀请者ID列表
     */
    List<Long> findDistinctInviterIdsByMonthKeyAndStatusAndDeleted(MonthKey monthKey, CommissionRecord.Status status, Boolean deleted);

    /**
     * 统计指定月份和状态的记录数量
     *
     * @param monthKey 月份键
     * @param status 状态
     * @param deleted 是否删除
     * @return 记录数量
     */
    Integer countByMonthKeyAndStatusAndDeleted(MonthKey monthKey, CommissionRecord.Status status, Boolean deleted);
}