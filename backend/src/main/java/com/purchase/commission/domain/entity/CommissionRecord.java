package com.purchase.commission.domain.entity;

import com.purchase.commission.domain.valueobject.CommissionRate;
import com.purchase.commission.domain.valueobject.Money;
import com.purchase.commission.domain.valueobject.MonthKey;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 佣金记录领域实体
 * 封装佣金记录的业务逻辑和状态管理
 */
public class CommissionRecord {
    
    /**
     * 佣金状态枚举
     */
    public enum Status {
        PENDING("待确认"),
        CONFIRMED("已确认"), 
        PAID("已支付"),
        CANCELLED("已取消");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 订单类型枚举
     */
    public enum OrderType {
        PURCHASE("采购订单"),
        LOGISTICS("货代订单");
        
        private final String description;
        
        OrderType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 佣金记录ID
     */
    private Long id;
    
    /**
     * 邀请者ID
     */
    private Long inviterId;
    
    /**
     * 被邀请者ID（订单买家）
     */
    private Long inviteeId;
    
    /**
     * 订单ID
     */
    private Long orderId;
    
    /**
     * 订单类型
     */
    private OrderType orderType;
    
    /**
     * 订单金额
     */
    private Money orderAmount;
    
    /**
     * 佣金比例
     */
    private CommissionRate commissionRate;
    
    /**
     * 佣金金额
     */
    private Money commissionAmount;
    
    /**
     * 状态
     */
    private Status status;
    
    /**
     * 月份键
     */
    private MonthKey monthKey;
    
    /**
     * 被邀请者买家角色验证
     */
    private Boolean buyerRoleVerified;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 是否删除
     */
    private Boolean deleted;
    
    /**
     * 默认构造函数（用于ORM框架）
     */
    protected CommissionRecord() {
    }
    
    /**
     * 创建佣金记录的构造函数
     */
    private CommissionRecord(Long inviterId, Long inviteeId, Long orderId, 
                           OrderType orderType, Money orderAmount, 
                           CommissionRate commissionRate, MonthKey monthKey) {
        this.inviterId = inviterId;
        this.inviteeId = inviteeId;
        this.orderId = orderId;
        this.orderType = orderType;
        this.orderAmount = orderAmount;
        this.commissionRate = commissionRate;
        this.monthKey = monthKey;
        this.commissionAmount = commissionRate.calculateCommission(orderAmount);
        this.status = Status.PENDING;
        this.buyerRoleVerified = true;
        this.deleted = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 创建佣金记录
     * 
     * @param inviterId 邀请者ID
     * @param inviteeId 被邀请者ID
     * @param orderId 订单ID
     * @param orderType 订单类型
     * @param orderAmount 订单金额
     * @param commissionRate 佣金比例
     * @param monthKey 月份键
     * @return 佣金记录实体
     */
    public static CommissionRecord create(Long inviterId, Long inviteeId, Long orderId,
                                        OrderType orderType, Money orderAmount,
                                        CommissionRate commissionRate, MonthKey monthKey) {
        validateCreateParameters(inviterId, inviteeId, orderId, orderType, orderAmount, commissionRate, monthKey);
        return new CommissionRecord(inviterId, inviteeId, orderId, orderType, orderAmount, commissionRate, monthKey);
    }
    
    /**
     * 验证创建参数
     */
    private static void validateCreateParameters(Long inviterId, Long inviteeId, Long orderId,
                                               OrderType orderType, Money orderAmount,
                                               CommissionRate commissionRate, MonthKey monthKey) {
        if (inviterId == null) {
            throw new IllegalArgumentException("邀请者ID不能为null");
        }
        if (inviteeId == null) {
            throw new IllegalArgumentException("被邀请者ID不能为null");
        }
        if (orderId == null) {
            throw new IllegalArgumentException("订单ID不能为null");
        }
        if (orderType == null) {
            throw new IllegalArgumentException("订单类型不能为null");
        }
        if (orderAmount == null) {
            throw new IllegalArgumentException("订单金额不能为null");
        }
        if (!orderAmount.isPositive()) {
            throw new IllegalArgumentException("订单金额必须大于零");
        }
        if (commissionRate == null) {
            throw new IllegalArgumentException("佣金比例不能为null");
        }
        if (monthKey == null) {
            throw new IllegalArgumentException("月份键不能为null");
        }
        if (inviterId.equals(inviteeId)) {
            throw new IllegalArgumentException("邀请者和被邀请者不能是同一人");
        }
    }
    
    /**
     * 确认佣金
     */
    public void confirm() {
        if (this.status != Status.PENDING) {
            throw new IllegalStateException("只有待确认状态的佣金记录才能确认");
        }
        if (this.deleted) {
            throw new IllegalStateException("已删除的佣金记录不能确认");
        }
        this.status = Status.CONFIRMED;
    }
    
    /**
     * 标记为已支付
     */
    public void markAsPaid() {
        if (this.status != Status.CONFIRMED) {
            throw new IllegalStateException("只有已确认状态的佣金记录才能标记为已支付");
        }
        if (this.deleted) {
            throw new IllegalStateException("已删除的佣金记录不能标记为已支付");
        }
        this.status = Status.PAID;
    }
    
    /**
     * 取消佣金
     */
    public void cancel() {
        if (this.status == Status.PAID) {
            throw new IllegalStateException("已支付的佣金记录不能取消");
        }
        if (this.deleted) {
            throw new IllegalStateException("已删除的佣金记录不能取消");
        }
        this.status = Status.CANCELLED;
    }
    
    /**
     * 软删除
     */
    public void softDelete() {
        if (this.status == Status.PAID) {
            throw new IllegalStateException("已支付的佣金记录不能删除");
        }
        if (this.deleted) {
            return; // 已经是删除状态
        }
        this.deleted = true;
    }
    
    /**
     * 设置买家角色验证状态
     */
    public void setBuyerRoleVerified(Boolean verified) {
        if (verified == null) {
            throw new IllegalArgumentException("验证状态不能为null");
        }
        this.buyerRoleVerified = verified;
    }
    
    /**
     * 重新计算佣金
     * 
     * @param newRate 新的佣金比例
     */
    public void recalculateCommission(CommissionRate newRate) {
        if (newRate == null) {
            throw new IllegalArgumentException("新的佣金比例不能为null");
        }
        if (this.status == Status.PAID) {
            throw new IllegalStateException("已支付的佣金记录不能重新计算");
        }
        if (this.deleted) {
            throw new IllegalStateException("已删除的佣金记录不能重新计算");
        }
        
        this.commissionRate = newRate;
        this.commissionAmount = this.commissionRate.calculateCommission(this.orderAmount);
    }
    
    /**
     * 判断是否可以结算
     */
    public boolean canSettle() {
        return this.status == Status.CONFIRMED && !this.deleted && this.buyerRoleVerified;
    }
    
    /**
     * 判断是否有效
     */
    public boolean isValid() {
        return !this.deleted && this.status != Status.CANCELLED && this.buyerRoleVerified;
    }
    
    // Getters
    public Long getId() {
        return id;
    }
    
    public Long getInviterId() {
        return inviterId;
    }
    
    public Long getInviteeId() {
        return inviteeId;
    }
    
    public Long getOrderId() {
        return orderId;
    }
    
    public OrderType getOrderType() {
        return orderType;
    }
    
    public Money getOrderAmount() {
        return orderAmount;
    }
    
    public CommissionRate getCommissionRate() {
        return commissionRate;
    }
    
    public Money getCommissionAmount() {
        return commissionAmount;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public MonthKey getMonthKey() {
        return monthKey;
    }
    
    public Boolean getBuyerRoleVerified() {
        return buyerRoleVerified;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public Boolean getDeleted() {
        return deleted;
    }
    
    // Setters for ORM
    public void setId(Long id) {
        this.id = id;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        CommissionRecord that = (CommissionRecord) obj;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "CommissionRecord{" +
                "id=" + id +
                ", inviterId=" + inviterId +
                ", inviteeId=" + inviteeId +
                ", orderId=" + orderId +
                ", orderType=" + orderType +
                ", commissionAmount=" + commissionAmount +
                ", status=" + status +
                ", monthKey=" + monthKey +
                '}';
    }
} 