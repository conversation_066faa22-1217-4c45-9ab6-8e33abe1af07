package com.purchase.commission.domain.service;

import com.purchase.commission.domain.valueobject.CommissionRate;
import com.purchase.commission.domain.valueobject.Money;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 佣金计算领域服务
 * 封装阶梯式提成和月度奖金的计算逻辑
 */
@Service
public class CommissionCalculationService {
    
    /**
     * 佣金配置阶梯
     */
    private static class CommissionTier {
        private final Money minAmount;
        private final Money maxAmount;
        private final CommissionRate rate;
        
        public CommissionTier(Money minAmount, Money maxAmount, CommissionRate rate) {
            this.minAmount = minAmount;
            this.maxAmount = maxAmount;
            this.rate = rate;
        }
        
        public boolean matches(Money amount) {
            boolean aboveMin = amount.greaterThanOrEqual(minAmount);
            boolean belowMax = maxAmount == null || amount.lessThanOrEqual(maxAmount);
            return aboveMin && belowMax;
        }
        
        public CommissionRate getRate() {
            return rate;
        }
    }
    
    /**
     * 奖金配置阶梯
     */
    private static class BonusTier {
        private final Money minAmount;
        private final Money maxAmount;
        private final Money bonusAmount;

        public BonusTier(Money minAmount, Money maxAmount, Money bonusAmount) {
            this.minAmount = minAmount;
            this.maxAmount = maxAmount;
            this.bonusAmount = bonusAmount;
        }

        public boolean qualifies(Money amount) {
            boolean aboveMin = amount.greaterThanOrEqual(minAmount);
            boolean belowMax = maxAmount == null || amount.lessThanOrEqual(maxAmount);
            return aboveMin && belowMax;
        }

        public Money getBonusAmount() {
            return bonusAmount;
        }

        public Money getMinAmount() {
            return minAmount;
        }

        public Money getMaxAmount() {
            return maxAmount;
        }
    }
    
    /**
     * 默认佣金阶梯配置
     * 根据NEW_COMMISSION_MECHANISM.md中的配置
     */
    private static final List<CommissionTier> DEFAULT_COMMISSION_TIERS = new ArrayList<>();
    
    /**
     * 默认奖金阶梯配置
     */
    private static final List<BonusTier> DEFAULT_BONUS_TIERS = new ArrayList<>();
    
    static {
        // 初始化佣金阶梯 - 新方案（美元单位）
        DEFAULT_COMMISSION_TIERS.add(new CommissionTier(
            Money.of("0"), Money.of("7000"), CommissionRate.ofPercentage(new BigDecimal("0.9"))
        ));
        DEFAULT_COMMISSION_TIERS.add(new CommissionTier(
            Money.of("7000.01"), Money.of("20000"), CommissionRate.ofPercentage(new BigDecimal("1.1"))
        ));
        DEFAULT_COMMISSION_TIERS.add(new CommissionTier(
            Money.of("20000.01"), Money.of("40000"), CommissionRate.ofPercentage(new BigDecimal("1.2"))
        ));
        DEFAULT_COMMISSION_TIERS.add(new CommissionTier(
            Money.of("40000.01"), Money.of("70000"), CommissionRate.ofPercentage(new BigDecimal("1.3"))
        ));
        DEFAULT_COMMISSION_TIERS.add(new CommissionTier(
            Money.of("70000.01"), null, CommissionRate.ofPercentage(new BigDecimal("1.4"))
        ));
        
        // 初始化奖金阶梯 - 新方案（美元单位，与数据库配置一致）
        // $7,000 - $15,000: $50
        DEFAULT_BONUS_TIERS.add(new BonusTier(Money.of("7000"), Money.of("15000"), Money.of("50")));
        // $15,000.01 - $30,000: $100
        DEFAULT_BONUS_TIERS.add(new BonusTier(Money.of("15000.01"), Money.of("30000"), Money.of("100")));
        // $30,000.01 - $50,000: $200
        DEFAULT_BONUS_TIERS.add(new BonusTier(Money.of("30000.01"), Money.of("50000"), Money.of("200")));
        // $50,000.01 - $70,000: $300
        DEFAULT_BONUS_TIERS.add(new BonusTier(Money.of("50000.01"), Money.of("70000"), Money.of("300")));
        // $70,000.01 - $100,000: $500
        DEFAULT_BONUS_TIERS.add(new BonusTier(Money.of("70000.01"), Money.of("100000"), Money.of("500")));
        // $100,000.01 - $150,000: $700
        DEFAULT_BONUS_TIERS.add(new BonusTier(Money.of("100000.01"), Money.of("150000"), Money.of("700")));
        // $150,000.01及以上: $900
        DEFAULT_BONUS_TIERS.add(new BonusTier(Money.of("150000.01"), null, Money.of("900")));
    }
    
    /**
     * 根据月度总订单金额计算适用的佣金比例
     * 
     * @param monthlyTotalAmount 月度总订单金额
     * @return 适用的佣金比例
     */
    public CommissionRate calculateCommissionRate(Money monthlyTotalAmount) {
        if (monthlyTotalAmount == null) {
            throw new IllegalArgumentException("月度总金额不能为null");
        }
        
        for (CommissionTier tier : DEFAULT_COMMISSION_TIERS) {
            if (tier.matches(monthlyTotalAmount)) {
                return tier.getRate();
            }
        }
        
        // 如果没有匹配的阶梯，返回最低比例
        return CommissionRate.ofPercentage(new BigDecimal("0.9"));
    }
    
    /**
     * 根据月度总订单金额计算月度奖金
     *
     * @param monthlyTotalAmount 月度总订单金额
     * @return 月度奖金金额
     */
    public Money calculateMonthlyBonus(Money monthlyTotalAmount) {
        if (monthlyTotalAmount == null) {
            throw new IllegalArgumentException("月度总金额不能为null");
        }

        // 遍历奖金阶梯，找到匹配的范围
        for (BonusTier tier : DEFAULT_BONUS_TIERS) {
            if (tier.qualifies(monthlyTotalAmount)) {
                return tier.getBonusAmount();
            }
        }

        // 如果没有符合条件的阶梯，返回零奖金
        return Money.zero();
    }
    
    /**
     * 计算单笔订单的佣金金额
     * 
     * @param orderAmount 订单金额
     * @param commissionRate 佣金比例
     * @return 佣金金额
     */
    public Money calculateCommissionAmount(Money orderAmount, CommissionRate commissionRate) {
        if (orderAmount == null) {
            throw new IllegalArgumentException("订单金额不能为null");
        }
        if (commissionRate == null) {
            throw new IllegalArgumentException("佣金比例不能为null");
        }
        
        return commissionRate.calculateCommission(orderAmount);
    }
    
    /**
     * 计算月度总提成
     * 
     * @param monthlyTotalAmount 月度总订单金额
     * @return 月度总提成金额
     */
    public Money calculateMonthlyCommission(Money monthlyTotalAmount) {
        if (monthlyTotalAmount == null) {
            throw new IllegalArgumentException("月度总金额不能为null");
        }
        
        CommissionRate rate = calculateCommissionRate(monthlyTotalAmount);
        return calculateCommissionAmount(monthlyTotalAmount, rate);
    }
    
    /**
     * 计算月度总收益（提成 + 奖金）
     * 
     * @param monthlyTotalAmount 月度总订单金额
     * @return 月度总收益
     */
    public Money calculateMonthlyTotalEarnings(Money monthlyTotalAmount) {
        if (monthlyTotalAmount == null) {
            throw new IllegalArgumentException("月度总金额不能为null");
        }
        
        Money commission = calculateMonthlyCommission(monthlyTotalAmount);
        Money bonus = calculateMonthlyBonus(monthlyTotalAmount);
        return commission.add(bonus);
    }
    
    /**
     * 验证订单是否符合佣金计算条件
     * 
     * @param orderType 订单类型
     * @param orderStatus 订单状态
     * @param buyerIsInvitee 买家是否为被邀请者
     * @return 是否符合条件
     */
    public boolean isValidCommissionOrder(String orderType, String orderStatus, boolean buyerIsInvitee) {
        // 1. 订单状态必须是已完成
        if (!"completed".equals(orderStatus)) {
            return false;
        }
        
        // 2. 排除样品采购订单
        if (orderType != null && orderType.toLowerCase().contains("sample")) {
            return false;
        }
        
        // 3. 订单类型必须是采购订单或货代订单
        if (!"purchase".equals(orderType) && !"logistics".equals(orderType)) {
            return false;
        }
        
        // 4. 买家必须是被邀请用户
        return buyerIsInvitee;
    }
    
    /**
     * 获取佣金比例说明
     * 
     * @param monthlyTotalAmount 月度总订单金额
     * @return 佣金比例说明文本
     */
    public String getCommissionRateDescription(Money monthlyTotalAmount) {
        CommissionRate rate = calculateCommissionRate(monthlyTotalAmount);
        
        for (CommissionTier tier : DEFAULT_COMMISSION_TIERS) {
            if (tier.matches(monthlyTotalAmount)) {
                String range;
                if (tier.maxAmount == null) {
                    range = tier.minAmount + "元以上";
                } else {
                    range = tier.minAmount + "-" + tier.maxAmount + "元";
                }
                return range + ": " + rate.getPercentageString();
            }
        }
        
        return "未知阶梯: " + rate.getPercentageString();
    }
    
    /**
     * 获取奖金说明
     *
     * @param monthlyTotalAmount 月度总订单金额
     * @return 奖金说明文本
     */
    public String getBonusDescription(Money monthlyTotalAmount) {
        Money bonus = calculateMonthlyBonus(monthlyTotalAmount);

        if (bonus.isZero()) {
            return "未达到奖金门槛";
        }

        for (BonusTier tier : DEFAULT_BONUS_TIERS) {
            if (tier.qualifies(monthlyTotalAmount)) {
                String range;
                if (tier.getMaxAmount() == null) {
                    range = tier.getMinAmount() + "元以上";
                } else {
                    range = tier.getMinAmount() + "-" + tier.getMaxAmount() + "元";
                }
                return range + ": " + tier.getBonusAmount() + "元";
            }
        }

        return "奖金: " + bonus + "元";
    }
} 