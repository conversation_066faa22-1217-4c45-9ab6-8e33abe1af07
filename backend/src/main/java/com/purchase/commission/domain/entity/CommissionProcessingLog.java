package com.purchase.commission.domain.entity;

import com.purchase.commission.domain.valueobject.MonthKey;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 佣金处理日志领域实体
 * 记录月度佣金批量处理的执行情况
 */
@Data
@NoArgsConstructor
public class CommissionProcessingLog {
    
    /**
     * 处理状态枚举
     */
    public enum Status {
        PROCESSING("处理中"),
        SUCCESS("成功"),
        FAILED("失败");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 处理日志ID
     */
    private Long id;
    
    /**
     * 处理月份
     */
    private MonthKey monthKey;
    
    /**
     * 处理日期
     */
    private LocalDate processingDate;
    
    /**
     * 总记录数
     */
    private Integer totalRecords;
    
    /**
     * 成功处理数
     */
    private Integer successCount;
    
    /**
     * 失败处理数
     */
    private Integer failureCount;
    
    /**
     * 处理状态
     */
    private Status status;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 开始时间
     */
    private LocalDateTime startedAt;
    
    /**
     * 完成时间
     */
    private LocalDateTime completedAt;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建处理日志
     */
    public static CommissionProcessingLog create(MonthKey monthKey) {
        CommissionProcessingLog log = new CommissionProcessingLog();
        log.monthKey = monthKey;
        log.processingDate = LocalDate.now();
        log.totalRecords = 0;
        log.successCount = 0;
        log.failureCount = 0;
        log.status = Status.PROCESSING;
        log.startedAt = LocalDateTime.now();
        log.createdAt = LocalDateTime.now();
        log.updatedAt = LocalDateTime.now();
        return log;
    }
    
    /**
     * 更新处理结果
     */
    public void updateResult(Integer totalRecords, Integer successCount, Integer failureCount) {
        this.totalRecords = totalRecords;
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.status = failureCount > 0 ? Status.FAILED : Status.SUCCESS;
        this.completedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 标记处理失败
     */
    public void markAsFailed(String errorMessage) {
        this.status = Status.FAILED;
        this.errorMessage = errorMessage;
        this.completedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 标记处理成功
     */
    public void markAsSuccess() {
        this.status = Status.SUCCESS;
        this.completedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查是否处理完成
     */
    public boolean isCompleted() {
        return status == Status.SUCCESS || status == Status.FAILED;
    }
    
    /**
     * 检查是否处理成功
     */
    public boolean isSuccess() {
        return status == Status.SUCCESS;
    }
    
    /**
     * 获取处理耗时（毫秒）
     */
    public Long getProcessingTimeMs() {
        if (startedAt == null || completedAt == null) {
            return null;
        }
        return java.time.Duration.between(startedAt, completedAt).toMillis();
    }
    
    /**
     * 获取成功率
     */
    public Double getSuccessRate() {
        if (totalRecords == null || totalRecords == 0) {
            return 0.0;
        }
        return (double) successCount / totalRecords * 100;
    }
    
    /**
     * 获取处理摘要
     */
    public String getSummary() {
        return String.format("月份: %s, 总数: %d, 成功: %d, 失败: %d, 状态: %s", 
                monthKey.toString(), 
                totalRecords != null ? totalRecords : 0,
                successCount != null ? successCount : 0,
                failureCount != null ? failureCount : 0,
                status.getDescription());
    }
}
