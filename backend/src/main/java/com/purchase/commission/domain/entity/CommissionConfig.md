# CommissionConfig.md

## 1. 文件概述

`CommissionConfig` 是一个领域实体（Domain Entity），它封装了系统中所有与佣金计算相关的配置规则。这个实体不仅包含了数据属性，还内聚了业务逻辑和规则，如判断配置类型、检查金额范围等。它通过 `ConfigType` 枚举区分了“佣金比例”和“月度奖金”两种不同的配置，是佣金计算引擎的核心数据模型和规则来源。

- **类型**: 领域实体 (Domain Entity)
- **核心职责**: 定义和管理佣金计算的规则，包括阶梯费率和月度奖金。
- **在模块中的位置**: 作为佣金领域模型的核心，被 `CommissionCalculationService` 等服务用来获取计算规则。

## 2. 核心功能

`CommissionConfig` 实体具备以下核心功能：

- **规则定义**: 能够定义一个完整的佣金规则，包括其类型（佣金比例或奖金）、适用的金额范围（`minAmount`, `maxAmount`）、具体的比例值或奖金金额。
- **类型安全**: 通过 `ConfigType` 和 `Status` 两个内部枚举，确保了配置类型和状态的类型安全，避免了使用魔法数字或字符串。
- **业务逻辑内聚**: 实体内部包含了多个业务方法，如 `isCommissionRateConfig()`, `isMonthlyBonusConfig()`, `isEnabled()`, 和 `isAmountInRange()`，使得业务规则与数据紧密结合。
- **值对象使用**: 实体属性中使用了 `Money` 和 `CommissionRate` 等值对象，增强了代码的表达力和健壮性。
- **规则获取**: 提供了 `getApplicableRate()` 和 `getApplicableBonus()` 方法，用于安全地获取与配置类型相匹配的规则值。

## 3. 接口说明

作为实体类，`CommissionConfig` 主要通过其业务方法和Getter/Setter与外部交互。

### 3.1 业务方法

- **`isCommissionRateConfig()`**: 判断当前配置是否为“佣金比例”类型。
- **`isMonthlyBonusConfig()`**: 判断当前配置是否为“月度奖金”类型。
- **`isEnabled()`**: 检查当前配置是否处于“启用”状态。
- **`isAmountInRange(Money amount)`**: 检查给定的金额是否落在当前配置的最小和最大金额范围内（包含边界）。
- **`getApplicableRate()`**: 获取佣金比例值。如果配置类型不是 `COMMISSION_RATE`，则会抛出 `IllegalStateException`。
- **`getApplicableBonus()`**: 获取月度奖金金额。如果配置类型不是 `MONTHLY_BONUS`，则会抛出 `IllegalStateException`。

### 3.2 内部枚举

- **`ConfigType`**: 定义了配置的两种类型：
  - `COMMISSION_RATE`: 佣金比例，用于按比例计算佣金。
  - `MONTHLY_BONUS`: 月度奖金，用于达到特定月度业绩后的额外奖励。
- **`Status`**: 定义了配置的两种状态：
  - `DISABLED`: 禁用。
  - `ENABLED`: 启用。

## 4. 使用示例

### 示例1: 创建一个新的阶梯佣金比例配置
```java
// 在一个初始化或后台管理服务中
CommissionConfig config = new CommissionConfig();
config.setConfigType(CommissionConfig.ConfigType.COMMISSION_RATE);
config.setDescription("订单金额 1万-5万 部分");
config.setMinAmount(new Money("10000.00"));
config.setMaxAmount(new Money("50000.00"));
config.setRateValue(new CommissionRate(0.02)); // 2% 的佣金比例
config.setStatus(CommissionConfig.Status.ENABLED);
config.setSortOrder(10);

// commissionConfigRepository.save(config);
```

### 示例2: 创建一个月度销售额奖金配置
```java
CommissionConfig bonusConfig = new CommissionConfig();
bonusConfig.setConfigType(CommissionConfig.ConfigType.MONTHLY_BONUS);
bonusConfig.setDescription("月销售额超过100万奖励");
bonusConfig.setMinAmount(new Money("1000000.00"));
bonusConfig.setBonusAmount(new Money("5000.00")); // 额外奖励5000元
bonusConfig.setStatus(CommissionConfig.Status.ENABLED);
bonusConfig.setSortOrder(100);

// commissionConfigRepository.save(bonusConfig);
```

### 示例3: 在佣金计算服务中使用配置
```java
// 在 CommissionCalculationService 中
public Money calculateCommission(Money orderAmount) {
    // 1. 从数据库获取所有启用的佣金比例配置，按 sortOrder 排序
    List<CommissionConfig> rateConfigs = repository.findByTypeAndStatus(ConfigType.COMMISSION_RATE, Status.ENABLED);

    // 2. 找到匹配当前订单金额的配置
    for (CommissionConfig config : rateConfigs) {
        if (config.isAmountInRange(orderAmount)) {
            CommissionRate rate = config.getApplicableRate();
            return orderAmount.multiply(rate.getValue());
        }
    }

    return Money.ZERO; // 没有匹配的配置
}
```

## 5. 注意事项

1.  **实体与数据表**: 这是一个领域实体，它直接映射到数据库中的 `commission_config` 表。
2.  **值对象**: `minAmount`, `maxAmount`, `rateValue`, `bonusAmount` 都被设计为值对象 (`Money`, `CommissionRate`)，而不是基本类型（如 `BigDecimal`），这使得业务意图更清晰，并能封装相关操作。
3.  **范围定义**: 金额范围 `[minAmount, maxAmount]` 是闭区间。`isAmountInRange` 的实现逻辑清晰地体现了这一点。
4.  **类型安全**: `getApplicableRate` 和 `getApplicableBonus` 方法在获取值之前会检查配置类型，这是保证类型安全的良好实践，避免了在错误类型的配置上获取不该有的值。
5.  **可扩展性**: 通过增加 `ConfigType` 枚举的成员，可以轻松扩展出新的配置类型，而不需要大规模修改现有代码。
6.  **排序**: `sortOrder` 字段非常重要，它决定了在有多个重叠或连续的金额范围时，配置应用的优先级。
7.  **空值处理**: `isAmountInRange` 方法正确处理了 `minAmount` 或 `maxAmount` 可能为 `null` 的情况，这允许定义开放式范围（如“10万以上”）。
8.  **不变性**: 虽然实体本身是可变的（有Setter），但在核心业务逻辑中，从数据库加载的配置对象应被视为不可变的，任何修改都应通过仓储（Repository）来持久化。
9.  **Equals 和 HashCode**: 实体正确地重写了 `equals` 和 `hashCode` 方法，并基于唯一的 `id` 字段进行判断，这对于在集合中正确处理实体至关重要。
10. **构造函数**: 提供了默认构造函数和全参构造函数，便于对象的创建和ORM框架的实例化。