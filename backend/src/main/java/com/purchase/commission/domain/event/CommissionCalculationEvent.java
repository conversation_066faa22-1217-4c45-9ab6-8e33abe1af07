package com.purchase.commission.domain.event;

import com.purchase.commission.domain.valueobject.Money;

import java.time.LocalDateTime;

/**
 * 佣金计算领域事件
 * 当订单状态变为已完成时触发，用于计算佣金
 */
public class CommissionCalculationEvent {
    
    /**
     * 订单ID
     */
    private final Long orderId;
    
    /**
     * 买家ID
     */
    private final Long buyerId;
    
    /**
     * 订单金额
     */
    private final Money orderAmount;
    
    /**
     * 订单类型
     */
    private final String orderType;
    
    /**
     * 完成时间
     */
    private final LocalDateTime completedAt;
    
    /**
     * 事件发生时间
     */
    private final LocalDateTime occurredAt;
    
    /**
     * 构造函数
     */
    public CommissionCalculationEvent(Long orderId, Long buyerId, Money orderAmount,
                                     String orderType, LocalDateTime completedAt) {
        this.orderId = orderId;
        this.buyerId = buyerId;
        this.orderAmount = orderAmount;
        this.orderType = orderType;
        this.completedAt = completedAt;
        this.occurredAt = LocalDateTime.now();
    }
    
    public Long getOrderId() {
        return orderId;
    }
    
    public Long getBuyerId() {
        return buyerId;
    }
    
    public Money getOrderAmount() {
        return orderAmount;
    }
    
    public String getOrderType() {
        return orderType;
    }
    
    public LocalDateTime getCompletedAt() {
        return completedAt;
    }
    
    public LocalDateTime getOccurredAt() {
        return occurredAt;
    }
    
    @Override
    public String toString() {
        return "CommissionCalculationEvent{" +
                "orderId=" + orderId +
                ", buyerId=" + buyerId +
                ", orderAmount=" + orderAmount +
                ", orderType='" + orderType + '\'' +
                ", completedAt=" + completedAt +
                ", occurredAt=" + occurredAt +
                '}';
    }
} 