package com.purchase.commission.domain.entity;

import com.purchase.commission.domain.valueobject.CommissionRate;
import com.purchase.commission.domain.valueobject.Money;
import com.purchase.commission.domain.valueobject.MonthKey;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 月度佣金汇总领域实体
 * 封装月度佣金汇总的业务逻辑和结算管理
 */
public class MonthlyCommissionSummary {
    
    /**
     * 结算状态枚举
     */
    public enum Status {
        PENDING("待结算"),
        PROCESSING("结算中"),
        COMPLETED("已完成"),
        CANCELLED("已取消");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 汇总记录ID
     */
    private Long id;
    
    /**
     * 邀请者ID
     */
    private Long inviterId;
    
    /**
     * 月份键
     */
    private MonthKey monthKey;
    
    /**
     * 月度订单总金额
     */
    private Money totalOrderAmount;
    
    /**
     * 月度适用佣金比例
     */
    private CommissionRate commissionRate;
    
    /**
     * 月度佣金总额
     */
    private Money commissionAmount;
    
    /**
     * 月度奖金
     */
    private Money monthlyBonus;
    
    /**
     * 月度总收益（佣金+奖金）
     */
    private Money totalAmount;
    
    /**
     * 订单数量
     */
    private Integer orderCount;
    
    /**
     * 状态
     */
    private Status status;
    
    /**
     * 结算时间
     */
    private LocalDateTime settledAt;

    /**
     * 最后计算时间
     */
    private LocalDateTime lastCalculatedAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 默认构造函数（用于ORM框架）
     */
    protected MonthlyCommissionSummary() {
    }
    
    /**
     * 创建月度佣金汇总的构造函数
     */
    private MonthlyCommissionSummary(Long inviterId, MonthKey monthKey) {
        this.inviterId = inviterId;
        this.monthKey = monthKey;
        this.totalOrderAmount = Money.zero();
        this.commissionRate = CommissionRate.zero();
        this.commissionAmount = Money.zero();
        this.monthlyBonus = Money.zero();
        this.totalAmount = Money.zero();
        this.orderCount = 0;
        this.status = Status.PENDING;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 创建月度佣金汇总
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 月度佣金汇总实体
     */
    public static MonthlyCommissionSummary create(Long inviterId, MonthKey monthKey) {
        if (inviterId == null) {
            throw new IllegalArgumentException("邀请者ID不能为null");
        }
        if (monthKey == null) {
            throw new IllegalArgumentException("月份键不能为null");
        }
        return new MonthlyCommissionSummary(inviterId, monthKey);
    }
    
    /**
     * 从持久化对象中重建领域实体（供仓储使用）
     */
    public static MonthlyCommissionSummary reconstitute(Long id, Long inviterId, MonthKey monthKey,
                                                      Money totalOrderAmount, CommissionRate commissionRate, Money commissionAmount, Money monthlyBonus, Money totalAmount,
                                                      Integer orderCount, Status status, LocalDateTime settledAt, LocalDateTime lastCalculatedAt,
                                                      LocalDateTime createdAt, LocalDateTime updatedAt) {
        MonthlyCommissionSummary summary = new MonthlyCommissionSummary(inviterId, monthKey);
        summary.id = id;
        summary.totalOrderAmount = totalOrderAmount;
        summary.commissionRate = commissionRate;
        summary.commissionAmount = commissionAmount;
        summary.monthlyBonus = monthlyBonus;
        summary.totalAmount = totalAmount;
        summary.orderCount = orderCount;
        summary.status = status;
        summary.settledAt = settledAt;
        summary.lastCalculatedAt = lastCalculatedAt;
        summary.createdAt = createdAt;
        summary.updatedAt = updatedAt;
        return summary;
    }
    
    /**
     * 更新汇总数据
     * 
     * @param totalOrderAmount 总订单金额
     * @param commissionRate 适用佣金比例
     * @param commissionAmount 总佣金
     * @param monthlyBonus 月度奖金
     * @param orderCount 订单数量
     */
    public void updateSummary(Money totalOrderAmount, CommissionRate commissionRate, Money commissionAmount, Money monthlyBonus, Integer orderCount) {
        validateUpdateParameters(totalOrderAmount, commissionRate, commissionAmount, monthlyBonus, orderCount);
        
        if (this.status == Status.COMPLETED) {
            throw new IllegalStateException("已完成结算的汇总不能更新");
        }
        
        this.totalOrderAmount = totalOrderAmount;
        this.commissionRate = commissionRate;
        this.commissionAmount = commissionAmount;
        this.monthlyBonus = monthlyBonus;
        this.totalAmount = commissionAmount.add(monthlyBonus);
        this.orderCount = orderCount;
    }
    
    /**
     * 验证更新参数
     */
    private void validateUpdateParameters(Money totalOrderAmount, CommissionRate commissionRate, Money commissionAmount, Money monthlyBonus, Integer orderCount) {
        if (totalOrderAmount == null) {
            throw new IllegalArgumentException("总订单金额不能为null");
        }
        if (commissionRate == null) {
            throw new IllegalArgumentException("佣金比例不能为null");
        }
        if (commissionAmount == null) {
            throw new IllegalArgumentException("总佣金不能为null");
        }
        if (monthlyBonus == null) {
            throw new IllegalArgumentException("月度奖金不能为null");
        }
        if (orderCount == null || orderCount < 0) {
            throw new IllegalArgumentException("订单数量不能为null或负数");
        }
    }
    
    /**
     * 开始结算
     */
    public void startSettlement() {
        if (this.status != Status.PENDING) {
            throw new IllegalStateException("只有待结算状态的汇总才能开始结算");
        }
        if (this.totalAmount.isZero()) {
            throw new IllegalStateException("总收益为零的汇总不能结算");
        }
        
        this.status = Status.PROCESSING;
    }
    
    /**
     * 完成结算
     * 
     * @param settledAt 结算时间
     */
    public void completeSettlement(LocalDateTime settledAt) {
        if (this.status != Status.PROCESSING) {
            throw new IllegalStateException("只有结算中状态的汇总才能完成结算");
        }
        if (settledAt == null) {
            throw new IllegalArgumentException("结算时间不能为null");
        }
        
        this.status = Status.COMPLETED;
        this.settledAt = settledAt;
    }
    
    /**
     * 取消结算
     * 
     * @param remark 取消原因
     */
    public void cancelSettlement(String remark) {
        if (this.status == Status.COMPLETED) {
            throw new IllegalStateException("已完成的结算不能取消");
        }
        
        this.status = Status.CANCELLED;
    }
    
    /**
     * 重置为待处理状态（用于取消结算后的状态恢复）
     */
    public void resetToPending() {
        if (this.status == Status.COMPLETED) {
            throw new IllegalStateException("已完成结算的汇总无法重置");
        }
        
        this.status = Status.PENDING;
        this.settledAt = null;
    }
    
    /**
     * 判断当前汇总是否可以结算
     *
     * @return 如果可以结算，返回true
     */
    public boolean canSettle() {
        return this.status == Status.PENDING && hasEarnings();
    }
    
    /**
     * 判断是否有收益
     *
     * @return 如果有收益，返回true
     */
    public boolean hasEarnings() {
        return this.totalAmount != null && this.totalAmount.isPositive();
    }
    
    /**
     * 汇总是否已结算
     *
     * @return 如果已结算，返回true
     */
    public boolean isSettled() {
        return this.status == Status.COMPLETED;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public Long getInviterId() {
        return inviterId;
    }
    
    public MonthKey getMonthKey() {
        return monthKey;
    }
    
    public Money getTotalOrderAmount() {
        return totalOrderAmount;
    }
    
    public CommissionRate getCommissionRate() {
        return commissionRate;
    }
    
    public Money getCommissionAmount() {
        return commissionAmount;
    }
    
    public Money getMonthlyBonus() {
        return monthlyBonus;
    }
    
    public Money getTotalAmount() {
        return totalAmount;
    }
    
    public Integer getOrderCount() {
        return orderCount;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public LocalDateTime getSettledAt() {
        return settledAt;
    }

    public LocalDateTime getLastCalculatedAt() {
        return lastCalculatedAt;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public void setLastCalculatedAt(LocalDateTime lastCalculatedAt) {
        this.lastCalculatedAt = lastCalculatedAt;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        MonthlyCommissionSummary that = (MonthlyCommissionSummary) obj;
        return Objects.equals(id, that.id) && 
               Objects.equals(inviterId, that.inviterId) && 
               Objects.equals(monthKey, that.monthKey);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, inviterId, monthKey);
    }
    
    @Override
    public String toString() {
        return "MonthlyCommissionSummary{" +
                "id=" + id +
                ", inviterId=" + inviterId +
                ", monthKey=" + monthKey +
                ", totalAmount=" + totalAmount +
                ", status=" + status +
                '}';
    }
} 