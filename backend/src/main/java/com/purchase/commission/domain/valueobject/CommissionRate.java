package com.purchase.commission.domain.valueobject;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 佣金比例值对象
 * 封装佣金比例相关的业务逻辑，确保比例计算的准确性
 */
public class CommissionRate {
    
    /**
     * 比例精度：保留4位小数
     */
    private static final int SCALE = 4;
    
    /**
     * 舍入模式：四舍五入
     */
    private static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;
    
    /**
     * 最小比例：0%
     */
    private static final BigDecimal MIN_RATE = BigDecimal.ZERO;
    
    /**
     * 最大比例：100%
     */
    private static final BigDecimal MAX_RATE = BigDecimal.ONE;
    
    /**
     * 比例值（小数形式，如0.0090表示0.9%）
     */
    private final BigDecimal rate;
    
    /**
     * 私有构造函数，确保通过工厂方法创建
     */
    private CommissionRate(BigDecimal rate) {
        this.rate = rate.setScale(SCALE, ROUNDING_MODE);
    }
    
    /**
     * 创建佣金比例对象（小数形式）
     * 
     * @param rate 比例值，如0.0090表示0.9%
     * @return CommissionRate对象
     * @throws IllegalArgumentException 如果比例值无效
     */
    public static CommissionRate of(BigDecimal rate) {
        if (rate == null) {
            throw new IllegalArgumentException("佣金比例不能为null");
        }
        if (rate.compareTo(MIN_RATE) < 0) {
            throw new IllegalArgumentException("佣金比例不能小于0%");
        }
        if (rate.compareTo(MAX_RATE) > 0) {
            throw new IllegalArgumentException("佣金比例不能大于100%");
        }
        return new CommissionRate(rate);
    }
    
    /**
     * 创建佣金比例对象（小数形式）
     * 
     * @param rate 比例值，如0.009表示0.9%
     * @return CommissionRate对象
     */
    public static CommissionRate of(double rate) {
        return of(BigDecimal.valueOf(rate));
    }
    
    /**
     * 创建佣金比例对象（小数形式）
     * 
     * @param rate 比例值字符串，如"0.009"表示0.9%
     * @return CommissionRate对象
     */
    public static CommissionRate of(String rate) {
        return of(new BigDecimal(rate));
    }
    
    /**
     * 创建佣金比例对象（百分比形式）
     * 
     * @param percentage 百分比值，如0.9表示0.9%
     * @return CommissionRate对象
     */
    public static CommissionRate ofPercentage(BigDecimal percentage) {
        if (percentage == null) {
            throw new IllegalArgumentException("百分比不能为null");
        }
        BigDecimal rate = percentage.divide(BigDecimal.valueOf(100), SCALE, ROUNDING_MODE);
        return of(rate);
    }
    
    /**
     * 创建佣金比例对象（百分比形式）
     * 
     * @param percentage 百分比值，如0.9表示0.9%
     * @return CommissionRate对象
     */
    public static CommissionRate ofPercentage(double percentage) {
        return ofPercentage(BigDecimal.valueOf(percentage));
    }
    
    /**
     * 创建零比例
     * 
     * @return 零比例对象
     */
    public static CommissionRate zero() {
        return new CommissionRate(BigDecimal.ZERO);
    }
    
    /**
     * 计算佣金金额
     * 
     * @param amount 基础金额
     * @return 佣金金额
     */
    public Money calculateCommission(Money amount) {
        if (amount == null) {
            throw new IllegalArgumentException("金额不能为null");
        }
        return amount.multiply(this.rate);
    }
    
    /**
     * 比较比例大小
     * 
     * @param other 另一个比例
     * @return 负数表示小于，0表示等于，正数表示大于
     */
    public int compareTo(CommissionRate other) {
        if (other == null) {
            return 1;
        }
        return this.rate.compareTo(other.rate);
    }
    
    /**
     * 判断是否大于另一个比例
     * 
     * @param other 另一个比例
     * @return true表示大于
     */
    public boolean greaterThan(CommissionRate other) {
        return compareTo(other) > 0;
    }
    
    /**
     * 判断是否大于等于另一个比例
     * 
     * @param other 另一个比例
     * @return true表示大于等于
     */
    public boolean greaterThanOrEqual(CommissionRate other) {
        return compareTo(other) >= 0;
    }
    
    /**
     * 判断是否小于另一个比例
     * 
     * @param other 另一个比例
     * @return true表示小于
     */
    public boolean lessThan(CommissionRate other) {
        return compareTo(other) < 0;
    }
    
    /**
     * 判断是否小于等于另一个比例
     * 
     * @param other 另一个比例
     * @return true表示小于等于
     */
    public boolean lessThanOrEqual(CommissionRate other) {
        return compareTo(other) <= 0;
    }
    
    /**
     * 判断是否为零
     * 
     * @return true表示为零
     */
    public boolean isZero() {
        return this.rate.compareTo(BigDecimal.ZERO) == 0;
    }
    
    /**
     * 判断是否为正数
     * 
     * @return true表示为正数
     */
    public boolean isPositive() {
        return this.rate.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * 获取比例值（小数形式）
     * 
     * @return 比例值
     */
    public BigDecimal getRate() {
        return rate;
    }
    
    /**
     * 获取百分比值
     * 
     * @return 百分比值，如0.9%返回0.9
     */
    public BigDecimal getPercentage() {
        return rate.multiply(BigDecimal.valueOf(100));
    }
    
    /**
     * 获取百分比字符串表示
     * 
     * @return 百分比字符串，如"0.90%"
     */
    public String getPercentageString() {
        return String.format("%.2f%%", getPercentage());
    }
    
    /**
     * 获取double值
     * 
     * @return double值
     */
    public double doubleValue() {
        return rate.doubleValue();
    }
    
    /**
     * 获取字符串表示
     * 
     * @return 比例字符串
     */
    @Override
    public String toString() {
        return getPercentageString();
    }
    
    /**
     * 判断相等性
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        CommissionRate that = (CommissionRate) obj;
        return Objects.equals(rate, that.rate);
    }
    
    /**
     * 计算哈希值
     */
    @Override
    public int hashCode() {
        return Objects.hash(rate);
    }
} 