package com.purchase.commission.domain.repository;

import com.purchase.commission.domain.entity.CommissionConfig;

import java.util.List;
import java.util.Optional;

/**
 * 佣金配置仓储接口
 * 定义佣金配置的数据访问操作
 */
public interface CommissionConfigRepository {
    
    /**
     * 保存佣金配置
     * 
     * @param commissionConfig 佣金配置
     * @return 保存后的佣金配置
     */
    CommissionConfig save(CommissionConfig commissionConfig);
    
    /**
     * 根据ID查找佣金配置
     * 
     * @param id 配置ID
     * @return 佣金配置
     */
    Optional<CommissionConfig> findById(Long id);
    
    /**
     * 查找所有启用的佣金比例配置
     * 按排序顺序返回
     * 
     * @return 佣金比例配置列表
     */
    List<CommissionConfig> findEnabledCommissionRates();
    
    /**
     * 查找所有启用的月度奖金配置
     * 按排序顺序返回
     * 
     * @return 月度奖金配置列表
     */
    List<CommissionConfig> findEnabledMonthlyBonuses();
    
    /**
     * 根据配置类型查找所有启用的配置
     * 按排序顺序返回
     * 
     * @param configType 配置类型
     * @return 配置列表
     */
    List<CommissionConfig> findEnabledByConfigType(CommissionConfig.ConfigType configType);
    
    /**
     * 查找所有配置
     * 
     * @return 所有配置列表
     */
    List<CommissionConfig> findAll();
    
    /**
     * 根据ID删除配置
     * 
     * @param id 配置ID
     */
    void deleteById(Long id);
    
    /**
     * 检查配置是否存在
     * 
     * @param id 配置ID
     * @return 是否存在
     */
    boolean existsById(Long id);
}
