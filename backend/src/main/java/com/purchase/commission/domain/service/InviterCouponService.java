package com.purchase.commission.domain.service;

import com.purchase.commission.domain.entity.InviterCoupon;
import com.purchase.commission.domain.valueobject.Money;
import com.purchase.commission.domain.valueobject.MonthKey;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 邀请人优惠券领域服务
 * 封装优惠券生成和管理的业务逻辑
 *
 * 注意：买家邀请人优惠券生成功能暂时未开放
 * 所有方法都已实现并经过测试，但实际调用已被注释
 * 保留完整代码以备将来启用此功能
 */
@Service
public class InviterCouponService {
    
    /**
     * 为买家邀请人生成优惠券
     * 根据订单金额计算优惠券价值
     *
     * 注意：此功能暂时未开放，但方法已完整实现并经过测试
     * 当买家邀请其他用户下单时，会为买家生成优惠券而非佣金
     *
     * @param inviterId 邀请人ID（买家）
     * @param inviteeId 被邀请者ID（订单创建者）
     * @param triggerOrderId 触发订单ID
     * @param orderAmount 订单金额
     * @param orderType 订单类型
     * @param monthKey 月份键
     * @return 生成的优惠券
     */
    public InviterCoupon generateCouponForBuyerInviter(Long inviterId, Long inviteeId, Long triggerOrderId,
                                                      Money orderAmount, String orderType, MonthKey monthKey) {
        if (inviterId == null) {
            throw new IllegalArgumentException("邀请人ID不能为null");
        }
        if (inviteeId == null) {
            throw new IllegalArgumentException("被邀请者ID不能为null");
        }
        if (triggerOrderId == null) {
            throw new IllegalArgumentException("触发订单ID不能为null");
        }
        if (orderAmount == null || !orderAmount.isPositive()) {
            throw new IllegalArgumentException("订单金额必须大于零");
        }
        if (monthKey == null) {
            throw new IllegalArgumentException("月份键不能为null");
        }
        
        // 生成唯一的优惠券代码
        String couponCode = generateCouponCode(inviterId, triggerOrderId);
        
        // 根据订单金额计算优惠券价值
        Money couponValue = calculateCouponValue(orderAmount, orderType);
        
        // 设置最低使用金额（通常是优惠券价值的10倍）
        Money minimumAmount = couponValue.multiply(10.0);
        
        // 设置有效期（3个月）
        LocalDateTime validFrom = LocalDateTime.now();
        LocalDateTime validUntil = validFrom.plusMonths(3);
        
        // 创建固定金额优惠券
        return InviterCoupon.createFixedAmountCoupon(
            inviterId, inviteeId, triggerOrderId, couponCode, couponValue, 
            minimumAmount, monthKey, validFrom, validUntil
        );
    }
    
    /**
     * 生成优惠券代码
     * 格式：INV-{inviterId}-{timestamp}-{random}
     * 
     * @param inviterId 邀请人ID
     * @param triggerOrderId 触发订单ID
     * @return 优惠券代码
     */
    private String generateCouponCode(Long inviterId, Long triggerOrderId) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String random = UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        return String.format("INV-%d-%s-%s", inviterId, timestamp, random);
    }
    
    /**
     * 根据订单金额和类型计算优惠券价值
     * 
     * @param orderAmount 订单金额
     * @param orderType 订单类型
     * @return 优惠券价值
     */
    private Money calculateCouponValue(Money orderAmount, String orderType) {
        // 基础优惠比例
        double baseRate = getBaseCouponRate(orderType);
        
        // 根据订单金额阶梯调整优惠比例
        double adjustedRate = adjustRateByAmount(orderAmount, baseRate);
        
        // 计算优惠券价值
        Money couponValue = orderAmount.multiply(adjustedRate);
        
        // 设置最小和最大优惠券价值
        Money minValue = Money.of("10.00");  // 最小10元
        Money maxValue = Money.of("500.00"); // 最大500元
        
        if (couponValue.lessThan(minValue)) {
            return minValue;
        }
        if (couponValue.greaterThan(maxValue)) {
            return maxValue;
        }
        
        return couponValue;
    }
    
    /**
     * 获取基础优惠比例
     * 
     * @param orderType 订单类型
     * @return 基础优惠比例
     */
    private double getBaseCouponRate(String orderType) {
        return switch (orderType.toLowerCase()) {
            case "purchase" -> 0.005;   // 采购订单：0.5%
            case "logistics" -> 0.003;  // 货代订单：0.3%
            default -> 0.002;           // 其他订单：0.2%
        };
    }
    
    /**
     * 根据订单金额调整优惠比例
     * 
     * @param orderAmount 订单金额
     * @param baseRate 基础比例
     * @return 调整后的比例
     */
    private double adjustRateByAmount(Money orderAmount, double baseRate) {
        // 订单金额越大，优惠比例越高
        if (orderAmount.greaterThanOrEqual(Money.of("100000"))) {
            return baseRate * 1.5;  // 10万以上：1.5倍
        } else if (orderAmount.greaterThanOrEqual(Money.of("50000"))) {
            return baseRate * 1.3;  // 5万以上：1.3倍
        } else if (orderAmount.greaterThanOrEqual(Money.of("20000"))) {
            return baseRate * 1.2;  // 2万以上：1.2倍
        } else if (orderAmount.greaterThanOrEqual(Money.of("10000"))) {
            return baseRate * 1.1;  // 1万以上：1.1倍
        } else {
            return baseRate;        // 1万以下：基础比例
        }
    }
    
    /**
     * 验证优惠券是否可以使用
     * 
     * @param coupon 优惠券
     * @param orderAmount 订单金额
     * @param userId 使用用户ID
     * @return 验证结果
     */
    public CouponValidationResult validateCouponUsage(InviterCoupon coupon, Money orderAmount, Long userId) {
        if (coupon == null) {
            return CouponValidationResult.failure("优惠券不存在");
        }
        
        if (!coupon.getInviterId().equals(userId)) {
            return CouponValidationResult.failure("优惠券不属于当前用户");
        }
        
        if (!coupon.isUsable()) {
            return CouponValidationResult.failure("优惠券不可用：" + coupon.getStatus().getDescription());
        }
        
        if (coupon.getMinimumAmount() != null && orderAmount.lessThan(coupon.getMinimumAmount())) {
            return CouponValidationResult.failure(
                String.format("订单金额不满足最低使用要求：%s", coupon.getMinimumAmount())
            );
        }
        
        Money discountAmount = coupon.calculateDiscount(orderAmount);
        return CouponValidationResult.success(discountAmount);
    }
    
    /**
     * 优惠券验证结果
     */
    public static class CouponValidationResult {
        private final boolean valid;
        private final String message;
        private final Money discountAmount;
        
        private CouponValidationResult(boolean valid, String message, Money discountAmount) {
            this.valid = valid;
            this.message = message;
            this.discountAmount = discountAmount;
        }
        
        public static CouponValidationResult success(Money discountAmount) {
            return new CouponValidationResult(true, "验证成功", discountAmount);
        }
        
        public static CouponValidationResult failure(String message) {
            return new CouponValidationResult(false, message, Money.zero());
        }
        
        public boolean isValid() { return valid; }
        public String getMessage() { return message; }
        public Money getDiscountAmount() { return discountAmount; }
    }
    
    /**
     * 计算优惠券的实际价值
     * 用于统计和报告
     * 
     * @param coupon 优惠券
     * @return 实际价值描述
     */
    public String getCouponValueDescription(InviterCoupon coupon) {
        if (coupon.getCouponType() == InviterCoupon.CouponType.FIXED_AMOUNT) {
            return String.format("固定优惠：%s元", coupon.getDiscountValue());
        } else {
            return String.format("折扣优惠：%.1f%%（最高%s元）", 
                coupon.getDiscountValue().doubleValue() * 100,
                coupon.getMaximumDiscount());
        }
    }
    
    /**
     * 检查优惠券是否即将过期
     * 
     * @param coupon 优惠券
     * @param daysBeforeExpiry 过期前天数
     * @return 是否即将过期
     */
    public boolean isExpiringSoon(InviterCoupon coupon, int daysBeforeExpiry) {
        if (coupon.getStatus() != InviterCoupon.Status.ACTIVE) {
            return false;
        }
        
        LocalDateTime expiryThreshold = LocalDateTime.now().plusDays(daysBeforeExpiry);
        return coupon.getValidUntil().isBefore(expiryThreshold);
    }
}
