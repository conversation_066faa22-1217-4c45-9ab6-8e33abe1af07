package com.purchase.commission.domain.entity;

import com.purchase.commission.domain.valueobject.Money;
import com.purchase.commission.domain.valueobject.CommissionRate;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 佣金配置领域实体
 * 封装佣金配置的业务逻辑和规则
 */
public class CommissionConfig {
    
    /**
     * 配置类型枚举
     */
    public enum ConfigType {
        COMMISSION_RATE("commission_rate", "佣金比例"),
        MONTHLY_BONUS("monthly_bonus", "月度奖金");
        
        private final String code;
        private final String description;
        
        ConfigType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static ConfigType fromCode(String code) {
            for (ConfigType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("未知的配置类型: " + code);
        }
    }
    
    /**
     * 配置状态枚举
     */
    public enum Status {
        DISABLED(0, "禁用"),
        ENABLED(1, "启用");
        
        private final int code;
        private final String description;
        
        Status(int code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public int getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static Status fromCode(int code) {
            for (Status status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("未知的状态码: " + code);
        }
    }
    
    // 实体属性
    private Long id;
    private ConfigType configType;
    private Money minAmount;
    private Money maxAmount;
    private CommissionRate rateValue;
    private Money bonusAmount;
    private String description;
    private Status status;
    private Integer sortOrder;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 构造函数
    public CommissionConfig() {}
    
    public CommissionConfig(Long id, ConfigType configType, Money minAmount, Money maxAmount,
                           CommissionRate rateValue, Money bonusAmount, String description,
                           Status status, Integer sortOrder, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.configType = configType;
        this.minAmount = minAmount;
        this.maxAmount = maxAmount;
        this.rateValue = rateValue;
        this.bonusAmount = bonusAmount;
        this.description = description;
        this.status = status;
        this.sortOrder = sortOrder;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    
    // 业务方法
    
    /**
     * 检查是否为佣金比例配置
     */
    public boolean isCommissionRateConfig() {
        return ConfigType.COMMISSION_RATE.equals(this.configType);
    }
    
    /**
     * 检查是否为月度奖金配置
     */
    public boolean isMonthlyBonusConfig() {
        return ConfigType.MONTHLY_BONUS.equals(this.configType);
    }
    
    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return Status.ENABLED.equals(this.status);
    }
    
    /**
     * 检查金额是否在配置范围内
     */
    public boolean isAmountInRange(Money amount) {
        if (amount == null) {
            return false;
        }

        boolean aboveMin = minAmount == null || amount.greaterThanOrEqual(minAmount);
        boolean belowMax = maxAmount == null || amount.lessThanOrEqual(maxAmount);

        return aboveMin && belowMax;
    }
    
    /**
     * 获取适用的佣金比例
     * 仅对佣金比例配置有效
     */
    public CommissionRate getApplicableRate() {
        if (!isCommissionRateConfig()) {
            throw new IllegalStateException("只有佣金比例配置才能获取比例值");
        }
        return rateValue;
    }
    
    /**
     * 获取适用的奖金金额
     * 仅对月度奖金配置有效
     */
    public Money getApplicableBonus() {
        if (!isMonthlyBonusConfig()) {
            throw new IllegalStateException("只有月度奖金配置才能获取奖金金额");
        }
        return bonusAmount;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public ConfigType getConfigType() {
        return configType;
    }
    
    public void setConfigType(ConfigType configType) {
        this.configType = configType;
    }
    
    public Money getMinAmount() {
        return minAmount;
    }
    
    public void setMinAmount(Money minAmount) {
        this.minAmount = minAmount;
    }
    
    public Money getMaxAmount() {
        return maxAmount;
    }
    
    public void setMaxAmount(Money maxAmount) {
        this.maxAmount = maxAmount;
    }
    
    public CommissionRate getRateValue() {
        return rateValue;
    }
    
    public void setRateValue(CommissionRate rateValue) {
        this.rateValue = rateValue;
    }
    
    public Money getBonusAmount() {
        return bonusAmount;
    }
    
    public void setBonusAmount(Money bonusAmount) {
        this.bonusAmount = bonusAmount;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // equals, hashCode, toString
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CommissionConfig that = (CommissionConfig) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "CommissionConfig{" +
                "id=" + id +
                ", configType=" + configType +
                ", minAmount=" + minAmount +
                ", maxAmount=" + maxAmount +
                ", rateValue=" + rateValue +
                ", bonusAmount=" + bonusAmount +
                ", description='" + description + '\'' +
                ", status=" + status +
                ", sortOrder=" + sortOrder +
                '}';
    }
}
