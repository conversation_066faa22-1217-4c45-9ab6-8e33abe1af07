package com.purchase.commission.domain.valueobject;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Objects;

/**
 * 月份键值对象
 * 封装月份标识的业务逻辑，确保月份格式的一致性
 */
public class MonthKey {
    
    /**
     * 月份格式：YYYY-MM
     */
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");
    
    /**
     * 月份值
     */
    private final String value;
    
    /**
     * 年月对象
     */
    private final YearMonth yearMonth;
    
    /**
     * 私有构造函数
     */
    private MonthKey(YearMonth yearMonth) {
        this.yearMonth = yearMonth;
        this.value = yearMonth.format(FORMATTER);
    }
    
    /**
     * 根据年月创建月份键
     * 
     * @param year 年份
     * @param month 月份
     * @return MonthKey对象
     */
    public static MonthKey of(int year, int month) {
        return new MonthKey(YearMonth.of(year, month));
    }
    
    /**
     * 根据日期创建月份键
     * 
     * @param date 日期
     * @return MonthKey对象
     */
    public static MonthKey of(LocalDate date) {
        if (date == null) {
            throw new IllegalArgumentException("日期不能为null");
        }
        return new MonthKey(YearMonth.from(date));
    }
    
    /**
     * 根据YearMonth创建月份键
     * 
     * @param yearMonth 年月对象
     * @return MonthKey对象
     */
    public static MonthKey of(YearMonth yearMonth) {
        if (yearMonth == null) {
            throw new IllegalArgumentException("年月不能为null");
        }
        return new MonthKey(yearMonth);
    }
    
    /**
     * 根据字符串创建月份键
     * 
     * @param monthKey 月份字符串，格式：YYYY-MM
     * @return MonthKey对象
     * @throws IllegalArgumentException 如果格式不正确
     */
    public static MonthKey of(String monthKey) {
        if (monthKey == null || monthKey.trim().isEmpty()) {
            throw new IllegalArgumentException("月份键不能为空");
        }
        
        try {
            YearMonth yearMonth = YearMonth.parse(monthKey.trim(), FORMATTER);
            return new MonthKey(yearMonth);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("月份键格式不正确，期望格式：YYYY-MM，实际：" + monthKey, e);
        }
    }
    
    /**
     * 获取当前月份的月份键
     * 
     * @return 当前月份的MonthKey对象
     */
    public static MonthKey current() {
        return new MonthKey(YearMonth.now());
    }
    
    /**
     * 获取上个月的月份键
     * 
     * @return 上个月的MonthKey对象
     */
    public MonthKey previousMonth() {
        return new MonthKey(this.yearMonth.minusMonths(1));
    }
    
    /**
     * 获取下个月的月份键
     * 
     * @return 下个月的MonthKey对象
     */
    public MonthKey nextMonth() {
        return new MonthKey(this.yearMonth.plusMonths(1));
    }
    
    /**
     * 减去指定月数
     * 
     * @param months 月数
     * @return 新的MonthKey对象
     */
    public MonthKey minusMonths(long months) {
        return new MonthKey(this.yearMonth.minusMonths(months));
    }
    
    /**
     * 加上指定月数
     * 
     * @param months 月数
     * @return 新的MonthKey对象
     */
    public MonthKey plusMonths(long months) {
        return new MonthKey(this.yearMonth.plusMonths(months));
    }
    
    /**
     * 判断是否在指定月份之前
     * 
     * @param other 另一个月份键
     * @return true表示在之前
     */
    public boolean isBefore(MonthKey other) {
        if (other == null) {
            return false;
        }
        return this.yearMonth.isBefore(other.yearMonth);
    }
    
    /**
     * 判断是否在指定月份之后
     * 
     * @param other 另一个月份键
     * @return true表示在之后
     */
    public boolean isAfter(MonthKey other) {
        if (other == null) {
            return true;
        }
        return this.yearMonth.isAfter(other.yearMonth);
    }
    
    /**
     * 比较月份大小
     * 
     * @param other 另一个月份键
     * @return 负数表示小于，0表示等于，正数表示大于
     */
    public int compareTo(MonthKey other) {
        if (other == null) {
            return 1;
        }
        return this.yearMonth.compareTo(other.yearMonth);
    }
    
    /**
     * 获取年份
     * 
     * @return 年份
     */
    public int getYear() {
        return yearMonth.getYear();
    }
    
    /**
     * 获取月份
     * 
     * @return 月份
     */
    public int getMonth() {
        return yearMonth.getMonthValue();
    }
    
    /**
     * 获取YearMonth对象
     * 
     * @return YearMonth对象
     */
    public YearMonth getYearMonth() {
        return yearMonth;
    }
    
    /**
     * 获取该月的第一天
     * 
     * @return 该月第一天的日期
     */
    public LocalDate getFirstDayOfMonth() {
        return yearMonth.atDay(1);
    }
    
    /**
     * 获取该月的最后一天
     * 
     * @return 该月最后一天的日期
     */
    public LocalDate getLastDayOfMonth() {
        return yearMonth.atEndOfMonth();
    }
    
    /**
     * 获取月份键值
     * 
     * @return 月份键字符串
     */
    public String getValue() {
        return value;
    }
    
    /**
     * 获取字符串表示
     * 
     * @return 月份键字符串
     */
    @Override
    public String toString() {
        return value;
    }
    
    /**
     * 判断相等性
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        MonthKey monthKey = (MonthKey) obj;
        return Objects.equals(value, monthKey.value);
    }
    
    /**
     * 计算哈希值
     */
    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
} 