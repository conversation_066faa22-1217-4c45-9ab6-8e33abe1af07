package com.purchase.commission.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 月度汇总数据值对象
 * 封装月度佣金汇总计算的结果数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonthlySummaryData {
    
    /**
     * 月度总订单金额
     */
    private Money totalOrderAmount;
    
    /**
     * 月度总佣金金额
     */
    private Money totalCommission;
    
    /**
     * 适用的佣金比例
     */
    private BigDecimal commissionRate;
    
    /**
     * 月度奖金
     */
    private Money monthlyBonus;
    
    /**
     * 佣金记录数量
     */
    private Integer recordCount;
    
    /**
     * 月度总收益（佣金 + 奖金）
     */
    public Money getTotalEarnings() {
        if (totalCommission == null && monthlyBonus == null) {
            return Money.zero();
        }
        if (totalCommission == null) {
            return monthlyBonus;
        }
        if (monthlyBonus == null) {
            return totalCommission;
        }
        return totalCommission.add(monthlyBonus);
    }
    
    /**
     * 获取佣金比例百分比字符串
     */
    public String getCommissionRatePercentage() {
        if (commissionRate == null) {
            return "0.00%";
        }
        return commissionRate.multiply(new BigDecimal("100")).setScale(2) + "%";
    }
    
    /**
     * 检查数据是否有效
     */
    public boolean isValid() {
        return totalOrderAmount != null && 
               totalCommission != null && 
               commissionRate != null && 
               monthlyBonus != null && 
               recordCount != null && 
               recordCount > 0;
    }
    
    /**
     * 获取平均订单金额
     */
    public Money getAverageOrderAmount() {
        if (totalOrderAmount == null || recordCount == null || recordCount == 0) {
            return Money.zero();
        }
        return totalOrderAmount.divide(new BigDecimal(recordCount));
    }
    
    /**
     * 获取平均佣金金额
     */
    public Money getAverageCommission() {
        if (totalCommission == null || recordCount == null || recordCount == 0) {
            return Money.zero();
        }
        return totalCommission.divide(new BigDecimal(recordCount));
    }
    
    /**
     * 创建空的汇总数据
     */
    public static MonthlySummaryData empty() {
        return new MonthlySummaryData(
            Money.zero(),
            Money.zero(),
            BigDecimal.ZERO,
            Money.zero(),
            0
        );
    }
    
    /**
     * 创建汇总数据
     */
    public static MonthlySummaryData of(Money totalOrderAmount, 
                                       Money totalCommission, 
                                       BigDecimal commissionRate, 
                                       Money monthlyBonus, 
                                       Integer recordCount) {
        return new MonthlySummaryData(
            totalOrderAmount,
            totalCommission,
            commissionRate,
            monthlyBonus,
            recordCount
        );
    }
    
    @Override
    public String toString() {
        return String.format("MonthlySummaryData{订单总额=%s, 佣金总额=%s, 佣金比例=%s, 月度奖金=%s, 记录数=%d, 总收益=%s}",
                totalOrderAmount != null ? totalOrderAmount.toString() : "0",
                totalCommission != null ? totalCommission.toString() : "0",
                getCommissionRatePercentage(),
                monthlyBonus != null ? monthlyBonus.toString() : "0",
                recordCount != null ? recordCount : 0,
                getTotalEarnings().toString());
    }
}
