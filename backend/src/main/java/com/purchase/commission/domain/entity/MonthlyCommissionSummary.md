# MonthlyCommissionSummary.java

## 文件概述 (File Overview)
`MonthlyCommissionSummary.java` 是月度佣金汇总领域实体，位于 `com.purchase.commission.domain.entity` 包中。该实体遵循DDD（领域驱动设计）原则，封装了月度佣金汇总的业务逻辑和结算管理功能。作为聚合根，它管理着邀请者月度佣金的完整生命周期，包括汇总计算、结算状态管理、业务规则验证等。该实体包含丰富的业务方法和状态转换逻辑，确保佣金结算过程的正确性和数据一致性。

## 核心功能 (Core Functionality)
*   **汇总数据管理**: 管理邀请者月度佣金的汇总数据，包括订单金额、佣金金额、奖金等
*   **状态生命周期**: 管理结算状态的完整生命周期（待结算→结算中→已完成/已取消）
*   **业务规则验证**: 内置业务规则验证，确保状态转换和数据更新的合法性
*   **结算流程控制**: 提供结算流程的启动、完成、取消等操作
*   **数据完整性**: 通过值对象和验证逻辑确保数据的完整性和一致性
*   **领域事件**: 支持领域事件的发布，实现与其他聚合的解耦
*   **不变性保护**: 通过封装和验证保护实体的不变性条件
*   **审计追踪**: 记录创建时间、更新时间、结算时间等审计信息

## 接口说明 (Interface Description)

### 状态枚举

#### Status - 结算状态枚举
*   **PENDING**: 待结算状态，汇总数据已生成但尚未开始结算
*   **PROCESSING**: 结算中状态，结算流程已启动但尚未完成
*   **COMPLETED**: 已完成状态，结算流程已成功完成
*   **CANCELLED**: 已取消状态，结算流程被取消或失败

### 核心业务方法

#### create - 创建月度佣金汇总
*   **方法签名**: `static MonthlyCommissionSummary create(Long inviterId, MonthKey monthKey)`
*   **参数**: 
    *   `inviterId` (Long) - 邀请者ID，不能为null
    *   `monthKey` (MonthKey) - 月份键值对象
*   **返回值**: `MonthlyCommissionSummary` - 新创建的汇总实体
*   **业务逻辑**: 
    *   验证参数有效性
    *   初始化实体状态为PENDING
    *   设置创建时间和更新时间
    *   返回新的汇总实体实例

#### updateSummary - 更新汇总数据
*   **方法签名**: `void updateSummary(Money totalOrderAmount, CommissionRate commissionRate, Money commissionAmount, Money monthlyBonus, Integer orderCount)`
*   **参数**: 
    *   `totalOrderAmount` (Money) - 订单总金额
    *   `commissionRate` (CommissionRate) - 佣金费率
    *   `commissionAmount` (Money) - 佣金金额
    *   `monthlyBonus` (Money) - 月度奖金
    *   `orderCount` (Integer) - 订单数量
*   **业务逻辑**: 
    *   验证实体状态（已完成的汇总不能更新）
    *   验证参数有效性
    *   更新汇总数据
    *   计算总金额（佣金+奖金）
    *   更新最后计算时间

#### startSettlement - 开始结算
*   **方法签名**: `void startSettlement()`
*   **业务逻辑**: 
    *   验证当前状态必须为PENDING
    *   将状态转换为PROCESSING
    *   更新最后更新时间
    *   可能发布结算开始事件

#### completeSettlement - 完成结算
*   **方法签名**: `void completeSettlement(LocalDateTime settledAt)`
*   **参数**: `settledAt` (LocalDateTime) - 结算完成时间
*   **业务逻辑**: 
    *   验证当前状态必须为PROCESSING
    *   将状态转换为COMPLETED
    *   设置结算完成时间
    *   更新最后更新时间
    *   可能发布结算完成事件

#### cancelSettlement - 取消结算
*   **方法签名**: `void cancelSettlement(String remark)`
*   **参数**: `remark` (String) - 取消原因备注
*   **业务逻辑**: 
    *   验证状态（已完成的结算不能取消）
    *   将状态转换为CANCELLED
    *   记录取消原因
    *   更新最后更新时间

#### resetToPending - 重置为待结算状态
*   **方法签名**: `void resetToPending()`
*   **业务逻辑**: 
    *   验证状态（已完成的汇总无法重置）
    *   将状态重置为PENDING
    *   清除结算时间
    *   更新最后更新时间

### 查询方法

#### canSettle - 是否可以结算
*   **方法签名**: `boolean canSettle()`
*   **返回值**: `boolean` - 是否可以进行结算
*   **业务逻辑**: 
    *   检查状态是否为PENDING
    *   检查是否有收益（总金额大于0）
    *   返回是否满足结算条件

#### hasEarnings - 是否有收益
*   **方法签名**: `boolean hasEarnings()`
*   **返回值**: `boolean` - 是否有收益
*   **业务逻辑**: 
    *   检查总金额是否不为null且大于0
    *   用于判断是否值得进行结算

#### isSettled - 是否已结算
*   **方法签名**: `boolean isSettled()`
*   **返回值**: `boolean` - 是否已完成结算
*   **业务逻辑**: 
    *   检查状态是否为COMPLETED
    *   用于快速判断结算状态

### 属性访问方法

#### 核心属性Getter方法
*   `getId()` - 获取汇总记录ID
*   `getInviterId()` - 获取邀请者ID
*   `getMonthKey()` - 获取月份键值
*   `getTotalOrderAmount()` - 获取订单总金额
*   `getCommissionRate()` - 获取佣金费率
*   `getCommissionAmount()` - 获取佣金金额
*   `getMonthlyBonus()` - 获取月度奖金
*   `getTotalAmount()` - 获取总金额（佣金+奖金）
*   `getOrderCount()` - 获取订单数量
*   `getStatus()` - 获取结算状态

#### 时间属性Getter方法
*   `getSettledAt()` - 获取结算完成时间
*   `getLastCalculatedAt()` - 获取最后计算时间
*   `getCreatedAt()` - 获取创建时间
*   `getUpdatedAt()` - 获取更新时间

#### 有限的Setter方法
*   `setId(Long id)` - 设置ID（通常由持久化框架使用）
*   `setCreatedAt(LocalDateTime createdAt)` - 设置创建时间
*   `setUpdatedAt(LocalDateTime updatedAt)` - 设置更新时间
*   `setLastCalculatedAt(LocalDateTime lastCalculatedAt)` - 设置最后计算时间

## 使用示例 (Usage Examples)

```java
// 1. 在领域服务中创建和管理汇总
@Service
public class MonthlyCommissionSummaryDomainService {
    
    @Autowired
    private MonthlyCommissionSummaryRepository summaryRepository;
    
    @Autowired
    private CommissionRecordRepository recordRepository;
    
    // 创建或更新月度汇总
    public MonthlyCommissionSummary createOrUpdateMonthlySummary(Long inviterId, MonthKey monthKey) {
        // 查找现有汇总
        Optional<MonthlyCommissionSummary> existingSummary = 
            summaryRepository.findByInviterIdAndMonthKey(inviterId, monthKey);
        
        MonthlyCommissionSummary summary;
        if (existingSummary.isPresent()) {
            summary = existingSummary.get();
        } else {
            // 创建新汇总
            summary = MonthlyCommissionSummary.create(inviterId, monthKey);
        }
        
        // 计算汇总数据
        List<CommissionRecord> records = recordRepository
            .findConfirmedRecordsByInviterAndMonth(inviterId, monthKey);
        
        Money totalOrderAmount = records.stream()
            .map(CommissionRecord::getOrderAmount)
            .reduce(Money.zero(), Money::add);
        
        Money commissionAmount = records.stream()
            .map(CommissionRecord::getCommissionAmount)
            .reduce(Money.zero(), Money::add);
        
        CommissionRate avgRate = calculateAverageRate(records);
        Money monthlyBonus = calculateMonthlyBonus(inviterId, monthKey, totalOrderAmount);
        Integer orderCount = records.size();
        
        // 更新汇总数据
        summary.updateSummary(totalOrderAmount, avgRate, commissionAmount, monthlyBonus, orderCount);
        
        // 保存汇总
        return summaryRepository.save(summary);
    }
    
    // 批量开始结算
    @Transactional
    public List<MonthlyCommissionSummary> batchStartSettlement(List<Long> summaryIds) {
        List<MonthlyCommissionSummary> summaries = summaryRepository.findByIds(summaryIds);
        List<MonthlyCommissionSummary> startedSummaries = new ArrayList<>();
        
        for (MonthlyCommissionSummary summary : summaries) {
            try {
                if (summary.canSettle()) {
                    summary.startSettlement();
                    startedSummaries.add(summary);
                    log.info("开始结算: inviterId={}, monthKey={}", 
                            summary.getInviterId(), summary.getMonthKey());
                } else {
                    log.warn("汇总不满足结算条件: id={}, status={}, hasEarnings={}", 
                            summary.getId(), summary.getStatus(), summary.hasEarnings());
                }
            } catch (Exception e) {
                log.error("开始结算失败: id={}", summary.getId(), e);
            }
        }
        
        return summaryRepository.saveAll(startedSummaries);
    }
}

// 2. 在应用服务中使用
@Service
public class CommissionSettlementApplicationService {
    
    @Autowired
    private MonthlyCommissionSummaryDomainService domainService;
    
    @Autowired
    private PaymentService paymentService;
    
    // 执行结算流程
    @Transactional
    public SettlementResult executeSettlement(Long summaryId) {
        MonthlyCommissionSummary summary = summaryRepository.findById(summaryId)
            .orElseThrow(() -> new EntityNotFoundException("汇总记录不存在"));
        
        SettlementResult result = new SettlementResult();
        result.setSummaryId(summaryId);
        
        try {
            // 1. 开始结算
            summary.startSettlement();
            summaryRepository.save(summary);
            
            // 2. 执行支付
            PaymentRequest paymentRequest = new PaymentRequest();
            paymentRequest.setRecipientId(summary.getInviterId());
            paymentRequest.setAmount(summary.getTotalAmount());
            paymentRequest.setDescription("月度佣金结算 - " + summary.getMonthKey());
            
            PaymentResult paymentResult = paymentService.processPayment(paymentRequest);
            
            if (paymentResult.isSuccess()) {
                // 3. 完成结算
                summary.completeSettlement(LocalDateTime.now());
                summaryRepository.save(summary);
                
                result.setSuccess(true);
                result.setPaymentId(paymentResult.getPaymentId());
                
                log.info("结算完成: summaryId={}, paymentId={}", 
                        summaryId, paymentResult.getPaymentId());
            } else {
                // 支付失败，取消结算
                summary.cancelSettlement("支付失败: " + paymentResult.getErrorMessage());
                summaryRepository.save(summary);
                
                result.setSuccess(false);
                result.setErrorMessage("支付失败: " + paymentResult.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("结算过程异常: summaryId={}", summaryId, e);
            
            try {
                summary.cancelSettlement("系统异常: " + e.getMessage());
                summaryRepository.save(summary);
            } catch (Exception ex) {
                log.error("取消结算失败: summaryId={}", summaryId, ex);
            }
            
            result.setSuccess(false);
            result.setErrorMessage("系统异常: " + e.getMessage());
        }
        
        return result;
    }
}

// 3. 在控制器中使用
@RestController
@RequestMapping("/api/v1/commission/monthly-summary")
public class MonthlyCommissionSummaryController {
    
    @Autowired
    private CommissionSettlementApplicationService settlementService;
    
    @Autowired
    private MonthlyCommissionSummaryRepository summaryRepository;
    
    // 查询邀请者的月度汇总
    @GetMapping("/inviter/{inviterId}")
    public Result<List<MonthlyCommissionSummaryVO>> getInviterSummaries(
            @PathVariable Long inviterId,
            @RequestParam(required = false) String startMonth,
            @RequestParam(required = false) String endMonth) {
        
        List<MonthlyCommissionSummary> summaries;
        
        if (startMonth != null && endMonth != null) {
            MonthKey start = MonthKey.parse(startMonth);
            MonthKey end = MonthKey.parse(endMonth);
            summaries = summaryRepository.findByInviterIdAndMonthRange(inviterId, start, end);
        } else {
            summaries = summaryRepository.findByInviterIdOrderByMonthKeyDesc(inviterId);
        }
        
        List<MonthlyCommissionSummaryVO> vos = summaries.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        
        return Result.success(vos);
    }
    
    // 手动触发结算
    @PostMapping("/{summaryId}/settle")
    @PreAuthorize("hasAuthority('admin')")
    public Result<SettlementResult> manualSettle(@PathVariable Long summaryId) {
        try {
            SettlementResult result = settlementService.executeSettlement(summaryId);
            
            if (result.isSuccess()) {
                return Result.success("结算成功", result);
            } else {
                return Result.error("结算失败: " + result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("手动结算失败: summaryId={}", summaryId, e);
            return Result.error("结算失败: " + e.getMessage());
        }
    }
    
    // 重置结算状态
    @PostMapping("/{summaryId}/reset")
    @PreAuthorize("hasAuthority('admin')")
    public Result<String> resetSettlement(@PathVariable Long summaryId) {
        try {
            MonthlyCommissionSummary summary = summaryRepository.findById(summaryId)
                .orElseThrow(() -> new EntityNotFoundException("汇总记录不存在"));
            
            summary.resetToPending();
            summaryRepository.save(summary);
            
            return Result.success("重置成功");
        } catch (Exception e) {
            log.error("重置结算状态失败: summaryId={}", summaryId, e);
            return Result.error("重置失败: " + e.getMessage());
        }
    }
}

// 4. 在事件处理中使用
@Component
public class CommissionEventHandler {

    @Autowired
    private MonthlyCommissionSummaryRepository summaryRepository;

    // 监听佣金记录确认事件
    @EventListener
    @Async
    public void handleCommissionRecordConfirmed(CommissionRecordConfirmedEvent event) {
        try {
            CommissionRecord record = event.getCommissionRecord();

            // 查找或创建月度汇总
            Optional<MonthlyCommissionSummary> summaryOpt = summaryRepository
                .findByInviterIdAndMonthKey(record.getInviterId(), record.getMonthKey());

            MonthlyCommissionSummary summary;
            if (summaryOpt.isPresent()) {
                summary = summaryOpt.get();
            } else {
                summary = MonthlyCommissionSummary.create(record.getInviterId(), record.getMonthKey());
            }

            // 重新计算汇总数据
            recalculateSummary(summary);

            summaryRepository.save(summary);

            log.info("月度汇总已更新: inviterId={}, monthKey={}",
                    record.getInviterId(), record.getMonthKey());

        } catch (Exception e) {
            log.error("处理佣金记录确认事件失败", e);
        }
    }

    // 监听结算完成事件
    @EventListener
    @Async
    public void handleSettlementCompleted(SettlementCompletedEvent event) {
        try {
            MonthlyCommissionSummary summary = event.getSummary();

            // 发送结算完成通知
            sendSettlementNotification(summary);

            // 更新用户积分或等级
            updateUserRewards(summary);

            log.info("结算完成事件处理完毕: summaryId={}", summary.getId());

        } catch (Exception e) {
            log.error("处理结算完成事件失败", e);
        }
    }
}

// 5. 在定时任务中使用
@Component
public class CommissionSettlementScheduler {

    @Autowired
    private MonthlyCommissionSummaryRepository summaryRepository;

    @Autowired
    private CommissionSettlementApplicationService settlementService;

    // 自动结算定时任务
    @Scheduled(cron = "0 0 3 5 * ?") // 每月5号凌晨3点执行
    public void autoSettlement() {
        log.info("开始执行自动结算任务");

        try {
            // 查找上个月待结算的汇总
            MonthKey lastMonth = MonthKey.of(LocalDate.now().minusMonths(1));
            List<MonthlyCommissionSummary> pendingSummaries = summaryRepository
                .findByMonthKeyAndStatus(lastMonth, MonthlyCommissionSummary.Status.PENDING);

            int successCount = 0;
            int failureCount = 0;

            for (MonthlyCommissionSummary summary : pendingSummaries) {
                try {
                    if (summary.canSettle()) {
                        SettlementResult result = settlementService.executeSettlement(summary.getId());
                        if (result.isSuccess()) {
                            successCount++;
                        } else {
                            failureCount++;
                            log.warn("自动结算失败: summaryId={}, error={}",
                                    summary.getId(), result.getErrorMessage());
                        }
                    } else {
                        log.info("汇总不满足结算条件，跳过: summaryId={}", summary.getId());
                    }
                } catch (Exception e) {
                    failureCount++;
                    log.error("自动结算异常: summaryId={}", summary.getId(), e);
                }
            }

            log.info("自动结算任务完成: 总数={}, 成功={}, 失败={}",
                    pendingSummaries.size(), successCount, failureCount);

            // 发送结算报告
            sendSettlementReport(lastMonth, successCount, failureCount);

        } catch (Exception e) {
            log.error("自动结算任务执行失败", e);
        }
    }

    // 清理过期的已取消汇总
    @Scheduled(cron = "0 0 1 1 * ?") // 每月1号凌晨1点执行
    public void cleanupCancelledSummaries() {
        log.info("开始清理过期的已取消汇总");

        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minusMonths(6);
            List<MonthlyCommissionSummary> cancelledSummaries = summaryRepository
                .findCancelledSummariesBeforeDate(cutoffDate);

            for (MonthlyCommissionSummary summary : cancelledSummaries) {
                try {
                    // 软删除或归档
                    summaryRepository.softDelete(summary.getId());
                    log.info("已清理取消的汇总: summaryId={}", summary.getId());
                } catch (Exception e) {
                    log.error("清理汇总失败: summaryId={}", summary.getId(), e);
                }
            }

            log.info("清理任务完成，清理数量: {}", cancelledSummaries.size());

        } catch (Exception e) {
            log.error("清理任务执行失败", e);
        }
    }
}

// 6. 在报表服务中使用
@Service
public class CommissionReportService {

    @Autowired
    private MonthlyCommissionSummaryRepository summaryRepository;

    // 生成月度佣金报表
    public MonthlyCommissionReport generateMonthlyReport(MonthKey monthKey) {
        List<MonthlyCommissionSummary> summaries = summaryRepository
            .findByMonthKey(monthKey);

        MonthlyCommissionReport report = new MonthlyCommissionReport();
        report.setMonthKey(monthKey);
        report.setGeneratedAt(LocalDateTime.now());

        // 统计总体数据
        int totalInviters = summaries.size();
        Money totalCommissionAmount = summaries.stream()
            .map(MonthlyCommissionSummary::getCommissionAmount)
            .filter(Objects::nonNull)
            .reduce(Money.zero(), Money::add);

        Money totalBonusAmount = summaries.stream()
            .map(MonthlyCommissionSummary::getMonthlyBonus)
            .filter(Objects::nonNull)
            .reduce(Money.zero(), Money::add);

        Integer totalOrders = summaries.stream()
            .map(MonthlyCommissionSummary::getOrderCount)
            .filter(Objects::nonNull)
            .reduce(0, Integer::sum);

        // 按状态统计
        Map<MonthlyCommissionSummary.Status, Long> statusCounts = summaries.stream()
            .collect(Collectors.groupingBy(
                MonthlyCommissionSummary::getStatus,
                Collectors.counting()
            ));

        // 设置报表数据
        report.setTotalInviters(totalInviters);
        report.setTotalCommissionAmount(totalCommissionAmount);
        report.setTotalBonusAmount(totalBonusAmount);
        report.setTotalOrders(totalOrders);
        report.setStatusDistribution(statusCounts);

        // 计算平均值
        if (totalInviters > 0) {
            report.setAverageCommissionPerInviter(
                totalCommissionAmount.divide(BigDecimal.valueOf(totalInviters))
            );
            report.setAverageOrdersPerInviter(totalOrders.doubleValue() / totalInviters);
        }

        // 排行榜
        List<InviterRanking> topInviters = summaries.stream()
            .filter(s -> s.getTotalAmount() != null && s.getTotalAmount().isPositive())
            .sorted((s1, s2) -> s2.getTotalAmount().compareTo(s1.getTotalAmount()))
            .limit(10)
            .map(s -> new InviterRanking(s.getInviterId(), s.getTotalAmount(), s.getOrderCount()))
            .collect(Collectors.toList());

        report.setTopInviters(topInviters);

        return report;
    }

    // 生成邀请者年度报表
    public InviterAnnualReport generateInviterAnnualReport(Long inviterId, int year) {
        List<MonthKey> monthKeys = IntStream.rangeClosed(1, 12)
            .mapToObj(month -> MonthKey.of(year, month))
            .collect(Collectors.toList());

        List<MonthlyCommissionSummary> summaries = summaryRepository
            .findByInviterIdAndMonthKeys(inviterId, monthKeys);

        InviterAnnualReport report = new InviterAnnualReport();
        report.setInviterId(inviterId);
        report.setYear(year);
        report.setGeneratedAt(LocalDateTime.now());

        // 按月份组织数据
        Map<MonthKey, MonthlyCommissionSummary> summaryMap = summaries.stream()
            .collect(Collectors.toMap(
                MonthlyCommissionSummary::getMonthKey,
                Function.identity()
            ));

        List<MonthlyData> monthlyDataList = monthKeys.stream()
            .map(monthKey -> {
                MonthlyCommissionSummary summary = summaryMap.get(monthKey);
                if (summary != null) {
                    return new MonthlyData(
                        monthKey,
                        summary.getCommissionAmount(),
                        summary.getMonthlyBonus(),
                        summary.getTotalAmount(),
                        summary.getOrderCount(),
                        summary.getStatus()
                    );
                } else {
                    return new MonthlyData(monthKey, Money.zero(), Money.zero(),
                                         Money.zero(), 0, null);
                }
            })
            .collect(Collectors.toList());

        report.setMonthlyData(monthlyDataList);

        // 计算年度汇总
        Money totalCommission = summaries.stream()
            .map(MonthlyCommissionSummary::getCommissionAmount)
            .filter(Objects::nonNull)
            .reduce(Money.zero(), Money::add);

        Money totalBonus = summaries.stream()
            .map(MonthlyCommissionSummary::getMonthlyBonus)
            .filter(Objects::nonNull)
            .reduce(Money.zero(), Money::add);

        Integer totalOrders = summaries.stream()
            .map(MonthlyCommissionSummary::getOrderCount)
            .filter(Objects::nonNull)
            .reduce(0, Integer::sum);

        report.setTotalCommission(totalCommission);
        report.setTotalBonus(totalBonus);
        report.setTotalEarnings(totalCommission.add(totalBonus));
        report.setTotalOrders(totalOrders);

        return report;
    }
}
```

## 注意事项 (Notes)
*   **领域驱动设计**: 作为聚合根，该实体封装了完整的业务逻辑和不变性条件
*   **状态管理**: 严格控制状态转换，确保业务流程的正确性和数据一致性
*   **不变性保护**: 通过验证方法和封装保护实体的业务规则不被违反
*   **值对象使用**: 使用Money、MonthKey等值对象确保数据的类型安全和业务语义
*   **异常处理**: 业务规则违反时抛出明确的异常，便于上层处理和用户理解
*   **事务边界**: 实体方法的调用应该在适当的事务边界内，确保数据一致性
*   **并发控制**: 多个线程同时操作同一汇总时需要考虑并发控制和乐观锁
*   **审计追踪**: 记录详细的时间戳信息，便于审计和问题排查
*   **性能考虑**: 汇总计算可能涉及大量数据，需要考虑性能优化
*   **数据完整性**: 确保汇总数据与明细数据的一致性，定期进行数据校验
*   **业务规则**: 结算规则可能随业务发展而变化，需要保持代码的灵活性
*   **领域事件**: 重要的状态变更应该发布领域事件，实现与其他聚合的解耦
*   **测试覆盖**: 复杂的业务逻辑需要充分的单元测试覆盖
*   **版本兼容**: 实体结构变更时需要考虑数据迁移和向后兼容性
*   **序列化**: 如果需要序列化，注意值对象的序列化处理
*   **缓存策略**: 频繁访问的汇总数据可以考虑缓存，但要注意缓存一致性
