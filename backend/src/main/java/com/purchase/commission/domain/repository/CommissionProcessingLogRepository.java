package com.purchase.commission.domain.repository;

import com.purchase.commission.domain.entity.CommissionProcessingLog;
import com.purchase.commission.domain.valueobject.MonthKey;

import java.util.List;
import java.util.Optional;

/**
 * 佣金处理日志仓储接口
 */
public interface CommissionProcessingLogRepository {
    
    /**
     * 保存处理日志
     */
    CommissionProcessingLog save(CommissionProcessingLog log);
    
    /**
     * 根据ID查找处理日志
     */
    Optional<CommissionProcessingLog> findById(Long id);
    
    /**
     * 根据月份键查找处理日志
     */
    Optional<CommissionProcessingLog> findByMonthKey(MonthKey monthKey);
    
    /**
     * 检查月份是否已处理
     */
    boolean existsByMonthKey(MonthKey monthKey);
    
    /**
     * 检查月份是否已处理（包含指定状态）
     */
    boolean existsByMonthKeyAndStatusIn(MonthKey monthKey, List<CommissionProcessingLog.Status> statuses);
    
    /**
     * 查找最近的处理日志
     */
    List<CommissionProcessingLog> findRecentLogs(int limit);
    
    /**
     * 查找指定状态的处理日志
     */
    List<CommissionProcessingLog> findByStatus(CommissionProcessingLog.Status status);
    
    /**
     * 查找指定月份范围的处理日志
     */
    List<CommissionProcessingLog> findByMonthKeyBetween(MonthKey startMonth, MonthKey endMonth);
    
    /**
     * 删除处理日志
     */
    void deleteById(Long id);
    
    /**
     * 统计处理日志数量
     */
    long count();
    
    /**
     * 统计指定状态的处理日志数量
     */
    long countByStatus(CommissionProcessingLog.Status status);
}
