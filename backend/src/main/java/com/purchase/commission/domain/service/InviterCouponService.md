# InviterCouponService.md

## 1. 文件概述

`InviterCouponService` 是一个领域服务，专门封装了与“邀请人优惠券”相关的业务逻辑。该服务的设计目标是为通过邀请链接注册并下单的用户，向其邀请人发放优惠券作为奖励。值得注意的是，该功能目前处于“已实现但未激活”的状态，代码完整且经过测试，但尚未在生产环境中启用。

- **类型**: 领域服务 (Domain Service)
- **核心职责**: 负责邀请人优惠券的生成、计算、验证和状态管理。
- **在模块中的位置**: 作为佣金和奖励域的一部分，它封装了优惠券这一具体的业务概念，提供了高度内聚的服务。

## 2. 核心功能

`InviterCouponService` 提供了以下核心功能：

- **优惠券生成**: 能够根据触发订单的金额和类型，为邀请人动态生成具有唯一代码、特定价值和有效期的优惠券。
- **价值计算**: 包含一套复杂的优惠券价值计算逻辑，该逻辑综合考虑了订单类型、订单金额阶梯，并设有最低和最高价值限制。
- **使用验证**: 提供了一个验证优惠券是否可用的方法，检查的条件包括属主、状态、有效期和订单最低消费金额。
- **状态查询**: 提供了查询优惠券价值描述和检查其是否即将过期等辅助功能。

## 3. 接口说明

### 3.1 为买家邀请人生成优惠券
- **方法**: `generateCouponForBuyerInviter(Long inviterId, Long inviteeId, Long triggerOrderId, Money orderAmount, String orderType, MonthKey monthKey)`
- **描述**: 这是服务的核心方法。当被邀请者完成一笔订单时，调用此方法为邀请人生成一张优惠券。
- **参数**:
  - `inviterId` (Long): 邀请人的ID。
  - `inviteeId` (Long): 被邀请者（下单用户）的ID。
  - `triggerOrderId` (Long): 触发此次生成的订单ID。
  - `orderAmount` (Money): 触发订单的金额。
  - `orderType` (String): 订单类型 (e.g., `purchase`, `logistics`)。
  - `monthKey` (MonthKey): 订单所属的月份，用于统计。
- **返回值**: `InviterCoupon` - 一个新创建的、包含所有计算好属性的优惠券实体。

### 3.2 验证优惠券是否可以使用
- **方法**: `validateCouponUsage(InviterCoupon coupon, Money orderAmount, Long userId)`
- **描述**: 在用户尝试使用优惠券结算时，调用此方法来验证该优惠券是否有效。
- **参数**:
  - `coupon` (InviterCoupon): 待验证的优惠券实体。
  - `orderAmount` (Money): 当前订单的金额。
  - `userId` (Long): 尝试使用该优惠券的用户ID。
- **返回值**: `CouponValidationResult` - 一个包含验证结果（是否有效、失败信息、可抵扣金额）的静态内部类实例。

### 3.3 检查优惠券是否即将过期
- **方法**: `isExpiringSoon(InviterCoupon coupon, int daysBeforeExpiry)`
- **描述**: 用于检查一张优惠券是否在指定天数内即将过期，可用于发送提醒通知。
- **参数**:
  - `coupon` (InviterCoupon): 待检查的优惠券。
  - `daysBeforeExpiry` (int): 定义“即将过期”的天数阈值。
- **返回值**: `boolean` - 如果优惠券状态为激活且在阈值内，返回 `true`。

## 4. 使用示例

### 示例1: 在订单完成后触发优惠券生成
```java
// 在一个订单完成的事件监听器中
@Autowired
private InviterCouponService inviterCouponService;
@Autowired
private InviterRelationshipRepository inviterRepo; // 假设有这样一个仓库
@Autowired
private InviterCouponRepository couponRepo;

@EventListener
public void handleOrderCompletion(OrderCompletedEvent event) {
    // 检查此功能是否已激活
    if (!featureFlags.isInviterCouponEnabled()) return;

    UnifiedOrder order = event.getOrder();
    Long inviteeId = order.getCreatorId();
    Long inviterId = inviterRepo.findInviterOf(inviteeId);

    if (inviterId != null) {
        InviterCoupon coupon = inviterCouponService.generateCouponForBuyerInviter(
            inviterId,
            inviteeId,
            order.getId(),
            new Money(order.getTotalAmount()),
            order.getType(),
            MonthKey.from(order.getCreationDate())
        );
        couponRepo.save(coupon);
    }
}
```

### 示例2: 在结算页面验证用户选择的优惠券
```java
// 在结算服务的实现中
@Autowired
private InviterCouponService inviterCouponService;
@Autowired
private InviterCouponRepository couponRepo;

public Money calculateFinalPrice(Long userId, Long couponId, Money orderAmount) {
    InviterCoupon coupon = couponRepo.findById(couponId).orElse(null);
    
    InviterCouponService.CouponValidationResult result = inviterCouponService.validateCouponUsage(coupon, orderAmount, userId);
    
    if (result.isValid()) {
        return orderAmount.subtract(result.getDiscountAmount());
    } else {
        throw new ValidationException(result.getMessage());
    }
}
```

## 5. 注意事项

1.  **功能未激活**: 最重要的一点是，该服务的所有功能当前都未被实际调用。在计划启用此功能时，需要解除相关代码的注释并进行全面的集成测试。
2.  **领域服务**: 这是一个典型的领域服务，它封装了不属于任何一个实体但又与领域密切相关的业务逻辑。
3.  **值对象**: 服务中广泛使用了 `Money` 和 `MonthKey` 等值对象，这使得代码在处理金额和日期时更加健壮和清晰。
4.  **参数校验**: `generateCouponForBuyerInviter` 方法在开始时对所有输入参数进行了严格的非空和有效性校验，这是良好的防御性编程实践。
5.  **私有辅助方法**: 复杂的计算逻辑被分解到多个私有的辅助方法中（如 `generateCouponCode`, `calculateCouponValue`），提高了代码的可读性和可维护性。
6.  **配置化**: 优惠券的计算规则（如基础比例、金额阶梯、有效期）目前是硬编码的。在正式上线时，应考虑将这些参数配置化，以便于运营人员调整。
7.  **唯一性**: `generateCouponCode` 方法通过结合时间戳和UUID来保证生成的优惠券代码具有高度的唯一性。
8.  **静态内部类**: `CouponValidationResult` 作为静态内部类，清晰地封装了验证操作的返回结果，比返回一个简单的布尔值或抛出异常提供了更丰富的信息。
9.  **无状态**: `InviterCouponService` 本身是无状态的，它的所有方法都依赖于传入的参数，这使得它易于测试和在并发环境中使用。
10. **测试覆盖**: 文档注释中提到所有方法都经过了测试，这是领域服务发布前的重要质量保证步骤。