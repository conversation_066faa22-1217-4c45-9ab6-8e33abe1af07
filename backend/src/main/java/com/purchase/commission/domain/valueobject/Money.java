package com.purchase.commission.domain.valueobject;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 金额值对象
 * 封装金额相关的业务逻辑，确保金额计算的准确性
 */
public class Money {
    
    /**
     * 金额精度：保留2位小数
     */
    private static final int SCALE = 2;
    
    /**
     * 舍入模式：四舍五入
     */
    private static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;
    
    /**
     * 金额值
     */
    private final BigDecimal amount;
    
    /**
     * 私有构造函数，确保通过工厂方法创建
     */
    private Money(BigDecimal amount) {
        this.amount = amount.setScale(SCALE, ROUNDING_MODE);
    }
    
    /**
     * 创建金额对象
     * 
     * @param amount 金额
     * @return Money对象
     * @throws IllegalArgumentException 如果金额为负数
     */
    public static Money of(BigDecimal amount) {
        if (amount == null) {
            throw new IllegalArgumentException("金额不能为null");
        }
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("金额不能为负数");
        }
        return new Money(amount);
    }
    
    /**
     * 创建金额对象
     * 
     * @param amount 金额
     * @return Money对象
     */
    public static Money of(double amount) {
        return of(BigDecimal.valueOf(amount));
    }
    
    /**
     * 创建金额对象
     * 
     * @param amount 金额字符串
     * @return Money对象
     */
    public static Money of(String amount) {
        return of(new BigDecimal(amount));
    }
    
    /**
     * 创建零金额
     * 
     * @return 零金额对象
     */
    public static Money zero() {
        return new Money(BigDecimal.ZERO);
    }
    
    /**
     * 加法运算
     * 
     * @param other 另一个金额
     * @return 相加后的新金额对象
     */
    public Money add(Money other) {
        if (other == null) {
            return this;
        }
        return new Money(this.amount.add(other.amount));
    }
    
    /**
     * 减法运算
     * 
     * @param other 另一个金额
     * @return 相减后的新金额对象
     * @throws IllegalArgumentException 如果结果为负数
     */
    public Money subtract(Money other) {
        if (other == null) {
            return this;
        }
        BigDecimal result = this.amount.subtract(other.amount);
        if (result.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("减法运算结果不能为负数");
        }
        return new Money(result);
    }
    
    /**
     * 乘法运算
     * 
     * @param multiplier 乘数
     * @return 相乘后的新金额对象
     */
    public Money multiply(BigDecimal multiplier) {
        if (multiplier == null) {
            throw new IllegalArgumentException("乘数不能为null");
        }
        if (multiplier.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("乘数不能为负数");
        }
        return new Money(this.amount.multiply(multiplier));
    }
    
    /**
     * 乘法运算
     * 
     * @param multiplier 乘数
     * @return 相乘后的新金额对象
     */
    public Money multiply(double multiplier) {
        return multiply(BigDecimal.valueOf(multiplier));
    }
    
    /**
     * 除法运算
     * 
     * @param divisor 除数
     * @return 相除后的新金额对象
     * @throws IllegalArgumentException 如果除数为零或负数
     */
    public Money divide(BigDecimal divisor) {
        if (divisor == null) {
            throw new IllegalArgumentException("除数不能为null");
        }
        if (divisor.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("除数必须大于零");
        }
        return new Money(this.amount.divide(divisor, SCALE, ROUNDING_MODE));
    }
    
    /**
     * 比较金额大小
     * 
     * @param other 另一个金额
     * @return 负数表示小于，0表示等于，正数表示大于
     */
    public int compareTo(Money other) {
        if (other == null) {
            return 1;
        }
        return this.amount.compareTo(other.amount);
    }
    
    /**
     * 判断是否大于另一个金额
     * 
     * @param other 另一个金额
     * @return true表示大于
     */
    public boolean greaterThan(Money other) {
        return compareTo(other) > 0;
    }
    
    /**
     * 判断是否大于等于另一个金额
     * 
     * @param other 另一个金额
     * @return true表示大于等于
     */
    public boolean greaterThanOrEqual(Money other) {
        return compareTo(other) >= 0;
    }
    
    /**
     * 判断是否小于另一个金额
     * 
     * @param other 另一个金额
     * @return true表示小于
     */
    public boolean lessThan(Money other) {
        return compareTo(other) < 0;
    }
    
    /**
     * 判断是否小于等于另一个金额
     * 
     * @param other 另一个金额
     * @return true表示小于等于
     */
    public boolean lessThanOrEqual(Money other) {
        return compareTo(other) <= 0;
    }
    
    /**
     * 判断是否为零
     * 
     * @return true表示为零
     */
    public boolean isZero() {
        return this.amount.compareTo(BigDecimal.ZERO) == 0;
    }
    
    /**
     * 判断是否为正数
     * 
     * @return true表示为正数
     */
    public boolean isPositive() {
        return this.amount.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * 判断是否为负数
     * 
     * @return true表示为负数
     */
    public boolean isNegative() {
        return this.amount.compareTo(BigDecimal.ZERO) < 0;
    }
    
    /**
     * 获取BigDecimal值
     * 
     * @return BigDecimal值
     */
    public BigDecimal getAmount() {
        return amount;
    }
    
    /**
     * 获取double值
     * 
     * @return double值
     */
    public double doubleValue() {
        return amount.doubleValue();
    }
    
    /**
     * 获取字符串表示
     * 
     * @return 金额字符串
     */
    @Override
    public String toString() {
        return amount.toString();
    }
    
    /**
     * 判断相等性
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Money money = (Money) obj;
        return Objects.equals(amount, money.amount);
    }
    
    /**
     * 计算哈希值
     */
    @Override
    public int hashCode() {
        return Objects.hash(amount);
    }
} 