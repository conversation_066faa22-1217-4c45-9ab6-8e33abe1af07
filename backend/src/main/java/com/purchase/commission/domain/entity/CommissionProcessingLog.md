# CommissionProcessingLog.md

## 1. 文件概述

`CommissionProcessingLog` 是佣金模块中的一个领域实体（Domain Entity），位于 `com.purchase.commission.domain.entity` 包中。它专门用于记录月度佣金批量处理任务的执行情况。这个实体不仅包含了处理过程中的关键数据（如处理月份、记录总数、成功/失败计数），还内聚了与处理状态相关的业务逻辑，如创建日志、更新处理结果、标记失败等。它是佣金系统后台任务可追溯性、可监控性的核心组成部分。

## 2. 核心功能

*   **日志创建**: 提供静态工厂方法 `create`，用于在月度佣金处理任务开始时，初始化一个日志记录，标记其为“处理中”状态。
*   **状态管理**: 内部定义了 `Status` 枚举（`PROCESSING`, `SUCCESS`, `FAILED`），并通过 `updateResult`, `markAsFailed`, `markAsSuccess` 等方法，实现了日志状态的流转和更新。
*   **结果记录**: 能够记录处理的总记录数、成功处理数和失败处理数，为任务的执行结果提供量化指标。
*   **错误追踪**: 提供了 `errorMessage` 字段，用于记录处理失败时的具体原因，便于问题排查。
*   **时间追踪**: 记录了任务的开始时间 (`startedAt`) 和完成时间 (`completedAt`)，便于计算处理耗时。
*   **统计与摘要**: 提供了 `getProcessingTimeMs`, `getSuccessRate`, `getSummary` 等辅助方法，用于生成处理报告和概览信息。
*   **值对象使用**: 属性中使用了 `MonthKey` 值对象，确保了月份数据的类型安全和业务含义。

## 3. 接口说明

作为领域实体，`CommissionProcessingLog` 主要通过其工厂方法和业务方法与外部交互。

### 3.1 静态工厂方法

#### create - 创建处理日志
*   **方法签名**: `static CommissionProcessingLog create(MonthKey monthKey)`
*   **描述**: 在月度佣金处理任务开始时调用，创建一个新的日志记录。
*   **参数**:
    *   `monthKey` (MonthKey): 表示当前处理月份的值对象。
*   **返回值**: `CommissionProcessingLog` - 一个初始化为 `PROCESSING` 状态的新日志实体。

### 3.2 核心业务方法

#### updateResult - 更新处理结果
*   **方法签名**: `void updateResult(Integer totalRecords, Integer successCount, Integer failureCount)`
*   **描述**: 在月度佣金处理任务完成后调用，更新处理的总记录数、成功数和失败数，并根据失败数自动设置状态为 `SUCCESS` 或 `FAILED`。
*   **参数**:
    *   `totalRecords` (Integer): 本次处理的总记录数。
    *   `successCount` (Integer): 成功处理的记录数。
    *   `failureCount` (Integer): 失败处理的记录数。
*   **返回值**: `void`

#### markAsFailed - 标记处理失败
*   **方法签名**: `void markAsFailed(String errorMessage)`
*   **描述**: 当月度佣金处理任务发生不可恢复的错误时调用，将日志状态标记为 `FAILED` 并记录错误信息。
*   **参数**:
    *   `errorMessage` (String): 导致处理失败的错误信息。
*   **返回值**: `void`

#### isCompleted - 检查是否处理完成
*   **方法签名**: `boolean isCompleted()`
*   **描述**: 判断当前日志记录所代表的任务是否已完成（无论是成功还是失败）。
*   **返回值**: `boolean` - 如果状态为 `SUCCESS` 或 `FAILED`，返回 `true`。

#### getSuccessRate - 获取成功率
*   **方法签名**: `Double getSuccessRate()`
*   **描述**: 计算并返回本次处理的成功率（成功处理数 / 总记录数 * 100）。
*   **返回值**: `Double` - 成功率百分比。如果 `totalRecords` 为0，返回0.0。

#### getSummary - 获取处理摘要
*   **方法签名**: `String getSummary()`
*   **描述**: 生成一个包含处理月份、总数、成功数、失败数和状态的简要字符串。
*   **返回值**: `String` - 格式化的处理摘要信息。

## 4. 业务规则

*   **状态流转**: 日志的状态只能从 `PROCESSING` 转换为 `SUCCESS` 或 `FAILED`。一旦进入 `SUCCESS` 或 `FAILED` 状态，就不能再回到 `PROCESSING`。
*   **数据完整性**: `updateResult` 方法确保了 `totalRecords`, `successCount`, `failureCount` 之间的逻辑关系（`totalRecords = successCount + failureCount`）。
*   **时间戳**: `startedAt` 在创建时设置，`completedAt` 在任务完成（成功或失败）时设置，确保了处理耗时计算的准确性。
*   **唯一性**: 通常，每个 `monthKey` 对应一个唯一的 `CommissionProcessingLog` 记录，以避免重复处理和日志混乱。

## 5. 使用示例

```java
// 1. 在月度佣金处理器 (MonthlyCommissionProcessor) 中使用
@Service
public class MonthlyCommissionProcessor {
    @Autowired
    private CommissionProcessingLogRepository logRepository;
    @Autowired
    private CommissionCalculator commissionCalculator;

    @Transactional
    public void processMonth(MonthKey monthKey) {
        CommissionProcessingLog log = CommissionProcessingLog.create(monthKey);
        logRepository.save(log); // 保存初始日志

        try {
            List<Order> orders = orderRepository.findByMonth(monthKey);
            int total = orders.size();
            int success = 0;
            int failure = 0;

            for (Order order : orders) {
                try {
                    commissionCalculator.calculateAndRecord(order);
                    success++;
                } catch (Exception e) {
                    log.error("处理订单失败: {}", order.getId(), e);
                    failure++;
                }
            }
            log.updateResult(total, success, failure);
            
        } catch (Exception e) {
            log.markAsFailed("整体处理异常: " + e.getMessage());
            throw e; // 重新抛出异常，让上层感知
        } finally {
            logRepository.save(log); // 确保最终状态被保存
        }
    }
}

// 2. 在Controller中获取处理状态
@RestController
@RequestMapping("/api/v1/commission/monthly")
public class MonthlyProcessingController {
    @Autowired
    private CommissionProcessingLogRepository logRepository;

    @GetMapping("/status/{monthKey}")
    public Result<CommissionProcessingLog> getStatus(@PathVariable String monthKey) {
        MonthKey key = MonthKey.of(monthKey);
        Optional<CommissionProcessingLog> logOpt = logRepository.findByMonthKey(key);
        if (logOpt.isPresent()) {
            return Result.success(logOpt.get());
        } else {
            return Result.error("未找到该月份的处理日志");
        }
    }
}

// 3. 测试示例
@SpringBootTest
class CommissionProcessingLogTest {
    @Test
    void testCreateAndUpdateLog() {
        MonthKey month = MonthKey.of("2024-07");
        CommissionProcessingLog log = CommissionProcessingLog.create(month);

        assertThat(log.getMonthKey()).isEqualTo(month);
        assertThat(log.getStatus()).isEqualTo(CommissionProcessingLog.Status.PROCESSING);
        assertThat(log.getStartedAt()).isNotNull();

        log.updateResult(100, 95, 5);
        assertThat(log.getTotalRecords()).isEqualTo(100);
        assertThat(log.getSuccessCount()).isEqualTo(95);
        assertThat(log.getFailureCount()).isEqualTo(5);
        assertThat(log.getStatus()).isEqualTo(CommissionProcessingLog.Status.FAILED); // 因为有失败，所以是FAILED
        assertThat(log.getCompletedAt()).isNotNull();
        assertThat(log.getProcessingTimeMs()).isNotNull();
        assertThat(log.getSuccessRate()).isEqualTo(95.0);

        // 再次更新为成功
        log.updateResult(100, 100, 0);
        assertThat(log.getStatus()).isEqualTo(CommissionProcessingLog.Status.SUCCESS);
    }
}
```

## 6. 注意事项

*   **领域实体**: `CommissionProcessingLog` 是一个典型的领域实体，它包含了数据和行为，并且其行为（如 `updateResult`, `markAsFailed`）直接反映了业务概念。
*   **Lombok**: 使用 `@Data` 和 `@NoArgsConstructor` 简化了Getter/Setter和无参构造函数的编写，但需要注意 `@Data` 可能会生成 `equals` 和 `hashCode`，如果实体有业务唯一标识（如 `monthKey`），可能需要手动重写以确保正确性。
*   **时间戳**: `startedAt`, `completedAt`, `createdAt`, `updatedAt` 的管理对于审计和监控非常重要。在实际持久化时，通常会通过ORM框架（如JPA的 `@CreatedDate`, `@LastModifiedDate`）或数据库触发器自动管理。
*   **状态机**: 实体内部的状态流转逻辑（`PROCESSING` -> `SUCCESS`/`FAILED`）是其核心业务规则之一，应确保其正确性。
*   **值对象**: `MonthKey` 作为值对象的使用，提高了代码的类型安全和业务表达力。
*   **可扩展性**: 如果未来需要记录更多处理细节（如每个失败记录的ID和原因），可以在实体中添加相应的字段或关联集合。
*   **持久化**: 该实体需要通过一个仓储（Repository）接口进行持久化操作，例如 `CommissionProcessingLogRepository`。
*   **并发**: 如果月度处理任务可能并发执行（尽管通常是单例），需要考虑对日志记录的并发控制，例如使用数据库的乐观锁或悲观锁。