package com.purchase.commission.domain.repository;

import com.purchase.commission.domain.entity.InviterCoupon;
import com.purchase.commission.domain.valueobject.MonthKey;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 邀请人优惠券仓储接口
 * 定义优惠券的持久化操作
 */
public interface InviterCouponRepository {
    
    /**
     * 保存优惠券
     * 
     * @param coupon 优惠券实体
     * @return 保存后的优惠券实体
     */
    InviterCoupon save(InviterCoupon coupon);
    
    /**
     * 根据ID查找优惠券
     * 
     * @param id 优惠券ID
     * @return 优惠券实体
     */
    Optional<InviterCoupon> findById(Long id);
    
    /**
     * 根据优惠券代码查找优惠券
     * 
     * @param couponCode 优惠券代码
     * @return 优惠券实体
     */
    Optional<InviterCoupon> findByCouponCode(String couponCode);
    
    /**
     * 根据邀请人ID查找优惠券列表
     * 
     * @param inviterId 邀请人ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 优惠券列表
     */
    List<InviterCoupon> findByInviterId(Long inviterId, Integer limit, Integer offset);
    
    /**
     * 根据邀请人ID和月份查找优惠券列表
     * 
     * @param inviterId 邀请人ID
     * @param monthKey 月份键
     * @return 优惠券列表
     */
    List<InviterCoupon> findByInviterIdAndMonthKey(Long inviterId, MonthKey monthKey);
    
    /**
     * 根据触发订单ID查找优惠券
     * 
     * @param triggerOrderId 触发订单ID
     * @return 优惠券实体
     */
    Optional<InviterCoupon> findByTriggerOrderId(Long triggerOrderId);
    
    /**
     * 查找有效的优惠券
     * 
     * @param inviterId 邀请人ID
     * @param currentTime 当前时间
     * @return 有效优惠券列表
     */
    List<InviterCoupon> findActiveByInviterId(Long inviterId, LocalDateTime currentTime);
    
    /**
     * 查找过期的优惠券
     * 
     * @param currentTime 当前时间
     * @param limit 限制数量
     * @return 过期优惠券列表
     */
    List<InviterCoupon> findExpiredCoupons(LocalDateTime currentTime, Integer limit);
    
    /**
     * 批量更新优惠券状态
     * 
     * @param couponIds 优惠券ID列表
     * @param status 新状态
     * @return 更新数量
     */
    int batchUpdateStatus(List<Long> couponIds, InviterCoupon.Status status);
    
    /**
     * 统计邀请人的优惠券数量
     * 
     * @param inviterId 邀请人ID
     * @param status 状态（可选）
     * @return 优惠券数量
     */
    long countByInviterIdAndStatus(Long inviterId, InviterCoupon.Status status);
    
    /**
     * 删除优惠券
     * 
     * @param id 优惠券ID
     */
    void deleteById(Long id);
}
