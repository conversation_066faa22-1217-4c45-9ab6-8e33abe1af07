# InviterCoupon.md

## 1. 文件概述

`InviterCoupon` 是一个领域实体，它完整地封装了“邀请人优惠券”的所有属性和行为。作为佣金和奖励领域的核心部分，这个实体不仅定义了优惠券的数据结构，还内聚了其整个生命周期的业务规则，包括创建、使用、取消和状态变更。它通过工厂方法模式提供了创建不同类型优惠券（固定金额、百分比折扣）的安全入口。

- **类型**: 领域实体 (Domain Entity)
- **核心职责**: 定义和管理邀请人优惠券的属性、状态和核心业务逻辑。
- **在模块中的位置**: 作为佣金领域模型的核心，是优惠券功能的基石，被 `InviterCouponService` 创建和管理，并由 `InviterCouponRepository` 持久化。

## 2. 核心功能

`InviterCoupon` 实体具备以下核心功能：

- **安全的创建逻辑**: 通过 `createFixedAmountCoupon` 和 `createPercentageCoupon` 两个静态工厂方法来创建实例，并在内部进行严格的参数校验，保证了实体的初始一致性。
- **状态机管理**: 实体内部定义了 `Status` 枚举（`ACTIVE`, `USED`, `EXPIRED`, `CANCELLED`）和相应的业务方法（`use`, `cancel`, `checkExpiry`），形成了一个完整的状态机，确保了状态转换的合法性。
- **业务逻辑内聚**: 核心业务逻辑，如计算实际折扣金额 (`calculateDiscount`) 和判断是否可用 (`isUsable`)，都作为实体的方法被封装起来，实现了数据和行为的统一。
- **类型安全**: 通过 `CouponType` 枚举区分了“固定金额”和“百分比折扣”两种类型，使得处理不同类型优惠券的逻辑更加清晰和安全。
- **值对象的使用**: 实体内部广泛使用了 `Money` 和 `MonthKey` 等值对象，极大地增强了代码的可读性和业务表达能力。

## 3. 接口说明

作为实体类，`InviterCoupon` 主要通过其工厂方法和业务方法与外部交互。

### 3.1 工厂方法

- **`createFixedAmountCoupon(...)`**: 创建一张固定金额的优惠券。所有必要的参数（如邀请人ID、优惠金额、有效期等）都必须提供。
- **`createPercentageCoupon(...)`**: 创建一张百分比折扣的优惠券。除了通用参数外，还必须提供最大优惠金额（`maximumDiscount`）。

### 3.2 核心业务方法

- **`use(Long orderId)`**: 将优惠券的状态从 `ACTIVE` 变更为 `USED`。在变更前会检查优惠券是否可用（状态和有效期），并记录使用时间和订单ID。
- **`cancel()`**: 将优惠券的状态变更为 `CANCELLED`。已使用的优惠券不能被取消。
- **`checkExpiry()`**: 检查并更新优惠券的过期状态。如果当前时间晚于有效期结束时间，则将状态置为 `EXPIRED`。
- **`isUsable()`**: 根据当前状态和有效期，判断优惠券是否可用。
- **`calculateDiscount(Money orderAmount)`**: 根据优惠券类型和订单金额，计算出实际可以抵扣的金额。它会考虑最低使用门槛、最大优惠上限，并确保优惠金额不超过订单本身金额。

## 4. 使用示例

### 示例1: 在服务层使用工厂方法创建优惠券
```java
// 在 InviterCouponService 中
public InviterCoupon generateCoupon(...) {
    // ... 逻辑判断 ...
    if (isFixed) {
        return InviterCoupon.createFixedAmountCoupon(
            // ... parameters ...
        );
    } else {
        return InviterCoupon.createPercentageCoupon(
            // ... parameters ...
        );
    }
}
```

### 示例2: 在结算流程中使用优惠券
```java
// 在一个结算服务中
@Transactional
public void applyCouponToOrder(Long couponId, Order order) {
    InviterCoupon coupon = couponRepository.findById(couponId).orElseThrow();
    
    // 检查优惠券是否可用
    if (!coupon.isUsable()) {
        throw new BusinessException("优惠券不可用");
    }
    
    // 计算折扣
    Money discount = coupon.calculateDiscount(order.getTotalAmount());
    if (discount.isZero()) {
        throw new BusinessException("不满足优惠券使用条件");
    }
    
    // 应用折扣到订单
    order.applyDiscount(discount);
    
    // 标记优惠券为已使用
    coupon.use(order.getId());
    
    couponRepository.save(coupon);
    orderRepository.save(order);
}
```

### 示例3: 定时任务检查并更新过期优惠券
```java
// 在一个定时任务 (Scheduled Job) 中
@Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
public void expireCoupons() {
    List<InviterCoupon> activeCoupons = couponRepository.findAllActiveCoupons();
    for (InviterCoupon coupon : activeCoupons) {
        coupon.checkExpiry();
        // 如果状态变更，则保存
        if (coupon.getStatus() == InviterCoupon.Status.EXPIRED) {
            couponRepository.save(coupon);
        }
    }
}
```

## 5. 注意事项

1.  **富领域模型**: `InviterCoupon` 是一个典型的“富领域模型”的例子，它不仅仅是数据的载体，更包含了丰富的业务逻辑，使得代码更加内聚和易于理解。
2.  **构造函数私有化**: 构造函数被声明为 `private`，强制外部代码必须通过静态工厂方法来创建实例，这是一种保证对象创建过程受控的有效手段。
3.  **参数校验**: 工厂方法中的 `validateCreateParameters` 私有方法对所有输入进行了严格的校验，确保了不会创建出无效状态的实体。
4.  **状态的封装**: 优惠券的状态 (`status`) 是私有的，外部只能通过 `use()`, `cancel()` 等业务方法来改变它，这保证了状态转换的有效性。
5.  **ORM兼容性**: 实体提供了一个 `protected` 的无参构造函数，并为ID和时间戳等字段提供了Setter，以兼容JPA/MyBatis等ORM框架的反射机制。
6.  **Equals 和 HashCode**: 实体基于唯一的 `id` 字段重写了 `equals` 和 `hashCode`，这对于实体在集合中的管理和持久化上下文中的识别至关重要。
7.  **不可变性考量**: 虽然实体本身是可变的，但其核心属性（如 `couponCode`, `discountValue`）在创建后就不应该再改变。没有为这些字段提供公共的Setter方法，体现了对部分不变性的保护。
8.  **职责清晰**: `calculateDiscount` 方法的逻辑非常清晰，它准确地实现了折扣计算的业务规则，是实体核心价值的体现。