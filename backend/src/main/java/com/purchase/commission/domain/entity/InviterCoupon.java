package com.purchase.commission.domain.entity;

import com.purchase.commission.domain.valueobject.Money;
import com.purchase.commission.domain.valueobject.MonthKey;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 邀请人优惠券领域实体
 * 当邀请人是买家时，为其生成优惠券而非佣金
 */
public class InviterCoupon {
    
    /**
     * 优惠券状态枚举
     */
    public enum Status {
        ACTIVE("有效"),
        USED("已使用"),
        EXPIRED("已过期"),
        CANCELLED("已取消");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 优惠券类型枚举
     */
    public enum CouponType {
        PERCENTAGE("百分比折扣"),
        FIXED_AMOUNT("固定金额");
        
        private final String description;
        
        CouponType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 优惠券ID
     */
    private Long id;
    
    /**
     * 邀请人ID（买家）
     */
    private Long inviterId;
    
    /**
     * 被邀请者ID（订单创建者）
     */
    private Long inviteeId;
    
    /**
     * 触发订单ID
     */
    private Long triggerOrderId;
    
    /**
     * 优惠券代码
     */
    private String couponCode;
    
    /**
     * 优惠券类型
     */
    private CouponType couponType;
    
    /**
     * 优惠金额或折扣比例
     */
    private Money discountValue;
    
    /**
     * 最低使用金额
     */
    private Money minimumAmount;
    
    /**
     * 最大优惠金额（用于百分比折扣）
     */
    private Money maximumDiscount;
    
    /**
     * 状态
     */
    private Status status;
    
    /**
     * 月份键
     */
    private MonthKey monthKey;
    
    /**
     * 有效期开始时间
     */
    private LocalDateTime validFrom;
    
    /**
     * 有效期结束时间
     */
    private LocalDateTime validUntil;
    
    /**
     * 使用时间
     */
    private LocalDateTime usedAt;
    
    /**
     * 使用订单ID
     */
    private Long usedOrderId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 默认构造函数（用于ORM框架）
     */
    protected InviterCoupon() {
    }
    
    /**
     * 创建邀请人优惠券的构造函数
     */
    private InviterCoupon(Long inviterId, Long inviteeId, Long triggerOrderId, 
                         String couponCode, CouponType couponType, Money discountValue,
                         Money minimumAmount, Money maximumDiscount, MonthKey monthKey,
                         LocalDateTime validFrom, LocalDateTime validUntil) {
        this.inviterId = inviterId;
        this.inviteeId = inviteeId;
        this.triggerOrderId = triggerOrderId;
        this.couponCode = couponCode;
        this.couponType = couponType;
        this.discountValue = discountValue;
        this.minimumAmount = minimumAmount;
        this.maximumDiscount = maximumDiscount;
        this.monthKey = monthKey;
        this.validFrom = validFrom;
        this.validUntil = validUntil;
        this.status = Status.ACTIVE;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 创建固定金额优惠券
     * 
     * @param inviterId 邀请人ID
     * @param inviteeId 被邀请者ID
     * @param triggerOrderId 触发订单ID
     * @param couponCode 优惠券代码
     * @param discountAmount 优惠金额
     * @param minimumAmount 最低使用金额
     * @param monthKey 月份键
     * @param validFrom 有效期开始时间
     * @param validUntil 有效期结束时间
     * @return 优惠券实体
     */
    public static InviterCoupon createFixedAmountCoupon(Long inviterId, Long inviteeId, Long triggerOrderId,
                                                       String couponCode, Money discountAmount, Money minimumAmount,
                                                       MonthKey monthKey, LocalDateTime validFrom, LocalDateTime validUntil) {
        validateCreateParameters(inviterId, inviteeId, triggerOrderId, couponCode, discountAmount, monthKey, validFrom, validUntil);
        
        return new InviterCoupon(inviterId, inviteeId, triggerOrderId, couponCode, 
                               CouponType.FIXED_AMOUNT, discountAmount, minimumAmount, null,
                               monthKey, validFrom, validUntil);
    }
    
    /**
     * 创建百分比折扣优惠券
     * 
     * @param inviterId 邀请人ID
     * @param inviteeId 被邀请者ID
     * @param triggerOrderId 触发订单ID
     * @param couponCode 优惠券代码
     * @param discountPercentage 折扣比例（如0.1表示10%）
     * @param minimumAmount 最低使用金额
     * @param maximumDiscount 最大优惠金额
     * @param monthKey 月份键
     * @param validFrom 有效期开始时间
     * @param validUntil 有效期结束时间
     * @return 优惠券实体
     */
    public static InviterCoupon createPercentageCoupon(Long inviterId, Long inviteeId, Long triggerOrderId,
                                                      String couponCode, Money discountPercentage, Money minimumAmount,
                                                      Money maximumDiscount, MonthKey monthKey, 
                                                      LocalDateTime validFrom, LocalDateTime validUntil) {
        validateCreateParameters(inviterId, inviteeId, triggerOrderId, couponCode, discountPercentage, monthKey, validFrom, validUntil);
        
        if (maximumDiscount == null) {
            throw new IllegalArgumentException("百分比折扣优惠券必须设置最大优惠金额");
        }
        
        return new InviterCoupon(inviterId, inviteeId, triggerOrderId, couponCode, 
                               CouponType.PERCENTAGE, discountPercentage, minimumAmount, maximumDiscount,
                               monthKey, validFrom, validUntil);
    }
    
    /**
     * 验证创建参数
     */
    private static void validateCreateParameters(Long inviterId, Long inviteeId, Long triggerOrderId,
                                               String couponCode, Money discountValue, MonthKey monthKey,
                                               LocalDateTime validFrom, LocalDateTime validUntil) {
        if (inviterId == null) {
            throw new IllegalArgumentException("邀请人ID不能为null");
        }
        if (inviteeId == null) {
            throw new IllegalArgumentException("被邀请者ID不能为null");
        }
        if (triggerOrderId == null) {
            throw new IllegalArgumentException("触发订单ID不能为null");
        }
        if (couponCode == null || couponCode.trim().isEmpty()) {
            throw new IllegalArgumentException("优惠券代码不能为空");
        }
        if (discountValue == null || !discountValue.isPositive()) {
            throw new IllegalArgumentException("优惠值必须大于零");
        }
        if (monthKey == null) {
            throw new IllegalArgumentException("月份键不能为null");
        }
        if (validFrom == null) {
            throw new IllegalArgumentException("有效期开始时间不能为null");
        }
        if (validUntil == null) {
            throw new IllegalArgumentException("有效期结束时间不能为null");
        }
        if (validUntil.isBefore(validFrom)) {
            throw new IllegalArgumentException("有效期结束时间不能早于开始时间");
        }
        if (inviterId.equals(inviteeId)) {
            throw new IllegalArgumentException("邀请人和被邀请者不能是同一人");
        }
    }
    
    /**
     * 使用优惠券
     * 
     * @param orderId 使用订单ID
     */
    public void use(Long orderId) {
        if (this.status != Status.ACTIVE) {
            throw new IllegalStateException("只有有效状态的优惠券才能使用");
        }
        if (LocalDateTime.now().isAfter(this.validUntil)) {
            throw new IllegalStateException("优惠券已过期");
        }
        if (orderId == null) {
            throw new IllegalArgumentException("使用订单ID不能为null");
        }
        
        this.status = Status.USED;
        this.usedAt = LocalDateTime.now();
        this.usedOrderId = orderId;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 取消优惠券
     */
    public void cancel() {
        if (this.status == Status.USED) {
            throw new IllegalStateException("已使用的优惠券不能取消");
        }
        
        this.status = Status.CANCELLED;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查优惠券是否过期
     */
    public void checkExpiry() {
        if (this.status == Status.ACTIVE && LocalDateTime.now().isAfter(this.validUntil)) {
            this.status = Status.EXPIRED;
            this.updatedAt = LocalDateTime.now();
        }
    }
    
    /**
     * 判断优惠券是否可用
     */
    public boolean isUsable() {
        return this.status == Status.ACTIVE && 
               LocalDateTime.now().isBefore(this.validUntil) &&
               LocalDateTime.now().isAfter(this.validFrom);
    }
    
    /**
     * 计算优惠金额
     * 
     * @param orderAmount 订单金额
     * @return 优惠金额
     */
    public Money calculateDiscount(Money orderAmount) {
        if (!isUsable()) {
            return Money.zero();
        }
        
        if (minimumAmount != null && orderAmount.lessThan(minimumAmount)) {
            return Money.zero();
        }
        
        Money discount;
        if (couponType == CouponType.FIXED_AMOUNT) {
            discount = discountValue;
        } else {
            // 百分比折扣
            discount = orderAmount.multiply(discountValue.getAmount());
            if (maximumDiscount != null && discount.greaterThan(maximumDiscount)) {
                discount = maximumDiscount;
            }
        }

        // 优惠金额不能超过订单金额
        if (discount.greaterThan(orderAmount)) {
            return orderAmount;
        }
        return discount;
    }
    
    // Getters
    public Long getId() { return id; }
    public Long getInviterId() { return inviterId; }
    public Long getInviteeId() { return inviteeId; }
    public Long getTriggerOrderId() { return triggerOrderId; }
    public String getCouponCode() { return couponCode; }
    public CouponType getCouponType() { return couponType; }
    public Money getDiscountValue() { return discountValue; }
    public Money getMinimumAmount() { return minimumAmount; }
    public Money getMaximumDiscount() { return maximumDiscount; }
    public Status getStatus() { return status; }
    public MonthKey getMonthKey() { return monthKey; }
    public LocalDateTime getValidFrom() { return validFrom; }
    public LocalDateTime getValidUntil() { return validUntil; }
    public LocalDateTime getUsedAt() { return usedAt; }
    public Long getUsedOrderId() { return usedOrderId; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    
    // Setters for ORM
    public void setId(Long id) { this.id = id; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        InviterCoupon that = (InviterCoupon) obj;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "InviterCoupon{" +
                "id=" + id +
                ", inviterId=" + inviterId +
                ", couponCode='" + couponCode + '\'' +
                ", discountValue=" + discountValue +
                ", status=" + status +
                '}';
    }
}
