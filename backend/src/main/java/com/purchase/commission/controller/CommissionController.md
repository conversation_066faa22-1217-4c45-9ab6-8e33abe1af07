# CommissionController.java

## 文件概述 (File Overview)
`CommissionController.java` 是佣金管理的REST控制器，位于 `com.purchase.commission.controller` 包中。该控制器作为佣金管理模块的HTTP接口层，负责处理所有佣金相关的REST API请求。通过集成 `CommissionApplicationService`、`CommissionCalculationService` 等业务服务，提供了佣金的完整生命周期管理功能，包括计算、分配、查询、统计、结算等。该控制器实现了基于Spring Security的多层权限控制，支持买家、卖家、货代、管理员等不同角色的差异化访问，并提供了完善的佣金业务流程管理。

## 核心功能 (Core Functionality)
*   **佣金计算管理**: 提供多种佣金计算方式，支持阶梯式计算和自定义规则
*   **佣金分配服务**: 实现佣金的自动分配和手动调整功能
*   **多维度查询**: 支持按用户、订单、时间、状态等多种维度查询佣金记录
*   **统计分析功能**: 提供佣金统计、趋势分析、收益排行等分析功能
*   **权限分级控制**: 基于用户角色的细粒度权限控制和数据隔离
*   **批量操作支持**: 支持佣金记录的批量处理和批量状态更新
*   **结算流程管理**: 完整的佣金结算流程，包括确认、支付、对账等
*   **实时通知服务**: 佣金状态变更的实时通知和消息推送
*   **审计日志记录**: 详细的佣金操作审计日志，支持合规要求
*   **数据导出功能**: 支持佣金数据的多格式导出和报表生成
*   **缓存优化策略**: 高频查询数据的缓存优化，提升系统性能
*   **异常处理机制**: 完善的异常捕获和错误恢复机制

### 3. 接口说明
#### 请求/响应示例
```java
/**
 * 佣金计算接口
 * @param request 计算请求
 * @return 计算结果
 */
@PostMapping("/calculate")
public ResponseEntity<CommissionResult> calculate(
    @Valid @RequestBody CommissionRequest request) {
    // 实现细节...
}

/**
 * 批量佣金查询
 * @param query 查询条件
 * @return 分页结果
 */
@GetMapping
public ResponseEntity<Page<CommissionVO>> query(
    @Valid CommissionQuery query, 
    Pageable pageable) {
    // 实现细节...
}
```

## 使用示例 (Usage Examples)

```java
// 1. 前端JavaScript调用示例
const CommissionAPI = {
    // 计算佣金
    async calculateCommission(orderData) {
        const response = await fetch('/api/v1/commission/calculate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                orderId: orderData.orderId,
                orderAmount: orderData.amount,
                inviterId: orderData.inviterId,
                inviterRole: orderData.inviterRole,
                calculationType: 'STANDARD'
            })
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage('佣金计算成功');
            return result.data;
        } else {
            showErrorMessage('计算失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 查询佣金记录
    async getCommissionRecords(params = {}) {
        const queryParams = new URLSearchParams({
            page: params.page || 1,
            size: params.size || 20,
            inviterId: params.inviterId,
            status: params.status,
            startDate: params.startDate,
            endDate: params.endDate
        });

        const response = await fetch(`/api/v1/commission/records?${queryParams}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 获取佣金统计
    async getCommissionStatistics(inviterId, monthKey) {
        const response = await fetch(`/api/v1/commission/statistics/${inviterId}/${monthKey}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },

    // 批量取消佣金记录
    async batchCancelCommissions(recordIds) {
        const response = await fetch('/api/v1/commission/batch/cancel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                recordIds: recordIds,
                reason: '批量取消操作'
            })
        });

        const result = await response.json();
        if (result.success) {
            showSuccessMessage(`成功取消 ${recordIds.length} 条佣金记录`);
            return result.data;
        } else {
            showErrorMessage('批量取消失败: ' + result.message);
            throw new Error(result.message);
        }
    },

    // 渲染佣金记录列表
    renderCommissionList(records, containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        records.forEach(record => {
            const recordDiv = document.createElement('div');
            recordDiv.className = 'commission-record';

            recordDiv.innerHTML = `
                <div class="record-header">
                    <h4>佣金记录 #${record.id}</h4>
                    <span class="status ${record.status.toLowerCase()}">${record.statusText}</span>
                </div>
                <div class="record-content">
                    <div class="record-info">
                        <p><strong>订单ID:</strong> ${record.orderId}</p>
                        <p><strong>订单金额:</strong> ¥${record.orderAmount.toLocaleString()}</p>
                        <p><strong>佣金金额:</strong> ¥${record.commissionAmount.toLocaleString()}</p>
                        <p><strong>邀请人:</strong> ${record.inviterName}</p>
                        <p><strong>创建时间:</strong> ${new Date(record.createdAt).toLocaleString()}</p>
                    </div>
                </div>
                <div class="record-actions">
                    <button onclick="viewCommissionDetails(${record.id})">查看详情</button>
                    ${record.status === 'PENDING' ?
                        `<button onclick="cancelCommission(${record.id})" class="cancel-btn">取消</button>` :
                        ''}
                </div>
            `;

            container.appendChild(recordDiv);
        });
    }
};

// 2. Java客户端调用示例
@Service
public class CommissionClientService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${api.base-url}")
    private String baseUrl;

    // 计算佣金
    public CommissionCalculationResult calculateCommission(CommissionCalculationRequest request, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<CommissionCalculationRequest> entity = new HttpEntity<>(request, headers);

        try {
            ResponseEntity<Result<CommissionCalculationResult>> response = restTemplate.exchange(
                baseUrl + "/api/v1/commission/calculate",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<Result<CommissionCalculationResult>>() {}
            );

            Result<CommissionCalculationResult> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("佣金计算失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用佣金计算API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }

    // 查询佣金记录
    public PageResult<CommissionRecordDTO> getCommissionRecords(CommissionQueryRequest request, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl + "/api/v1/commission/records")
            .queryParam("page", request.getPage())
            .queryParam("size", request.getSize())
            .queryParam("inviterId", request.getInviterId())
            .queryParam("status", request.getStatus());

        HttpEntity<?> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<Result<PageResult<CommissionRecordDTO>>> response = restTemplate.exchange(
                builder.toUriString(),
                HttpMethod.GET,
                entity,
                new ParameterizedTypeReference<Result<PageResult<CommissionRecordDTO>>>() {}
            );

            Result<PageResult<CommissionRecordDTO>> result = response.getBody();
            if (result != null && result.isSuccess()) {
                return result.getData();
            } else {
                throw new BusinessException("查询佣金记录失败: " + (result != null ? result.getMessage() : "未知错误"));
            }
        } catch (RestClientException e) {
            log.error("调用查询佣金记录API失败", e);
            throw new SystemException("网络请求失败", e);
        }
    }
}

// 3. 业务服务集成示例
@Service
public class CommissionWorkflowService {

    @Autowired
    private CommissionApplicationService commissionService;

    @Autowired
    private NotificationService notificationService;

    // 订单完成后自动计算佣金
    @EventListener
    @Async
    public void handleOrderCompleted(OrderCompletedEvent event) {
        try {
            // 1. 计算佣金
            CommissionCalculationRequest request = new CommissionCalculationRequest();
            request.setOrderId(event.getOrderId());
            request.setOrderAmount(event.getOrderAmount());
            request.setInviterId(event.getInviterId());
            request.setInviterRole(event.getInviterRole());

            CommissionCalculationResult result = commissionService.calculateCommission(request);

            // 2. 发送佣金通知
            notificationService.sendCommissionCalculatedNotification(result);

            // 3. 记录业务日志
            auditLogService.logCommissionCalculation(event.getOrderId(), result);

            log.info("订单佣金计算完成: orderId={}, commissionAmount={}",
                event.getOrderId(), result.getCommissionAmount());

        } catch (Exception e) {
            log.error("订单佣金计算失败: orderId={}", event.getOrderId(), e);
        }
    }

    // 月度佣金结算处理
    @Scheduled(cron = "0 0 2 1 * ?") // 每月1号凌晨2点
    public void processMonthlyCommissionSettlement() {
        log.info("开始月度佣金结算处理");

        try {
            String monthKey = LocalDate.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));

            // 1. 获取待结算的佣金记录
            List<CommissionRecordDTO> pendingRecords = commissionService.getPendingCommissionsByMonth(monthKey);

            // 2. 批量确认佣金
            List<Long> recordIds = pendingRecords.stream()
                .map(CommissionRecordDTO::getId)
                .collect(Collectors.toList());

            commissionService.batchConfirmCommissions(recordIds);

            // 3. 生成结算报告
            MonthlyCommissionReport report = generateMonthlyReport(monthKey, pendingRecords);

            // 4. 发送结算通知
            notificationService.sendMonthlySettlementNotification(report);

            log.info("月度佣金结算处理完成: month={}, recordCount={}", monthKey, pendingRecords.size());

        } catch (Exception e) {
            log.error("月度佣金结算处理失败", e);
        }
    }
}

// 4. 定时任务示例
@Component
public class CommissionScheduledTasks {

    @Autowired
    private CommissionApplicationService commissionService;

    // 检查异常佣金记录
    @Scheduled(cron = "0 0 */4 * * ?") // 每4小时执行一次
    public void checkAbnormalCommissions() {
        log.info("开始检查异常佣金记录");

        try {
            // 检查计算异常的记录
            List<CommissionRecordDTO> abnormalRecords = commissionService.findAbnormalCommissions();

            for (CommissionRecordDTO record : abnormalRecords) {
                // 重新计算佣金
                try {
                    commissionService.recalculateCommission(record.getId());
                    log.info("重新计算佣金成功: recordId={}", record.getId());
                } catch (Exception e) {
                    log.error("重新计算佣金失败: recordId={}", record.getId(), e);
                    // 发送告警通知
                    notificationService.sendCommissionErrorAlert(record, e.getMessage());
                }
            }

            log.info("异常佣金记录检查完成，处理记录数: {}", abnormalRecords.size());

        } catch (Exception e) {
            log.error("检查异常佣金记录失败", e);
        }
    }

    // 生成佣金统计报告
    @Scheduled(cron = "0 0 9 * * MON") // 每周一早上9点
    public void generateWeeklyCommissionReport() {
        log.info("开始生成周度佣金报告");

        try {
            WeeklyCommissionReport report = new WeeklyCommissionReport();
            report.setReportPeriod("最近一周");
            report.setTotalCommissionAmount(commissionService.getTotalCommissionThisWeek());
            report.setNewCommissionCount(commissionService.getNewCommissionCountThisWeek());
            report.setTopEarners(commissionService.getTopEarnersThisWeek(10));
            report.setCommissionTrend(commissionService.getCommissionTrendThisWeek());

            // 发送报告给管理员
            notificationService.sendWeeklyCommissionReport(report);

            log.info("周度佣金报告生成完成");

        } catch (Exception e) {
            log.error("生成周度佣金报告失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class CommissionControllerTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @MockBean
    private CommissionApplicationService commissionService;

    @Test
    void testCalculateCommission() {
        // 准备测试数据
        CommissionCalculationRequest request = new CommissionCalculationRequest();
        request.setOrderId(1001L);
        request.setOrderAmount(new BigDecimal("5000.00"));
        request.setInviterId(100L);
        request.setInviterRole("seller");

        CommissionCalculationResult expectedResult = new CommissionCalculationResult();
        expectedResult.setCommissionAmount(new BigDecimal("250.00"));
        expectedResult.setCalculationDetails("阶梯计算: 5000 * 0.05 = 250");

        // Mock服务调用
        when(commissionService.calculateCommission(any(CommissionCalculationRequest.class)))
            .thenReturn(expectedResult);

        // 执行测试
        HttpEntity<CommissionCalculationRequest> entity = new HttpEntity<>(request);
        ResponseEntity<Result> response = restTemplate.postForEntity(
            "/api/v1/commission/calculate", entity, Result.class);

        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // 验证服务调用
        verify(commissionService, times(1)).calculateCommission(any(CommissionCalculationRequest.class));
    }
}
```

## 注意事项 (Notes)
*   **权限控制**: 使用Spring Security的@PreAuthorize注解进行权限验证，确保只有相应角色的用户才能访问对应功能
*   **佣金计算**: 支持多种计算规则，包括固定比例、阶梯计算、自定义规则等，需要根据业务需求配置
*   **数据一致性**: 佣金计算和状态更新需要保证数据一致性，使用事务控制和乐观锁机制
*   **异步处理**: 佣金计算等耗时操作使用异步处理，提高系统响应性能
*   **缓存策略**: 佣金统计等频繁查询的数据使用缓存优化，减少数据库压力
*   **审计日志**: 所有佣金相关操作都需要记录详细的审计日志，满足合规要求
*   **异常处理**: 完善的异常处理机制，确保佣金计算错误时能够及时发现和处理
*   **批量操作**: 批量操作需要控制数据量，避免长时间锁定和内存溢出
*   **状态管理**: 佣金记录的状态流转需要严格控制，防止非法状态变更
*   **通知机制**: 重要的佣金状态变更需要及时通知相关用户，提升用户体验
*   **性能监控**: 佣金计算的性能需要持续监控，及时发现和解决性能瓶颈
*   **数据安全**: 佣金数据涉及财务信息，需要严格的数据加密和访问控制
*   **定时任务**: 月度结算等定时任务需要考虑分布式环境下的任务调度和幂等性
*   **错误恢复**: 佣金计算失败时需要有完善的错误恢复机制，确保数据完整性
*   **国际化**: 佣金相关的错误消息和通知需要支持多语言，便于国际化部署
*   **API版本**: 佣金API的版本管理需要考虑向后兼容性，避免影响现有功能