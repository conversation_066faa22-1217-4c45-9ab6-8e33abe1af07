# CommissionValidationService.md

## 1. 文件概述

`CommissionValidationService.java` 是佣金模块中的一个应用服务，位于 `com.purchase.commission.application.service` 包中。它专注于验证月度佣金处理的数据一致性。该服务通过执行一系列的校验规则，确保佣金记录和月度汇总数据之间的逻辑正确性，并识别潜在的数据问题（如遗漏的待处理记录、汇总数据不一致、孤立记录等）。它还集成了通知适配器，在发现问题时能够及时向管理员发送告警。该服务通常作为定时任务或手动触发的后台操作运行，是佣金系统数据质量保障的关键组成部分。

## 2. 核心功能

*   **月度数据校验**: 提供了 `validateMonth` 方法，用于对指定月份的佣金数据进行全面的校验，包括佣金记录和月度汇总的完整性和一致性。
*   **定时任务**: 通过 `@Scheduled` 注解，配置了定时任务，每月自动执行上个月的数据校验，实现自动化监控。
*   **问题识别**: 能够识别多种数据不一致问题，如：
    *   存在未处理的 `PENDING` 状态佣金记录。
    *   月度汇总数据（订单总金额、佣金总金额、订单数量）与实际佣金记录不一致。
    *   佣金计算结果与预期不符（警告）。
    *   存在孤立的汇总记录（无对应佣金记录）或缺少汇总记录的佣金记录。
*   **告警通知**: 集成了 `CommissionNotificationAdapter`，在发现错误或警告时，能够异步发送详细的告警通知给管理员。
*   **手动触发与历史查询**: 提供了 `manualValidateMonth` 方法供手动触发校验，以及 `getRecentValidationStatus` 方法查询最近几个月的校验结果，便于运营人员进行数据审计和问题排查。
*   **结果封装**: 内部定义了 `ValidationResult` 静态类，用于封装校验过程中的错误和警告信息，并提供汇总摘要。

## 3. 接口说明

### 3.1 核心校验方法

#### validateMonth - 校验指定月份的数据
*   **方法签名**: `ValidationResult validateMonth(MonthKey monthKey)`
*   **描述**: 对指定月份的佣金记录和月度汇总数据进行全面的数据一致性校验。
*   **参数**:
    *   `monthKey` (MonthKey): 需要校验的月份键。
*   **返回值**: `ValidationResult` - 包含校验结果（是否通过、错误列表、警告列表、摘要）的对象。
*   **业务逻辑**: 
    1.  初始化 `ValidationResult`。
    2.  调用 `validatePendingRecords` 检查未处理记录。
    3.  调用 `validateSummaryConsistency` 验证月度汇总数据与实际记录的一致性。
    4.  调用 `validateCommissionCalculation` 验证佣金计算的准确性。
    5.  调用 `validateDataIntegrity` 验证数据完整性（孤立记录、缺少汇总）。
    6.  返回最终的 `ValidationResult`。

### 3.2 定时任务与手动触发

#### validateMonthlyProcessing - 月度数据校验定时任务
*   **方法签名**: `void validateMonthlyProcessing()`
*   **描述**: 每月2-3号凌晨2:30自动执行，校验上个月的佣金数据。在发现问题时发送通知。
*   **业务逻辑**: 
    1.  获取上个月的 `MonthKey`。
    2.  调用 `validateMonth` 执行校验。
    3.  根据 `ValidationResult` 的结果，调用 `notificationAdapter` 发送成功、警告或错误通知。
    4.  捕获并记录执行过程中的异常。

#### manualValidateMonth - 手动校验指定月份
*   **方法签名**: `ValidationResult manualValidateMonth(MonthKey monthKey)`
*   **描述**: 提供给外部手动触发指定月份数据校验的接口。
*   **参数**:
    *   `monthKey` (MonthKey): 需要校验的月份键。
*   **返回值**: `ValidationResult` - 校验结果。

#### getRecentValidationStatus - 获取最近几个月的校验状态
*   **方法签名**: `List<ValidationResult> getRecentValidationStatus(int months)`
*   **描述**: 获取最近指定月份的佣金数据校验结果列表。
*   **参数**:
    *   `months` (int): 需要获取的月份数量。
*   **返回值**: `List<ValidationResult>` - 校验结果列表。

### 3.3 辅助校验方法

#### validatePendingRecords - 检查是否有遗漏的PENDING记录
*   **方法签名**: `private void validatePendingRecords(MonthKey monthKey, ValidationResult result)`
*   **描述**: 查询指定月份是否存在状态为 `PENDING` 的佣金记录，如果存在则添加错误。

#### validateSummaryConsistency - 验证月度汇总数据一致性
*   **方法签名**: `private void validateSummaryConsistency(MonthKey monthKey, ValidationResult result)`
*   **描述**: 遍历所有月度汇总记录，与实际的佣金记录进行比对，验证订单总金额、佣金总金额和订单数量是否一致。

#### validateCommissionCalculation - 验证佣金计算准确性
*   **方法签名**: `private void validateCommissionCalculation(MonthKey monthKey, ValidationResult result)`
*   **描述**: 遍历所有已确认的佣金记录，验证每条记录的佣金金额是否与其订单金额和佣金比例计算结果一致。

#### validateDataIntegrity - 验证数据完整性
*   **方法签名**: `private void validateDataIntegrity(MonthKey monthKey, ValidationResult result)`
*   **描述**: 检查是否存在孤立的汇总记录（无对应佣金记录）或缺少汇总记录的佣金记录。

#### isAmountEqual - 比较金额是否相等
*   **方法签名**: `private boolean isAmountEqual(Money amount1, Money amount2)`
*   **描述**: 比较两个 `Money` 对象是否相等，允许小的精度差异。

## 4. 业务规则

*   **数据一致性**: 佣金记录和月度汇总数据之间必须保持严格的一致性。任何不一致都将被标记为错误或警告。
*   **定时执行**: 月度校验任务通过定时调度确保数据质量的持续监控。
*   **告警机制**: 发现数据问题时，通过通知适配器及时通知管理员，以便快速介入处理。
*   **容错性**: 校验过程中的异常不会中断整个校验流程，而是被捕获并记录。
*   **精度容忍**: `isAmountEqual` 方法允许金额之间存在微小的精度差异，这在浮点数计算中是常见的处理方式。

## 5. 使用示例

```java
// 1. 在 MonthlyProcessingController 中调用 CommissionValidationService
@RestController
@RequestMapping("/api/v1/commission/monthly")
public class MonthlyProcessingController {
    @Autowired
    private MonthlyCommissionProcessor monthlyProcessor;
    @Autowired
    private CommissionValidationService validationService;

    @PostMapping("/validate/{monthKey}")
    @PreAuthorize("hasAuthority('admin')")
    public ResponseEntity<Result<CommissionValidationService.ValidationResult>> manualValidateMonth(@PathVariable String monthKey) {
        try {
            MonthKey month = MonthKey.of(monthKey);
            CommissionValidationService.ValidationResult result = validationService.manualValidateMonth(month);
            
            // 将 ValidationResult 转换为 DTO 返回
            // ValidationResultDTO dto = ValidationResultDTO.fromResult(result);
            
            log.info("手动数据校验完成: {}, 结果: {}", monthKey, result.getSummary());
            return ResponseEntity.ok(Result.success(result)); // 直接返回领域层的ValidationResult
            
        } catch (Exception e) {
            log.error("手动数据校验失败: monthKey={}", monthKey, e);
            return ResponseEntity.internalServerError().body(Result.error("校验失败: " + e.getMessage()));
        }
    }

    @GetMapping("/validation/recent")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder')")
    public ResponseEntity<Result<List<CommissionValidationService.ValidationResult>>> getRecentValidationStatus(
            @RequestParam(defaultValue = "3") int months) {
        try {
            List<CommissionValidationService.ValidationResult> results = 
                validationService.getRecentValidationStatus(months);
            
            return ResponseEntity.ok(Result.success(results));
            
        } catch (Exception e) {
            log.error("获取校验状态失败", e);
            return ResponseEntity.internalServerError().body(Result.error("获取校验状态失败: " + e.getMessage()));
        }
    }
}

// 2. 测试示例
@SpringBootTest
class CommissionValidationServiceTest {
    @Autowired
    private CommissionValidationService validationService;

    @MockBean
    private CommissionRecordRepository commissionRecordRepository;
    @MockBean
    private MonthlyCommissionSummaryRepository monthlySummaryRepository;
    @MockBean
    private CommissionNotificationAdapter notificationAdapter;

    @Test
    void testValidateMonth_Success() {
        MonthKey monthKey = MonthKey.of(LocalDate.of(2024, 7, 1));
        
        // 模拟所有数据都一致的情况
        when(commissionRecordRepository.findByMonthKeyAndStatusAndDeleted(eq(monthKey), eq(CommissionRecord.Status.PENDING), eq(false))).thenReturn(Collections.emptyList());
        
        MonthlyCommissionSummary summary = MonthlyCommissionSummary.create(1L, monthKey);
        summary.updateSummary(Money.of("1000"), CommissionRate.of(0.01), Money.of("10"), Money.zero(), 1);
        when(monthlySummaryRepository.findByMonthKey(monthKey)).thenReturn(List.of(summary));
        when(monthlySummaryRepository.existsByInviterIdAndMonthKey(anyLong(), any(MonthKey.class))).thenReturn(true);

        CommissionRecord record = CommissionRecord.create(1L, 10L, 100L, CommissionRecord.OrderType.PURCHASE, Money.of("1000"), new CommissionRate(0.01), monthKey);
        record.setStatus(CommissionRecord.Status.CONFIRMED);
        when(commissionRecordRepository.findByInviterIdAndMonthKeyAndStatusAndDeleted(eq(1L), eq(monthKey), eq(CommissionRecord.Status.CONFIRMED), eq(false))).thenReturn(List.of(record));
        when(commissionRecordRepository.findDistinctInviterIdsByMonthKeyAndStatusAndDeleted(eq(monthKey), eq(CommissionRecord.Status.CONFIRMED), eq(false))).thenReturn(List.of(1L));

        CommissionValidationService.ValidationResult result = validationService.validateMonth(monthKey);

        assertThat(result.isValid()).isTrue();
        assertThat(result.hasErrors()).isFalse();
        assertThat(result.hasWarnings()).isFalse();
        verify(notificationAdapter, never()).sendValidationAlert(any(MonthKey.class), anyString());
    }

    @Test
    void testValidateMonth_PendingRecordsFound() {
        MonthKey monthKey = MonthKey.of(LocalDate.of(2024, 7, 1));
        CommissionRecord pendingRecord = CommissionRecord.create(1L, 10L, 100L, CommissionRecord.OrderType.PURCHASE, Money.of("100"), new CommissionRate(0.01), monthKey);
        pendingRecord.setStatus(CommissionRecord.Status.PENDING);
        when(commissionRecordRepository.findByMonthKeyAndStatusAndDeleted(eq(monthKey), eq(CommissionRecord.Status.PENDING), eq(false))).thenReturn(List.of(pendingRecord));

        CommissionValidationService.ValidationResult result = validationService.validateMonth(monthKey);

        assertThat(result.isValid()).isFalse();
        assertThat(result.hasErrors()).isTrue();
        assertThat(result.getErrors().get(0)).contains("存在未处理的PENDING记录");
        verify(notificationAdapter, times(1)).sendValidationAlert(eq(monthKey), anyString());
    }
}
```

## 6. 注意事项

*   **DDD分层**: `CommissionValidationService` 明确属于应用服务层，其职责是协调领域服务和仓储，实现业务用例。它不包含核心领域逻辑，而是委托给领域实体和领域服务。
*   **事务管理**: 校验服务通常是只读操作，因此不需要 `@Transactional` 注解。但如果校验过程中涉及数据修改（例如修复数据），则需要添加事务。
*   **定时任务**: `@Scheduled` 注解使得该服务能够自动执行月度校验，是实现自动化运维的关键。
*   **通知机制**: 服务与 `CommissionNotificationAdapter` 紧密集成，在发现数据问题时能够及时发送告警通知，提高了系统的可观测性和响应能力。
*   **数据一致性校验**: 实现了多项数据一致性校验规则，包括佣金记录状态、月度汇总与记录的匹配、佣金计算准确性以及数据完整性（孤立/缺失记录）。
*   **精度容忍**: `isAmountEqual` 方法在比较金额时允许微小的精度差异，这在处理浮点数时是必要的。
*   **日志记录**: 服务中使用了 `log.info`, `log.warn`, `log.error` 记录关键操作和异常信息，这对于监控和问题排查非常重要。
*   **可扩展性**: 如果未来需要增加新的校验规则，可以在 `validateMonth` 方法中添加新的私有校验方法。