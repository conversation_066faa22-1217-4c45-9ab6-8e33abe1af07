# CommissionApplicationService.md

## 1. 文件概述

`CommissionApplicationService.java` 是佣金模块中的一个应用服务，位于 `com.purchase.commission.application.service` 包中。它负责协调领域服务和仓储，处理佣金相关的业务用例。该服务是佣金计算和管理流程的入口点，它监听订单完成事件，触发佣金记录的创建、月度佣金汇总的更新，并提供佣金记录的查询、取消、支付标记以及月度结算的启动和完成等功能。它通过封装复杂的业务流程，将领域逻辑暴露为简洁的API，确保了佣金业务的正确性和数据一致性。

## 2. 核心功能

*   **订单完成事件处理**: 监听并处理订单完成事件，这是佣金计算的触发点。它会验证订单是否符合佣金条件，查找邀请关系，并根据邀请人角色决定是生成佣金记录还是优惠券（目前优惠券功能未开放）。
*   **佣金记录创建**: 根据订单信息和计算出的佣金比例，创建新的佣金记录并持久化。
*   **月度佣金汇总更新**: 实时更新邀请人的月度佣金汇总数据，包括订单总金额、总佣金、佣金比例和月度奖金。
*   **佣金记录管理**: 提供取消佣金记录和标记佣金为已支付的功能。
*   **月度结算流程管理**: 支持启动和完成月度结算流程，并标记相关佣金记录为已支付。
*   **佣金配置查询**: 提供获取佣金阶梯配置和月度奖金配置的功能，用于前端展示或业务逻辑判断。
*   **多维度查询**: 支持查询邀请者的月度佣金汇总和佣金记录列表。
*   **DDD协调**: 协调 `CommissionCalculationService`（领域服务）进行佣金计算，以及多个仓储（`CommissionConfigRepository`, `CommissionRecordRepository`, `MonthlyCommissionSummaryRepository`, `InviterCouponRepository`）进行数据持久化。

## 3. 接口说明

### 3.1 佣金业务处理方法

#### handleOrderCompleted - 处理订单完成事件，创建佣金记录
*   **方法签名**: `void handleOrderCompleted(Long orderId, Long buyerId, Money orderAmount, String orderType, LocalDate completedAt)`
*   **描述**: 订单完成时调用，用于判断是否生成佣金记录或优惠券，并执行相应的业务逻辑。
*   **参数**:
    *   `orderId` (Long): 订单ID。
    *   `buyerId` (Long): 订单创建者ID（可能是买家或卖家）。
    *   `orderAmount` (Money): 订单金额。
    *   `orderType` (String): 订单类型（`purchase`, `logistics`）。
    *   `completedAt` (LocalDate): 订单完成日期。
*   **业务逻辑**: 
    1.  验证订单是否符合佣金条件。
    2.  通过 `getInviterByUserId` 查找订单创建者的邀请人ID。
    3.  如果邀请人是买家，则跳过佣金处理（优惠券功能未开放）。
    4.  检查是否已存在佣金记录，避免重复创建。
    5.  计算当月已有的订单总金额和新的月度总金额。
    6.  调用 `calculationService.calculateCommissionRate` 计算佣金比例。
    7.  创建 `CommissionRecord` 实体并保存。
    8.  调用 `updateMonthlySummary` 更新月度佣金汇总。

#### cancelCommission - 取消佣金记录
*   **方法签名**: `void cancelCommission(Long commissionRecordId)`
*   **描述**: 取消指定ID的佣金记录，并更新月度汇总。
*   **参数**:
    *   `commissionRecordId` (Long): 佣金记录ID。
*   **业务逻辑**: 
    1.  查找佣金记录，如果不存在则抛出异常。
    2.  调用 `CommissionRecord.cancel()` 标记记录为已取消。
    3.  保存更新后的佣金记录。
    4.  调用 `updateMonthlySummary` 重新计算并更新月度汇总。

#### markCommissionsAsPaid - 标记佣金为已支付
*   **方法签名**: `void markCommissionsAsPaid(List<Long> commissionRecordIds)`
*   **描述**: 批量标记佣金记录为已支付。
*   **参数**:
    *   `commissionRecordIds` (List<Long>): 佣金记录ID列表。
*   **业务逻辑**: 遍历ID列表，查找每条佣金记录，调用 `CommissionRecord.markAsPaid()`，然后保存。

#### startMonthlySettlement - 开始月度结算
*   **方法签名**: `void startMonthlySettlement(Long inviterId, MonthKey monthKey)`
*   **描述**: 启动指定邀请者和月份的月度结算流程。
*   **业务逻辑**: 查找月度汇总记录，调用 `MonthlyCommissionSummary.startSettlement()`，然后保存。

#### completeMonthlySettlement - 完成月度结算
*   **方法签名**: `void completeMonthlySettlement(Long inviterId, MonthKey monthKey, LocalDateTime settlementDate)`
*   **描述**: 完成指定邀请者和月份的月度结算流程，并标记相关佣金记录为已支付。
*   **业务逻辑**: 查找月度汇总记录，调用 `MonthlyCommissionSummary.completeSettlement()`，保存。然后查找所有可结算的佣金记录，并调用 `markCommissionsAsPaid` 批量标记为已支付。

### 3.2 佣金查询方法

#### getMonthlySummary - 获取邀请者的月度佣金汇总
*   **方法签名**: `Optional<MonthlyCommissionSummary> getMonthlySummary(Long inviterId, MonthKey monthKey)`
*   **描述**: 获取指定邀请者和月份的月度佣金汇总记录。
*   **参数**:
    *   `inviterId` (Long): 邀请者ID。
    *   `monthKey` (MonthKey): 月份键。
*   **返回值**: `Optional<MonthlyCommissionSummary>` - 月度佣金汇总记录。

#### getCommissionRecords - 获取邀请者的佣金记录列表
*   **方法签名**: `List<CommissionRecord> getCommissionRecords(Long inviterId, Integer limit, Integer offset)`
*   **描述**: 获取指定邀请者的佣金记录列表，支持分页。
*   **参数**:
    *   `inviterId` (Long): 邀请者ID。
    *   `limit` (Integer): 限制数量。
    *   `offset` (Integer): 偏移量。
*   **返回值**: `List<CommissionRecord>` - 佣金记录列表。

#### getCommissionRecordsCount - 获取佣金记录总数
*   **方法签名**: `Integer getCommissionRecordsCount(Long inviterId)`
*   **描述**: 获取指定邀请者的佣金记录总数。
*   **参数**:
    *   `inviterId` (Long): 邀请者ID。
*   **返回值**: `Integer` - 总记录数。

#### getMonthlyCommissionRecords - 获取指定月份的佣金记录
*   **方法签名**: `List<CommissionRecord> getMonthlyCommissionRecords(Long inviterId, MonthKey monthKey)`
*   **描述**: 获取指定邀请者在指定月份的佣金记录列表。
*   **参数**:
    *   `inviterId` (Long): 邀请者ID。
    *   `monthKey` (MonthKey): 月份键。
*   **返回值**: `List<CommissionRecord>` - 佣金记录列表。

#### getCommissionTiers - 获取佣金阶梯配置
*   **方法签名**: `List<Map<String, Object>> getCommissionTiers()`
*   **描述**: 获取所有启用的佣金阶梯配置，并转换为Map列表。
*   **返回值**: `List<Map<String, Object>>` - 佣金阶梯配置列表。

#### getMonthlyBonusConfig - 获取月度奖金配置
*   **方法签名**: `List<Map<String, Object>> getMonthlyBonusConfig()`
*   **描述**: 获取所有启用的月度奖金配置，并转换为Map列表。
*   **返回值**: `List<Map<String, Object>>` - 月度奖金配置列表。

### 3.3 辅助方法

#### calculateMonthlyTotalAmount - 计算指定月份的订单总金额
*   **方法签名**: `private Money calculateMonthlyTotalAmount(Long inviterId, MonthKey monthKey)`
*   **描述**: 计算指定邀请者在指定月份所有有效佣金记录的订单总金额。

#### convertOrderType - 转换订单类型
*   **方法签名**: `private CommissionRecord.OrderType convertOrderType(String orderType)`
*   **描述**: 将字符串订单类型转换为领域对象 `CommissionRecord.OrderType`。

#### getInviterByUserId - 根据用户ID获取邀请者ID
*   **方法签名**: `private Long getInviterByUserId(Long userId)`
*   **描述**: 通过 `UserMapper` 查询用户的邀请关系，获取邀请者ID。

#### convertCommissionTierToMap - 将佣金阶梯配置转换为Map
*   **方法签名**: `private Map<String, Object> convertCommissionTierToMap(CommissionConfig config)`
*   **描述**: 将 `CommissionConfig` 实体（佣金阶梯类型）转换为Map，用于前端展示。

#### convertMonthlyBonusToMap - 将月度奖金配置转换为Map
*   **方法签名**: `private Map<String, Object> convertMonthlyBonusToMap(CommissionConfig config)`
*   **描述**: 将 `CommissionConfig` 实体（月度奖金类型）转换为Map，用于前端展示。

## 4. 业务规则

*   **DDD分层**: `CommissionApplicationService` 明确属于应用服务层，其职责是协调领域服务和仓储，实现业务用例。它不包含核心领域逻辑，而是委托给领域实体和领域服务。
*   **事务管理**: 所有涉及数据修改的方法都使用 `@Transactional` 注解，确保操作的原子性和数据一致性。
*   **事件驱动**: `handleOrderCompleted` 方法是事件驱动的典型应用，它响应订单完成事件，触发佣金计算流程。
*   **邀请关系**: 佣金的生成依赖于用户之间的邀请关系，通过 `UserMapper` 获取。
*   **佣金计算逻辑**: 佣金比例和月度奖金的计算委托给 `CommissionCalculationService` 领域服务，确保了业务规则的封装和可维护性。
*   **月度汇总**: `updateMonthlySummary` 方法确保了月度佣金汇总数据的实时性和准确性，它会重新计算当月所有有效佣金记录的汇总。
*   **优惠券功能**: `generateCouponForBuyerInviter` 方法虽然已实现，但目前被标记为“未开放”，表明该功能在生产环境中尚未启用。
*   **异常处理**: 服务在遇到业务规则冲突或数据不存在时，会抛出 `IllegalArgumentException` 或 `BusinessException`，并包含明确的错误信息，便于上层处理。
*   **日志记录**: 服务中使用了 `log.info` 和 `log.error` 记录关键操作和异常信息，这对于监控和问题排查非常重要。

## 5. 使用示例

```java
// 1. 在 OrderCompletedEventListener 中调用 CommissionApplicationService
@Component
@RequiredArgsConstructor
public class OrderCompletedEventListener {
    private final CommissionApplicationService commissionApplicationService;

    @EventListener
    public void handleOrderCompletedEvent(OrderCompletedEvent event) {
        commissionApplicationService.handleOrderCompleted(
            event.getOrderId(),
            event.getBuyerId(), // 订单创建者ID
            event.getOrderAmount(),
            event.getOrderType(),
            event.getCompletedAt().toLocalDate()
        );
    }
}

// 2. 在 Controller 中调用 CommissionApplicationService 获取佣金记录
@RestController
@RequestMapping("/api/v1/commissions")
public class CommissionController {
    @Autowired
    private CommissionApplicationService commissionApplicationService;

    @GetMapping("/records")
    public Result<List<CommissionRecord>> getMyCommissionRecords(
            @RequestParam Long inviterId,
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(defaultValue = "0") Integer offset) {
        List<CommissionRecord> records = commissionApplicationService.getCommissionRecords(inviterId, limit, offset);
        return Result.success(records);
    }

    @GetMapping("/monthly-summary")
    public Result<MonthlyCommissionSummary> getMonthlySummary(
            @RequestParam Long inviterId,
            @RequestParam String monthKey) {
        Optional<MonthlyCommissionSummary> summary = commissionApplicationService.getMonthlySummary(inviterId, MonthKey.of(monthKey));
        return summary.map(Result::success).orElseGet(() -> Result.error("未找到月度汇总"));
    }

    @GetMapping("/tiers")
    public Result<List<Map<String, Object>>> getCommissionTiers() {
        List<Map<String, Object>> tiers = commissionApplicationService.getCommissionTiers();
        return Result.success(tiers);
    }
}

// 3. 测试示例
@SpringBootTest
class CommissionApplicationServiceTest {
    @Autowired
    private CommissionApplicationService commissionApplicationService;

    @MockBean
    private CommissionConfigRepository commissionConfigRepository;
    @MockBean
    private CommissionRecordRepository commissionRecordRepository;
    @MockBean
    private MonthlyCommissionSummaryRepository monthlySummaryRepository;
    @MockBean
    private InviterCouponRepository inviterCouponRepository;
    @MockBean
    private CommissionCalculationService calculationService;
    @MockBean
    private InviterCouponService inviterCouponService;
    @MockBean
    private UserMapper userMapper;

    @Test
    @Transactional
    void testHandleOrderCompleted_CreatesCommissionRecord() {
        Long orderId = 1L;
        Long buyerId = 10L;
        Money orderAmount = Money.of("1000.00");
        String orderType = "purchase";
        LocalDate completedAt = LocalDate.now();
        Long inviterId = 20L;
        MonthKey monthKey = MonthKey.of(completedAt);

        // 模拟依赖行为
        when(calculationService.isValidCommissionOrder(anyString(), anyString(), anyBoolean())).thenReturn(true);
        when(userMapper.selectById(buyerId)).thenReturn(new User(buyerId, "buyer", inviterId)); // 模拟用户有邀请人
        when(userMapper.selectRoleById(inviterId)).thenReturn("seller"); // 模拟邀请人是卖家
        when(commissionRecordRepository.existsByOrderIdAndInviterId(orderId, inviterId)).thenReturn(false);
        when(commissionRecordRepository.findByInviterIdAndMonthKey(inviterId, monthKey)).thenReturn(new ArrayList<>());
        when(calculationService.calculateCommissionRate(any(Money.class))).thenReturn(new CommissionRate(0.01));
        when(monthlySummaryRepository.findByInviterIdAndMonthKey(inviterId, monthKey)).thenReturn(Optional.empty());
        when(monthlySummaryRepository.save(any(MonthlyCommissionSummary.class))).thenReturn(new MonthlyCommissionSummary());
        when(commissionRecordRepository.save(any(CommissionRecord.class))).thenReturn(new CommissionRecord());

        commissionApplicationService.handleOrderCompleted(orderId, buyerId, orderAmount, orderType, completedAt);

        verify(commissionRecordRepository, times(1)).save(any(CommissionRecord.class));
        verify(monthlySummaryRepository, times(1)).save(any(MonthlyCommissionSummary.class));
    }

    @Test
    @Transactional
    void testCancelCommission_UpdatesMonthlySummary() {
        Long commissionRecordId = 1L;
        Long inviterId = 20L;
        MonthKey monthKey = MonthKey.of(LocalDate.now());

        CommissionRecord mockRecord = CommissionRecord.create(inviterId, 10L, 100L, CommissionRecord.OrderType.PURCHASE, Money.of("500"), new CommissionRate(0.01), monthKey);
        when(commissionRecordRepository.findById(commissionRecordId)).thenReturn(Optional.of(mockRecord));
        when(commissionRecordRepository.findByInviterIdAndMonthKey(inviterId, monthKey)).thenReturn(List.of(mockRecord));
        when(monthlySummaryRepository.findByInviterIdAndMonthKey(inviterId, monthKey)).thenReturn(Optional.of(MonthlyCommissionSummary.create(inviterId, monthKey)));

        commissionApplicationService.cancelCommission(commissionRecordId);

        verify(commissionRecordRepository, times(1)).save(mockRecord);
        verify(monthlySummaryRepository, times(1)).save(any(MonthlyCommissionSummary.class));
        assertThat(mockRecord.getStatus()).isEqualTo(CommissionRecord.Status.CANCELLED);
    }
}
```

## 6. 注意事项

*   **DDD分层**: `CommissionApplicationService` 明确属于应用服务层，其职责是协调领域服务和仓储，实现业务用例。它不包含核心领域逻辑，而是委托给领域实体和领域服务。
*   **事务管理**: 所有涉及数据修改的方法都使用 `@Transactional` 注解，确保操作的原子性和数据一致性。
*   **事件驱动**: `handleOrderCompleted` 方法是事件驱动的典型应用，它响应订单完成事件，触发佣金计算流程。
*   **邀请关系**: 佣金的生成依赖于用户之间的邀请关系，通过 `UserMapper` 获取。
*   **佣金计算逻辑**: 佣金比例和月度奖金的计算委托给 `CommissionCalculationService` 领域服务，确保了业务规则的封装和可维护性。
*   **月度汇总**: `updateMonthlySummary` 方法确保了月度佣金汇总数据的实时性和准确性，它会重新计算当月所有有效佣金记录的汇总。
*   **优惠券功能**: `generateCouponForBuyerInviter` 方法虽然已实现，但目前被标记为“未开放”，表明该功能在生产环境中尚未启用。
*   **异常处理**: 服务在遇到业务规则冲突或数据不存在时，会抛出 `IllegalArgumentException` 或 `BusinessException`，并包含明确的错误信息，便于上层处理。
*   **日志记录**: 服务中使用了 `log.info` 和 `log.error` 记录关键操作和异常信息，这对于监控和问题排查非常重要。
*   **依赖注入**: 服务通过构造函数注入其依赖，遵循了依赖倒置原则。