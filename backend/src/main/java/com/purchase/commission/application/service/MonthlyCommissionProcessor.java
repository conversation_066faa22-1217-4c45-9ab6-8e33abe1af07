package com.purchase.commission.application.service;

import com.purchase.commission.domain.entity.CommissionProcessingLog;
import com.purchase.commission.domain.entity.CommissionRecord;
import com.purchase.commission.domain.repository.CommissionProcessingLogRepository;
import com.purchase.commission.domain.repository.CommissionRecordRepository;
import com.purchase.commission.domain.valueobject.MonthKey;
import com.purchase.commission.domain.valueobject.ProcessingResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 月度佣金处理器
 * 负责月初统一处理上月的佣金记录
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MonthlyCommissionProcessor {
    
    private final CommissionBatchService batchService;
    private final CommissionProcessingLogRepository processingLogRepository;
    private final CommissionRecordRepository commissionRecordRepository;
    private final CommissionNotificationAdapter notificationAdapter;
    
    /**
     * 月度佣金处理定时任务
     * 每月1-3号凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 1-3 * ?")
    public void processMonthlyCommissions() {
        MonthKey lastMonth = MonthKey.of(LocalDate.now().minusMonths(1));
        
        // 防重复检查
        if (isAlreadyProcessed(lastMonth)) {
            log.info("月度佣金处理任务已完成: {}", lastMonth);
            return;
        }
        
        log.info("开始处理月度佣金: {}", lastMonth);
        
        try {
            doProcessMonthlyCommissions(lastMonth);
        } catch (Exception e) {
            log.error("月度佣金处理失败: {}", lastMonth, e);
            // 发送错误告警
            notificationAdapter.sendErrorAlert(lastMonth, e);
        }
    }
    
    /**
     * 检查是否已经处理过
     */
    private boolean isAlreadyProcessed(MonthKey monthKey) {
        return processingLogRepository.existsByMonthKeyAndStatusIn(
            monthKey, 
            Arrays.asList(CommissionProcessingLog.Status.PROCESSING, CommissionProcessingLog.Status.SUCCESS)
        );
    }
    
    /**
     * 执行月度佣金处理
     */
    private void doProcessMonthlyCommissions(MonthKey monthKey) {
        // 创建处理日志（防重复）
        CommissionProcessingLog processingLog = createProcessingLog(monthKey);
        
        try {
            // 查询待处理记录
            List<CommissionRecord> pendingRecords = commissionRecordRepository
                .findByMonthKeyAndStatusAndDeleted(monthKey, CommissionRecord.Status.PENDING, false);
            
            if (pendingRecords.isEmpty()) {
                log.info("上月无待处理佣金记录: {}", monthKey);
                processingLog.updateResult(0, 0, 0);
                processingLog.markAsSuccess();
                processingLogRepository.save(processingLog);
                return;
            }
            
            log.info("查询到待处理佣金记录: {} 条", pendingRecords.size());
            
            // 按邀请者分组处理
            Map<Long, List<CommissionRecord>> groupedRecords = pendingRecords.stream()
                .collect(Collectors.groupingBy(CommissionRecord::getInviterId));
            
            log.info("按邀请者分组: {} 个邀请者", groupedRecords.size());
            
            // 批量处理
            ProcessingResult result = batchService.batchProcessCommissions(groupedRecords, monthKey);
            
            // 更新处理日志
            updateProcessingLog(processingLog, result);
            
            // 发送处理结果通知
            sendProcessingNotification(monthKey, result);
            
            log.info("月度佣金处理完成: {}, 成功: {}, 失败: {}", 
                    monthKey, result.getSuccessCount(), result.getFailureCount());
                    
        } catch (Exception e) {
            processingLog.markAsFailed(e.getMessage());
            processingLogRepository.save(processingLog);
            throw e;
        }
    }
    
    /**
     * 创建处理日志
     */
    private CommissionProcessingLog createProcessingLog(MonthKey monthKey) {
        try {
            CommissionProcessingLog log = CommissionProcessingLog.create(monthKey);
            return processingLogRepository.save(log);
        } catch (DataIntegrityViolationException e) {
            // 唯一约束冲突，说明已经在处理
            throw new IllegalStateException("月度佣金处理任务已在执行: " + monthKey);
        }
    }
    
    /**
     * 更新处理日志
     */
    private void updateProcessingLog(CommissionProcessingLog processingLog, ProcessingResult result) {
        processingLog.updateResult(
            result.getTotalRecords(),
            result.getSuccessCount(),
            result.getFailureCount()
        );
        
        if (result.hasFailures()) {
            processingLog.setErrorMessage(result.getErrorSummary());
        }
        
        processingLogRepository.save(processingLog);
    }
    
    /**
     * 发送处理结果通知
     */
    private void sendProcessingNotification(MonthKey monthKey, ProcessingResult result) {
        try {
            if (result.hasFailures()) {
                notificationAdapter.sendProcessingAlert(monthKey, result);
            } else {
                notificationAdapter.sendProcessingSuccess(monthKey, result);
            }
        } catch (Exception e) {
            log.warn("发送处理结果通知失败: monthKey={}", monthKey, e);
        }
    }
    
    /**
     * 手动触发月度处理（用于测试或补偿处理）
     */
    public void manualProcessMonth(MonthKey monthKey) {
        log.info("手动触发月度佣金处理: {}", monthKey);
        
        if (isAlreadyProcessed(monthKey)) {
            throw new IllegalStateException("该月份已经处理过: " + monthKey);
        }
        
        try {
            doProcessMonthlyCommissions(monthKey);
        } catch (Exception e) {
            log.error("手动月度佣金处理失败: {}", monthKey, e);
            throw e;
        }
    }
    
    /**
     * 获取处理状态
     */
    public CommissionProcessingLog getProcessingStatus(MonthKey monthKey) {
        return processingLogRepository.findByMonthKey(monthKey).orElse(null);
    }
    
    /**
     * 获取最近的处理日志
     */
    public List<CommissionProcessingLog> getRecentProcessingLogs(int limit) {
        return processingLogRepository.findRecentLogs(limit);
    }
}
