package com.purchase.commission.application.service;

import com.purchase.commission.domain.valueobject.MonthKey;
import com.purchase.commission.domain.valueobject.ProcessingResult;
import com.purchase.message.notification.entity.Notification;
import com.purchase.message.notification.service.NotificationService;
import com.purchase.user.entity.User;
import com.purchase.user.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 佣金通知适配器
 * 使用现有的通知服务发送佣金处理相关的通知
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CommissionNotificationAdapter {
    
    private final NotificationService notificationService;
    private final UserMapper userMapper;
    
    @Value("${commission.notification.enabled:true}")
    private boolean notificationEnabled;
    
    /**
     * 发送处理成功通知
     */
    public void sendProcessingSuccess(MonthKey monthKey, ProcessingResult result) {
        if (!notificationEnabled) {
            log.debug("通知功能已禁用，跳过发送成功通知");
            return;
        }
        
        try {
            String title = String.format("佣金处理成功 - %s", monthKey);
            String content = buildSuccessNotificationContent(monthKey, result);
            
            // 发送给所有管理员
            sendToAllAdmins("COMMISSION_PROCESSING_SUCCESS", title, content, null);
            
            log.info("发送处理成功通知完成: {}", monthKey);
            
        } catch (Exception e) {
            log.error("发送处理成功通知失败: monthKey={}", monthKey, e);
        }
    }
    
    /**
     * 发送处理告警通知
     */
    public void sendProcessingAlert(MonthKey monthKey, ProcessingResult result) {
        if (!notificationEnabled) {
            log.debug("通知功能已禁用，跳过发送告警通知");
            return;
        }
        
        try {
            String title = String.format("佣金处理告警 - %s", monthKey);
            String content = buildAlertNotificationContent(monthKey, result);
            
            // 发送给所有管理员
            sendToAllAdmins("COMMISSION_PROCESSING_ALERT", title, content, null);
            
            log.warn("发送处理告警通知完成: {}", monthKey);
            
        } catch (Exception e) {
            log.error("发送处理告警通知失败: monthKey={}", monthKey, e);
        }
    }
    
    /**
     * 发送错误告警通知
     */
    public void sendErrorAlert(MonthKey monthKey, Exception error) {
        if (!notificationEnabled) {
            log.debug("通知功能已禁用，跳过发送错误告警");
            return;
        }
        
        try {
            String title = String.format("佣金处理错误 - %s", monthKey);
            String content = buildErrorNotificationContent(monthKey, error);
            
            // 发送给所有管理员
            sendToAllAdmins("COMMISSION_PROCESSING_ERROR", title, content, null);
            
            log.error("发送错误告警通知完成: {}", monthKey);
            
        } catch (Exception e) {
            log.error("发送错误告警通知失败: monthKey={}", monthKey, e);
        }
    }
    
    /**
     * 发送数据校验告警通知
     */
    public void sendValidationAlert(MonthKey monthKey, String validationMessage) {
        if (!notificationEnabled) {
            log.debug("通知功能已禁用，跳过发送校验告警");
            return;
        }
        
        try {
            String title = String.format("佣金数据校验告警 - %s", monthKey);
            String content = buildValidationAlertContent(monthKey, validationMessage);
            
            // 发送给所有管理员
            sendToAllAdmins("COMMISSION_VALIDATION_ALERT", title, content, null);
            
            log.warn("发送数据校验告警通知完成: {}", monthKey);
            
        } catch (Exception e) {
            log.error("发送数据校验告警通知失败: monthKey={}", monthKey, e);
        }
    }
    
    /**
     * 发送给所有管理员
     */
    private void sendToAllAdmins(String type, String title, String content, Long relatedId) {
        try {
            List<User> adminUsers = userMapper.selectAllAdmins();
            
            for (User admin : adminUsers) {
                Notification notification = new Notification();
                notification.setType(type);
                notification.setTitle(title);
                notification.setContent(content);
                notification.setReceiverId(admin.getId());
                notification.setReceiverRole("admin");
                notification.setRelatedId(relatedId);
                notification.setCreatedAt(LocalDateTime.now());
                notification.setStatus(0); // 未读状态
                
                notificationService.createNotification(notification);
            }
            
            log.debug("通知已发送给 {} 位管理员", adminUsers.size());
            
        } catch (Exception e) {
            log.error("发送管理员通知失败: type={}, title={}", type, title, e);
        }
    }
    
    /**
     * 构建成功通知内容
     */
    private String buildSuccessNotificationContent(MonthKey monthKey, ProcessingResult result) {
        StringBuilder content = new StringBuilder();
        content.append(String.format("月度佣金处理已成功完成！\n\n"));
        content.append(String.format("处理月份：%s\n", monthKey));
        content.append(String.format("处理时间：%s\n", LocalDateTime.now()));
        
        if (result != null) {
            content.append(String.format("处理结果：\n"));
            content.append(String.format("- 总记录数：%d\n", result.getTotalRecords()));
            content.append(String.format("- 成功处理：%d\n", result.getSuccessCount()));
            content.append(String.format("- 失败记录：%d\n", result.getFailureCount()));
            content.append(String.format("- 成功率：%.2f%%\n", result.getSuccessRate()));
        }
        
        content.append("\n请登录系统查看详细信息。");
        return content.toString();
    }
    
    /**
     * 构建告警通知内容
     */
    private String buildAlertNotificationContent(MonthKey monthKey, ProcessingResult result) {
        StringBuilder content = new StringBuilder();
        content.append(String.format("月度佣金处理出现异常情况！\n\n"));
        content.append(String.format("处理月份：%s\n", monthKey));
        content.append(String.format("处理时间：%s\n", LocalDateTime.now()));
        
        if (result != null) {
            content.append(String.format("处理结果：\n"));
            content.append(String.format("- 总记录数：%d\n", result.getTotalRecords()));
            content.append(String.format("- 成功处理：%d\n", result.getSuccessCount()));
            content.append(String.format("- 失败记录：%d\n", result.getFailureCount()));
            content.append(String.format("- 成功率：%.2f%%\n", result.getSuccessRate()));
            
            if (result.getFailureCount() > 0) {
                content.append(String.format("\n⚠️ 存在 %d 条处理失败的记录，请及时处理！", result.getFailureCount()));
            }
        }
        
        content.append("\n\n请立即登录系统检查并处理异常情况。");
        return content.toString();
    }
    
    /**
     * 构建错误通知内容
     */
    private String buildErrorNotificationContent(MonthKey monthKey, Exception error) {
        StringBuilder content = new StringBuilder();
        content.append(String.format("月度佣金处理发生严重错误！\n\n"));
        content.append(String.format("处理月份：%s\n", monthKey));
        content.append(String.format("错误时间：%s\n", LocalDateTime.now()));
        content.append(String.format("错误信息：%s\n", error.getMessage()));
        
        content.append("\n🚨 请立即登录系统检查错误原因并重新处理！");
        return content.toString();
    }
    
    /**
     * 构建数据校验告警内容
     */
    private String buildValidationAlertContent(MonthKey monthKey, String validationMessage) {
        StringBuilder content = new StringBuilder();
        content.append(String.format("月度佣金数据校验发现异常！\n\n"));
        content.append(String.format("校验月份：%s\n", monthKey));
        content.append(String.format("校验时间：%s\n", LocalDateTime.now()));
        content.append(String.format("异常信息：%s\n", validationMessage));
        
        content.append("\n⚠️ 请检查数据一致性并及时修复！");
        return content.toString();
    }
}
