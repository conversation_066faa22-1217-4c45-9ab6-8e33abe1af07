# CommissionNotificationAdapter.md

## 1. 文件概述

`CommissionNotificationAdapter.java` 是佣金模块中的一个应用服务（适配器），位于 `com.purchase.commission.application.service` 包中。它负责将佣金处理过程中的各种事件（如处理成功、告警、错误、数据校验告警）转换为标准化的通知，并通过现有的通知服务 (`NotificationService`) 发送给相关的用户（主要是管理员）。该适配器旨在解耦佣金处理的核心业务逻辑与通知发送的具体实现，使得佣金模块可以专注于其核心职责，同时确保系统在关键时刻能够及时地向运营人员发出警报和报告。

## 2. 核心功能

*   **通知发送**: 提供了发送多种类型佣金处理通知的方法，包括成功通知、告警通知、错误告警通知和数据校验告警通知。
*   **内容构建**: 内部包含了构建不同类型通知内容的逻辑，将佣金处理结果格式化为用户友好的文本信息。
*   **管理员通知**: 所有通知都默认发送给系统中的所有管理员，确保关键信息能够触达负责佣金管理的团队。
*   **通知开关**: 支持通过配置属性 (`commission.notification.enabled`) 控制通知功能的启用或禁用，方便在不同环境或特定场景下进行管理。
*   **异步发送**: 通知发送被设计为异步操作，避免阻塞佣金处理的主流程。
*   **依赖注入**: 依赖于 `NotificationService` 和 `UserMapper`，通过依赖注入获取所需的服务和数据。

## 3. 接口说明

### 3.1 通知发送方法

#### sendProcessingSuccess - 发送处理成功通知
*   **方法签名**: `void sendProcessingSuccess(MonthKey monthKey, ProcessingResult result)`
*   **描述**: 当月度佣金处理成功完成时调用，发送成功通知给所有管理员。
*   **参数**:
    *   `monthKey` (MonthKey): 处理的月份键。
    *   `result` (ProcessingResult): 佣金处理结果，包含成功/失败记录数等信息。
*   **业务逻辑**: 构建成功通知的标题和内容，然后调用 `sendToAllAdmins` 方法发送通知。

#### sendProcessingAlert - 发送处理告警通知
*   **方法签名**: `void sendProcessingAlert(MonthKey monthKey, ProcessingResult result)`
*   **描述**: 当月度佣金处理过程中出现告警情况（例如有部分记录处理失败）时调用，发送告警通知给所有管理员。
*   **参数**:
    *   `monthKey` (MonthKey): 处理的月份键。
    *   `result` (ProcessingResult): 佣金处理结果。
*   **业务逻辑**: 构建告警通知的标题和内容，然后调用 `sendToAllAdmins` 方法发送通知。

#### sendErrorAlert - 发送错误告警通知
*   **方法签名**: `void sendErrorAlert(MonthKey monthKey, Exception error)`
*   **描述**: 当月度佣金处理发生严重错误（导致处理中断）时调用，发送错误告警通知给所有管理员。
*   **参数**:
    *   `monthKey` (MonthKey): 处理的月份键。
    *   `error` (Exception): 导致错误的异常对象。
*   **业务逻辑**: 构建错误告警通知的标题和内容（包含错误信息），然后调用 `sendToAllAdmins` 方法发送通知。

#### sendValidationAlert - 发送数据校验告警通知
*   **方法签名**: `void sendValidationAlert(MonthKey monthKey, String validationMessage)`
*   **描述**: 当佣金数据校验发现异常时调用，发送数据校验告警通知给所有管理员。
*   **参数**:
    *   `monthKey` (MonthKey): 校验的月份键。
    *   `validationMessage` (String): 校验发现的异常信息。
*   **业务逻辑**: 构建数据校验告警通知的标题和内容，然后调用 `sendToAllAdmins` 方法发送通知。

### 3.2 辅助方法

#### sendToAllAdmins - 发送给所有管理员
*   **方法签名**: `private void sendToAllAdmins(String type, String title, String content, Long relatedId)`
*   **描述**: 内部辅助方法，用于获取所有管理员用户，并为每个管理员创建并发送一个通知。
*   **参数**:
    *   `type` (String): 通知类型代码。
    *   `title` (String): 通知标题。
    *   `content` (String): 通知内容。
    *   `relatedId` (Long): 关联的业务ID（例如佣金处理日志ID）。

#### buildSuccessNotificationContent - 构建成功通知内容
*   **方法签名**: `private String buildSuccessNotificationContent(MonthKey monthKey, ProcessingResult result)`
*   **描述**: 格式化佣金处理成功通知的详细内容。

#### buildAlertNotificationContent - 构建告警通知内容
*   **方法签名**: `private String buildAlertNotificationContent(MonthKey monthKey, ProcessingResult result)`
*   **描述**: 格式化佣金处理告警通知的详细内容，突出失败记录。

#### buildErrorNotificationContent - 构建错误通知内容
*   **方法签名**: `private String buildErrorNotificationContent(MonthKey monthKey, Exception error)`
*   **描述**: 格式化佣金处理错误告警通知的详细内容，包含错误信息。

#### buildValidationAlertContent - 构建数据校验告警内容
*   **方法签名**: `private String buildValidationAlertContent(MonthKey monthKey, String validationMessage)`
*   **描述**: 格式化数据校验告警通知的详细内容。

## 4. 业务规则

*   **通知开关**: `notificationEnabled` 配置项允许灵活控制通知的发送，便于在开发、测试和生产环境中进行管理。
*   **异步发送**: 所有通知发送方法都应被设计为异步的，以避免阻塞调用方的业务流程。这通常通过Spring的 `@Async` 注解实现。
*   **管理员接收**: 佣金处理相关的通知主要发送给管理员，确保运营团队能够及时了解系统状态和潜在问题。
*   **通知内容**: 通知内容应包含足够的信息，以便接收者快速理解事件的性质和影响，并采取相应的行动。
*   **错误容忍**: 通知发送失败不应影响佣金处理的主流程。因此，通知发送方法内部应有健壮的异常处理，只记录日志而不重新抛出异常。

## 5. 使用示例

```java
// 1. 在 MonthlyCommissionProcessor 中调用 CommissionNotificationAdapter
@Service
@RequiredArgsConstructor
@Slf4j
public class MonthlyCommissionProcessor {
    private final CommissionBatchService commissionBatchService;
    private final CommissionRecordRepository commissionRecordRepository;
    private final CommissionProcessingLogRepository commissionProcessingLogRepository;
    private final CommissionNotificationAdapter notificationAdapter; // 注入通知适配器

    @Transactional
    public void manualProcessMonth(MonthKey monthKey) {
        CommissionProcessingLog logEntry = CommissionProcessingLog.create(monthKey);
        commissionProcessingLogRepository.save(logEntry);

        try {
            List<CommissionRecord> recordsToProcess = commissionRecordRepository.findByMonthKeyAndStatus(
                monthKey, CommissionRecord.Status.PENDING
            );
            Map<Long, List<CommissionRecord>> groupedRecords = recordsToProcess.stream()
                .collect(Collectors.groupingBy(CommissionRecord::getInviterId));

            ProcessingResult result = commissionBatchService.batchProcessCommissions(groupedRecords, monthKey);

            logEntry.updateResult(result.getTotalProcessed(), result.getSuccessCount(), result.getFailureCount());
            logEntry.setErrorMessage(result.getErrorMessage());
            
            if (result.getFailureCount() > 0) {
                logEntry.markAsFailed("部分记录处理失败");
                notificationAdapter.sendProcessingAlert(monthKey, result); // 发送告警通知
            } else {
                logEntry.markAsSuccess();
                notificationAdapter.sendProcessingSuccess(monthKey, result); // 发送成功通知
            }

        } catch (Exception e) {
            logEntry.markAsFailed("月度处理失败: " + e.getMessage());
            notificationAdapter.sendErrorAlert(monthKey, e); // 发送错误告警
            throw new BusinessException("月度处理失败", e);
        } finally {
            commissionProcessingLogRepository.save(logEntry);
        }
    }
}

// 2. 测试示例
@SpringBootTest
class CommissionNotificationAdapterTest {
    @Autowired
    private CommissionNotificationAdapter notificationAdapter;

    @MockBean
    private NotificationService notificationService;
    @MockBean
    private UserMapper userMapper;

    @BeforeEach
    void setUp() {
        // 模拟管理员用户
        User admin1 = new User(); admin1.setId(1L); admin1.setUsername("admin1");
        User admin2 = new User(); admin2.setId(2L); admin2.setUsername("admin2");
        when(userMapper.selectAllAdmins()).thenReturn(List.of(admin1, admin2));
    }

    @Test
    void testSendProcessingSuccess() {
        MonthKey monthKey = MonthKey.of(LocalDate.of(2024, 7, 1));
        ProcessingResult result = new ProcessingResult();
        result.addSuccess(10L, 50);
        result.addSuccess(20L, 30);
        result.end();

        notificationAdapter.sendProcessingSuccess(monthKey, result);

        // 验证通知服务被调用了两次（给两个管理员）
        verify(notificationService, times(2)).createNotification(any(Notification.class));
        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);
        verify(notificationService, atLeastOnce()).createNotification(captor.capture());
        
        Notification capturedNotification = captor.getValue();
        assertThat(capturedNotification.getTitle()).contains("佣金处理成功");
        assertThat(capturedNotification.getContent()).contains("总记录数：80");
    }

    @Test
    void testSendErrorAlert() {
        MonthKey monthKey = MonthKey.of(LocalDate.of(2024, 6, 1));
        Exception error = new RuntimeException("数据库连接中断");

        notificationAdapter.sendErrorAlert(monthKey, error);

        verify(notificationService, times(2)).createNotification(any(Notification.class));
        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);
        verify(notificationService, atLeastOnce()).createNotification(captor.capture());

        Notification capturedNotification = captor.getValue();
        assertThat(capturedNotification.getTitle()).contains("佣金处理错误");
        assertThat(capturedNotification.getContent()).contains("数据库连接中断");
    }
}
```

## 6. 注意事项

*   **DDD分层**: `CommissionNotificationAdapter` 明确属于应用服务层，作为适配器，它将佣金领域的事件转换为通用通知服务可理解的格式，实现了领域层与基础设施层（通知服务）的解耦。
*   **异步发送**: `@Async` 注解确保了通知发送不会阻塞佣金处理的主流程，提高了系统的响应性和吞吐量。需要确保Spring的异步任务执行器 (`taskExecutor`) 已正确配置。
*   **通知内容构建**: 内部的 `build...NotificationContent` 方法负责动态构建通知的详细内容，这使得通知内容可以根据处理结果进行个性化定制。
*   **通知开关**: `@Value("${commission.notification.enabled:true}")` 提供了一个灵活的配置开关，可以在不修改代码的情况下启用或禁用通知功能。
*   **错误容忍**: 通知发送失败不应影响佣金处理的主流程。因此，通知发送方法内部应有健壮的异常处理，只记录日志而不重新抛出异常。
*   **依赖注入**: 服务通过构造函数注入其依赖，遵循了依赖倒置原则。
*   **日志记录**: 服务中使用了 `log.info`, `log.warn`, `log.error` 记录关键操作和异常信息，这对于监控和问题排查非常重要。
*   **通知类型**: 通知类型（`COMMISSION_PROCESSING_SUCCESS`, `COMMISSION_PROCESSING_ALERT` 等）应在 `NotificationType` 枚举中定义，以确保类型安全和一致性。