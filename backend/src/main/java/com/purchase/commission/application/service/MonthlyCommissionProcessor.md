# MonthlyCommissionProcessor.java

## 文件概述 (File Overview)
`MonthlyCommissionProcessor.java` 是月度佣金处理器，位于 `com.purchase.commission.application.service` 包中。该组件负责在每月初统一处理上个月的佣金记录，通过定时任务自动执行月度佣金的批量处理工作。它协调佣金批处理服务、处理日志记录、通知适配器等多个组件，确保佣金处理的自动化、可靠性和可追溯性。该处理器采用防重复处理机制，支持手动触发和状态查询功能，是佣金系统自动化运营的核心组件。

## 核心功能 (Core Functionality)
*   **自动化处理**: 通过定时任务自动执行月度佣金处理，无需人工干预
*   **防重复处理**: 内置防重复处理机制，确保同一月份不会被重复处理
*   **批量处理**: 集成佣金批处理服务，高效处理大量佣金记录
*   **处理日志**: 完整的处理日志记录，包括处理状态、结果和异常信息
*   **手动触发**: 支持管理员手动触发特定月份的佣金处理
*   **状态查询**: 提供处理状态查询和历史日志查询功能
*   **异常处理**: 完善的异常处理和恢复机制
*   **通知机制**: 集成通知适配器，及时通知处理结果

## 接口说明 (Interface Description)

### 核心处理方法

#### processMonthlyCommissions - 月度佣金处理定时任务
*   **方法签名**: `void processMonthlyCommissions()`
*   **定时配置**: `@Scheduled(cron = "0 0 2 1-3 * ?")` - 每月1-3号凌晨2点执行
*   **参数**: 无（自动获取上个月的数据）
*   **业务逻辑**: 
    *   自动获取上个月的月份键值
    *   执行防重复处理检查
    *   调用批处理服务处理佣金记录
    *   记录处理日志和结果
    *   发送处理完成通知
    *   处理异常情况并发送告警

#### manualProcessMonth - 手动处理指定月份
*   **方法签名**: `void manualProcessMonth(MonthKey monthKey)`
*   **参数**: `monthKey` (MonthKey) - 要处理的月份键值
*   **业务逻辑**: 
    *   提供手动触发处理的接口
    *   执行防重复处理检查
    *   记录手动处理的操作日志
    *   调用核心处理逻辑
    *   处理手动操作的异常情况

### 状态查询方法

#### getProcessingStatus - 获取处理状态
*   **方法签名**: `CommissionProcessingLog getProcessingStatus(MonthKey monthKey)`
*   **参数**: `monthKey` (MonthKey) - 查询的月份键值
*   **返回值**: `CommissionProcessingLog` - 处理日志对象，如果未处理则返回null
*   **业务逻辑**: 
    *   查询指定月份的处理日志
    *   返回处理状态、开始时间、结束时间等信息
    *   用于监控和状态展示

#### getRecentProcessingLogs - 获取最近的处理日志
*   **方法签名**: `List<CommissionProcessingLog> getRecentProcessingLogs(int limit)`
*   **参数**: `limit` (int) - 返回记录的数量限制
*   **返回值**: `List<CommissionProcessingLog>` - 最近的处理日志列表
*   **业务逻辑**: 
    *   按时间倒序查询最近的处理日志
    *   用于历史记录查看和趋势分析
    *   支持管理后台的日志展示

### 辅助方法

#### isAlreadyProcessed - 检查是否已处理
*   **方法签名**: `boolean isAlreadyProcessed(MonthKey monthKey)`
*   **参数**: `monthKey` (MonthKey) - 要检查的月份键值
*   **返回值**: `boolean` - 是否已经处理过
*   **业务逻辑**: 
    *   查询处理日志表检查是否存在成功的处理记录
    *   防止重复处理同一月份的数据
    *   确保数据处理的幂等性

#### executeProcessing - 执行核心处理逻辑
*   **方法签名**: `void executeProcessing(MonthKey monthKey)`
*   **参数**: `monthKey` (MonthKey) - 要处理的月份键值
*   **业务逻辑**: 
    *   创建处理日志记录
    *   调用批处理服务执行实际处理
    *   更新处理状态和结果
    *   处理异常情况和回滚

## 使用示例 (Usage Examples)

```java
// 1. 控制器层调用示例
@RestController
@RequestMapping("/api/v1/admin/commission/processing")
public class CommissionProcessingController {
    
    @Autowired
    private MonthlyCommissionProcessor processor;
    
    // 手动触发月度处理
    @PostMapping("/manual/{year}/{month}")
    @PreAuthorize("hasAuthority('admin')")
    public Result<String> manualProcess(
            @PathVariable int year,
            @PathVariable int month) {
        
        MonthKey monthKey = MonthKey.of(year, month);
        
        try {
            processor.manualProcessMonth(monthKey);
            return Result.success("月度佣金处理已启动: " + monthKey);
        } catch (Exception e) {
            log.error("手动触发月度处理失败: {}", monthKey, e);
            return Result.error("处理失败: " + e.getMessage());
        }
    }
    
    // 获取处理状态
    @GetMapping("/status/{year}/{month}")
    @PreAuthorize("hasAuthority('admin')")
    public Result<CommissionProcessingLog> getProcessingStatus(
            @PathVariable int year,
            @PathVariable int month) {
        
        MonthKey monthKey = MonthKey.of(year, month);
        CommissionProcessingLog log = processor.getProcessingStatus(monthKey);
        
        if (log == null) {
            return Result.success("未找到处理记录", null);
        }
        
        return Result.success(log);
    }
    
    // 获取处理历史
    @GetMapping("/history")
    @PreAuthorize("hasAuthority('admin')")
    public Result<List<CommissionProcessingLog>> getProcessingHistory(
            @RequestParam(defaultValue = "12") int limit) {
        
        List<CommissionProcessingLog> logs = processor.getRecentProcessingLogs(limit);
        return Result.success(logs);
    }
    
    // 获取处理统计
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('admin')")
    public Result<ProcessingStatistics> getProcessingStatistics() {
        List<CommissionProcessingLog> recentLogs = processor.getRecentProcessingLogs(12);
        
        ProcessingStatistics statistics = new ProcessingStatistics();
        statistics.setTotalProcessed(recentLogs.size());
        statistics.setSuccessCount((int) recentLogs.stream()
            .filter(log -> "SUCCESS".equals(log.getStatus())).count());
        statistics.setFailureCount((int) recentLogs.stream()
            .filter(log -> "FAILED".equals(log.getStatus())).count());
        statistics.setAverageProcessingTime(calculateAverageProcessingTime(recentLogs));
        
        return Result.success(statistics);
    }
}

// 2. 前端JavaScript调用示例
const CommissionProcessingAPI = {
    // 手动触发处理
    async manualProcess(year, month) {
        const response = await fetch(`/api/v1/admin/commission/processing/manual/${year}/${month}`, {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + adminToken,
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },
    
    // 获取处理状态
    async getProcessingStatus(year, month) {
        const response = await fetch(`/api/v1/admin/commission/processing/status/${year}/${month}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + adminToken
            }
        });
        
        return await response.json();
    },
    
    // 获取处理历史
    async getProcessingHistory(limit = 12) {
        const response = await fetch(`/api/v1/admin/commission/processing/history?limit=${limit}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + adminToken
            }
        });
        
        return await response.json();
    },
    
    // 渲染处理历史
    renderProcessingHistory(logs) {
        const container = document.getElementById('processing-history');
        container.innerHTML = '';
        
        logs.forEach(log => {
            const logDiv = document.createElement('div');
            logDiv.className = `processing-log ${log.status.toLowerCase()}`;
            
            const duration = log.endTime ? 
                new Date(log.endTime) - new Date(log.startTime) : 
                '处理中...';
            
            logDiv.innerHTML = `
                <div class="log-header">
                    <h4>${log.monthKey} - ${getStatusText(log.status)}</h4>
                    <span class="duration">${formatDuration(duration)}</span>
                </div>
                <div class="log-details">
                    <p>开始时间: ${new Date(log.startTime).toLocaleString()}</p>
                    ${log.endTime ? `<p>结束时间: ${new Date(log.endTime).toLocaleString()}</p>` : ''}
                    ${log.processedCount ? `<p>处理数量: ${log.processedCount}</p>` : ''}
                    ${log.errorMessage ? `<p class="error">错误信息: ${log.errorMessage}</p>` : ''}
                </div>
            `;
            
            container.appendChild(logDiv);
        });
    }
};

// 3. 监控和告警集成示例
@Component
public class CommissionProcessingMonitor {
    
    @Autowired
    private MonthlyCommissionProcessor processor;
    
    @Autowired
    private AlertService alertService;
    
    // 监控处理状态
    @Scheduled(cron = "0 */30 * * * ?") // 每30分钟检查一次
    public void monitorProcessingStatus() {
        MonthKey currentMonth = MonthKey.of(LocalDate.now());
        MonthKey lastMonth = MonthKey.of(LocalDate.now().minusMonths(1));
        
        // 检查上月是否已处理
        CommissionProcessingLog lastMonthLog = processor.getProcessingStatus(lastMonth);
        if (lastMonthLog == null && LocalDate.now().getDayOfMonth() > 5) {
            alertService.sendAlert(
                "月度佣金处理告警",
                String.format("上月(%s)佣金尚未处理，请检查定时任务状态", lastMonth)
            );
        }
        
        // 检查处理失败的情况
        if (lastMonthLog != null && "FAILED".equals(lastMonthLog.getStatus())) {
            alertService.sendAlert(
                "月度佣金处理失败",
                String.format("月份%s的佣金处理失败: %s", lastMonth, lastMonthLog.getErrorMessage())
            );
        }
        
        // 检查处理时间过长
        if (lastMonthLog != null && "PROCESSING".equals(lastMonthLog.getStatus())) {
            long processingTime = System.currentTimeMillis() - lastMonthLog.getStartTime().getTime();
            if (processingTime > 2 * 60 * 60 * 1000) { // 超过2小时
                alertService.sendAlert(
                    "月度佣金处理超时",
                    String.format("月份%s的佣金处理已超过2小时，可能存在异常", lastMonth)
                );
            }
        }
    }
    
    // 生成处理报告
    @Scheduled(cron = "0 0 10 2 * ?") // 每月2号上午10点
    public void generateProcessingReport() {
        List<CommissionProcessingLog> recentLogs = processor.getRecentProcessingLogs(12);
        
        ProcessingReport report = new ProcessingReport();
        report.setReportPeriod("最近12个月");
        report.setTotalProcessed(recentLogs.size());
        report.setSuccessRate(calculateSuccessRate(recentLogs));
        report.setAverageProcessingTime(calculateAverageProcessingTime(recentLogs));
        report.setProcessingTrend(calculateProcessingTrend(recentLogs));
        
        // 发送报告
        notificationService.sendProcessingReport(report);
    }
}

// 4. 业务集成示例
@Service
public class CommissionManagementService {
    
    @Autowired
    private MonthlyCommissionProcessor processor;
    
    @Autowired
    private CommissionValidationService validationService;
    
    // 完整的月度处理流程
    public MonthlyProcessingResult executeMonthlyProcessing(MonthKey monthKey) {
        MonthlyProcessingResult result = new MonthlyProcessingResult();
        result.setMonthKey(monthKey);
        
        try {
            // 1. 预处理检查
            log.info("开始月度佣金处理流程: {}", monthKey);
            result.addStep("预处理检查", "开始");
            
            // 检查数据准备情况
            if (!isDataReady(monthKey)) {
                throw new IllegalStateException("数据尚未准备完成");
            }
            result.addStep("预处理检查", "完成");
            
            // 2. 执行佣金处理
            result.addStep("佣金处理", "开始");
            processor.manualProcessMonth(monthKey);
            result.addStep("佣金处理", "完成");
            
            // 3. 数据校验
            result.addStep("数据校验", "开始");
            ValidationResult validation = validationService.validateMonth(monthKey);
            if (validation.hasErrors()) {
                result.addStep("数据校验", "失败: " + validation.getErrors());
                result.setSuccess(false);
                return result;
            }
            result.addStep("数据校验", "完成");
            
            // 4. 后处理
            result.addStep("后处理", "开始");
            executePostProcessing(monthKey);
            result.addStep("后处理", "完成");
            
            result.setSuccess(true);
            log.info("月度佣金处理流程完成: {}", monthKey);
            
        } catch (Exception e) {
            log.error("月度佣金处理流程失败: {}", monthKey, e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }
}

// 5. 异常处理和恢复示例
@Service
public class CommissionProcessingRecoveryService {

    @Autowired
    private MonthlyCommissionProcessor processor;

    @Autowired
    private CommissionRecordRepository recordRepository;

    // 处理失败恢复
    public RecoveryResult recoverFailedProcessing(MonthKey monthKey) {
        log.info("开始恢复失败的月度处理: {}", monthKey);

        RecoveryResult result = new RecoveryResult();
        result.setMonthKey(monthKey);

        try {
            // 1. 检查失败状态
            CommissionProcessingLog failedLog = processor.getProcessingStatus(monthKey);
            if (failedLog == null || !"FAILED".equals(failedLog.getStatus())) {
                result.setSuccess(false);
                result.setMessage("未找到失败的处理记录");
                return result;
            }

            // 2. 分析失败原因
            String failureReason = analyzeFailureReason(failedLog);
            result.setFailureReason(failureReason);

            // 3. 清理不完整的数据
            int cleanedRecords = cleanIncompleteData(monthKey);
            result.setCleanedRecords(cleanedRecords);

            // 4. 重新处理
            processor.manualProcessMonth(monthKey);

            // 5. 验证恢复结果
            CommissionProcessingLog newLog = processor.getProcessingStatus(monthKey);
            if ("SUCCESS".equals(newLog.getStatus())) {
                result.setSuccess(true);
                result.setMessage("恢复成功");
            } else {
                result.setSuccess(false);
                result.setMessage("恢复失败: " + newLog.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("恢复失败的月度处理异常: {}", monthKey, e);
            result.setSuccess(false);
            result.setMessage("恢复过程异常: " + e.getMessage());
        }

        return result;
    }

    // 批量恢复历史失败记录
    public BatchRecoveryResult batchRecoverFailedProcessing() {
        List<CommissionProcessingLog> failedLogs = getFailedProcessingLogs();
        BatchRecoveryResult batchResult = new BatchRecoveryResult();

        for (CommissionProcessingLog failedLog : failedLogs) {
            try {
                RecoveryResult result = recoverFailedProcessing(failedLog.getMonthKey());
                batchResult.addResult(failedLog.getMonthKey(), result);
            } catch (Exception e) {
                log.error("批量恢复失败: monthKey={}", failedLog.getMonthKey(), e);
                batchResult.addFailure(failedLog.getMonthKey(), e.getMessage());
            }
        }

        return batchResult;
    }
}

// 6. 性能监控示例
@Component
public class CommissionProcessingPerformanceMonitor {

    @Autowired
    private MonthlyCommissionProcessor processor;

    @Autowired
    private MeterRegistry meterRegistry;

    // 性能指标收集
    @EventListener
    public void handleProcessingCompleted(ProcessingCompletedEvent event) {
        CommissionProcessingLog log = event.getProcessingLog();

        // 记录处理时间
        long processingTime = log.getEndTime().getTime() - log.getStartTime().getTime();
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("commission.processing.duration")
            .tag("month", log.getMonthKey().toString())
            .tag("status", log.getStatus())
            .register(meterRegistry));

        // 记录处理数量
        meterRegistry.counter("commission.processing.records",
            "month", log.getMonthKey().toString(),
            "status", log.getStatus())
            .increment(log.getProcessedCount());

        // 记录成功率
        meterRegistry.gauge("commission.processing.success.rate",
            calculateRecentSuccessRate());
    }

    // 生成性能报告
    @Scheduled(cron = "0 0 9 * * MON") // 每周一早上9点
    public void generatePerformanceReport() {
        List<CommissionProcessingLog> recentLogs = processor.getRecentProcessingLogs(12);

        PerformanceReport report = new PerformanceReport();
        report.setReportPeriod("最近12个月");
        report.setAverageProcessingTime(calculateAverageProcessingTime(recentLogs));
        report.setMaxProcessingTime(calculateMaxProcessingTime(recentLogs));
        report.setMinProcessingTime(calculateMinProcessingTime(recentLogs));
        report.setThroughput(calculateThroughput(recentLogs));
        report.setResourceUtilization(calculateResourceUtilization(recentLogs));

        // 发送性能报告
        notificationService.sendPerformanceReport(report);

        // 性能告警检查
        if (report.getAverageProcessingTime() > Duration.ofHours(1)) {
            alertService.sendAlert("佣金处理性能告警",
                "平均处理时间超过1小时，请检查系统性能");
        }
    }
}

// 7. 配置管理示例
@Configuration
@ConfigurationProperties(prefix = "commission.processing")
@Data
public class CommissionProcessingConfig {

    /**
     * 是否启用自动处理
     */
    private boolean autoProcessingEnabled = true;

    /**
     * 处理超时时间（分钟）
     */
    private int timeoutMinutes = 120;

    /**
     * 最大重试次数
     */
    private int maxRetryAttempts = 3;

    /**
     * 批处理大小
     */
    private int batchSize = 1000;

    /**
     * 是否启用处理前数据校验
     */
    private boolean preValidationEnabled = true;

    /**
     * 是否启用处理后数据校验
     */
    private boolean postValidationEnabled = true;

    /**
     * 通知接收人列表
     */
    private List<String> notificationRecipients = new ArrayList<>();
}

// 配置化的处理器
@Component
@ConditionalOnProperty(name = "commission.processing.auto-processing-enabled", havingValue = "true")
public class ConfigurableMonthlyCommissionProcessor {

    @Autowired
    private MonthlyCommissionProcessor processor;

    @Autowired
    private CommissionProcessingConfig config;

    @Scheduled(cron = "${commission.processing.cron:0 0 2 1-3 * ?}")
    public void processWithConfig() {
        if (!config.isAutoProcessingEnabled()) {
            log.info("自动处理已禁用，跳过月度佣金处理");
            return;
        }

        // 使用配置参数执行处理
        executeWithTimeout(config.getTimeoutMinutes());
    }

    private void executeWithTimeout(int timeoutMinutes) {
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            processor.processMonthlyCommissions();
        });

        try {
            future.get(timeoutMinutes, TimeUnit.MINUTES);
        } catch (TimeoutException e) {
            log.error("月度佣金处理超时，超时时间: {}分钟", timeoutMinutes);
            future.cancel(true);
            // 发送超时告警
            sendTimeoutAlert(timeoutMinutes);
        } catch (Exception e) {
            log.error("月度佣金处理异常", e);
        }
    }
}
```

## 注意事项 (Notes)
*   **定时任务配置**: 定时任务设置在每月1-3号执行，提供容错时间窗口，避免单点故障
*   **防重复处理**: 通过处理日志表实现防重复处理，确保数据处理的幂等性
*   **异常处理**: 处理过程中的异常需要完整记录，便于问题排查和数据恢复
*   **事务控制**: 处理过程涉及多个数据操作，需要适当的事务边界控制
*   **性能考虑**: 月度处理可能涉及大量数据，需要考虑批处理和性能优化
*   **监控告警**: 处理失败、超时等异常情况需要及时告警通知相关人员
*   **日志记录**: 详细的处理日志便于问题排查，但要注意日志量的控制
*   **数据一致性**: 处理过程中需要确保数据的一致性，避免部分成功的情况
*   **手动触发**: 手动触发功能需要严格的权限控制，只允许管理员操作
*   **状态管理**: 处理状态的变更需要原子性，避免状态不一致问题
*   **依赖服务**: 依赖批处理服务和通知适配器，需要确保这些服务的可用性
*   **时区处理**: 月份计算需要考虑时区问题，确保在不同时区的正确性
*   **资源管理**: 处理过程可能消耗大量系统资源，需要合理的资源管理
*   **备份恢复**: 处理前建议备份关键数据，便于异常情况下的数据恢复
*   **配置管理**: 处理参数应该可配置，便于不同环境的调整和优化
*   **版本兼容**: 处理逻辑变更时需要考虑历史数据的兼容性问题
