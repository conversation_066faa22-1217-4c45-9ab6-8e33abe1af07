package com.purchase.commission.application.service;

import com.purchase.commission.domain.entity.CommissionConfig;
import com.purchase.commission.domain.entity.CommissionRecord;
import com.purchase.commission.domain.entity.InviterCoupon;
import com.purchase.commission.domain.entity.MonthlyCommissionSummary;
import com.purchase.commission.domain.repository.CommissionConfigRepository;
import com.purchase.commission.domain.repository.CommissionRecordRepository;
import com.purchase.commission.domain.repository.InviterCouponRepository;
import com.purchase.commission.domain.repository.MonthlyCommissionSummaryRepository;
import com.purchase.commission.domain.service.CommissionCalculationService;
import com.purchase.commission.domain.service.InviterCouponService;
import com.purchase.commission.domain.valueobject.CommissionRate;
import com.purchase.commission.domain.valueobject.Money;
import com.purchase.commission.domain.valueobject.MonthKey;
import com.purchase.user.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 佣金应用服务
 * 协调领域服务和仓储，处理佣金相关的业务用例
 */
@Slf4j
@Service
@Transactional
public class CommissionApplicationService {
    
    private final CommissionConfigRepository commissionConfigRepository;
    private final CommissionRecordRepository commissionRecordRepository;
    private final MonthlyCommissionSummaryRepository monthlySummaryRepository;
    private final InviterCouponRepository inviterCouponRepository;
    private final CommissionCalculationService calculationService;
    private final InviterCouponService inviterCouponService;
    private final UserMapper userMapper;

    public CommissionApplicationService(CommissionConfigRepository commissionConfigRepository,
                                      CommissionRecordRepository commissionRecordRepository,
                                      MonthlyCommissionSummaryRepository monthlySummaryRepository,
                                      InviterCouponRepository inviterCouponRepository,
                                      CommissionCalculationService calculationService,
                                      InviterCouponService inviterCouponService,
                                      UserMapper userMapper) {
        this.commissionConfigRepository = commissionConfigRepository;
        this.commissionRecordRepository = commissionRecordRepository;
        this.monthlySummaryRepository = monthlySummaryRepository;
        this.inviterCouponRepository = inviterCouponRepository;
        this.calculationService = calculationService;
        this.inviterCouponService = inviterCouponService;
        this.userMapper = userMapper;
    }
    
    /**
     * 处理订单完成事件，创建佣金记录
     * 
     * @param orderId 订单ID
     * @param buyerId 买家ID（实际是订单创建者ID，可能是买家或卖家）
     * @param orderAmount 订单金额
     * @param orderType 订单类型
     * @param completedAt 完成时间
     */
    public void handleOrderCompleted(Long orderId, Long buyerId, Money orderAmount, 
                                   String orderType, LocalDate completedAt) {
        log.info("处理订单完成事件: orderId={}, buyerId={}, orderAmount={}, orderType={}", 
                orderId, buyerId, orderAmount, orderType);
        
        // 1. 验证订单是否符合佣金条件
        if (!calculationService.isValidCommissionOrder(orderType, "completed", true)) {
            log.info("订单不符合佣金条件，跳过处理: orderId={}", orderId);
            return;
        }
        
        // 2. 查找创建者的邀请关系（需要通过用户服务获取）
        Long inviterId = getInviterByUserId(buyerId);
        if (inviterId == null) {
            log.info("创建者不是被邀请用户，无需处理佣金: orderId={}, creatorId={}", orderId, buyerId);
            return; // 创建者不是被邀请用户
        }
        
        // 3. 检查邀请人的角色
        String inviterRole = userMapper.selectRoleById(inviterId);
        if ("buyer".equals(inviterRole)) {
            log.info("邀请人是买家，暂不产生优惠券（功能未开放）: orderId={}, inviterId={}, inviterRole={}",
                    orderId, inviterId, inviterRole);
            // TODO: 为邀请人产生优惠券而非佣金（功能暂时未开放，以后会启用）
            // generateCouponForBuyerInviter(orderId, buyerId, inviterId, orderAmount, orderType, completedAt);
            return;
        }
        
        log.info("邀请人角色为: {}, 正常处理佣金逻辑", inviterRole);
        
        // 4. 检查是否已存在佣金记录
        if (commissionRecordRepository.existsByOrderIdAndInviterId(orderId, inviterId)) {
            log.warn("佣金记录已存在，跳过处理: orderId={}, inviterId={}", orderId, inviterId);
            return; // 避免重复创建
        }
        
        // 5. 获取月份键
        MonthKey monthKey = MonthKey.of(completedAt);
        
        // 6. 计算当月已有的订单总金额
        Money currentMonthTotal = calculateMonthlyTotalAmount(inviterId, monthKey);
        
        // 7. 计算新的月度总金额
        Money newMonthlyTotal = currentMonthTotal.add(orderAmount);
        
        // 8. 计算佣金比例
        CommissionRate commissionRate = calculationService.calculateCommissionRate(newMonthlyTotal);
        
        // 9. 创建佣金记录
        CommissionRecord.OrderType domainOrderType = convertOrderType(orderType);
        CommissionRecord commissionRecord = CommissionRecord.create(
            inviterId, buyerId, orderId, domainOrderType, orderAmount, commissionRate, monthKey
        );
        
        // 10. 保存佣金记录
        commissionRecordRepository.save(commissionRecord);
        log.info("佣金记录创建成功: orderId={}, inviterId={}, commissionAmount={}", 
                orderId, inviterId, commissionRecord.getCommissionAmount());
        
        // 11. 更新月度汇总
        updateMonthlySummary(inviterId, monthKey);
    }
    
    /**
     * 为买家邀请人产生优惠券
     * 当邀请人是买家时，不产生佣金而是产生优惠券
     *
     * 注意：此功能暂时未开放，保留代码以备将来启用
     *
     * @param orderId 订单ID
     * @param creatorId 订单创建者ID
     * @param inviterId 邀请人ID（买家）
     * @param orderAmount 订单金额
     * @param orderType 订单类型
     * @param completedAt 完成时间
     */
    @SuppressWarnings("unused") // 保留方法以备将来使用
    private void generateCouponForBuyerInviter(Long orderId, Long creatorId, Long inviterId,
                                             Money orderAmount, String orderType, LocalDate completedAt) {
        log.info("为买家邀请人产生优惠券 - orderId: {}, creatorId: {}, buyerInviterId: {}, amount: {}",
                orderId, creatorId, inviterId, orderAmount);

        try {
            // 1. 检查是否已经为该订单生成过优惠券
            if (inviterCouponRepository.findByTriggerOrderId(orderId).isPresent()) {
                log.warn("订单已生成过优惠券，跳过处理: orderId={}", orderId);
                return;
            }

            // 2. 获取月份键
            MonthKey monthKey = MonthKey.of(completedAt);

            // 3. 生成优惠券
            InviterCoupon coupon = inviterCouponService.generateCouponForBuyerInviter(
                inviterId, creatorId, orderId, orderAmount, orderType, monthKey
            );

            // 4. 保存优惠券
            InviterCoupon savedCoupon = inviterCouponRepository.save(coupon);

            log.info("买家邀请人优惠券生成成功: orderId={}, inviterId={}, couponCode={}, couponValue={}",
                    orderId, inviterId, savedCoupon.getCouponCode(), savedCoupon.getDiscountValue());

            // 5. 发送通知给买家邀请人
            // TODO: 实现通知功能
            // notificationService.sendCouponGeneratedNotification(inviterId, savedCoupon);

        } catch (Exception e) {
            log.error("为买家邀请人生成优惠券失败: orderId={}, inviterId={}, error={}",
                    orderId, inviterId, e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }
    
    /**
     * 更新月度佣金汇总
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     */
    public void updateMonthlySummary(Long inviterId, MonthKey monthKey) {
        // 1. 获取该月所有有效的佣金记录
        List<CommissionRecord> records = commissionRecordRepository
            .findByInviterIdAndMonthKey(inviterId, monthKey);
        
        // 2. 过滤有效记录
        List<CommissionRecord> validRecords = records.stream()
            .filter(CommissionRecord::isValid)
            .toList();
        
        if (validRecords.isEmpty()) {
            return;
        }
        
        // 3. 计算汇总数据
        Money totalOrderAmount = validRecords.stream()
            .map(CommissionRecord::getOrderAmount)
            .reduce(Money.zero(), Money::add);
        
        Money totalCommission = validRecords.stream()
            .map(CommissionRecord::getCommissionAmount)
            .reduce(Money.zero(), Money::add);
        
        // 4. 重新计算佣金比例和月度奖金
        CommissionRate commissionRate = calculationService.calculateCommissionRate(totalOrderAmount);
        Money monthlyBonus = calculationService.calculateMonthlyBonus(totalOrderAmount);
        
        // 5. 获取或创建月度汇总
        Optional<MonthlyCommissionSummary> summaryOpt = monthlySummaryRepository
            .findByInviterIdAndMonthKey(inviterId, monthKey);
        
        MonthlyCommissionSummary summary;
        if (summaryOpt.isPresent()) {
            summary = summaryOpt.get();
        } else {
            summary = MonthlyCommissionSummary.create(inviterId, monthKey);
        }
        
        // 6. 更新汇总数据
        summary.updateSummary(totalOrderAmount, commissionRate, totalCommission, 
                            monthlyBonus, validRecords.size());
        
        // 7. 保存汇总
        monthlySummaryRepository.save(summary);
    }

    /**
     * 取消佣金记录
     * 
     * @param commissionRecordId 佣金记录ID
     */
    public void cancelCommission(Long commissionRecordId) {
        CommissionRecord record = commissionRecordRepository.findById(commissionRecordId)
            .orElseThrow(() -> new IllegalArgumentException("佣金记录不存在"));
        
        record.cancel();
        commissionRecordRepository.save(record);
        
        // 更新月度汇总
        updateMonthlySummary(record.getInviterId(), record.getMonthKey());
    }
    
    /**
     * 标记佣金为已支付
     * 
     * @param commissionRecordIds 佣金记录ID列表
     */
    public void markCommissionsAsPaid(List<Long> commissionRecordIds) {
        for (Long id : commissionRecordIds) {
            CommissionRecord record = commissionRecordRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("佣金记录不存在: " + id));
            
            record.markAsPaid();
            commissionRecordRepository.save(record);
        }
    }
    
    /**
     * 开始月度结算
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     */
    public void startMonthlySettlement(Long inviterId, MonthKey monthKey) {
        MonthlyCommissionSummary summary = monthlySummaryRepository
            .findByInviterIdAndMonthKey(inviterId, monthKey)
            .orElseThrow(() -> new IllegalArgumentException("未找到月度汇总记录"));
        
        summary.startSettlement();
        monthlySummaryRepository.save(summary);
    }
    
    /**
     * 完成月度结算
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @param settlementDate 结算日期
     */
    public void completeMonthlySettlement(Long inviterId, MonthKey monthKey, 
                                        LocalDateTime settlementDate) {
        MonthlyCommissionSummary summary = monthlySummaryRepository
            .findByInviterIdAndMonthKey(inviterId, monthKey)
            .orElseThrow(() -> new IllegalArgumentException("未找到月度汇总记录"));
        
        summary.completeSettlement(settlementDate);
        monthlySummaryRepository.save(summary);
        
        // 标记相关佣金记录为已支付
        List<CommissionRecord> settlableRecords = commissionRecordRepository
            .findSettlableRecords(inviterId, monthKey);
        
        List<Long> recordIds = settlableRecords.stream()
            .map(CommissionRecord::getId)
            .toList();
        
        if (!recordIds.isEmpty()) {
            markCommissionsAsPaid(recordIds);
        }
    }
    
    /**
     * 获取邀请者的月度佣金汇总
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 月度佣金汇总
     */
    @Transactional(readOnly = true)
    public Optional<MonthlyCommissionSummary> getMonthlySummary(Long inviterId, MonthKey monthKey) {
        return monthlySummaryRepository.findByInviterIdAndMonthKey(inviterId, monthKey);
    }
    
    /**
     * 获取邀请者的佣金记录列表
     *
     * @param inviterId 邀请者ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 佣金记录列表
     */
    @Transactional(readOnly = true)
    public List<CommissionRecord> getCommissionRecords(Long inviterId, Integer limit, Integer offset) {
        return commissionRecordRepository.findByInviterId(inviterId, limit, offset);
    }

    /**
     * 获取佣金记录总数
     *
     * @param inviterId 邀请者ID
     * @return 总记录数
     */
    @Transactional(readOnly = true)
    public Integer getCommissionRecordsCount(Long inviterId) {
        return commissionRecordRepository.countByInviterId(inviterId);
    }
    
    /**
     * 获取指定月份的佣金记录
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 佣金记录列表
     */
    @Transactional(readOnly = true)
    public List<CommissionRecord> getMonthlyCommissionRecords(Long inviterId, MonthKey monthKey) {
        return commissionRecordRepository.findByInviterIdAndMonthKey(inviterId, monthKey);
    }
    
    /**
     * 计算指定月份的订单总金额
     * 
     * @param inviterId 邀请者ID
     * @param monthKey 月份键
     * @return 订单总金额
     */
    private Money calculateMonthlyTotalAmount(Long inviterId, MonthKey monthKey) {
        List<CommissionRecord> records = commissionRecordRepository
            .findByInviterIdAndMonthKey(inviterId, monthKey);
        
        return records.stream()
            .filter(CommissionRecord::isValid)
            .map(CommissionRecord::getOrderAmount)
            .reduce(Money.zero(), Money::add);
    }
    
    /**
     * 转换订单类型
     * 
     * @param orderType 字符串类型
     * @return 领域对象类型
     */
    private CommissionRecord.OrderType convertOrderType(String orderType) {
        return switch (orderType.toLowerCase()) {
            case "purchase" -> CommissionRecord.OrderType.PURCHASE;
            case "logistics" -> CommissionRecord.OrderType.LOGISTICS;
            default -> throw new IllegalArgumentException("不支持的订单类型: " + orderType);
        };
    }
    
    /**
     * 根据用户ID获取邀请者ID
     * 通过UserMapper查询用户的邀请关系
     *
     * @param userId 用户ID
     * @return 邀请者ID，如果用户不存在或没有邀请者则返回null
     */
    private Long getInviterByUserId(Long userId) {
        try {
            var user = userMapper.selectById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                return null;
            }
            return user.getInviterId();
        } catch (Exception e) {
            log.error("查询用户邀请关系失败: userId={}", userId, e);
            return null;
        }
    }

    /**
     * 获取佣金阶梯配置
     * @return 佣金阶梯配置列表
     */
    public List<Map<String, Object>> getCommissionTiers() {
        List<CommissionConfig> configs = commissionConfigRepository.findEnabledCommissionRates();

        return configs.stream()
                .map(this::convertCommissionTierToMap)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取月度奖金配置
     * @return 月度奖金配置列表
     */
    public List<Map<String, Object>> getMonthlyBonusConfig() {
        List<CommissionConfig> configs = commissionConfigRepository.findEnabledMonthlyBonuses();

        return configs.stream()
                .map(this::convertMonthlyBonusToMap)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 将佣金阶梯配置转换为Map
     */
    private Map<String, Object> convertCommissionTierToMap(CommissionConfig config) {
        Map<String, Object> tier = new HashMap<>();
        tier.put("minAmount", config.getMinAmount() != null ? config.getMinAmount().getAmount() : null);
        tier.put("maxAmount", config.getMaxAmount() != null ? config.getMaxAmount().getAmount() : null);
        tier.put("rate", config.getRateValue() != null ? config.getRateValue().getPercentage() : null);
        tier.put("description", config.getDescription());
        tier.put("sortOrder", config.getSortOrder());
        return tier;
    }

    /**
     * 将月度奖金配置转换为Map
     */
    private Map<String, Object> convertMonthlyBonusToMap(CommissionConfig config) {
        Map<String, Object> bonus = new HashMap<>();
        bonus.put("minAmount", config.getMinAmount() != null ? config.getMinAmount().getAmount() : null);
        bonus.put("maxAmount", config.getMaxAmount() != null ? config.getMaxAmount().getAmount() : null);
        bonus.put("bonusAmount", config.getBonusAmount() != null ? config.getBonusAmount().getAmount() : null);
        bonus.put("description", config.getDescription());
        bonus.put("sortOrder", config.getSortOrder());
        return bonus;
    }
}