# CommissionBatchService.md

## 1. 文件概述

`CommissionBatchService` 是佣金模块中的一个应用服务，位于 `com.purchase.commission.application.service` 包中。它专注于批量处理佣金记录和月度佣金汇总。该服务是佣金结算流程中处理大量数据的核心组件，负责将佣金记录的状态更新为“已确认”，并根据这些记录计算和更新邀请人的月度佣金汇总。它协调领域服务 (`CommissionCalculationService`) 和仓储 (`CommissionRecordRepository`, `MonthlyCommissionSummaryRepository`)，确保批量操作的事务一致性和数据准确性。

## 2. 核心功能

*   **批量佣金处理**: 提供了 `batchProcessCommissions` 方法，用于批量处理按邀请人分组的佣金记录。这包括更新记录状态和更新月度汇总。
*   **月度汇总计算**: 内部 `calculateMonthlySummary` 方法负责根据佣金记录列表计算当月的总订单金额、总佣金金额、佣金比例和月度奖金。
*   **批量状态更新**: 实现了将佣金记录批量更新为 `CONFIRMED` 状态的功能，提高了数据库操作效率。
*   **月度汇总管理**: 负责月度佣金汇总记录的创建或更新，确保每个邀请人每个月只有一条汇总记录。
*   **重新计算汇总**: 提供了 `recalculateMonthSummary` 方法，用于在数据出现问题时，根据已确认的佣金记录重新计算并更新月度汇总，支持数据修复。
*   **事务管理**: 所有核心操作都通过 `@Transactional` 注解确保事务一致性。
*   **日志记录**: 详细记录了批量处理过程中的关键信息和异常，便于监控和问题排查。

## 3. 接口说明

### 3.1 批量处理方法

#### batchProcessCommissions - 批量处理佣金记录
*   **方法签名**: `ProcessingResult batchProcessCommissions(Map<Long, List<CommissionRecord>> groupedRecords, MonthKey monthKey)`
*   **描述**: 批量处理按邀请者ID分组的佣金记录。对于每个邀请者，它会计算月度汇总数据，批量更新佣金记录状态，并保存或更新月度汇总。
*   **参数**:
    *   `groupedRecords` (Map<Long, List<CommissionRecord>>): 按邀请者ID分组的佣金记录Map。
    *   `monthKey` (MonthKey): 当前处理的月份键。
*   **返回值**: `ProcessingResult` - 包含处理成功/失败数量和详细信息的处理结果对象。
*   **业务逻辑**: 
    1.  初始化 `ProcessingResult`。
    2.  遍历 `groupedRecords`，对每个邀请者的佣金记录进行处理。
    3.  调用 `calculateMonthlySummary` 计算月度汇总数据。
    4.  调用 `batchUpdateRecordsToConfirmed` 批量更新佣金记录状态为 `CONFIRMED`。
    5.  调用 `saveOrUpdateMonthlySummary` 保存或更新月度佣金汇总。
    6.  记录处理结果到 `ProcessingResult`。
    7.  捕获并记录处理过程中的异常。

#### recalculateMonthSummary - 重新计算指定月份的汇总
*   **方法签名**: `void recalculateMonthSummary(Long inviterId, MonthKey monthKey)`
*   **描述**: 重新计算指定邀请者在指定月份的月度佣金汇总，通常用于数据修复或调整。
*   **参数**:
    *   `inviterId` (Long): 邀请者ID。
    *   `monthKey` (MonthKey): 月份键。
*   **业务逻辑**: 
    1.  查询该月所有已确认的佣金记录。
    2.  如果未找到有效记录，则直接返回。
    3.  调用 `calculateMonthlySummary` 重新计算汇总数据。
    4.  调用 `saveOrUpdateMonthlySummary` 保存或更新月度汇总。

### 3.2 辅助方法

#### calculateMonthlySummary - 计算月度汇总数据
*   **方法签名**: `private MonthlySummaryData calculateMonthlySummary(List<CommissionRecord> records)`
*   **描述**: 根据佣金记录列表计算总订单金额、总佣金金额、佣金比例和月度奖金。

#### batchUpdateRecordsToConfirmed - 批量更新记录状态为CONFIRMED
*   **方法签名**: `private int batchUpdateRecordsToConfirmed(List<Long> recordIds)`
*   **描述**: 将指定ID列表的佣金记录状态批量更新为 `CONFIRMED`，并设置确认时间。采用分批更新以避免SQL语句过长。

#### saveOrUpdateMonthlySummary - 保存或更新月度汇总
*   **方法签名**: `private void saveOrUpdateMonthlySummary(Long inviterId, MonthKey monthKey, MonthlySummaryData data)`
*   **描述**: 查找现有月度汇总记录，如果存在则更新，否则创建新记录，并保存到数据库。

## 4. 业务规则

*   **事务一致性**: `batchProcessCommissions` 和 `recalculateMonthSummary` 方法都使用 `@Transactional` 注解，确保了批量操作的原子性。即使在处理过程中发生异常，所有数据库操作也会回滚。
*   **数据准确性**: `calculateMonthlySummary` 方法确保了月度汇总数据的准确性，它会重新计算所有有效佣金记录的聚合值。
*   **幂等性**: 批量更新操作应设计为幂等，即使重复执行也不会导致数据错误。
*   **分批处理**: `batchUpdateRecordsToConfirmed` 方法通过分批处理，避免了单次SQL操作数据量过大导致的问题，提高了性能和稳定性。
*   **日志记录**: 详细的日志记录对于批量任务的监控和问题排查至关重要。

## 5. 使用示例

```java
// 1. 在 MonthlyCommissionProcessor 中调用 CommissionBatchService
@Service
@RequiredArgsConstructor
@Slf4j
public class MonthlyCommissionProcessor {
    private final CommissionBatchService commissionBatchService;
    private final CommissionRecordRepository commissionRecordRepository;
    private final CommissionProcessingLogRepository commissionProcessingLogRepository;

    @Transactional
    public void manualProcessMonth(MonthKey monthKey) {
        CommissionProcessingLog logEntry = CommissionProcessingLog.create(monthKey);
        commissionProcessingLogRepository.save(logEntry);

        try {
            // 假设这里获取了需要处理的佣金记录
            List<CommissionRecord> recordsToProcess = commissionRecordRepository.findByMonthKeyAndStatus(
                monthKey, CommissionRecord.Status.PENDING
            );
            Map<Long, List<CommissionRecord>> groupedRecords = recordsToProcess.stream()
                .collect(Collectors.groupingBy(CommissionRecord::getInviterId));

            ProcessingResult result = commissionBatchService.batchProcessCommissions(groupedRecords, monthKey);

            logEntry.updateResult(result.getTotalProcessed(), result.getSuccessCount(), result.getFailureCount());
            logEntry.setErrorMessage(result.getErrorMessage());
            logEntry.markAsSuccess(); // 根据实际结果设置成功或失败

        } catch (Exception e) {
            logEntry.markAsFailed("月度处理失败: " + e.getMessage());
            throw new BusinessException("月度处理失败", e);
        } finally {
            commissionProcessingLogRepository.save(logEntry);
        }
    }
}

// 2. 测试示例
@SpringBootTest
class CommissionBatchServiceTest {
    @Autowired
    private CommissionBatchService commissionBatchService;

    @MockBean
    private CommissionRecordRepository commissionRecordRepository;
    @MockBean
    private MonthlyCommissionSummaryRepository monthlySummaryRepository;
    @MockBean
    private CommissionCalculationService calculationService;

    @Test
    @Transactional
    void testBatchProcessCommissions_Success() {
        MonthKey monthKey = MonthKey.of(LocalDate.of(2024, 7, 1));
        Long inviterId1 = 1L;
        Long inviterId2 = 2L;

        // 模拟佣金记录
        CommissionRecord record1 = CommissionRecord.create(inviterId1, 10L, 100L, CommissionRecord.OrderType.PURCHASE, Money.of("100"), new CommissionRate(0.01), monthKey);
        record1.setId(101L);
        CommissionRecord record2 = CommissionRecord.create(inviterId1, 11L, 101L, CommissionRecord.OrderType.LOGISTICS, Money.of("200"), new CommissionRate(0.005), monthKey);
        record2.setId(102L);
        CommissionRecord record3 = CommissionRecord.create(inviterId2, 12L, 102L, CommissionRecord.OrderType.PURCHASE, Money.of("300"), new CommissionRate(0.01), monthKey);
        record3.setId(103L);

        Map<Long, List<CommissionRecord>> groupedRecords = new HashMap<>();
        groupedRecords.put(inviterId1, List.of(record1, record2));
        groupedRecords.put(inviterId2, List.of(record3));

        // 模拟仓储和领域服务行为
        when(commissionRecordRepository.batchUpdateStatusToConfirmed(anyList(), any(LocalDateTime.class))).thenReturn(1);
        when(monthlySummaryRepository.findByInviterIdAndMonthKey(anyLong(), any(MonthKey.class))).thenReturn(Optional.empty());
        when(monthlySummaryRepository.save(any(MonthlyCommissionSummary.class))).thenReturn(new MonthlyCommissionSummary());
        when(calculationService.calculateCommissionRate(any(Money.class))).thenReturn(new CommissionRate(0.01));
        when(calculationService.calculateMonthlyBonus(any(Money.class))).thenReturn(Money.zero());

        ProcessingResult result = commissionBatchService.batchProcessCommissions(groupedRecords, monthKey);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTotalProcessed()).isEqualTo(2); // 两个邀请者
        assertThat(result.getSuccessCount()).isEqualTo(2);
        verify(commissionRecordRepository, times(2)).batchUpdateStatusToConfirmed(anyList(), any(LocalDateTime.class));
        verify(monthlySummaryRepository, times(2)).save(any(MonthlyCommissionSummary.class));
    }
}
```

## 6. 注意事项

*   **DDD分层**: `CommissionBatchService` 明确属于应用服务层，其职责是协调领域服务和仓储，实现业务用例。它不包含核心领域逻辑，而是委托给领域实体和领域服务。
*   **事务管理**: 所有涉及数据修改的方法都使用 `@Transactional` 注解，确保操作的原子性和数据一致性。特别是 `batchProcessCommissions`，它涉及多条记录的更新和汇总的保存。
*   **性能优化**: 批量处理是该服务的核心。`batchUpdateRecordsToConfirmed` 方法中的分批处理逻辑是提高性能的关键。在实际生产环境中，需要根据数据库性能和数据量调整 `batchSize`。
*   **数据准确性**: `calculateMonthlySummary` 方法确保了月度汇总数据的准确性，它会重新计算当月所有有效佣金记录的聚合值。
*   **幂等性**: 批量更新操作应设计为幂等，即使重复执行也不会导致数据错误。
*   **日志记录**: 服务中使用了 `log.info` 和 `log.debug` 记录关键操作和异常信息，这对于监控和问题排查非常重要。
*   **依赖注入**: 服务通过构造函数注入其依赖，遵循了依赖倒置原则。
*   **错误处理**: 捕获并记录处理过程中的异常，并返回 `ProcessingResult`，使得调用方可以了解处理的详细结果，而不是简单地失败。