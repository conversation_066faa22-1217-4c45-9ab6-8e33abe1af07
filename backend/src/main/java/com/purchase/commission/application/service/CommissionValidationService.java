package com.purchase.commission.application.service;

import com.purchase.commission.domain.entity.CommissionRecord;
import com.purchase.commission.domain.entity.MonthlyCommissionSummary;
import com.purchase.commission.domain.repository.CommissionRecordRepository;
import com.purchase.commission.domain.repository.MonthlyCommissionSummaryRepository;
import com.purchase.commission.domain.valueobject.MonthKey;
import com.purchase.commission.domain.valueobject.Money;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 佣金数据校验服务
 * 负责验证月度佣金处理的数据一致性
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CommissionValidationService {
    
    private final CommissionRecordRepository commissionRecordRepository;
    private final MonthlyCommissionSummaryRepository monthlySummaryRepository;
    private final CommissionNotificationAdapter notificationAdapter;
    
    /**
     * 校验结果
     */
    @Data
    public static class ValidationResult {
        private MonthKey monthKey;
        private boolean valid;
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();
        
        public ValidationResult(MonthKey monthKey) {
            this.monthKey = monthKey;
            this.valid = true;
        }
        
        public void addError(String error) {
            this.errors.add(error);
            this.valid = false;
        }
        
        public void addWarning(String warning) {
            this.warnings.add(warning);
        }
        
        public boolean hasErrors() {
            return !errors.isEmpty();
        }
        
        public boolean hasWarnings() {
            return !warnings.isEmpty();
        }
        
        public String getSummary() {
            StringBuilder sb = new StringBuilder();
            sb.append("月份: ").append(monthKey).append(", ");
            sb.append("状态: ").append(valid ? "通过" : "失败");
            
            if (hasErrors()) {
                sb.append(", 错误: ").append(errors.size()).append("个");
            }
            if (hasWarnings()) {
                sb.append(", 警告: ").append(warnings.size()).append("个");
            }
            
            return sb.toString();
        }
    }
    
    /**
     * 月度数据校验定时任务
     * 每月2-3号凌晨2:30执行
     */
    @Scheduled(cron = "0 30 2 2-3 * ?")
    public void validateMonthlyProcessing() {
        MonthKey lastMonth = MonthKey.of(LocalDate.now().minusMonths(1));
        
        log.info("开始执行月度数据校验: {}", lastMonth);
        
        try {
            ValidationResult result = validateMonth(lastMonth);
            
            if (result.hasErrors()) {
                log.error("月度数据校验发现错误: {}", result.getSummary());
                notificationAdapter.sendValidationAlert(lastMonth,
                    "数据校验失败: " + String.join("; ", result.getErrors()));
            } else if (result.hasWarnings()) {
                log.warn("月度数据校验发现警告: {}", result.getSummary());
                notificationAdapter.sendValidationAlert(lastMonth,
                    "数据校验警告: " + String.join("; ", result.getWarnings()));
            } else {
                log.info("月度数据校验通过: {}", lastMonth);
            }

        } catch (Exception e) {
            log.error("月度数据校验执行失败: {}", lastMonth, e);
            notificationAdapter.sendValidationAlert(lastMonth, "校验执行异常: " + e.getMessage());
        }
    }
    
    /**
     * 校验指定月份的数据
     */
    public ValidationResult validateMonth(MonthKey monthKey) {
        ValidationResult result = new ValidationResult(monthKey);
        
        log.info("开始校验月份数据: {}", monthKey);
        
        // 1. 检查是否有遗漏的PENDING记录
        validatePendingRecords(monthKey, result);
        
        // 2. 验证月度汇总数据一致性
        validateSummaryConsistency(monthKey, result);
        
        // 3. 验证佣金计算准确性
        validateCommissionCalculation(monthKey, result);
        
        // 4. 验证数据完整性
        validateDataIntegrity(monthKey, result);
        
        log.info("月份数据校验完成: {}", result.getSummary());
        
        return result;
    }
    
    /**
     * 检查是否有遗漏的PENDING记录
     */
    private void validatePendingRecords(MonthKey monthKey, ValidationResult result) {
        List<CommissionRecord> pendingRecords = commissionRecordRepository
            .findByMonthKeyAndStatusAndDeleted(monthKey, CommissionRecord.Status.PENDING, false);
        
        if (!pendingRecords.isEmpty()) {
            result.addError(String.format("存在未处理的PENDING记录: %d条", pendingRecords.size()));
            log.warn("发现未处理的PENDING记录: monthKey={}, count={}", monthKey, pendingRecords.size());
        }
    }
    
    /**
     * 验证月度汇总数据一致性
     */
    private void validateSummaryConsistency(MonthKey monthKey, ValidationResult result) {
        // 获取所有月度汇总记录
        List<MonthlyCommissionSummary> summaries = monthlySummaryRepository.findByMonthKey(monthKey);
        
        for (MonthlyCommissionSummary summary : summaries) {
            // 获取该邀请者的所有CONFIRMED记录
            List<CommissionRecord> confirmedRecords = commissionRecordRepository
                .findByInviterIdAndMonthKeyAndStatusAndDeleted(
                    summary.getInviterId(), monthKey, CommissionRecord.Status.CONFIRMED, false);
            
            // 计算实际的总金额和总佣金
            Money actualTotalAmount = confirmedRecords.stream()
                .map(CommissionRecord::getOrderAmount)
                .reduce(Money.zero(), Money::add);
            
            Money actualTotalCommission = confirmedRecords.stream()
                .map(CommissionRecord::getCommissionAmount)
                .reduce(Money.zero(), Money::add);
            
            // 比较汇总数据
            if (!isAmountEqual(summary.getTotalOrderAmount(), actualTotalAmount)) {
                result.addError(String.format("邀请者%d订单总金额不一致: 汇总=%s, 实际=%s", 
                    summary.getInviterId(), summary.getTotalOrderAmount(), actualTotalAmount));
            }
            
            if (!isAmountEqual(summary.getCommissionAmount(), actualTotalCommission)) {
                result.addError(String.format("邀请者%d佣金总金额不一致: 汇总=%s, 实际=%s", 
                    summary.getInviterId(), summary.getCommissionAmount(), actualTotalCommission));
            }
            
            if (summary.getOrderCount() != confirmedRecords.size()) {
                result.addError(String.format("邀请者%d订单数量不一致: 汇总=%d, 实际=%d", 
                    summary.getInviterId(), summary.getOrderCount(), confirmedRecords.size()));
            }
        }
    }
    
    /**
     * 验证佣金计算准确性
     */
    private void validateCommissionCalculation(MonthKey monthKey, ValidationResult result) {
        List<CommissionRecord> confirmedRecords = commissionRecordRepository
            .findByMonthKeyAndStatusAndDeleted(monthKey, CommissionRecord.Status.CONFIRMED, false);
        
        for (CommissionRecord record : confirmedRecords) {
            // 验证佣金金额计算是否正确
            Money expectedCommission = record.getCommissionRate().calculateCommission(record.getOrderAmount());
            
            if (!isAmountEqual(record.getCommissionAmount(), expectedCommission)) {
                result.addWarning(String.format("记录%d佣金计算可能有误: 记录=%s, 计算=%s", 
                    record.getId(), record.getCommissionAmount(), expectedCommission));
            }
        }
    }
    
    /**
     * 验证数据完整性
     */
    private void validateDataIntegrity(MonthKey monthKey, ValidationResult result) {
        // 检查是否有孤立的汇总记录（没有对应的佣金记录）
        List<MonthlyCommissionSummary> summaries = monthlySummaryRepository.findByMonthKey(monthKey);
        
        for (MonthlyCommissionSummary summary : summaries) {
            List<CommissionRecord> records = commissionRecordRepository
                .findByInviterIdAndMonthKeyAndStatusAndDeleted(
                    summary.getInviterId(), monthKey, CommissionRecord.Status.CONFIRMED, false);
            
            if (records.isEmpty()) {
                result.addWarning(String.format("邀请者%d存在孤立的汇总记录（无对应佣金记录）", 
                    summary.getInviterId()));
            }
        }
        
        // 检查是否有佣金记录但没有汇总记录的情况
        List<Long> inviterIds = commissionRecordRepository
            .findDistinctInviterIdsByMonthKeyAndStatusAndDeleted(
                monthKey, CommissionRecord.Status.CONFIRMED, false);
        
        for (Long inviterId : inviterIds) {
            if (!monthlySummaryRepository.existsByInviterIdAndMonthKey(inviterId, monthKey)) {
                result.addError(String.format("邀请者%d缺少月度汇总记录", inviterId));
            }
        }
    }
    
    /**
     * 比较金额是否相等（允许小的精度差异）
     */
    private boolean isAmountEqual(Money amount1, Money amount2) {
        if (amount1 == null && amount2 == null) {
            return true;
        }
        if (amount1 == null || amount2 == null) {
            return false;
        }
        
        BigDecimal diff = amount1.getAmount().subtract(amount2.getAmount()).abs();
        return diff.compareTo(new BigDecimal("0.01")) <= 0; // 允许1分的差异
    }
    
    /**
     * 手动校验指定月份
     */
    public ValidationResult manualValidateMonth(MonthKey monthKey) {
        log.info("手动执行月度数据校验: {}", monthKey);
        return validateMonth(monthKey);
    }
    
    /**
     * 获取最近几个月的校验状态
     */
    public List<ValidationResult> getRecentValidationStatus(int months) {
        List<ValidationResult> results = new ArrayList<>();
        
        for (int i = 0; i < months; i++) {
            MonthKey monthKey = MonthKey.of(LocalDate.now().minusMonths(i + 1));
            ValidationResult result = validateMonth(monthKey);
            results.add(result);
        }
        
        return results;
    }
}
