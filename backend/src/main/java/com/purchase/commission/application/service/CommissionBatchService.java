package com.purchase.commission.application.service;

import com.purchase.commission.domain.entity.CommissionRecord;
import com.purchase.commission.domain.entity.MonthlyCommissionSummary;
import com.purchase.commission.domain.repository.CommissionRecordRepository;
import com.purchase.commission.domain.repository.MonthlyCommissionSummaryRepository;
import com.purchase.commission.domain.service.CommissionCalculationService;
import com.purchase.commission.domain.valueobject.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 佣金批量处理服务
 * 负责批量处理佣金记录和月度汇总
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CommissionBatchService {
    
    private final CommissionRecordRepository commissionRecordRepository;
    private final MonthlyCommissionSummaryRepository monthlySummaryRepository;
    private final CommissionCalculationService calculationService;
    
    /**
     * 批量处理佣金记录
     */
    @Transactional
    public ProcessingResult batchProcessCommissions(
            Map<Long, List<CommissionRecord>> groupedRecords, 
            MonthKey monthKey) {
        
        ProcessingResult result = new ProcessingResult();
        result.start();
        
        log.info("开始批量处理佣金: 邀请者数量={}, 月份={}", groupedRecords.size(), monthKey);
        
        for (Map.Entry<Long, List<CommissionRecord>> entry : groupedRecords.entrySet()) {
            Long inviterId = entry.getKey();
            List<CommissionRecord> records = entry.getValue();
            
            try {
                log.debug("处理邀请者佣金: inviterId={}, 记录数={}", inviterId, records.size());
                
                // 计算月度汇总数据
                MonthlySummaryData summaryData = calculateMonthlySummary(records);
                
                // 批量更新记录状态为CONFIRMED
                List<Long> recordIds = records.stream()
                    .map(CommissionRecord::getId)
                    .collect(Collectors.toList());
                
                int updatedCount = batchUpdateRecordsToConfirmed(recordIds);
                
                if (updatedCount != recordIds.size()) {
                    log.warn("批量更新记录数不匹配: 期望={}, 实际={}, inviterId={}", 
                            recordIds.size(), updatedCount, inviterId);
                }
                
                // 保存或更新月度汇总
                saveOrUpdateMonthlySummary(inviterId, monthKey, summaryData);
                
                result.addSuccess(inviterId, records.size());
                
                log.debug("邀请者佣金处理成功: inviterId={}, 记录数={}, 总金额={}, 总佣金={}", 
                        inviterId, records.size(), summaryData.getTotalOrderAmount(), summaryData.getTotalCommission());
                
            } catch (Exception e) {
                log.error("处理邀请者佣金失败: inviterId={}, 记录数={}", inviterId, records.size(), e);
                result.addFailure(inviterId, e.getMessage());
            }
        }
        
        result.end();
        
        log.info("批量处理佣金完成: {}", result.getSummary());
        
        return result;
    }
    
    /**
     * 计算月度汇总数据
     */
    private MonthlySummaryData calculateMonthlySummary(List<CommissionRecord> records) {
        // 计算总订单金额
        Money totalOrderAmount = records.stream()
            .map(CommissionRecord::getOrderAmount)
            .reduce(Money.zero(), Money::add);
        
        // 计算总佣金金额
        Money totalCommission = records.stream()
            .map(CommissionRecord::getCommissionAmount)
            .reduce(Money.zero(), Money::add);
        
        // 根据总订单金额计算佣金比例和月度奖金
        CommissionRate commissionRate = calculationService.calculateCommissionRate(totalOrderAmount);
        Money monthlyBonus = calculationService.calculateMonthlyBonus(totalOrderAmount);
        
        return MonthlySummaryData.of(
            totalOrderAmount,
            totalCommission,
            commissionRate.getRate(),
            monthlyBonus,
            records.size()
        );
    }
    
    /**
     * 批量更新记录状态为CONFIRMED
     */
    private int batchUpdateRecordsToConfirmed(List<Long> recordIds) {
        // 更新状态和确认时间
        LocalDateTime confirmedAt = LocalDateTime.now();
        
        // 分批更新，避免SQL语句过长
        int batchSize = 1000;
        int totalUpdated = 0;
        
        for (int i = 0; i < recordIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, recordIds.size());
            List<Long> batchIds = recordIds.subList(i, endIndex);
            
            int updated = commissionRecordRepository.batchUpdateStatusToConfirmed(batchIds, confirmedAt);
            totalUpdated += updated;
            
            log.debug("批量更新记录状态: 批次大小={}, 更新数量={}", batchIds.size(), updated);
        }
        
        return totalUpdated;
    }
    
    /**
     * 保存或更新月度汇总
     */
    private void saveOrUpdateMonthlySummary(Long inviterId, MonthKey monthKey, MonthlySummaryData data) {
        // 查找现有汇总记录
        Optional<MonthlyCommissionSummary> existingSummary = 
            monthlySummaryRepository.findByInviterIdAndMonthKey(inviterId, monthKey);
        
        MonthlyCommissionSummary summary;
        if (existingSummary.isPresent()) {
            summary = existingSummary.get();
            log.debug("更新现有月度汇总: inviterId={}, monthKey={}", inviterId, monthKey);
        } else {
            summary = MonthlyCommissionSummary.create(inviterId, monthKey);
            log.debug("创建新月度汇总: inviterId={}, monthKey={}", inviterId, monthKey);
        }
        
        // 更新汇总数据
        summary.updateSummary(
            data.getTotalOrderAmount(),
            CommissionRate.of(data.getCommissionRate()),
            data.getTotalCommission(),
            data.getMonthlyBonus(),
            data.getRecordCount()
        );
        
        // 设置最后计算时间
        summary.setLastCalculatedAt(LocalDateTime.now());
        
        // 保存到数据库
        monthlySummaryRepository.save(summary);
        
        log.debug("月度汇总保存成功: inviterId={}, monthKey={}, 总金额={}, 总佣金={}, 奖金={}", 
                inviterId, monthKey, data.getTotalOrderAmount(), data.getTotalCommission(), data.getMonthlyBonus());
    }
    
    /**
     * 重新计算指定月份的汇总（用于数据修复）
     */
    @Transactional
    public void recalculateMonthSummary(Long inviterId, MonthKey monthKey) {
        log.info("重新计算月度汇总: inviterId={}, monthKey={}", inviterId, monthKey);
        
        // 查询该月所有有效的佣金记录
        List<CommissionRecord> validRecords = commissionRecordRepository
            .findByInviterIdAndMonthKeyAndStatusAndDeleted(
                inviterId, monthKey, CommissionRecord.Status.CONFIRMED, false);
        
        if (validRecords.isEmpty()) {
            log.warn("未找到有效的佣金记录: inviterId={}, monthKey={}", inviterId, monthKey);
            return;
        }
        
        // 重新计算汇总数据
        MonthlySummaryData summaryData = calculateMonthlySummary(validRecords);
        
        // 保存或更新月度汇总
        saveOrUpdateMonthlySummary(inviterId, monthKey, summaryData);
        
        log.info("月度汇总重新计算完成: inviterId={}, monthKey={}, 记录数={}", 
                inviterId, monthKey, validRecords.size());
    }
}
