# CommissionController 佣金记录控制器

## 文件概述

CommissionController 是佣金记录管理系统的核心控制器，负责处理佣金记录相关的所有HTTP请求。该控制器采用DDD架构设计，提供完整的佣金记录管理功能，包括查询、确认、取消等操作。

## 权限控制

### 权限注解说明
所有接口都已添加 `@PreAuthorize` 权限控制注解，确保只有具备相应权限的用户才能访问：

- **查询权限**: `buyer`, `seller`, `forwarder`, `admin` - 可以查询佣金记录
- **操作权限**: `buyer`, `seller`, `forwarder`, `admin` - 可以确认、取消佣金记录
- **订单关联查询**: `seller`, `forwarder`, `buyer`, `admin` - 可以根据订单ID查询佣金记录

### 角色权限分析
- **Buyer (买家)**: 作为邀请者，可以管理自己的佣金记录；作为被邀请者，可以查看与自己订单相关的佣金记录
- **Seller (卖家)**: 作为邀请者，可以管理自己的佣金记录
- **Forwarder (货代)**: 作为邀请者，可以管理自己的佣金记录
- **Admin (管理员)**: 拥有所有佣金记录的管理权限

## 核心功能

### 1. 佣金记录查询
- **根据ID查询** (`GET /{id}`): 查询单条佣金记录详情
- **根据订单ID查询** (`GET /order/{orderId}`): 查询订单相关的佣金记录
- **根据邀请者ID查询** (`GET /inviter/{inviterId}`): 分页查询邀请者的佣金记录
- **根据邀请者和月份查询** (`GET /inviter/{inviterId}/month/{monthKey}`): 查询特定月份的佣金记录

### 2. 佣金记录管理
- **取消佣金记录** (`POST /{id}/cancel`): 将佣金记录状态变为已取消
- **批量取消** (`POST /batch/cancel`): 批量取消多个佣金记录

### 3. 月度处理说明
佣金确认功能已改为月初自动处理机制：
- **自动处理**: 每月1-3号凌晨2点自动处理上月佣金
- **手动触发**: 可通过MonthlyProcessingController手动触发处理
- **状态查询**: 可查询处理日志了解处理状态

## API接口详情

### 1. 查询接口

#### 根据ID查询佣金记录
```http
GET /api/commission/records/{id}
Authorization: 需要 seller, forwarder, admin 权限
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "id": 1,
        "inviterId": 1,
        "inviterName": "张三",
        "inviteeId": 2,
        "inviteeName": "李四",
        "orderId": 1001,
        "orderType": "PURCHASE",
        "orderTypeDescription": "采购订单",
        "orderAmount": 10000.00,
        "commissionRate": 1.2,
        "commissionAmount": 120.00,
        "status": "PENDING",
        "statusDescription": "待确认",
        "monthKey": "2024-01",
        "buyerRoleVerified": true,
        "createdAt": "2024-01-15T10:30:00",
        "updatedAt": "2024-01-15T10:30:00",
        "deleted": false
    }
}
```

#### 根据邀请者ID查询佣金记录（分页）
```http
GET /api/commission/records/inviter/{inviterId}?page=1&size=20
Authorization: 需要 seller, forwarder, admin 权限
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": [
        {
            "id": 1,
            "inviterId": 1,
            "orderId": 1001,
            "orderAmount": 10000.00,
            "commissionAmount": 120.00,
            "status": "PENDING",
            "createdAt": "2024-01-15T10:30:00"
        }
    ]
}
```

### 2. 管理接口



## 业务规则

### 1. 权限验证
- 所有接口都需要用户认证
- 不同角色拥有不同的操作权限
- 管理员拥有最高权限

### 2. 状态管理
- **PENDING** → **CONFIRMED**: 通过月初自动处理
- **PENDING/CONFIRMED** → **CANCELLED**: 通过取消操作
- **CONFIRMED** → **PAID**: 通过月度结算流程
- **PAID** 状态的记录不能被取消或删除

### 3. 数据验证
- 所有路径参数都必须是正整数
- 批量操作的ID列表不能为空
- 分页参数有默认值（page=1, size=20）

## 技术实现

### 1. 架构模式
- 采用DDD（领域驱动设计）架构
- 控制器层只负责HTTP请求处理
- 业务逻辑委托给应用服务层

### 2. 数据转换
```java
private CommissionRecordDTO convertToDTO(CommissionRecord record) {
    CommissionRecordDTO dto = new CommissionRecordDTO();
    dto.setId(record.getId());
    dto.setInviterId(record.getInviterId());
    dto.setInviteeId(record.getInviteeId());
    dto.setOrderId(record.getOrderId());
    dto.setOrderType(record.getOrderType().name());
    dto.setOrderTypeDescription(record.getOrderType().getDescription());
    dto.setOrderAmount(record.getOrderAmount().getAmount());
    dto.setCommissionRate(record.getCommissionRate().getPercentage());
    dto.setCommissionAmount(record.getCommissionAmount().getAmount());
    dto.setStatus(record.getStatus().name());
    dto.setStatusDescription(record.getStatus().getDescription());
    dto.setMonthKey(record.getMonthKey().getValue());
    dto.setBuyerRoleVerified(record.getBuyerRoleVerified());
    dto.setCreatedAt(record.getCreatedAt());
    dto.setUpdatedAt(record.getUpdatedAt());
    dto.setDeleted(record.getDeleted());
    return dto;
}
```

### 3. 异常处理
- 统一异常处理机制
- 详细的日志记录
- 用户友好的错误信息

## 注意事项

### 1. 权限安全
- 严格的权限控制，防止越权访问
- 敏感操作需要特定角色权限
- 建议在业务层再次验证用户权限

### 2. 性能考虑
- 分页查询避免大量数据加载
- 批量操作需要注意事务管理
- 考虑添加缓存机制优化查询性能

### 3. 扩展性
- 控制器设计遵循RESTful规范
- 易于添加新的查询和管理功能
- 支持不同的响应格式

## 相关文件

- **实体类**: `CommissionRecord.java`
- **应用服务**: `CommissionApplicationService.java`
- **DTO**: `CommissionRecordDTO.java`
- **仓储接口**: `CommissionRecordRepository.java`
- **领域服务**: `CommissionCalculationService.java` 