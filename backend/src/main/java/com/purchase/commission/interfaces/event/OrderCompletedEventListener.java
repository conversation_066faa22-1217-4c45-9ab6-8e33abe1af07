package com.purchase.commission.interfaces.event;

import com.purchase.commission.application.service.CommissionApplicationService;
import com.purchase.commission.domain.event.CommissionCalculationEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 订单完成事件监听器
 * 
 * 监听订单完成事件，触发佣金计算和记录创建。
 * 使用异步处理，避免影响主业务流程。
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCompletedEventListener {
    
    private final CommissionApplicationService commissionApplicationService;
    
    /**
     * 处理订单完成事件
     * 
     * @param event 订单完成事件
     */
    @Async("commissionTaskExecutor")
    @EventListener
    public void handleOrderCompleted(CommissionCalculationEvent event) {
        log.info("接收到订单完成事件: 订单ID={}, 买家ID={}, 订单金额={}, 订单类型={}", 
                 event.getOrderId(), event.getBuyerId(), event.getOrderAmount(), event.getOrderType());
        
        try {
            // 调用应用服务处理订单完成事件
            commissionApplicationService.handleOrderCompleted(
                    event.getOrderId(),
                    event.getBuyerId(),
                    event.getOrderAmount(),
                    event.getOrderType(),
                    event.getCompletedAt().toLocalDate()
            );
            
            log.info("订单完成事件处理成功: 订单ID={}", event.getOrderId());
            
        } catch (Exception e) {
            log.error("订单完成事件处理失败: 订单ID={}, 错误信息={}", event.getOrderId(), e.getMessage(), e);
            
            // 这里可以考虑发送失败通知或进行重试机制
            // 暂时只记录错误日志
        }
    }
} 