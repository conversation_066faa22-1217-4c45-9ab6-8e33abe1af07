package com.purchase.commission.interfaces.web.controller;

import com.purchase.commission.application.service.CommissionApplicationService;
import com.purchase.commission.domain.entity.CommissionRecord;
import com.purchase.commission.domain.valueobject.MonthKey;
import com.purchase.commission.interfaces.web.dto.CommissionRecordDTO;
import com.purchase.commission.interfaces.web.dto.PagedCommissionRecordsDTO;
import com.purchase.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 佣金记录控制器
 * 
 * 提供佣金记录相关的REST API接口，包括查询、确认、取消等操作。
 * 遵循RESTful设计原则，提供统一的响应格式。
 */
@Tag(name = "佣金记录管理", description = "佣金记录相关的API接口")
@Slf4j
@RestController
@RequestMapping("/api/v1/commission/records")
@RequiredArgsConstructor
@Validated
public class CommissionController {

    private final CommissionApplicationService commissionApplicationService;

    @Operation(summary = "根据ID查询佣金记录", description = "通过佣金记录ID查询详细信息")
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<CommissionRecordDTO>> getCommissionRecord(
            @Parameter(description = "佣金记录ID", required = true)
            @PathVariable @NotNull @Positive Long id) {
        
        log.info("查询佣金记录: ID={}", id);
        
        try {
            // 注意：由于应用服务没有提供根据ID查询的方法，这里需要添加实现
            // 暂时返回错误响应
            return ResponseEntity.ok(Result.error("功能暂未实现"));
        } catch (Exception e) {
            log.error("查询佣金记录失败: ID={}", id, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "根据订单ID查询佣金记录", description = "通过订单ID查询相关的佣金记录列表")
    @GetMapping("/order/{orderId}")
    @PreAuthorize("hasAnyAuthority('seller', 'forwarder', 'buyer', 'admin')")
    public ResponseEntity<Result<List<CommissionRecordDTO>>> getCommissionRecordsByOrderId(
            @Parameter(description = "订单ID", required = true)
            @PathVariable @NotNull @Positive Long orderId) {
        
        log.info("根据订单ID查询佣金记录: 订单ID={}", orderId);
        
        try {
            // 注意：应用服务暂无此方法，返回功能未实现
            return ResponseEntity.ok(Result.error("功能暂未实现"));
        } catch (Exception e) {
            log.error("根据订单ID查询佣金记录失败: 订单ID={}", orderId, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "根据邀请者ID查询佣金记录", description = "通过邀请者ID查询相关的佣金记录列表")
    @GetMapping("/inviter/{inviterId}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<PagedCommissionRecordsDTO>> getCommissionRecordsByInviterId(
            @Parameter(description = "邀请者ID", required = true)
            @PathVariable @NotNull @Positive Long inviterId,
            @Parameter(description = "页码", required = false)
            @RequestParam(defaultValue = "1") @Positive Integer page,
            @Parameter(description = "页大小", required = false)
            @RequestParam(defaultValue = "20") @Positive Integer size) {

        log.info("根据邀请者ID查询佣金记录: 邀请者ID={}, 页码={}, 页大小={}", inviterId, page, size);

        try {
            // 获取佣金记录列表
            List<CommissionRecord> records = commissionApplicationService.getCommissionRecords(
                    inviterId, size, (page - 1) * size);
            List<CommissionRecordDTO> dtos = records.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            // 获取总记录数
            Integer totalCount = commissionApplicationService.getCommissionRecordsCount(inviterId);

            // 创建分页响应
            PagedCommissionRecordsDTO pagedResponse = PagedCommissionRecordsDTO.of(
                    dtos, page, size, totalCount.longValue());

            return ResponseEntity.ok(Result.success(pagedResponse));
        } catch (Exception e) {
            log.error("根据邀请者ID查询佣金记录失败: 邀请者ID={}", inviterId, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "根据邀请者ID和月份查询佣金记录", description = "通过邀请者ID和月份查询相关的佣金记录列表")
    @GetMapping("/inviter/{inviterId}/month/{monthKey}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<List<CommissionRecordDTO>>> getCommissionRecordsByInviterIdAndMonth(
            @Parameter(description = "邀请者ID", required = true)
            @PathVariable @NotNull @Positive Long inviterId,
            @Parameter(description = "月份键（YYYY-MM格式）", required = true)
            @PathVariable @NotNull String monthKey) {
        
        log.info("根据邀请者ID和月份查询佣金记录: 邀请者ID={}, 月份={}", inviterId, monthKey);
        
        try {
            List<CommissionRecord> records = commissionApplicationService.getMonthlyCommissionRecords(
                    inviterId, MonthKey.of(monthKey));
            List<CommissionRecordDTO> dtos = records.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(Result.success(dtos));
        } catch (Exception e) {
            log.error("根据邀请者ID和月份查询佣金记录失败: 邀请者ID={}, 月份={}", inviterId, monthKey, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }



    @Operation(summary = "取消佣金记录", description = "取消指定的佣金记录，将状态变为已取消")
    @PostMapping("/{id}/cancel")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<String>> cancelCommissionRecord(
            @Parameter(description = "佣金记录ID", required = true)
            @PathVariable @NotNull @Positive Long id) {
        
        log.info("取消佣金记录: ID={}", id);
        
        try {
            commissionApplicationService.cancelCommission(id);
            return ResponseEntity.ok(Result.success("佣金记录取消成功"));
        } catch (Exception e) {
            log.error("取消佣金记录失败: ID={}", id, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }



    @Operation(summary = "批量取消佣金记录", description = "批量取消多个佣金记录")
    @PostMapping("/batch/cancel")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<String>> batchCancelCommissionRecords(
            @Parameter(description = "佣金记录ID列表", required = true)
            @RequestBody @Valid List<@NotNull @Positive Long> ids) {
        
        log.info("批量取消佣金记录: IDs={}", ids);
        
        try {
            // 注意：应用服务暂无批量取消方法，需要逐个处理
            for (Long id : ids) {
                commissionApplicationService.cancelCommission(id);
            }
            return ResponseEntity.ok(Result.success("佣金记录批量取消成功"));
        } catch (Exception e) {
            log.error("批量取消佣金记录失败: IDs={}", ids, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "获取佣金统计信息", description = "获取指定邀请者的佣金统计信息")
    @GetMapping("/summary/{inviterId}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<Map<String, Object>>> getCommissionSummary(
            @Parameter(description = "邀请者ID", required = true)
            @PathVariable @NotNull @Positive Long inviterId) {

        log.info("获取佣金统计信息: 邀请者ID={}", inviterId);

        try {
            // 获取所有佣金记录（使用较大的limit来获取所有记录）
            List<CommissionRecord> allRecords = commissionApplicationService.getCommissionRecords(inviterId, 10000, 0);

            // 计算统计信息
            Map<String, Object> summary = new HashMap<>();

            // 累计佣金
            BigDecimal totalCommission = allRecords.stream()
                    .map(record -> record.getCommissionAmount().getAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 本月佣金
            String currentMonth = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM"));
            BigDecimal currentMonthCommission = allRecords.stream()
                    .filter(record -> record.getMonthKey().getValue().equals(currentMonth))
                    .map(record -> record.getCommissionAmount().getAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 待确认佣金
            BigDecimal pendingCommission = allRecords.stream()
                    .filter(record -> record.getStatus().name().equals("PENDING"))
                    .map(record -> record.getCommissionAmount().getAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 已确认佣金
            BigDecimal confirmedCommission = allRecords.stream()
                    .filter(record -> record.getStatus().name().equals("CONFIRMED"))
                    .map(record -> record.getCommissionAmount().getAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            summary.put("totalCommission", totalCommission);
            summary.put("currentMonthCommission", currentMonthCommission);
            summary.put("pendingCommission", pendingCommission);
            summary.put("confirmedCommission", confirmedCommission);
            summary.put("paidCommission", BigDecimal.ZERO); // 暂时设为0
            summary.put("totalCount", allRecords.size());
            summary.put("pendingCount", (int) allRecords.stream().filter(record -> record.getStatus().name().equals("PENDING")).count());
            summary.put("confirmedCount", (int) allRecords.stream().filter(record -> record.getStatus().name().equals("CONFIRMED")).count());
            summary.put("currentMonthAmount", currentMonthCommission);

            return ResponseEntity.ok(Result.success(summary));
        } catch (Exception e) {
            log.error("获取佣金统计信息失败: 邀请者ID={}", inviterId, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "获取月度佣金报表", description = "获取指定邀请者和月份的佣金报表")
    @GetMapping("/monthly-report/{inviterId}/{monthKey}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<Map<String, Object>>> getMonthlyReport(
            @Parameter(description = "邀请者ID", required = true)
            @PathVariable @NotNull @Positive Long inviterId,
            @Parameter(description = "月份键（YYYY-MM格式）", required = true)
            @PathVariable @NotNull String monthKey) {

        log.info("获取月度佣金报表: 邀请者ID={}, 月份={}", inviterId, monthKey);

        try {
            List<CommissionRecord> monthlyRecords = commissionApplicationService.getMonthlyCommissionRecords(
                    inviterId, MonthKey.of(monthKey));

            Map<String, Object> report = new HashMap<>();

            // 月度总金额
            BigDecimal monthlyAmount = monthlyRecords.stream()
                    .map(record -> record.getCommissionAmount().getAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 记录数量
            int recordCount = monthlyRecords.size();

            // 按状态分组统计
            Map<String, Long> statusCounts = monthlyRecords.stream()
                    .collect(Collectors.groupingBy(
                            record -> record.getStatus().name(),
                            Collectors.counting()
                    ));

            report.put("monthKey", monthKey);
            report.put("monthlyAmount", monthlyAmount);
            report.put("recordCount", recordCount);
            report.put("statusCounts", statusCounts);
            report.put("records", monthlyRecords.stream().map(this::convertToDTO).collect(Collectors.toList()));

            return ResponseEntity.ok(Result.success(report));
        } catch (Exception e) {
            log.error("获取月度佣金报表失败: 邀请者ID={}, 月份={}", inviterId, monthKey, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "获取佣金趋势数据", description = "获取指定邀请者的佣金趋势数据")
    @GetMapping("/trend/{inviterId}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<List<Map<String, Object>>>> getCommissionTrend(
            @Parameter(description = "邀请者ID", required = true)
            @PathVariable @NotNull @Positive Long inviterId,
            @Parameter(description = "月份数量", required = false)
            @RequestParam(defaultValue = "12") @Positive Integer months) {

        log.info("获取佣金趋势数据: 邀请者ID={}, 月份数量={}", inviterId, months);

        try {
            List<Map<String, Object>> trendData = new ArrayList<>();

            // 获取最近N个月的数据
            java.time.LocalDate now = java.time.LocalDate.now();
            for (int i = months - 1; i >= 0; i--) {
                java.time.LocalDate targetDate = now.minusMonths(i);
                String monthKey = targetDate.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM"));

                List<CommissionRecord> monthlyRecords = commissionApplicationService.getMonthlyCommissionRecords(
                        inviterId, MonthKey.of(monthKey));

                BigDecimal monthlyAmount = monthlyRecords.stream()
                        .map(record -> record.getCommissionAmount().getAmount())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                Map<String, Object> monthData = new HashMap<>();
                monthData.put("monthKey", monthKey);
                monthData.put("amount", monthlyAmount);
                monthData.put("recordCount", monthlyRecords.size());

                trendData.add(monthData);
            }

            return ResponseEntity.ok(Result.success(trendData));
        } catch (Exception e) {
            log.error("获取佣金趋势数据失败: 邀请者ID={}, 月份数量={}", inviterId, months, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    /**
     * 将实体对象转换为DTO
     */
    private CommissionRecordDTO convertToDTO(CommissionRecord record) {
        CommissionRecordDTO dto = new CommissionRecordDTO();
        dto.setId(record.getId());
        dto.setInviterId(record.getInviterId());
        dto.setInviteeId(record.getInviteeId());
        dto.setOrderId(record.getOrderId());
        dto.setOrderType(record.getOrderType().name());
        dto.setOrderTypeDescription(record.getOrderType().getDescription());
        dto.setOrderAmount(record.getOrderAmount().getAmount());
        dto.setCommissionRate(record.getCommissionRate().getPercentage());
        dto.setCommissionAmount(record.getCommissionAmount().getAmount());
        dto.setStatus(record.getStatus().name());
        dto.setStatusDescription(record.getStatus().getDescription());
        dto.setMonthKey(record.getMonthKey().getValue());
        dto.setBuyerRoleVerified(record.getBuyerRoleVerified());
        dto.setCreatedAt(record.getCreatedAt());
        dto.setUpdatedAt(record.getUpdatedAt());
        dto.setDeleted(record.getDeleted());
        return dto;
    }

    @Operation(summary = "获取佣金阶梯配置", description = "获取佣金阶梯配置信息")
    @GetMapping("/config/tiers")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<List<Map<String, Object>>>> getCommissionTiers() {

        log.info("获取佣金阶梯配置");

        try {
            // 通过应用服务获取佣金阶梯配置
            List<Map<String, Object>> tiers = commissionApplicationService.getCommissionTiers();
            return ResponseEntity.ok(Result.success(tiers));
        } catch (Exception e) {
            log.error("获取佣金阶梯配置失败", e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "获取月度奖金配置", description = "获取月度奖金配置信息")
    @GetMapping("/config/bonus")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<List<Map<String, Object>>>> getMonthlyBonusConfig() {

        log.info("获取月度奖金配置");

        try {
            // 通过应用服务获取月度奖金配置
            List<Map<String, Object>> bonusConfig = commissionApplicationService.getMonthlyBonusConfig();
            return ResponseEntity.ok(Result.success(bonusConfig));
        } catch (Exception e) {
            log.error("获取月度奖金配置失败", e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }
}