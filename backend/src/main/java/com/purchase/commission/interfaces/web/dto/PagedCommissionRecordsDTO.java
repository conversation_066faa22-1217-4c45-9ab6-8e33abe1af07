package com.purchase.commission.interfaces.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分页佣金记录响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分页佣金记录响应")
public class PagedCommissionRecordsDTO {
    
    @Schema(description = "佣金记录列表")
    private List<CommissionRecordDTO> records;
    
    @Schema(description = "当前页码")
    private Integer current;
    
    @Schema(description = "页大小")
    private Integer size;
    
    @Schema(description = "总记录数")
    private Long total;
    
    @Schema(description = "总页数")
    private Integer pages;
    
    /**
     * 创建分页响应
     */
    public static PagedCommissionRecordsDTO of(List<CommissionRecordDTO> records, 
                                               Integer current, 
                                               Integer size, 
                                               Long total) {
        Integer pages = (int) Math.ceil((double) total / size);
        return new PagedCommissionRecordsDTO(records, current, size, total, pages);
    }
}
