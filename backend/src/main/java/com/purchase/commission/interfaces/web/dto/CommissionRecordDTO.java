package com.purchase.commission.interfaces.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 佣金记录数据传输对象
 * 
 * 用于前后端数据传输，封装佣金记录相关信息。
 * 包含数据验证规则和格式化要求。
 */
@Data
public class CommissionRecordDTO {

    /**
     * 佣金记录ID
     */
    private Long id;

    /**
     * 邀请者ID
     */
    @NotNull(message = "邀请者ID不能为空")
    private Long inviterId;

    /**
     * 邀请者用户名
     */
    private String inviterName;

    /**
     * 被邀请者ID（订单买家）
     */
    @NotNull(message = "被邀请者ID不能为空")
    private Long inviteeId;

    /**
     * 被邀请者用户名
     */
    private String inviteeName;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    /**
     * 订单类型
     */
    @NotNull(message = "订单类型不能为空")
    private String orderType;

    /**
     * 订单类型描述
     */
    private String orderTypeDescription;

    /**
     * 订单金额
     */
    @NotNull(message = "订单金额不能为空")
    @Positive(message = "订单金额必须大于0")
    private BigDecimal orderAmount;

    /**
     * 佣金比例（百分比形式，如1.2表示1.2%）
     */
    @NotNull(message = "佣金比例不能为空")
    @Positive(message = "佣金比例必须大于0")
    private BigDecimal commissionRate;

    /**
     * 佣金金额
     */
    @NotNull(message = "佣金金额不能为空")
    @Positive(message = "佣金金额必须大于0")
    private BigDecimal commissionAmount;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    private String status;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 月份键（YYYY-MM格式）
     */
    @NotNull(message = "月份键不能为空")
    private String monthKey;

    /**
     * 被邀请者买家角色验证
     */
    private Boolean buyerRoleVerified;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 是否删除
     */
    private Boolean deleted;
} 