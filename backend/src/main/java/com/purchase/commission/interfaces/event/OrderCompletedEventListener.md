# OrderCompletedEventListener

## 文件概述

该事件监听器负责处理订单完成事件，触发佣金计算和记录创建的业务流程。作为接口层的核心组件，它连接了订单系统和佣金系统，使用异步处理机制确保不影响主业务流程的性能。

### 设计思路
- 遵循事件驱动架构，实现系统解耦
- 使用异步处理，避免阻塞主业务流程
- 提供完整的错误处理和日志记录
- 支持重试机制和失败通知
- 确保佣金计算的及时性和准确性

### 架构位置
- 位于接口层（Interfaces Layer）
- 监听领域事件（Domain Events）
- 调用应用服务（Application Services）
- 与事件发布器协作完成事件处理

## 核心功能

### 主要职责

#### 1. 事件监听
- **功能**: 监听`OrderCompletedEvent`订单完成事件
- **触发时机**: 订单状态变更为完成时
- **事件来源**: 订单系统或其他业务模块
- **处理方式**: 异步处理，不阻塞事件发布方

#### 2. 参数提取
- **订单ID**: 提取订单的唯一标识
- **买家ID**: 提取订单的买家信息
- **订单金额**: 提取订单的成交金额
- **订单类型**: 提取订单类型（采购/货代等）
- **完成时间**: 提取订单完成的时间

#### 3. 业务委托
- **服务调用**: 委托给`CommissionApplicationService`处理
- **参数转换**: 将事件参数转换为服务方法参数
- **时间转换**: 将LocalDateTime转换为LocalDate
- **异常处理**: 捕获和处理业务异常

#### 4. 错误处理
- **异常捕获**: 捕获所有处理异常
- **日志记录**: 记录详细的错误信息
- **失败通知**: 可扩展失败通知机制
- **重试策略**: 可配置重试机制

### 注解说明

#### @Component
- **功能**: 标记为Spring组件，自动注册到容器
- **生命周期**: 由Spring容器管理
- **依赖注入**: 支持构造器注入

#### @EventListener
- **功能**: 标记方法为事件监听器
- **事件类型**: 通过方法参数类型推断
- **执行时机**: 事件发布时自动调用

#### @Async("taskExecutor")
- **功能**: 异步执行事件处理
- **线程池**: 使用指定的任务执行器
- **性能优势**: 不阻塞事件发布线程

#### @RequiredArgsConstructor
- **功能**: 自动生成构造器
- **依赖注入**: 注入final字段
- **代码简洁**: 减少样板代码

## 使用示例

### 事件发布
```java
@Service
public class OrderService {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    public void completeOrder(Long orderId) {
        // 完成订单业务逻辑
        Order order = completeOrderLogic(orderId);
        
        // 发布订单完成事件
        OrderCompletedEvent event = new OrderCompletedEvent(
            order.getId(),
            order.getBuyerId(),
            Money.of(order.getAmount()),
            order.getType(),
            order.getCompletedAt()
        );
        
        eventPublisher.publishEvent(event);
    }
}
```

### 监听器配置
```java
@Configuration
@EnableAsync
public class AsyncConfig {
    
    @Bean("taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("Commission-");
        executor.initialize();
        return executor;
    }
}
```

### 扩展重试机制
```java
@Component
@RequiredArgsConstructor
public class OrderCompletedEventListener {
    
    private final CommissionApplicationService commissionApplicationService;
    private final RetryTemplate retryTemplate;
    
    @Async("taskExecutor")
    @EventListener
    public void handleOrderCompleted(OrderCompletedEvent event) {
        log.info("接收到订单完成事件: 订单ID={}", event.getOrderId());
        
        try {
            // 使用重试模板处理
            retryTemplate.execute(context -> {
                commissionApplicationService.handleOrderCompleted(
                    event.getOrderId(),
                    event.getBuyerId(),
                    event.getOrderAmount(),
                    event.getOrderType(),
                    event.getCompletedAt().toLocalDate()
                );
                return null;
            });
            
        } catch (Exception e) {
            log.error("订单完成事件处理失败: 订单ID={}", event.getOrderId(), e);
            // 发送失败通知
            sendFailureNotification(event, e);
        }
    }
    
    private void sendFailureNotification(OrderCompletedEvent event, Exception e) {
        // 实现失败通知逻辑
        // 如：发送邮件、短信、或写入死信队列
    }
}
```

### 条件监听
```java
@EventListener(condition = "#event.orderType == 'PURCHASE'")
public void handlePurchaseOrderCompleted(OrderCompletedEvent event) {
    // 只处理采购订单的完成事件
}

@EventListener(condition = "#event.orderAmount.amount > 1000")
public void handleLargeOrderCompleted(OrderCompletedEvent event) {
    // 只处理大额订单的完成事件
}
```

### 多监听器处理
```java
@Component
public class CommissionEventHandlers {
    
    @EventListener
    @Order(1)
    public void validateOrderCompleted(OrderCompletedEvent event) {
        // 第一步：验证订单数据
    }
    
    @EventListener
    @Order(2)
    public void calculateCommission(OrderCompletedEvent event) {
        // 第二步：计算佣金
    }
    
    @EventListener
    @Order(3)
    public void sendNotification(OrderCompletedEvent event) {
        // 第三步：发送通知
    }
}
```

## 注意事项

### 异步处理
- **线程池配置**: 合理配置线程池大小，避免资源浪费
- **异常隔离**: 异步异常不会影响事件发布方
- **事务边界**: 异步方法在新事务中执行
- **上下文传递**: 注意Spring Security上下文的传递

### 性能优化
- **批量处理**: 可考虑批量处理多个事件
- **缓存策略**: 频繁查询的数据可以缓存
- **数据库连接**: 合理配置数据库连接池
- **监控指标**: 监控事件处理时间和成功率

### 错误处理
- **异常分类**: 区分业务异常和技术异常
- **重试策略**: 对临时性异常进行重试
- **死信队列**: 处理失败的事件写入死信队列
- **告警机制**: 异常率过高时及时告警

### 数据一致性
- **幂等性**: 确保重复处理不会产生副作用
- **事务管理**: 合理设计事务边界
- **补偿机制**: 提供业务补偿逻辑
- **数据校验**: 处理前校验事件数据的完整性

### 测试策略
- **单元测试**: 测试监听器的核心逻辑
- **集成测试**: 测试事件发布和监听的完整流程
- **异步测试**: 使用@Async测试注解测试异步行为
- **异常测试**: 测试各种异常场景的处理

### 监控和运维
- **日志级别**: 合理设置不同场景的日志级别
- **性能指标**: 监控事件处理的延迟和吞吐量
- **错误率**: 监控事件处理的成功率和失败率
- **资源使用**: 监控线程池和内存使用情况

### 扩展性考虑
- **多实例部署**: 支持多实例部署，避免重复处理
- **消息队列**: 可考虑使用MQ替代Spring事件
- **分布式事务**: 跨系统时考虑分布式事务方案
- **版本兼容**: 事件结构变更要保持向后兼容 