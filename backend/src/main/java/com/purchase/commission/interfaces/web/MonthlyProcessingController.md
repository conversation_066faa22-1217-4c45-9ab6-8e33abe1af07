# MonthlyProcessingController.md

## 1. 文件概述

`MonthlyProcessingController.java` 是佣金模块中负责处理月度结算任务的核心控制器，位于 `com.purchase.commission.interfaces.web` 包中。该控制器作为月度佣金处理功能的API层，为管理员提供了一系列用于触发、监控和校验月度佣金结算流程的接口。它通过依赖注入的 `MonthlyCommissionProcessor` 和 `CommissionValidationService`，将复杂的后台批量任务和数据校验过程暴露为安全的、可管理的RESTful API，是佣金系统自动化和管理后台的关键组成部分。

## 2. 核心功能

*   **手动触发结算**: 提供 `/process/{monthKey}` 接口，允许管理员手动启动指定月份的佣金计算和汇总任务。这通常用于补充自动调度任务或处理异常情况。
*   **状态查询**: 通过 `/status/{monthKey}` 接口，可以查询特定月份结算任务的详细执行状态，包括是否开始、是否完成、成功/失败记录数、错误信息和处理耗时等。
*   **日志查看**: 提供 `/logs` 接口，允许管理员查看最近执行的结算任务日志，快速了解系统的健康状况。
*   **数据校验**: 提供 `/validate/{monthKey}` 接口，允许管理员在正式结算前或结算后，手动触发对指定月份的源数据（如订单、佣金记录）的校验，以确保数据质量。
*   **校验历史查询**: 通过 `/validation/recent` 接口，可以获取最近几个月的数据校验结果，便于追踪数据质量的变化趋势。
*   **权限控制**: 所有接口都通过 `@PreAuthorize` 注解进行了严格的权限控制，确保只有授权用户（主要是`admin`）才能执行敏感操作。
*   **DTO封装**: 控制器内部定义了多个静态DTO类（`ProcessingStatusDTO`, `ProcessingLogDTO`, `ValidationResultDTO`），用于封装和格式化返回给前端的数据，实现了内外模型的隔离。

## 3. 接口说明

### 佣金处理接口

#### manualProcessMonth - 手动触发月度处理
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/commission/monthly/process/{monthKey}`
*   **权限**: `hasAuthority('admin')`
*   **参数**:
    *   `monthKey` (String, path, required): 月份键，格式为 `YYYY-MM` (e.g., `2024-07`)。
*   **返回值**: `ResponseEntity<Result<String>>` - 返回一个表示操作结果的响应。成功时返回启动信息，失败时返回具体的错误原因。
*   **业务逻辑**: 将路径变量 `monthKey` 转换为 `MonthKey` 值对象，然后调用 `monthlyProcessor.manualProcessMonth` 来异步或同步地启动处理流程。控制器负责捕获处理过程中可能发生的异常，并返回相应的HTTP状态码和错误信息。

#### getProcessingStatus - 获取处理状态
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/commission/monthly/status/{monthKey}`
*   **权限**: `hasAnyAuthority('admin', 'seller', 'forwarder')`
*   **参数**:
    *   `monthKey` (String, path, required): 月份键，格式为 `YYYY-MM`。
*   **返回值**: `ResponseEntity<Result<ProcessingStatusDTO>>` - 包含指定月份处理状态详情的DTO。
*   **业务逻辑**: 调用 `monthlyProcessor.getProcessingStatus` 获取 `CommissionProcessingLog` 实体，如果不存在，则返回一个表示“未处理”的DTO；否则，将实体转换为 `ProcessingStatusDTO` 并返回。

### 数据校验接口

#### manualValidateMonth - 手动数据校验
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/commission/monthly/validate/{monthKey}`
*   **权限**: `hasAuthority('admin')`
*   **参数**:
    *   `monthKey` (String, path, required): 月份键，格式为 `YYYY-MM`。
*   **返回值**: `ResponseEntity<Result<ValidationResultDTO>>` - 包含校验结果（是否有效、错误和警告列表、摘要）的DTO。
*   **业务逻辑**: 调用 `validationService.manualValidateMonth` 执行校验，并将返回的 `ValidationResult` 领域对象转换为 `ValidationResultDTO` 以便序列化。

## 4. 业务规则

*   **月份键**: 所有接口都使用 `YYYY-MM` 格式的 `monthKey` 作为核心标识，确保了对月份操作的统一性。
*   **幂等性**: 手动触发处理的接口 (`manualProcessMonth`) 在服务层实现时应考虑幂等性。例如，如果某个月份已经成功处理，再次触发应直接返回成功或提示已处理，而不是重新计算。
*   **权限分离**: 明确区分了不同角色的权限，普通用户（`seller`, `forwarder`）只能查看状态和日志，而只有管理员（`admin`）才能触发处理和校验等写操作。
*   **异步处理**: 月度佣金处理和数据校验通常是耗时较长的批量任务。`MonthlyCommissionProcessor` 的实现应为异步的（例如，使用 `@Async`），控制器在接收到请求后应立即返回，而不是等待任务完成。

## 5. 使用示例

```java
// 1. 前端 (React + Ant Design) 管理后台调用示例
// a. 触发月度结算
const processMonth = async (monthKey) => {
  try {
    const response = await api.post(`/api/v1/commission/monthly/process/${monthKey}`);
    message.success(response.data.data);
  } catch (error) {
    message.error(error.response.data.message);
  }
};

// b. 轮询查询处理状态
const pollStatus = (monthKey) => {
  const interval = setInterval(async () => {
    const response = await api.get(`/api/v1/commission/monthly/status/${monthKey}`);
    const status = response.data.data;
    if (status.status === 'COMPLETED' || status.status === 'FAILED') {
      clearInterval(interval);
      updateUI(status);
    }
  }, 5000); // 每5秒查询一次
};

// 2. Java客户端 (Feign) 调用示例
@FeignClient(name = "purchase-system", path = "/api/v1/commission/monthly")
public interface CommissionMonthlyClient {
    @PostMapping("/process/{monthKey}")
    @PreAuthorize("hasAuthority('admin')")
    ResponseEntity<Result<String>> manualProcessMonth(@PathVariable("monthKey") String monthKey);

    @GetMapping("/status/{monthKey}")
    ResponseEntity<Result<ProcessingStatusDTO>> getProcessingStatus(@PathVariable("monthKey") String monthKey);
}

// 3. 测试示例 (MockMvc)
@SpringBootTest
@AutoConfigureMockMvc
class MonthlyProcessingControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MonthlyCommissionProcessor monthlyProcessor;

    @Test
    @WithMockUser(authorities = "admin")
    void testManualProcessMonth_Success() throws Exception {
        String monthKey = "2024-07";
        doNothing().when(monthlyProcessor).manualProcessMonth(any(MonthKey.class));

        mockMvc.perform(post("/api/v1/commission/monthly/process/{monthKey}", monthKey))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value("月度佣金处理已启动: " + monthKey));
    }

    @Test
    @WithMockUser(authorities = "admin")
    void testManualProcessMonth_AlreadyProcessed() throws Exception {
        String monthKey = "2024-06";
        doThrow(new IllegalStateException("月份 2024-06 已经处理完成，不能重复处理"))
            .when(monthlyProcessor).manualProcessMonth(any(MonthKey.class));

        mockMvc.perform(post("/api/v1/commission/monthly/process/{monthKey}", monthKey))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("月份 2024-06 已经处理完成，不能重复处理"));
    }
}
```

## 6. 注意事项

*   **安全性**: 所有接口都必须通过 `@PreAuthorize` 进行严格的权限控制，防止未经授权的访问，特别是写操作接口。
*   **健壮性**: 控制器对 `Service` 层可能抛出的各种异常（如 `IllegalStateException`, `Exception`）都进行了捕获，并返回了不同的HTTP状态码（`400 Bad Request`, `500 Internal Server Error`），这是非常健壮的错误处理实践。
*   **DTO设计**: 在控制器内部定义静态DTO是一种选择，但在大型项目中，更推荐将DTO提取为独立的顶级类，放在专门的 `dto` 包中，以提高可维护性和复用性。
*   **异步通信**: 由于后端任务是异步的，前端需要通过轮询或WebSocket来获取任务的最终状态。API的设计应支持这种交互模式。
*   **日志清晰**: 控制器中的日志记录非常清晰，区分了 `info`, `warn`, `error` 等不同级别，并记录了关键参数，便于问题排查。
*   **MonthKey转换**: `MonthKey.of(monthKey)` 的转换可能会失败（如果格式不正确），服务层或控制器层应处理这种 `IllegalArgumentException`。
*   **返回类型**: 使用 `ResponseEntity` 包装 `Result` 对象，使得控制器可以灵活地控制HTTP响应的状态码，是比直接返回 `Result` 对象更好的实践。
*   **代码可读性**: 方法和参数命名清晰，结合Lombok的 `@RequiredArgsConstructor`，使得代码非常简洁易读。