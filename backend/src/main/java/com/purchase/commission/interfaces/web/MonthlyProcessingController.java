package com.purchase.commission.interfaces.web;

import com.purchase.commission.application.service.CommissionValidationService;
import com.purchase.commission.application.service.MonthlyCommissionProcessor;
import com.purchase.commission.domain.entity.CommissionProcessingLog;
import com.purchase.commission.domain.valueobject.MonthKey;
import com.purchase.common.response.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 月度佣金处理控制器
 * 提供月度佣金处理相关的API接口
 */
@RestController
@RequestMapping("/api/v1/commission/monthly")
@RequiredArgsConstructor
@Slf4j
public class MonthlyProcessingController {
    
    private final MonthlyCommissionProcessor monthlyProcessor;
    private final CommissionValidationService validationService;
    
    /**
     * 手动触发月度处理
     */
    @PostMapping("/process/{monthKey}")
    @PreAuthorize("hasAuthority('admin')")
    public ResponseEntity<Result<String>> manualProcessMonth(@PathVariable String monthKey) {
        try {
            MonthKey month = MonthKey.of(monthKey);
            monthlyProcessor.manualProcessMonth(month);
            
            log.info("手动触发月度佣金处理成功: {}", monthKey);
            return ResponseEntity.ok(Result.success("月度佣金处理已启动: " + monthKey));
            
        } catch (IllegalStateException e) {
            log.warn("月度佣金处理失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(e.getMessage()));
        } catch (Exception e) {
            log.error("手动触发月度佣金处理异常: monthKey={}", monthKey, e);
            return ResponseEntity.internalServerError().body(Result.error("处理失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取处理状态
     */
    @GetMapping("/status/{monthKey}")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder')")
    public ResponseEntity<Result<ProcessingStatusDTO>> getProcessingStatus(@PathVariable String monthKey) {
        try {
            MonthKey month = MonthKey.of(monthKey);
            CommissionProcessingLog log = monthlyProcessor.getProcessingStatus(month);
            
            if (log == null) {
                return ResponseEntity.ok(Result.success(ProcessingStatusDTO.notProcessed(monthKey)));
            }
            
            ProcessingStatusDTO dto = ProcessingStatusDTO.fromEntity(log);
            return ResponseEntity.ok(Result.success(dto));
            
        } catch (Exception e) {
            log.error("获取处理状态失败: monthKey={}", monthKey, e);
            return ResponseEntity.internalServerError().body(Result.error("获取状态失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取最近的处理日志
     */
    @GetMapping("/logs")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder')")
    public ResponseEntity<Result<List<ProcessingLogDTO>>> getRecentLogs(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<CommissionProcessingLog> logs = monthlyProcessor.getRecentProcessingLogs(limit);
            List<ProcessingLogDTO> dtos = logs.stream()
                .map(ProcessingLogDTO::fromEntity)
                .toList();
            
            return ResponseEntity.ok(Result.success(dtos));
            
        } catch (Exception e) {
            log.error("获取处理日志失败", e);
            return ResponseEntity.internalServerError().body(Result.error("获取日志失败: " + e.getMessage()));
        }
    }
    
    /**
     * 手动数据校验
     */
    @PostMapping("/validate/{monthKey}")
    @PreAuthorize("hasAuthority('admin')")
    public ResponseEntity<Result<ValidationResultDTO>> manualValidateMonth(@PathVariable String monthKey) {
        try {
            MonthKey month = MonthKey.of(monthKey);
            CommissionValidationService.ValidationResult result = validationService.manualValidateMonth(month);
            
            ValidationResultDTO dto = ValidationResultDTO.fromResult(result);
            
            log.info("手动数据校验完成: {}, 结果: {}", monthKey, result.getSummary());
            return ResponseEntity.ok(Result.success(dto));
            
        } catch (Exception e) {
            log.error("手动数据校验失败: monthKey={}", monthKey, e);
            return ResponseEntity.internalServerError().body(Result.error("校验失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取最近几个月的校验状态
     */
    @GetMapping("/validation/recent")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder')")
    public ResponseEntity<Result<List<ValidationResultDTO>>> getRecentValidationStatus(
            @RequestParam(defaultValue = "3") int months) {
        try {
            List<CommissionValidationService.ValidationResult> results = 
                validationService.getRecentValidationStatus(months);
            
            List<ValidationResultDTO> dtos = results.stream()
                .map(ValidationResultDTO::fromResult)
                .toList();
            
            return ResponseEntity.ok(Result.success(dtos));
            
        } catch (Exception e) {
            log.error("获取校验状态失败", e);
            return ResponseEntity.internalServerError().body(Result.error("获取校验状态失败: " + e.getMessage()));
        }
    }
    
    /**
     * 处理状态DTO
     */
    public static class ProcessingStatusDTO {
        private String monthKey;
        private String status;
        private String statusDescription;
        private Integer totalRecords;
        private Integer successCount;
        private Integer failureCount;
        private String errorMessage;
        private String startedAt;
        private String completedAt;
        private Long processingTimeMs;
        private Double successRate;
        
        // 构造函数和getter/setter省略...
        
        public static ProcessingStatusDTO fromEntity(CommissionProcessingLog log) {
            ProcessingStatusDTO dto = new ProcessingStatusDTO();
            dto.monthKey = log.getMonthKey().toString();
            dto.status = log.getStatus().name();
            dto.statusDescription = log.getStatus().getDescription();
            dto.totalRecords = log.getTotalRecords();
            dto.successCount = log.getSuccessCount();
            dto.failureCount = log.getFailureCount();
            dto.errorMessage = log.getErrorMessage();
            dto.startedAt = log.getStartedAt() != null ? log.getStartedAt().toString() : null;
            dto.completedAt = log.getCompletedAt() != null ? log.getCompletedAt().toString() : null;
            dto.processingTimeMs = log.getProcessingTimeMs();
            dto.successRate = log.getSuccessRate();
            return dto;
        }
        
        public static ProcessingStatusDTO notProcessed(String monthKey) {
            ProcessingStatusDTO dto = new ProcessingStatusDTO();
            dto.monthKey = monthKey;
            dto.status = "NOT_PROCESSED";
            dto.statusDescription = "未处理";
            return dto;
        }
        
        // Getters and setters...
        public String getMonthKey() { return monthKey; }
        public void setMonthKey(String monthKey) { this.monthKey = monthKey; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getStatusDescription() { return statusDescription; }
        public void setStatusDescription(String statusDescription) { this.statusDescription = statusDescription; }
        public Integer getTotalRecords() { return totalRecords; }
        public void setTotalRecords(Integer totalRecords) { this.totalRecords = totalRecords; }
        public Integer getSuccessCount() { return successCount; }
        public void setSuccessCount(Integer successCount) { this.successCount = successCount; }
        public Integer getFailureCount() { return failureCount; }
        public void setFailureCount(Integer failureCount) { this.failureCount = failureCount; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public String getStartedAt() { return startedAt; }
        public void setStartedAt(String startedAt) { this.startedAt = startedAt; }
        public String getCompletedAt() { return completedAt; }
        public void setCompletedAt(String completedAt) { this.completedAt = completedAt; }
        public Long getProcessingTimeMs() { return processingTimeMs; }
        public void setProcessingTimeMs(Long processingTimeMs) { this.processingTimeMs = processingTimeMs; }
        public Double getSuccessRate() { return successRate; }
        public void setSuccessRate(Double successRate) { this.successRate = successRate; }
    }
    
    /**
     * 处理日志DTO
     */
    public static class ProcessingLogDTO {
        private Long id;
        private String monthKey;
        private String status;
        private String statusDescription;
        private Integer totalRecords;
        private Integer successCount;
        private Integer failureCount;
        private String startedAt;
        private String completedAt;
        private String summary;
        
        public static ProcessingLogDTO fromEntity(CommissionProcessingLog log) {
            ProcessingLogDTO dto = new ProcessingLogDTO();
            dto.id = log.getId();
            dto.monthKey = log.getMonthKey().toString();
            dto.status = log.getStatus().name();
            dto.statusDescription = log.getStatus().getDescription();
            dto.totalRecords = log.getTotalRecords();
            dto.successCount = log.getSuccessCount();
            dto.failureCount = log.getFailureCount();
            dto.startedAt = log.getStartedAt() != null ? log.getStartedAt().toString() : null;
            dto.completedAt = log.getCompletedAt() != null ? log.getCompletedAt().toString() : null;
            dto.summary = log.getSummary();
            return dto;
        }
        
        // Getters and setters...
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getMonthKey() { return monthKey; }
        public void setMonthKey(String monthKey) { this.monthKey = monthKey; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getStatusDescription() { return statusDescription; }
        public void setStatusDescription(String statusDescription) { this.statusDescription = statusDescription; }
        public Integer getTotalRecords() { return totalRecords; }
        public void setTotalRecords(Integer totalRecords) { this.totalRecords = totalRecords; }
        public Integer getSuccessCount() { return successCount; }
        public void setSuccessCount(Integer successCount) { this.successCount = successCount; }
        public Integer getFailureCount() { return failureCount; }
        public void setFailureCount(Integer failureCount) { this.failureCount = failureCount; }
        public String getStartedAt() { return startedAt; }
        public void setStartedAt(String startedAt) { this.startedAt = startedAt; }
        public String getCompletedAt() { return completedAt; }
        public void setCompletedAt(String completedAt) { this.completedAt = completedAt; }
        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }
    }
    
    /**
     * 校验结果DTO
     */
    public static class ValidationResultDTO {
        private String monthKey;
        private boolean valid;
        private List<String> errors;
        private List<String> warnings;
        private String summary;
        
        public static ValidationResultDTO fromResult(CommissionValidationService.ValidationResult result) {
            ValidationResultDTO dto = new ValidationResultDTO();
            dto.monthKey = result.getMonthKey().toString();
            dto.valid = result.isValid();
            dto.errors = result.getErrors();
            dto.warnings = result.getWarnings();
            dto.summary = result.getSummary();
            return dto;
        }
        
        // Getters and setters...
        public String getMonthKey() { return monthKey; }
        public void setMonthKey(String monthKey) { this.monthKey = monthKey; }
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
        public List<String> getWarnings() { return warnings; }
        public void setWarnings(List<String> warnings) { this.warnings = warnings; }
        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }
    }
}
