package com.purchase.commission.interfaces.web.controller;

import com.purchase.commission.domain.entity.InviterCoupon;
import com.purchase.commission.domain.repository.InviterCouponRepository;
import com.purchase.commission.domain.service.InviterCouponService;
import com.purchase.commission.domain.valueobject.Money;
import com.purchase.commission.domain.valueobject.MonthKey;
import com.purchase.commission.interfaces.web.dto.InviterCouponDTO;
import com.purchase.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 邀请人优惠券控制器
 * 
 * 提供邀请人优惠券相关的REST API接口，包括查询、使用、验证等操作。
 * 主要服务于买家邀请人查看和使用优惠券的需求。
 */
@Tag(name = "邀请人优惠券管理", description = "邀请人优惠券相关的API接口")
@Slf4j
@RestController
@RequestMapping("/api/commission/coupons")
@RequiredArgsConstructor
@Validated
public class InviterCouponController {

    private final InviterCouponRepository inviterCouponRepository;
    private final InviterCouponService inviterCouponService;

    @Operation(summary = "根据ID查询优惠券", description = "通过优惠券ID查询详细信息")
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<InviterCouponDTO>> getCoupon(
            @Parameter(description = "优惠券ID", required = true)
            @PathVariable @NotNull @Positive Long id) {
        
        log.info("查询优惠券: ID={}", id);
        
        try {
            Optional<InviterCoupon> couponOpt = inviterCouponRepository.findById(id);
            if (couponOpt.isPresent()) {
                InviterCouponDTO dto = convertToDTO(couponOpt.get());
                return ResponseEntity.ok(Result.success(dto));
            } else {
                return ResponseEntity.ok(Result.error("优惠券不存在"));
            }
        } catch (Exception e) {
            log.error("查询优惠券失败: ID={}", id, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "根据优惠券代码查询", description = "通过优惠券代码查询详细信息")
    @GetMapping("/code/{couponCode}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<InviterCouponDTO>> getCouponByCode(
            @Parameter(description = "优惠券代码", required = true)
            @PathVariable @NotNull String couponCode) {
        
        log.info("根据代码查询优惠券: 代码={}", couponCode);
        
        try {
            Optional<InviterCoupon> couponOpt = inviterCouponRepository.findByCouponCode(couponCode);
            if (couponOpt.isPresent()) {
                InviterCouponDTO dto = convertToDTO(couponOpt.get());
                return ResponseEntity.ok(Result.success(dto));
            } else {
                return ResponseEntity.ok(Result.error("优惠券不存在"));
            }
        } catch (Exception e) {
            log.error("根据代码查询优惠券失败: 代码={}", couponCode, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "根据邀请人ID查询优惠券列表", description = "查询指定邀请人的优惠券列表")
    @GetMapping("/inviter/{inviterId}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<List<InviterCouponDTO>>> getCouponsByInviterId(
            @Parameter(description = "邀请人ID", required = true)
            @PathVariable @NotNull @Positive Long inviterId,
            @Parameter(description = "页码", required = false)
            @RequestParam(defaultValue = "1") @Positive Integer page,
            @Parameter(description = "页大小", required = false)
            @RequestParam(defaultValue = "20") @Positive Integer size) {
        
        log.info("根据邀请人ID查询优惠券列表: 邀请人ID={}, 页码={}, 页大小={}", inviterId, page, size);
        
        try {
            List<InviterCoupon> coupons = inviterCouponRepository.findByInviterId(
                    inviterId, size, (page - 1) * size);
            List<InviterCouponDTO> dtos = coupons.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(Result.success(dtos));
        } catch (Exception e) {
            log.error("根据邀请人ID查询优惠券列表失败: 邀请人ID={}", inviterId, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "根据邀请人ID和月份查询优惠券", description = "查询指定邀请人在指定月份的优惠券")
    @GetMapping("/inviter/{inviterId}/month/{monthKey}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<List<InviterCouponDTO>>> getCouponsByInviterIdAndMonth(
            @Parameter(description = "邀请人ID", required = true)
            @PathVariable @NotNull @Positive Long inviterId,
            @Parameter(description = "月份键（YYYY-MM格式）", required = true)
            @PathVariable @NotNull String monthKey) {
        
        log.info("根据邀请人ID和月份查询优惠券: 邀请人ID={}, 月份={}", inviterId, monthKey);
        
        try {
            List<InviterCoupon> coupons = inviterCouponRepository.findByInviterIdAndMonthKey(
                    inviterId, MonthKey.of(monthKey));
            List<InviterCouponDTO> dtos = coupons.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(Result.success(dtos));
        } catch (Exception e) {
            log.error("根据邀请人ID和月份查询优惠券失败: 邀请人ID={}, 月份={}", inviterId, monthKey, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "查询有效优惠券", description = "查询指定邀请人的有效优惠券列表")
    @GetMapping("/inviter/{inviterId}/active")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<List<InviterCouponDTO>>> getActiveCoupons(
            @Parameter(description = "邀请人ID", required = true)
            @PathVariable @NotNull @Positive Long inviterId) {
        
        log.info("查询有效优惠券: 邀请人ID={}", inviterId);
        
        try {
            List<InviterCoupon> coupons = inviterCouponRepository.findActiveByInviterId(
                    inviterId, LocalDateTime.now());
            List<InviterCouponDTO> dtos = coupons.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(Result.success(dtos));
        } catch (Exception e) {
            log.error("查询有效优惠券失败: 邀请人ID={}", inviterId, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "验证优惠券使用条件", description = "验证优惠券是否可以在指定订单中使用")
    @PostMapping("/{id}/validate")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<CouponValidationResponse>> validateCoupon(
            @Parameter(description = "优惠券ID", required = true)
            @PathVariable @NotNull @Positive Long id,
            @Parameter(description = "订单金额", required = true)
            @RequestParam @NotNull @Positive String orderAmount,
            @Parameter(description = "用户ID", required = true)
            @RequestParam @NotNull @Positive Long userId) {
        
        log.info("验证优惠券使用条件: ID={}, 订单金额={}, 用户ID={}", id, orderAmount, userId);
        
        try {
            Optional<InviterCoupon> couponOpt = inviterCouponRepository.findById(id);
            if (!couponOpt.isPresent()) {
                return ResponseEntity.ok(Result.error("优惠券不存在"));
            }
            
            InviterCoupon coupon = couponOpt.get();
            Money amount = Money.of(orderAmount);
            
            InviterCouponService.CouponValidationResult result = 
                    inviterCouponService.validateCouponUsage(coupon, amount, userId);
            
            CouponValidationResponse response = new CouponValidationResponse();
            response.setValid(result.isValid());
            response.setMessage(result.getMessage());
            response.setDiscountAmount(result.getDiscountAmount().getAmount());
            
            return ResponseEntity.ok(Result.success(response));
        } catch (Exception e) {
            log.error("验证优惠券使用条件失败: ID={}", id, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "使用优惠券", description = "在指定订单中使用优惠券")
    @PostMapping("/{id}/use")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<String>> useCoupon(
            @Parameter(description = "优惠券ID", required = true)
            @PathVariable @NotNull @Positive Long id,
            @Parameter(description = "使用订单ID", required = true)
            @RequestParam @NotNull @Positive Long orderId) {
        
        log.info("使用优惠券: ID={}, 订单ID={}", id, orderId);
        
        try {
            Optional<InviterCoupon> couponOpt = inviterCouponRepository.findById(id);
            if (!couponOpt.isPresent()) {
                return ResponseEntity.ok(Result.error("优惠券不存在"));
            }
            
            InviterCoupon coupon = couponOpt.get();
            coupon.use(orderId);
            inviterCouponRepository.save(coupon);
            
            return ResponseEntity.ok(Result.success("优惠券使用成功"));
        } catch (Exception e) {
            log.error("使用优惠券失败: ID={}, 订单ID={}", id, orderId, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    @Operation(summary = "取消优惠券", description = "取消指定的优惠券")
    @PostMapping("/{id}/cancel")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<String>> cancelCoupon(
            @Parameter(description = "优惠券ID", required = true)
            @PathVariable @NotNull @Positive Long id) {
        
        log.info("取消优惠券: ID={}", id);
        
        try {
            Optional<InviterCoupon> couponOpt = inviterCouponRepository.findById(id);
            if (!couponOpt.isPresent()) {
                return ResponseEntity.ok(Result.error("优惠券不存在"));
            }
            
            InviterCoupon coupon = couponOpt.get();
            coupon.cancel();
            inviterCouponRepository.save(coupon);
            
            return ResponseEntity.ok(Result.success("优惠券取消成功"));
        } catch (Exception e) {
            log.error("取消优惠券失败: ID={}", id, e);
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }

    /**
     * 将实体对象转换为DTO
     */
    private InviterCouponDTO convertToDTO(InviterCoupon coupon) {
        InviterCouponDTO dto = new InviterCouponDTO();
        dto.setId(coupon.getId());
        dto.setInviterId(coupon.getInviterId());
        dto.setInviteeId(coupon.getInviteeId());
        dto.setTriggerOrderId(coupon.getTriggerOrderId());
        dto.setCouponCode(coupon.getCouponCode());
        dto.setCouponType(coupon.getCouponType().name());
        dto.setCouponTypeDescription(coupon.getCouponType().getDescription());
        dto.setDiscountValue(coupon.getDiscountValue().getAmount());
        dto.setMinimumAmount(coupon.getMinimumAmount() != null ? coupon.getMinimumAmount().getAmount() : null);
        dto.setMaximumDiscount(coupon.getMaximumDiscount() != null ? coupon.getMaximumDiscount().getAmount() : null);
        dto.setStatus(coupon.getStatus().name());
        dto.setStatusDescription(coupon.getStatus().getDescription());
        dto.setMonthKey(coupon.getMonthKey().getValue());
        dto.setValidFrom(coupon.getValidFrom());
        dto.setValidUntil(coupon.getValidUntil());
        dto.setUsedAt(coupon.getUsedAt());
        dto.setUsedOrderId(coupon.getUsedOrderId());
        dto.setCreatedAt(coupon.getCreatedAt());
        dto.setUpdatedAt(coupon.getUpdatedAt());
        dto.setValueDescription(inviterCouponService.getCouponValueDescription(coupon));
        dto.setUsable(coupon.isUsable());
        dto.setExpiringSoon(inviterCouponService.isExpiringSoon(coupon, 7));
        return dto;
    }

    /**
     * 优惠券验证响应
     */
    public static class CouponValidationResponse {
        private boolean valid;
        private String message;
        private java.math.BigDecimal discountAmount;

        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public java.math.BigDecimal getDiscountAmount() { return discountAmount; }
        public void setDiscountAmount(java.math.BigDecimal discountAmount) { this.discountAmount = discountAmount; }
    }
}
