# InviterCouponController.md

## 1. 文件概述

`InviterCouponController.java` 是佣金模块中负责管理“邀请人优惠券”的API控制器，位于 `com.purchase.commission.interfaces.web.controller` 包中。该控制器作为邀请人优惠券功能的前端接口，为客户端（主要是Web前端）提供了一套完整的RESTful API，用于查询、验证和操作优惠券。它通过依赖注入的 `InviterCouponRepository` 和 `InviterCouponService`，将后端的领域实体和业务逻辑暴露为安全、易于使用的HTTP接口，是实现优惠券功能闭环的关键部分。

## 2. 核心功能

*   **多维度查询**: 提供了丰富的查询接口，支持通过ID、唯一的优惠券代码、邀请人ID、以及邀请人ID和月份的组合来查询优惠券。
*   **状态查询**: 支持专门查询指定邀请人的所有有效（`ACTIVE`）优惠券，便于用户在结算时选择。
*   **使用验证**: 提供 `/validate` 接口，允许客户端在实际使用优惠券前，先行验证其是否满足使用条件（如订单金额、用户归属等），并返回可抵扣的金额。
*   **状态变更**: 提供了 `/use` 和 `/cancel` 两个核心的写操作接口，用于将优惠券标记为“已使用”或“已取消”。
*   **权限控制**: 所有接口都通过 `@PreAuthorize` 注解进行了权限控制，确保了操作的安全性。
*   **数据校验**: 使用 `@Validated` 和JSR-303注解（如 `@NotNull`, `@Positive`）对请求参数进行了声明式校验，保证了输入数据的合法性。
*   **DTO转换**: 包含一个私有的 `convertToDTO` 方法，负责将后端的 `InviterCoupon` 领域实体转换为前端友好的 `InviterCouponDTO` 数据传输对象，其中包含了额外的描述性字段和状态字段。

## 3. 接口说明

### 优惠券查询接口

#### getCouponsByInviterId - 根据邀请人ID查询优惠券列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/commission/coupons/inviter/{inviterId}`
*   **权限**: `hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')`
*   **参数**:
    *   `inviterId` (Long, path, required): 邀请人的用户ID。
    *   `page` (Integer, query, optional, default=1): 页码。
    *   `size` (Integer, query, optional, default=20): 每页大小。
*   **返回值**: `ResponseEntity<Result<List<InviterCouponDTO>>>` - 包含优惠券DTO列表的响应。
*   **业务逻辑**: 调用 `inviterCouponRepository.findByInviterId` 进行分页查询，然后将查询到的实体列表通过 `convertToDTO` 方法转换为DTO列表后返回。

#### getActiveCoupons - 查询有效优惠券
*   **HTTP方法**: `GET`
*   **路径**: `/api/commission/coupons/inviter/{inviterId}/active`
*   **权限**: `hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')`
*   **参数**:
    *   `inviterId` (Long, path, required): 邀请人的用户ID。
*   **返回值**: `ResponseEntity<Result<List<InviterCouponDTO>>>` - 包含所有有效优惠券的DTO列表。
*   **业务逻辑**: 调用 `inviterCouponRepository.findActiveByInviterId`，传入当前时间来查询所有未过期且状态为`ACTIVE`的优惠券。

### 优惠券操作接口

#### validateCoupon - 验证优惠券使用条件
*   **HTTP方法**: `POST`
*   **路径**: `/api/commission/coupons/{id}/validate`
*   **权限**: `hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')`
*   **参数**:
    *   `id` (Long, path, required): 优惠券ID。
    *   `orderAmount` (String, query, required): 订单金额。
    *   `userId` (Long, query, required): 当前尝试使用的用户ID。
*   **返回值**: `ResponseEntity<Result<CouponValidationResponse>>` - 包含验证结果（`valid`, `message`, `discountAmount`）的响应对象。
*   **业务逻辑**: 先从仓库获取优惠券实体，然后调用 `inviterCouponService.validateCouponUsage` 执行核心验证逻辑，最后将返回的 `CouponValidationResult` 领域对象转换为 `CouponValidationResponse` DTO并返回。

#### useCoupon - 使用优惠券
*   **HTTP方法**: `POST`
*   **路径**: `/api/commission/coupons/{id}/use`
*   **权限**: `hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')`
*   **参数**:
    *   `id` (Long, path, required): 优惠券ID。
    *   `orderId` (Long, query, required): 使用该优惠券的订单ID。
*   **返回值**: `ResponseEntity<Result<String>>` - 表示操作结果的字符串信息。
*   **业务逻辑**: 从仓库获取优惠券实体，调用其 `use(orderId)` 方法来变更状态，最后通过仓库的 `save` 方法将变更持久化。

## 4. 业务规则

*   **所有权验证**: 在 `validateCoupon` 和 `useCoupon` 等核心操作中，业务逻辑层（`InviterCouponService` 和 `InviterCoupon` 实体）会校验操作的用户ID是否与优惠券的 `inviterId` 匹配，防止越权使用。
*   **状态机**: `useCoupon` 和 `cancelCoupon` 接口直接调用领域实体的状态变更方法，严格遵循了实体内部定义的状态机规则（例如，已使用的优惠券不能再被使用或取消）。
*   **数据转换**: `convertToDTO` 方法是控制器与前端之间数据格式适配的核心。它不仅转换了基础数据，还调用服务计算并添加了如 `valueDescription`, `usable`, `expiringSoon` 等派生状态，减少了前端的计算逻辑。

## 5. 使用示例

```java
// 1. 前端 (React) 在结算页面验证和使用优惠券
const validateAndApplyCoupon = async (couponId, orderAmount, userId, orderId) => {
  try {
    // 步骤1: 验证优惠券
    const validationResponse = await api.post(`/api/commission/coupons/${couponId}/validate`, null, {
      params: { orderAmount, userId }
    });
    const validationResult = validationResponse.data.data;

    if (!validationResult.valid) {
      message.error(`优惠券无效: ${validationResult.message}`);
      return;
    }

    // 步骤2: (前端确认后) 使用优惠券
    const useResponse = await api.post(`/api/commission/coupons/${couponId}/use`, null, {
      params: { orderId }
    });

    if (useResponse.data.success) {
      message.success("优惠券使用成功！");
      // 更新订单价格，刷新UI
      updateOrderPrice(orderAmount - validationResult.discountAmount);
    } else {
      message.error(useResponse.data.message);
    }
  } catch (error) {
    message.error("操作失败，请重试");
  }
};

// 2. Java客户端 (Feign) 调用示例
@FeignClient(name = "purchase-system", path = "/api/commission/coupons")
public interface CouponClient {
    @GetMapping("/inviter/{inviterId}/active")
    ResponseEntity<Result<List<InviterCouponDTO>>> getActiveCoupons(@PathVariable("inviterId") Long inviterId);

    @PostMapping("/{id}/use")
    ResponseEntity<Result<String>> useCoupon(@PathVariable("id") Long id, @RequestParam("orderId") Long orderId);
}

// 3. 测试示例 (MockMvc)
@SpringBootTest
@AutoConfigureMockMvc
class InviterCouponControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private InviterCouponRepository couponRepository;
    @MockBean
    private InviterCouponService couponService;

    @Test
    @WithMockUser(authorities = "buyer")
    void testUseCoupon_Success() throws Exception {
        InviterCoupon mockCoupon = InviterCoupon.createFixedAmountCoupon(/*...*/);
        // 必须先给ID，因为实体默认ID为null
        ReflectionTestUtils.setField(mockCoupon, "id", 1L);

        when(couponRepository.findById(1L)).thenReturn(Optional.of(mockCoupon));
        when(couponRepository.save(any(InviterCoupon.class))).thenReturn(mockCoupon);

        mockMvc.perform(post("/api/commission/coupons/1/use").param("orderId", "100"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value("优惠券使用成功"));
        
        // 验证coupon的use方法被调用
        assertThat(mockCoupon.getStatus()).isEqualTo(InviterCoupon.Status.USED);
    }
}
```

## 6. 注意事项

*   **安全性**: 所有接口都通过 `@PreAuthorize` 进行了保护。在实际业务中，`getCouponsByInviterId` 等接口的 `inviterId` 应与当前认证用户的ID进行比对，防止用户查询他人的优惠券信息。
*   **事务管理**: `useCoupon` 和 `cancelCoupon` 是写操作，其对应的服务层方法应声明为事务性的 (`@Transactional`)，以确保对数据库的读和写操作在同一个事务中完成。
*   **参数校验**: 控制器层面使用了 `@Validated`，对路径变量和请求参数进行了校验，这是保证API健壮性的第一道防线。
*   **异常处理**: 控制器对所有可能发生的异常都进行了 `try-catch` 封装，并返回统一的 `Result.error` 格式，这使得前端可以一致地处理API错误。
*   **DTO转换逻辑**: `convertToDTO` 方法中包含了对 `inviterCouponService` 的调用，用于获取派生数据。这虽然方便，但也耦合了Controller和Service。另一种设计是让Service层直接返回DTO，让Controller保持纯粹的请求转发和格式转换。
*   **幂等性**: `useCoupon` 接口的实现不是幂等的。如果客户端因网络问题重试，可能会因为优惠券状态已变为 `USED` 而收到错误。在需要严格幂等性的场景，需要设计更复杂的机制（如在请求中加入唯一的重试ID）。
*   **日志**: 每个接口都记录了清晰的日志，包含了关键的请求参数，这对于线上问题的排查至关重要。