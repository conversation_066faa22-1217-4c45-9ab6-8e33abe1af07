package com.purchase.commission.interfaces.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 邀请人优惠券数据传输对象
 * 用于API接口的数据传输
 */
@Data
@Schema(description = "邀请人优惠券信息")
public class InviterCouponDTO {
    
    @Schema(description = "优惠券ID")
    private Long id;
    
    @Schema(description = "邀请人ID（买家）")
    private Long inviterId;
    
    @Schema(description = "被邀请者ID（订单创建者）")
    private Long inviteeId;
    
    @Schema(description = "触发订单ID")
    private Long triggerOrderId;
    
    @Schema(description = "优惠券代码")
    private String couponCode;
    
    @Schema(description = "优惠券类型", allowableValues = {"FIXED_AMOUNT", "PERCENTAGE"})
    private String couponType;
    
    @Schema(description = "优惠券类型描述")
    private String couponTypeDescription;
    
    @Schema(description = "优惠金额或折扣比例")
    private BigDecimal discountValue;
    
    @Schema(description = "最低使用金额")
    private BigDecimal minimumAmount;
    
    @Schema(description = "最大优惠金额（用于百分比折扣）")
    private BigDecimal maximumDiscount;
    
    @Schema(description = "状态", allowableValues = {"ACTIVE", "USED", "EXPIRED", "CANCELLED"})
    private String status;
    
    @Schema(description = "状态描述")
    private String statusDescription;
    
    @Schema(description = "月份键（格式：YYYY-MM）")
    private String monthKey;
    
    @Schema(description = "有效期开始时间")
    private LocalDateTime validFrom;
    
    @Schema(description = "有效期结束时间")
    private LocalDateTime validUntil;
    
    @Schema(description = "使用时间")
    private LocalDateTime usedAt;
    
    @Schema(description = "使用订单ID")
    private Long usedOrderId;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
    
    @Schema(description = "优惠券价值描述")
    private String valueDescription;
    
    @Schema(description = "是否可用")
    private Boolean usable;
    
    @Schema(description = "是否即将过期")
    private Boolean expiringSoon;
}
