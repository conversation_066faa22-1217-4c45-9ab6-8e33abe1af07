# Commission事件监听与佣金计算机制详解

## 概述

Commission功能模块采用**事件驱动架构**，通过监听订单完成事件来触发佣金计算。本文档详细说明该模块如何分别处理采购订单和货代订单，以及基于邀请人角色的差异化激励机制。

## 整体架构

### 事件驱动流程图

```
订单完成 → 发布事件 → 事件监听 → 角色判断 → 激励策略
    ↓          ↓          ↓          ↓          ↓
采购/货代   OrderCompleted  EventListener  买家?   佣金/优惠券
 订单        Event                      非买家?
```

### 核心组件

1. **事件发布者**: 采购订单(`UnifiedOrderServiceImpl`) & 货代订单(`ForwarderOrderServiceImpl`)
2. **事件监听器**: `OrderCompletedEventListener`
3. **应用服务**: `CommissionApplicationService`
4. **计算服务**: `CommissionCalculationService`

---

## 一、事件监听机制

### 1.1 事件监听器 (`OrderCompletedEventListener`)

```java
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCompletedEventListener {
    
    private final CommissionApplicationService commissionApplicationService;
    
    /**
     * 异步处理订单完成事件
     * 使用@Async避免影响主业务流程
     */
    @Async("commissionTaskExecutor")
    @EventListener
    public void handleOrderCompleted(OrderCompletedEvent event) {
        log.info("接收到订单完成事件: 订单ID={}, 创建者ID={}, 订单金额={}, 订单类型={}", 
                 event.getOrderId(), event.getBuyerId(), event.getOrderAmount(), event.getOrderType());
        
        try {
            // 调用应用服务处理事件
            commissionApplicationService.handleOrderCompleted(
                event.getOrderId(),
                event.getBuyerId(),        // 实际是订单创建者ID
                event.getOrderAmount(),
                event.getOrderType(),      // "purchase" 或 "logistics"
                event.getCompletedAt().toLocalDate()
            );
            
            log.info("订单完成事件处理成功: 订单ID={}", event.getOrderId());
            
        } catch (Exception e) {
            log.error("订单完成事件处理失败: 订单ID={}, 错误信息={}", 
                     event.getOrderId(), e.getMessage(), e);
        }
    }
}
```

### 1.2 事件异步处理

- **异步执行**: 使用`@Async("commissionTaskExecutor")`确保不阻塞主业务流程
- **异常隔离**: 佣金计算失败不会影响订单完成流程
- **日志记录**: 完整的事件处理日志，便于监控和排错

---

## 二、采购订单佣金处理

### 2.1 事件发布 (`UnifiedOrderServiceImpl`)

采购订单在买家确认收货时发布事件：

```java
@Transactional(rollbackFor = Exception.class)
public UnifiedOrder confirmReceipt(Long orderId, Long buyerId, String remark) {
    // ... 确认收货业务逻辑 ...
    
    // 更新订单状态为已完成
    order.setOrderStatus(UnifiedOrder.OrderStatus.COMPLETED);
    order.setCompletionTime(LocalDateTime.now());
    
    // 发布订单完成事件
    OrderCompletedEvent event = new OrderCompletedEvent(order);
    eventPublisher.publishEvent(event);
    
    return order;
}
```

### 2.2 采购订单事件数据

```java
OrderCompletedEvent {
    orderId: 采购订单ID,
    buyerId: 实际买家ID (订单购买方),
    orderAmount: 采购订单金额,
    orderType: "purchase",
    completedAt: 完成时间
}
```

### 2.3 采购订单佣金计算特点

- **触发条件**: 买家确认收货且订单状态为已完成
- **买家ID**: 明确的买家身份，直接对应订单购买方
- **邀请关系**: 查找买家的邀请人进行佣金计算
- **金额来源**: 采购订单的实际成交金额

---

## 三、货代订单佣金处理

### 3.1 事件发布 (`ForwarderOrderServiceImpl`)

货代订单在状态变为已完成时发布事件：

```java
@Transactional(rollbackFor = Exception.class)
public ForwarderOrderVO updateOrderStatus(Long id, String status) {
    // ... 更新订单状态 ...
    
    // 如果状态变为已完成，发布事件
    if (ForwarderOrder.OrderStatus.COMPLETED.equals(status)) {
        LocalDateTime completionTime = LocalDateTime.now();
        order.setCompletionTime(completionTime);
        
        // 发布订单完成事件，使用creatorId作为buyerId
        Money orderAmount = Money.of(order.getTotalPrice() != null ? 
                                   order.getTotalPrice() : BigDecimal.ZERO);
        OrderCompletedEvent event = new OrderCompletedEvent(
            order.getId(),           // 货代订单ID
            order.getCreatorId(),    // 发起人ID（可能是买家或卖家）
            orderAmount,             // 货代订单金额
            "logistics",             // 订单类型：货代订单
            completionTime           // 完成时间
        );
        eventPublisher.publishEvent(event);
    }
    
    return convertToVO(order);
}
```

### 3.2 货代订单事件数据

```java
OrderCompletedEvent {
    orderId: 货代订单ID,
    buyerId: order.creatorId (可能是买家或卖家),
    orderAmount: 货代服务金额,
    orderType: "logistics",
    completedAt: 完成时间
}
```

### 3.3 货代订单佣金计算特点

- **触发条件**: 货代订单状态变为已完成
- **创建者ID**: 使用`creatorId`作为`buyerId`传递（实际是需求发起人）
- **多角色支持**: 买家和卖家都可以发起货代需求
- **邀请关系**: 查找创建者的邀请人进行佣金计算
- **金额来源**: 货代服务的实际费用

---

## 四、角色差异化激励机制

### 4.1 核心判断逻辑

```java
public void handleOrderCompleted(Long orderId, Long buyerId, Money orderAmount, 
                               String orderType, LocalDate completedAt) {
    // 1. 验证订单有效性
    if (!calculationService.isValidCommissionOrder(orderType, "completed", true)) {
        return;
    }
    
    // 2. 查找邀请关系
    Long inviterId = getInviterByUserId(buyerId);
    if (inviterId == null) {
        return; // 无邀请关系
    }
    
    // 3. 关键判断：检查邀请人角色
    String inviterRole = userMapper.selectRoleById(inviterId);
    if ("buyer".equals(inviterRole)) {
        // 邀请人是买家 → 产生优惠券
        generateCouponForBuyerInviter(orderId, buyerId, inviterId, 
                                    orderAmount, orderType, completedAt);
        return;
    }
    
    // 4. 邀请人非买家 → 正常佣金计算
    // 继续佣金计算流程...
}
```

### 4.2 激励策略矩阵

| 邀请人角色 | 被邀请者创建订单类型 | 激励形式 | 实现状态 | 说明 |
|-----------|-------------------|---------|----------|------|
| 买家(buyer) | 采购订单 | 优惠券 | TODO | 买家邀请人获得购物优惠券 |
| 买家(buyer) | 货代订单 | 优惠券 | TODO | 买家邀请人获得购物优惠券 |
| 卖家(seller) | 采购订单 | 佣金 | ✅已实现 | 现金佣金，可提现 |
| 卖家(seller) | 货代订单 | 佣金 | ✅已实现 | 现金佣金，可提现 |
| 货代(forwarder) | 采购订单 | 佣金 | ✅已实现 | 现金佣金，可提现 |
| 货代(forwarder) | 货代订单 | 佣金 | ✅已实现 | 现金佣金，可提现 |
| 管理员(admin) | 任何订单 | 佣金 | ✅已实现 | 现金佣金，可提现 |

### 4.3 具体场景示例

#### 场景A：采购订单佣金计算
```
买家B通过卖家A的邀请码注册 → 买家B创建采购订单 → 订单完成 → 卖家A获得佣金
```

#### 场景B：货代订单佣金计算
```
买家C通过货代D的邀请码注册 → 买家C创建货代需求 → 货代订单完成 → 货代D获得佣金
```

#### 场景C：买家邀请人获得优惠券
```
卖家E通过买家F的邀请码注册 → 卖家E创建货代订单 → 订单完成 → 买家F获得优惠券（非佣金）
```

---

## 五、佣金计算详细流程

### 5.1 订单有效性验证

```java
public boolean isValidCommissionOrder(String orderType, String orderStatus, boolean buyerIsInvitee) {
    // 1. 订单状态必须是已完成
    if (!"completed".equals(orderStatus)) {
        return false;
    }
    
    // 2. 排除样品采购订单
    if (orderType != null && orderType.toLowerCase().contains("sample")) {
        return false;
    }
    
    // 3. 订单类型必须是采购订单或货代订单
    if (!"purchase".equals(orderType) && !"logistics".equals(orderType)) {
        return false;
    }
    
    // 4. 创建者必须是被邀请用户
    return buyerIsInvitee;
}
```

### 5.2 佣金比例计算

基于月度累计金额的阶梯式佣金比例：

| 月度成交金额 | 佣金比例 |
|-------------|---------|
| 0 - 5万元 | 0.9% |
| 5万+ - 15万元 | 1.1% |
| 15万+ - 30万元 | 1.2% |
| 30万+ - 50万元 | 1.3% |
| 50万元以上 | 1.4% |

### 5.3 月度奖金计算

达到特定金额阶梯时的额外奖金：

| 月度成交金额 | 奖金金额 |
|-------------|---------|
| ≥5万元 | 300元 |
| ≥10万元 | 800元 |
| ≥20万元 | 1500元 |
| ≥30万元 | 2500元 |
| ≥50万元 | 4000元 |
| ≥80万元 | 6000元 |
| ≥100万元 | 8000元 |

---

## 六、数据流转与状态管理

### 6.1 佣金记录生命周期

```
PENDING → CONFIRMED → PAID
    ↓
CANCELLED
```

- **PENDING**: 待确认状态，新创建的佣金记录
- **CONFIRMED**: 已确认状态，可进入结算流程
- **PAID**: 已支付状态，佣金已结算
- **CANCELLED**: 已取消状态，不参与结算

### 6.2 月度汇总管理

```java
public void updateMonthlySummary(Long inviterId, MonthKey monthKey) {
    // 1. 获取该月所有有效佣金记录
    List<CommissionRecord> records = commissionRecordRepository
        .findByInviterIdAndMonthKey(inviterId, monthKey);
    
    // 2. 计算汇总数据
    Money totalOrderAmount = records.stream()
        .filter(CommissionRecord::isValid)
        .map(CommissionRecord::getOrderAmount)
        .reduce(Money.zero(), Money::add);
    
    // 3. 重新计算佣金比例和奖金
    CommissionRate commissionRate = calculationService.calculateCommissionRate(totalOrderAmount);
    Money monthlyBonus = calculationService.calculateMonthlyBonus(totalOrderAmount);
    
    // 4. 更新月度汇总
    summary.updateSummary(totalOrderAmount, commissionRate, 
                         totalCommission, monthlyBonus, validRecords.size());
}
```

---

## 七、关键技术特性

### 7.1 异步处理
- **非阻塞**: 事件处理异步执行，不影响订单完成流程
- **解耦**: 佣金计算与订单业务完全解耦
- **容错**: 佣金计算失败不会影响订单状态

### 7.2 事务管理
- **应用服务事务**: `@Transactional`确保佣金记录和汇总的一致性
- **跨模块隔离**: 佣金模块异常不会回滚订单事务

### 7.3 防重复机制
```java
// 检查是否已存在佣金记录
if (commissionRecordRepository.existsByOrderIdAndInviterId(orderId, inviterId)) {
    log.warn("佣金记录已存在，跳过处理");
    return; // 避免重复创建
}
```

### 7.4 日志追踪
- **完整链路**: 从事件接收到佣金创建的完整日志
- **关键参数**: 订单ID、邀请人ID、金额、角色等关键信息
- **异常记录**: 详细的异常信息和堆栈跟踪

---

## 八、扩展与优化

### 8.1 TODO项目

1. **优惠券服务实现**
   - 完成`generateCouponForBuyerInviter()`方法
   - 设计优惠券数据模型和业务逻辑

2. **事件重试机制**
   - 处理失败事件的重试策略
   - 死信队列处理机制

3. **性能优化**
   - 批量处理机制
   - 缓存热点数据
   - 数据库查询优化

### 8.2 监控与告警

- **业务指标**: 佣金生成数量、金额统计
- **技术指标**: 事件处理延迟、失败率
- **异常告警**: 佣金计算异常、数据不一致告警

---

## 九、总结

Commission模块通过事件驱动架构实现了：

1. **统一事件处理**: 采购订单和货代订单使用相同的事件监听机制
2. **角色差异化激励**: 基于邀请人角色的不同激励策略
3. **业务解耦**: 佣金计算与订单流程完全解耦
4. **数据一致性**: 完善的事务管理和防重复机制
5. **可扩展性**: 预留优惠券等新激励形式的扩展接口

该设计确保了佣金系统的可靠性、可维护性和可扩展性，为平台的激励机制提供了稳定的技术支撑。 