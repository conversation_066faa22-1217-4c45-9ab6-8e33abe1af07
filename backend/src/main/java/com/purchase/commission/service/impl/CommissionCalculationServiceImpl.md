## CommissionCalculationServiceImpl 文档

### 1. 文件概述
佣金计算核心服务实现类，负责处理：
- 基于交易金额的佣金计算
- 佣金规则应用
- 阶梯费率计算
- 最低佣金保障

### 2. 核心功能
#### 主要职责
- 根据订单金额和佣金规则计算应付佣金
- 处理特殊业务场景的佣金调整
- 生成佣金计算记录

#### 业务规则
1. 基础佣金率：5%（可配置）
2. 阶梯计算规则：
   - 0-1000元：5%
   - 1001-5000元：4% 
   - 5000元以上：3%
3. 最低佣金：10元

### 3. 接口说明
#### 依赖组件
- `CommissionRuleRepository`：获取佣金规则
- `CommissionRecordRepository`：保存计算结果

#### 核心方法
```java
/**
 * 计算单笔订单佣金
 * @param order 订单实体
 * @return 佣金计算结果 
 */
@Override
public CommissionResult calculate(Order order) {
    // 实现细节...
}

/**
 * 批量计算佣金
 * @param orders 订单列表
 * @return 批量计算结果
 */
@Override
public List<CommissionResult> batchCalculate(List<Order> orders) {
    // 实现细节...  
}
```

### 4. 使用示例
```java
// 单笔计算示例
Order order = orderRepository.findById(1001L);
CommissionResult result = commissionCalculationService.calculate(order);

// 批量计算示例  
List<Order> orders = orderRepository.findByCreateTimeBetween(startDate, endDate);
List<CommissionResult> results = commissionCalculationService.batchCalculate(orders);
```

### 5. 注意事项
1. 线程安全：非线程安全，需保证调用方同步
2. 性能：批量计算时建议每100条一批
3. 事务：计算过程不包含事务，需调用方管理
4. 缓存：频繁调用的规则数据建议缓存