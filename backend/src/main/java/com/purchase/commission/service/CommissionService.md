# CommissionService.java

## 文件概述 (File Overview)
`CommissionService.java` 是佣金业务服务接口，位于 `com.purchase.commission.service` 包中。该接口定义了佣金管理的核心业务规范，提供了佣金计算、分配、结算、查询、统计等完整的业务功能。作为佣金模块的业务服务层接口，它抽象了复杂的佣金业务逻辑，为控制器层提供了统一的业务操作接口。该接口支持多种佣金类型，实现了完善的阶梯计算机制，并提供了丰富的查询和统计功能。

## 核心功能 (Core Functionality)
*   **佣金计算管理**: 提供多种佣金计算方式，支持阶梯式计算和自定义规则
*   **佣金分配服务**: 实现佣金的自动分配和手动调整功能
*   **结算流程管理**: 完整的佣金结算流程，包括确认、支付、对账等
*   **多维度查询**: 支持按用户、订单、时间、状态等多种维度查询佣金记录
*   **统计分析功能**: 提供佣金统计、趋势分析、收益排行等分析功能
*   **批量操作支持**: 支持佣金记录的批量处理和批量状态更新
*   **月度处理机制**: 自动化的月度佣金汇总和处理功能
*   **实时通知服务**: 佣金状态变更的实时通知和消息推送
*   **审计日志记录**: 详细的佣金操作审计日志，支持合规要求
*   **数据导出功能**: 支持佣金数据的多格式导出和报表生成
*   **缓存优化策略**: 高频查询数据的缓存优化，提升系统性能
*   **异常处理机制**: 完善的异常捕获和错误恢复机制

## 接口说明 (Interface Description)

### 佣金计算方法

#### calculateCommission - 计算佣金
*   **方法签名**: `CommissionCalculationResult calculateCommission(CommissionCalculationRequest request)`
*   **参数**: `request` (CommissionCalculationRequest) - 佣金计算请求对象，包含订单信息、邀请人信息等
*   **返回值**: `CommissionCalculationResult` - 佣金计算结果对象
*   **业务逻辑**: 
    *   验证请求参数的完整性和有效性
    *   获取佣金计算规则和配置
    *   执行阶梯式佣金计算
    *   生成佣金记录
    *   返回计算结果

#### batchCalculateCommission - 批量计算佣金
*   **方法签名**: `List<CommissionCalculationResult> batchCalculateCommission(List<CommissionCalculationRequest> requests)`
*   **参数**: `requests` (List<CommissionCalculationRequest>) - 批量计算请求列表
*   **返回值**: `List<CommissionCalculationResult>` - 批量计算结果列表
*   **业务逻辑**: 
    *   验证批量请求的数量限制
    *   并行执行佣金计算
    *   处理计算异常和失败情况
    *   返回批量计算结果

### 佣金查询方法

#### getCommissionRecords - 获取佣金记录
*   **方法签名**: `PageResult<CommissionRecordDTO> getCommissionRecords(CommissionQueryRequest request)`
*   **参数**: `request` (CommissionQueryRequest) - 查询请求对象
*   **返回值**: `PageResult<CommissionRecordDTO>` - 分页的佣金记录列表
*   **业务逻辑**: 
    *   验证查询参数和权限
    *   构建查询条件
    *   执行分页查询
    *   转换为DTO格式

#### getCommissionById - 根据ID获取佣金详情
*   **方法签名**: `CommissionRecordDTO getCommissionById(Long commissionId)`
*   **参数**: `commissionId` (Long) - 佣金记录ID
*   **返回值**: `CommissionRecordDTO` - 佣金记录详情
*   **业务逻辑**: 
    *   验证佣金记录存在性
    *   检查用户访问权限
    *   查询佣金详细信息
    *   转换为DTO格式

### 佣金统计方法

#### getCommissionStatistics - 获取佣金统计信息
*   **方法签名**: `CommissionStatisticsResponse getCommissionStatistics(StatisticsRequest request)`
*   **参数**: `request` (StatisticsRequest) - 统计请求对象
*   **返回值**: `CommissionStatisticsResponse` - 佣金统计响应对象
*   **业务逻辑**: 
    *   验证统计时间范围
    *   执行多维度统计查询
    *   计算统计指标
    *   组装统计结果

#### getMonthlyCommissionSummary - 获取月度佣金汇总
*   **方法签名**: `MonthlyCommissionSummaryDTO getMonthlyCommissionSummary(Long inviterId, String monthKey)`
*   **参数**: 
    *   `inviterId` (Long) - 邀请人ID
    *   `monthKey` (String) - 月份键（格式：yyyy-MM）
*   **返回值**: `MonthlyCommissionSummaryDTO` - 月度佣金汇总信息
*   **业务逻辑**: 
    *   验证邀请人和月份参数
    *   查询月度汇总数据
    *   计算月度统计指标
    *   返回汇总结果

### 佣金结算方法

#### processMonthlySettlement - 处理月度结算
*   **方法签名**: `MonthlySettlementResult processMonthlySettlement(String monthKey)`
*   **参数**: `monthKey` (String) - 月份键
*   **返回值**: `MonthlySettlementResult` - 月度结算结果
*   **业务逻辑**: 
    *   验证月份参数和结算条件
    *   查询待结算的佣金记录
    *   执行批量结算处理
    *   生成结算报告
    *   发送结算通知

#### confirmCommissionSettlement - 确认佣金结算
*   **方法签名**: `void confirmCommissionSettlement(Long commissionId)`
*   **参数**: `commissionId` (Long) - 佣金记录ID
*   **业务逻辑**: 
    *   验证佣金记录状态
    *   更新结算状态
    *   记录结算日志
    *   发送确认通知

### 批量操作方法

#### batchCancelCommissions - 批量取消佣金记录
*   **方法签名**: `BatchOperationResult batchCancelCommissions(List<Long> commissionIds, String reason)`
*   **参数**: 
    *   `commissionIds` (List<Long>) - 佣金记录ID列表
    *   `reason` (String) - 取消原因
*   **返回值**: `BatchOperationResult` - 批量操作结果
*   **业务逻辑**: 
    *   验证批量操作权限
    *   检查佣金记录状态
    *   执行批量取消操作
    *   记录操作日志
    *   返回操作结果

#### batchConfirmCommissions - 批量确认佣金记录
*   **方法签名**: `BatchOperationResult batchConfirmCommissions(List<Long> commissionIds)`
*   **参数**: `commissionIds` (List<Long>) - 佣金记录ID列表
*   **返回值**: `BatchOperationResult` - 批量操作结果
*   **业务逻辑**: 
    *   验证批量操作权限
    *   检查佣金记录状态
    *   执行批量确认操作
    *   更新相关统计数据
    *   发送批量通知

## 业务规则 (Business Rules)
*   **佣金计算**: 支持阶梯式计算，不同金额区间使用不同的佣金比例
*   **状态管理**: 佣金记录状态包括待处理、已确认、已支付、已取消等
*   **权限控制**: 不同角色对佣金记录有不同的访问和操作权限
*   **月度处理**: 每月自动执行佣金汇总和结算处理
*   **数据一致性**: 所有佣金操作必须保证数据的一致性和完整性
*   **审计要求**: 所有佣金操作必须记录详细的审计日志
*   **并发控制**: 使用乐观锁防止佣金记录的并发修改冲突
*   **异常处理**: 完善的异常处理和恢复机制，确保业务连续性

## 使用示例 (Usage Examples)

```java
// 1. 服务实现类示例
@Service
@Transactional
public class CommissionServiceImpl implements CommissionService {

    @Autowired
    private CommissionRecordRepository commissionRecordRepository;

    @Autowired
    private CommissionCalculationService calculationService;

    @Autowired
    private NotificationService notificationService;

    @Override
    public CommissionCalculationResult calculateCommission(CommissionCalculationRequest request) {
        // 1. 参数验证
        validateCalculationRequest(request);

        // 2. 检查是否已存在佣金记录
        CommissionRecord existingRecord = commissionRecordRepository.findByOrderId(request.getOrderId());
        if (existingRecord != null) {
            throw new BusinessException("订单已存在佣金记录: " + request.getOrderId());
        }

        // 3. 获取佣金计算规则
        CommissionRule rule = getCommissionRule(request.getInviterRole());

        // 4. 执行佣金计算
        BigDecimal commissionAmount = calculationService.calculate(
            request.getOrderAmount(),
            rule.getCommissionRate()
        );

        // 5. 创建佣金记录
        CommissionRecord record = new CommissionRecord();
        record.setOrderId(request.getOrderId());
        record.setInviterId(request.getInviterId());
        record.setOrderAmount(request.getOrderAmount());
        record.setCommissionAmount(commissionAmount);
        record.setStatus(CommissionStatus.PENDING);
        record.setCreatedAt(LocalDateTime.now());

        // 6. 保存佣金记录
        commissionRecordRepository.save(record);

        // 7. 发送计算完成通知
        notificationService.sendCommissionCalculatedNotification(record);

        // 8. 记录审计日志
        auditLogService.logCommissionCalculation(record);

        // 9. 组装返回结果
        CommissionCalculationResult result = new CommissionCalculationResult();
        result.setCommissionId(record.getId());
        result.setCommissionAmount(commissionAmount);
        result.setCalculationDetails(buildCalculationDetails(request, rule));

        return result;
    }

    @Override
    public PageResult<CommissionRecordDTO> getCommissionRecords(CommissionQueryRequest request) {
        // 1. 参数验证和权限检查
        validateQueryRequest(request);
        checkQueryPermission(request.getInviterId());

        // 2. 构建查询条件
        QueryWrapper<CommissionRecord> queryWrapper = new QueryWrapper<>();

        if (request.getInviterId() != null) {
            queryWrapper.eq("inviter_id", request.getInviterId());
        }

        if (request.getStatus() != null) {
            queryWrapper.eq("status", request.getStatus());
        }

        if (request.getStartDate() != null && request.getEndDate() != null) {
            queryWrapper.between("created_at", request.getStartDate(), request.getEndDate());
        }

        // 3. 执行分页查询
        Page<CommissionRecord> page = new Page<>(request.getPage(), request.getSize());
        IPage<CommissionRecord> result = commissionRecordRepository.selectPage(page, queryWrapper);

        // 4. 转换为DTO
        List<CommissionRecordDTO> dtoList = result.getRecords().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());

        // 5. 组装分页结果
        return new PageResult<>(dtoList, result.getCurrent(), result.getSize(), result.getTotal());
    }

    @Override
    public MonthlySettlementResult processMonthlySettlement(String monthKey) {
        // 1. 验证月份参数
        if (!isValidMonthKey(monthKey)) {
            throw new BusinessException("无效的月份参数: " + monthKey);
        }

        // 2. 检查是否已处理
        if (isMonthlySettlementProcessed(monthKey)) {
            throw new BusinessException("月度结算已处理: " + monthKey);
        }

        // 3. 查询待结算的佣金记录
        List<CommissionRecord> pendingRecords = commissionRecordRepository
            .findPendingRecordsByMonth(monthKey);

        if (pendingRecords.isEmpty()) {
            return new MonthlySettlementResult(monthKey, 0, BigDecimal.ZERO);
        }

        // 4. 执行批量结算
        BigDecimal totalAmount = BigDecimal.ZERO;
        int processedCount = 0;

        for (CommissionRecord record : pendingRecords) {
            try {
                // 更新状态为已确认
                record.setStatus(CommissionStatus.CONFIRMED);
                record.setConfirmedAt(LocalDateTime.now());
                commissionRecordRepository.updateById(record);

                totalAmount = totalAmount.add(record.getCommissionAmount());
                processedCount++;

            } catch (Exception e) {
                log.error("处理佣金记录失败: recordId={}", record.getId(), e);
            }
        }

        // 5. 创建月度汇总记录
        createMonthlyCommissionSummary(monthKey, processedCount, totalAmount);

        // 6. 发送结算完成通知
        notificationService.sendMonthlySettlementNotification(monthKey, processedCount, totalAmount);

        // 7. 记录结算日志
        auditLogService.logMonthlySettlement(monthKey, processedCount, totalAmount);

        return new MonthlySettlementResult(monthKey, processedCount, totalAmount);
    }

    // 私有辅助方法
    private void validateCalculationRequest(CommissionCalculationRequest request) {
        if (request.getOrderId() == null) {
            throw new BusinessException("订单ID不能为空");
        }
        if (request.getOrderAmount() == null || request.getOrderAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("订单金额必须大于0");
        }
        if (request.getInviterId() == null) {
            throw new BusinessException("邀请人ID不能为空");
        }
    }

    private CommissionRecordDTO convertToDTO(CommissionRecord record) {
        CommissionRecordDTO dto = new CommissionRecordDTO();
        dto.setId(record.getId());
        dto.setOrderId(record.getOrderId());
        dto.setInviterId(record.getInviterId());
        dto.setOrderAmount(record.getOrderAmount());
        dto.setCommissionAmount(record.getCommissionAmount());
        dto.setStatus(record.getStatus().name());
        dto.setCreatedAt(record.getCreatedAt());
        dto.setConfirmedAt(record.getConfirmedAt());
        return dto;
    }
}

// 2. 控制器层调用示例
@RestController
@RequestMapping("/api/v1/commission")
public class CommissionController {

    @Autowired
    private CommissionService commissionService;

    @PostMapping("/calculate")
    public Result<CommissionCalculationResult> calculateCommission(
            @Valid @RequestBody CommissionCalculationRequest request) {
        try {
            CommissionCalculationResult result = commissionService.calculateCommission(request);
            return Result.success(result);
        } catch (BusinessException e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/records")
    public Result<PageResult<CommissionRecordDTO>> getCommissionRecords(
            @Valid CommissionQueryRequest request) {
        try {
            PageResult<CommissionRecordDTO> result = commissionService.getCommissionRecords(request);
            return Result.success(result);
        } catch (BusinessException e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/monthly/settlement/{monthKey}")
    @PreAuthorize("hasAuthority('admin')")
    public Result<MonthlySettlementResult> processMonthlySettlement(@PathVariable String monthKey) {
        try {
            MonthlySettlementResult result = commissionService.processMonthlySettlement(monthKey);
            return Result.success(result);
        } catch (BusinessException e) {
            return Result.error(e.getMessage());
        }
    }
}

// 3. 事件监听器示例
@Component
public class CommissionEventListener {

    @Autowired
    private CommissionService commissionService;

    // 监听订单完成事件，自动计算佣金
    @EventListener
    @Async
    public void handleOrderCompleted(OrderCompletedEvent event) {
        try {
            // 构建佣金计算请求
            CommissionCalculationRequest request = new CommissionCalculationRequest();
            request.setOrderId(event.getOrderId());
            request.setOrderAmount(event.getOrderAmount());
            request.setInviterId(event.getInviterId());
            request.setInviterRole(event.getInviterRole());

            // 计算佣金
            CommissionCalculationResult result = commissionService.calculateCommission(request);

            log.info("订单佣金计算完成: orderId={}, commissionAmount={}",
                event.getOrderId(), result.getCommissionAmount());

        } catch (Exception e) {
            log.error("订单佣金计算失败: orderId={}", event.getOrderId(), e);
        }
    }
}

// 4. 定时任务示例
@Component
public class CommissionScheduledTasks {

    @Autowired
    private CommissionService commissionService;

    // 每月1号自动执行月度结算
    @Scheduled(cron = "0 0 2 1 * ?") // 每月1号凌晨2点
    public void autoMonthlySettlement() {
        log.info("开始自动月度佣金结算");

        try {
            // 获取上个月的月份键
            String lastMonthKey = LocalDate.now().minusMonths(1)
                .format(DateTimeFormatter.ofPattern("yyyy-MM"));

            // 执行月度结算
            MonthlySettlementResult result = commissionService.processMonthlySettlement(lastMonthKey);

            log.info("自动月度佣金结算完成: month={}, count={}, amount={}",
                lastMonthKey, result.getProcessedCount(), result.getTotalAmount());

        } catch (Exception e) {
            log.error("自动月度佣金结算失败", e);
        }
    }

    // 检查异常佣金记录
    @Scheduled(cron = "0 0 */6 * * ?") // 每6小时执行一次
    public void checkAbnormalCommissions() {
        log.info("开始检查异常佣金记录");

        try {
            // 查询长时间处于待处理状态的记录
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(7);
            List<CommissionRecordDTO> abnormalRecords =
                commissionService.findAbnormalCommissions(cutoffTime);

            if (!abnormalRecords.isEmpty()) {
                // 发送异常告警
                notificationService.sendAbnormalCommissionAlert(abnormalRecords);
                log.warn("发现异常佣金记录: count={}", abnormalRecords.size());
            }

            log.info("异常佣金记录检查完成");

        } catch (Exception e) {
            log.error("检查异常佣金记录失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
@Transactional
class CommissionServiceTest {

    @Autowired
    private CommissionService commissionService;

    @MockBean
    private NotificationService notificationService;

    @Test
    void testCalculateCommission() {
        // 准备测试数据
        CommissionCalculationRequest request = new CommissionCalculationRequest();
        request.setOrderId(1001L);
        request.setOrderAmount(new BigDecimal("5000.00"));
        request.setInviterId(100L);
        request.setInviterRole("seller");

        // 执行测试
        CommissionCalculationResult result = commissionService.calculateCommission(request);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCommissionAmount()).isGreaterThan(BigDecimal.ZERO);
        assertThat(result.getCommissionId()).isNotNull();

        // 验证数据库记录
        CommissionRecord record = commissionRecordRepository.findByOrderId(1001L);
        assertThat(record).isNotNull();
        assertThat(record.getStatus()).isEqualTo(CommissionStatus.PENDING);

        // 验证通知发送
        verify(notificationService, times(1))
            .sendCommissionCalculatedNotification(any(CommissionRecord.class));
    }

    @Test
    void testGetCommissionRecords() {
        // 准备测试数据
        CommissionQueryRequest request = new CommissionQueryRequest();
        request.setInviterId(100L);
        request.setPage(1);
        request.setSize(10);
        request.setStatus(CommissionStatus.PENDING);

        // 执行测试
        PageResult<CommissionRecordDTO> result = commissionService.getCommissionRecords(request);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getRecords()).isNotNull();
        assertThat(result.getPage()).isEqualTo(1);
        assertThat(result.getSize()).isEqualTo(10);
    }

    @Test
    void testProcessMonthlySettlement() {
        // 准备测试数据
        String monthKey = "2024-01";

        // 创建一些待结算的佣金记录
        createPendingCommissionRecords(monthKey);

        // 执行测试
        MonthlySettlementResult result = commissionService.processMonthlySettlement(monthKey);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getMonthKey()).isEqualTo(monthKey);
        assertThat(result.getProcessedCount()).isGreaterThan(0);
        assertThat(result.getTotalAmount()).isGreaterThan(BigDecimal.ZERO);

        // 验证佣金记录状态已更新
        List<CommissionRecord> records = commissionRecordRepository.findByMonthKey(monthKey);
        assertThat(records).allMatch(record -> record.getStatus() == CommissionStatus.CONFIRMED);
    }
}
```

## 注意事项 (Notes)
*   **接口与实现分离**: 该接口定义了佣金服务的契约，具体实现需要与数据访问层、其他服务和工具类集成
*   **事务管理**: 佣金计算和结算操作涉及多个数据表，必须使用@Transactional注解确保数据一致性
*   **并发控制**: 使用乐观锁或分布式锁防止佣金记录的并发修改冲突
*   **阶梯计算**: 支持复杂的阶梯式佣金计算，需要根据业务规则配置不同的计算策略
*   **状态管理**: 佣金记录的状态流转需要严格控制，防止非法状态变更
*   **异常处理**: 完善的异常处理机制，确保佣金计算错误时能够正确回滚和恢复
*   **审计日志**: 所有重要操作必须记录审计日志，包括操作人、时间、原因等
*   **性能优化**: 大量佣金查询时使用分页和索引优化，避免全表扫描
*   **缓存策略**: 佣金统计等频繁查询的数据可以考虑使用缓存提高性能
*   **消息队列**: 佣金状态变更等事件可以通过消息队列异步处理，提高系统响应性
*   **幂等性**: 佣金计算等关键操作需要保证幂等性，防止重复处理
*   **定时任务**: 月度结算等定时任务需要考虑分布式环境下的任务调度和幂等性
*   **数据一致性**: 佣金金额计算需要考虑精度问题，使用BigDecimal进行金额运算
*   **权限验证**: 确保用户只能操作自己有权限的佣金记录，防止越权访问
*   **监控告警**: 对佣金计算失败、结算异常等关键指标进行监控和告警
*   **国际化**: 佣金状态和错误消息需要支持多语言，便于国际化部署
*   **接口版本**: 佣金API的版本管理需要考虑向后兼容性，避免影响现有功能
