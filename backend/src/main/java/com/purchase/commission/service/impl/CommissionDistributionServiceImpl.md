## CommissionDistributionServiceImpl 文档

### 1. 文件概述
佣金分配核心服务实现类，负责处理：
- 佣金的多级分配（平台/代理商/销售）
- 分配比例管理
- 分配记录生成
- 异常分配处理

### 2. 核心功能
#### 主要职责
- 根据分配规则计算各级佣金
- 处理分配冲突和异常
- 生成分配记录
- 提供分配结果查询

#### 业务规则
1. 默认分配比例：
   - 平台：40%
   - 代理商：35%
   - 销售：25%
2. 特殊规则可覆盖默认比例
3. 最低分配金额：1元

### 3. 接口说明
#### 依赖组件
- `CommissionRuleRepository`：获取分配规则
- `CommissionDistributionRepository`：保存分配记录

#### 核心方法
```java
/**
 * 执行佣金分配
 * @param commission 佣金计算结果
 * @return 分配结果
 */
@Override
public DistributionResult distribute(CommissionResult commission) {
    // 实现细节...
}

/**
 * 批量分配佣金
 * @param commissions 佣金列表
 * @return 批量分配结果
 */
@Override
public List<DistributionResult> batchDistribute(List<CommissionResult> commissions) {
    // 实现细节...
}
```

### 4. 使用示例
```java
// 单笔分配示例
CommissionResult commission = commissionCalculationService.calculate(order);
DistributionResult result = commissionDistributionService.distribute(commission);

// 批量分配示例
List<CommissionResult> commissions = commissionCalculationService.batchCalculate(orders);
List<DistributionResult> results = commissionDistributionService.batchDistribute(commissions);
```

### 5. 注意事项
1. 事务管理：分配过程包含完整事务
2. 性能优化：批量处理建议每50条一批
3. 日志记录：完整记录分配操作
4. 异常处理：提供重试机制