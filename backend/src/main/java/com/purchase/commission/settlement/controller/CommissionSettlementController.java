package com.purchase.commission.settlement.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.commission.settlement.application.command.CreateSettlementCommand;
import com.purchase.commission.settlement.application.dto.MonthlyCommissionSummaryDTO;
import com.purchase.commission.settlement.application.dto.SettlementPreviewDTO;
import com.purchase.commission.settlement.application.dto.SettlementStatisticsDTO;
import com.purchase.commission.settlement.application.service.CommissionSettlementService;
import com.purchase.commission.settlement.application.service.SettlementStatisticsService;
import com.purchase.commission.settlement.controller.dto.ApiResponse;
import com.purchase.commission.settlement.domain.entity.CommissionSettlement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 佣金结算控制器
 * 提供结算单据的REST API接口
 */
@RestController
@RequestMapping("/api/v1/commission/settlements")
@Slf4j
public class CommissionSettlementController {

    private final CommissionSettlementService settlementService;
    private final SettlementStatisticsService statisticsService;

    public CommissionSettlementController(CommissionSettlementService settlementService,
                                        SettlementStatisticsService statisticsService) {
        this.settlementService = settlementService;
        this.statisticsService = statisticsService;
    }

    /**
     * 分页查询结算单据列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('admin')")
    public ResponseEntity<ApiResponse<IPage<CommissionSettlement>>> getSettlements(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        log.info("分页查询结算单据列表: page={}, size={}", page, size);

        try {
            IPage<CommissionSettlement> settlements = settlementService.getSettlements(page, size);
            log.info("查询结算单据列表成功: 总数={}, 当前页数量={}",
                    settlements.getTotal(), settlements.getRecords().size());

            return ResponseEntity.ok(ApiResponse.success(settlements, "查询结算单据列表成功"));

        } catch (Exception e) {
            log.error("查询结算单据列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询结算单据列表失败: " + e.getMessage()));
        }
    }

    /**
     * 创建结算单据
     */
    @PostMapping
    public ResponseEntity<ApiResponse<CommissionSettlement>> createSettlement(
            @Valid @RequestBody CreateSettlementCommand command,
            BindingResult bindingResult) {
        
        // 参数验证
        if (bindingResult.hasErrors()) {
            Map<String, String> errors = new HashMap<>();
            for (FieldError error : bindingResult.getFieldErrors()) {
                errors.put(error.getField(), error.getDefaultMessage());
            }
            return ResponseEntity.badRequest()
                .body(ApiResponse.validationError("参数验证失败", errors));
        }

        try {
            CommissionSettlement settlement = settlementService.createSettlement(command);
            log.info("创建结算单据成功: settlementId={}, inviterId={}, monthKey={}", 
                    settlement.getId(), command.getInviterId(), command.getMonthKey());
            
            return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(settlement, "结算单据创建成功"));
                
        } catch (IllegalArgumentException e) {
            log.warn("创建结算单据失败 - 参数错误: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
                
        } catch (IllegalStateException e) {
            log.warn("创建结算单据失败 - 状态错误: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
                
        } catch (Exception e) {
            log.error("创建结算单据失败 - 系统错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统内部错误"));
        }
    }

    /**
     * 批量创建结算单据
     */
    @PostMapping("/batch")
    public ResponseEntity<ApiResponse<List<CommissionSettlement>>> batchCreateSettlements(
            @Valid @RequestBody List<CreateSettlementCommand> commands,
            BindingResult bindingResult) {
        
        // 参数验证
        if (bindingResult.hasErrors()) {
            Map<String, String> errors = new HashMap<>();
            for (FieldError error : bindingResult.getFieldErrors()) {
                errors.put(error.getField(), error.getDefaultMessage());
            }
            return ResponseEntity.badRequest()
                .body(ApiResponse.validationError("参数验证失败", errors));
        }

        try {
            List<CommissionSettlement> settlements = settlementService.batchCreateSettlements(commands);
            log.info("批量创建结算单据成功: 总数={}, 成功数={}", commands.size(), settlements.size());
            
            return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(settlements, "批量创建结算单据成功"));
                
        } catch (Exception e) {
            log.error("批量创建结算单据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("批量创建失败"));
        }
    }

    /**
     * 结算预览
     */
    @GetMapping("/preview")
    public ResponseEntity<ApiResponse<SettlementPreviewDTO>> previewSettlement(
            @RequestParam @NotNull @Positive Long inviterId,
            @RequestParam @NotBlank String monthKey) {
        
        try {
            SettlementPreviewDTO preview = settlementService.previewSettlement(inviterId, monthKey);
            log.info("结算预览成功: inviterId={}, monthKey={}", inviterId, monthKey);
            
            return ResponseEntity.ok(ApiResponse.success(preview, "结算预览获取成功"));
            
        } catch (IllegalArgumentException e) {
            log.warn("结算预览失败 - 参数错误: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
                
        } catch (IllegalStateException e) {
            log.warn("结算预览失败 - 状态错误: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
                
        } catch (Exception e) {
            log.error("结算预览失败 - 系统错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统内部错误"));
        }
    }

    /**
     * 根据ID获取结算单据
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<CommissionSettlement>> getSettlementById(
            @PathVariable @NotNull @Positive Long id) {
        
        try {
            CommissionSettlement settlement = settlementService.getSettlementById(id);
            log.info("获取结算单据成功: settlementId={}", id);
            
            return ResponseEntity.ok(ApiResponse.success(settlement, "结算单据获取成功"));
            
        } catch (IllegalArgumentException e) {
            log.warn("获取结算单据失败 - 不存在: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.getMessage()));
                
        } catch (Exception e) {
            log.error("获取结算单据失败 - 系统错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统内部错误"));
        }
    }

    /**
     * 根据邀请者ID和月份查询结算单据
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<CommissionSettlement>> getSettlementByInviterAndMonth(
            @RequestParam @NotNull @Positive Long inviterId,
            @RequestParam @NotBlank String monthKey) {
        
        try {
            CommissionSettlement settlement = settlementService
                .getSettlementByInviterIdAndMonthKey(inviterId, monthKey);
            log.info("查询结算单据成功: inviterId={}, monthKey={}", inviterId, monthKey);
            
            return ResponseEntity.ok(ApiResponse.success(settlement, "结算单据查询成功"));
            
        } catch (IllegalArgumentException e) {
            log.warn("查询结算单据失败 - 不存在: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.getMessage()));
                
        } catch (Exception e) {
            log.error("查询结算单据失败 - 系统错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统内部错误"));
        }
    }

    /**
     * 获取待结算的月度汇总列表
     */
    @GetMapping("/pending-summaries")
    @PreAuthorize("hasAuthority('admin')")
    public ResponseEntity<ApiResponse<IPage<MonthlyCommissionSummaryDTO>>> getPendingSummaries(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        log.info("获取待结算的月度汇总列表: page={}, size={}", page, size);

        try {
            IPage<com.purchase.commission.settlement.domain.entity.MonthlyCommissionSummaryView> summaryPage =
                settlementService.getPendingSummaries(page, size);

            // 转换为DTO
            IPage<MonthlyCommissionSummaryDTO> dtoPage = summaryPage.convert(MonthlyCommissionSummaryDTO::fromEntity);

            log.info("获取待结算月度汇总成功: 总数={}, 当前页数量={}", dtoPage.getTotal(), dtoPage.getRecords().size());

            return ResponseEntity.ok(ApiResponse.success(dtoPage, "获取待结算月度汇总成功"));

        } catch (Exception e) {
            log.error("获取待结算月度汇总失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取待结算月度汇总失败: " + e.getMessage()));
        }
    }

    /**
     * 获取结算统计数据
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('admin')")
    public ResponseEntity<ApiResponse<SettlementStatisticsDTO>> getSettlementStatistics() {

        log.info("获取结算统计数据");

        try {
            SettlementStatisticsDTO statistics = SettlementStatisticsDTO.fromValueObject(
                statisticsService.getSettlementStatistics());

            log.info("获取结算统计数据成功");

            return ResponseEntity.ok(ApiResponse.success(statistics, "获取结算统计数据成功"));

        } catch (Exception e) {
            log.error("获取结算统计数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取结算统计数据失败: " + e.getMessage()));
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("service", "commission-settlement");
        healthData.put("timestamp", LocalDateTime.now());
        healthData.put("status", "UP");

        return ResponseEntity.ok(ApiResponse.success(healthData, "Commission Settlement Service is healthy"));
    }
}
