package com.purchase.commission.settlement.domain.valueobject;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Set;

/**
 * 回执文件值对象
 * 不可变对象，封装文件相关信息
 */
public class ReceiptFile {

    private final String fileUrl;
    private final String fileName;
    private final String fileType;
    private final String description;
    private final LocalDateTime uploadedAt;
    private final String uploadedBy;

    private static final Set<String> SUPPORTED_FILE_TYPES =
        Set.of("pdf", "jpg", "jpeg", "png", "webp");

    @JsonCreator
    private ReceiptFile(@JsonProperty("fileUrl") String fileUrl,
                       @JsonProperty("fileName") String fileName,
                       @JsonProperty("fileType") String fileType,
                       @JsonProperty("description") String description,
                       @JsonProperty("uploadedAt") LocalDateTime uploadedAt,
                       @JsonProperty("uploadedBy") String uploadedBy) {
        this.fileUrl = fileUrl;
        this.fileName = fileName;
        this.fileType = fileType;
        this.description = description;
        this.uploadedAt = uploadedAt;
        this.uploadedBy = uploadedBy;
    }

    /**
     * 创建回执文件
     */
    public static ReceiptFile create(String fileUrl, String description, String uploadedBy) {
        validateParameters(fileUrl, description, uploadedBy);

        String fileName = extractFileName(fileUrl);
        String fileType = extractFileType(fileUrl);

        if (!SUPPORTED_FILE_TYPES.contains(fileType.toLowerCase())) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileType);
        }

        return new ReceiptFile(
            fileUrl, fileName, fileType.toLowerCase(), description,
            LocalDateTime.now(), uploadedBy
        );
    }

    private static void validateParameters(String fileUrl, String description, String uploadedBy) {
        if (!StringUtils.hasText(fileUrl)) {
            throw new IllegalArgumentException("文件URL不能为空");
        }
        if (!StringUtils.hasText(description)) {
            throw new IllegalArgumentException("文件描述不能为空");
        }
        if (!StringUtils.hasText(uploadedBy)) {
            throw new IllegalArgumentException("上传人不能为空");
        }
    }

    private static String extractFileName(String fileUrl) {
        return fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
    }

    private static String extractFileType(String fileUrl) {
        String fileName = extractFileName(fileUrl);
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex <= 0 || dotIndex == fileName.length() - 1) {
            throw new IllegalArgumentException("不支持的文件类型: 文件名无扩展名");
        }
        return fileName.substring(dotIndex + 1);
    }

    // getters
    public String getFileUrl() { return fileUrl; }
    public String getFileName() { return fileName; }
    public String getFileType() { return fileType; }
    public String getDescription() { return description; }
    public LocalDateTime getUploadedAt() { return uploadedAt; }
    public String getUploadedBy() { return uploadedBy; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReceiptFile that = (ReceiptFile) o;
        return Objects.equals(fileUrl, that.fileUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fileUrl);
    }

    @Override
    public String toString() {
        return "ReceiptFile{" +
                "fileUrl='" + fileUrl + '\'' +
                ", fileName='" + fileName + '\'' +
                ", fileType='" + fileType + '\'' +
                ", description='" + description + '\'' +
                ", uploadedAt=" + uploadedAt +
                ", uploadedBy='" + uploadedBy + '\'' +
                '}';
    }
}
