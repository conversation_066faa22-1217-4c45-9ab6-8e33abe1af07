# SettlementPreviewDTO 结算预览DTO文档

## 文件概述

`SettlementPreviewDTO` 是结算预览数据传输对象，用于在创建结算单据前预览结算信息。该DTO采用不可变对象设计，通过Builder模式构建，为用户提供结算前的预览数据，包括佣金金额、奖金、订单统计等关键信息。

## 核心功能

### 主要职责
- **预览展示**: 为用户提供结算前的预览信息
- **数据封装**: 封装结算相关的计算结果和统计数据
- **不可变性**: 确保数据的不可变性和线程安全
- **构建便利**: 通过Builder模式提供便利的构建方式

### 业务特点
- 使用@Builder注解支持建造者模式
- 所有字段都是final，确保不可变性
- 包含完整的结算预览信息
- 支持前端展示和用户确认

## 接口说明

### 核心字段

#### inviterId
- **类型**: Long
- **修饰符**: private final
- **描述**: 邀请者ID
- **用途**: 标识结算的邀请人

#### monthKey
- **类型**: String
- **修饰符**: private final
- **描述**: 月份键值，格式为YYYY-MM
- **用途**: 标识结算的月份
- **示例**: "2024-01", "2024-12"

#### totalCommission
- **类型**: BigDecimal
- **修饰符**: private final
- **描述**: 总佣金金额
- **用途**: 该月份邀请人获得的总佣金

#### monthlyBonus
- **类型**: BigDecimal
- **修饰符**: private final
- **描述**: 月度奖金
- **用途**: 根据业绩计算的额外奖金

#### totalAmount
- **类型**: BigDecimal
- **修饰符**: private final
- **描述**: 结算总金额
- **用途**: 总佣金 + 月度奖金的总和

#### suggestedSettlementNumber
- **类型**: String
- **修饰符**: private final
- **描述**: 建议的结算编号
- **用途**: 系统生成的建议结算编号
- **格式**: 通常包含日期和序号

#### orderCount
- **类型**: Integer
- **修饰符**: private final
- **描述**: 订单数量
- **用途**: 该月份邀请人相关的订单总数

#### totalOrderAmount
- **类型**: BigDecimal
- **修饰符**: private final
- **描述**: 订单总金额
- **用途**: 该月份相关订单的总金额

## 使用示例

### 构建预览DTO
```java
@Service
public class SettlementPreviewService {
    
    public SettlementPreviewDTO buildPreview(Long inviterId, String monthKey) {
        // 1. 查询月度佣金汇总
        MonthlyCommissionSummary summary = getMonthlyCommissionSummary(inviterId, monthKey);
        
        // 2. 计算月度奖金
        BigDecimal monthlyBonus = calculateMonthlyBonus(summary);
        
        // 3. 计算总金额
        BigDecimal totalAmount = summary.getTotalCommission().add(monthlyBonus);
        
        // 4. 生成建议的结算编号
        String suggestedNumber = generateSettlementNumber(inviterId, monthKey);
        
        // 5. 构建预览DTO
        return SettlementPreviewDTO.builder()
            .inviterId(inviterId)
            .monthKey(monthKey)
            .totalCommission(summary.getTotalCommission())
            .monthlyBonus(monthlyBonus)
            .totalAmount(totalAmount)
            .suggestedSettlementNumber(suggestedNumber)
            .orderCount(summary.getOrderCount())
            .totalOrderAmount(summary.getTotalOrderAmount())
            .build();
    }
    
    private BigDecimal calculateMonthlyBonus(MonthlyCommissionSummary summary) {
        // 根据业绩计算月度奖金
        BigDecimal totalCommission = summary.getTotalCommission();
        
        if (totalCommission.compareTo(new BigDecimal("10000")) >= 0) {
            // 佣金超过10000，给予5%奖金
            return totalCommission.multiply(new BigDecimal("0.05"));
        } else if (totalCommission.compareTo(new BigDecimal("5000")) >= 0) {
            // 佣金超过5000，给予3%奖金
            return totalCommission.multiply(new BigDecimal("0.03"));
        } else {
            // 无奖金
            return BigDecimal.ZERO;
        }
    }
    
    private String generateSettlementNumber(Long inviterId, String monthKey) {
        // 生成格式：SETTLE-{monthKey}-{inviterId}-{sequence}
        String sequence = String.format("%03d", getNextSequence(monthKey));
        return String.format("SETTLE-%s-%d-%s", monthKey, inviterId, sequence);
    }
}
```

### 控制器中使用
```java
@RestController
@RequestMapping("/api/commission/settlements")
public class SettlementController {
    
    @Autowired
    private CommissionSettlementService settlementService;
    
    @GetMapping("/preview")
    public ApiResponse<SettlementPreviewDTO> previewSettlement(
            @RequestParam Long inviterId,
            @RequestParam String monthKey) {
        
        try {
            SettlementPreviewDTO preview = settlementService.previewSettlement(inviterId, monthKey);
            return ApiResponse.success(preview);
        } catch (BusinessException e) {
            return ApiResponse.error(e.getMessage());
        }
    }
}
```

### 前端使用示例
```javascript
// 获取结算预览
const getSettlementPreview = async (inviterId, monthKey) => {
  try {
    const response = await fetch(`/api/commission/settlements/preview?inviterId=${inviterId}&monthKey=${monthKey}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取结算预览失败:', error);
    throw error;
  }
};

// 格式化预览数据
const formatPreviewData = (preview) => {
  return {
    ...preview,
    formattedTotalCommission: `¥${preview.totalCommission.toLocaleString()}`,
    formattedMonthlyBonus: `¥${preview.monthlyBonus.toLocaleString()}`,
    formattedTotalAmount: `¥${preview.totalAmount.toLocaleString()}`,
    formattedTotalOrderAmount: `¥${preview.totalOrderAmount.toLocaleString()}`,
    monthDisplay: formatMonthKey(preview.monthKey)
  };
};

// 结算预览组件
const SettlementPreview = ({ inviterId, monthKey, onConfirm, onCancel }) => {
  const [preview, setPreview] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    loadPreview();
  }, [inviterId, monthKey]);
  
  const loadPreview = async () => {
    try {
      const data = await getSettlementPreview(inviterId, monthKey);
      setPreview(formatPreviewData(data));
    } catch (error) {
      message.error('加载预览数据失败');
    } finally {
      setLoading(false);
    }
  };
  
  if (loading) return <Spin size="large" />;
  if (!preview) return <div>暂无数据</div>;
  
  return (
    <Card title="结算预览" className="settlement-preview">
      <Descriptions column={2} bordered>
        <Descriptions.Item label="结算月份">
          {preview.monthDisplay}
        </Descriptions.Item>
        <Descriptions.Item label="建议结算编号">
          {preview.suggestedSettlementNumber}
        </Descriptions.Item>
        <Descriptions.Item label="订单数量">
          {preview.orderCount} 笔
        </Descriptions.Item>
        <Descriptions.Item label="订单总金额">
          {preview.formattedTotalOrderAmount}
        </Descriptions.Item>
        <Descriptions.Item label="总佣金">
          {preview.formattedTotalCommission}
        </Descriptions.Item>
        <Descriptions.Item label="月度奖金">
          {preview.formattedMonthlyBonus}
        </Descriptions.Item>
        <Descriptions.Item label="结算总金额" span={2}>
          <span className="total-amount">{preview.formattedTotalAmount}</span>
        </Descriptions.Item>
      </Descriptions>
      
      <div className="preview-actions">
        <Button onClick={onCancel}>
          取消
        </Button>
        <Button 
          type="primary" 
          onClick={() => onConfirm(preview)}
          disabled={preview.totalAmount <= 0}
        >
          确认结算
        </Button>
      </div>
    </Card>
  );
};
```

### 数据验证使用
```java
@Component
public class SettlementPreviewValidator {
    
    public void validatePreview(SettlementPreviewDTO preview) {
        // 验证邀请者ID
        if (preview.getInviterId() == null || preview.getInviterId() <= 0) {
            throw new BusinessException("邀请者ID无效");
        }
        
        // 验证月份格式
        if (!isValidMonthKey(preview.getMonthKey())) {
            throw new BusinessException("月份格式无效，应为YYYY-MM格式");
        }
        
        // 验证金额
        if (preview.getTotalCommission() == null || preview.getTotalCommission().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("总佣金金额无效");
        }
        
        if (preview.getMonthlyBonus() == null || preview.getMonthlyBonus().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("月度奖金金额无效");
        }
        
        if (preview.getTotalAmount() == null || preview.getTotalAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("结算总金额无效");
        }
        
        // 验证金额一致性
        BigDecimal calculatedTotal = preview.getTotalCommission().add(preview.getMonthlyBonus());
        if (calculatedTotal.compareTo(preview.getTotalAmount()) != 0) {
            throw new BusinessException("结算总金额计算不正确");
        }
        
        // 验证订单数量
        if (preview.getOrderCount() == null || preview.getOrderCount() < 0) {
            throw new BusinessException("订单数量无效");
        }
        
        // 验证订单总金额
        if (preview.getTotalOrderAmount() == null || preview.getTotalOrderAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("订单总金额无效");
        }
    }
    
    private boolean isValidMonthKey(String monthKey) {
        if (monthKey == null || monthKey.length() != 7) {
            return false;
        }
        
        try {
            String[] parts = monthKey.split("-");
            if (parts.length != 2) {
                return false;
            }
            
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);
            
            return year >= 2020 && year <= 2030 && month >= 1 && month <= 12;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
```

### 业务规则应用
```java
@Service
public class SettlementBusinessRuleService {
    
    public boolean canCreateSettlement(SettlementPreviewDTO preview) {
        // 1. 检查金额是否大于最小结算金额
        BigDecimal minSettlementAmount = new BigDecimal("100.00");
        if (preview.getTotalAmount().compareTo(minSettlementAmount) < 0) {
            return false;
        }
        
        // 2. 检查是否在结算周期内
        if (!isInSettlementPeriod(preview.getMonthKey())) {
            return false;
        }
        
        // 3. 检查是否已存在结算记录
        if (settlementExists(preview.getInviterId(), preview.getMonthKey())) {
            return false;
        }
        
        return true;
    }
    
    public String getSettlementRestrictionMessage(SettlementPreviewDTO preview) {
        BigDecimal minSettlementAmount = new BigDecimal("100.00");
        if (preview.getTotalAmount().compareTo(minSettlementAmount) < 0) {
            return "结算金额不足最小结算金额（¥100.00）";
        }
        
        if (!isInSettlementPeriod(preview.getMonthKey())) {
            return "当前不在结算周期内";
        }
        
        if (settlementExists(preview.getInviterId(), preview.getMonthKey())) {
            return "该月份已存在结算记录";
        }
        
        return null;
    }
}
```

## 注意事项

### 不可变性
1. **final字段**: 所有字段都是final，确保对象不可变
2. **线程安全**: 不可变对象天然线程安全
3. **防御性复制**: 如果包含可变对象，需要防御性复制
4. **Builder模式**: 使用Builder模式简化不可变对象的构建

### 数据精度
1. **BigDecimal**: 使用BigDecimal确保金额计算精度
2. **舍入模式**: 明确指定舍入模式
3. **比较操作**: 使用compareTo方法比较BigDecimal
4. **格式化**: 前端显示时注意格式化

### 业务规则
1. **验证逻辑**: 在构建时进行必要的验证
2. **业务约束**: 确保数据符合业务约束
3. **一致性检查**: 验证计算结果的一致性
4. **边界条件**: 处理各种边界条件

### 性能考虑
1. **对象创建**: 不可变对象的创建成本
2. **内存使用**: 注意内存使用效率
3. **缓存友好**: 不可变对象适合缓存
4. **GC压力**: 大量创建时的GC压力
