# SettlementStatisticsService.java

## 文件概述 (File Overview)
`SettlementStatisticsService.java` 是结算统计应用服务，位于 `com.purchase.commission.settlement.application.service` 包中。该类遵循DDD（领域驱动设计）的应用服务职责，协调领域对象完成结算统计业务逻辑。作为佣金结算模块的统计服务，它负责聚合和计算各种结算相关的统计数据，包括待处理结算数量、已完成结算数量、结算金额统计、邀请者统计等。该服务采用TDD（测试驱动开发）方式开发，确保代码质量和业务逻辑的正确性。

## 核心功能 (Core Functionality)
*   **结算统计聚合**: 提供结算业务的核心统计数据聚合功能
*   **多维度统计**: 支持按状态、时间、邀请者等多个维度进行统计
*   **实时数据**: 提供实时的结算统计数据，支持管理决策
*   **DDD架构**: 遵循领域驱动设计原则，协调领域对象完成业务逻辑
*   **事务控制**: 使用只读事务确保数据一致性和性能优化
*   **异常处理**: 完善的异常处理机制，确保服务稳定性
*   **日志记录**: 详细的操作日志，便于问题排查和业务监控
*   **值对象封装**: 使用SettlementStatistics值对象封装统计结果

## 接口说明 (Interface Description)

### 主要方法

#### getSettlementStatistics - 获取结算统计数据
*   **方法签名**: `SettlementStatistics getSettlementStatistics()`
*   **事务**: `@Transactional(readOnly = true)`
*   **参数**: 无
*   **返回值**: `SettlementStatistics` - 结算统计值对象
*   **业务逻辑**: 
    *   获取当前月份作为统计基准
    *   查询待处理结算数量（状态为PENDING）
    *   查询本月已完成结算数量（状态为COMPLETED且为当前月）
    *   计算待结算总金额（状态为PENDING的金额汇总）
    *   计算本月已结算总金额（状态为COMPLETED且为当前月的金额汇总）
    *   统计待结算的邀请者数量（去重统计）
    *   创建并返回SettlementStatistics值对象

### 统计维度说明

#### 待处理结算统计
*   **统计项**: `pendingCount` - 待处理结算数量
*   **查询条件**: 状态为"PENDING"的结算记录
*   **业务含义**: 需要管理员处理的结算申请数量
*   **用途**: 管理员工作台待办事项提醒

#### 本月完成结算统计
*   **统计项**: `completedCountThisMonth` - 本月完成结算数量
*   **查询条件**: 状态为"COMPLETED"且结算月份为当前月
*   **业务含义**: 当前月份已完成的结算数量
*   **用途**: 月度结算工作量统计

#### 待结算金额统计
*   **统计项**: `totalPendingAmount` - 待结算总金额
*   **查询条件**: 状态为"PENDING"的结算记录金额汇总
*   **业务含义**: 需要支付的结算金额总计
*   **用途**: 财务预算和资金准备

#### 本月结算金额统计
*   **统计项**: `totalCompletedAmountThisMonth` - 本月结算总金额
*   **查询条件**: 状态为"COMPLETED"且为当前月的金额汇总
*   **业务含义**: 当前月份已支付的结算金额
*   **用途**: 月度财务报表和成本分析

#### 邀请者统计
*   **统计项**: `inviterCount` - 待结算邀请者数量
*   **查询条件**: 状态为"PENDING"的结算记录中不重复的邀请者数量
*   **业务含义**: 有待结算佣金的邀请者人数
*   **用途**: 用户活跃度和推广效果分析

## 使用示例 (Usage Examples)

```java
// 1. 控制器层调用示例
@RestController
@RequestMapping("/api/v1/admin/settlement")
public class SettlementStatisticsController {
    
    @Autowired
    private SettlementStatisticsService statisticsService;
    
    // 获取结算统计数据
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('admin')")
    public Result<SettlementStatistics> getSettlementStatistics() {
        SettlementStatistics statistics = statisticsService.getSettlementStatistics();
        return Result.success("获取结算统计成功", statistics);
    }
    
    // 管理员工作台数据
    @GetMapping("/dashboard")
    @PreAuthorize("hasAuthority('admin')")
    public Result<AdminDashboardData> getAdminDashboard() {
        SettlementStatistics statistics = statisticsService.getSettlementStatistics();
        
        AdminDashboardData dashboard = new AdminDashboardData();
        dashboard.setPendingSettlements(statistics.getPendingCount());
        dashboard.setPendingAmount(statistics.getTotalPendingAmount());
        dashboard.setMonthlyCompleted(statistics.getCompletedCountThisMonth());
        dashboard.setMonthlyAmount(statistics.getTotalCompletedAmountThisMonth());
        dashboard.setActiveInviters(statistics.getInviterCount());
        
        return Result.success(dashboard);
    }
}

// 2. 前端JavaScript调用示例
const SettlementStatisticsAPI = {
    // 获取结算统计数据
    async getStatistics() {
        const response = await fetch('/api/v1/admin/settlement/statistics', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + adminToken,
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    },
    
    // 渲染统计数据到页面
    renderStatistics(statistics) {
        document.getElementById('pending-count').textContent = statistics.pendingCount;
        document.getElementById('completed-count').textContent = statistics.completedCountThisMonth;
        document.getElementById('pending-amount').textContent = 
            '¥' + statistics.totalPendingAmount.toLocaleString();
        document.getElementById('completed-amount').textContent = 
            '¥' + statistics.totalCompletedAmountThisMonth.toLocaleString();
        document.getElementById('inviter-count').textContent = statistics.inviterCount;
        
        // 更新进度条
        const completionRate = statistics.completedCountThisMonth / 
            (statistics.pendingCount + statistics.completedCountThisMonth) * 100;
        document.getElementById('completion-progress').style.width = completionRate + '%';
    }
};

// 3. 定时任务集成示例
@Component
public class SettlementStatisticsScheduledTasks {
    
    @Autowired
    private SettlementStatisticsService statisticsService;
    
    @Autowired
    private NotificationService notificationService;
    
    // 每日统计报告
    @Scheduled(cron = "0 0 9 * * ?") // 每天早上9点执行
    public void generateDailyReport() {
        log.info("开始生成每日结算统计报告");
        
        try {
            SettlementStatistics statistics = statisticsService.getSettlementStatistics();
            
            // 生成报告内容
            String reportContent = buildDailyReport(statistics);
            
            // 发送给管理员
            notificationService.sendAdminNotification(
                "每日结算统计报告", 
                reportContent,
                NotificationType.DAILY_REPORT
            );
            
            // 检查是否需要告警
            checkAndSendAlerts(statistics);
            
        } catch (Exception e) {
            log.error("生成每日结算统计报告失败", e);
        }
    }
    
    private void checkAndSendAlerts(SettlementStatistics statistics) {
        // 待处理数量过多告警
        if (statistics.getPendingCount() > 100) {
            notificationService.sendAdminAlert(
                "结算待处理数量告警",
                String.format("当前待处理结算数量: %d，请及时处理", statistics.getPendingCount())
            );
        }
        
        // 待结算金额过大告警
        if (statistics.getTotalPendingAmount().compareTo(new BigDecimal("100000")) > 0) {
            notificationService.sendFinanceAlert(
                "结算金额告警",
                String.format("当前待结算金额: ¥%s，请确保资金充足", 
                             statistics.getTotalPendingAmount().toPlainString())
            );
        }
    }
}

// 4. 业务服务集成示例
@Service
public class SettlementManagementService {
    
    @Autowired
    private SettlementStatisticsService statisticsService;
    
    @Autowired
    private CommissionSettlementService settlementService;
    
    // 批量处理结算
    public BatchProcessResult batchProcessSettlements(List<Long> settlementIds, Long adminId) {
        // 获取处理前统计
        SettlementStatistics beforeStats = statisticsService.getSettlementStatistics();
        
        BatchProcessResult result = new BatchProcessResult();
        int successCount = 0;
        int failCount = 0;
        
        for (Long settlementId : settlementIds) {
            try {
                settlementService.processSettlement(settlementId, adminId);
                successCount++;
            } catch (Exception e) {
                log.error("处理结算失败，结算ID: {}", settlementId, e);
                failCount++;
            }
        }
        
        // 获取处理后统计
        SettlementStatistics afterStats = statisticsService.getSettlementStatistics();
        
        result.setSuccessCount(successCount);
        result.setFailCount(failCount);
        result.setBeforeStats(beforeStats);
        result.setAfterStats(afterStats);
        
        return result;
    }
    
    // 结算效率分析
    public SettlementEfficiencyReport analyzeSettlementEfficiency() {
        SettlementStatistics currentStats = statisticsService.getSettlementStatistics();
        
        // 计算处理效率指标
        BigDecimal avgProcessingAmount = currentStats.getTotalCompletedAmountThisMonth()
            .divide(new BigDecimal(currentStats.getCompletedCountThisMonth()), 2, RoundingMode.HALF_UP);
        
        double processingRate = currentStats.getCompletedCountThisMonth().doubleValue() /
            (currentStats.getPendingCount() + currentStats.getCompletedCountThisMonth()) * 100;
        
        SettlementEfficiencyReport report = new SettlementEfficiencyReport();
        report.setCurrentStats(currentStats);
        report.setAvgProcessingAmount(avgProcessingAmount);
        report.setProcessingRate(processingRate);
        report.setGeneratedAt(LocalDateTime.now());
        
        return report;
    }
}

// 5. 缓存集成示例
@Service
public class CachedSettlementStatisticsService {
    
    @Autowired
    private SettlementStatisticsService statisticsService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CACHE_KEY = "settlement:statistics";
    private static final Duration CACHE_TTL = Duration.ofMinutes(5);
    
    // 带缓存的统计查询
    public SettlementStatistics getSettlementStatisticsWithCache() {
        // 尝试从缓存获取
        SettlementStatistics cached = (SettlementStatistics) redisTemplate.opsForValue().get(CACHE_KEY);
        if (cached != null) {
            log.debug("从缓存获取结算统计数据");
            return cached;
        }
        
        // 缓存未命中，查询数据库
        SettlementStatistics statistics = statisticsService.getSettlementStatistics();
        
        // 存入缓存
        redisTemplate.opsForValue().set(CACHE_KEY, statistics, CACHE_TTL);
        log.debug("结算统计数据已缓存");
        
        return statistics;
    }
    
    // 清除缓存
    public void clearStatisticsCache() {
        redisTemplate.delete(CACHE_KEY);
        log.info("结算统计缓存已清除");
    }
    
    // 结算处理后清除缓存
    @EventListener
    public void handleSettlementProcessed(SettlementProcessedEvent event) {
        clearStatisticsCache();
        log.info("结算处理完成，已清除统计缓存");
    }
}
```

## 注意事项 (Notes)
*   **只读事务**: 使用@Transactional(readOnly = true)优化查询性能，避免不必要的事务开销
*   **DDD架构**: 遵循领域驱动设计原则，协调领域对象而不直接操作数据
*   **值对象封装**: 使用SettlementStatistics值对象封装统计结果，确保数据完整性
*   **异常处理**: 统一的异常处理机制，将底层异常转换为业务异常
*   **日志记录**: 详细的操作日志，包括统计结果的关键数据
*   **性能考虑**: 统计查询可能涉及大量数据，需要考虑数据库性能优化
*   **缓存策略**: 统计数据变化不频繁，建议使用缓存提高查询性能
*   **时间处理**: 使用YearMonth处理月份统计，需要注意时区问题
*   **数据一致性**: 统计数据的一致性依赖于底层数据的事务控制
*   **监控告警**: 统计异常或数据异常时需要有相应的监控告警
*   **业务规则**: 不同状态的结算记录有不同的业务含义，需要准确理解
*   **扩展性**: 新增统计维度时需要修改值对象和查询逻辑
*   **测试覆盖**: 遵循TDD原则，确保有完整的单元测试覆盖
*   **数据库索引**: 统计查询涉及的字段需要建立适当的索引
*   **并发安全**: 多个管理员同时查看统计数据时需要考虑数据一致性
