package com.purchase.commission.settlement.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.purchase.commission.settlement.domain.entity.CommissionSettlement;
import com.purchase.commission.settlement.domain.repository.CommissionSettlementRepository;
import com.purchase.commission.settlement.infrastructure.mapper.CommissionSettlementMapper;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 仓储实现类 - 基础设施层
 * 实现领域仓储接口，使用MyBatis-Plus进行数据访问
 */
@Repository
public class CommissionSettlementRepositoryImpl implements CommissionSettlementRepository {
    
    private final CommissionSettlementMapper settlementMapper;
    
    public CommissionSettlementRepositoryImpl(CommissionSettlementMapper settlementMapper) {
        this.settlementMapper = settlementMapper;
    }
    
    @Override
    public CommissionSettlement save(CommissionSettlement settlement) {
        if (settlement.getId() == null) {
            settlementMapper.insert(settlement);
        } else {
            settlementMapper.updateById(settlement);
        }
        return settlement;
    }
    
    @Override
    public Optional<CommissionSettlement> findById(Long id) {
        CommissionSettlement settlement = settlementMapper.selectById(id);
        return Optional.ofNullable(settlement);
    }
    
    @Override
    public Optional<CommissionSettlement> findByInviterIdAndMonthKey(Long inviterId, String monthKey) {
        CommissionSettlement settlement = settlementMapper.selectByInviterIdAndMonthKey(inviterId, monthKey);
        return Optional.ofNullable(settlement);
    }
    
    @Override
    public boolean existsByInviterIdAndMonthKey(Long inviterId, String monthKey) {
        return settlementMapper.existsByInviterIdAndMonthKey(inviterId, monthKey);
    }
    
    @Override
    public org.springframework.data.domain.Page<CommissionSettlement> findAll(Pageable pageable) {
        IPage<CommissionSettlement> page = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        LambdaQueryWrapper<CommissionSettlement> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionSettlement::getDeleted, false)
               .orderByDesc(CommissionSettlement::getCreatedAt);
        
        IPage<CommissionSettlement> result = settlementMapper.selectPage(page, wrapper);
        
        return new PageImpl<>(result.getRecords(), pageable, result.getTotal());
    }
    
    @Override
    @Transactional
    public List<CommissionSettlement> saveAll(List<CommissionSettlement> settlements) {
        settlements.forEach(this::save);
        return settlements;
    }
}
