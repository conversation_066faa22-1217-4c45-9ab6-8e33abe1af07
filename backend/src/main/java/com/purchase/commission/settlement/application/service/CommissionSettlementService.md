# CommissionSettlementService 佣金结算应用服务文档

## 文件概述

`CommissionSettlementService` 是佣金结算的应用服务类，负责协调领域对象完成佣金结算的业务用例。该服务采用DDD（领域驱动设计）架构，通过应用服务层协调领域对象、仓储、事件发布等组件，实现复杂的佣金结算业务逻辑。

## 核心功能

### 主要职责
- **业务用例协调**: 协调多个领域对象完成复杂的业务用例
- **结算预览**: 提供结算前的预览功能，让用户确认结算信息
- **结算创建**: 执行实际的结算创建操作
- **状态管理**: 管理结算的各种状态变更
- **事件发布**: 发布领域事件通知其他模块

### 业务特点
- 采用DDD架构模式
- 支持事务管理和回滚
- 集成事件驱动架构
- 提供完整的结算生命周期管理
- 支持复杂的佣金计算逻辑

## 接口说明

### 依赖注入

#### 核心依赖
```java
private final CommissionSettlementRepository settlementRepository;
private final SettlementMonthlyCommissionSummaryMapper summaryMapper;
private final ApplicationEventPublisher eventPublisher;
```
- **CommissionSettlementRepository**: 佣金结算领域仓储
- **SettlementMonthlyCommissionSummaryMapper**: 月度佣金汇总数据访问
- **ApplicationEventPublisher**: Spring事件发布器

### 核心业务方法

#### previewSettlement(Long inviterId, String monthKey)
```java
@Transactional(readOnly = true)
public SettlementPreviewDTO previewSettlement(Long inviterId, String monthKey)
```
- **功能**: 结算预览，生成前预览结算信息
- **事务**: 只读事务，不修改数据
- **参数**:
  - `inviterId`: 邀请人ID
  - `monthKey`: 月份键值（格式：YYYY-MM）
- **返回值**: 结算预览DTO，包含预计结算金额、佣金明细等
- **业务逻辑**:
  1. 获取月度汇总数据
  2. 计算预计结算金额
  3. 检查是否已存在结算记录
  4. 返回预览信息

#### createSettlement(CreateSettlementCommand command)
```java
@Transactional
public CommissionSettlement createSettlement(CreateSettlementCommand command)
```
- **功能**: 创建佣金结算记录
- **事务**: 写事务，确保数据一致性
- **参数**: `command`: 创建结算命令对象
- **返回值**: 创建的佣金结算实体
- **业务逻辑**:
  1. 验证命令参数的有效性
  2. 检查重复结算
  3. 创建结算领域对象
  4. 保存到仓储
  5. 发布结算创建事件

#### getSettlementsByInviter(Long inviterId, Integer page, Integer size)
```java
@Transactional(readOnly = true)
public IPage<CommissionSettlement> getSettlementsByInviter(Long inviterId, Integer page, Integer size)
```
- **功能**: 获取邀请人的结算记录分页列表
- **事务**: 只读事务
- **参数**:
  - `inviterId`: 邀请人ID
  - `page`: 页码
  - `size`: 每页大小
- **返回值**: 分页的结算记录列表

#### updateSettlementStatus(Long settlementId, String newStatus)
```java
@Transactional
public void updateSettlementStatus(Long settlementId, String newStatus)
```
- **功能**: 更新结算状态
- **事务**: 写事务
- **参数**:
  - `settlementId`: 结算ID
  - `newStatus`: 新状态
- **业务逻辑**:
  1. 获取结算实体
  2. 验证状态流转的合法性
  3. 更新状态
  4. 发布状态变更事件

#### uploadReceiptFile(Long settlementId, ReceiptFile receiptFile)
```java
@Transactional
public void uploadReceiptFile(Long settlementId, ReceiptFile receiptFile)
```
- **功能**: 上传结算凭证文件
- **事务**: 写事务
- **参数**:
  - `settlementId`: 结算ID
  - `receiptFile`: 凭证文件值对象
- **业务逻辑**:
  1. 验证文件格式和大小
  2. 更新结算记录的凭证信息
  3. 发布凭证上传事件

## 使用示例

### 结算预览使用
```java
@RestController
@RequestMapping("/api/commission/settlements")
public class CommissionSettlementController {
    
    @Autowired
    private CommissionSettlementService settlementService;
    
    @GetMapping("/preview")
    public ApiResponse<SettlementPreviewDTO> previewSettlement(
            @RequestParam Long inviterId,
            @RequestParam String monthKey) {
        
        try {
            SettlementPreviewDTO preview = settlementService.previewSettlement(inviterId, monthKey);
            return ApiResponse.success(preview);
        } catch (BusinessException e) {
            return ApiResponse.error(e.getMessage());
        }
    }
}
```

### 创建结算使用
```java
@Service
public class SettlementBusinessService {
    
    @Autowired
    private CommissionSettlementService settlementService;
    
    public CommissionSettlement processMonthlySettlement(Long inviterId, String monthKey) {
        // 1. 先预览结算信息
        SettlementPreviewDTO preview = settlementService.previewSettlement(inviterId, monthKey);
        
        if (preview.getTotalCommissionAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("本月无可结算佣金");
        }
        
        // 2. 构建创建命令
        CreateSettlementCommand command = CreateSettlementCommand.builder()
            .inviterId(inviterId)
            .monthKey(monthKey)
            .totalCommissionAmount(preview.getTotalCommissionAmount())
            .settlementAmount(preview.getSettlementAmount())
            .accountInfo(preview.getAccountInfo())
            .build();
        
        // 3. 创建结算
        CommissionSettlement settlement = settlementService.createSettlement(command);
        
        log.info("月度结算创建成功: inviterId={}, monthKey={}, settlementId={}", 
                inviterId, monthKey, settlement.getId());
        
        return settlement;
    }
}
```

### 状态管理使用
```java
@Service
public class SettlementStatusService {
    
    @Autowired
    private CommissionSettlementService settlementService;
    
    public void approveSettlement(Long settlementId) {
        try {
            settlementService.updateSettlementStatus(settlementId, "APPROVED");
            log.info("结算审核通过: settlementId={}", settlementId);
        } catch (Exception e) {
            log.error("结算审核失败: settlementId={}", settlementId, e);
            throw new BusinessException("结算审核失败: " + e.getMessage());
        }
    }
    
    public void rejectSettlement(Long settlementId, String reason) {
        try {
            settlementService.updateSettlementStatus(settlementId, "REJECTED");
            // 可以在这里记录拒绝原因
            log.info("结算审核拒绝: settlementId={}, reason={}", settlementId, reason);
        } catch (Exception e) {
            log.error("结算拒绝失败: settlementId={}", settlementId, e);
            throw new BusinessException("结算拒绝失败: " + e.getMessage());
        }
    }
    
    public void completeSettlement(Long settlementId) {
        try {
            settlementService.updateSettlementStatus(settlementId, "COMPLETED");
            log.info("结算完成: settlementId={}", settlementId);
        } catch (Exception e) {
            log.error("结算完成失败: settlementId={}", settlementId, e);
            throw new BusinessException("结算完成失败: " + e.getMessage());
        }
    }
}
```

### 事件处理使用
```java
@Component
public class SettlementEventHandler {
    
    @EventListener
    @Async
    public void handleSettlementCreated(SettlementCreatedEvent event) {
        log.info("处理结算创建事件: settlementId={}", event.getSettlementId());
        
        // 发送通知
        notificationService.notifySettlementCreated(event.getInviterId(), event.getSettlementId());
        
        // 更新统计数据
        statisticsService.updateSettlementStatistics(event.getMonthKey());
    }
    
    @EventListener
    @Async
    public void handleSettlementStatusChanged(SettlementStatusChangedEvent event) {
        log.info("处理结算状态变更事件: settlementId={}, oldStatus={}, newStatus={}", 
                event.getSettlementId(), event.getOldStatus(), event.getNewStatus());
        
        // 发送状态变更通知
        notificationService.notifySettlementStatusChanged(
            event.getInviterId(), 
            event.getSettlementId(), 
            event.getNewStatus()
        );
    }
}
```

### 凭证上传使用
```java
@Service
public class ReceiptFileService {
    
    @Autowired
    private CommissionSettlementService settlementService;
    
    @Autowired
    private FileStorageService fileStorageService;
    
    public void uploadSettlementReceipt(Long settlementId, MultipartFile file) {
        // 1. 验证文件
        validateReceiptFile(file);
        
        // 2. 上传文件到存储服务
        String fileUrl = fileStorageService.uploadFile(file, "settlement-receipts");
        
        // 3. 创建凭证文件值对象
        ReceiptFile receiptFile = ReceiptFile.builder()
            .fileName(file.getOriginalFilename())
            .fileUrl(fileUrl)
            .fileSize(file.getSize())
            .contentType(file.getContentType())
            .uploadTime(LocalDateTime.now())
            .build();
        
        // 4. 更新结算记录
        settlementService.uploadReceiptFile(settlementId, receiptFile);
        
        log.info("结算凭证上传成功: settlementId={}, fileName={}", 
                settlementId, file.getOriginalFilename());
    }
    
    private void validateReceiptFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }
        
        if (file.getSize() > 10 * 1024 * 1024) { // 10MB
            throw new BusinessException("文件大小不能超过10MB");
        }
        
        String contentType = file.getContentType();
        if (!Arrays.asList("image/jpeg", "image/png", "application/pdf").contains(contentType)) {
            throw new BusinessException("只支持JPG、PNG、PDF格式的文件");
        }
    }
}
```

## 注意事项

### DDD架构
1. **领域驱动**: 以业务领域为核心，技术服务于业务
2. **分层清晰**: 应用层、领域层、基础设施层职责明确
3. **聚合根**: 通过聚合根维护数据一致性
4. **值对象**: 使用值对象封装业务概念

### 事务管理
1. **事务边界**: 合理设置事务边界，避免长事务
2. **只读事务**: 查询操作使用只读事务提高性能
3. **异常回滚**: 确保业务异常时的数据回滚
4. **事务传播**: 正确设置事务传播行为

### 事件驱动
1. **异步处理**: 使用@Async进行异步事件处理
2. **事件解耦**: 通过事件实现模块间的解耦
3. **事件顺序**: 注意事件处理的顺序和依赖关系
4. **异常处理**: 事件处理失败的异常处理机制

### 性能优化
1. **查询优化**: 优化数据库查询性能
2. **缓存策略**: 合理使用缓存提高性能
3. **分页查询**: 大数据量查询使用分页
4. **批量处理**: 大量数据操作使用批量处理

### 安全考虑
1. **权限验证**: 确保用户只能操作有权限的数据
2. **数据验证**: 严格验证输入数据的有效性
3. **敏感信息**: 保护敏感的财务信息
4. **操作日志**: 记录重要的业务操作日志
