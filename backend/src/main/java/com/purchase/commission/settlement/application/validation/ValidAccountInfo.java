package com.purchase.commission.settlement.application.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 账户信息验证注解
 * 
 * 验证 accountInfo 字段的 JSON 格式和业务规则
 * 
 * 验证规则：
 * 1. 必须是有效的 JSON 格式
 * 2. 必须包含 settlementMethod 字段
 * 3. 必须包含 accountHolder 字段
 * 4. 根据 settlementMethod 验证对应的必填字段：
 *    - bank_transfer: bankName, accountNumber, branchName
 *    - alipay: alipayAccount
 *    - wechat: wechatAccount
 */
@Documented
@Constraint(validatedBy = AccountInfoValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidAccountInfo {
    
    String message() default "账户信息格式不正确";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}
