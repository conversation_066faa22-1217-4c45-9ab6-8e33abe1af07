# CommissionSettlement 佣金结算聚合根文档

## 文件概述

`CommissionSettlement` 是佣金结算的聚合根实体，采用DDD（领域驱动设计）架构模式。该实体封装了佣金结算的完整业务逻辑和数据，包括结算金额计算、状态管理、凭证处理等核心功能，是佣金结算领域的核心业务对象。

## 核心功能

### 主要职责
- **聚合根**: 作为佣金结算聚合的根实体，维护数据一致性
- **业务逻辑**: 封装佣金结算的核心业务规则和计算逻辑
- **状态管理**: 管理结算的完整生命周期状态
- **数据完整性**: 确保结算数据的完整性和一致性
- **领域行为**: 提供丰富的领域行为方法

### 业务特点
- 采用DDD聚合根模式
- 封装复杂的业务逻辑
- 支持多种结算方式
- 完整的状态流转控制
- 集成凭证文件管理

## 接口说明

### 基础属性字段

#### id
- **类型**: Long
- **注解**: @TableId(value = "id", type = IdType.AUTO)
- **描述**: 结算单据主键ID，自动生成
- **用途**: 实体的唯一标识

#### settlementNumber
- **类型**: String
- **注解**: @TableField("settlement_number")
- **描述**: 结算编号
- **用途**: 业务可见的结算单据编号
- **格式**: 通常包含日期和序号

#### inviterId
- **类型**: Long
- **注解**: @TableField("inviter_id")
- **描述**: 邀请人ID
- **用途**: 标识结算的邀请人

#### monthKey
- **类型**: String
- **注解**: @TableField("month_key")
- **描述**: 月份键值，格式为YYYY-MM
- **用途**: 标识结算的月份
- **示例**: "2024-01", "2024-12"

### 金额相关字段

#### totalCommission
- **类型**: BigDecimal
- **注解**: @TableField("total_commission")
- **描述**: 总佣金金额
- **用途**: 该月份邀请人获得的总佣金

#### monthlyBonus
- **类型**: BigDecimal
- **注解**: @TableField("monthly_bonus")
- **描述**: 月度奖金
- **用途**: 根据业绩计算的额外奖金

#### totalAmount
- **类型**: BigDecimal
- **注解**: @TableField("total_amount")
- **描述**: 结算总金额
- **用途**: 总佣金 + 月度奖金的总和

### 结算方式字段

#### settlementMethod
- **类型**: String
- **注解**: @TableField("settlement_method")
- **描述**: 结算方式
- **可选值**: bank_transfer(银行转账), alipay(支付宝), wechat(微信)
- **用途**: 指定结算的支付方式

#### accountInfo
- **类型**: String
- **注解**: @TableField("account_info")
- **描述**: 账户信息
- **用途**: 存储结算账户的详细信息（JSON格式）

### 状态和时间字段

#### status
- **类型**: String
- **注解**: @TableField("settlement_status")
- **描述**: 结算状态
- **可选值**: PENDING(待处理), APPROVED(已审核), PAID(已支付), REJECTED(已拒绝), COMPLETED(已完成)
- **默认值**: COMPLETED

#### settlementTime
- **类型**: LocalDateTime
- **注解**: @TableField("settlement_time")
- **描述**: 结算时间
- **用途**: 记录结算完成的时间

#### createdAt / updatedAt
- **类型**: LocalDateTime
- **注解**: @TableField(fill = FieldFill.INSERT) / @TableField(fill = FieldFill.INSERT_UPDATE)
- **描述**: 创建时间和更新时间
- **用途**: 记录实体的时间戳

### 凭证文件字段

#### receiptFiles
- **类型**: List<ReceiptFile>
- **注解**: @TableField(value = "receipt_files", typeHandler = ReceiptFileListTypeHandler.class)
- **描述**: 结算凭证文件列表
- **用途**: 存储结算相关的凭证文件信息

## 使用示例

### 创建结算单据
```java
@Service
public class CommissionSettlementDomainService {
    
    public CommissionSettlement createSettlement(CreateSettlementCommand command) {
        // 1. 创建结算实体
        CommissionSettlement settlement = new CommissionSettlement();
        
        // 2. 设置基础信息
        settlement.setSettlementNumber(generateSettlementNumber(command.getInviterId(), command.getMonthKey()));
        settlement.setInviterId(command.getInviterId());
        settlement.setMonthKey(command.getMonthKey());
        
        // 3. 设置金额信息
        settlement.setTotalCommission(command.getTotalCommission());
        settlement.setMonthlyBonus(command.getMonthlyBonus());
        settlement.calculateTotalAmount(); // 使用领域方法计算总金额
        
        // 4. 设置结算方式
        settlement.setSettlementMethod(command.getSettlementMethod());
        settlement.setAccountInfo(command.getAccountInfo());
        
        // 5. 设置初始状态
        settlement.setStatus("PENDING");
        settlement.setCreatedAt(LocalDateTime.now());
        
        // 6. 验证业务规则
        settlement.validateBusinessRules();
        
        return settlement;
    }
    
    private String generateSettlementNumber(Long inviterId, String monthKey) {
        // 生成格式：SETTLE-{monthKey}-{inviterId}-{sequence}
        String sequence = String.format("%03d", getNextSequence(monthKey));
        return String.format("SETTLE-%s-%d-%s", monthKey, inviterId, sequence);
    }
}
```

### 状态管理
```java
public class CommissionSettlement {
    
    /**
     * 审核通过
     */
    public void approve() {
        if (!"PENDING".equals(this.status)) {
            throw new IllegalStateException("只有待处理状态的结算单据才能审核通过");
        }
        this.status = "APPROVED";
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 审核拒绝
     */
    public void reject(String reason) {
        if (!"PENDING".equals(this.status)) {
            throw new IllegalStateException("只有待处理状态的结算单据才能审核拒绝");
        }
        this.status = "REJECTED";
        this.updatedAt = LocalDateTime.now();
        // 可以记录拒绝原因
    }
    
    /**
     * 标记为已支付
     */
    public void markAsPaid() {
        if (!"APPROVED".equals(this.status)) {
            throw new IllegalStateException("只有已审核的结算单据才能标记为已支付");
        }
        this.status = "PAID";
        this.settlementTime = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 完成结算
     */
    public void complete() {
        if (!"PAID".equals(this.status)) {
            throw new IllegalStateException("只有已支付的结算单据才能完成结算");
        }
        this.status = "COMPLETED";
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查是否可以修改
     */
    public boolean canModify() {
        return "PENDING".equals(this.status);
    }
    
    /**
     * 检查是否可以删除
     */
    public boolean canDelete() {
        return "PENDING".equals(this.status) || "REJECTED".equals(this.status);
    }
}
```

### 金额计算
```java
public class CommissionSettlement {
    
    /**
     * 计算总金额
     */
    public void calculateTotalAmount() {
        if (this.totalCommission == null) {
            this.totalCommission = BigDecimal.ZERO;
        }
        if (this.monthlyBonus == null) {
            this.monthlyBonus = BigDecimal.ZERO;
        }
        
        this.totalAmount = this.totalCommission.add(this.monthlyBonus)
            .setScale(2, RoundingMode.HALF_UP);
    }
    
    /**
     * 计算月度奖金
     */
    public void calculateMonthlyBonus() {
        if (this.totalCommission == null) {
            this.monthlyBonus = BigDecimal.ZERO;
            return;
        }
        
        // 根据佣金金额计算奖金
        if (this.totalCommission.compareTo(new BigDecimal("10000")) >= 0) {
            // 佣金超过10000，给予5%奖金
            this.monthlyBonus = this.totalCommission.multiply(new BigDecimal("0.05"))
                .setScale(2, RoundingMode.HALF_UP);
        } else if (this.totalCommission.compareTo(new BigDecimal("5000")) >= 0) {
            // 佣金超过5000，给予3%奖金
            this.monthlyBonus = this.totalCommission.multiply(new BigDecimal("0.03"))
                .setScale(2, RoundingMode.HALF_UP);
        } else {
            // 无奖金
            this.monthlyBonus = BigDecimal.ZERO;
        }
    }
    
    /**
     * 验证金额的一致性
     */
    public void validateAmountConsistency() {
        BigDecimal calculatedTotal = this.totalCommission.add(this.monthlyBonus)
            .setScale(2, RoundingMode.HALF_UP);
        
        if (calculatedTotal.compareTo(this.totalAmount) != 0) {
            throw new IllegalStateException("结算总金额计算不正确");
        }
    }
}
```

### 凭证文件管理
```java
public class CommissionSettlement {
    
    /**
     * 添加凭证文件
     */
    public void addReceiptFile(ReceiptFile receiptFile) {
        if (this.receiptFiles == null) {
            this.receiptFiles = new ArrayList<>();
        }
        
        // 验证文件格式和大小
        receiptFile.validate();
        
        this.receiptFiles.add(receiptFile);
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 移除凭证文件
     */
    public void removeReceiptFile(String fileName) {
        if (this.receiptFiles != null) {
            this.receiptFiles.removeIf(file -> file.getFileName().equals(fileName));
            this.updatedAt = LocalDateTime.now();
        }
    }
    
    /**
     * 获取凭证文件数量
     */
    public int getReceiptFileCount() {
        return this.receiptFiles != null ? this.receiptFiles.size() : 0;
    }
    
    /**
     * 检查是否有凭证文件
     */
    public boolean hasReceiptFiles() {
        return this.receiptFiles != null && !this.receiptFiles.isEmpty();
    }
}
```

### 业务规则验证
```java
public class CommissionSettlement {
    
    /**
     * 验证业务规则
     */
    public void validateBusinessRules() {
        validateBasicInfo();
        validateAmounts();
        validateSettlementMethod();
        validateStatus();
    }
    
    private void validateBasicInfo() {
        if (this.inviterId == null || this.inviterId <= 0) {
            throw new IllegalArgumentException("邀请人ID不能为空");
        }
        
        if (!StringUtils.hasText(this.monthKey) || !isValidMonthKey(this.monthKey)) {
            throw new IllegalArgumentException("月份键值格式无效");
        }
        
        if (!StringUtils.hasText(this.settlementNumber)) {
            throw new IllegalArgumentException("结算编号不能为空");
        }
    }
    
    private void validateAmounts() {
        if (this.totalCommission == null || this.totalCommission.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("总佣金金额不能为负数");
        }
        
        if (this.monthlyBonus == null || this.monthlyBonus.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("月度奖金不能为负数");
        }
        
        if (this.totalAmount == null || this.totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("结算总金额必须大于0");
        }
        
        validateAmountConsistency();
    }
    
    private void validateSettlementMethod() {
        if (!StringUtils.hasText(this.settlementMethod)) {
            throw new IllegalArgumentException("结算方式不能为空");
        }
        
        List<String> validMethods = Arrays.asList("bank_transfer", "alipay", "wechat");
        if (!validMethods.contains(this.settlementMethod)) {
            throw new IllegalArgumentException("无效的结算方式");
        }
        
        if (!StringUtils.hasText(this.accountInfo)) {
            throw new IllegalArgumentException("账户信息不能为空");
        }
    }
    
    private void validateStatus() {
        if (!StringUtils.hasText(this.status)) {
            throw new IllegalArgumentException("结算状态不能为空");
        }
        
        List<String> validStatuses = Arrays.asList("PENDING", "APPROVED", "PAID", "REJECTED", "COMPLETED");
        if (!validStatuses.contains(this.status)) {
            throw new IllegalArgumentException("无效的结算状态");
        }
    }
    
    private boolean isValidMonthKey(String monthKey) {
        if (monthKey.length() != 7) {
            return false;
        }
        
        try {
            String[] parts = monthKey.split("-");
            if (parts.length != 2) {
                return false;
            }
            
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);
            
            return year >= 2020 && year <= 2030 && month >= 1 && month <= 12;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
```

## 注意事项

### DDD设计原则
1. **聚合根**: 作为聚合的根实体，控制数据一致性
2. **封装性**: 通过方法而不是直接字段访问来修改状态
3. **不变量**: 维护业务不变量和约束条件
4. **领域行为**: 将业务逻辑封装在实体内部

### 数据一致性
1. **事务边界**: 聚合根是事务的边界
2. **状态管理**: 严格控制状态的流转
3. **业务规则**: 确保业务规则的一致性
4. **数据验证**: 在修改时进行数据验证

### 性能考虑
1. **懒加载**: 大对象使用懒加载策略
2. **缓存策略**: 频繁访问的数据考虑缓存
3. **批量操作**: 大量数据操作使用批量处理
4. **索引优化**: 确保查询字段有合适的索引

### 扩展性
1. **开闭原则**: 对扩展开放，对修改关闭
2. **策略模式**: 使用策略模式处理不同的业务规则
3. **事件驱动**: 通过领域事件实现解耦
4. **版本兼容**: 考虑数据结构的版本兼容性
