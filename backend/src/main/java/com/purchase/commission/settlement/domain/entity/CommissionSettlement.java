package com.purchase.commission.settlement.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.purchase.commission.settlement.domain.valueobject.ReceiptFile;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 佣金结算单据聚合根
 * 使用MyBatis-Plus注解进行ORM映射
 */
@TableName("commission_settlement")
public class CommissionSettlement {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("settlement_number")
    private String settlementNumber;
    
    @TableField("inviter_id")
    private Long inviterId;
    
    @TableField("month_key")
    private String monthKey; // 格式：YYYY-MM
    
    @TableField("total_commission")
    private BigDecimal totalCommission;
    
    @TableField("monthly_bonus")
    private BigDecimal monthlyBonus;
    
    @TableField("total_amount")
    private BigDecimal totalAmount;
    
    @TableField("settlement_method")
    private String settlementMethod; // bank_transfer/alipay/wechat (数据库实际值)
    
    @TableField("account_info")
    private String accountInfo;
    
    @TableField("settlement_status")
    private String status; // COMPLETED (与数据库默认值一致)
    
    @TableField("settlement_time")
    private LocalDateTime settlementTime;
    
    @TableField(value = "receipt_files", typeHandler = ReceiptFileListTypeHandler.class)
    private List<ReceiptFile> receiptFiles;
    
    @TableField("notes")
    private String notes;
    
    @TableField("payment_reference")
    private String paymentReference;
    
    // 审计字段
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField("created_by")
    private String createdBy;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableField("updated_by")
    private String updatedBy;
    
    @TableField("completed_by")
    private String completedBy;
    
    @TableField("completed_at")
    private LocalDateTime completedAt;
    
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;
    
    // 私有构造函数，强制使用工厂方法
    private CommissionSettlement() {}
    
    /**
     * 工厂方法：创建结算单据
     */
    public static CommissionSettlement create(Long inviterId, String monthKey,
                                            BigDecimal totalCommission, BigDecimal monthlyBonus,
                                            String settlementMethod, String accountInfo,
                                            List<ReceiptFile> receiptFiles, String notes,
                                            String paymentReference, String createdBy) {
        
        // 业务规则验证
        validateCreateParameters(inviterId, monthKey, totalCommission, monthlyBonus, 
                               receiptFiles, notes, paymentReference);
        
        CommissionSettlement settlement = new CommissionSettlement();
        settlement.settlementNumber = generateSettlementNumber(monthKey);
        settlement.inviterId = inviterId;
        settlement.monthKey = monthKey;
        settlement.totalCommission = totalCommission.setScale(2, RoundingMode.HALF_UP);
        settlement.monthlyBonus = monthlyBonus.setScale(2, RoundingMode.HALF_UP);
        settlement.totalAmount = settlement.totalCommission.add(settlement.monthlyBonus);
        settlement.settlementMethod = settlementMethod; // 支持: bank_transfer/alipay/wechat
        settlement.accountInfo = accountInfo;
        settlement.status = "COMPLETED"; // 生成即完成，与数据库默认值一致
        settlement.settlementTime = LocalDateTime.now();
        settlement.receiptFiles = new ArrayList<>(receiptFiles);
        settlement.notes = notes;
        settlement.paymentReference = paymentReference;
        settlement.createdAt = LocalDateTime.now(); // 手动设置创建时间
        settlement.createdBy = createdBy;
        settlement.updatedAt = LocalDateTime.now(); // 手动设置更新时间
        settlement.updatedBy = createdBy;
        settlement.completedBy = createdBy; // 生成即完成，完成人就是创建人
        settlement.completedAt = LocalDateTime.now(); // 生成即完成，完成时间就是创建时间
        settlement.deleted = false;
        
        return settlement;
    }
    
    /**
     * 业务方法：添加回执文件
     */
    public void addReceiptFile(ReceiptFile receiptFile) {
        if (this.receiptFiles.size() >= 5) {
            throw new IllegalStateException("回执文件数量不能超过5个");
        }
        this.receiptFiles.add(receiptFile);
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 验证方法：是否有回执文件
     */
    public boolean hasReceiptFiles() {
        return receiptFiles != null && !receiptFiles.isEmpty();
    }
    
    /**
     * 验证方法：是否已完成
     */
    public boolean isCompleted() {
        return "COMPLETED".equals(this.status);
    }
    
    /**
     * 静态方法：生成结算编号
     */
    public static String generateSettlementNumber(String monthKey) {
        String prefix = "SETTLE-" + monthKey.replace("-", "") + "-";
        // 使用纳秒时间戳 + 线程ID确保唯一性
        long nanoTime = System.nanoTime();
        long threadId = Thread.currentThread().getId();
        String sequence = String.format("%06d", (nanoTime + threadId) % 1000000);
        return prefix + sequence;
    }
    
    /**
     * 私有验证方法
     */
    private static void validateCreateParameters(Long inviterId, String monthKey,
                                               BigDecimal totalCommission, BigDecimal monthlyBonus,
                                               List<ReceiptFile> receiptFiles, String notes,
                                               String paymentReference) {
        if (inviterId == null || inviterId <= 0) {
            throw new IllegalArgumentException("邀请者ID不能为空且必须大于0");
        }
        if (!StringUtils.hasText(monthKey)) {
            throw new IllegalArgumentException("月份不能为空");
        }
        if (totalCommission == null || totalCommission.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("结算金额必须大于0");
        }
        if (monthlyBonus == null || monthlyBonus.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("月度奖金不能为负数");
        }
        if (receiptFiles == null || receiptFiles.isEmpty()) {
            throw new IllegalArgumentException("转账回执文件不能为空");
        }
        if (receiptFiles.size() > 5) {
            throw new IllegalArgumentException("转账回执文件数量不能超过5个");
        }
        if (!StringUtils.hasText(notes)) {
            throw new IllegalArgumentException("结算备注不能为空");
        }
        if (!StringUtils.hasText(paymentReference)) {
            throw new IllegalArgumentException("支付参考号不能为空");
        }
    }
    
    // getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public String getSettlementNumber() { return settlementNumber; }
    public void setSettlementNumber(String settlementNumber) { this.settlementNumber = settlementNumber; }
    public Long getInviterId() { return inviterId; }
    public void setInviterId(Long inviterId) { this.inviterId = inviterId; }
    public String getMonthKey() { return monthKey; }
    public void setMonthKey(String monthKey) { this.monthKey = monthKey; }
    public BigDecimal getTotalCommission() { return totalCommission; }
    public void setTotalCommission(BigDecimal totalCommission) { this.totalCommission = totalCommission; }
    public BigDecimal getMonthlyBonus() { return monthlyBonus; }
    public void setMonthlyBonus(BigDecimal monthlyBonus) { this.monthlyBonus = monthlyBonus; }
    public BigDecimal getTotalAmount() { return totalAmount; }
    public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
    public String getSettlementMethod() { return settlementMethod; }
    public void setSettlementMethod(String settlementMethod) { this.settlementMethod = settlementMethod; }
    public String getAccountInfo() { return accountInfo; }
    public void setAccountInfo(String accountInfo) { this.accountInfo = accountInfo; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    public LocalDateTime getSettlementTime() { return settlementTime; }
    public void setSettlementTime(LocalDateTime settlementTime) { this.settlementTime = settlementTime; }
    public List<ReceiptFile> getReceiptFiles() { return receiptFiles; }
    public void setReceiptFiles(List<ReceiptFile> receiptFiles) { this.receiptFiles = receiptFiles; }
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    public String getPaymentReference() { return paymentReference; }
    public void setPaymentReference(String paymentReference) { this.paymentReference = paymentReference; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    public String getUpdatedBy() { return updatedBy; }
    public void setUpdatedBy(String updatedBy) { this.updatedBy = updatedBy; }
    public String getCompletedBy() { return completedBy; }
    public void setCompletedBy(String completedBy) { this.completedBy = completedBy; }
    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }
    public Boolean getDeleted() { return deleted; }
    public void setDeleted(Boolean deleted) { this.deleted = deleted; }
}
