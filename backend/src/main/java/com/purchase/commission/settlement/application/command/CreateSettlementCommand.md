# CreateSettlementCommand 创建结算命令文档

## 文件概述

`CreateSettlementCommand` 是创建结算单据的命令对象，采用CQRS（命令查询职责分离）模式设计。该命令对象是不可变的，封装了创建结算单据所需的所有参数，并集成了完整的JSR-303验证注解，确保数据的有效性和完整性。

## 核心功能

### 主要职责
- **命令封装**: 封装创建结算单据的所有必要参数
- **数据验证**: 通过JSR-303注解进行全面的数据验证
- **不可变性**: 确保命令对象的不可变性和线程安全
- **JSON支持**: 支持JSON序列化和反序列化
- **类型安全**: 提供类型安全的参数传递

### 业务特点
- 采用CQRS命令模式
- 不可变对象设计
- 完整的验证规则
- 支持嵌套对象验证
- 标准的JSON处理

## 接口说明

### 核心字段

#### inviterId
- **类型**: Long
- **修饰符**: private final
- **验证**: @NotNull, @Positive
- **描述**: 邀请者ID
- **约束**: 不能为空，必须大于0
- **用途**: 标识结算的邀请人

#### monthKey
- **类型**: String
- **修饰符**: private final
- **验证**: @NotBlank, @Pattern(regexp = "^\\d{4}-(0[1-9]|1[0-2])$")
- **描述**: 月份键值
- **格式**: YYYY-MM（如：2024-01）
- **约束**: 不能为空，必须符合年月格式

#### settlementMethod
- **类型**: String
- **修饰符**: private final
- **验证**: @NotBlank, @Pattern(regexp = "^(bank_transfer|alipay|wechat)$")
- **描述**: 结算方式
- **可选值**: bank_transfer(银行转账), alipay(支付宝), wechat(微信)
- **约束**: 不能为空，必须为指定值之一

#### accountInfo
- **类型**: String
- **修饰符**: private final
- **验证**: @NotBlank, @Size(max = 500), @ValidAccountInfo
- **描述**: 账户信息（JSON格式）
- **约束**: 不能为空，长度不超过500字符，必须为有效的JSON格式

#### receiptFiles
- **类型**: List<ReceiptFileDTO>
- **修饰符**: private final
- **验证**: @NotEmpty, @Size(max = 5), @Valid
- **描述**: 回执文件列表
- **约束**: 不能为空，最多5个文件，每个文件都要通过验证

#### notes
- **类型**: String
- **修饰符**: private final
- **验证**: @NotBlank, @Size(max = 1000)
- **描述**: 结算备注
- **约束**: 不能为空，长度不超过1000字符

#### paymentReference
- **类型**: String
- **修饰符**: private final
- **验证**: @NotBlank, @Size(max = 100)
- **描述**: 支付参考号
- **约束**: 不能为空，长度不超过100字符

#### createdBy
- **类型**: String
- **修饰符**: private final
- **验证**: @NotBlank, @Size(max = 50)
- **描述**: 创建人
- **约束**: 不能为空，长度不超过50字符

### 嵌套对象

#### ReceiptFileDTO
```java
@Getter
@Builder
public static class ReceiptFileDTO {
    @NotBlank(message = "文件URL不能为空")
    @Size(max = 500, message = "文件URL长度不能超过500字符")
    private final String fileUrl;

    @NotBlank(message = "文件描述不能为空")
    @Size(max = 200, message = "文件描述长度不能超过200字符")
    private final String description;
}
```
- **fileUrl**: 文件URL地址，不能为空，长度不超过500字符
- **description**: 文件描述，不能为空，长度不超过200字符

## 使用示例

### 构建命令对象
```java
@Service
public class SettlementCommandService {
    
    public CreateSettlementCommand buildCreateCommand(SettlementRequest request, String currentUser) {
        // 1. 构建回执文件列表
        List<CreateSettlementCommand.ReceiptFileDTO> receiptFiles = request.getReceiptFiles()
            .stream()
            .map(file -> CreateSettlementCommand.ReceiptFileDTO.builder()
                .fileUrl(file.getUrl())
                .description(file.getDescription())
                .build())
            .collect(Collectors.toList());
        
        // 2. 构建账户信息JSON
        String accountInfo = buildAccountInfoJson(request.getAccountInfo());
        
        // 3. 构建命令对象
        return CreateSettlementCommand.builder()
            .inviterId(request.getInviterId())
            .monthKey(request.getMonthKey())
            .settlementMethod(request.getSettlementMethod())
            .accountInfo(accountInfo)
            .receiptFiles(receiptFiles)
            .notes(request.getNotes())
            .paymentReference(generatePaymentReference())
            .createdBy(currentUser)
            .build();
    }
    
    private String buildAccountInfoJson(AccountInfo accountInfo) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(accountInfo);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("账户信息序列化失败", e);
        }
    }
    
    private String generatePaymentReference() {
        return "PAY-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 8);
    }
}
```

### 控制器中使用
```java
@RestController
@RequestMapping("/api/v1/commission/settlements")
public class SettlementController {
    
    @Autowired
    private CommissionSettlementService settlementService;
    
    @PostMapping
    @PreAuthorize("hasAnyAuthority('ADMIN', 'FINANCE')")
    public ResponseEntity<ApiResponse<CommissionSettlement>> createSettlement(
            @Valid @RequestBody CreateSettlementCommand command,
            BindingResult bindingResult) {
        
        // 1. 检查验证结果
        if (bindingResult.hasErrors()) {
            Map<String, String> errors = new HashMap<>();
            for (FieldError error : bindingResult.getFieldErrors()) {
                errors.put(error.getField(), error.getDefaultMessage());
            }
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("参数验证失败", errors));
        }
        
        try {
            // 2. 执行创建命令
            CommissionSettlement settlement = settlementService.createSettlement(command);
            
            return ResponseEntity.ok(ApiResponse.success(settlement));
            
        } catch (BusinessException e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("创建结算失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统内部错误"));
        }
    }
}
```

### 前端使用示例
```javascript
// 创建结算命令
const createSettlementCommand = {
  inviterId: 12345,
  monthKey: "2024-01",
  settlementMethod: "bank_transfer",
  accountInfo: JSON.stringify({
    bankName: "中国工商银行",
    accountNumber: "6222021234567890123",
    accountHolder: "张三",
    branchName: "北京分行"
  }),
  receiptFiles: [
    {
      fileUrl: "https://example.com/receipt1.pdf",
      description: "银行转账凭证"
    },
    {
      fileUrl: "https://example.com/receipt2.jpg",
      description: "收款确认截图"
    }
  ],
  notes: "2024年1月份佣金结算，包含推荐奖金",
  paymentReference: "PAY-********-ABC12345",
  createdBy: "admin"
};

// 发送创建请求
const createSettlement = async (command) => {
  try {
    const response = await fetch('/api/v1/commission/settlements', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(command)
    });
    
    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建结算失败:', error);
    throw error;
  }
};

// React表单组件
const CreateSettlementForm = ({ onSubmit, onCancel }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  
  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      // 构建命令对象
      const command = {
        inviterId: values.inviterId,
        monthKey: values.monthKey,
        settlementMethod: values.settlementMethod,
        accountInfo: JSON.stringify(values.accountInfo),
        receiptFiles: values.receiptFiles.map(file => ({
          fileUrl: file.url,
          description: file.description
        })),
        notes: values.notes,
        paymentReference: values.paymentReference,
        createdBy: getCurrentUser().username
      };
      
      const result = await createSettlement(command);
      message.success('结算创建成功');
      onSubmit(result);
    } catch (error) {
      message.error('创建失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Form form={form} onFinish={handleSubmit} layout="vertical">
      <Form.Item
        name="inviterId"
        label="邀请人ID"
        rules={[
          { required: true, message: '邀请人ID不能为空' },
          { type: 'number', min: 1, message: '邀请人ID必须大于0' }
        ]}
      >
        <InputNumber style={{ width: '100%' }} />
      </Form.Item>
      
      <Form.Item
        name="monthKey"
        label="结算月份"
        rules={[
          { required: true, message: '结算月份不能为空' },
          { pattern: /^\d{4}-(0[1-9]|1[0-2])$/, message: '月份格式必须为YYYY-MM' }
        ]}
      >
        <DatePicker.MonthPicker format="YYYY-MM" style={{ width: '100%' }} />
      </Form.Item>
      
      <Form.Item
        name="settlementMethod"
        label="结算方式"
        rules={[{ required: true, message: '结算方式不能为空' }]}
      >
        <Select>
          <Option value="bank_transfer">银行转账</Option>
          <Option value="alipay">支付宝</Option>
          <Option value="wechat">微信</Option>
        </Select>
      </Form.Item>
      
      <Form.Item
        name="notes"
        label="结算备注"
        rules={[
          { required: true, message: '结算备注不能为空' },
          { max: 1000, message: '备注长度不能超过1000字符' }
        ]}
      >
        <TextArea rows={4} />
      </Form.Item>
      
      <Form.Item>
        <Space>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" htmlType="submit" loading={loading}>
            创建结算
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};
```

### 验证器使用
```java
@Component
public class CreateSettlementCommandValidator {
    
    public void validateCommand(CreateSettlementCommand command) {
        // 1. 基础验证（JSR-303已处理）
        
        // 2. 业务规则验证
        validateBusinessRules(command);
        
        // 3. 账户信息验证
        validateAccountInfo(command.getAccountInfo(), command.getSettlementMethod());
        
        // 4. 回执文件验证
        validateReceiptFiles(command.getReceiptFiles());
    }
    
    private void validateBusinessRules(CreateSettlementCommand command) {
        // 检查是否已存在该月份的结算
        if (settlementExists(command.getInviterId(), command.getMonthKey())) {
            throw new BusinessException("该月份已存在结算记录");
        }
        
        // 检查邀请人是否存在
        if (!userExists(command.getInviterId())) {
            throw new BusinessException("邀请人不存在");
        }
        
        // 检查月份是否在有效范围内
        if (!isValidSettlementMonth(command.getMonthKey())) {
            throw new BusinessException("结算月份超出有效范围");
        }
    }
    
    private void validateAccountInfo(String accountInfo, String settlementMethod) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode accountNode = mapper.readTree(accountInfo);
            
            switch (settlementMethod) {
                case "bank_transfer":
                    validateBankAccount(accountNode);
                    break;
                case "alipay":
                    validateAlipayAccount(accountNode);
                    break;
                case "wechat":
                    validateWechatAccount(accountNode);
                    break;
                default:
                    throw new BusinessException("不支持的结算方式");
            }
        } catch (JsonProcessingException e) {
            throw new BusinessException("账户信息JSON格式错误");
        }
    }
    
    private void validateReceiptFiles(List<CreateSettlementCommand.ReceiptFileDTO> receiptFiles) {
        for (CreateSettlementCommand.ReceiptFileDTO file : receiptFiles) {
            // 验证文件URL是否可访问
            if (!isValidFileUrl(file.getFileUrl())) {
                throw new BusinessException("文件URL无效: " + file.getFileUrl());
            }
            
            // 验证文件类型
            if (!isSupportedFileType(file.getFileUrl())) {
                throw new BusinessException("不支持的文件类型: " + file.getFileUrl());
            }
        }
    }
}
```

## 注意事项

### 不可变性
1. **final字段**: 所有字段都是final，确保对象不可变
2. **线程安全**: 不可变对象天然线程安全
3. **Builder模式**: 使用Builder模式简化对象构建
4. **防御性复制**: 对于集合类型进行防御性复制

### 验证规则
1. **JSR-303**: 使用标准的验证注解
2. **自定义验证**: 实现复杂的业务验证逻辑
3. **错误消息**: 提供清晰的错误消息
4. **国际化**: 支持错误消息的国际化

### JSON处理
1. **序列化**: 支持标准的JSON序列化
2. **反序列化**: 使用@JsonCreator支持反序列化
3. **字段映射**: 使用@JsonProperty明确字段映射
4. **类型安全**: 确保JSON转换的类型安全

### 性能考虑
1. **对象创建**: 不可变对象的创建成本
2. **验证开销**: 复杂验证的性能开销
3. **内存使用**: 注意内存使用效率
4. **缓存友好**: 不可变对象适合缓存
