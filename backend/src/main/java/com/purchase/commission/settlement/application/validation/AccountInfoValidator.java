package com.purchase.commission.settlement.application.validation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 账户信息验证器
 *
 * 验证 accountInfo 字段的 JSON 格式和业务规则
 */
public class AccountInfoValidator implements ConstraintValidator<ValidAccountInfo, String> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 结算方式常量
    private static final String SETTLEMENT_METHOD_BANK = "银行转账";
    private static final String SETTLEMENT_METHOD_ALIPAY = "支付宝";
    private static final String SETTLEMENT_METHOD_WECHAT = "微信支付";

    // 必填字段常量
    private static final String FIELD_SETTLEMENT_METHOD = "settlementMethod";
    private static final String FIELD_ACCOUNT_HOLDER = "accountHolder";
    private static final String FIELD_BANK_NAME = "bankName";
    private static final String FIELD_ACCOUNT_NUMBER = "accountNumber";
    private static final String FIELD_BRANCH_NAME = "branchName";
    private static final String FIELD_ALIPAY_ACCOUNT = "alipayAccount";
    private static final String FIELD_WECHAT_ACCOUNT = "wechatAccount";

    @Override
    public void initialize(ValidAccountInfo constraintAnnotation) {
        // 初始化方法，暂时不需要特殊处理
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // null 值由 @NotBlank 注解处理
        if (value == null) {
            return true;
        }

        // 空字符串验证失败
        if (value.trim().isEmpty()) {
            return false;
        }

        try {
            // 1. 验证 JSON 格式
            JsonNode jsonNode = objectMapper.readTree(value);

            // 2. 验证必填字段：settlementMethod
            if (!jsonNode.has(FIELD_SETTLEMENT_METHOD) ||
                jsonNode.get(FIELD_SETTLEMENT_METHOD).asText().trim().isEmpty()) {
                return false;
            }

            // 3. 验证必填字段：accountHolder
            if (!jsonNode.has(FIELD_ACCOUNT_HOLDER) ||
                jsonNode.get(FIELD_ACCOUNT_HOLDER).asText().trim().isEmpty()) {
                return false;
            }

            // 4. 根据 settlementMethod 验证对应的必填字段
            String settlementMethod = jsonNode.get(FIELD_SETTLEMENT_METHOD).asText();
            return validateBySettlementMethod(jsonNode, settlementMethod);

        } catch (Exception e) {
            // JSON 解析失败
            return false;
        }
    }

    /**
     * 根据结算方式验证对应的必填字段
     */
    private boolean validateBySettlementMethod(JsonNode jsonNode, String settlementMethod) {
        switch (settlementMethod) {
            case SETTLEMENT_METHOD_BANK:
                return validateBankTransfer(jsonNode);
            case SETTLEMENT_METHOD_ALIPAY:
                return validateAlipay(jsonNode);
            case SETTLEMENT_METHOD_WECHAT:
                return validateWechat(jsonNode);
            default:
                // 未知的结算方式
                return false;
        }
    }

    /**
     * 验证银行转账必填字段
     */
    private boolean validateBankTransfer(JsonNode jsonNode) {
        return hasNonEmptyField(jsonNode, FIELD_BANK_NAME) &&
               hasNonEmptyField(jsonNode, FIELD_ACCOUNT_NUMBER) &&
               hasNonEmptyField(jsonNode, FIELD_BRANCH_NAME);
    }

    /**
     * 验证支付宝必填字段
     */
    private boolean validateAlipay(JsonNode jsonNode) {
        return hasNonEmptyField(jsonNode, FIELD_ALIPAY_ACCOUNT);
    }

    /**
     * 验证微信支付必填字段
     */
    private boolean validateWechat(JsonNode jsonNode) {
        return hasNonEmptyField(jsonNode, FIELD_WECHAT_ACCOUNT);
    }

    /**
     * 检查字段是否存在且非空
     */
    private boolean hasNonEmptyField(JsonNode jsonNode, String fieldName) {
        return jsonNode.has(fieldName) && 
               !jsonNode.get(fieldName).asText().trim().isEmpty();
    }
}
