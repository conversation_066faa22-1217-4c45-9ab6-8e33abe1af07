package com.purchase.commission.settlement.controller;

import com.purchase.commission.settlement.controller.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;

/**
 * 佣金结算异常处理器
 * 统一处理控制器层的异常并返回标准格式的响应
 */
@RestControllerAdvice(basePackages = "com.purchase.commission.settlement.controller")
@Slf4j
public class SettlementExceptionHandler {

    /**
     * 处理参数验证异常（@Valid注解触发）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Object>> handleValidationException(
            MethodArgumentNotValidException ex) {
        
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getFieldErrors().forEach(error -> 
            errors.put(error.getField(), error.getDefaultMessage()));
        
        log.warn("参数验证失败: {}", errors);
        return ResponseEntity.badRequest()
            .body(ApiResponse.validationError("参数验证失败", errors));
    }

    /**
     * 处理约束验证异常（@RequestParam验证触发）
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleConstraintViolationException(
            ConstraintViolationException ex) {
        
        Map<String, String> errors = new HashMap<>();
        for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
            String propertyPath = violation.getPropertyPath().toString();
            String message = violation.getMessage();
            errors.put(propertyPath, message);
        }
        
        log.warn("约束验证失败: {}", errors);
        return ResponseEntity.badRequest()
            .body(ApiResponse.validationError("参数验证失败", errors));
    }

    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ApiResponse<Object>> handleMissingParameterException(
            MissingServletRequestParameterException ex) {
        
        String message = String.format("缺少必需的请求参数: %s", ex.getParameterName());
        log.warn("缺少请求参数: {}", ex.getParameterName());
        
        return ResponseEntity.badRequest()
            .body(ApiResponse.error(message));
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Object>> handleTypeMismatchException(
            MethodArgumentTypeMismatchException ex) {
        
        String message = String.format("参数类型不匹配: %s 应该是 %s 类型", 
                                      ex.getName(), ex.getRequiredType().getSimpleName());
        log.warn("参数类型不匹配: {}", message);
        
        return ResponseEntity.badRequest()
            .body(ApiResponse.error(message));
    }

    /**
     * 处理不支持的媒体类型异常
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<ApiResponse<Object>> handleUnsupportedMediaTypeException(
            HttpMediaTypeNotSupportedException ex) {
        
        String message = "不支持的媒体类型: " + ex.getContentType();
        log.warn("不支持的媒体类型: {}", ex.getContentType());
        
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
            .body(ApiResponse.error(message));
    }

    /**
     * 处理JSON格式错误异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ApiResponse<Object>> handleJsonParseException(
            HttpMessageNotReadableException ex) {
        
        String message = "JSON格式错误，请检查请求体格式";
        log.warn("JSON解析失败: {}", ex.getMessage());
        
        return ResponseEntity.badRequest()
            .body(ApiResponse.error(message));
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler({IllegalArgumentException.class, IllegalStateException.class})
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(RuntimeException ex) {
        log.warn("业务异常: {}", ex.getMessage());
        
        return ResponseEntity.badRequest()
            .body(ApiResponse.error(ex.getMessage()));
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGenericException(Exception ex) {
        log.error("系统异常", ex);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(ApiResponse.error("系统内部错误"));
    }
}
