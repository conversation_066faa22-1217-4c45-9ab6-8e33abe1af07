package com.purchase.commission.settlement.domain.entity;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.purchase.commission.settlement.domain.valueobject.ReceiptFile;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.util.StringUtils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * ReceiptFile列表的JSON类型处理器
 * 用于MyBatis-Plus处理JSON字段
 */
public class ReceiptFileListType<PERSON><PERSON><PERSON> extends BaseTypeHandler<List<ReceiptFile>> {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<ReceiptFile> parameter, JdbcType jdbcType) throws SQLException {
        try {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        } catch (JsonProcessingException e) {
            throw new SQLException("Error converting ReceiptFile list to JSON", e);
        }
    }

    @Override
    public List<ReceiptFile> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public List<ReceiptFile> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public List<ReceiptFile> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private List<ReceiptFile> parseJson(String json) throws SQLException {
        if (!StringUtils.hasText(json)) {
            return new ArrayList<>();
        }
        try {
            // 处理可能的双重引用问题
            String cleanJson = json;
            if (json.startsWith("\"") && json.endsWith("\"")) {
                // 如果JSON字符串被引号包围，需要先解析一次
                cleanJson = objectMapper.readValue(json, String.class);
            }

            TypeReference<List<ReceiptFile>> typeRef = new TypeReference<List<ReceiptFile>>() {};
            return objectMapper.readValue(cleanJson, typeRef);
        } catch (JsonProcessingException e) {
            throw new SQLException("Error parsing JSON to ReceiptFile list: " + json, e);
        }
    }
}
