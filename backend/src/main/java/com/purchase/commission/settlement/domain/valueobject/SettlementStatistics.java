package com.purchase.commission.settlement.domain.valueobject;

import lombok.Value;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 结算统计值对象
 * 
 * 按照DDD设计原则，这是一个不可变的值对象，
 * 封装了结算相关的统计数据。
 */
@Value
public class SettlementStatistics {
    
    /**
     * 待处理结算数量
     */
    Long pendingCount;
    
    /**
     * 本月已完成结算数量
     */
    Long completedCountThisMonth;
    
    /**
     * 待结算总金额
     */
    BigDecimal totalPendingAmount;
    
    /**
     * 本月结算总金额
     */
    BigDecimal totalCompletedAmountThisMonth;
    
    /**
     * 涉及邀请者数量（待结算）
     */
    Long inviterCount;
    
    /**
     * 私有构造函数，强制使用工厂方法
     */
    private SettlementStatistics(Long pendingCount, 
                                Long completedCountThisMonth,
                                BigDecimal totalPendingAmount, 
                                BigDecimal totalCompletedAmountThisMonth,
                                Long inviterCount) {
        this.pendingCount = pendingCount;
        this.completedCountThisMonth = completedCountThisMonth;
        this.totalPendingAmount = totalPendingAmount;
        this.totalCompletedAmountThisMonth = totalCompletedAmountThisMonth;
        this.inviterCount = inviterCount;
    }
    
    /**
     * 工厂方法：创建结算统计
     * 
     * @param pendingCount 待处理结算数量
     * @param completedCountThisMonth 本月已完成结算数量
     * @param totalPendingAmount 待结算总金额
     * @param totalCompletedAmountThisMonth 本月结算总金额
     * @param inviterCount 涉及邀请者数量
     * @return 结算统计值对象
     */
    public static SettlementStatistics create(Long pendingCount,
                                            Long completedCountThisMonth,
                                            BigDecimal totalPendingAmount,
                                            BigDecimal totalCompletedAmountThisMonth,
                                            Long inviterCount) {
        // 业务规则验证
        validateParameters(pendingCount, completedCountThisMonth, totalPendingAmount, 
                         totalCompletedAmountThisMonth, inviterCount);
        
        return new SettlementStatistics(
            pendingCount,
            completedCountThisMonth,
            totalPendingAmount != null ? totalPendingAmount : BigDecimal.ZERO,
            totalCompletedAmountThisMonth != null ? totalCompletedAmountThisMonth : BigDecimal.ZERO,
            inviterCount
        );
    }
    
    /**
     * 工厂方法：创建空统计（所有值为零）
     */
    public static SettlementStatistics empty() {
        return new SettlementStatistics(0L, 0L, BigDecimal.ZERO, BigDecimal.ZERO, 0L);
    }
    
    /**
     * 业务方法：是否有待处理的结算
     */
    public boolean hasPendingSettlements() {
        return pendingCount != null && pendingCount > 0;
    }
    
    /**
     * 业务方法：是否有本月完成的结算
     */
    public boolean hasCompletedSettlementsThisMonth() {
        return completedCountThisMonth != null && completedCountThisMonth > 0;
    }
    
    /**
     * 业务方法：获取平均待结算金额
     */
    public BigDecimal getAveragePendingAmount() {
        if (pendingCount == null || pendingCount == 0 || totalPendingAmount == null) {
            return BigDecimal.ZERO;
        }
        return totalPendingAmount.divide(BigDecimal.valueOf(pendingCount), 2, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 参数验证
     */
    private static void validateParameters(Long pendingCount,
                                         Long completedCountThisMonth,
                                         BigDecimal totalPendingAmount,
                                         BigDecimal totalCompletedAmountThisMonth,
                                         Long inviterCount) {
        if (pendingCount == null || pendingCount < 0) {
            throw new IllegalArgumentException("待处理结算数量不能为null或负数");
        }
        if (completedCountThisMonth == null || completedCountThisMonth < 0) {
            throw new IllegalArgumentException("本月已完成结算数量不能为null或负数");
        }
        if (totalPendingAmount != null && totalPendingAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("待结算总金额不能为负数");
        }
        if (totalCompletedAmountThisMonth != null && totalCompletedAmountThisMonth.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("本月结算总金额不能为负数");
        }
        if (inviterCount == null || inviterCount < 0) {
            throw new IllegalArgumentException("邀请者数量不能为null或负数");
        }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SettlementStatistics that = (SettlementStatistics) o;
        return Objects.equals(pendingCount, that.pendingCount) &&
               Objects.equals(completedCountThisMonth, that.completedCountThisMonth) &&
               Objects.equals(totalPendingAmount, that.totalPendingAmount) &&
               Objects.equals(totalCompletedAmountThisMonth, that.totalCompletedAmountThisMonth) &&
               Objects.equals(inviterCount, that.inviterCount);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(pendingCount, completedCountThisMonth, totalPendingAmount, 
                          totalCompletedAmountThisMonth, inviterCount);
    }
}
