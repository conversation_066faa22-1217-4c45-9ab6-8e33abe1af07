package com.purchase.commission.settlement.application.dto;

import com.purchase.commission.settlement.domain.valueobject.SettlementStatistics;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 结算统计DTO
 * 用于API响应的数据传输对象
 */
@Data
public class SettlementStatisticsDTO {
    
    /**
     * 待处理结算数量
     */
    private Long pendingCount;
    
    /**
     * 本月已完成结算数量
     */
    private Long completedCountThisMonth;
    
    /**
     * 待结算总金额
     */
    private BigDecimal totalPendingAmount;
    
    /**
     * 本月结算总金额
     */
    private BigDecimal totalCompletedAmountThisMonth;
    
    /**
     * 涉及邀请者数量（待结算）
     */
    private Long inviterCount;
    
    /**
     * 是否有待处理的结算
     */
    private Boolean hasPendingSettlements;
    
    /**
     * 是否有本月完成的结算
     */
    private Boolean hasCompletedSettlementsThisMonth;
    
    /**
     * 平均待结算金额
     */
    private BigDecimal averagePendingAmount;
    
    /**
     * 从领域值对象转换为DTO
     */
    public static SettlementStatisticsDTO fromValueObject(SettlementStatistics statistics) {
        if (statistics == null) {
            return null;
        }
        
        SettlementStatisticsDTO dto = new SettlementStatisticsDTO();
        dto.setPendingCount(statistics.getPendingCount());
        dto.setCompletedCountThisMonth(statistics.getCompletedCountThisMonth());
        dto.setTotalPendingAmount(statistics.getTotalPendingAmount());
        dto.setTotalCompletedAmountThisMonth(statistics.getTotalCompletedAmountThisMonth());
        dto.setInviterCount(statistics.getInviterCount());
        dto.setHasPendingSettlements(statistics.hasPendingSettlements());
        dto.setHasCompletedSettlementsThisMonth(statistics.hasCompletedSettlementsThisMonth());
        dto.setAveragePendingAmount(statistics.getAveragePendingAmount());
        
        return dto;
    }
}
