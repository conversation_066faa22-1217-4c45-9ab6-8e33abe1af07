package com.purchase.commission.settlement.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.purchase.commission.settlement.domain.entity.CommissionSettlement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * CommissionSettlement Mapper接口
 * 继承MyBatis-Plus的BaseMapper，提供基础CRUD操作
 */
@Mapper
public interface CommissionSettlementMapper extends BaseMapper<CommissionSettlement> {
    
    /**
     * 根据邀请人ID和月份查询结算单据
     */
    @Select("SELECT * FROM commission_settlement WHERE inviter_id = #{inviterId} AND month_key = #{monthKey} AND deleted = 0")
    CommissionSettlement selectByInviterIdAndMonthKey(@Param("inviterId") Long inviterId, 
                                                     @Param("monthKey") String monthKey);
    
    /**
     * 检查结算单据是否存在
     */
    @Select("SELECT COUNT(1) > 0 FROM commission_settlement WHERE inviter_id = #{inviterId} AND month_key = #{monthKey} AND deleted = 0")
    boolean existsByInviterIdAndMonthKey(@Param("inviterId") Long inviterId, 
                                        @Param("monthKey") String monthKey);
}
