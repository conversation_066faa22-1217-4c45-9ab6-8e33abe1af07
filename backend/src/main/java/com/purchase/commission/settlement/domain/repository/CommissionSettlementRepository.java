package com.purchase.commission.settlement.domain.repository;

import com.purchase.commission.settlement.domain.entity.CommissionSettlement;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 结算单据仓储接口 - 领域层
 * 定义领域对象的持久化操作
 */
public interface CommissionSettlementRepository {
    
    /**
     * 保存结算单据
     */
    CommissionSettlement save(CommissionSettlement settlement);
    
    /**
     * 根据ID查找
     */
    Optional<CommissionSettlement> findById(Long id);
    
    /**
     * 根据邀请人和月份查找
     */
    Optional<CommissionSettlement> findByInviterIdAndMonthKey(Long inviterId, String monthKey);
    
    /**
     * 检查是否存在
     */
    boolean existsByInviterIdAndMonthKey(Long inviterId, String monthKey);
    
    /**
     * 分页查询
     */
    Page<CommissionSettlement> findAll(Pageable pageable);
    
    /**
     * 批量保存
     */
    List<CommissionSettlement> saveAll(List<CommissionSettlement> settlements);
}
