# CommissionSettlementController 佣金结算控制器文档

## 文件概述

`CommissionSettlementController` 是佣金结算的REST API控制器，提供结算单据的完整HTTP接口。该控制器采用RESTful设计风格，集成了权限控制、参数验证、异常处理等功能，为前端提供标准化的佣金结算管理API服务。

## 核心功能

### 主要职责
- **API接口**: 提供RESTful风格的佣金结算管理接口
- **权限控制**: 基于Spring Security的细粒度权限验证
- **参数验证**: 使用JSR-303注解进行参数验证
- **异常处理**: 统一的异常处理和错误响应
- **数据转换**: 协调应用服务完成业务操作

### 业务特点
- 支持多角色权限控制
- 提供完整的结算生命周期管理
- 支持分页查询和条件筛选
- 集成统计分析功能
- 标准化的API响应格式

## 接口说明

### 依赖注入

#### 核心服务
```java
private final CommissionSettlementService settlementService;
private final SettlementStatisticsService statisticsService;
```
- **CommissionSettlementService**: 佣金结算应用服务
- **SettlementStatisticsService**: 结算统计服务

### 查询接口

#### GET /api/v1/commission/settlements
```java
@GetMapping
@PreAuthorize("hasAnyAuthority('ADMIN', 'FINANCE', 'INVITER')")
public ResponseEntity<ApiResponse<IPage<CommissionSettlement>>> getSettlements(
    @RequestParam(defaultValue = "1") @Positive Integer page,
    @RequestParam(defaultValue = "10") @Positive Integer size,
    @RequestParam(required = false) Long inviterId,
    @RequestParam(required = false) String status,
    @RequestParam(required = false) String monthKey)
```
- **功能**: 分页查询结算单据列表
- **权限**: 管理员、财务、邀请人
- **参数**:
  - `page`: 页码，默认1，必须为正数
  - `size`: 每页大小，默认10，必须为正数
  - `inviterId`: 邀请人ID（可选）
  - `status`: 结算状态（可选）
  - `monthKey`: 月份键值（可选）
- **返回值**: 分页的结算单据列表

#### GET /api/v1/commission/settlements/{id}
```java
@GetMapping("/{id}")
@PreAuthorize("hasAnyAuthority('ADMIN', 'FINANCE', 'INVITER')")
public ResponseEntity<ApiResponse<CommissionSettlement>> getSettlementById(@PathVariable @NotNull Long id)
```
- **功能**: 根据ID获取结算单据详情
- **权限**: 管理员、财务、邀请人
- **参数**: `id`: 结算单据ID，不能为空
- **返回值**: 结算单据详情

#### GET /api/v1/commission/settlements/preview
```java
@GetMapping("/preview")
@PreAuthorize("hasAnyAuthority('ADMIN', 'FINANCE', 'INVITER')")
public ResponseEntity<ApiResponse<SettlementPreviewDTO>> previewSettlement(
    @RequestParam @NotNull Long inviterId,
    @RequestParam @NotBlank String monthKey)
```
- **功能**: 获取结算预览信息
- **权限**: 管理员、财务、邀请人
- **参数**:
  - `inviterId`: 邀请人ID，不能为空
  - `monthKey`: 月份键值，不能为空
- **返回值**: 结算预览DTO

### 创建接口

#### POST /api/v1/commission/settlements
```java
@PostMapping
@PreAuthorize("hasAnyAuthority('ADMIN', 'FINANCE')")
public ResponseEntity<ApiResponse<CommissionSettlement>> createSettlement(
    @Valid @RequestBody CreateSettlementCommand command,
    BindingResult bindingResult)
```
- **功能**: 创建新的结算单据
- **权限**: 仅管理员、财务
- **参数**: `command`: 创建结算命令对象，需要验证
- **返回值**: 创建成功的结算单据
- **验证**: 使用@Valid进行参数验证

### 更新接口

#### PUT /api/v1/commission/settlements/{id}/status
```java
@PutMapping("/{id}/status")
@PreAuthorize("hasAnyAuthority('ADMIN', 'FINANCE')")
public ResponseEntity<ApiResponse<Void>> updateSettlementStatus(
    @PathVariable @NotNull Long id,
    @RequestParam @NotBlank String status)
```
- **功能**: 更新结算状态
- **权限**: 仅管理员、财务
- **参数**:
  - `id`: 结算单据ID，不能为空
  - `status`: 新状态，不能为空
- **返回值**: 操作结果

#### POST /api/v1/commission/settlements/{id}/receipt
```java
@PostMapping("/{id}/receipt")
@PreAuthorize("hasAnyAuthority('ADMIN', 'FINANCE', 'INVITER')")
public ResponseEntity<ApiResponse<Void>> uploadReceiptFile(
    @PathVariable @NotNull Long id,
    @RequestParam("file") MultipartFile file)
```
- **功能**: 上传结算凭证文件
- **权限**: 管理员、财务、邀请人
- **参数**:
  - `id`: 结算单据ID
  - `file`: 凭证文件
- **返回值**: 上传结果

### 统计接口

#### GET /api/v1/commission/settlements/statistics
```java
@GetMapping("/statistics")
@PreAuthorize("hasAnyAuthority('ADMIN', 'FINANCE')")
public ResponseEntity<ApiResponse<SettlementStatisticsDTO>> getSettlementStatistics(
    @RequestParam(required = false) String startMonth,
    @RequestParam(required = false) String endMonth,
    @RequestParam(required = false) Long inviterId)
```
- **功能**: 获取结算统计数据
- **权限**: 仅管理员、财务
- **参数**:
  - `startMonth`: 开始月份（可选）
  - `endMonth`: 结束月份（可选）
  - `inviterId`: 邀请人ID（可选）
- **返回值**: 结算统计DTO

#### GET /api/v1/commission/settlements/monthly-summary
```java
@GetMapping("/monthly-summary")
@PreAuthorize("hasAnyAuthority('ADMIN', 'FINANCE', 'INVITER')")
public ResponseEntity<ApiResponse<List<MonthlyCommissionSummaryDTO>>> getMonthlyCommissionSummary(
    @RequestParam @NotNull Long inviterId,
    @RequestParam(required = false) String startMonth,
    @RequestParam(required = false) String endMonth)
```
- **功能**: 获取月度佣金汇总
- **权限**: 管理员、财务、邀请人
- **参数**:
  - `inviterId`: 邀请人ID，不能为空
  - `startMonth`: 开始月份（可选）
  - `endMonth`: 结束月份（可选）
- **返回值**: 月度佣金汇总列表

## 使用示例

### 前端API调用示例
```javascript
// 获取结算列表
const getSettlements = async (params = {}) => {
  try {
    const queryParams = new URLSearchParams({
      page: params.page || 1,
      size: params.size || 10,
      ...(params.inviterId && { inviterId: params.inviterId }),
      ...(params.status && { status: params.status }),
      ...(params.monthKey && { monthKey: params.monthKey })
    });
    
    const response = await fetch(`/api/v1/commission/settlements?${queryParams}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取结算列表失败:', error);
    throw error;
  }
};

// 获取结算预览
const getSettlementPreview = async (inviterId, monthKey) => {
  try {
    const response = await fetch(
      `/api/v1/commission/settlements/preview?inviterId=${inviterId}&monthKey=${monthKey}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取结算预览失败:', error);
    throw error;
  }
};

// 创建结算
const createSettlement = async (command) => {
  try {
    const response = await fetch('/api/v1/commission/settlements', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(command)
    });
    
    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建结算失败:', error);
    throw error;
  }
};

// 更新结算状态
const updateSettlementStatus = async (id, status) => {
  try {
    const response = await fetch(
      `/api/v1/commission/settlements/${id}/status?status=${status}`,
      {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('更新结算状态失败:', error);
    throw error;
  }
};
```

### React组件使用示例
```javascript
// 结算管理组件
const SettlementManagement = () => {
  const [settlements, setSettlements] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({});
  
  const loadSettlements = async (page = 1, size = 10, filterParams = {}) => {
    setLoading(true);
    try {
      const params = { page, size, ...filterParams };
      const data = await getSettlements(params);
      
      setSettlements(data.records);
      setPagination({
        current: data.current,
        pageSize: data.size,
        total: data.total
      });
    } catch (error) {
      message.error('加载结算列表失败');
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    loadSettlements();
  }, []);
  
  const handleStatusChange = async (id, status) => {
    try {
      await updateSettlementStatus(id, status);
      message.success('状态更新成功');
      loadSettlements(pagination.current, pagination.pageSize, filters);
    } catch (error) {
      message.error('状态更新失败');
    }
  };
  
  const columns = [
    {
      title: '结算编号',
      dataIndex: 'settlementNumber',
      key: 'settlementNumber'
    },
    {
      title: '邀请人',
      dataIndex: 'inviterName',
      key: 'inviterName'
    },
    {
      title: '结算月份',
      dataIndex: 'monthKey',
      key: 'monthKey'
    },
    {
      title: '结算金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => `¥${amount.toLocaleString()}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Select
          value={status}
          onChange={(value) => handleStatusChange(record.id, value)}
          style={{ width: 120 }}
        >
          <Option value="PENDING">待处理</Option>
          <Option value="APPROVED">已审核</Option>
          <Option value="PAID">已支付</Option>
          <Option value="REJECTED">已拒绝</Option>
        </Select>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button size="small" onClick={() => viewDetail(record.id)}>
            查看
          </Button>
          <Button size="small" onClick={() => downloadReceipt(record.id)}>
            下载凭证
          </Button>
        </Space>
      )
    }
  ];
  
  return (
    <div>
      <div className="filter-bar">
        <Space>
          <Select
            placeholder="选择状态"
            allowClear
            onChange={(value) => setFilters({...filters, status: value})}
          >
            <Option value="PENDING">待处理</Option>
            <Option value="APPROVED">已审核</Option>
            <Option value="PAID">已支付</Option>
            <Option value="REJECTED">已拒绝</Option>
          </Select>
          <DatePicker.MonthPicker
            placeholder="选择月份"
            onChange={(date, dateString) => setFilters({...filters, monthKey: dateString})}
          />
          <Button onClick={() => loadSettlements(1, pagination.pageSize, filters)}>
            查询
          </Button>
        </Space>
      </div>
      
      <Table
        dataSource={settlements}
        columns={columns}
        loading={loading}
        pagination={{
          ...pagination,
          onChange: (page, size) => loadSettlements(page, size, filters)
        }}
        rowKey="id"
      />
    </div>
  );
};
```

### 权限控制示例
```java
// 自定义权限验证
@Component
public class SettlementPermissionEvaluator {
    
    public boolean canAccessSettlement(Authentication authentication, Long settlementId) {
        String role = authentication.getAuthorities().iterator().next().getAuthority();
        Long userId = Long.valueOf(authentication.getName());
        
        // 管理员和财务可以访问所有结算
        if ("ADMIN".equals(role) || "FINANCE".equals(role)) {
            return true;
        }
        
        // 邀请人只能访问自己的结算
        if ("INVITER".equals(role)) {
            CommissionSettlement settlement = settlementService.getById(settlementId);
            return settlement != null && Objects.equals(settlement.getInviterId(), userId);
        }
        
        return false;
    }
}
```

## 注意事项

### 权限控制
1. **细粒度权限**: 使用@PreAuthorize进行方法级权限控制
2. **角色分离**: 不同角色有不同的操作权限
3. **数据权限**: 确保用户只能访问有权限的数据
4. **权限验证**: 每个接口都要进行权限验证

### 参数验证
1. **JSR-303**: 使用标准的验证注解
2. **自定义验证**: 实现复杂的业务验证逻辑
3. **错误处理**: 统一处理验证错误
4. **友好提示**: 提供用户友好的错误信息

### 异常处理
1. **统一响应**: 使用统一的API响应格式
2. **错误码**: 定义明确的错误码和消息
3. **日志记录**: 记录重要的操作和错误信息
4. **降级处理**: 提供合理的降级处理策略

### 性能优化
1. **分页查询**: 大数据量查询使用分页
2. **缓存策略**: 频繁查询的数据考虑缓存
3. **查询优化**: 优化数据库查询性能
4. **响应压缩**: 大数据量响应考虑压缩
