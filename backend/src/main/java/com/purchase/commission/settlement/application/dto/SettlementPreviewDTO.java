package com.purchase.commission.settlement.application.dto;

import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * 结算预览DTO
 * 用于在创建结算单据前预览结算信息
 */
@Getter
@Builder
public class SettlementPreviewDTO {
    
    /**
     * 邀请者ID
     */
    private final Long inviterId;
    
    /**
     * 月份键值：YYYY-MM
     */
    private final String monthKey;
    
    /**
     * 总佣金金额
     */
    private final BigDecimal totalCommission;
    
    /**
     * 月度奖金
     */
    private final BigDecimal monthlyBonus;
    
    /**
     * 结算总金额
     */
    private final BigDecimal totalAmount;
    
    /**
     * 建议的结算编号
     */
    private final String suggestedSettlementNumber;
    
    /**
     * 订单数量
     */
    private final Integer orderCount;

    /**
     * 订单总金额
     */
    private final BigDecimal totalOrderAmount;
}
