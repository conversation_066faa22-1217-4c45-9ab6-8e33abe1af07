package com.purchase.commission.settlement.application.dto;

import com.purchase.commission.settlement.domain.entity.MonthlyCommissionSummaryView;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 月度佣金汇总DTO
 * 用于API响应的数据传输对象
 */
@Data
public class MonthlyCommissionSummaryDTO {
    
    /**
     * 汇总记录ID
     */
    private Long id;
    
    /**
     * 邀请者ID
     */
    private Long inviterId;
    
    /**
     * 月份键
     */
    private String monthKey;
    
    /**
     * 订单数量
     */
    private Integer orderCount;
    
    /**
     * 订单总金额
     */
    private BigDecimal totalOrderAmount;

    /**
     * 佣金比例
     */
    private BigDecimal commissionRate;

    /**
     * 佣金金额
     */
    private BigDecimal commissionAmount;
    
    /**
     * 奖金金额
     */
    private BigDecimal bonusAmount;
    
    /**
     * 总金额（佣金+奖金）
     */
    private BigDecimal totalAmount;
    
    /**
     * 结算状态
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 最后计算时间
     */
    private LocalDateTime lastCalculatedAt;

    /**
     * 计算版本号
     */
    private Integer calculationVersion;

    /**
     * 结算时间
     */
    private LocalDateTime settledAt;
    
    /**
     * 从MonthlyCommissionSummaryView转换为DTO
     */
    public static MonthlyCommissionSummaryDTO fromEntity(MonthlyCommissionSummaryView entity) {
        if (entity == null) {
            return null;
        }
        
        MonthlyCommissionSummaryDTO dto = new MonthlyCommissionSummaryDTO();
        dto.setId(entity.getId());
        dto.setInviterId(entity.getInviterId());
        dto.setMonthKey(entity.getMonthKey());
        dto.setOrderCount(entity.getOrderCount());
        dto.setTotalOrderAmount(entity.getTotalOrderAmount());
        dto.setCommissionRate(entity.getCommissionRate());
        dto.setCommissionAmount(entity.getCommissionAmount());
        dto.setBonusAmount(entity.getBonusAmount());
        dto.setTotalAmount(entity.getTotalAmount());
        dto.setStatus(entity.getStatus());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setLastCalculatedAt(entity.getLastCalculatedAt());
        dto.setCalculationVersion(entity.getCalculationVersion());
        dto.setSettledAt(entity.getSettledAt());
        
        return dto;
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知";
        }
        
        switch (status) {
            case "PENDING":
                return "待结算";
            case "PROCESSING":
                return "结算中";
            case "COMPLETED":
                return "已完成";
            case "CANCELLED":
                return "已取消";
            default:
                return status;
        }
    }
    
    /**
     * 检查是否可以结算
     */
    public boolean canSettle() {
        return "PENDING".equals(status);
    }
    
    /**
     * 检查是否已结算
     */
    public boolean isSettled() {
        return "COMPLETED".equals(status);
    }
}
