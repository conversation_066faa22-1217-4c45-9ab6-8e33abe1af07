# MonthlyCommissionSummaryDTO 月度佣金汇总DTO文档

## 文件概述

`MonthlyCommissionSummaryDTO` 是月度佣金汇总数据传输对象，用于封装和传输月度佣金结算的汇总数据。该DTO包含完整的佣金计算信息、结算状态、时间记录等，提供静态转换方法和业务判断方法，为佣金结算系统的API响应和前端展示提供标准化的数据结构。

## 核心功能

### 主要职责
- **佣金数据封装**: 封装月度佣金汇总的完整信息
- **状态管理**: 管理佣金结算的状态和流程
- **数据转换**: 提供实体到DTO的转换方法
- **业务判断**: 提供佣金结算相关的业务判断方法
- **API响应**: 为前端提供标准化的数据格式

### 业务特点
- 完整的佣金计算链路
- 详细的时间追踪记录
- 灵活的状态管理机制
- 支持版本化计算
- 标准化的数据传输格式

## 接口说明

### 基础信息字段

#### id
- **类型**: Long
- **描述**: 汇总记录ID
- **用途**: 唯一标识月度佣金汇总记录

#### inviterId
- **类型**: Long
- **描述**: 邀请者ID
- **用途**: 标识获得佣金的邀请者

#### monthKey
- **类型**: String
- **描述**: 月份键
- **格式**: "YYYY-MM"
- **用途**: 标识佣金汇总的月份

### 订单统计字段

#### orderCount
- **类型**: Integer
- **描述**: 订单数量
- **用途**: 统计该月份邀请者相关的订单数量

#### totalOrderAmount
- **类型**: BigDecimal
- **描述**: 订单总金额
- **用途**: 统计该月份订单的总金额

### 佣金计算字段

#### commissionRate
- **类型**: BigDecimal
- **描述**: 佣金比例
- **格式**: 小数形式（如0.05表示5%）
- **用途**: 佣金计算的比例基数

#### commissionAmount
- **类型**: BigDecimal
- **描述**: 佣金金额
- **计算**: totalOrderAmount × commissionRate
- **用途**: 基础佣金金额

#### bonusAmount
- **类型**: BigDecimal
- **描述**: 奖金金额
- **用途**: 额外的奖励金额

#### totalAmount
- **类型**: BigDecimal
- **描述**: 总金额
- **计算**: commissionAmount + bonusAmount
- **用途**: 最终结算的总金额

### 状态管理字段

#### status
- **类型**: String
- **描述**: 结算状态
- **可选值**: 
  - PENDING: 待结算
  - PROCESSING: 结算中
  - COMPLETED: 已完成
  - CANCELLED: 已取消

### 时间记录字段

#### createdAt
- **类型**: LocalDateTime
- **描述**: 创建时间
- **用途**: 记录汇总记录的创建时间

#### updatedAt
- **类型**: LocalDateTime
- **描述**: 更新时间
- **用途**: 记录最后一次更新时间

#### lastCalculatedAt
- **类型**: LocalDateTime
- **描述**: 最后计算时间
- **用途**: 记录佣金最后一次计算的时间

#### settledAt
- **类型**: LocalDateTime
- **描述**: 结算时间
- **用途**: 记录佣金结算完成的时间

#### calculationVersion
- **类型**: Integer
- **描述**: 计算版本号
- **用途**: 支持佣金重新计算和版本管理

## 使用示例

### 静态转换方法使用
```java
@Service
public class CommissionSummaryService {
    
    @Autowired
    private MonthlyCommissionSummaryRepository repository;
    
    public List<MonthlyCommissionSummaryDTO> getMonthlyCommissionSummaries(Long inviterId, String year) {
        List<MonthlyCommissionSummaryView> entities = repository.findByInviterIdAndYear(inviterId, year);
        
        return entities.stream()
            .map(MonthlyCommissionSummaryDTO::fromEntity)
            .collect(Collectors.toList());
    }
    
    public MonthlyCommissionSummaryDTO getMonthlyCommissionSummary(Long inviterId, String monthKey) {
        MonthlyCommissionSummaryView entity = repository.findByInviterIdAndMonthKey(inviterId, monthKey);
        return MonthlyCommissionSummaryDTO.fromEntity(entity);
    }
}
```

### 控制器中使用
```java
@RestController
@RequestMapping("/api/commission/summary")
public class CommissionSummaryController {
    
    @Autowired
    private CommissionSummaryService commissionSummaryService;
    
    @GetMapping("/monthly/{inviterId}")
    public ApiResponse<List<MonthlyCommissionSummaryDTO>> getMonthlyCommissionSummaries(
            @PathVariable Long inviterId,
            @RequestParam(defaultValue = "2024") String year) {
        
        List<MonthlyCommissionSummaryDTO> summaries = 
            commissionSummaryService.getMonthlyCommissionSummaries(inviterId, year);
        
        return ApiResponse.success(summaries);
    }
    
    @GetMapping("/monthly/{inviterId}/{monthKey}")
    public ApiResponse<MonthlyCommissionSummaryDTO> getMonthlyCommissionSummary(
            @PathVariable Long inviterId,
            @PathVariable String monthKey) {
        
        MonthlyCommissionSummaryDTO summary = 
            commissionSummaryService.getMonthlyCommissionSummary(inviterId, monthKey);
        
        if (summary == null) {
            return ApiResponse.error("未找到指定月份的佣金汇总");
        }
        
        return ApiResponse.success(summary);
    }
    
    @PostMapping("/settle/{inviterId}/{monthKey}")
    public ApiResponse<Void> settleMonthlyCommission(
            @PathVariable Long inviterId,
            @PathVariable String monthKey) {
        
        MonthlyCommissionSummaryDTO summary = 
            commissionSummaryService.getMonthlyCommissionSummary(inviterId, monthKey);
        
        if (summary == null) {
            return ApiResponse.error("未找到指定月份的佣金汇总");
        }
        
        if (!summary.canSettle()) {
            return ApiResponse.error("当前状态无法结算: " + summary.getStatusDescription());
        }
        
        commissionSummaryService.settleMonthlyCommission(inviterId, monthKey);
        return ApiResponse.success("结算请求已提交");
    }
}
```

### 前端使用示例
```javascript
// 获取月度佣金汇总列表
const fetchMonthlyCommissionSummaries = async (inviterId, year = '2024') => {
  try {
    const response = await fetch(`/api/commission/summary/monthly/${inviterId}?year=${year}`, {
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`
      }
    });
    
    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取月度佣金汇总失败:', error);
    throw error;
  }
};

// 结算月度佣金
const settleMonthlyCommission = async (inviterId, monthKey) => {
  try {
    const response = await fetch(`/api/commission/summary/settle/${inviterId}/${monthKey}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`
      }
    });
    
    const result = await response.json();
    if (result.success) {
      return result;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('结算月度佣金失败:', error);
    throw error;
  }
};

// React月度佣金汇总组件
const MonthlyCommissionSummary = ({ inviterId }) => {
  const [summaries, setSummaries] = useState([]);
  const [selectedYear, setSelectedYear] = useState('2024');
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    loadCommissionSummaries();
  }, [inviterId, selectedYear]);
  
  const loadCommissionSummaries = async () => {
    setLoading(true);
    try {
      const data = await fetchMonthlyCommissionSummaries(inviterId, selectedYear);
      setSummaries(data);
    } catch (error) {
      console.error('加载佣金汇总失败:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleSettle = async (monthKey) => {
    try {
      await settleMonthlyCommission(inviterId, monthKey);
      toast.success('结算请求已提交');
      loadCommissionSummaries(); // 重新加载数据
    } catch (error) {
      toast.error(`结算失败: ${error.message}`);
    }
  };
  
  const formatCurrency = (amount) => {
    return `¥${amount?.toLocaleString() || '0.00'}`;
  };
  
  const getStatusBadge = (status, statusDescription) => {
    const statusColors = {
      'PENDING': 'warning',
      'PROCESSING': 'info',
      'COMPLETED': 'success',
      'CANCELLED': 'danger'
    };
    
    return (
      <span className={`badge badge-${statusColors[status] || 'secondary'}`}>
        {statusDescription}
      </span>
    );
  };
  
  if (loading) {
    return <div className="loading">加载中...</div>;
  }
  
  return (
    <div className="monthly-commission-summary">
      <div className="summary-header">
        <h3>月度佣金汇总</h3>
        <select
          value={selectedYear}
          onChange={(e) => setSelectedYear(e.target.value)}
          className="form-select"
        >
          <option value="2024">2024年</option>
          <option value="2023">2023年</option>
        </select>
      </div>
      
      <div className="summary-table">
        <table className="table table-striped">
          <thead>
            <tr>
              <th>月份</th>
              <th>订单数量</th>
              <th>订单总额</th>
              <th>佣金比例</th>
              <th>佣金金额</th>
              <th>奖金金额</th>
              <th>总金额</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {summaries.map(summary => (
              <tr key={summary.id}>
                <td>{summary.monthKey}</td>
                <td>{summary.orderCount}</td>
                <td>{formatCurrency(summary.totalOrderAmount)}</td>
                <td>{(summary.commissionRate * 100).toFixed(1)}%</td>
                <td>{formatCurrency(summary.commissionAmount)}</td>
                <td>{formatCurrency(summary.bonusAmount)}</td>
                <td className="font-weight-bold">
                  {formatCurrency(summary.totalAmount)}
                </td>
                <td>
                  {getStatusBadge(summary.status, summary.statusDescription)}
                </td>
                <td>
                  {summary.canSettle && (
                    <button
                      className="btn btn-sm btn-primary"
                      onClick={() => handleSettle(summary.monthKey)}
                    >
                      结算
                    </button>
                  )}
                  {summary.isSettled && (
                    <span className="text-muted">已结算</span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {summaries.length === 0 && (
        <div className="no-data">
          暂无{selectedYear}年的佣金汇总数据
        </div>
      )}
    </div>
  );
};
```

### 业务方法使用示例
```java
@Service
public class CommissionSettlementService {
    
    public void processMonthlySettlement(MonthlyCommissionSummaryDTO summary) {
        // 检查是否可以结算
        if (!summary.canSettle()) {
            throw new BusinessException("当前状态无法结算: " + summary.getStatusDescription());
        }
        
        // 验证金额
        if (summary.getTotalAmount() == null || summary.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("结算金额必须大于0");
        }
        
        // 执行结算逻辑
        try {
            // 更新状态为处理中
            updateSettlementStatus(summary.getId(), "PROCESSING");
            
            // 调用支付服务
            paymentService.processCommissionPayment(summary);
            
            // 更新状态为已完成
            updateSettlementStatus(summary.getId(), "COMPLETED");
            
        } catch (Exception e) {
            // 结算失败，回滚状态
            updateSettlementStatus(summary.getId(), "PENDING");
            throw new BusinessException("结算失败: " + e.getMessage());
        }
    }
    
    public CommissionSummaryReport generateMonthlyReport(Long inviterId, String monthKey) {
        MonthlyCommissionSummaryDTO summary = getMonthlyCommissionSummary(inviterId, monthKey);
        
        if (summary == null) {
            throw new BusinessException("未找到指定月份的佣金汇总");
        }
        
        CommissionSummaryReport report = new CommissionSummaryReport();
        report.setInviterId(summary.getInviterId());
        report.setMonthKey(summary.getMonthKey());
        report.setOrderCount(summary.getOrderCount());
        report.setTotalOrderAmount(summary.getTotalOrderAmount());
        report.setCommissionAmount(summary.getCommissionAmount());
        report.setBonusAmount(summary.getBonusAmount());
        report.setTotalAmount(summary.getTotalAmount());
        report.setStatus(summary.getStatusDescription());
        report.setIsSettled(summary.isSettled());
        report.setGeneratedAt(LocalDateTime.now());
        
        return report;
    }
}
```

## 注意事项

### 数据一致性
1. **金额计算**: 确保totalAmount = commissionAmount + bonusAmount
2. **状态流转**: 严格控制状态的流转规则
3. **版本管理**: 使用calculationVersion支持重新计算
4. **时间记录**: 准确记录各个时间节点

### 业务规则
1. **结算条件**: 明确结算的前置条件
2. **状态管理**: 正确处理各种状态转换
3. **权限控制**: 确保只有授权用户可以操作
4. **审计追踪**: 记录所有重要操作的审计日志

### 性能优化
1. **查询优化**: 优化月度汇总的查询性能
2. **缓存策略**: 缓存频繁查询的汇总数据
3. **批量处理**: 支持批量结算操作
4. **异步处理**: 大量数据处理使用异步方式

### 扩展性
1. **字段扩展**: 支持新增汇总字段
2. **状态扩展**: 支持新的结算状态
3. **计算规则**: 支持灵活的佣金计算规则
4. **报表功能**: 支持多样化的报表需求
