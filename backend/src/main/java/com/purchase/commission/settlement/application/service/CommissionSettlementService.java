package com.purchase.commission.settlement.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.purchase.commission.settlement.application.command.CreateSettlementCommand;
import com.purchase.commission.settlement.application.dto.SettlementPreviewDTO;
import com.purchase.commission.settlement.domain.entity.CommissionSettlement;
import com.purchase.commission.settlement.domain.entity.MonthlyCommissionSummaryView;
import com.purchase.commission.settlement.domain.repository.CommissionSettlementRepository;
import com.purchase.commission.settlement.domain.valueobject.ReceiptFile;
import com.purchase.commission.settlement.infrastructure.mapper.SettlementMonthlyCommissionSummaryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 佣金结算应用服务
 * 协调领域对象完成业务用例
 */
@Service
@Transactional
@Slf4j
public class CommissionSettlementService {

    private final CommissionSettlementRepository settlementRepository;
    private final SettlementMonthlyCommissionSummaryMapper summaryMapper;
    private final ApplicationEventPublisher eventPublisher;

    public CommissionSettlementService(
            CommissionSettlementRepository settlementRepository,
            SettlementMonthlyCommissionSummaryMapper summaryMapper,
            ApplicationEventPublisher eventPublisher) {
        this.settlementRepository = settlementRepository;
        this.summaryMapper = summaryMapper;
        this.eventPublisher = eventPublisher;
    }

    /**
     * 结算预览 - 生成前预览结算信息
     */
    @Transactional(readOnly = true)
    public SettlementPreviewDTO previewSettlement(Long inviterId, String monthKey) {
        // 1. 获取月度汇总数据
        MonthlyCommissionSummaryView summary = summaryMapper
            .selectByInviterIdAndMonthKey(inviterId, monthKey);
        
        if (summary == null) {
            throw new IllegalArgumentException("未找到月度汇总记录");
        }
        
        if (!"PENDING".equals(summary.getStatus())) {
            throw new IllegalStateException("只能预览待结算状态的汇总");
        }
        
        // 2. 检查是否已存在结算单据
        boolean exists = settlementRepository.existsByInviterIdAndMonthKey(inviterId, monthKey);
        if (exists) {
            throw new IllegalStateException("该月度汇总已存在结算单据");
        }
        
        // 3. 生成预览信息
        return SettlementPreviewDTO.builder()
            .inviterId(inviterId)
            .monthKey(monthKey)
            .totalCommission(summary.getCommissionAmount())
            .monthlyBonus(summary.getBonusAmount())
            .totalAmount(summary.getCommissionAmount().add(summary.getBonusAmount()))
            .totalOrderAmount(summary.getTotalOrderAmount()) // 添加订单总金额
            .suggestedSettlementNumber(CommissionSettlement.generateSettlementNumber(monthKey))
            .orderCount(summary.getOrderCount())
            .build();
    }

    /**
     * 创建结算单据
     */
    public CommissionSettlement createSettlement(CreateSettlementCommand command) {
        // 1. 验证月度汇总存在且状态正确
        MonthlyCommissionSummaryView summary = summaryMapper
            .selectByInviterIdAndMonthKey(command.getInviterId(), command.getMonthKey());
        
        if (summary == null) {
            throw new IllegalArgumentException(
                String.format("未找到月度汇总记录: inviterId=%d, monthKey=%s",
                             command.getInviterId(), command.getMonthKey()));
        }

        if (!"PENDING".equals(summary.getStatus())) {
            throw new IllegalStateException("只能为待结算状态的汇总创建结算单据");
        }

        // 2. 验证结算单据不存在
        if (settlementRepository.existsByInviterIdAndMonthKey(
                command.getInviterId(), command.getMonthKey())) {
            throw new IllegalStateException("该月度汇总已存在结算单据");
        }

        // 3. 转换回执文件
        List<ReceiptFile> receiptFiles = command.getReceiptFiles().stream()
            .map(dto -> ReceiptFile.create(dto.getFileUrl(), dto.getDescription(), command.getCreatedBy()))
            .collect(Collectors.toList());

        // 4. 创建结算单据
        CommissionSettlement settlement = CommissionSettlement.create(
            command.getInviterId(),
            command.getMonthKey(),
            summary.getCommissionAmount(),
            summary.getBonusAmount(),
            command.getSettlementMethod(),
            command.getAccountInfo(),
            receiptFiles,
            command.getNotes(),
            command.getPaymentReference(),
            command.getCreatedBy()
        );

        // 5. 保存结算单据
        settlementRepository.save(settlement);

        // 6. 更新月度汇总状态为已完成，并设置结算时间
        // 根据领域模型设计，结算单据创建成功即表示结算完成
        summary.setStatus("COMPLETED");
        summary.setSettledAt(java.time.LocalDateTime.now());
        summaryMapper.updateById(summary);

        // 7. 发布领域事件
        eventPublisher.publishEvent(new SettlementCompletedEvent(
            settlement.getId(), settlement.getInviterId(),
            settlement.getMonthKey(), settlement.getTotalAmount()));

        log.info("创建结算单据成功: settlementNumber={}, inviterId={}, monthKey={}",
                settlement.getSettlementNumber(), command.getInviterId(), command.getMonthKey());

        return settlement;
    }

    /**
     * 批量创建结算单据
     */
    public List<CommissionSettlement> batchCreateSettlements(
            List<CreateSettlementCommand> commands) {

        return commands.stream()
            .map(command -> {
                try {
                    return createSettlement(command);
                } catch (Exception e) {
                    log.error("批量创建结算单据失败: inviterId={}, monthKey={}, error={}",
                             command.getInviterId(), command.getMonthKey(), e.getMessage());
                    // 继续处理下一个，不中断整个批量操作
                    return null;
                }
            })
            .filter(settlement -> settlement != null)
            .collect(Collectors.toList());
    }

    /**
     * 根据ID获取结算单据
     */
    @Transactional(readOnly = true)
    public CommissionSettlement getSettlementById(Long settlementId) {
        return settlementRepository.findById(settlementId)
            .orElseThrow(() -> new IllegalArgumentException("结算单据不存在: " + settlementId));
    }

    /**
     * 根据邀请者ID和月份获取结算单据
     */
    @Transactional(readOnly = true)
    public CommissionSettlement getSettlementByInviterIdAndMonthKey(Long inviterId, String monthKey) {
        return settlementRepository.findByInviterIdAndMonthKey(inviterId, monthKey)
            .orElseThrow(() -> new IllegalArgumentException(
                String.format("结算单据不存在: inviterId=%d, monthKey=%s", inviterId, monthKey)));
    }

    /**
     * 分页查询结算单据列表
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页的结算单据列表
     */
    @Transactional(readOnly = true)
    public IPage<CommissionSettlement> getSettlements(int page, int size) {
        log.info("分页查询结算单据列表，页码: {}, 每页大小: {}", page, size);

        try {
            // 使用Spring Data的分页
            org.springframework.data.domain.Pageable pageable =
                org.springframework.data.domain.PageRequest.of(page, size,
                    org.springframework.data.domain.Sort.by(
                        org.springframework.data.domain.Sort.Direction.DESC, "createdAt"));

            org.springframework.data.domain.Page<CommissionSettlement> springPage =
                settlementRepository.findAll(pageable);

            // 转换为MyBatis-Plus的IPage格式以保持接口一致性
            IPage<CommissionSettlement> result = new Page<>(page + 1, size);
            result.setRecords(springPage.getContent());
            result.setTotal(springPage.getTotalElements());
            result.setCurrent(page + 1);
            result.setSize(size);

            log.info("查询结算单据列表成功，总数: {}, 当前页数量: {}",
                    result.getTotal(), result.getRecords().size());

            return result;

        } catch (Exception e) {
            log.error("查询结算单据列表失败", e);
            throw new RuntimeException("查询结算单据列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取待结算的月度汇总
     *
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页的待结算月度汇总
     */
    @Transactional(readOnly = true)
    public IPage<MonthlyCommissionSummaryView> getPendingSummaries(int page, int size) {
        log.info("获取待结算的月度汇总，页码: {}, 每页大小: {}", page, size);

        try {
            // 创建分页对象，MyBatis-Plus的页码从1开始，前端传入的page也是从1开始
            Page<MonthlyCommissionSummaryView> pageRequest = new Page<>(page, size);

            // 创建查询条件：只查询PENDING状态的记录
            LambdaQueryWrapper<MonthlyCommissionSummaryView> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MonthlyCommissionSummaryView::getStatus, "PENDING")
                       .orderByDesc(MonthlyCommissionSummaryView::getCreatedAt);

            // 执行分页查询
            IPage<MonthlyCommissionSummaryView> result = summaryMapper.selectPage(pageRequest, queryWrapper);

            log.info("查询到 {} 条待结算记录，总计 {} 条", result.getRecords().size(), result.getTotal());

            return result;

        } catch (Exception e) {
            log.error("获取待结算月度汇总失败", e);
            throw new RuntimeException("获取待结算月度汇总失败: " + e.getMessage(), e);
        }
    }

    /**
     * 简单的领域事件类
     */
    public static class SettlementCompletedEvent {
        private final Long settlementId;
        private final Long inviterId;
        private final String monthKey;
        private final java.math.BigDecimal totalAmount;

        public SettlementCompletedEvent(Long settlementId, Long inviterId, String monthKey, java.math.BigDecimal totalAmount) {
            this.settlementId = settlementId;
            this.inviterId = inviterId;
            this.monthKey = monthKey;
            this.totalAmount = totalAmount;
        }

        // getters
        public Long getSettlementId() { return settlementId; }
        public Long getInviterId() { return inviterId; }
        public String getMonthKey() { return monthKey; }
        public java.math.BigDecimal getTotalAmount() { return totalAmount; }
    }
}
