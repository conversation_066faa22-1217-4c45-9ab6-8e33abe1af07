package com.purchase.commission.settlement.application.command;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.purchase.commission.settlement.application.validation.ValidAccountInfo;
import lombok.Builder;
import lombok.Getter;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * 创建结算单据命令
 * 不可变对象，封装创建结算单据所需的所有参数
 */
@Getter
@Builder
public class CreateSettlementCommand {

    @NotNull(message = "邀请者ID不能为空")
    @Positive(message = "邀请者ID必须大于0")
    private final Long inviterId;
    
    @NotBlank(message = "月份不能为空")
    @Pattern(regexp = "^\\d{4}-(0[1-9]|1[0-2])$", message = "月份格式必须为YYYY-MM")
    private final String monthKey;
    
    @NotBlank(message = "结算方式不能为空")
    @Pattern(regexp = "^(bank_transfer|alipay|wechat)$", message = "结算方式必须为bank_transfer、alipay或wechat")
    private final String settlementMethod;
    
    @NotBlank(message = "账户信息不能为空")
    @Size(max = 500, message = "账户信息长度不能超过500字符")
    @ValidAccountInfo(message = "账户信息JSON格式不正确或缺少必填字段")
    private final String accountInfo;
    
    @NotEmpty(message = "回执文件不能为空")
    @Size(max = 5, message = "回执文件数量不能超过5个")
    @Valid
    private final List<ReceiptFileDTO> receiptFiles;
    
    @NotBlank(message = "结算备注不能为空")
    @Size(max = 1000, message = "结算备注长度不能超过1000字符")
    private final String notes;
    
    @NotBlank(message = "支付参考号不能为空")
    @Size(max = 100, message = "支付参考号长度不能超过100字符")
    private final String paymentReference;
    
    @NotBlank(message = "创建人不能为空")
    @Size(max = 50, message = "创建人长度不能超过50字符")
    private final String createdBy;

    /**
     * JSON反序列化构造函数
     */
    @JsonCreator
    public CreateSettlementCommand(
            @JsonProperty("inviterId") Long inviterId,
            @JsonProperty("monthKey") String monthKey,
            @JsonProperty("settlementMethod") String settlementMethod,
            @JsonProperty("accountInfo") String accountInfo,
            @JsonProperty("receiptFiles") List<ReceiptFileDTO> receiptFiles,
            @JsonProperty("notes") String notes,
            @JsonProperty("paymentReference") String paymentReference,
            @JsonProperty("createdBy") String createdBy) {
        this.inviterId = inviterId;
        this.monthKey = monthKey;
        this.settlementMethod = settlementMethod;
        this.accountInfo = accountInfo;
        this.receiptFiles = receiptFiles;
        this.notes = notes;
        this.paymentReference = paymentReference;
        this.createdBy = createdBy;
    }

    /**
     * 回执文件DTO
     */
    @Getter
    @Builder
    public static class ReceiptFileDTO {

        @NotBlank(message = "文件URL不能为空")
        @Size(max = 500, message = "文件URL长度不能超过500字符")
        private final String fileUrl;

        @NotBlank(message = "文件描述不能为空")
        @Size(max = 200, message = "文件描述长度不能超过200字符")
        private final String description;

        /**
         * JSON反序列化构造函数
         */
        @JsonCreator
        public ReceiptFileDTO(
                @JsonProperty("fileUrl") String fileUrl,
                @JsonProperty("description") String description) {
            this.fileUrl = fileUrl;
            this.description = description;
        }
    }
}
