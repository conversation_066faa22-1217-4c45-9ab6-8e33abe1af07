package com.purchase.commission.settlement.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.purchase.commission.settlement.domain.entity.MonthlyCommissionSummaryView;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * SettlementMonthlyCommissionSummaryView Mapper接口
 * 继承MyBatis-Plus的BaseMapper，提供基础CRUD操作
 */
@Mapper
public interface SettlementMonthlyCommissionSummaryMapper extends BaseMapper<MonthlyCommissionSummaryView> {

    /**
     * 根据邀请人ID和月份查询月度汇总
     */
    @Select("SELECT * FROM monthly_commission_summary WHERE inviter_id = #{inviterId} AND month_key = #{monthKey} AND deleted = 0")
    MonthlyCommissionSummaryView selectByInviterIdAndMonthKey(@Param("inviterId") Long inviterId,
                                                             @Param("monthKey") String monthKey);

    // ==================== 统计查询方法 ====================

    /**
     * 根据状态统计数量
     */
    @Select("SELECT COUNT(*) FROM monthly_commission_summary WHERE status = #{status} AND deleted = 0")
    Long countByStatus(@Param("status") String status);

    /**
     * 根据状态和月份统计数量
     */
    @Select("SELECT COUNT(*) FROM monthly_commission_summary WHERE status = #{status} AND month_key = #{monthKey} AND deleted = 0")
    Long countByStatusAndMonth(@Param("status") String status, @Param("monthKey") String monthKey);

    /**
     * 根据状态统计总金额
     */
    @Select("SELECT COALESCE(SUM(total_amount), 0) FROM monthly_commission_summary WHERE status = #{status} AND deleted = 0")
    BigDecimal sumTotalAmountByStatus(@Param("status") String status);

    /**
     * 根据状态和月份统计总金额
     */
    @Select("SELECT COALESCE(SUM(total_amount), 0) FROM monthly_commission_summary WHERE status = #{status} AND month_key = #{monthKey} AND deleted = 0")
    BigDecimal sumTotalAmountByStatusAndMonth(@Param("status") String status, @Param("monthKey") String monthKey);

    /**
     * 根据状态统计不重复邀请者数量
     */
    @Select("SELECT COUNT(DISTINCT inviter_id) FROM monthly_commission_summary WHERE status = #{status} AND deleted = 0")
    Long countDistinctInvitersByStatus(@Param("status") String status);
}
