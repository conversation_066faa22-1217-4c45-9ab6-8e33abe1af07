package com.purchase.commission.settlement.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 月度佣金汇总视图实体
 * 用于结算单据创建的前置条件查询
 * 注意：这是一个轻量级的数据传输对象，不包含业务逻辑
 */
@Data
@TableName("monthly_commission_summary")
public class MonthlyCommissionSummaryView {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("inviter_id")
    private Long inviterId;
    
    @TableField("month_key")
    private String monthKey;
    
    @TableField("commission_rate")
    private BigDecimal commissionRate;

    @TableField("commission_amount")
    private BigDecimal commissionAmount;

    @TableField("bonus_amount")
    private BigDecimal bonusAmount;

    @TableField("status")
    private String status; // PENDING/PROCESSING/COMPLETED/CANCELLED
    
    @TableField("order_count")
    private Integer orderCount;

    @TableField("total_order_amount")
    private BigDecimal totalOrderAmount;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @TableField("last_calculated_at")
    private LocalDateTime lastCalculatedAt;

    @TableField("calculation_version")
    private Integer calculationVersion;

    @TableField("settled_at")
    private LocalDateTime settledAt;

    @TableLogic
    @TableField("deleted")
    private Boolean deleted;
}
