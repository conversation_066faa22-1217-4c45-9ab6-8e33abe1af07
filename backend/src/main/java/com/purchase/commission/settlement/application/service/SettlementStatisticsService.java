package com.purchase.commission.settlement.application.service;

import com.purchase.commission.settlement.domain.valueobject.SettlementStatistics;
import com.purchase.commission.settlement.domain.repository.CommissionSettlementRepository;
import com.purchase.commission.settlement.infrastructure.mapper.SettlementMonthlyCommissionSummaryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.YearMonth;

/**
 * 结算统计应用服务
 * 
 * 按照DDD应用服务的职责，协调领域对象完成统计业务逻辑。
 * 遵循TDD开发方式，先有测试再有实现。
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SettlementStatisticsService {
    
    private final CommissionSettlementRepository settlementRepository;
    private final SettlementMonthlyCommissionSummaryMapper summaryMapper;
    
    /**
     * 获取结算统计数据
     * 
     * @return 结算统计值对象
     */
    @Transactional(readOnly = true)
    public SettlementStatistics getSettlementStatistics() {
        log.info("开始获取结算统计数据");
        
        try {
            // 获取当前月份
            String currentMonth = YearMonth.now().toString();
            
            // 查询各项统计数据
            Long pendingCount = summaryMapper.countByStatus("PENDING");
            Long completedCountThisMonth = summaryMapper.countByStatusAndMonth("COMPLETED", currentMonth);
            BigDecimal totalPendingAmount = summaryMapper.sumTotalAmountByStatus("PENDING");
            BigDecimal totalCompletedAmountThisMonth = summaryMapper.sumTotalAmountByStatusAndMonth("COMPLETED", currentMonth);
            Long inviterCount = summaryMapper.countDistinctInvitersByStatus("PENDING");
            
            // 创建统计值对象
            SettlementStatistics statistics = SettlementStatistics.create(
                pendingCount,
                completedCountThisMonth,
                totalPendingAmount,
                totalCompletedAmountThisMonth,
                inviterCount
            );
            
            log.info("获取结算统计数据成功: 待处理={}, 本月完成={}, 待结算金额={}, 本月结算金额={}, 邀请者数量={}",
                    statistics.getPendingCount(),
                    statistics.getCompletedCountThisMonth(),
                    statistics.getTotalPendingAmount(),
                    statistics.getTotalCompletedAmountThisMonth(),
                    statistics.getInviterCount());
            
            return statistics;
            
        } catch (Exception e) {
            log.error("获取结算统计数据失败", e);
            throw new RuntimeException("获取结算统计数据失败: " + e.getMessage(), e);
        }
    }
}
