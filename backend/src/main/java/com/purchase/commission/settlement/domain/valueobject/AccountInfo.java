package com.purchase.commission.settlement.domain.valueobject;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;

import java.util.Objects;

/**
 * 账户信息值对象
 * 
 * 用于封装不同结算方式的账户信息，支持JSON序列化和反序列化
 * 这是一个不可变值对象，确保数据的一致性和安全性
 */
@Getter
public class AccountInfo {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    /**
     * 结算方式：bank_transfer, alipay, wechat
     */
    private final String settlementMethod;
    
    /**
     * 银行名称（仅银行转账）
     */
    private final String bankName;
    
    /**
     * 账户号码（仅银行转账）
     */
    private final String accountNumber;
    
    /**
     * 账户持有人姓名
     */
    private final String accountHolder;
    
    /**
     * 分行名称（仅银行转账，可选）
     */
    private final String branchName;
    
    /**
     * 支付宝账号（仅支付宝）
     */
    private final String alipayAccount;
    
    /**
     * 微信账号（仅微信）
     */
    private final String wechatAccount;
    
    /**
     * 私有构造函数，强制使用工厂方法
     */
    @JsonCreator
    private AccountInfo(
            @JsonProperty("settlementMethod") String settlementMethod,
            @JsonProperty("bankName") String bankName,
            @JsonProperty("accountNumber") String accountNumber,
            @JsonProperty("accountHolder") String accountHolder,
            @JsonProperty("branchName") String branchName,
            @JsonProperty("alipayAccount") String alipayAccount,
            @JsonProperty("wechatAccount") String wechatAccount) {
        
        this.settlementMethod = settlementMethod;
        this.bankName = bankName;
        this.accountNumber = accountNumber;
        this.accountHolder = accountHolder;
        this.branchName = branchName;
        this.alipayAccount = alipayAccount;
        this.wechatAccount = wechatAccount;
    }
    
    /**
     * 创建银行转账账户信息
     */
    public static AccountInfo createBankTransfer(String bankName, String accountNumber, 
                                               String accountHolder, String branchName) {
        validateAccountHolder(accountHolder);
        validateNotBlank(bankName, "银行名称不能为空");
        validateNotBlank(accountNumber, "账户号码不能为空");
        
        return new AccountInfo("bank_transfer", bankName, accountNumber, 
                             accountHolder, branchName, null, null);
    }
    
    /**
     * 创建支付宝账户信息
     */
    public static AccountInfo createAlipay(String alipayAccount, String accountHolder) {
        validateAccountHolder(accountHolder);
        validateNotBlank(alipayAccount, "支付宝账号不能为空");
        
        return new AccountInfo("alipay", null, null, 
                             accountHolder, null, alipayAccount, null);
    }
    
    /**
     * 创建微信账户信息
     */
    public static AccountInfo createWechat(String wechatAccount, String accountHolder) {
        validateAccountHolder(accountHolder);
        validateNotBlank(wechatAccount, "微信账号不能为空");
        
        return new AccountInfo("wechat", null, null, 
                             accountHolder, null, null, wechatAccount);
    }
    
    /**
     * 从JSON字符串创建AccountInfo
     */
    public static AccountInfo fromJsonString(String jsonString) {
        try {
            return OBJECT_MAPPER.readValue(jsonString, AccountInfo.class);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("无效的JSON格式: " + e.getMessage(), e);
        }
    }
    
    /**
     * 转换为JSON字符串
     */
    public String toJsonString() {
        try {
            return OBJECT_MAPPER.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON序列化失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证账户持有人
     */
    private static void validateAccountHolder(String accountHolder) {
        validateNotBlank(accountHolder, "账户持有人不能为空");
    }
    
    /**
     * 验证字符串不为空
     */
    private static void validateNotBlank(String value, String message) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException(message);
        }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AccountInfo that = (AccountInfo) o;
        return Objects.equals(settlementMethod, that.settlementMethod) &&
               Objects.equals(bankName, that.bankName) &&
               Objects.equals(accountNumber, that.accountNumber) &&
               Objects.equals(accountHolder, that.accountHolder) &&
               Objects.equals(branchName, that.branchName) &&
               Objects.equals(alipayAccount, that.alipayAccount) &&
               Objects.equals(wechatAccount, that.wechatAccount);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(settlementMethod, bankName, accountNumber, 
                          accountHolder, branchName, alipayAccount, wechatAccount);
    }
    
    @Override
    public String toString() {
        return "AccountInfo{" +
               "settlementMethod='" + settlementMethod + '\'' +
               ", bankName='" + bankName + '\'' +
               ", accountNumber='" + accountNumber + '\'' +
               ", accountHolder='" + accountHolder + '\'' +
               ", branchName='" + branchName + '\'' +
               ", alipayAccount='" + alipayAccount + '\'' +
               ", wechatAccount='" + wechatAccount + '\'' +
               '}';
    }
}
