# CommissionSettlementRepository 佣金结算仓储接口文档

## 文件概述

`CommissionSettlementRepository` 是佣金结算的领域仓储接口，采用DDD（领域驱动设计）架构模式。该接口定义了结算聚合根的持久化操作，遵循仓储模式的设计原则，为领域层提供数据访问的抽象，隔离了领域逻辑与数据访问技术的实现细节。

## 核心功能

### 主要职责
- **数据抽象**: 为领域层提供数据访问的抽象接口
- **聚合管理**: 管理CommissionSettlement聚合根的持久化
- **查询封装**: 封装常用的查询操作
- **技术隔离**: 隔离领域逻辑与具体的数据访问技术
- **一致性保证**: 确保聚合的数据一致性

### 业务特点
- 遵循DDD仓储模式
- 面向聚合根设计
- 提供领域友好的接口
- 支持分页和批量操作
- 隔离技术实现细节

## 接口说明

### 基础操作方法

#### save(CommissionSettlement settlement)
```java
CommissionSettlement save(CommissionSettlement settlement)
```
- **功能**: 保存结算单据聚合根
- **参数**: `settlement`: 结算单据聚合根对象
- **返回值**: 保存后的结算单据对象
- **用途**: 新增或更新结算单据
- **事务**: 通常在应用服务层的事务边界内执行

#### findById(Long id)
```java
Optional<CommissionSettlement> findById(Long id)
```
- **功能**: 根据ID查找结算单据
- **参数**: `id`: 结算单据ID
- **返回值**: Optional包装的结算单据对象
- **用途**: 通过主键获取聚合根
- **设计**: 使用Optional避免null返回

#### saveAll(List<CommissionSettlement> settlements)
```java
List<CommissionSettlement> saveAll(List<CommissionSettlement> settlements)
```
- **功能**: 批量保存结算单据
- **参数**: `settlements`: 结算单据列表
- **返回值**: 保存后的结算单据列表
- **用途**: 批量操作提高性能
- **注意**: 需要考虑事务边界和内存使用

### 业务查询方法

#### findByInviterIdAndMonthKey(Long inviterId, String monthKey)
```java
Optional<CommissionSettlement> findByInviterIdAndMonthKey(Long inviterId, String monthKey)
```
- **功能**: 根据邀请人和月份查找结算单据
- **参数**:
  - `inviterId`: 邀请人ID
  - `monthKey`: 月份键值（YYYY-MM格式）
- **返回值**: Optional包装的结算单据对象
- **用途**: 检查特定月份是否已有结算记录
- **业务规则**: 每个邀请人每月只能有一条结算记录

#### existsByInviterIdAndMonthKey(Long inviterId, String monthKey)
```java
boolean existsByInviterIdAndMonthKey(Long inviterId, String monthKey)
```
- **功能**: 检查是否存在指定邀请人和月份的结算记录
- **参数**:
  - `inviterId`: 邀请人ID
  - `monthKey`: 月份键值
- **返回值**: 是否存在的布尔值
- **用途**: 防止重复创建结算记录
- **性能**: 比findBy方法更高效，只返回存在性

### 分页查询方法

#### findAll(Pageable pageable)
```java
Page<CommissionSettlement> findAll(Pageable pageable)
```
- **功能**: 分页查询所有结算单据
- **参数**: `pageable`: 分页参数对象
- **返回值**: 分页结果对象
- **用途**: 管理界面的分页列表查询
- **扩展**: 可以扩展为带条件的分页查询

## 使用示例

### 仓储实现示例
```java
@Repository
public class CommissionSettlementRepositoryImpl implements CommissionSettlementRepository {
    
    @Autowired
    private CommissionSettlementMapper settlementMapper;
    
    @Override
    public CommissionSettlement save(CommissionSettlement settlement) {
        if (settlement.getId() == null) {
            // 新增
            settlement.setId(snowflakeIdGenerator.nextId());
            settlement.setCreatedAt(LocalDateTime.now());
            settlement.setUpdatedAt(LocalDateTime.now());
            settlementMapper.insert(settlement);
        } else {
            // 更新
            settlement.setUpdatedAt(LocalDateTime.now());
            settlementMapper.updateById(settlement);
        }
        return settlement;
    }
    
    @Override
    public Optional<CommissionSettlement> findById(Long id) {
        CommissionSettlement settlement = settlementMapper.selectById(id);
        return Optional.ofNullable(settlement);
    }
    
    @Override
    public Optional<CommissionSettlement> findByInviterIdAndMonthKey(Long inviterId, String monthKey) {
        QueryWrapper<CommissionSettlement> wrapper = new QueryWrapper<>();
        wrapper.eq("inviter_id", inviterId)
               .eq("month_key", monthKey)
               .eq("deleted", 0);
        
        CommissionSettlement settlement = settlementMapper.selectOne(wrapper);
        return Optional.ofNullable(settlement);
    }
    
    @Override
    public boolean existsByInviterIdAndMonthKey(Long inviterId, String monthKey) {
        QueryWrapper<CommissionSettlement> wrapper = new QueryWrapper<>();
        wrapper.eq("inviter_id", inviterId)
               .eq("month_key", monthKey)
               .eq("deleted", 0);
        
        return settlementMapper.selectCount(wrapper) > 0;
    }
    
    @Override
    public Page<CommissionSettlement> findAll(Pageable pageable) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<CommissionSettlement> page = 
            new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(
                pageable.getPageNumber() + 1, 
                pageable.getPageSize()
            );
        
        IPage<CommissionSettlement> result = settlementMapper.selectPage(page, null);
        
        return new PageImpl<>(
            result.getRecords(),
            pageable,
            result.getTotal()
        );
    }
    
    @Override
    @Transactional
    public List<CommissionSettlement> saveAll(List<CommissionSettlement> settlements) {
        List<CommissionSettlement> savedSettlements = new ArrayList<>();
        
        for (CommissionSettlement settlement : settlements) {
            savedSettlements.add(save(settlement));
        }
        
        return savedSettlements;
    }
}
```

### 应用服务中使用
```java
@Service
@Transactional
public class CommissionSettlementService {
    
    private final CommissionSettlementRepository settlementRepository;
    
    public CommissionSettlementService(CommissionSettlementRepository settlementRepository) {
        this.settlementRepository = settlementRepository;
    }
    
    public CommissionSettlement createSettlement(CreateSettlementCommand command) {
        // 1. 检查是否已存在
        if (settlementRepository.existsByInviterIdAndMonthKey(
                command.getInviterId(), command.getMonthKey())) {
            throw new BusinessException("该月份已存在结算记录");
        }
        
        // 2. 创建聚合根
        CommissionSettlement settlement = CommissionSettlement.create(command);
        
        // 3. 保存到仓储
        return settlementRepository.save(settlement);
    }
    
    public CommissionSettlement getSettlementById(Long id) {
        return settlementRepository.findById(id)
            .orElseThrow(() -> new BusinessException("结算单据不存在"));
    }
    
    public Optional<CommissionSettlement> findSettlementByInviterAndMonth(Long inviterId, String monthKey) {
        return settlementRepository.findByInviterIdAndMonthKey(inviterId, monthKey);
    }
    
    public void updateSettlementStatus(Long settlementId, String newStatus) {
        CommissionSettlement settlement = getSettlementById(settlementId);
        settlement.updateStatus(newStatus);
        settlementRepository.save(settlement);
    }
    
    public Page<CommissionSettlement> getSettlements(Pageable pageable) {
        return settlementRepository.findAll(pageable);
    }
}
```

### 领域服务中使用
```java
@DomainService
public class SettlementDomainService {
    
    private final CommissionSettlementRepository settlementRepository;
    
    public SettlementDomainService(CommissionSettlementRepository settlementRepository) {
        this.settlementRepository = settlementRepository;
    }
    
    public void validateUniqueSettlement(Long inviterId, String monthKey) {
        if (settlementRepository.existsByInviterIdAndMonthKey(inviterId, monthKey)) {
            throw new DomainException("该邀请人在指定月份已存在结算记录");
        }
    }
    
    public CommissionSettlement getOrCreateSettlement(Long inviterId, String monthKey) {
        Optional<CommissionSettlement> existing = settlementRepository
            .findByInviterIdAndMonthKey(inviterId, monthKey);
        
        if (existing.isPresent()) {
            return existing.get();
        }
        
        // 创建新的结算单据
        CommissionSettlement newSettlement = CommissionSettlement.createEmpty(inviterId, monthKey);
        return settlementRepository.save(newSettlement);
    }
    
    public List<CommissionSettlement> batchCreateSettlements(List<CreateSettlementCommand> commands) {
        List<CommissionSettlement> settlements = commands.stream()
            .map(CommissionSettlement::create)
            .collect(Collectors.toList());
        
        return settlementRepository.saveAll(settlements);
    }
}
```

### 查询服务中使用
```java
@Service
@Transactional(readOnly = true)
public class SettlementQueryService {
    
    private final CommissionSettlementRepository settlementRepository;
    
    public SettlementQueryService(CommissionSettlementRepository settlementRepository) {
        this.settlementRepository = settlementRepository;
    }
    
    public SettlementDetailDTO getSettlementDetail(Long id) {
        CommissionSettlement settlement = settlementRepository.findById(id)
            .orElseThrow(() -> new BusinessException("结算单据不存在"));
        
        return SettlementDetailDTO.from(settlement);
    }
    
    public boolean hasSettlementForMonth(Long inviterId, String monthKey) {
        return settlementRepository.existsByInviterIdAndMonthKey(inviterId, monthKey);
    }
    
    public Page<SettlementListDTO> getSettlementList(Pageable pageable) {
        Page<CommissionSettlement> settlements = settlementRepository.findAll(pageable);
        
        return settlements.map(SettlementListDTO::from);
    }
}
```

## 注意事项

### DDD设计原则
1. **聚合边界**: 仓储操作以聚合根为边界
2. **技术无关**: 接口不依赖具体的技术实现
3. **领域语言**: 使用领域专家能理解的方法命名
4. **一致性**: 确保聚合内的数据一致性

### 事务管理
1. **事务边界**: 仓储操作通常在应用服务的事务边界内
2. **原子性**: 聚合的保存操作应该是原子的
3. **隔离级别**: 根据业务需要设置合适的隔离级别
4. **并发控制**: 考虑乐观锁或悲观锁的使用

### 性能优化
1. **查询优化**: 合理设计查询方法避免N+1问题
2. **批量操作**: 提供批量操作方法提高性能
3. **缓存策略**: 在实现层考虑缓存策略
4. **分页查询**: 大数据量查询使用分页

### 扩展性
1. **接口设计**: 预留扩展空间，避免频繁修改接口
2. **查询方法**: 根据业务需要添加新的查询方法
3. **规范命名**: 遵循Spring Data的命名规范
4. **版本兼容**: 考虑接口的向后兼容性

### 实现注意事项
1. **空值处理**: 使用Optional避免null返回
2. **异常处理**: 明确定义异常的抛出时机
3. **日志记录**: 在实现层记录重要的操作日志
4. **监控指标**: 提供必要的性能监控指标
