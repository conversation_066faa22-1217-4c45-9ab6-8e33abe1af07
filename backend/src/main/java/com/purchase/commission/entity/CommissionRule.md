## CommissionRule 文档

### 1. 文件概述
佣金规则实体类，定义佣金计算和分配的业务规则，映射到数据库`t_commission_rule`表

### 2. 核心字段说明
#### 规则定义字段
- `id` : Long - 主键ID
- `name` : String - 规则名称
- `ruleType` : String - 规则类型（PERCENTAGE/FIXED/TIERED）
- `baseRate` : BigDecimal - 基础费率（精度5,4）
- `minAmount` : BigDecimal - 最低佣金金额

#### 业务控制字段
- `effectiveDate` : LocalDate - 生效日期
- `expiryDate` : LocalDate - 失效日期
- `isActive` : Boolean - 是否激活
- `priority` : Integer - 规则优先级

#### 分配配置
- `platformRate` : BigDecimal - 平台分成比例
- `agentRate` : BigDecimal - 代理商分成比例
- `salesRate` : BigDecimal - 销售分成比例

### 3. 数据库映射
#### 表结构
```sql
CREATE TABLE `t_commission_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `rule_type` varchar(20) NOT NULL,
  `base_rate` decimal(5,4) DEFAULT NULL,
  `min_amount` decimal(19,4) DEFAULT NULL,
  `effective_date` date NOT NULL,
  `expiry_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `priority` int NOT NULL DEFAULT '0',
  `platform_rate` decimal(5,4) DEFAULT NULL,
  `agent_rate` decimal(5,4) DEFAULT NULL,
  `sales_rate` decimal(5,4) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_effective_date` (`effective_date`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 4. 使用示例
#### 实体操作示例
```java
// 创建阶梯佣金规则
CommissionRule rule = new CommissionRule();
rule.setName("VIP客户阶梯佣金");
rule.setRuleType("TIERED");
rule.setBaseRate(new BigDecimal("0.05"));
rule.setMinAmount(new BigDecimal("10.00"));
rule.setEffectiveDate(LocalDate.now());
rule.setExpiryDate(LocalDate.now().plusYears(1));
rule.setActive(true);
rule.setPriority(1);
rule.setPlatformRate(new BigDecimal("0.40"));
rule.setAgentRate(new BigDecimal("0.35"));
rule.setSalesRate(new BigDecimal("0.25"));
rule = commissionRuleRepository.save(rule);

// 查询有效规则
List<CommissionRule> activeRules = commissionRuleRepository
    .findByActiveTrueAndEffectiveDateLessThanEqual(LocalDate.now());
```

### 5. 注意事项
1. 版本控制：规则变更需创建新版本
2. 冲突检测：生效日期范围不能重叠
3. 缓存策略：活跃规则建议缓存
4. 数据校验：费率总和必须等于1
5. 审计跟踪：规则变更需记录完整历史