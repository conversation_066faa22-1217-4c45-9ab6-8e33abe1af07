# CommissionConfig Entity Documentation

## Overview
The `CommissionConfig` entity represents the configuration settings for commission calculations in the purchase system.

## Fields

| Field Name | Type | Description |
|------------|------|-------------|
| id | Long | Primary key |
| commissionRate | BigDecimal | Base commission rate percentage |
| minCommission | BigDecimal | Minimum commission amount |
| maxCommission | BigDecimal | Maximum commission amount |
| effectiveDate | LocalDateTime | When this configuration becomes active |
| expiryDate | LocalDateTime | When this configuration expires |
| status | Integer | Active status (1=active, 0=inactive) |
| createdBy | String | User who created this config |
| createdAt | LocalDateTime | Creation timestamp |
| updatedBy | String | User who last updated |
| updatedAt | LocalDateTime | Last update timestamp |

## Relationships
- One-to-many with `CommissionRule` (not shown in this table)

## Business Rules
1. Commission calculations must use the active configuration with the most recent effective date
2. New configurations must have effectiveDate > current date
3. Overlapping effective periods are not allowed

## Example JSON
```json
{
  "id": 1,
  "commissionRate": 0.05,
  "minCommission": 10.00,
  "maxCommission": 1000.00,
  "effectiveDate": "2025-01-01T00:00:00",
  "expiryDate": "2025-12-31T23:59:59",
  "status": 1
}
```

## Change Log
- 2025-07-01: Initial version
