## Settlement 文档

### 1. 文件概述
结算单实体类，表示佣金结算的核心数据模型，映射到数据库`t_settlement`表

### 2. 核心字段说明
#### 基础字段
- `id` : Long - 主键ID
- `serialNumber` : String - 结算单编号（唯一）
- `totalAmount` : BigDecimal - 结算总金额（精度19,4）
- `status` : String - 状态（CREATED/PAID/CANCELLED）

#### 业务字段
- `paymentTime` : LocalDateTime - 支付时间
- `paymentProof` : String - 支付凭证URL
- `commissionCount` : Integer - 包含佣金记录数
- `remark` : String - 结算备注

#### 关联字段
- `commissionRecords` : List<CommissionRecord> - 关联的佣金记录（一对多）

### 3. 数据库映射
#### 表结构
```sql
CREATE TABLE `t_settlement` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `serial_number` varchar(32) NOT NULL,
  `total_amount` decimal(19,4) NOT NULL,
  `status` varchar(20) NOT NULL,
  `payment_time` datetime DEFAULT NULL,
  `payment_proof` varchar(255) DEFAULT NULL,
  `commission_count` int DEFAULT '0',
  `remark` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_serial_number` (`serial_number`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 4. 使用示例
#### 实体操作示例
```java
// 创建结算单
Settlement settlement = new Settlement();
settlement.setSerialNumber("ST-" + System.currentTimeMillis());
settlement.setTotalAmount(new BigDecimal("1250.00"));
settlement.setStatus("CREATED");
settlement.setCommissionCount(5);
settlement = settlementRepository.save(settlement);

// 关联佣金记录
commissionRecords.forEach(record -> {
    record.setSettlementId(settlement.getId());
    commissionRecordRepository.save(record);
});

// 完成结算
settlement.setStatus("PAID");
settlement.setPaymentTime(LocalDateTime.now());
settlement.setPaymentProof("https://payments/12345");
settlementRepository.save(settlement);
```

### 5. 注意事项
1. 事务管理：结算单状态变更需在事务中完成
2. 数据一致性：与佣金记录表通过应用层维护关联
3. 序列化：paymentProof字段需URI编码
4. 查询优化：按状态查询已建立索引
5. 审计日志：状态变更需记录完整操作日志