## CommissionRecord 文档

### 1. 文件概述
佣金记录实体类，表示单笔佣金的核心数据模型，映射到数据库`t_commission_record`表

### 2. 核心字段说明
#### 基础字段
- `id` : Long - 主键ID
- `orderId` : Long - 关联订单ID（索引）
- `amount` : BigDecimal - 佣金金额（精度19,4）
- `status` : String - 状态（CALCULATED/DISTRIBUTED/PAID）

#### 业务字段
- `calculationTime` : LocalDateTime - 计算时间
- `distributionTime` : LocalDateTime - 分配时间
- `paymentTime` : LocalDateTime - 支付时间
- `details` : String - 佣金明细（JSON格式）

#### 关联字段
- `ruleId` : Long - 关联佣金规则ID
- `settlementId` : Long - 关联结算单ID

### 3. 数据库映射
#### 表结构
```sql
CREATE TABLE `t_commission_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NOT NULL,
  `amount` decimal(19,4) NOT NULL,
  `status` varchar(20) NOT NULL,
  `calculation_time` datetime DEFAULT NULL,
  `distribution_time` datetime DEFAULT NULL,
  `payment_time` datetime DEFAULT NULL,
  `details` text,
  `rule_id` bigint DEFAULT NULL,
  `settlement_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 4. 使用示例
#### 实体操作示例
```java
// 创建记录
CommissionRecord record = new CommissionRecord();
record.setOrderId(1001L);
record.setAmount(new BigDecimal("250.00"));
record.setStatus("CALCULATED");
record.setCalculationTime(LocalDateTime.now());
record = commissionRecordRepository.save(record);

// 查询示例
CommissionRecord found = commissionRecordRepository.findById(record.getId());

// 更新状态
record.setStatus("PAID");
record.setPaymentTime(LocalDateTime.now());
commissionRecordRepository.save(record);
```

### 5. 注意事项
1. 并发控制：状态变更需加乐观锁
2. 数据一致性：与订单表需要外键约束
3. 历史记录：重要字段变更需记录审计日志
4. 查询优化：高频查询字段已建立索引
5. 序列化：details字段使用JSON格式存储