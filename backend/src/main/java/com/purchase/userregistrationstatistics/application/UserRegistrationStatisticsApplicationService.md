# UserRegistrationStatisticsApplicationService.md

## 1. 文件概述

`UserRegistrationStatisticsApplicationService.java` 是用户注册统计模块中的一个应用服务，位于 `com.purchase.userregistrationstatistics.application` 包中。该服务作为用户注册统计功能的协调者，负责处理与用户注册数据分析相关的业务用例。它不直接包含核心业务逻辑，而是通过协调领域服务 (`RegistrationStatisticsService`) 和基础设施层（如 `UserRegistrationStatisticsRepository`），将复杂的统计计算过程封装为简洁的API，为前端提供用户注册趋势和数量的查询功能。

## 2. 核心功能

*   **注册统计查询**: 提供 `queryRegistrationStatistics` 方法，允许客户端根据指定的统计周期（如日、周、月）查询用户注册数量。这是该服务最核心的功能。
*   **领域服务协调**: 该服务充当了领域服务 `RegistrationStatisticsService` 的门面，将复杂的统计计算逻辑委托给领域服务处理，自身只负责参数校验、结果转换和异常处理。
*   **数据封装与转换**: 使用 `StatisticsQuery` DTO接收查询条件，并使用 `StatisticsResult` DTO封装和返回统计结果，实现了应用层与外部接口的数据隔离。
*   **健壮性**: 对输入参数进行了基本的非空校验，并捕获了领域服务可能抛出的异常，确保了服务的健壮性。

## 3. 接口说明

### 注册统计查询接口

#### queryRegistrationStatistics - 查询注册统计
*   **方法签名**: `StatisticsResult queryRegistrationStatistics(StatisticsQuery query)`
*   **参数**:
    *   `query` (StatisticsQuery): 一个DTO，包含了查询统计所需的条件，例如：
        *   `period` (String): 统计周期，例如 `DAILY`, `WEEKLY`, `MONTHLY`。
        *   `startDate` (LocalDate, optional): 统计的开始日期。
        *   `endDate` (LocalDate, optional): 统计的结束日期。
*   **返回值**: `StatisticsResult` - 一个DTO，包含了查询结果：
    *   `period` (String): 实际统计的周期。
    *   `count` (Long): 在该周期内的注册用户数量。
    *   `success` (boolean): 查询是否成功。
    *   `message` (String): 查询结果或错误信息。
*   **业务逻辑**: 
    1.  对传入的 `query` 对象进行非空校验，并确保 `period` 字段不为空。
    2.  调用领域服务 `domainService.calculateStatistics(query.getPeriod())` 来执行实际的统计计算。这里假设领域服务会根据周期计算出 `RegistrationStatistics` 领域模型。
    3.  将领域模型 `RegistrationStatistics` 转换为 `StatisticsResult` DTO，并返回。
    4.  捕获可能发生的异常，并返回一个表示失败的 `StatisticsResult`。

## 4. 业务规则

*   **统计周期**: 统计周期（`period`）是核心参数，其取值应是预定义的枚举或常量，例如 `DAILY`, `WEEKLY`, `MONTHLY`, `YEARLY`。领域服务会根据这个周期来聚合数据。
*   **数据源**: 注册统计的数据源通常是用户注册表（`user` 表）中的 `created_at` 或 `registration_time` 字段。
*   **性能考量**: 统计查询可能涉及大量数据扫描和聚合，尤其是在数据量大时。领域服务 `RegistrationStatisticsService` 的实现需要高度优化，例如使用数据库索引、预计算、物化视图或数据仓库技术。
*   **数据准确性**: 统计结果必须准确反映实际的用户注册情况。需要确保数据聚合逻辑的正确性。

## 5. 使用示例

```java
// 1. 服务实现类示例
@Service
public class UserRegistrationStatisticsApplicationServiceImpl implements UserRegistrationStatisticsApplicationService {
    // 构造函数注入依赖
    public UserRegistrationStatisticsApplicationServiceImpl(
            UserRegistrationStatisticsRepository repository, 
            RegistrationStatisticsService domainService) {
        this.repository = repository;
        this.domainService = domainService;
    }

    // queryRegistrationStatistics 方法如接口说明所示
}

// 2. 在Controller中调用，提供注册统计API
@RestController
@RequestMapping("/api/v1/statistics/registration")
public class RegistrationStatisticsController {
    @Autowired
    private UserRegistrationStatisticsApplicationService statsService;

    @GetMapping
    public Result<StatisticsResult> getRegistrationStats(
            @RequestParam @NotNull String period,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        StatisticsQuery query = new StatisticsQuery();
        query.setPeriod(period);
        query.setStartDate(startDate);
        query.setEndDate(endDate);

        StatisticsResult result = statsService.queryRegistrationStatistics(query);
        
        if (result.isSuccess()) {
            return Result.success(result);
        } else {
            return Result.error(result.getMessage());
        }
    }
}

// 3. 测试示例
@SpringBootTest
class UserRegistrationStatisticsApplicationServiceTest {
    @Autowired
    private UserRegistrationStatisticsApplicationService applicationService;

    @MockBean
    private RegistrationStatisticsService domainService;

    @Test
    void testQueryRegistrationStatistics_Success() {
        // 模拟领域服务返回成功结果
        RegistrationStatistics mockStats = new RegistrationStatistics("DAILY", 123L);
        when(domainService.calculateStatistics(anyString())).thenReturn(mockStats);

        StatisticsQuery query = new StatisticsQuery();
        query.setPeriod("DAILY");

        StatisticsResult result = applicationService.queryRegistrationStatistics(query);

        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getCount()).isEqualTo(123L);
        assertThat(result.getPeriod()).isEqualTo("DAILY");
    }

    @Test
    void testQueryRegistrationStatistics_InvalidPeriod() {
        StatisticsQuery query = new StatisticsQuery();
        query.setPeriod(null); // 模拟无效周期

        // 预期抛出 IllegalArgumentException
        assertThrows(IllegalArgumentException.class, () -> {
            applicationService.queryRegistrationStatistics(query);
        });
    }
}
```

## 6. 注意事项

*   **分层架构**: 该服务是典型的应用服务层，它协调领域服务和基础设施，但不包含核心业务逻辑。这种分层有助于保持代码的清晰度和可维护性。
*   **参数校验**: 在调用领域服务之前，对输入参数进行基本的非空校验是良好的实践，可以避免将无效数据传递到更深层次的业务逻辑中。
*   **领域服务职责**: `RegistrationStatisticsService` 作为领域服务，应该专注于统计计算的业务逻辑，例如如何根据不同的周期聚合数据，如何处理日期范围等。
*   **DTO与领域模型**: `StatisticsQuery` 和 `StatisticsResult` 是DTO，用于与外部（如Controller）交互。`RegistrationStatistics` 是领域模型，代表了业务概念。应用服务负责两者之间的转换。
*   **异常处理**: 应用服务捕获了领域服务可能抛出的异常，并将其转换为友好的 `StatisticsResult` 返回给调用方，避免了直接暴露内部异常。
*   **可扩展性**: 如果未来需要增加新的统计维度（如按地区、按用户类型），可以在 `StatisticsQuery` 和 `StatisticsResult` 中增加字段，并在领域服务中扩展计算逻辑，而应用服务层通常不需要大的改动。
*   **性能优化**: 统计功能往往是性能瓶颈。在实现 `RegistrationStatisticsService` 时，需要特别关注数据库查询的优化，例如使用合适的索引、预计算、数据缓存等技术。
*   **日志记录**: 在服务中添加适当的日志记录，可以帮助监控服务的调用情况和排查问题。