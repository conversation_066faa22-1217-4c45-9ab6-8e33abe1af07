# UserRegistrationStatistics.md

## 1. 文件概述

`UserRegistrationStatistics` 是用户注册统计模块中的一个值对象（Value Object），位于 `com.purchase.userregistrationstatistics.domain` 包中。它是一个不可变的类，封装了用户注册的各项统计数据，如今日、本周、本月和总注册用户数。该值对象严格遵循领域驱动设计（DDD）原则，强调值语义和不可变性，并在构造时执行严格的业务规则验证，确保数据的有效性和一致性。

## 2. 核心功能

*   **数据封装**: 封装了用户注册统计的四个核心指标：`todayCount`（今日注册数）、`thisWeekCount`（本周注册数）、`thisMonthCount`（本月注册数）和 `totalCount`（总注册数）。
*   **不可变性**: 一旦创建，其内部的统计数据就不能被修改，保证了数据的一致性和线程安全。
*   **构造时验证**: 在对象创建时，会执行严格的参数校验（非空、非负）和业务规则验证（例如，今日注册数不能大于本周注册数），确保了数据的逻辑正确性。
*   **业务行为**: 提供了 `hasNewRegistrations()`（判断今日是否有新注册）和 `isEmpty()`（判断是否所有统计数据都为零）等业务方法，使得值对象不仅仅是数据的载体，还包含了与数据相关的行为。
*   **值语义**: 重写了 `equals` 和 `hashCode` 方法，确保了当所有属性值相等时，两个 `UserRegistrationStatistics` 对象被认为是相等的，符合值对象的特性。

## 3. 接口说明

作为值对象，`UserRegistrationStatistics` 主要通过其构造函数和Getter方法与外部交互。

### 3.1 构造函数

#### UserRegistrationStatistics - 构造用户注册统计值对象
*   **方法签名**: `UserRegistrationStatistics(Long todayCount, Long thisWeekCount, Long thisMonthCount, Long totalCount)`
*   **描述**: 用于创建 `UserRegistrationStatistics` 的实例。在创建过程中，会对所有传入的计数进行非空、非负以及业务规则的验证。
*   **参数**:
    *   `todayCount` (Long): 今日注册用户数。
    *   `thisWeekCount` (Long): 本周注册用户数。
    *   `thisMonthCount` (Long): 本月注册用户数。
    *   `totalCount` (Long): 总注册用户数。
*   **异常**: `IllegalArgumentException` - 如果任何参数为空、为负数或违反了内部业务规则（如今日数大于本周数）。

### 3.2 核心业务方法

- **`getTodayCount()`**: 获取今日注册用户数。
- **`getThisWeekCount()`**: 获取本周注册用户数。
- **`getThisMonthCount()`**: 获取本月注册用户数。
- **`getTotalCount()`**: 获取总注册用户数。
- **`hasNewRegistrations()`**: 判断今日是否有新用户注册（`todayCount > 0`）。
- **`isEmpty()`**: 判断所有统计计数是否都为零。

## 4. 业务规则

*   **计数非负**: 所有统计计数（今日、本周、本月、总数）都不能为负数。
*   **层级递增**: 统计数据之间存在逻辑上的层级关系，必须满足以下条件：
    *   `todayCount <= thisWeekCount`
    *   `thisWeekCount <= thisMonthCount`
    *   `thisMonthCount <= totalCount`
    这些规则在构造函数中通过 `validateBusinessRules()` 方法强制执行。

## 5. 使用示例

```java
// 1. 在领域服务 (RegistrationStatisticsService) 中创建值对象
@Service
public class RegistrationStatisticsService {
    @Autowired
    private UserRegistrationRepository userRepository;

    public UserRegistrationStatistics calculateStatistics() {
        Long today = userRepository.countRegisteredToday();
        Long thisWeek = userRepository.countRegisteredThisWeek();
        Long thisMonth = userRepository.countRegisteredThisMonth();
        Long total = userRepository.countTotalRegistered();

        // 创建值对象，触发内部验证
        return new UserRegistrationStatistics(today, thisWeek, thisMonth, total);
    }
}

// 2. 在应用服务 (UserRegistrationStatisticsApplicationService) 中使用值对象
@Service
public class UserRegistrationStatisticsApplicationService {
    @Autowired
    private RegistrationStatisticsService domainService;

    public StatisticsResult queryRegistrationStatistics() {
        try {
            UserRegistrationStatistics stats = domainService.calculateStatistics();
            return new StatisticsResult(
                stats.getTodayCount(),
                stats.getThisWeekCount(),
                stats.getThisMonthCount(),
                stats.getTotalCount(),
                true, "查询成功"
            );
        } catch (IllegalArgumentException e) {
            return new StatisticsResult(0L, 0L, 0L, 0L, false, "参数错误: " + e.getMessage());
        }
    }
}

// 3. 测试示例
@SpringBootTest
class UserRegistrationStatisticsTest {
    @Test
    void testValidConstruction() {
        UserRegistrationStatistics stats = new UserRegistrationStatistics(10L, 50L, 200L, 1000L);
        assertThat(stats.getTodayCount()).isEqualTo(10L);
        assertThat(stats.getThisWeekCount()).isEqualTo(50L);
        assertThat(stats.hasNewRegistrations()).isTrue();
        assertThat(stats.isEmpty()).isFalse();
    }

    @Test
    void testInvalidConstruction_TodayGreaterThanThisWeek() {
        // 违反业务规则：今日注册数 > 本周注册数
        assertThrows(IllegalArgumentException.class, () -> {
            new UserRegistrationStatistics(60L, 50L, 200L, 1000L);
        });
    }

    @Test
    void testInvalidConstruction_NegativeCount() {
        // 违反参数校验：计数为负数
        assertThrows(IllegalArgumentException.class, () -> {
            new UserRegistrationStatistics(-1L, 50L, 200L, 1000L);
        });
    }

    @Test
    void testEqualsAndHashCode() {
        UserRegistrationStatistics stats1 = new UserRegistrationStatistics(1L, 2L, 3L, 4L);
        UserRegistrationStatistics stats2 = new UserRegistrationStatistics(1L, 2L, 3L, 4L);
        UserRegistrationStatistics stats3 = new UserRegistrationStatistics(5L, 6L, 7L, 8L);

        assertThat(stats1).isEqualTo(stats2);
        assertThat(stats1.hashCode()).isEqualTo(stats2.hashCode());
        assertThat(stats1).isNotEqualTo(stats3);
    }
}
```

## 6. 注意事项

*   **值对象特性**: `UserRegistrationStatistics` 是一个典型的DDD值对象。它没有唯一标识（ID），其相等性基于所有属性的值。它应该是不可变的，并且在创建时进行所有必要的验证。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。这使得对象在多线程环境中是安全的。
*   **构造函数验证**: 所有的业务规则和参数校验都集中在构造函数中。这意味着一旦一个 `UserRegistrationStatistics` 对象被成功创建，它就处于一个有效的、一致的状态。
*   **业务规则内聚**: 像 `hasNewRegistrations()` 和 `isEmpty()` 这样的业务方法直接定义在值对象内部，使得与统计数据相关的行为与数据本身紧密结合。
*   **与实体区别**: 与实体（Entity）不同，值对象没有生命周期，不能被单独持久化。它通常作为实体的一部分被持久化，或者在需要时动态创建。
*   **性能考量**: 虽然值对象本身是轻量级的，但其数据的计算（如 `calculateStatistics`）可能涉及复杂的数据库查询。在实际应用中，这些统计数据通常会被缓存或预计算，以提高查询性能。
*   **可读性**: 明确的字段命名和业务方法使得代码意图清晰，易于理解。
