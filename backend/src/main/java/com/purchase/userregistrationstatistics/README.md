# UserRegistrationStatistics 用户注册统计模块文档

## 模块概述

用户注册统计模块是采购系统的数据分析子模块，专门负责用户注册相关的数据统计和分析。该模块采用领域驱动设计（DDD）架构，提供用户注册趋势分析、注册来源统计、用户增长监控等功能，为运营决策提供数据支持。

## 目录结构概览

```
com.purchase.userregistrationstatistics/
├── application/                 # 应用层
│   ├── UserRegistrationStatisticsApplicationService.java # 应用服务
│   └── dto/                            # 应用层DTO
├── domain/                      # 领域层
│   ├── UserRegistrationStatistics.java        # 领域实体
│   ├── UserRegistrationStatisticsDomainService.java # 领域服务
│   ├── UserRegistrationStatisticsRepository.java   # 仓储接口
│   ├── model/                          # 领域模型
│   ├── service/                        # 领域服务
│   └── valueobject/                    # 值对象
├── infrastructure/             # 基础设施层
│   ├── mapper/                         # 数据访问映射
│   └── repository/                     # 仓储实现
└── interfaces/                 # 接口层
    ├── UserRegistrationStatisticsController.java # 控制器
    └── dto/                            # 接口层DTO
```

## 核心功能详述

### 1. 用户注册统计 (UserRegistrationStatistics)

#### 主要功能
- **注册趋势分析**: 按日、周、月统计用户注册趋势
- **注册来源统计**: 统计用户注册来源渠道
- **用户类型分析**: 按买家、卖家、货代等角色统计
- **地域分布统计**: 按国家、地区统计用户分布
- **增长率计算**: 计算用户注册增长率

#### 核心领域实体
- `UserRegistrationStatistics.java`: 用户注册统计聚合根
  - 统计周期管理
  - 注册数据聚合
  - 趋势计算逻辑
  - 统计规则验证

### 2. 领域服务 (UserRegistrationStatisticsDomainService)

#### 主要功能
- **数据聚合**: 原始注册数据的聚合计算
- **趋势分析**: 注册趋势的计算和分析
- **异常检测**: 注册数据异常的检测
- **预测模型**: 基于历史数据的注册预测

#### 业务规则
- 统计数据按UTC时间计算
- 重复注册数据去重处理
- 测试账号排除在统计之外
- 统计数据定期更新和校验

### 3. 应用服务 (UserRegistrationStatisticsApplicationService)

#### 主要功能
- **统计查询**: 提供各种维度的统计查询
- **报表生成**: 生成用户注册统计报表
- **数据导出**: 支持统计数据的导出功能
- **实时监控**: 实时用户注册监控

#### 查询功能
- 按时间范围查询注册统计
- 按用户类型查询注册分布
- 按注册来源查询渠道效果
- 按地域查询用户分布

## 数据模型说明

### UserRegistrationStatistics 用户注册统计

#### 核心字段
- `statisticsDate`: 统计日期
- `totalRegistrations`: 总注册数
- `buyerRegistrations`: 买家注册数
- `sellerRegistrations`: 卖家注册数
- `forwarderRegistrations`: 货代注册数
- `dailyGrowthRate`: 日增长率
- `weeklyGrowthRate`: 周增长率
- `monthlyGrowthRate`: 月增长率

#### 值对象
- `RegistrationPeriod`: 统计周期
- `RegistrationSource`: 注册来源
- `UserTypeDistribution`: 用户类型分布
- `GeographicDistribution`: 地理分布

## API 接口概览

### 统计查询接口
- `GET /api/v1/user-registration-statistics/daily` - 获取日注册统计
- `GET /api/v1/user-registration-statistics/weekly` - 获取周注册统计
- `GET /api/v1/user-registration-statistics/monthly` - 获取月注册统计
- `GET /api/v1/user-registration-statistics/trend` - 获取注册趋势

### 分析报告接口
- `GET /api/v1/user-registration-statistics/growth-rate` - 获取增长率分析
- `GET /api/v1/user-registration-statistics/source-analysis` - 获取来源分析
- `GET /api/v1/user-registration-statistics/type-distribution` - 获取类型分布
- `GET /api/v1/user-registration-statistics/geographic-distribution` - 获取地域分布

### 数据管理接口
- `POST /api/v1/user-registration-statistics/refresh` - 刷新统计数据
- `GET /api/v1/user-registration-statistics/export` - 导出统计数据

## 使用示例

### 获取注册趋势
```java
@Service
public class RegistrationAnalysisService {
    
    @Autowired
    private UserRegistrationStatisticsApplicationService statisticsService;
    
    public RegistrationTrendReport getRegistrationTrend(LocalDate startDate, LocalDate endDate) {
        // 获取指定时间范围的注册统计
        List<UserRegistrationStatistics> statistics = 
            statisticsService.getRegistrationStatistics(startDate, endDate);
        
        // 计算趋势指标
        RegistrationTrendReport report = new RegistrationTrendReport();
        report.setTotalRegistrations(calculateTotal(statistics));
        report.setAverageDaily(calculateDailyAverage(statistics));
        report.setGrowthRate(calculateGrowthRate(statistics));
        report.setPeakDay(findPeakDay(statistics));
        
        return report;
    }
}
```

### 前端图表展示
```javascript
// 注册趋势图表
const RegistrationTrendChart = () => {
  const [trendData, setTrendData] = useState([]);
  
  useEffect(() => {
    fetchRegistrationTrend();
  }, []);
  
  const fetchRegistrationTrend = async () => {
    const response = await fetch('/api/v1/user-registration-statistics/trend?days=30');
    const data = await response.json();
    setTrendData(data.data);
  };
  
  return (
    <LineChart data={trendData}>
      <XAxis dataKey="date" />
      <YAxis />
      <CartesianGrid strokeDasharray="3 3" />
      <Tooltip />
      <Legend />
      <Line type="monotone" dataKey="totalRegistrations" stroke="#8884d8" />
      <Line type="monotone" dataKey="buyerRegistrations" stroke="#82ca9d" />
      <Line type="monotone" dataKey="sellerRegistrations" stroke="#ffc658" />
    </LineChart>
  );
};
```

## 业务规则

### 统计规则
1. 统计数据按UTC时间计算，避免时区问题
2. 重复注册（同一用户多次注册）只计算一次
3. 测试账号和管理员账号不计入统计
4. 统计数据每日凌晨自动更新

### 数据质量规则
1. 统计数据必须与原始数据保持一致
2. 异常数据（如单日注册量异常高）需要标记
3. 历史数据不可随意修改，需要审计记录
4. 统计计算错误需要重新计算并记录

### 访问权限规则
1. 只有管理员和运营人员可以查看详细统计
2. 普通用户只能查看公开的汇总数据
3. 敏感统计数据需要额外权限验证
4. 数据导出功能需要特殊权限

## 集成说明

### 与其他模块的关系
- **用户模块**: 获取用户注册原始数据
- **分析模块**: 作为分析模块的子模块
- **消息模块**: 发送统计报告和异常通知
- **文件模块**: 统计报告的文件导出

### 数据流向
1. **数据收集**: 从用户模块收集注册事件
2. **数据处理**: 清洗和聚合原始数据
3. **数据存储**: 存储统计结果到数据库
4. **数据展示**: 通过API提供统计数据

## 注意事项

### 开发注意事项
1. **数据一致性**: 确保统计数据与原始数据一致
2. **性能优化**: 大量数据统计时的性能考虑
3. **实时性**: 平衡数据实时性和系统性能
4. **扩展性**: 支持新的统计维度扩展

### 性能优化
1. **批量处理**: 使用批量处理提高统计效率
2. **缓存策略**: 缓存频繁查询的统计数据
3. **异步计算**: 复杂统计使用异步计算
4. **索引优化**: 为统计查询建立合适索引

### 数据安全
1. **权限控制**: 严格的数据访问权限控制
2. **数据脱敏**: 敏感统计数据的脱敏处理
3. **审计日志**: 记录所有统计数据访问日志
4. **备份策略**: 重要统计数据的备份策略

### 监控告警
1. **数据异常**: 监控统计数据异常情况
2. **计算失败**: 监控统计计算失败情况
3. **性能监控**: 监控统计查询性能
4. **容量监控**: 监控数据存储容量

## 扩展功能

### 未来规划
1. **机器学习**: 集成机器学习预测模型
2. **实时流处理**: 实时用户注册流处理
3. **多维分析**: 支持更多维度的交叉分析
4. **可视化增强**: 更丰富的数据可视化

### 集成能力
1. **BI工具**: 与商业智能工具集成
2. **数据仓库**: 与企业数据仓库集成
3. **第三方分析**: 与第三方分析平台集成
4. **API开放**: 提供开放API供外部系统使用
