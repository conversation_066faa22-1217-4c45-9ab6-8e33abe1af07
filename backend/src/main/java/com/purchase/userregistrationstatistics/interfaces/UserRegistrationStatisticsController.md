# UserRegistrationStatisticsController.java

## 文件概述 (File Overview)
`UserRegistrationStatisticsController.java` 是用户注册统计的REST控制器，专门为管理员提供用户注册数据的统计查询API接口。该控制器通过调用 `UserRegistrationStatisticsApplicationService` 处理具体的业务逻辑，提供今日、本周、本月、本年等不同时间维度的用户注册统计数据，以及详细的注册统计报告，为运营决策提供数据支持。

## 核心功能 (Core Functionality)
*   **今日注册统计:** 获取当日新注册用户数量统计。
*   **本周注册统计:** 获取本周新注册用户数量统计。
*   **本月注册统计:** 获取本月新注册用户数量统计。
*   **本年注册统计:** 获取本年新注册用户数量统计。
*   **详细统计报告:** 提供包含多维度数据的详细注册统计报告。
*   **权限控制:** 所有接口都限制为管理员权限，确保数据安全。
*   **异常处理:** 提供完善的异常处理机制，确保接口的稳定性。

## 接口说明 (Interface Description)

### 主要方法 (Main Methods)

#### 1. getTodayRegistrations()
*   **HTTP方法:** GET
*   **路径:** `/api/v1/admin/user-registration-statistics/today`
*   **权限:** `@PreAuthorize("hasAuthority('admin')")`
*   **返回值:** `ApiResponse<Long>`
*   **功能:** 获取今日注册用户数量
*   **业务逻辑:** 查询当日0点到当前时间的新注册用户数量

#### 2. getWeekRegistrations()
*   **HTTP方法:** GET
*   **路径:** `/api/v1/admin/user-registration-statistics/week`
*   **权限:** `@PreAuthorize("hasAuthority('admin')")`
*   **返回值:** `ApiResponse<Long>`
*   **功能:** 获取本周注册用户数量
*   **业务逻辑:** 查询本周一到当前时间的新注册用户数量

#### 3. getMonthRegistrations()
*   **HTTP方法:** GET
*   **路径:** `/api/v1/admin/user-registration-statistics/month`
*   **权限:** `@PreAuthorize("hasAuthority('admin')")`
*   **返回值:** `ApiResponse<Long>`
*   **功能:** 获取本月注册用户数量
*   **业务逻辑:** 查询本月1号到当前时间的新注册用户数量

#### 4. getYearRegistrations()
*   **HTTP方法:** GET
*   **路径:** `/api/v1/admin/user-registration-statistics/year`
*   **权限:** `@PreAuthorize("hasAuthority('admin')")`
*   **返回值:** `ApiResponse<Long>`
*   **功能:** 获取本年注册用户数量
*   **业务逻辑:** 查询本年1月1日到当前时间的新注册用户数量

#### 5. getDetailedStatistics()
*   **HTTP方法:** GET
*   **路径:** `/api/v1/admin/user-registration-statistics/detailed`
*   **权限:** `@PreAuthorize("hasAuthority('admin')")`
*   **返回值:** `ApiResponse<RegistrationStatisticsResponse>`
*   **功能:** 获取详细的注册统计报告
*   **业务逻辑:** 返回包含多个时间维度的综合统计数据

### 权限控制 (Permission Control)
*   **类级权限:** `@PreAuthorize("hasAuthority('admin')")` - 整个控制器只允许管理员访问
*   **接口路径:** 所有接口都在 `/api/v1/admin/` 路径下，明确标识为管理员接口

## 使用示例 (Usage Examples)

```java
// 前端JavaScript调用示例
// 1. 获取今日注册统计
const getTodayRegistrations = async () => {
    try {
        const response = await fetch('/api/v1/admin/user-registration-statistics/today', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + adminToken,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        if (result.success) {
            console.log('今日注册用户数:', result.data);
            return result.data;
        } else {
            console.error('获取今日注册统计失败:', result.message);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
};

// 2. 获取详细统计报告
const getDetailedStatistics = async () => {
    try {
        const response = await fetch('/api/v1/admin/user-registration-statistics/detailed', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + adminToken,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        if (result.success) {
            const stats = result.data;
            console.log('详细统计数据:', {
                today: stats.todayCount,
                week: stats.weekCount,
                month: stats.monthCount,
                year: stats.yearCount,
                trend: stats.trendData
            });
            return stats;
        }
    } catch (error) {
        console.error('获取详细统计失败:', error);
    }
};

// 3. 构建管理员仪表盘
const buildAdminDashboard = async () => {
    try {
        // 并行获取所有统计数据
        const [todayCount, weekCount, monthCount, yearCount] = await Promise.all([
            getTodayRegistrations(),
            fetch('/api/v1/admin/user-registration-statistics/week').then(r => r.json()),
            fetch('/api/v1/admin/user-registration-statistics/month').then(r => r.json()),
            fetch('/api/v1/admin/user-registration-statistics/year').then(r => r.json())
        ]);

        // 更新仪表盘UI
        updateDashboard({
            today: todayCount,
            week: weekCount.data,
            month: monthCount.data,
            year: yearCount.data
        });
    } catch (error) {
        console.error('构建仪表盘失败:', error);
    }
};

// Java客户端调用示例
@Service
public class AdminStatisticsService {
    @Autowired
    private RestTemplate restTemplate;

    public Long getTodayRegistrations(String adminToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(adminToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<ApiResponse> response = restTemplate.exchange(
                "/api/v1/admin/user-registration-statistics/today",
                HttpMethod.GET,
                entity,
                ApiResponse.class
            );

            ApiResponse result = response.getBody();
            if (result != null && result.isSuccess()) {
                return (Long) result.getData();
            }
        } catch (Exception e) {
            log.error("获取今日注册统计失败", e);
        }
        return 0L;
    }

    public RegistrationStatisticsResponse getDetailedStatistics(String adminToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(adminToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<ApiResponse> response = restTemplate.exchange(
                "/api/v1/admin/user-registration-statistics/detailed",
                HttpMethod.GET,
                entity,
                ApiResponse.class
            );

            ApiResponse result = response.getBody();
            if (result != null && result.isSuccess()) {
                return (RegistrationStatisticsResponse) result.getData();
            }
        } catch (Exception e) {
            log.error("获取详细统计失败", e);
        }
        return null;
    }
}

// 在其他Controller中使用
@RestController
@RequestMapping("/api/v1/admin/dashboard")
public class AdminDashboardController {
    @Autowired
    private UserRegistrationStatisticsApplicationService statisticsService;

    @GetMapping("/overview")
    @PreAuthorize("hasAuthority('admin')")
    public Result<Map<String, Object>> getDashboardOverview() {
        Map<String, Object> overview = new HashMap<>();

        try {
            // 获取各时间维度的注册统计
            StatisticsQuery todayQuery = new StatisticsQuery(StatisticsPeriod.TODAY);
            StatisticsQuery weekQuery = new StatisticsQuery(StatisticsPeriod.WEEK);
            StatisticsQuery monthQuery = new StatisticsQuery(StatisticsPeriod.MONTH);

            StatisticsResult todayResult = statisticsService.queryRegistrationStatistics(todayQuery);
            StatisticsResult weekResult = statisticsService.queryRegistrationStatistics(weekQuery);
            StatisticsResult monthResult = statisticsService.queryRegistrationStatistics(monthQuery);

            overview.put("todayRegistrations", todayResult.getCount());
            overview.put("weekRegistrations", weekResult.getCount());
            overview.put("monthRegistrations", monthResult.getCount());

            return Result.success(overview);
        } catch (Exception e) {
            return Result.error("获取仪表盘概览失败: " + e.getMessage());
        }
    }
}
```

## 注意事项 (Notes)
*   **管理员权限:** 所有接口都使用 `@PreAuthorize("hasAuthority('admin')")` 注解进行权限控制，确保只有管理员才能访问用户注册统计数据。
*   **数据安全:** 用户注册统计数据属于敏感的运营数据，需要严格的权限控制和访问日志记录。
*   **异常处理:** 所有方法都包含完善的异常处理机制，确保在出现错误时返回友好的错误信息。
*   **业务委托:** 控制器只负责HTTP请求处理和响应封装，具体的统计逻辑委托给 `UserRegistrationStatisticsApplicationService` 处理。
*   **响应格式:** 所有接口都返回统一的 `ApiResponse<T>` 格式，确保前端能够统一处理响应数据。
*   **时间维度:** 统计数据按照不同的时间维度（今日、本周、本月、本年）进行分类，便于运营分析。
*   **性能考虑:** 统计查询可能涉及大量数据，建议在应用服务层实现缓存机制以提高查询性能。
*   **数据一致性:** 统计数据的计算需要考虑时区问题，确保在不同时区的用户看到一致的统计结果。
*   **监控和告警:** 建议对注册统计数据设置监控和告警机制，及时发现异常的注册趋势。
