package com.purchase.forwarding.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.purchase.forwarding.bidding.entity.ForwardingBidding;
import com.purchase.forwarding.bidding.mapper.ForwardingBiddingMapper;
import com.purchase.forwarding.dto.ForwarderDashboardStatisticsDTO;
import com.purchase.forwarding.order.entity.ForwarderOrder;
import com.purchase.forwarding.order.mapper.ForwarderOrderMapper;
import com.purchase.forwarding.order.service.ForwarderOrderService;
import com.purchase.forwarding.order.vo.ForwarderOrderVO;
import com.purchase.forwarding.service.ForwarderDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 货代仪表盘服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ForwarderDashboardServiceImpl implements ForwarderDashboardService {
    
    private final ForwardingBiddingMapper forwardingBiddingMapper;
    private final ForwarderOrderMapper forwarderOrderMapper;
    private final ForwarderOrderService forwarderOrderService;
    
    @Override
    public ForwarderDashboardStatisticsDTO getDashboardStatistics(Long forwarderId) {
        log.info("获取货代仪表盘统计数据, forwarderId: {}", forwarderId);
        
        try {
            // 统计总竞价数
            Long bidCount = forwardingBiddingMapper.selectCount(
                new LambdaQueryWrapper<ForwardingBidding>()
                    .eq(ForwardingBidding::getForwarderId, forwarderId)
            );
            
            // 统计中标项目数（状态为ACCEPTED或COMPLETED的竞价）
            Long winCount = forwardingBiddingMapper.selectCount(
                new LambdaQueryWrapper<ForwardingBidding>()
                    .eq(ForwardingBidding::getForwarderId, forwarderId)
                    .in(ForwardingBidding::getStatus, "ACCEPTED", "COMPLETED")
            );
            
            // 统计待处理物流数（状态为PENDING或PROCESSING的订单）
            Long pendingCount = forwarderOrderMapper.selectCount(
                new LambdaQueryWrapper<ForwarderOrder>()
                    .eq(ForwarderOrder::getForwarderId, forwarderId)
                    .in(ForwarderOrder::getOrderStatus, "pending", "processing")
            );
            
            ForwarderDashboardStatisticsDTO statistics = ForwarderDashboardStatisticsDTO.builder()
                .bidCount(bidCount)
                .winCount(winCount)
                .pendingCount(pendingCount)
                .build();
            
            log.info("货代仪表盘统计数据获取成功: {}", statistics);
            return statistics;
            
        } catch (Exception e) {
            log.error("获取货代仪表盘统计数据失败, forwarderId: {}", forwarderId, e);
            // 返回默认值，避免前端报错
            return ForwarderDashboardStatisticsDTO.builder()
                .bidCount(0L)
                .winCount(0L)
                .pendingCount(0L)
                .build();
        }
    }
    
    @Override
    public List<ForwarderOrderVO> getRecentOrders(Long forwarderId, Integer limit) {
        log.info("获取货代最近订单列表, forwarderId: {}, limit: {}", forwarderId, limit);
        
        try {
            // 设置默认限制数量
            if (limit == null || limit <= 0) {
                limit = 5;
            }
            if (limit > 20) {
                limit = 20; // 最多返回20条
            }
            
            // 查询最近的订单
            IPage<ForwarderOrder> page = new Page<>(1, limit);
            LambdaQueryWrapper<ForwarderOrder> queryWrapper = new LambdaQueryWrapper<ForwarderOrder>()
                .eq(ForwarderOrder::getForwarderId, forwarderId)
                .orderByDesc(ForwarderOrder::getCreatedAt);
            
            IPage<ForwarderOrder> orderPage = forwarderOrderMapper.selectPage(page, queryWrapper);
            
            // 转换为VO对象
            List<ForwarderOrderVO> recentOrders = orderPage.getRecords().stream()
                .map(forwarderOrderService::convertToVO)
                .collect(Collectors.toList());
            
            log.info("货代最近订单列表获取成功, 数量: {}", recentOrders.size());
            return recentOrders;
            
        } catch (Exception e) {
            log.error("获取货代最近订单列表失败, forwarderId: {}", forwarderId, e);
            return List.of(); // 返回空列表，避免前端报错
        }
    }
} 