# ForwarderDashboardService.md

## 1. 文件概述

`ForwarderDashboardService.java` 是货代模块中一个专门为货代用户仪表盘（Dashboard）提供聚合数据的服务接口，位于 `com.purchase.forwarding.service` 包中。该接口的核心职责是整合与特定货代相关的各类业务数据，如订单统计、待办事项、近期活动等，并以一种清晰、简洁的数据结构（DTO）返回，旨在为货代提供一个“一站式”的工作台视图，帮助他们快速了解业务概况和处理关键任务。

## 2. 核心功能

*   **数据聚合与统计**: `getDashboardStatistics` 方法是其核心，负责从多个数据源（如订单、竞价、需求、财务等）拉取数据，并聚合成一个包含多维度统计指标的 `ForwarderDashboardStatisticsDTO` 对象。这包括进行中的订单数、待处理的竞价数、本月收入等关键绩效指标（KPIs）。
*   **近期活动查询**: `getRecentOrders` 方法提供了一个便捷的途径来获取该货代最近的几笔订单，通常用于在仪表盘上展示一个“最新订单”列表，方便用户快速访问。
*   **单一职责**: 该服务接口严格遵循单一职责原则，其所有功能都紧密围绕“为货代仪表盘提供数据”这一核心目标，没有混入其他不相关的业务逻辑。
*   **面向特定角色**: 这是一个高度面向特定用户角色（货代）的服务。所有查询都以 `forwarderId` 作为关键参数，确保了数据的隔离和相关性。

## 3. 接口说明

### 仪表盘数据接口

#### getDashboardStatistics - 获取货代仪表盘统计数据
*   **方法签名**: `ForwarderDashboardStatisticsDTO getDashboardStatistics(Long forwarderId)`
*   **参数**:
    *   `forwarderId` (Long): 需要查询其统计数据的货代的用户ID。
*   **返回值**: `ForwarderDashboardStatisticsDTO` - 一个包含了多维度统计数据的数据传输对象。该DTO可能包含以下字段：
    *   `ongoingOrdersCount`: 进行中的订单数量。
    *   `pendingBiddingsCount`: 待处理的竞价邀请数量。
    *   `completedOrdersThisMonth`: 本月完成的订单数。
    *   `totalRevenueThisMonth`: 本月总收入。
    *   `newRequirementsCount`: 新发布的需求数量。
    *   `exceptionShipmentsCount`: 异常状态的货运数量。
*   **业务逻辑**: 服务的实现类需要分别调用多个不同的服务或仓库（如 `ForwarderOrderService`, `BiddingService`, `FinanceService` 等），执行多次查询来获取各个统计指标，然后将这些数据组装进一个 `ForwarderDashboardStatisticsDTO` 对象中返回。

#### getRecentOrders - 获取货代最近订单列表
*   **方法签名**: `List<ForwarderOrderVO> getRecentOrders(Long forwarderId, Integer limit)`
*   **参数**:
    *   `forwarderId` (Long): 货代的用户ID。
    *   `limit` (Integer): 需要返回的最近订单的数量。
*   **返回值**: `List<ForwarderOrderVO>` - 一个包含了最近订单信息的视图对象列表，按创建时间降序排列。
*   **业务逻辑**: 调用 `ForwarderOrderService` 的分页查询接口，将页码设置为1，每页大小设置为 `limit`，并按订单创建时间 `creation_date` 降序排序，然后返回查询结果。

## 4. 业务规则

*   **数据范围**: 所有统计数据的时间范围需要明确定义。例如，“本月”是指日历月，“近期”是指过去7天或30天，这些规则应在服务实现中保持一致。
*   **性能考量**: `getDashboardStatistics` 方法可能会执行多次数据库查询。为了保证仪表盘加载性能，其实现需要被高度优化。可以考虑使用缓存（如Redis）来缓存这些不频繁变动的统计数据，或者通过一个复杂的聚合SQL查询一次性获取所有数据。
*   *   **数据准确性**: 统计数据的准确性至关重要。服务实现需要确保其依赖的各个服务或查询能够提供精准的数据，并且在事务处理上保持一致性。

## 5. 使用示例

```java
// 1. 服务实现类示例
@Service
public class ForwarderDashboardServiceImpl implements ForwarderDashboardService {
    @Autowired
    private ForwarderOrderService orderService;
    @Autowired
    private BiddingService biddingService;
    // ... 其他依赖的服务 ...

    @Override
    @Cacheable(value = "dashboardStats", key = "#forwarderId")
    public ForwarderDashboardStatisticsDTO getDashboardStatistics(Long forwarderId) {
        // 并行或串行调用各个服务获取数据
        CompletableFuture<Long> ongoingOrdersFuture = CompletableFuture.supplyAsync(() -> orderService.countByStatus(forwarderId, "ONGOING"));
        CompletableFuture<Long> pendingBiddingsFuture = CompletableFuture.supplyAsync(() -> biddingService.countByStatus(forwarderId, "PENDING"));
        // ... 其他异步查询 ...

        // 等待所有结果并组装DTO
        CompletableFuture.allOf(ongoingOrdersFuture, pendingBiddingsFuture).join();
        
        ForwarderDashboardStatisticsDTO dto = new ForwarderDashboardStatisticsDTO();
        dto.setOngoingOrdersCount(ongoingOrdersFuture.get());
        dto.setPendingBiddingsCount(pendingBiddingsFuture.get());
        // ... 设置其他属性 ...

        return dto;
    }

    @Override
    public List<ForwarderOrderVO> getRecentOrders(Long forwarderId, Integer limit) {
        // 调用订单服务的分页接口获取第一页数据
        IPage<ForwarderOrderVO> orderPage = orderService.getOrderPage(1, limit, forwarderId, null, null);
        return orderPage.getRecords();
    }
}

// 2. 在仪表盘Controller中调用
@RestController
@RequestMapping("/api/v1/forwarder/dashboard")
@PreAuthorize("hasAuthority('forwarder')")
public class ForwarderDashboardController {
    @Autowired
    private ForwarderDashboardService dashboardService;

    @GetMapping
    public Result<Map<String, Object>> getDashboardData() {
        Long currentForwarderId = // ... 从SecurityContext获取当前用户ID

        ForwarderDashboardStatisticsDTO stats = dashboardService.getDashboardStatistics(currentForwarderId);
        List<ForwarderOrderVO> recentOrders = dashboardService.getRecentOrders(currentForwarderId, 5);

        Map<String, Object> dashboardData = new HashMap<>();
        dashboardData.put("statistics", stats);
        dashboardData.put("recentOrders", recentOrders);

        return Result.success(dashboardData);
    }
}

// 3. 测试示例
@SpringBootTest
class ForwarderDashboardServiceTest {
    @Autowired
    private ForwarderDashboardService dashboardService;

    @MockBean
    private ForwarderOrderService orderService;
    @MockBean
    private BiddingService biddingService;

    @Test
    void testGetDashboardStatistics() {
        Long forwarderId = 1L;
        // 模拟依赖服务的返回
        when(orderService.countByStatus(forwarderId, "ONGOING")).thenReturn(10L);
        when(biddingService.countByStatus(forwarderId, "PENDING")).thenReturn(5L);

        // 调用服务
        ForwarderDashboardStatisticsDTO stats = dashboardService.getDashboardStatistics(forwarderId);

        // 断言结果
        assertThat(stats).isNotNull();
        assertThat(stats.getOngoingOrdersCount()).isEqualTo(10L);
        assertThat(stats.getPendingBiddingsCount()).isEqualTo(5L);
    }
}
```

## 6. 注意事项

*   **性能优化**: `getDashboardStatistics` 是性能敏感点。除了缓存，还可以考虑创建数据库物化视图（Materialized View）或使用数据仓库（Data Warehouse）进行预计算，以空间换时间，为仪表盘提供毫秒级响应。
*   **权限控制**: 服务实现和控制器都必须严格校验 `forwarderId` 是否与当前登录用户匹配，防止数据泄露。
*   **数据一致性**: 由于数据来自多个源头，需要考虑最终一致性。如果实时性要求不高，缓存是可接受的。如果要求高，则需要更复杂的分布式事务或事件溯源机制来保证。
*   **可扩展性**: 随着业务发展，仪表盘上可能需要展示更多维度的统计数据。`ForwarderDashboardStatisticsDTO` 需要能够方便地增加字段，服务实现也应易于扩展以聚合新的数据源。
*   **避免N+1查询**: 在聚合数据时，要警惕N+1查询问题。应尽可能使用 `IN` 查询或 `GROUP BY` 等SQL功能来批量获取数据。
*   **异步执行**: 如示例所示，使用 `CompletableFuture` 可以并行执行多个独立的数据库查询，从而显著减少 `getDashboardStatistics` 方法的总响应时间。
*   **错误处理**: 如果某个聚合查询失败，服务应如何响应？是返回部分数据还是直接报错？这需要根据产品需求来定。一种健壮的做法是，即使部分数据获取失败，也返回一个包含部分数据和错误信息的DTO。