package com.purchase.forwarding.service;

import com.purchase.forwarding.dto.ForwarderDashboardStatisticsDTO;
import com.purchase.forwarding.order.vo.ForwarderOrderVO;

import java.util.List;

/**
 * 货代仪表盘服务接口
 */
public interface ForwarderDashboardService {
    
    /**
     * 获取货代仪表盘统计数据
     *
     * @param forwarderId 货代ID
     * @return 统计数据
     */
    ForwarderDashboardStatisticsDTO getDashboardStatistics(Long forwarderId);
    
    /**
     * 获取货代最近订单列表
     *
     * @param forwarderId 货代ID
     * @param limit 限制数量
     * @return 最近订单列表
     */
    List<ForwarderOrderVO> getRecentOrders(Long forwarderId, Integer limit);
} 