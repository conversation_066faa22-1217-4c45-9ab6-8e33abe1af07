package com.purchase.forwarding.requirement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.purchase.common.exception.BusinessException;
import com.purchase.common.util.SecurityContextUtil;
import com.purchase.forwarding.bidding.mapper.ForwardingBiddingMapper;
import com.purchase.forwarding.requirement.dto.request.ForwardingRequirementCreateRequest;
import com.purchase.forwarding.requirement.dto.request.ForwardingRequirementSearchRequest;
import com.purchase.forwarding.requirement.dto.request.ForwardingRequirementUpdateRequest;
import com.purchase.forwarding.requirement.dto.response.ForwardingRequirementDetailResponse;
import com.purchase.forwarding.requirement.entity.ForwardingRequirement;
import com.purchase.forwarding.requirement.mapper.ForwardingRequirementMapper;
import com.purchase.forwarding.requirement.service.ForwardingRequirementService;
import com.purchase.forwarding.requirement.vo.ForwardingRequirementVO;
import com.purchase.forwarding.requirement.dto.ForwardingRequirementStatistics;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.purchase.user.service.UserService;
import com.purchase.message.notification.service.NotificationService;
import com.purchase.message.notification.entity.Notification;
import com.purchase.message.notification.enums.NotificationType;
import com.purchase.user.mapper.UserMapper;
import com.purchase.user.entity.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ForwardingRequirementServiceImpl extends ServiceImpl<ForwardingRequirementMapper, ForwardingRequirement> 
        implements ForwardingRequirementService {

    private final ForwardingRequirementMapper forwardingRequirementMapper;

    private final UserService userService;
    private final ForwardingBiddingMapper forwardingBiddingMapper;
    private final NotificationService notificationService;
    private final UserMapper userMapper;



    @Override
    @Transactional
    public ForwardingRequirementDetailResponse createRequirement(ForwardingRequirementCreateRequest request) {
        // 创建需求
        ForwardingRequirement requirement = new ForwardingRequirement();
        BeanUtils.copyProperties(request, requirement);
        
        // 简化订单验证逻辑（移除直接依赖）
        if (request.getOrderId() != null) {
            // 检查该订单是否已有货代需求
            ForwardingRequirement existingRequirement = forwardingRequirementMapper.findByOrderId(request.getOrderId());
            if (existingRequirement != null) {
                throw new BusinessException(400, "该订单已经有货代需求");
            }

            // 注意：订单存在性验证和权限验证已移除，改为在业务层面通过其他方式验证
            // 港口信息和贸易条款的继承也已移除，需要在前端或其他地方处理
        }
        
        // 设置创建者和状态
        requirement.setCreatedBy(SecurityContextUtil.getCurrentUserId());
        // 设置创建者角色
        String role = SecurityContextUtil.getCurrentUserRole();
        requirement.setCreatorRole(request.getCreatorRole() != null ? request.getCreatorRole() : role);
        requirement.setStatus("open");
        
        // 处理文件
        // if (request.getDocuments() != null && request.getDocuments().length > 0) {
        //     requirement.setDocuments(String.join(",", request.getDocuments()));
        // }
        
        // 保存到数据库
        save(requirement);
        
        // 发送通知
        sendForwardingRequirementPublishedNotifications(requirement);
        
        // 返回详情
        return convertToDetailResponse(requirement);
    }

    @Override
    @Transactional
    public ForwardingRequirementDetailResponse updateRequirement(ForwardingRequirementUpdateRequest request) {
        // 获取需求
        ForwardingRequirement requirement = getById(request.getId());
        if (requirement == null || "1".equals(requirement.getDeleted())) {
            throw new BusinessException(404, "需求不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        if (!"admin".equals(role) && !requirement.getCreatedBy().equals(currentUserId)) {
            throw new BusinessException(403, "无权修改此需求");
        }
        
        // 状态检查
        if (!"open".equals(requirement.getStatus()) && !"in_progress".equals(requirement.getStatus())) {
            throw new BusinessException(400, "只能修改开放或进行中状态的需求");
        }
        
        // 更新需求
        if (request.getTitle() != null) requirement.setTitle(request.getTitle());
        if (request.getProductName() != null) requirement.setProductName(request.getProductName());
        if (request.getHsCode() != null) requirement.setHsCode(request.getHsCode());
        if (request.getDestinationCountry() != null) requirement.setDestinationCountry(request.getDestinationCountry());
        if (request.getDestinationAddress() != null) requirement.setDestinationAddress(request.getDestinationAddress());
        if (request.getOriginCountry() != null) requirement.setOriginCountry(request.getOriginCountry());
        if (request.getOriginAddress() != null) requirement.setOriginAddress(request.getOriginAddress());
        if (request.getContactPerson() != null) requirement.setContactPerson(request.getContactPerson());
        if (request.getContactPhone() != null) requirement.setContactPhone(request.getContactPhone());
        if (request.getExpectedDeliveryDate() != null) requirement.setExpectedDeliveryDate(request.getExpectedDeliveryDate());
        if (request.getDescription() != null) requirement.setDescription(request.getDescription());
        if (request.getCargoType() != null) requirement.setCargoType(request.getCargoType());
        if (request.getProductCategory() != null) requirement.setProductCategory(request.getProductCategory());
        if (request.getCargoWeight() != null) requirement.setCargoWeight(request.getCargoWeight());
        if (request.getCargoVolume() != null) requirement.setCargoVolume(request.getCargoVolume());
        if (request.getPackageCount() != null) requirement.setPackageCount(request.getPackageCount());
        if (request.getSpecialRequirements() != null) requirement.setSpecialRequirements(request.getSpecialRequirements());
        if (request.getBudget() != null) requirement.setBudget(request.getBudget());
        if (request.getDeliveryTerms() != null) requirement.setDeliveryTerms(request.getDeliveryTerms());
        if (request.getProductImages() != null) requirement.setProductImages(request.getProductImages());
        if (request.getProductVideos() != null) requirement.setProductVideos(request.getProductVideos());
        
        // 更新港口信息
        if (request.getPortOfLoadingName() != null) requirement.setPortOfLoadingName(request.getPortOfLoadingName());
        if (request.getPortOfLoadingCode() != null) requirement.setPortOfLoadingCode(request.getPortOfLoadingCode());
        if (request.getPortOfDestinationName() != null) requirement.setPortOfDestinationName(request.getPortOfDestinationName());
        if (request.getPortOfDestinationCode() != null) requirement.setPortOfDestinationCode(request.getPortOfDestinationCode());
        
        // 更新创建者角色（只允许管理员更新）
        if (request.getCreatorRole() != null && "admin".equals(role)) {
            requirement.setCreatorRole(request.getCreatorRole());
        }
        
        // 处理文件
        // if (request.getDocuments() != null && request.getDocuments().length > 0) {
        //     requirement.setDocuments(String.join(",", request.getDocuments()));
        // }
        
        // 更新时间
        requirement.setUpdatedAt(LocalDateTime.now());
        
        // 保存更新
        updateById(requirement);
        
        // 返回更新后的详情
        return convertToDetailResponse(requirement);
    }

    @Override
    @Transactional
    public void deleteRequirement(Long id) {
        // 获取需求
        ForwardingRequirement requirement = getById(id);
        if (requirement == null || "1".equals(requirement.getDeleted())) {
            throw new BusinessException(404, "需求不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        if (!"admin".equals(role) && !requirement.getCreatedBy().equals(currentUserId)) {
            throw new BusinessException(403, "无权删除此需求");
        }
        
        // 状态检查
        if (!"open".equals(requirement.getStatus()) && !"cancelled".equals(requirement.getStatus())) {
            throw new BusinessException(400, "只能删除开放或已取消状态的需求");
        }
        
        // 逻辑删除
        removeById(id);
    }

    @Override
    public ForwardingRequirementDetailResponse getRequirementById(Long id) {
        ForwardingRequirement requirement = getById(id);
        if (requirement == null || "1".equals(requirement.getDeleted())) {
            throw new BusinessException(404, "需求不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        
        // admin和forwarder可以查看所有需求
        if (!"admin".equals(role) && !"forwarder".equals(role)) {
            // buyer和seller只能查看自己创建的需求
            if (!requirement.getCreatedBy().equals(currentUserId)) {
                throw new BusinessException(403, "无权查看此需求");
            }
        }
        
        return convertToDetailResponse(requirement);
    }

    @Override
    public ForwardingRequirementDetailResponse getRequirementByOrderId(Long orderId) {
        ForwardingRequirement requirement = forwardingRequirementMapper.findByOrderId(orderId);
        if (requirement == null || "1".equals(requirement.getDeleted())) {
            throw new BusinessException(404, "此订单尚未创建货代需求");
        }
        
        // 权限验证
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        
        // admin和forwarder可以查看所有需求
        if (!"admin".equals(role) && !"forwarder".equals(role)) {
            // buyer和seller只能查看自己创建的需求
            if (!requirement.getCreatedBy().equals(currentUserId)) {
                throw new BusinessException(403, "无权查看此需求");
            }
        }
        
        return convertToDetailResponse(requirement);
    }

    @Override
    public IPage<ForwardingRequirementVO> searchRequirements(ForwardingRequirementSearchRequest request) {
        // 构建查询条件
        LambdaQueryWrapper<ForwardingRequirement> queryWrapper = buildSearchQueryWrapper(request);
        
        // 分页查询
        Page<ForwardingRequirement> page = new Page<>(request.getPage(), request.getSize());
        IPage<ForwardingRequirement> resultPage = page(page, queryWrapper);
        
        // 转换结果
        return resultPage.convert(this::convertToVO);
    }

    /**
     * 构建搜索查询条件
     * @param request 搜索请求参数
     * @return 查询条件构造器
     */
    private LambdaQueryWrapper<ForwardingRequirement> buildSearchQueryWrapper(ForwardingRequirementSearchRequest request) {
        LambdaQueryWrapper<ForwardingRequirement> queryWrapper = new LambdaQueryWrapper<>();
        
        // 基础条件：未删除
        queryWrapper.eq(ForwardingRequirement::getDeleted, "0");
        
        // 权限控制
        String role = SecurityContextUtil.getCurrentUserRole();
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        
        // admin和forwarder可以查看所有需求
        if (!"admin".equals(role) && !"forwarder".equals(role)) {
            // buyer和seller只能查看自己创建的需求
            queryWrapper.eq(ForwardingRequirement::getCreatedBy, currentUserId);
        } else {
            // 如果指定了特定用户ID，则按该用户过滤
            if (request.getSellerId() != null) {
                queryWrapper.eq(ForwardingRequirement::getCreatedBy, request.getSellerId());
            }
            if (request.getBuyerId() != null) {
                queryWrapper.eq(ForwardingRequirement::getCreatedBy, request.getBuyerId());
            }
            if (request.getForwarderId() != null) {
                // 查询货代参与过竞价的需求
                List<Long> requirementIds = forwardingBiddingMapper.findRequirementIdsByForwarderId(request.getForwarderId());
                if (requirementIds != null && !requirementIds.isEmpty()) {
                    queryWrapper.in(ForwardingRequirement::getId, requirementIds);
                } else {
                    // 如果没有参与过竞价，返回空结果
                    queryWrapper.eq(ForwardingRequirement::getId, -1L);
                }
            }
        }
        
        // 关键词搜索
        if (request.getKeyword() != null && !request.getKeyword().trim().isEmpty()) {
            String keyword = "%" + request.getKeyword().trim() + "%";
            queryWrapper.and(wrapper -> wrapper
                .like(ForwardingRequirement::getTitle, keyword)
                .or().like(ForwardingRequirement::getProductName, keyword)
                .or().like(ForwardingRequirement::getOriginAddress, keyword)
                .or().like(ForwardingRequirement::getOriginCountry, keyword)
                .or().like(ForwardingRequirement::getDestinationAddress, keyword)
                .or().like(ForwardingRequirement::getDestinationCountry, keyword)
                .or().like(ForwardingRequirement::getPortOfLoadingName, keyword)
                .or().like(ForwardingRequirement::getPortOfDestinationName, keyword)
                .or().like(ForwardingRequirement::getDescription, keyword)
            );
        }
        
        // 状态筛选
        if (request.getStatus() != null && !request.getStatus().trim().isEmpty()) {
            queryWrapper.eq(ForwardingRequirement::getStatus, request.getStatus());
        }
        
        // 运输类型筛选
        if (request.getShipmentType() != null && !request.getShipmentType().trim().isEmpty()) {
            queryWrapper.eq(ForwardingRequirement::getShipmentType, request.getShipmentType());
        }
        
        // 贸易条款筛选
        if (request.getDeliveryTerms() != null && !request.getDeliveryTerms().trim().isEmpty()) {
            queryWrapper.eq(ForwardingRequirement::getDeliveryTerms, request.getDeliveryTerms());
        }
        
        // 排序
        String sortBy = request.getSortBy() != null ? request.getSortBy() : "createdAt";
        String sortOrder = request.getSortOrder() != null ? request.getSortOrder() : "desc";
        
        switch (sortBy) {
            case "createdAt":
                if ("asc".equals(sortOrder)) {
                    queryWrapper.orderByAsc(ForwardingRequirement::getCreatedAt);
                } else {
                    queryWrapper.orderByDesc(ForwardingRequirement::getCreatedAt);
                }
                break;
            case "cargoWeight":
                if ("asc".equals(sortOrder)) {
                    queryWrapper.orderByAsc(ForwardingRequirement::getCargoWeight);
                } else {
                    queryWrapper.orderByDesc(ForwardingRequirement::getCargoWeight);
                }
                break;
            case "expectedDeliveryDate":
                if ("asc".equals(sortOrder)) {
                    queryWrapper.orderByAsc(ForwardingRequirement::getExpectedDeliveryDate);
                } else {
                    queryWrapper.orderByDesc(ForwardingRequirement::getExpectedDeliveryDate);
                }
                break;
            case "biddingCount":
                // 竞价数量排序需要特殊处理，暂时按创建时间排序
                if ("asc".equals(sortOrder)) {
                    queryWrapper.orderByAsc(ForwardingRequirement::getCreatedAt);
                } else {
                    queryWrapper.orderByDesc(ForwardingRequirement::getCreatedAt);
                }
                break;
            default:
                queryWrapper.orderByDesc(ForwardingRequirement::getCreatedAt);
        }
        
        return queryWrapper;
    }

    @Override
    public IPage<ForwardingRequirementVO> getRequirementList(Long sellerId, String status, Integer current, Integer size) {
        // 构建查询条件
        LambdaQueryWrapper<ForwardingRequirement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForwardingRequirement::getDeleted, "0");
        
        // 根据角色过滤
        String role = SecurityContextUtil.getCurrentUserRole();
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        
        // admin和forwarder可以查看所有需求
        if (!"admin".equals(role) && !"forwarder".equals(role)) {
            // buyer和seller只能查看自己创建的需求
            queryWrapper.eq(ForwardingRequirement::getCreatedBy, currentUserId);
        } else if (sellerId != null) {
            // 如果是admin或forwarder，并且指定了sellerId参数，则按sellerId过滤
            queryWrapper.eq(ForwardingRequirement::getCreatedBy, sellerId);
        }
        
        // 状态过滤
        if (status != null && !status.isEmpty()) {
            // 支持逗号分隔的多状态查询
            List<String> statusList = Arrays.asList(status.split(","));
            // 过滤掉空字符串，以防输入如 ",PUBLISHED"
            statusList = statusList.stream().filter(s -> !s.trim().isEmpty()).collect(Collectors.toList());
            if (!statusList.isEmpty()) {
                queryWrapper.in(ForwardingRequirement::getStatus, statusList);
            }
        }
        
        // 排序
        queryWrapper.orderByDesc(ForwardingRequirement::getCreatedAt);
        
        // 分页查询
        Page<ForwardingRequirement> page = new Page<>(current, size);
        IPage<ForwardingRequirement> resultPage = page(page, queryWrapper);
        
        // 转换结果
        return resultPage.convert(this::convertToVO);
    }

    @Override
    public List<ForwardingRequirementVO> getMyRequirements(Long sellerId) {
        // 验证参数
        if (sellerId == null) {
            sellerId = SecurityContextUtil.getCurrentUserId();
        }

        // 当前用户角色
        String role = SecurityContextUtil.getCurrentUserRole();
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        
        // admin和forwarder可以查看指定用户的需求
        // 其他角色只能查看自己的需求
        if (!"admin".equals(role) && !"forwarder".equals(role) && !currentUserId.equals(sellerId)) {
            return new ArrayList<>();
        }
        
        // 查询需求
        List<ForwardingRequirement> requirements = forwardingRequirementMapper.findByCreatedBy(sellerId);
        
        // 转换结果
        return requirements.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ForwardingRequirementVO> getRequirementsByBuyerId(Long buyerId) {
        // 验证参数
        if (buyerId == null) {
            buyerId = SecurityContextUtil.getCurrentUserId();
        }

        // 当前用户角色
        String role = SecurityContextUtil.getCurrentUserRole();
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        
        // admin和forwarder可以查看指定用户的需求
        // 其他角色只能查看自己的需求
        if (!"admin".equals(role) && !"forwarder".equals(role) && !currentUserId.equals(buyerId)) {
            return new ArrayList<>();
        }
        
        // 直接查询创建者为buyerId的需求，并且创建者角色为buyer
        // 如果创建者角色字段为空，则查询创建者为buyerId的所有需求
        List<ForwardingRequirement> requirements;
        try {
            requirements = forwardingRequirementMapper.findByCreatedByAndRole(buyerId, "buyer");
            // 如果没有查到，尝试查询所有
            if (requirements == null || requirements.isEmpty()) {
                requirements = forwardingRequirementMapper.findByCreatedBy(buyerId);
            }
        } catch (Exception e) {
            // 如果查询失败（可能是creator_role字段尚未添加），则使用原来的方法
            requirements = forwardingRequirementMapper.findByCreatedBy(buyerId);
        }
        
        // 转换结果
        return requirements.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean updateStatus(Long id, String status) {
        // 获取需求
        ForwardingRequirement requirement = getById(id);
        if (requirement == null || "1".equals(requirement.getDeleted())) {
            throw new BusinessException(404, "需求不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        
        // 使用更全面的权限验证
        boolean hasPermission = false;
        
        // 管理员、货代始终有权限
        if ("admin".equals(role) || "forwarder".equals(role)) {
            hasPermission = true;
        }
        // 需求创建者有权限
        else if (requirement.getCreatedBy().equals(currentUserId)) {
            hasPermission = true;
        }
        // 简化权限检查（移除对订单服务的直接依赖）
        else if (("buyer".equals(role) || "seller".equals(role)) && requirement.getOrderId() != null) {
            // 注意：订单关联权限验证已简化，实际项目中可通过其他方式实现
            log.info("用户 {} 尝试访问关联订单 {} 的货代需求", currentUserId, requirement.getOrderId());
            hasPermission = true; // 临时允许，实际项目中需要更严格的验证
        }
        
        // 如果没有权限，抛出异常
        if (!hasPermission) {
            throw new BusinessException(403, "无权更新此需求状态，只有需求创建者、订单关联的买家/卖家、货代或管理员可以更新");
        }
        
        // 验证状态值
        List<String> validStatuses = Arrays.asList("open", "in_progress", "completed", "cancelled");
        if (!validStatuses.contains(status)) {
            throw new BusinessException(400, "无效的状态值");
        }
        
        // 状态流转验证
        String currentStatus = requirement.getStatus();
        if ("completed".equals(currentStatus) && !"completed".equals(status)) {
            throw new BusinessException(400, "已完成的需求不能更改状态");
        }
        
        // 更新状态
        requirement.setStatus(status);
        requirement.setUpdatedAt(LocalDateTime.now());
        
        return updateById(requirement);
    }
    
    // 辅助方法：将实体转换为详情响应
    private ForwardingRequirementDetailResponse convertToDetailResponse(ForwardingRequirement requirement) {
        ForwardingRequirementDetailResponse response = new ForwardingRequirementDetailResponse();
        BeanUtils.copyProperties(requirement, response);
        
        // 处理文件列表
        // if (requirement.getDocuments() != null && !requirement.getDocuments().isEmpty()) {
        //     response.setDocuments(Arrays.asList(requirement.getDocuments().split(",")));
        // } else {
        //     response.setDocuments(new ArrayList<>());
        // }

        // 处理产品图片列表
        if (requirement.getProductImages() != null && !requirement.getProductImages().isEmpty()) {
            response.setProductImages(Arrays.asList(requirement.getProductImages().split(",")));
        } else {
            response.setProductImages(new ArrayList<>());
        }
        
        // 处理产品视频列表
        if (requirement.getProductVideos() != null && !requirement.getProductVideos().isEmpty()) {
            response.setProductVideos(Arrays.asList(requirement.getProductVideos().split(",")));
        } else {
            response.setProductVideos(new ArrayList<>());
        }

        // 设置OrderId，可能为null（独立货代需求）
        response.setOrderId(requirement.getOrderId());
        
        // 确保createdBy字段被正确设置
        response.setCreatedBy(requirement.getCreatedBy());
        
        // 确保creatorRole字段被正确设置
        response.setCreatorRole(requirement.getCreatorRole());
        
        // 获取创建者名称
        response.setCreatorName(getCreatorName(requirement.getCreatedBy()));
        
        // 获取竞价数量
        response.setBiddingCount(getBiddingCount(requirement.getId()));
        
        return response;
    }
    
    // 辅助方法：将实体转换为视图对象
    private ForwardingRequirementVO convertToVO(ForwardingRequirement requirement) {
        ForwardingRequirementVO vo = new ForwardingRequirementVO();
        BeanUtils.copyProperties(requirement, vo);
        
        // 确保createdBy字段被正确设置
        vo.setCreatedBy(requirement.getCreatedBy());
        
        // 确保creatorRole字段被正确设置
        vo.setCreatorRole(requirement.getCreatorRole());
        
        // 设置默认值
        vo.setOriginAddress("中国");
        
        // 处理产品图片列表
        if (requirement.getProductImages() != null && !requirement.getProductImages().isEmpty()) {
            vo.setProductImages(Arrays.asList(requirement.getProductImages().split(",")));
        } else {
            vo.setProductImages(new ArrayList<>());
        }
        
        // 处理产品视频列表
        if (requirement.getProductVideos() != null && !requirement.getProductVideos().isEmpty()) {
            vo.setProductVideos(Arrays.asList(requirement.getProductVideos().split(",")));
        } else {
            vo.setProductVideos(new ArrayList<>());
        }
        
        // 简化订单信息获取（移除对订单服务的直接依赖）
        if (requirement.getOrderId() != null) {
            // 注意：订单信息获取已简化，实际项目中可通过其他方式获取
            vo.setOrderNumber("ORDER-" + requirement.getOrderId()); // 简化的订单编号
            log.debug("关联订单ID: {}", requirement.getOrderId());
        }
        
        // 获取竞价数量
        vo.setBiddingCount(getBiddingCount(requirement.getId()));
        
//        // 设置预计提货日期 - 可以是预计交付日期前的几天
//        if (requirement.getExpectedDeliveryDate() != null) {
//            // 假设提货日期是交付日期前7天
//            vo.setExpectedPickupDate(requirement.getExpectedDeliveryDate().minusDays(7));
//        }
        
        return vo;
    }
    
    // 辅助方法：获取创建者名称
    private String getCreatorName(Long userId) {
        // 这里应该调用用户服务获取用户名称
        // 简化实现，实际项目中需要替换为真实的用户服务调用
        if (userId == null) {
            return null;
        } else {
            return userService.getUsernameById(userId);
        }

    }
    
    // 辅助方法：获取竞价数量
    private Integer getBiddingCount(Long requirementId) {
        // 这里应该调用竞价服务获取竞价数量
        // 简化实现，实际项目中需要替换为真实的竞价服务调用
        return 0;
    }


    @Override
    public ForwardingRequirement getById(Long requirementId) {
        return forwardingRequirementMapper.findById(requirementId);
    }

    public Long getRequirementIdByOrderId(Long orderId){
        return forwardingRequirementMapper.getRequirementIdByOrderId(orderId);
    }
    
    @Override
    public List<ForwardingRequirementVO> getRequirementsByForwarderId(Long forwarderId) {
        // 1. 从货代竞价表中获取该货代参与过竞价的所有需求ID
        List<Long> requirementIds = forwardingBiddingMapper.findRequirementIdsByForwarderId(forwarderId);
        
        if (requirementIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 2. 根据需求ID批量查询需求详情
        List<ForwardingRequirement> requirements = listByIds(requirementIds);
        
        // 3. 转换为VO对象
        return requirements.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
    
    @Override
    public IPage<ForwardingRequirementVO> getRequirementPageByForwarderId(Long forwarderId, String status, Integer current, Integer size) {
        // 1. 从货代竞价表中获取该货代参与过竞价的所有需求ID
        List<Long> requirementIds = forwardingBiddingMapper.findRequirementIdsByForwarderId(forwarderId);
        
        if (requirementIds.isEmpty()) {
            // 返回空页
            Page<ForwardingRequirementVO> emptyPage = new Page<>(current, size);
            emptyPage.setRecords(new ArrayList<>());
            emptyPage.setTotal(0);
            return emptyPage;
        }
        
        // 2. 创建分页对象
        Page<ForwardingRequirement> page = new Page<>(current, size);
        
        // 3. 构建查询条件
        LambdaQueryWrapper<ForwardingRequirement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ForwardingRequirement::getId, requirementIds);
        
        // 如果指定了状态，添加状态过滤
        if (status != null && !status.isEmpty()) {
            queryWrapper.eq(ForwardingRequirement::getStatus, status);
        }
        
        // 按更新时间降序排序，最新的在前面
        queryWrapper.orderByDesc(ForwardingRequirement::getUpdatedAt);
        
        // 4. 执行分页查询
        Page<ForwardingRequirement> resultPage = page(page, queryWrapper);
        
        // 5. 转换为VO分页对象
        Page<ForwardingRequirementVO> voPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        List<ForwardingRequirementVO> voList = resultPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        voPage.setRecords(voList);
        
        return voPage;
    }

    /**
     * 发送货代需求发布通知
     * 异步发送给所有货代和管理员
     * 
     * @param requirement 已创建的货代需求
     */
    @Async("taskExecutor")
    private void sendForwardingRequirementPublishedNotifications(ForwardingRequirement requirement) {
        try {
            log.info("开始发送货代需求发布通知，需求ID: {}", requirement.getId());
            
            // 通知所有货代
            List<User> forwarderUsers = userMapper.selectAllForwarders();
            for (User forwarder : forwarderUsers) {
                Notification forwarderNotification = new Notification();
                forwarderNotification.setType(NotificationType.FORWARDING_REQUIREMENT_PUBLISHED.getCode());
                forwarderNotification.setTitle("有新的货代需求发布");
                forwarderNotification.setContent("新的货代需求：" + requirement.getTitle());
                forwarderNotification.setReceiverId(forwarder.getId());
                forwarderNotification.setReceiverRole("forwarder");
                forwarderNotification.setRelatedId(requirement.getId());
                notificationService.createNotification(forwarderNotification);
            }
            
            // 通知所有管理员
            List<User> adminUsers = userMapper.selectAllAdmins();
            for (User admin : adminUsers) {
                Notification adminNotification = new Notification();
                adminNotification.setType(NotificationType.FORWARDING_REQUIREMENT_PUBLISHED.getCode());
                adminNotification.setTitle("有新的货代需求发布");
                adminNotification.setContent("新的货代需求：" + requirement.getTitle());
                adminNotification.setReceiverId(admin.getId());
                adminNotification.setReceiverRole("admin");
                adminNotification.setRelatedId(requirement.getId());
                notificationService.createNotification(adminNotification);
            }
            
            log.info("货代需求发布通知发送成功，需求ID: {}", requirement.getId());
        } catch (Exception e) {
            log.error("发送货代需求发布通知失败，需求ID: {}", requirement.getId(), e);
        }
    }

    @Override
    public ForwardingRequirementStatistics getStatistics() {
        // 总需求数
        Long totalRequirements = forwardingRequirementMapper.selectCount(null);
        
        // 按状态统计
        QueryWrapper<ForwardingRequirement> openWrapper = new QueryWrapper<>();
        openWrapper.eq("status", "open");
        Long openRequirements = forwardingRequirementMapper.selectCount(openWrapper);
        
        QueryWrapper<ForwardingRequirement> inProgressWrapper = new QueryWrapper<>();
        inProgressWrapper.eq("status", "in_progress");
        Long inProgressRequirements = forwardingRequirementMapper.selectCount(inProgressWrapper);
        
        QueryWrapper<ForwardingRequirement> completedWrapper = new QueryWrapper<>();
        completedWrapper.eq("status", "completed");
        Long completedRequirements = forwardingRequirementMapper.selectCount(completedWrapper);
        
        QueryWrapper<ForwardingRequirement> cancelledWrapper = new QueryWrapper<>();
        cancelledWrapper.eq("status", "cancelled");
        Long cancelledRequirements = forwardingRequirementMapper.selectCount(cancelledWrapper);
        
        // 按运输类型统计
        QueryWrapper<ForwardingRequirement> fclWrapper = new QueryWrapper<>();
        fclWrapper.eq("shipment_type", "FCL");
        Long fclRequirements = forwardingRequirementMapper.selectCount(fclWrapper);
        
        QueryWrapper<ForwardingRequirement> lclWrapper = new QueryWrapper<>();
        lclWrapper.eq("shipment_type", "LCL");
        Long lclRequirements = forwardingRequirementMapper.selectCount(lclWrapper);
        
        return ForwardingRequirementStatistics.builder()
                .totalRequirements(totalRequirements)
                .openRequirements(openRequirements)
                .inProgressRequirements(inProgressRequirements)
                .completedRequirements(completedRequirements)
                .cancelledRequirements(cancelledRequirements)
                .fclRequirements(fclRequirements)
                .lclRequirements(lclRequirements)
                .build();
    }
}