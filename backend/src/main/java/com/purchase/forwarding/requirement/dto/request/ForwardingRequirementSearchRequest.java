package com.purchase.forwarding.requirement.dto.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

/**
 * 货代需求搜索请求DTO
 * 用于前端搜索和筛选货代需求
 */
@Data
public class ForwardingRequirementSearchRequest {

    /**
     * 关键词搜索
     * 支持搜索标题、起始地址、目的地址、港口名称等字段
     */
    private String keyword;

    /**
     * 需求状态筛选
     * 可选值：draft(草稿)、published(已发布)、in_progress(进行中)、completed(已完成)、cancelled(已取消)、closed(已关闭)
     */
    @Pattern(regexp = "^(draft|published|in_progress|completed|cancelled|closed)$", 
             message = "状态值必须是：draft、published、in_progress、completed、cancelled、closed 中的一个")
    private String status;

    /**
     * 运输类型筛选
     * 可选值：FCL(整柜)、LCL(拼柜)
     */
    @Pattern(regexp = "^(FCL|LCL)$", message = "运输类型必须是：FCL 或 LCL")
    private String shipmentType;

    /**
     * 贸易条款筛选
     * 可选值：FOB、CIF、EXW、DDP、DDU
     */
    @Pattern(regexp = "^(FOB|CIF|EXW|DDP|DDU)$", 
             message = "贸易条款必须是：FOB、CIF、EXW、DDP、DDU 中的一个")
    private String deliveryTerms;

    /**
     * 排序字段
     * 可选值：createdAt(发布时间)、cargoWeight(货物重量)、expectedDeliveryDate(交货日期)、biddingCount(竞价数量)
     * 默认值：createdAt
     */
    @Pattern(regexp = "^(createdAt|cargoWeight|expectedDeliveryDate|biddingCount)$", 
             message = "排序字段必须是：createdAt、cargoWeight、expectedDeliveryDate、biddingCount 中的一个")
    private String sortBy = "createdAt";

    /**
     * 排序方向
     * 可选值：asc(升序)、desc(降序)
     * 默认值：desc
     */
    @Pattern(regexp = "^(asc|desc)$", message = "排序方向必须是：asc 或 desc")
    private String sortOrder = "desc";

    /**
     * 当前页码
     * 最小值：1
     * 默认值：1
     */
    @Min(value = 1, message = "页码必须大于等于1")
    private Integer page = 1;

    /**
     * 每页大小
     * 最小值：1，最大值：100
     * 默认值：10
     */
    @Min(value = 1, message = "每页大小必须大于等于1")
    private Integer size = 10;

    /**
     * 卖家ID（可选）
     * 用于筛选特定卖家的货代需求
     */
    private Long sellerId;

    /**
     * 买家ID（可选）
     * 用于筛选特定买家的货代需求
     */
    private Long buyerId;

    /**
     * 货代ID（可选）
     * 用于筛选特定货代参与的需求
     */
    private Long forwarderId;
} 