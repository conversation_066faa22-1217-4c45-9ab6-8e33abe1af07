package com.purchase.forwarding.requirement.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.forwarding.requirement.dto.ForwardingRequirementStatistics;
import com.purchase.forwarding.requirement.dto.request.ForwardingRequirementCreateRequest;
import com.purchase.forwarding.requirement.dto.request.ForwardingRequirementSearchRequest;
import com.purchase.forwarding.requirement.dto.request.ForwardingRequirementUpdateRequest;
import com.purchase.forwarding.requirement.dto.response.ForwardingRequirementDetailResponse;
import com.purchase.forwarding.requirement.entity.ForwardingRequirement;
import com.purchase.forwarding.requirement.vo.ForwardingRequirementVO;

import java.util.List;

public interface ForwardingRequirementService {
    
    ForwardingRequirementDetailResponse createRequirement(ForwardingRequirementCreateRequest request);
    
    ForwardingRequirementDetailResponse updateRequirement(ForwardingRequirementUpdateRequest request);
    
    void deleteRequirement(Long id);
    
    ForwardingRequirementDetailResponse getRequirementById(Long id);
    
    ForwardingRequirementDetailResponse getRequirementByOrderId(Long orderId);

    
    
    /**
     * 搜索货代需求列表（支持关键词、筛选、排序）
     * @param request 搜索请求参数
     * @return 分页货代需求列表
     */
    IPage<ForwardingRequirementVO> searchRequirements(ForwardingRequirementSearchRequest request);
    
    IPage<ForwardingRequirementVO> getRequirementList(Long sellerId, String status, Integer current, Integer size);
    
    List<ForwardingRequirementVO> getMyRequirements(Long sellerId);
    
    List<ForwardingRequirementVO> getRequirementsByBuyerId(Long buyerId);
    
    boolean updateStatus(Long id, String status);


    /**
     * 根据ID获取需求实体
     */
    ForwardingRequirement getById(Long requirementId);



    Long getRequirementIdByOrderId(Long orderId);
    
    /**
     * 获取货代参与过竞价的需求列表
     * @param forwarderId 货代ID
     * @return 需求视图对象列表
     */
    List<ForwardingRequirementVO> getRequirementsByForwarderId(Long forwarderId);
    
    /**
     * 分页获取货代参与过竞价的需求列表
     * @param forwarderId 货代ID
     * @param status 需求状态，可为空
     * @param current 当前页
     * @param size 每页大小
     * @return 分页需求视图对象
     */
    IPage<ForwardingRequirementVO> getRequirementPageByForwarderId(Long forwarderId, String status, Integer current, Integer size);

    /**
     * 获取货代需求统计数据
     */
    ForwardingRequirementStatistics getStatistics();
} 