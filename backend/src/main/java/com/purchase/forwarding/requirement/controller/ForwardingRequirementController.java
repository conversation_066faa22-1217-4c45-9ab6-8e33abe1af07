package com.purchase.forwarding.requirement.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.common.response.Result;
import com.purchase.common.util.SecurityContextUtil;
import com.purchase.forwarding.requirement.dto.ForwardingRequirementStatistics;
import com.purchase.forwarding.requirement.dto.request.ForwardingRequirementCreateRequest;
import com.purchase.forwarding.requirement.dto.request.ForwardingRequirementSearchRequest;
import com.purchase.forwarding.requirement.dto.request.ForwardingRequirementUpdateRequest;
import com.purchase.forwarding.requirement.dto.response.ForwardingRequirementDetailResponse;
import com.purchase.forwarding.requirement.service.ForwardingRequirementService;
import com.purchase.forwarding.requirement.vo.ForwardingRequirementVO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/v1/forwarding/requirements")
@RequiredArgsConstructor
public class ForwardingRequirementController {

    private final ForwardingRequirementService forwardingRequirementService;

    /**
     * 创建货代需求
     * @param request 创建货代需求的请求参数
     * @return 包含货代需求详情的响应实体
     */
    @PostMapping
    @PreAuthorize("hasAnyAuthority('seller', 'admin', 'buyer')")
    public ResponseEntity<Result<ForwardingRequirementDetailResponse>> createRequirement(
            @Valid @RequestBody ForwardingRequirementCreateRequest request) {
        ForwardingRequirementDetailResponse response = forwardingRequirementService.createRequirement(request);
        return ResponseEntity.ok(Result.success(response));
    }

    /**
     * 更新货代需求
     * @param id 货代需求ID
     * @param request 更新货代需求的请求参数
     * @return 包含更新后货代需求详情的响应实体
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('seller', 'admin', 'buyer')")
    public ResponseEntity<Result<ForwardingRequirementDetailResponse>> updateRequirement(
            @PathVariable Long id,
            @Valid @RequestBody ForwardingRequirementUpdateRequest request) {
        request.setId(id);
        ForwardingRequirementDetailResponse response = forwardingRequirementService.updateRequirement(request);
        return ResponseEntity.ok(Result.success(response));
    }

    /**
     * 删除货代需求
     * @param id 要删除的货代需求ID
     * @return 空响应实体
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('seller', 'admin', 'buyer')")
    public ResponseEntity<Result<Void>> deleteRequirement(@PathVariable Long id) {
        forwardingRequirementService.deleteRequirement(id);
        return ResponseEntity.ok(Result.success());
    }

    /**
     * 根据ID获取货代需求详情
     * @param id 货代需求ID
     * @return 包含货代需求详情的响应实体
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<ForwardingRequirementDetailResponse>> getRequirementById(@PathVariable Long id) {
        ForwardingRequirementDetailResponse response = forwardingRequirementService.getRequirementById(id);
        return ResponseEntity.ok(Result.success(response));
    }

    /**
     * 根据订单ID获取货代需求详情
     * @param orderId 订单ID
     * @return 包含货代需求详情的响应实体
     */
    @GetMapping("/order/{orderId}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')")
    public ResponseEntity<Result<ForwardingRequirementDetailResponse>> getRequirementByOrderId(@PathVariable Long orderId) {
        ForwardingRequirementDetailResponse response = forwardingRequirementService.getRequirementByOrderId(orderId);
        return ResponseEntity.ok(Result.success(response));
    }

    /**
     * 搜索货代需求列表（支持关键词、筛选、排序）
     * @param request 搜索请求参数
     * @return 包含分页货代需求列表的响应实体
     */
    @GetMapping("/search")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<Result<IPage<ForwardingRequirementVO>>> searchRequirements(
            @Valid ForwardingRequirementSearchRequest request) {
        IPage<ForwardingRequirementVO> page = forwardingRequirementService.searchRequirements(request);
        return ResponseEntity.ok(Result.success(page));
    }

    /**
     * 分页获取货代需求列表（保持兼容性）
     * @param sellerId 卖家ID（可选）
     * @param status 需求状态（可选）
     * @param current 当前页码，默认为1
     * @param size 每页大小，默认为10
     * @return 包含分页货代需求列表的响应实体
     */
    @GetMapping
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<Result<IPage<ForwardingRequirementVO>>> getRequirementList(
            @RequestParam(required = false) Long sellerId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        IPage<ForwardingRequirementVO> page = forwardingRequirementService.getRequirementList(sellerId, status, current, size);
        return ResponseEntity.ok(Result.success(page));
    }

    /**
     * 获取当前用户的货代需求列表
     * @param sellerId 卖家ID（可选）
     * @return 包含当前用户货代需求列表的响应实体
     */
    @GetMapping("/my")
    @PreAuthorize("hasAnyAuthority('seller', 'admin')")
    public ResponseEntity<Result<List<ForwardingRequirementVO>>> getMyRequirements(
            @RequestParam(required = false) Long sellerId) {
        List<ForwardingRequirementVO> requirements = forwardingRequirementService.getMyRequirements(sellerId);
        return ResponseEntity.ok(Result.success(requirements));
    }

    /**
     * 根据买家ID获取货代需求列表
     * @param buyerId 买家ID
     * @return 包含货代需求列表的响应实体
     */
    @GetMapping("/buyer/{buyerId}")
    @PreAuthorize("hasAnyAuthority('buyer', 'admin')")
    public ResponseEntity<Result<List<ForwardingRequirementVO>>> getRequirementsByBuyerId(
            @PathVariable Long buyerId) {
        List<ForwardingRequirementVO> requirements = forwardingRequirementService.getRequirementsByBuyerId(buyerId);
        return ResponseEntity.ok(Result.success(requirements));
    }

    /**
     * 更新货代需求状态
     * @param id 货代需求ID
     * @param status 新状态
     * @return 包含更新结果的响应实体
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("hasAnyAuthority('seller', 'admin', 'buyer')")
    public ResponseEntity<Result<Boolean>> updateStatus(
            @PathVariable Long id,
            @RequestParam String status) {
        boolean success = forwardingRequirementService.updateStatus(id, status);
        return ResponseEntity.ok(Result.success(success));
    }

    /**
     * 获取当前货代参与过竞价的需求列表
     * @return 包含需求列表的响应实体
     */
    @GetMapping("/forwarder/bidding")
    @PreAuthorize("hasAuthority('forwarder')")
    public ResponseEntity<Result<List<ForwardingRequirementVO>>> getForwarderBiddingRequirements() {
        // 获取当前登录的货代用户ID
        Long forwarderId = SecurityContextUtil.getCurrentUserId();
        List<ForwardingRequirementVO> requirements = forwardingRequirementService.getRequirementsByForwarderId(forwarderId);
        return ResponseEntity.ok(Result.success(requirements));
    }
    
    /**
     * 分页获取当前货代参与过竞价的需求列表
     * @param status 需求状态（可选）
     * @param current 当前页码，默认为1
     * @param size 每页大小，默认为10
     * @return 包含分页需求列表的响应实体
     */
    @GetMapping("/forwarder/bidding/page")
    @PreAuthorize("hasAuthority('forwarder')")
    public ResponseEntity<Result<IPage<ForwardingRequirementVO>>> getForwarderBiddingRequirementsPage(
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        // 获取当前登录的货代用户ID
        Long forwarderId = SecurityContextUtil.getCurrentUserId();
        IPage<ForwardingRequirementVO> page = forwardingRequirementService.getRequirementPageByForwarderId(forwarderId, status, current, size);
        return ResponseEntity.ok(Result.success(page));
    }

    /**
     * 获取货代需求统计数据
     */
    @GetMapping("/statistics")
    public ResponseEntity<Result<ForwardingRequirementStatistics>> getStatistics() {
        try {
            ForwardingRequirementStatistics statistics = forwardingRequirementService.getStatistics();
            return ResponseEntity.ok(Result.success(statistics));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Result.error("获取统计数据失败"));
        }
    }
}