## 类用途
转发需求统计结果DTO，用于构建采购数据看板

## 统计维度
| 维度类型 | 指标说明 | 计算方式 |
|---------|--------|---------|
| 时间趋势 | 每日/周/月需求数 | 按时间分组计数 |
| 状态分布 | 各状态需求占比 | 状态分组计数 |
| 分类排名 | Top分类需求数 | 分类分组计数 |
| 转发商效率 | 平均响应时间 | 投标创建时间-需求转发时间 |

## 核心字段
| 字段名 | 类型 | 描述 | 示例值 |
|-------|------|-----|--------|
| timeSeries | Map<String, Integer> | 时间序列数据 | {"2025-07": 120} |
| statusDistribution | Map<String, Integer> | 状态分布 | {"FORWARDED": 30} |  
| categoryTopN | List<CategoryStat> | 分类TopN | [{"name":"手机","count":50}] |
| avgResponseHours | BigDecimal | 平均响应时长 | 12.5 |

## 嵌套对象
### CategoryStat
| 字段名 | 描述 |
|-------|-----|
| name | 分类名称 |
| count | 需求数量 |
| percentage | 占比 |

## 使用示例
```java
// 看板数据获取示例
ForwardingRequirementStatistics stats = service.getRequirementStats(
    LocalDate.now().minusMonths(1),
    LocalDate.now(),
    "agent123"
);

// 前端使用示例
chartData = {
    labels: Object.keys(stats.timeSeries),
    data: Object.values(stats.timeSeries) 
}
```

## 注意事项
- 大数据量统计建议异步计算
- 敏感数据需权限过滤
- 时间范围限制最大1年