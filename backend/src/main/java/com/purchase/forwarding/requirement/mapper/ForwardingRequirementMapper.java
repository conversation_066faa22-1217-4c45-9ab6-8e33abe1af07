package com.purchase.forwarding.requirement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.purchase.forwarding.requirement.entity.ForwardingRequirement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ForwardingRequirementMapper extends BaseMapper<ForwardingRequirement> {
    
    @Select("SELECT * FROM forwarding_requirement WHERE id = #{requirementId} AND deleted = '0'")
    ForwardingRequirement findById(@Param("requirementId") Long requirementId);
    
    @Select("SELECT * FROM forwarding_requirement WHERE order_id = #{orderId} AND deleted = '0'")
    ForwardingRequirement findByOrderId(@Param("orderId") Long orderId);
    
    @Select("SELECT * FROM forwarding_requirement WHERE created_by = #{sellerId} AND deleted = '0'")
    List<ForwardingRequirement> findByCreatedBy(@Param("sellerId") Long sellerId);
    
    @Select("SELECT COUNT(*) FROM forwarding_requirement WHERE status = #{status} AND deleted = '0'")
    Integer countByStatus(@Param("status") String status);

    @Select("SELECT id FROM forwarding_requirement WHERE order_id = #{orderId} AND deleted = '0'")
    Long getRequirementIdByOrderId(@Param("orderId") Long orderId);
    
    @Select("SELECT * FROM forwarding_requirement WHERE created_by = #{userId} AND creator_role = #{role} AND deleted = '0'")
    List<ForwardingRequirement> findByCreatedByAndRole(@Param("userId") Long userId, @Param("role") String role);
}
