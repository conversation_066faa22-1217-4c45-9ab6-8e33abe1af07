## 类用途
转发需求搜索请求DTO，支持多条件组合查询

## 可搜索字段
| 字段名 | 类型 | 描述 | 查询类型 |
|-------|------|-----|---------|
| keyword | String | 关键词 | 模糊匹配 |
| statusList | List<String> | 状态集合 | 精确匹配 |
| categoryId | String | 分类ID | 精确匹配 |
| minCreateTime | LocalDateTime | 最小创建时间 | 范围查询 |
| maxCreateTime | LocalDateTime | 最大创建时间 | 范围查询 |
| forwardingAgentId | String | 转发商ID | 精确匹配 |
| pageable | PageRequest | 分页排序 | 非搜索条件 |

## 特殊查询逻辑
- 关键词同时匹配：需求编号/产品名称/备注
- 状态为空时默认查非终止状态
- 时间范围限制最大90天跨度

## 使用示例
```java
ForwardingRequirementSearchRequest request = new ForwardingRequirementSearchRequest(
    "手机", 
    List.of("FORWARDED", "BIDDING"), 
    "cat123",
    LocalDateTime.now().minusMonths(1),
    LocalDateTime.now(),
    "agent456",
    PageRequest.of(0, 10, Sort.by("createTime").descending())
);
```

## 注意事项
- 生产环境建议添加复杂查询限流
- 敏感字段需权限过滤（如转发商ID）
- 大数据量分页需优化