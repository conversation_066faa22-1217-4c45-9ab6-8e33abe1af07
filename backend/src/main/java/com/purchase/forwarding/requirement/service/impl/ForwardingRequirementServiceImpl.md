# ForwardingRequirementServiceImpl.md

## 1. 文件概述

`ForwardingRequirementServiceImpl.java` 是货代模块中 `ForwardingRequirementService` 接口的实现类，位于 `com.purchase.forwarding.requirement.service.impl` 包中。它继承自MyBatis-Plus的 `ServiceImpl`，提供了货代需求业务逻辑的具体实现。该服务负责处理货代需求的创建、更新、删除、查询以及状态变更等核心业务流程。它协调多个领域服务和仓储（如 `ForwardingRequirementMapper`, `UserService`, `ForwardingBiddingMapper`, `NotificationService`），并严格执行权限校验和业务规则，确保货代需求流程的正确性和数据一致性。

## 2. 核心功能

*   **货代需求创建**: 实现了货代需求的创建逻辑，包括权限校验、订单关联检查、创建者和状态设置，以及通知发送。
*   **货代需求更新**: 实现了货代需求的更新逻辑，支持部分字段更新和状态检查。
*   **货代需求删除**: 实现了货代需求的逻辑删除，并包含权限和状态检查（只允许删除 `open` 或 `cancelled` 状态的需求）。
*   **多维度查询**: 实现了根据需求ID、订单ID、创建者ID、买家ID、货代ID以及各种筛选条件进行查询，并根据用户角色进行数据过滤。
*   **货代参与竞价的需求查询**: 提供了查询货代参与过竞价的需求列表的功能，支持分页。
*   **货代需求状态变更**: 实现了需求状态的更新，并包含严格的状态转换校验和权限校验。
*   **通知机制**: 在货代需求发布时，异步发送通知给所有货代和管理员，确保信息及时触达。
*   **货代需求统计**: 提供了货代需求的统计功能，包括按状态和运输类型进行统计。
*   **数据转换**: 负责将请求DTO转换为实体，以及将实体转换为详情响应DTO或VO。

## 3. 接口说明

### 3.1 货代需求操作实现

#### createRequirement - 创建货代需求
*   **方法签名**: `ForwardingRequirementDetailResponse createRequirement(ForwardingRequirementCreateRequest request)`
*   **描述**: 实现了货代需求的创建逻辑。包括权限校验、订单关联检查、创建者和状态设置，以及通知发送。
*   **业务逻辑**: 
    1.  检查请求中是否包含 `orderId`，如果存在，则检查该订单是否已存在货代需求。
    2.  将请求DTO属性复制到 `ForwardingRequirement` 实体。
    3.  设置创建者ID和角色（从安全上下文获取），设置需求状态为 `open`。
    4.  保存 `ForwardingRequirement` 实体。
    5.  异步发送货代需求发布通知给所有货代和管理员。
    6.  转换为 `ForwardingRequirementDetailResponse` 返回。

#### updateRequirement - 更新货代需求
*   **方法签名**: `ForwardingRequirementDetailResponse updateRequirement(ForwardingRequirementUpdateRequest request)`
*   **描述**: 实现了货代需求的更新逻辑。支持部分字段更新，并包含权限和状态检查。
*   **业务逻辑**: 
    1.  获取并验证需求记录是否存在。
    2.  验证当前用户是否为需求创建者或管理员。
    3.  检查需求状态是否允许修改（只能修改 `open` 或 `in_progress` 状态的需求）。
    4.  根据请求更新需求的各个字段。
    5.  更新时间戳。
    6.  保存更新。
    7.  转换为 `ForwardingRequirementDetailResponse` 返回。

#### deleteRequirement - 删除货代需求
*   **方法签名**: `void deleteRequirement(Long id)`
*   **描述**: 实现了货代需求的逻辑删除。只允许删除 `open` 或 `cancelled` 状态的需求。
*   **业务逻辑**: 
    1.  获取并验证需求记录是否存在。
    2.  验证当前用户是否为需求创建者或管理员。
    3.  检查需求状态是否允许删除。
    4.  执行逻辑删除操作。

#### updateStatus - 更新货代需求状态
*   **方法签名**: `boolean updateStatus(Long id, String status)`
*   **描述**: 实现了货代需求状态的更新逻辑。包含状态转换校验和权限校验。
*   **业务逻辑**: 
    1.  获取并验证需求记录是否存在。
    2.  验证当前用户是否为需求创建者、订单关联的买家/卖家、货代或管理员。
    3.  验证状态值是否合法。
    4.  验证状态转换是否合法（例如，已完成的需求不能更改状态）。
    5.  更新需求状态。
    6.  保存更新。

### 3.2 货代需求查询实现

#### getRequirementById - 根据ID获取货代需求详情
*   **方法签名**: `ForwardingRequirementDetailResponse getRequirementById(Long id)`
*   **描述**: 实现了根据需求ID获取需求详情的逻辑，并根据用户角色进行权限校验。
*   **业务逻辑**: 
    1.  获取并验证需求记录是否存在。
    2.  根据当前用户角色（管理员、货代、买家、卖家）进行权限判断。
    3.  如果无权查看，抛出 `BusinessException`。
    4.  转换为 `ForwardingRequirementDetailResponse` 返回。

#### getRequirementByOrderId - 根据订单ID获取货代需求详情
*   **方法签名**: `ForwardingRequirementDetailResponse getRequirementByOrderId(Long orderId)`
*   **描述**: 实现了根据订单ID获取需求详情的逻辑，并根据用户角色进行权限校验。
*   **业务逻辑**: 
    1.  获取并验证需求记录是否存在。
    2.  根据当前用户角色（管理员、货代、买家、卖家）进行权限判断。
    3.  如果无权查看，抛出 `BusinessException`。
    4.  转换为 `ForwardingRequirementDetailResponse` 返回。

#### searchRequirements - 搜索货代需求列表
*   **方法签名**: `IPage<ForwardingRequirementVO> searchRequirements(ForwardingRequirementSearchRequest request)`
*   **描述**: 实现了分页搜索货代需求列表的逻辑，支持关键词、状态、运输类型、贸易条款等多种筛选和排序。
*   **业务逻辑**: 
    1.  调用 `buildSearchQueryWrapper` 辅助方法构建查询条件。
    2.  执行分页查询。
    3.  将查询结果转换为 `IPage<ForwardingRequirementVO>` 返回。

#### getRequirementList - 分页获取货代需求列表（兼容性接口）
*   **方法签名**: `IPage<ForwardingRequirementVO> getRequirementList(Long sellerId, String status, Integer current, Integer size)`
*   **描述**: 实现了分页获取货代需求列表的逻辑，支持按卖家ID和状态过滤，并根据用户角色进行权限过滤。
*   **业务逻辑**: 
    1.  构建 `LambdaQueryWrapper`，设置逻辑删除条件。
    2.  根据用户角色和 `sellerId` 参数添加权限过滤。
    3.  根据 `status` 添加状态过滤（支持多状态查询）。
    4.  按创建时间降序排序。
    5.  执行分页查询。
    6.  转换为 `IPage<ForwardingRequirementVO>` 返回。

#### getMyRequirements - 获取当前用户的货代需求列表
*   **方法签名**: `List<ForwardingRequirementVO> getMyRequirements(Long sellerId)`
*   **描述**: 实现了获取当前用户（或指定卖家）货代需求列表的逻辑，并进行权限校验。
*   **业务逻辑**: 
    1.  验证 `sellerId` 参数，如果为空则使用当前用户ID。
    2.  验证当前用户是否为该卖家或管理员。
    3.  调用 `forwardingRequirementMapper.findByCreatedBy` 查询需求列表。
    4.  转换为 `List<ForwardingRequirementVO>` 返回。

#### getRequirementsByBuyerId - 根据买家ID获取货代需求列表
*   **方法签名**: `List<ForwardingRequirementVO> getRequirementsByBuyerId(Long buyerId)`
*   **描述**: 实现了根据买家ID获取货代需求列表的逻辑，并进行权限校验。
*   **业务逻辑**: 
    1.  验证 `buyerId` 参数，如果为空则使用当前用户ID。
    2.  验证当前用户是否为该买家或管理员。
    3.  调用 `forwardingRequirementMapper.findByCreatedByAndRole` 查询需求列表。
    4.  转换为 `List<ForwardingRequirementVO>` 返回。

#### getRequirementsByForwarderId - 获取当前货代参与过竞价的需求列表
*   **方法签名**: `List<ForwardingRequirementVO> getRequirementsByForwarderId(Long forwarderId)`
*   **描述**: 实现了获取指定货代参与过竞价的需求列表的逻辑。
*   **业务逻辑**: 
    1.  调用 `forwardingBiddingMapper.findRequirementIdsByForwarderId` 获取货代参与过的所有需求ID。
    2.  根据这些ID批量查询需求详情。
    3.  转换为 `List<ForwardingRequirementVO>` 返回。

#### getRequirementPageByForwarderId - 分页获取当前货代参与过竞价的需求列表
*   **方法签名**: `IPage<ForwardingRequirementVO> getRequirementPageByForwarderId(Long forwarderId, String status, Integer current, Integer size)`
*   **描述**: 实现了分页获取指定货代参与过竞价的需求列表的逻辑，支持按状态过滤。
*   **业务逻辑**: 
    1.  调用 `forwardingBiddingMapper.findRequirementIdsByForwarderId` 获取货代参与过的所有需求ID。
    2.  构建分页和查询条件，过滤需求ID和状态。
    3.  执行分页查询。
    4.  转换为 `IPage<ForwardingRequirementVO>` 返回。

#### getStatistics - 获取货代需求统计数据
*   **方法签名**: `ForwardingRequirementStatistics getStatistics()`
*   **描述**: 实现了货代需求统计数据的获取逻辑，包括按状态和运输类型进行统计。
*   **业务逻辑**: 
    1.  调用 `forwardingRequirementMapper.selectCount` 统计总数和按状态、运输类型分类的数量。
    2.  构建 `ForwardingRequirementStatistics` 对象并返回。

### 3.3 辅助方法

#### convertToDetailResponse - 辅助方法：将实体转换为详情响应
*   **方法签名**: `private ForwardingRequirementDetailResponse convertToDetailResponse(ForwardingRequirement requirement)`
*   **描述**: 将 `ForwardingRequirement` 实体转换为 `ForwardingRequirementDetailResponse` DTO，并填充关联的订单ID、创建者名称和竞价数量等信息。

#### convertToVO - 辅助方法：将实体转换为视图对象
*   **方法签名**: `private ForwardingRequirementVO convertToVO(ForwardingRequirement requirement)`
*   **描述**: 将 `ForwardingRequirement` 实体转换为 `ForwardingRequirementVO`，并填充关联的订单编号、竞价数量和预计提货日期等信息。

#### buildSearchQueryWrapper - 构建搜索查询条件
*   **方法签名**: `private LambdaQueryWrapper<ForwardingRequirement> buildSearchQueryWrapper(ForwardingRequirementSearchRequest request)`
*   **描述**: 根据 `ForwardingRequirementSearchRequest` 中的参数构建MyBatis-Plus的查询条件。

#### getCreatorName - 辅助方法：获取创建者名称
*   **方法签名**: `private String getCreatorName(Long userId)`
*   **描述**: 通过 `userService` 获取用户名称，如果获取失败则返回默认值。

#### getBiddingCount - 辅助方法：获取竞价数量
*   **方法签名**: `private Integer getBiddingCount(Long requirementId)`
*   **描述**: 通过 `forwardingBiddingMapper` 获取指定需求下的竞价数量。

#### sendForwardingRequirementPublishedNotifications - 发送货代需求发布通知
*   **方法签名**: `private void sendForwardingRequirementPublishedNotifications(ForwardingRequirement requirement)`
*   **描述**: 异步发送货代需求发布通知给所有货代和管理员。

## 4. 业务规则

*   **DDD分层**: `ForwardingRequirementServiceImpl` 明确属于应用服务层，其职责是协调领域对象和仓储，实现业务用例。它不包含核心领域逻辑，而是委托给领域实体和领域服务。
*   **事务管理**: 所有涉及数据修改的方法都使用 `@Transactional` 注解，确保操作的原子性和数据一致性。
*   **权限校验**: 服务层通过 `SecurityContextUtil` 获取当前用户ID和角色，并根据业务逻辑进行细粒度的权限校验。例如，只有需求创建者或管理员可以修改/删除需求，不同角色对需求的可见性也不同。
*   **业务异常**: 服务在遇到业务规则冲突或数据不存在时，会抛出 `BusinessException`，并包含明确的错误码和信息，便于上层处理。
*   **数据转换**: 服务层负责将请求DTO转换为领域实体，以及将领域实体转换为前端所需的响应DTO或VO。`convertToDetailResponse` 和 `convertToVO` 辅助方法承担了这一职责。
*   **通知机制**: 在货代需求发布时，系统会异步发送通知给相关用户。`@Async` 注解确保了通知发送不会阻塞主业务流程。
*   **依赖注入**: 服务通过构造函数注入其依赖，遵循了依赖倒置原则。
*   **状态机**: 需求的状态流转（`status`）遵循严格的业务规则。例如，已完成的需求不能更改状态。
*   **动态查询**: `buildSearchQueryWrapper` 方法展示了如何构建复杂的动态查询，包括多条件过滤、关键词搜索和动态排序。
*   **日志记录**: 服务中使用了 `log.info` 和 `log.error` 记录关键操作和异常信息，这对于监控和问题排查非常重要。

## 5. 使用示例

```java
// 1. 在 ForwardingRequirementController 中调用 ForwardingRequirementService
@RestController
@RequestMapping("/api/v1/forwarding/requirements")
public class ForwardingRequirementController {
    @Autowired
    private ForwardingRequirementService forwardingRequirementService;

    @PostMapping
    @PreAuthorize("hasAnyAuthority('seller', 'admin', 'buyer')")
    public ResponseEntity<Result<ForwardingRequirementDetailResponse>> createRequirement(
            @Valid @RequestBody ForwardingRequirementCreateRequest request) {
        ForwardingRequirementDetailResponse response = forwardingRequirementService.createRequirement(request);
        return ResponseEntity.ok(Result.success(response));
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<Result<IPage<ForwardingRequirementVO>>> searchRequirements(
            @Valid ForwardingRequirementSearchRequest request) {
        IPage<ForwardingRequirementVO> page = forwardingRequirementService.searchRequirements(request);
        return ResponseEntity.ok(Result.success(page));
    }

    @PutMapping("/{id}/status")
    @PreAuthorize("hasAnyAuthority('seller', 'admin', 'buyer')")
    public ResponseEntity<Result<Boolean>> updateStatus(
            @PathVariable Long id,
            @RequestParam String status) {
        boolean success = forwardingRequirementService.updateStatus(id, status);
        return ResponseEntity.ok(Result.success(success));
    }
}

// 2. 测试示例
@SpringBootTest
class ForwardingRequirementServiceImplTest {
    @Autowired
    private ForwardingRequirementService forwardingRequirementService;

    @MockBean
    private ForwardingRequirementMapper forwardingRequirementMapper;
    @MockBean
    private UserService userService;
    @MockBean
    private ForwardingBiddingMapper forwardingBiddingMapper;
    @MockBean
    private NotificationService notificationService;
    @MockBean
    private UserMapper userMapper;

    @BeforeEach
    void setUp() {
        // 模拟 SecurityContextUtil
        try (MockedStatic<SecurityContextUtil> mocked = Mockito.mockStatic(SecurityContextUtil.class)) {
            mocked.when(SecurityContextUtil::getCurrentUserId).thenReturn(10L);
            mocked.when(SecurityContextUtil::getCurrentUserRole).thenReturn("seller");
            mocked.when(() -> SecurityContextUtil.hasAuthority(anyString())).thenReturn(true);
        }

        // 模拟 UserService
        when(userService.getUsernameById(anyLong())).thenReturn("Test User");

        // 模拟 UserMapper
        when(userMapper.selectAllForwarders()).thenReturn(new ArrayList<>());
        when(userMapper.selectAllAdmins()).thenReturn(new ArrayList<>());

        // 模拟 NotificationService
        doNothing().when(notificationService).createNotification(any(Notification.class));
    }

    @Test
    @Transactional
    void testCreateRequirement_Success() {
        ForwardingRequirementCreateRequest request = new ForwardingRequirementCreateRequest();
        request.setTitle("Test Requirement");
        request.setCargoType("General");
        request.setDestinationCountry("USA");
        request.setExpectedDeliveryDate(LocalDate.now().plusMonths(1));
        request.setCreatorRole("seller");

        when(forwardingRequirementMapper.findByOrderId(anyLong())).thenReturn(null);
        doAnswer(invocation -> {
            ForwardingRequirement req = invocation.getArgument(0);
            req.setId(100L); // 模拟ID生成
            return 1;
        }).when(forwardingRequirementMapper).insert(any(ForwardingRequirement.class));

        ForwardingRequirementDetailResponse response = forwardingRequirementService.createRequirement(request);

        assertThat(response).isNotNull();
        assertThat(response.getId()).isEqualTo(100L);
        assertThat(response.getStatus()).isEqualTo("open");
        verify(forwardingRequirementMapper, times(1)).insert(any(ForwardingRequirement.class));
        verify(notificationService, times(anyInt())).createNotification(any(Notification.class));
    }

    @Test
    @Transactional
    void testUpdateStatus_Success() {
        Long requirementId = 1L;
        String newStatus = "in_progress";
        ForwardingRequirement existingRequirement = new ForwardingRequirement();
        existingRequirement.setId(requirementId);
        existingRequirement.setStatus("open");
        existingRequirement.setDeleted("0");
        existingRequirement.setCreatedBy(10L);

        when(forwardingRequirementMapper.findById(requirementId)).thenReturn(existingRequirement);
        when(forwardingRequirementMapper.updateById(any(ForwardingRequirement.class))).thenReturn(1);

        boolean result = forwardingRequirementService.updateStatus(requirementId, newStatus);

        assertThat(result).isTrue();
        assertThat(existingRequirement.getStatus()).isEqualTo(newStatus);
        verify(forwardingRequirementMapper, times(1)).updateById(any(ForwardingRequirement.class));
    }

    @Test
    @Transactional
    void testDeleteRequirement_Success() {
        Long requirementId = 1L;
        ForwardingRequirement existingRequirement = new ForwardingRequirement();
        existingRequirement.setId(requirementId);
        existingRequirement.setStatus("open");
        existingRequirement.setDeleted("0");
        existingRequirement.setCreatedBy(10L);

        when(forwardingRequirementMapper.findById(requirementId)).thenReturn(existingRequirement);
        when(forwardingRequirementMapper.deleteById(requirementId)).thenReturn(1);

        forwardingRequirementService.deleteRequirement(requirementId);

        verify(forwardingRequirementMapper, times(1)).deleteById(requirementId);
    }
}
```

## 6. 注意事项

*   **DDD分层**: `ForwardingRequirementServiceImpl` 明确属于应用服务层，其职责是协调领域对象和仓储，实现业务用例。它不包含核心领域逻辑，而是委托给领域实体和领域服务。
*   **事务管理**: 所有涉及数据修改的方法都使用 `@Transactional` 注解，确保操作的原子性和数据一致性。
*   **权限校验**: 服务层通过 `SecurityContextUtil` 获取当前用户ID和角色，并根据业务逻辑进行细粒度的权限校验。例如，只有需求创建者或管理员可以修改/删除需求，不同角色对需求的可见性也不同。
*   **业务异常**: 服务在遇到业务规则冲突或数据不存在时，会抛出 `BusinessException`，并包含明确的错误码和信息，便于上层处理。
*   **数据转换**: 服务层负责将请求DTO转换为领域实体，以及将领域实体转换为前端所需的响应DTO或VO。`convertToDetailResponse` 和 `convertToVO` 辅助方法承担了这一职责。
*   **通知机制**: 在货代需求发布时，系统会异步发送通知给相关用户。`@Async` 注解确保了通知发送不会阻塞主业务流程。
*   **依赖注入**: 服务通过构造函数注入其依赖，遵循了依赖倒置原则。
*   **状态机**: 需求的状态流转（`status`）遵循严格的业务规则。例如，已完成的需求不能更改状态。
*   **动态查询**: `buildSearchQueryWrapper` 方法展示了如何构建复杂的动态查询，包括多条件过滤、关键词搜索和动态排序。
*   **日志记录**: 服务中使用了 `log.info` 和 `log.error` 记录关键操作和异常信息，这对于监控和问题排查非常重要。
*   **简化实现**: 某些辅助方法（如 `getBiddingCount`）当前是简化实现，在生产环境中需要替换为真实的业务逻辑。