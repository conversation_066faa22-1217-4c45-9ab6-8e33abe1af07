# ForwardingRequirementMapper.md

## 1. 文件概述

`ForwardingRequirementMapper.java` 是货代模块中的一个MyBatis-Plus Mapper接口，位于 `com.purchase.forwarding.requirement.mapper` 包中。它继承自MyBatis-Plus的 `BaseMapper<ForwardingRequirement>` 接口，并在此基础上定义了多个自定义的查询方法。`ForwardingRequirement` 实体代表了用户发布的货运需求。该Mapper接口是货代需求服务层与数据库进行交互的桥梁，负责将业务对象的操作转换为SQL语句，实现货运需求的持久化管理和多维度查询。

## 2. 核心功能

*   **基础CRUD操作**: 继承 `BaseMapper`，自动拥有对 `ForwardingRequirement` 实体进行插入（`insert`）、根据ID查询（`selectById`）、根据条件查询列表（`selectList`）、更新（`updateById`）和删除（`deleteById`）等基础的增删改查功能。
*   **按ID查询**: 提供了 `findById` 方法，用于根据货运需求ID查询单个需求，并排除了逻辑删除的记录。
*   **按订单ID查询**: 提供了 `findByOrderId` 和 `getRequirementIdByOrderId` 方法，支持根据关联的订单ID查询货运需求，体现了需求与订单的业务关联。
*   **按创建者查询**: 提供了 `findByCreatedBy` 和 `findByCreatedByAndRole` 方法，支持根据创建者（卖家）ID和角色查询其发布的所有货运需求。
*   **按状态计数**: 提供了 `countByStatus` 方法，用于统计特定状态的货运需求数量，为仪表盘或统计分析提供数据。
*   **逻辑删除过滤**: 所有自定义查询都包含了 `AND deleted = '0'` 条件，确保只查询未被逻辑删除的记录。

## 3. 接口说明

`ForwardingRequirementMapper` 在继承 `BaseMapper` 的基础上，定义了以下自定义方法：

### 3.1 自定义查询方法

#### findById - 根据ID查询货运需求
*   **方法签名**: `ForwardingRequirement findById(@Param("requirementId") Long requirementId)`
*   **描述**: 根据货运需求的主键ID查询一条记录，并过滤掉已逻辑删除的记录。
*   **参数**:
    *   `requirementId` (Long): 货运需求的主键ID。
*   **返回值**: `ForwardingRequirement` - 匹配的货运需求实体，如果不存在或已删除则返回 `null`。

#### findByOrderId - 根据订单ID查询货运需求
*   **方法签名**: `ForwardingRequirement findByOrderId(@Param("orderId") Long orderId)`
*   **描述**: 根据关联的订单ID查询货运需求，并过滤掉已逻辑删除的记录。通常一个订单只对应一个货运需求。
*   **参数**:
    *   `orderId` (Long): 关联的订单ID。
*   **返回值**: `ForwardingRequirement` - 匹配的货运需求实体，如果不存在或已删除则返回 `null`。

#### findByCreatedBy - 根据创建者ID查询货运需求列表
*   **方法签名**: `List<ForwardingRequirement> findByCreatedBy(@Param("sellerId") Long sellerId)`
*   **描述**: 查询指定卖家（创建者）发布的所有未逻辑删除的货运需求列表。
*   **参数**:
    *   `sellerId` (Long): 卖家的用户ID。
*   **返回值**: `List<ForwardingRequirement>` - 匹配的货运需求实体列表。

#### countByStatus - 根据状态统计货运需求数量
*   **方法签名**: `Integer countByStatus(@Param("status") String status)`
*   **描述**: 统计指定状态的货运需求数量，只统计未逻辑删除的记录。
*   **参数**:
    *   `status` (String): 货运需求的状态（例如：`OPEN`, `CLOSED`, `IN_PROGRESS`）。
*   **返回值**: `Integer` - 匹配的货运需求数量。

#### getRequirementIdByOrderId - 根据订单ID获取需求ID
*   **方法签名**: `Long getRequirementIdByOrderId(@Param("orderId") Long orderId)`
*   **描述**: 专门用于根据订单ID获取对应的货运需求ID，通常用于业务关联查询。
*   **参数**:
    *   `orderId` (Long): 关联的订单ID。
*   **返回值**: `Long` - 匹配的货运需求ID，如果不存在则返回 `null`。

## 4. 业务规则

*   **SQL注解**: 所有自定义查询都使用了 `@Select` 注解直接编写SQL。这使得SQL逻辑清晰可见，但对于非常复杂的查询，可能导致注解过长，难以维护。此时可以考虑将SQL提取到XML文件中。
*   **参数绑定**: 使用 `@Param` 注解将Java方法参数绑定到SQL中的命名参数，有效防止SQL注入。
*   **逻辑删除**: 所有的查询都默认排除了 `deleted = '1'` 的记录，这意味着系统采用了逻辑删除策略，而不是物理删除。
*   **性能优化**: 对于 `findByCreatedBy` 等可能返回大量数据的查询，应考虑分页。同时，确保数据库中对 `id`, `order_id`, `created_by`, `status`, `deleted` 等字段建立了合适的索引。
*   **事务管理**: 在服务层调用Mapper方法时，必须确保在事务中执行，以保证数据操作的原子性和一致性。
*   **可读性**: 自定义查询方法的命名清晰，准确反映了其查询目的。

## 5. 使用示例

```java
// 1. 在 ForwardingRequirementService 实现中创建需求
@Service
public class ForwardingRequirementServiceImpl extends ServiceImpl<ForwardingRequirementMapper, ForwardingRequirement>
        implements ForwardingRequirementService {
    @Autowired
    private ForwardingRequirementMapper requirementMapper;

    @Transactional
    public ForwardingRequirementDetailResponse createRequirement(ForwardingRequirementCreateRequest request) {
        ForwardingRequirement requirement = new ForwardingRequirement();
        // ... 将 request 中的数据复制到 requirement ...
        requirement.setCreatedBy(SecurityContextUtil.getCurrentUserId());
        requirement.setCreatorRole(SecurityContextUtil.getCurrentUserRole());
        requirement.setStatus("open"); // 默认开放状态
        requirement.setDeleted("0");

        requirementMapper.insert(requirement);
        return convertToDetailResponse(requirement);
    }
}

// 2. 在 ForwardingRequirementService 实现中根据订单ID获取需求
@Service
public class ForwardingRequirementServiceImpl extends ServiceImpl<ForwardingRequirementMapper, ForwardingRequirement>
        implements ForwardingRequirementService {
    @Autowired
    private ForwardingRequirementMapper requirementMapper;

    public ForwardingRequirementDetailResponse getRequirementByOrderId(Long orderId) {
        ForwardingRequirement requirement = requirementMapper.findByOrderId(orderId);
        if (requirement == null) {
            return null; // 或者抛出异常
        }
        return convertToDetailResponse(requirement);
    }
}

// 3. 在统计服务中统计开放状态的需求数量
@Service
public class DashboardService {
    @Autowired
    private ForwardingRequirementMapper requirementMapper;

    public Integer getOpenRequirementCount() {
        return requirementMapper.countByStatus("OPEN");
    }
}

// 4. 测试示例
@SpringBootTest
class ForwardingRequirementMapperTest {
    @Autowired
    private ForwardingRequirementMapper requirementMapper;

    @Test
    @Transactional
    void testInsertAndFindById() {
        ForwardingRequirement requirement = new ForwardingRequirement();
        requirement.setCreatedBy(1L);
        requirement.setCreatorRole("seller");
        requirement.setTitle("测试货运需求");
        requirement.setCargoType("General");
        requirement.setDestinationCountry("USA");
        requirement.setStatus("open");
        requirement.setDeleted("0");
        requirement.setExpectedDeliveryDate(LocalDate.now().plusMonths(1));

        int result = requirementMapper.insert(requirement);
        assertThat(result).isEqualTo(1);
        assertThat(requirement.getId()).isNotNull();
        assertThat(requirement.getCreatedAt()).isNotNull();
        assertThat(requirement.getUpdatedAt()).isNotNull();

        ForwardingRequirement found = requirementMapper.findById(requirement.getId());
        assertThat(found).isNotNull();
        assertThat(found.getTitle()).isEqualTo("测试货运需求");
        assertThat(found.getStatus()).isEqualTo("open");
    }

    @Test
    @Transactional
    void testFindByOrderId() {
        ForwardingRequirement requirement = new ForwardingRequirement();
        requirement.setOrderId(99L);
        requirement.setDeleted("0");
        // ... 设置其他必要属性 ...
        requirementMapper.insert(requirement);

        ForwardingRequirement found = requirementMapper.findByOrderId(99L);
        assertThat(found).isNotNull();
        assertThat(found.getOrderId()).isEqualTo(99L);
    }

    @Test
    @Transactional
    void testCountByStatus() {
        // 插入一些测试数据
        ForwardingRequirement req1 = new ForwardingRequirement(); req1.setStatus("OPEN"); req1.setDeleted("0"); requirementMapper.insert(req1);
        ForwardingRequirement req2 = new ForwardingRequirement(); req2.setStatus("OPEN"); req2.setDeleted("0"); requirementMapper.insert(req2);
        ForwardingRequirement req3 = new ForwardingRequirement(); req3.setStatus("CLOSED"); req3.setDeleted("0"); requirementMapper.insert(req3);

        Integer openCount = requirementMapper.countByStatus("OPEN");
        assertThat(openCount).isGreaterThanOrEqualTo(2); // 可能会有其他测试数据
    }
}
```

## 6. 注意事项

*   **MyBatis-Plus集成**: 继承 `BaseMapper` 使得该Mapper自动拥有强大的CRUD能力，减少了重复代码。
*   **注解SQL**: 自定义方法使用了 `@Select` 注解直接编写SQL，这在简单查询中很方便。对于复杂查询，可以考虑使用XML配置。
*   **参数绑定**: 使用 `@Param` 注解将Java方法参数绑定到SQL中的命名参数，有效防止SQL注入。
*   **逻辑删除**: 所有的查询都默认排除了 `deleted = '1'` 的记录，这意味着系统采用了逻辑删除策略，而不是物理删除。
*   **性能优化**: 对于 `findByCreatedBy` 等可能返回大量数据的查询，应考虑分页。同时，确保数据库中对 `id`, `order_id`, `created_by`, `status`, `deleted` 等字段建立了合适的索引。
*   **事务管理**: 在服务层调用Mapper方法时，必须确保在事务中执行，以保证数据操作的原子性和一致性。
*   **可读性**: 自定义查询方法的命名清晰，准确反映了其查询目的。
*   **错误处理**: 当 `findById` 或 `findByOrderId` 未找到记录时，会返回 `null`，服务层需要进行相应的判空处理。
