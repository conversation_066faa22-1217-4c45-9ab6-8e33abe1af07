# ForwardingRequirement.md

## 1. 文件概述

`ForwardingRequirement` 是货代模块中的一个实体（Entity），位于 `com.purchase.forwarding.requirement.entity` 包中。它直接映射到数据库的 `forwarding_requirement` 表，用于持久化存储用户发布的货运需求。这个实体包含了货运需求的所有核心属性，如创建者信息、货物详情、起运地和目的地信息、特殊要求、预算、状态以及贸易条款等。它是货运需求业务流程的核心数据载体，通过MyBatis-Plus的注解实现了与数据库表的自动化ORM映射。

## 2. 核心功能

*   **数据持久化**: 定义了货运需求在数据库中的存储结构，是货运需求领域模型在持久化层的具体表现。
*   **多维度需求描述**: 包含了丰富的字段，能够全面描述货物的类型、重量、体积、包装、特殊要求以及起运地和目的地等信息。
*   **状态管理**: 包含了 `status` 字段，支持货运需求的生命周期管理（如 `open`, `in_progress`, `completed`, `cancelled`）。
*   **MyBatis-Plus集成**: 通过 `@TableName`, `@TableId`, `@TableField`, `@TableLogic`, `@Data` 等注解与MyBatis-Plus框架深度集成，实现了主键自增长、字段映射、逻辑删除和自动填充时间戳等功能，极大地简化了数据库操作。
*   **逻辑删除**: `deleted` 字段配合 `@TableLogic` 注解实现了逻辑删除功能，确保数据不会被物理删除，便于数据恢复和审计。
*   **关联信息**: 记录了 `orderId`（关联的采购订单ID）和 `createdBy`（创建者ID），便于追溯和关联业务。

## 3. 属性说明

- **`id` (Long)**: 货运需求的主键ID，自增长。
- **`orderId` (Long)**: 关联的采购订单ID。
- **`createdBy` (Long)**: 创建者用户ID。
- **`creatorRole` (String)**: 创建者角色（`buyer`, `seller`, `forwarder`, `admin`）。
- **`title` (String)**: 需求标题。
- **`productName` (String)**: 产品名称。
- **`hsCode` (String)**: 海关商品编码(HS Code)。
- **`destinationCountry` (String)**: 目的国家。
- **`destinationAddress` (String)**: 目的地址。
- **`originCountry` (String)**: 起始国家。
- **`originAddress` (String)**: 起始地址。
- **`contactPerson` (String)**: 联系人。
- **`contactPhone` (String)**: 联系电话。
- **`expectedDeliveryDate` (LocalDate)**: 预期交付日期。
- **`description` (String)**: 需求描述。
- **`cargoType` (String)**: 货物类型。
- **`productCategory` (String)**: 产品分类。
- **`cargoWeight` (BigDecimal)**: 货物重量(kg)。
- **`cargoVolume` (BigDecimal)**: 货物体积(m³)。
- **`packageCount` (Integer)**: 包装数量。
- **`specialRequirements` (String)**: 特殊要求。
- **`productImages` (String)**: 产品图片路径，多个图片以逗号分隔。
- **`productVideos` (String)**: 产品视频路径，多个视频以逗号分隔。
- **`budget` (BigDecimal)**: 预算。
- **`status` (String)**: 需求状态（例如：`open`, `in_progress`, `completed`, `cancelled`）。
- **`deliveryTerms` (String)**: 贸易条款（如FOB, CIF, EXW等）。
- **`portOfLoadingName` (String)**: 起始港口名称。
- **`portOfLoadingCode` (String)**: 起始港口 UN/LOCODE。
- **`portOfDestinationName` (String)**: 目的港口名称。
- **`portOfDestinationCode` (String)**: 目的港口 UN/LOCODE。
- **`shipmentType` (String)**: 运输类型（`LCL`-拼柜，`FCL`-整柜）。
- **`containerSize` (String)**: 货柜尺寸（仅当 `shipmentType=FCL` 时有效）。
- **`containerQty` (Integer)**: 货柜数量（仅当 `shipmentType=FCL` 时有效）。
- **`needCertification` (String)**: 是否需要认证（`0`-不需要，`1`-需要）。
- **`needFumigation` (String)**: 是否需要熏蒸（`0`-不需要，`1`-需要）。
- **`customsService` (Integer)**: 是否包含报关服务（`0`-不包含，`1`-包含）。
- **`insuranceIncluded` (Integer)**: 是否包含保险（`0`-不包含，`1`-包含）。
- **`deleted` (String)**: 逻辑删除标志（`0`-未删除，`1`-已删除）。
- **`createdAt` (LocalDateTime)**: 记录创建时间，插入时自动填充。
- **`updatedAt` (LocalDateTime)**: 记录更新时间，插入和更新时自动填充。

## 4. 业务规则

*   **状态流转**: `status` 字段的更新应遵循预定义的状态机规则，确保需求流程的正确性。
*   **逻辑删除**: `deleted` 字段配合MyBatis-Plus的 `@TableLogic` 注解，实现了数据的逻辑删除，查询时会自动过滤已删除的记录。
*   **多关联**: `ForwardingRequirement` 与 `UnifiedOrder` 等业务实体通过ID进行关联，便于追溯和查询。
*   **数据类型**: `BigDecimal` 用于重量、体积和预算计算，以避免浮点数精度问题。`LocalDate` 和 `LocalDateTime` 用于日期时间字段，确保了时间的精确性和时区处理的便利性。
*   **JSON字段**: `productImages` 和 `productVideos` 字段存储逗号分隔的URL字符串，在业务逻辑中需要进行字符串解析和处理。
*   **条件必填**: `containerSize` 和 `containerQty` 仅在 `shipmentType` 为 `FCL` 时有效，业务逻辑层应确保这些字段的正确使用。

## 5. 使用示例

```java
// 1. 在 ForwardingRequirementMapper 接口中定义对 ForwardingRequirement 的操作
@Mapper
public interface ForwardingRequirementMapper extends BaseMapper<ForwardingRequirement> {
    // 继承 BaseMapper 提供了基本的 CRUD 方法
    // 也可以在此定义自定义查询方法，例如：
    @Select("SELECT * FROM forwarding_requirement WHERE created_by = #{createdBy} AND deleted = '0' ORDER BY created_at DESC")
    List<ForwardingRequirement> findByCreatedBy(@Param("createdBy") Long createdBy);

    @Select("SELECT * FROM forwarding_requirement WHERE status = #{status} AND deleted = '0'")
    List<ForwardingRequirement> findByStatus(@Param("status") String status);
}

// 2. 在 ForwardingRequirementService 实现中创建和更新 ForwardingRequirement
@Service
public class ForwardingRequirementServiceImpl extends ServiceImpl<ForwardingRequirementMapper, ForwardingRequirement>
        implements ForwardingRequirementService {
    @Autowired
    private ForwardingRequirementMapper requirementMapper;

    @Transactional
    public ForwardingRequirementDetailResponse createRequirement(ForwardingRequirementCreateRequest request) {
        ForwardingRequirement requirement = new ForwardingRequirement();
        // ... 将 request 中的数据复制到 requirement ...
        requirement.setCreatedBy(SecurityContextUtil.getCurrentUserId());
        requirement.setCreatorRole(SecurityContextUtil.getCurrentUserRole());
        requirement.setStatus("open"); // 默认开放状态
        requirement.setDeleted("0");

        requirementMapper.insert(requirement);
        return convertToDetailResponse(requirement);
    }

    @Transactional
    public ForwardingRequirementDetailResponse updateRequirement(ForwardingRequirementUpdateRequest request) {
        ForwardingRequirement existingRequirement = getById(request.getId());
        if (existingRequirement == null || "1".equals(existingRequirement.getDeleted())) {
            throw new BusinessException("货代需求不存在");
        }
        // ... 权限校验，确保只有创建者或管理员可以修改 ...
        // ... 将 request 中的可更新数据复制到 existingRequirement ...
        existingRequirement.setStatus(request.getStatus());
        existingRequirement.setUpdatedAt(LocalDateTime.now());
        requirementMapper.updateById(existingRequirement);
        return convertToDetailResponse(existingRequirement);
    }
}

// 3. 测试示例
@SpringBootTest
class ForwardingRequirementTest {
    @Autowired
    private ForwardingRequirementMapper forwardingRequirementMapper;

    @Test
    @Transactional
    void testInsertAndRetrieve() {
        ForwardingRequirement requirement = new ForwardingRequirement();
        requirement.setCreatedBy(1L);
        requirement.setCreatorRole("seller");
        requirement.setTitle("测试货运需求");
        requirement.setCargoType("General");
        requirement.setDestinationCountry("USA");
        requirement.setStatus("open");
        requirement.setDeleted("0");
        requirement.setExpectedDeliveryDate(LocalDate.now().plusMonths(1));

        int result = forwardingRequirementMapper.insert(requirement);
        assertThat(result).isEqualTo(1);
        assertThat(requirement.getId()).isNotNull();
        assertThat(requirement.getCreatedAt()).isNotNull();
        assertThat(requirement.getUpdatedAt()).isNotNull();

        ForwardingRequirement retrievedRequirement = forwardingRequirementMapper.selectById(requirement.getId());
        assertThat(retrievedRequirement).isNotNull();
        assertThat(retrievedRequirement.getTitle()).isEqualTo("测试货运需求");
        assertThat(retrievedRequirement.getStatus()).isEqualTo("open");
    }

    @Test
    @Transactional
    void testLogicalDelete() {
        ForwardingRequirement requirement = new ForwardingRequirement();
        requirement.setCreatedBy(2L);
        requirement.setCreatorRole("buyer");
        requirement.setTitle("待删除需求");
        requirement.setStatus("open");
        requirement.setDeleted("0");
        forwardingRequirementMapper.insert(requirement);

        // 执行逻辑删除
        forwardingRequirementMapper.deleteById(requirement.getId());

        // 再次查询，应该找不到（因为逻辑删除）
        ForwardingRequirement deletedRequirement = forwardingRequirementMapper.selectById(requirement.getId());
        assertThat(deletedRequirement).isNull();
    }
}
```

## 6. 注意事项

*   **领域实体**: `ForwardingRequirement` 是一个典型的领域实体，它包含了数据和行为，并且其行为（如状态变更）直接反映了业务概念。
*   **MyBatis-Plus集成**: 充分利用MyBatis-Plus的注解和功能，可以大大简化数据访问层的开发。特别是 `@TableLogic` 对于逻辑删除的实现非常方便。
*   **Lombok注解**: `@Data` 极大地简化了实体类的编写，减少了样板代码。
*   **时间戳**: `createdAt` 和 `updatedAt` 的自动填充依赖于MyBatis-Plus的配置（如 `MyMetaObjectHandler`），确保了记录的创建和更新时间被准确捕获。
*   **多关联ID**: 实体中包含 `orderId` 和 `createdBy`，表明货运需求与采购订单和用户存在关联。
*   **数据类型**: `BigDecimal` 用于重量、体积和预算计算，以避免浮点数精度问题。`LocalDate` 和 `LocalDateTime` 用于日期时间字段，确保了时间的精确性和时区处理的便利性。
*   **JSON字段**: `productImages` 和 `productVideos` 字段存储逗号分隔的URL字符串，在业务逻辑中需要进行字符串解析和处理。
*   **状态管理**: 实体内部的 `status` 字段是其生命周期的关键，业务逻辑层应确保状态流转的正确性。
*   **可扩展性**: 如果未来需要增加更多货运需求相关的属性，可以在实体中添加相应的字段。