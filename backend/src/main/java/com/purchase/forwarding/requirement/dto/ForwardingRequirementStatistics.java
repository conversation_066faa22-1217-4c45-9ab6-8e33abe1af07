package com.purchase.forwarding.requirement.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 货代需求统计数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForwardingRequirementStatistics {
    
    /**
     * 总需求数
     */
    private Long totalRequirements;
    
    /**
     * 已发布需求数（open状态）
     */
    private Long openRequirements;
    
    /**
     * 进行中需求数（in_progress状态）
     */
    private Long inProgressRequirements;
    
    /**
     * 已完成需求数（completed状态）
     */
    private Long completedRequirements;
    
    /**
     * 已取消需求数（cancelled状态）
     */
    private Long cancelledRequirements;
    
    /**
     * FCL整柜需求数
     */
    private Long fclRequirements;
    
    /**
     * LCL拼柜需求数
     */
    private Long lclRequirements;
} 