package com.purchase.forwarding.requirement.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("forwarding_requirement")
public class ForwardingRequirement {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long orderId;
    
    private Long createdBy;
    
    /**
     * 创建者角色(buyer-买家, seller-卖家, forwarder-货代, admin-管理员)
     */
    private String creatorRole;
    
    private String title;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 海关商品编码(HS Code)
     */
    private String hsCode;
    
    private String destinationCountry;
    
    private String destinationAddress;
    
    /**
     * 起始国家
     */
    private String originCountry;
    
    /**
     * 起始地址
     */
    private String originAddress;
    
    private String contactPerson;
    
    private String contactPhone;
    
    private LocalDate expectedDeliveryDate;
    
    private String description;
    
    private String cargoType;
    
    /**
     * 产品分类
     */
    private String productCategory;
    
    private BigDecimal cargoWeight;
    
    private BigDecimal cargoVolume;
    
    private Integer packageCount;
    
    private String specialRequirements;
    
    // private String[] documents;
    
    /**
     * 产品图片，多个图片以逗号分隔
     */
    private String productImages;
    
    /**
     * 产品视频，多个视频以逗号分隔
     */
    private String productVideos;
    
    private BigDecimal budget;
    
    private String status;
    
    /**
     * 贸易条款（如FOB, CIF, EXW等）
     */
    private String deliveryTerms;
    
    /**
     * 起始港口名称 (例如: Shanghai)
     */
    private String portOfLoadingName;
    
    /**
     * 起始港口 UN/LOCODE (例如: CNSHA)
     */
    private String portOfLoadingCode;
    
    /**
     * 目的港口名称 (例如: Rotterdam)
     */
    private String portOfDestinationName;
    
    /**
     * 目的港口 UN/LOCODE (例如: NLRTM)
     */
    private String portOfDestinationCode;
    
    
    
    /**
     * 运输类型：LCL（拼柜）、FCL（整柜）
     */
    private String shipmentType;
    
    /**
     * 货柜尺寸（如20GP、40HQ等），仅当shipmentType=FCL时有效
     */
    private String containerSize;
    
    /**
     * 货柜数量（默认0），仅当shipmentType=FCL时有效
     */
    private Integer containerQty;


    /**
     * 是否需要认证
     */
    private String needCertification;
    
    /**
     * 是否需要熏蒸
     */
    private String needFumigation;
    
    /**
     * 是否包含报关服务：0-不包含，1-包含
     */
    @TableField("customs_service")
    private Integer customsService;
    
    /**
     * 是否包含保险：0-不包含，1-包含
     */
    @TableField("insurance_included")
    private Integer insuranceIncluded;
    
    @TableLogic(value = "0", delval = "1")
    private String deleted;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}