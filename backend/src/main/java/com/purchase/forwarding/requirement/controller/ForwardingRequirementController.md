# ForwardingRequirementController.md

## 1. 文件概述

`ForwardingRequirementController.java` 是货代模块中专门处理货代需求的控制器，位于 `com.purchase.forwarding.requirement.controller` 包中。它提供了与货代需求相关的RESTful API接口，涵盖了货代需求的创建、更新、删除、查询以及状态变更等全生命周期管理。该控制器通过依赖注入 `ForwardingRequirementService` 来执行具体的业务逻辑，并利用Spring Security的 `@PreAuthorize` 注解进行严格的权限控制，确保不同角色（买家、卖家、货代、管理员）的用户只能执行其被授权的操作。

## 2. 核心功能

*   **货代需求创建与更新**: 允许卖家、管理员、买家创建和更新货代需求。
*   **货代需求删除**: 允许卖家、管理员、买家逻辑删除货代需求。
*   **货代需求查询**: 提供多维度查询接口，支持按需求ID、订单ID、卖家ID、买家ID、需求状态等条件获取需求详情。同时支持分页查询需求列表，并根据用户角色进行数据过滤。
*   **货代需求状态变更**: 允许卖家、管理员、买家更新货代需求的状态。
*   **货代参与竞价的需求列表**: 允许货代查看其参与过竞价的需求列表，并支持分页。
*   **货代需求统计**: 提供货代需求的统计数据查询。
*   **权限控制**: 所有接口都通过 `@PreAuthorize` 注解进行严格的权限控制，确保只有授权用户才能访问和操作。
*   **统一响应**: 所有接口都返回一个标准化的 `ResponseEntity<Result<...>>` 对象，统一了成功和失败的响应格式，便于前端处理。

## 3. 接口说明

### 3.1 货代需求操作接口

#### createRequirement - 创建货代需求
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/forwarding/requirements`
*   **权限**: `hasAnyAuthority('seller', 'admin', 'buyer')`
*   **参数**:
    *   `request` (ForwardingRequirementCreateRequest, body, required): 包含创建货代需求所需信息的请求体。
*   **返回值**: `ResponseEntity<Result<ForwardingRequirementDetailResponse>>` - 创建成功的货代需求详情。
*   **业务逻辑**: 接收创建请求，调用 `forwardingRequirementService.createRequirement` 处理业务逻辑，并返回结果。

#### updateRequirement - 更新货代需求
*   **HTTP方法**: `PUT`
*   **路径**: `/api/v1/forwarding/requirements/{id}`
*   **权限**: `hasAnyAuthority('seller', 'admin', 'buyer')`
*   **参数**:
    *   `id` (Long, path, required): 货代需求ID。
    *   `request` (ForwardingRequirementUpdateRequest, body, required): 包含更新货代需求所需信息的请求体。
*   **返回值**: `ResponseEntity<Result<ForwardingRequirementDetailResponse>>` - 更新后的货代需求详情。
*   **业务逻辑**: 接收更新请求，设置ID，调用 `forwardingRequirementService.updateRequirement` 处理业务逻辑，并返回结果。

#### deleteRequirement - 删除货代需求
*   **HTTP方法**: `DELETE`
*   **路径**: `/api/v1/forwarding/requirements/{id}`
*   **权限**: `hasAnyAuthority('seller', 'admin', 'buyer')`
*   **参数**:
    *   `id` (Long, path, required): 要删除的货代需求ID。
*   **返回值**: `ResponseEntity<Result<Void>>` - 空响应实体，表示操作成功。
*   **业务逻辑**: 调用 `forwardingRequirementService.deleteRequirement` 处理业务逻辑。

#### updateStatus - 更新货代需求状态
*   **HTTP方法**: `PUT`
*   **路径**: `/api/v1/forwarding/requirements/{id}/status`
*   **权限**: `hasAnyAuthority('seller', 'admin', 'buyer')`
*   **参数**:
    *   `id` (Long, path, required): 货代需求ID。
    *   `status` (String, query, required): 新状态。
*   **返回值**: `ResponseEntity<Result<Boolean>>` - 包含更新结果的响应实体。
*   **业务逻辑**: 调用 `forwardingRequirementService.updateStatus` 处理业务逻辑。

### 3.2 货代需求查询接口

#### getRequirementById - 根据ID获取货代需求详情
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarding/requirements/{id}`
*   **权限**: `hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')`
*   **参数**:
    *   `id` (Long, path, required): 货代需求ID。
*   **返回值**: `ResponseEntity<Result<ForwardingRequirementDetailResponse>>` - 货代需求详情。
*   **业务逻辑**: 调用 `forwardingRequirementService.getRequirementById` 获取需求详情。

#### getRequirementByOrderId - 根据订单ID获取货代需求详情
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarding/requirements/order/{orderId}`
*   **权限**: `hasAnyAuthority('buyer', 'seller', 'forwarder', 'admin')`
*   **参数**:
    *   `orderId` (Long, path, required): 订单ID。
*   **返回值**: `ResponseEntity<Result<ForwardingRequirementDetailResponse>>` - 货代需求详情。
*   **业务逻辑**: 调用 `forwardingRequirementService.getRequirementByOrderId` 获取需求详情。

#### searchRequirements - 搜索货代需求列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarding/requirements/search`
*   **权限**: `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
*   **参数**:
    *   `request` (ForwardingRequirementSearchRequest, query, required): 搜索请求参数。
*   **返回值**: `ResponseEntity<Result<IPage<ForwardingRequirementVO>>>` - 包含分页货代需求列表的响应实体。
*   **业务逻辑**: 调用 `forwardingRequirementService.searchRequirements` 获取需求列表。

#### getRequirementList - 分页获取货代需求列表（兼容性接口）
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarding/requirements`
*   **权限**: `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
*   **参数**:
    *   `sellerId` (Long, query, optional): 卖家ID。
    *   `status` (String, query, optional): 需求状态。
    *   `current` (Integer, query, optional, default=1): 当前页码。
    *   `size` (Integer, query, optional, default=10): 每页大小。
*   **返回值**: `ResponseEntity<Result<IPage<ForwardingRequirementVO>>>` - 包含分页货代需求列表的响应实体。
*   **业务逻辑**: 调用 `forwardingRequirementService.getRequirementList` 获取需求列表。

#### getMyRequirements - 获取当前用户的货代需求列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarding/requirements/my`
*   **权限**: `hasAnyAuthority('seller', 'admin')`
*   **参数**:
    *   `sellerId` (Long, query, optional): 卖家ID。
*   **返回值**: `ResponseEntity<Result<List<ForwardingRequirementVO>>>` - 包含当前用户货代需求列表的响应实体。
*   **业务逻辑**: 调用 `forwardingRequirementService.getMyRequirements` 获取需求列表。

#### getRequirementsByBuyerId - 根据买家ID获取货代需求列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarding/requirements/buyer/{buyerId}`
*   **权限**: `hasAnyAuthority('buyer', 'admin')`
*   **参数**:
    *   `buyerId` (Long, path, required): 买家ID。
*   **返回值**: `ResponseEntity<Result<List<ForwardingRequirementVO>>>` - 包含货代需求列表的响应实体。
*   **业务逻辑**: 调用 `forwardingRequirementService.getRequirementsByBuyerId` 获取需求列表。

#### getForwarderBiddingRequirements - 获取当前货代参与过竞价的需求列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarding/requirements/forwarder/bidding`
*   **权限**: `hasAuthority('forwarder')`
*   **参数**: 无。
*   **返回值**: `ResponseEntity<Result<List<ForwardingRequirementVO>>>` - 包含需求列表的响应实体。
*   **业务逻辑**: 获取当前登录货代用户ID，调用 `forwardingRequirementService.getRequirementsByForwarderId` 获取需求列表。

#### getForwarderBiddingRequirementsPage - 分页获取当前货代参与过竞价的需求列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarding/requirements/forwarder/bidding/page`
*   **权限**: `hasAuthority('forwarder')`
*   **参数**:
    *   `status` (String, query, optional): 需求状态。
    *   `current` (Integer, query, optional, default=1): 当前页码。
    *   `size` (Integer, query, optional, default=10): 每页大小。
*   **返回值**: `ResponseEntity<Result<IPage<ForwardingRequirementVO>>>` - 包含分页需求列表的响应实体。
*   **业务逻辑**: 获取当前登录货代用户ID，调用 `forwardingRequirementService.getRequirementPageByForwarderId` 获取需求列表。

#### getStatistics - 获取货代需求统计数据
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarding/requirements/statistics`
*   **权限**: 无显式权限，通常需要认证。
*   **参数**: 无。
*   **返回值**: `ResponseEntity<Result<ForwardingRequirementStatistics>>` - 货代需求统计数据。
*   **业务逻辑**: 调用 `forwardingRequirementService.getStatistics` 获取统计数据。

## 4. 业务规则

*   **权限控制**: 所有接口都通过 `@PreAuthorize` 注解进行严格的权限控制，确保不同角色用户只能访问其被授权的资源和操作。
*   **参数校验**: 请求体中的DTO通过 `@Valid` 注解进行JSR-303校验，确保输入数据的合法性。
*   **统一响应**: 所有接口都返回 `ResponseEntity<Result<...>>` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **日志记录**: 对所有接收到的请求都记录了详细的日志，包括请求参数，便于系统监控和调试。
*   **职责分离**: 控制器仅负责请求的接收、参数的初步校验和转发，具体的业务逻辑和数据处理委托给 `ForwardingRequirementService`。
*   **安全上下文**: 控制器通过 `SecurityContextUtil.getCurrentUserId()` 获取当前登录用户ID，确保操作的归属和权限。

## 5. 使用示例

```java
// 1. 前端 (React) 创建货代需求
async function createForwardingRequirement(requirementData) {
  try {
    const response = await axios.post('/api/v1/forwarding/requirements', requirementData, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('货代需求创建成功:', response.data.data);
  } catch (error) {
    console.error('货代需求创建失败:', error.response.data);
  }
}

// 2. 卖家获取自己的货代需求列表
async function fetchMyForwardingRequirements() {
  try {
    const response = await axios.get('/api/v1/forwarding/requirements/my', {
      params: { status: 'open' },
      headers: { Authorization: `Bearer ${sellerToken}` }
    });
    console.log('我的货代需求:', response.data.data);
  } catch (error) {
    console.error('获取我的货代需求失败:', error.response.data);
  }
}

// 3. 货代获取其参与竞价的需求列表
async function fetchForwarderBiddingRequirements() {
  try {
    const response = await axios.get('/api/v1/forwarding/requirements/forwarder/bidding', {
      headers: { Authorization: `Bearer ${forwarderToken}` }
    });
    console.log('货代参与竞价的需求:', response.data.data);
  } catch (error) {
    console.error('获取货代参与竞价的需求失败:', error.response.data);
  }
}

// 4. 测试示例 (MockMvc)
@SpringBootTest
@AutoConfigureMockMvc
class ForwardingRequirementControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ForwardingRequirementService forwardingRequirementService;

    @BeforeEach
    void setUp() {
        // 模拟 SecurityContextUtil 返回一个卖家用户ID
        try (MockedStatic<SecurityContextUtil> mocked = Mockito.mockStatic(SecurityContextUtil.class)) {
            mocked.when(SecurityContextUtil::getCurrentUserId).thenReturn(100L);
            mocked.when(SecurityContextUtil::getCurrentUserRole).thenReturn("seller");
            mocked.when(() -> SecurityContextUtil.hasAuthority(anyString())).thenReturn(true);
        }
    }

    @Test
    @WithMockUser(authorities = "seller")
    void testCreateRequirement_Success() throws Exception {
        ForwardingRequirementCreateRequest request = new ForwardingRequirementCreateRequest();
        request.setTitle("Test Requirement");
        request.setCargoType("General");
        // ... set other fields ...

        ForwardingRequirementDetailResponse mockResponse = new ForwardingRequirementDetailResponse();
        mockResponse.setId(1L);
        when(forwardingRequirementService.createRequirement(any(ForwardingRequirementCreateRequest.class))).thenReturn(mockResponse);

        mockMvc.perform(post("/api/v1/forwarding/requirements")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(1L));
    }

    @Test
    @WithMockUser(authorities = "buyer")
    void testGetRequirementById_Success() throws Exception {
        Long requirementId = 123L;
        ForwardingRequirementDetailResponse mockResponse = new ForwardingRequirementDetailResponse();
        mockResponse.setId(requirementId);
        when(forwardingRequirementService.getRequirementById(eq(requirementId))).thenReturn(mockResponse);

        mockMvc.perform(get("/api/v1/forwarding/requirements/{id}", requirementId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(requirementId));
    }

    @Test
    @WithMockUser(authorities = "admin")
    void testUpdateStatus_Success() throws Exception {
        Long requirementId = 123L;
        String newStatus = "closed";
        when(forwardingRequirementService.updateStatus(eq(requirementId), eq(newStatus))).thenReturn(true);

        mockMvc.perform(put("/api/v1/forwarding/requirements/{id}/status", requirementId)
                .param("status", newStatus))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));
    }
}
```

## 6. 注意事项

*   **权限控制**: 所有接口都通过 `@PreAuthorize` 进行严格的权限控制，确保只有授权用户才能访问和操作。这是系统安全的关键。
*   **参数校验**: 请求体中的DTO通过 `@Valid` 注解进行JSR-303校验，确保输入数据的合法性。
*   **统一响应**: 所有接口都返回 `ResponseEntity<Result<...>>` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **日志记录**: 对所有接收到的请求都记录了详细的日志，包括请求参数，便于系统监控和调试。
*   **职责分离**: 控制器仅负责请求的接收、参数的初步校验和转发，具体的业务逻辑和数据处理委托给 `ForwardingRequirementService`。
*   **安全上下文**: 控制器通过 `SecurityContextUtil.getCurrentUserId()` 获取当前登录用户ID，确保操作的归属和权限。
*   **多维度查询**: 提供了多种查询入口（按ID、订单ID、搜索请求对象、分页列表、我的需求、按买家ID、货代参与竞价的需求），满足了不同业务场景的需求。