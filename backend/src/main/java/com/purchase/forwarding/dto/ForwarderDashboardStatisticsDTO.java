package com.purchase.forwarding.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 货代仪表盘统计数据DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForwarderDashboardStatisticsDTO {
    
    /**
     * 总竞价数
     */
    private Long bidCount;
    
    /**
     * 中标项目数
     */
    private Long winCount;
    
    /**
     * 待处理物流数
     */
    private Long pendingCount;
} 