# ForwardingBiddingController.java

## 文件概述 (File Overview)
`ForwardingBiddingController.java` 是一个 Spring REST 控制器，负责处理所有与货代竞价相关的 HTTP 请求。它提供了货代竞价的创建、更新、取消、查询（根据采购订单、需求、货代 ID、分页查询）、选择中标货代、更新物流跟踪信息、接受/拒绝竞价以及管理员审核等功能。该控制器通过与 `ForwardingBiddingService` 交互来处理业务逻辑，并利用 Spring Security 的 `@PreAuthorize` 注解进行严格的权限控制。

## 核心功能 (Core Functionality)
*   **竞价查询:**
    *   根据采购订单 ID、需求 ID、货代 ID 获取竞价列表。
    *   分页获取竞价列表，支持按需求 ID、货代 ID、状态筛选。
    *   管理员获取所有货代竞价记录（增强版分页查询，支持多维度过滤和排序）。
    *   获取需求的中标竞价和竞价数量。
    *   获取可见的竞价列表（审核通过的竞价和自己的竞价）。
*   **竞价管理:**
    *   **创建竞价:** 允许货代创建新的竞价。
    *   **更新竞价:** 允许货代更新自己创建的竞价。
    *   **取消竞价:** 允许货代取消自己创建的竞价。
    *   **选择中标货代:** 允许管理员或卖家选择某个竞价为中标竞价。
    *   **更新物流跟踪信息:** 允许管理员或货代更新竞价的物流跟踪号和 URL。
*   **竞价审批:**
    *   **接受竞价:** 允许管理员、买家或卖家接受某个竞价，并触发后续的货代订单创建流程。
    *   **拒绝竞价:** 允许管理员、买家或卖家拒绝某个竞价。
    *   **管理员审核竞价:** 允许管理员对竞价进行批准或拒绝。
*   **权限控制:** 所有接口都通过 `@PreAuthorize` 注解进行严格的权限验证。

## 接口说明 (Interface Description)

### 构造函数
*   通过 `@RequiredArgsConstructor` 自动注入 `ForwardingBiddingService`。

### 公共方法 (Public Methods)
*   `ResponseEntity<Result<List<ForwardingBiddingVO>>> getBiddingsByPurchaseOrderId(@PathVariable Long purchaseOrderId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/bidding/purchase-order/{purchaseOrderId}`
    *   **摘要:** 根据采购订单ID获取货代竞价列表
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'buyer', 'forwarder')`
    *   **参数:** `purchaseOrderId` (Long, `@PathVariable`): 采购订单ID。
    *   **返回值:** `ResponseEntity<Result<List<ForwardingBiddingVO>>>` - 竞价列表。

*   `ResponseEntity<Result<ForwardingBiddingDetailResponse>> createBidding(@RequestBody @Validated ForwardingBiddingCreateRequest request)`:
    *   **HTTP 方法:** `POST`
    *   **路径:** `/api/v1/forwarding/bidding`
    *   **摘要:** 创建竞价
    *   **权限:** `hasAuthority('forwarder')`
    *   **参数:** `request` (ForwardingBiddingCreateRequest, `@RequestBody`, `@Validated`): 竞价创建请求DTO。
    *   **返回值:** `ResponseEntity<Result<ForwardingBiddingDetailResponse>>` - 创建后的竞价详情。

*   `ResponseEntity<Result<ForwardingBiddingDetailResponse>> updateBidding(@RequestBody @Validated ForwardingBiddingUpdateRequest request)`:
    *   **HTTP 方法:** `PUT`
    *   **路径:** `/api/v1/forwarding/bidding`
    *   **摘要:** 更新竞价
    *   **权限:** `hasAuthority('forwarder')`
    *   **参数:** `request` (ForwardingBiddingUpdateRequest, `@RequestBody`, `@Validated`): 竞价更新请求DTO。
    *   **返回值:** `ResponseEntity<Result<ForwardingBiddingDetailResponse>>` - 更新后的竞价详情。

*   `ResponseEntity<Void> cancelBidding(@PathVariable Long biddingId)`:
    *   **HTTP 方法:** `DELETE`
    *   **路径:** `/api/v1/forwarding/bidding/{biddingId}`
    *   **摘要:** 取消竞价
    *   **权限:** `hasAuthority('forwarder')`
    *   **参数:** `biddingId` (Long, `@PathVariable`): 竞价ID。
    *   **返回值:** `ResponseEntity<Void>` - 空响应（200 OK）。

*   `ResponseEntity<Result<ForwardingBiddingDetailResponse>> getBiddingById(@PathVariable Long biddingId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/bidding/{biddingId}`
    *   **摘要:** 获取竞价详情
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `biddingId` (Long, `@PathVariable`): 竞价ID。
    *   **返回值:** `ResponseEntity<Result<ForwardingBiddingDetailResponse>>` - 竞价详情。

*   `ResponseEntity<Result<List<ForwardingBiddingVO>>> getBiddingsByRequirementId(@PathVariable Long requirementId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/bidding/requirement/{requirementId}`
    *   **摘要:** 获取需求的所有竞价
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `requirementId` (Long, `@PathVariable`): 需求ID。
    *   **返回值:** `ResponseEntity<Result<List<ForwardingBiddingVO>>>` - 竞价列表。

*   `ResponseEntity<Result<List<ForwardingBiddingVO>>> getBiddingsByOrderId(@PathVariable Long orderId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/bidding/order/{orderId}`
    *   **摘要:** 根据订单ID获取所有的竞价
    *   **权限:** `hasAnyAuthority('admin','seller', 'buyer')`
    *   **参数:** `orderId` (Long, `@PathVariable`): 订单ID。
    *   **返回值:** `ResponseEntity<Result<List<ForwardingBiddingVO>>>` - 竞价列表。

*   `ResponseEntity<Result<List<ForwardingBiddingVO>>> getBiddingsByForwarderId(@PathVariable Long forwarderId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/bidding/forwarder/{forwarderId}`
    *   **摘要:** 获取货代的所有竞价
    *   **权限:** `hasAnyAuthority('admin', 'forwarder')`
    *   **参数:** `forwarderId` (Long, `@PathVariable`): 货代ID。
    *   **返回值:** `ResponseEntity<Result<List<ForwardingBiddingVO>>>` - 竞价列表。

*   `ResponseEntity<Result<List<ForwardingBiddingVO>>> getMyBiddings()`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/bidding/my`
    *   **摘要:** 获取当前货代的所有竞价
    *   **权限:** `hasAuthority('forwarder')`
    *   **参数:** 无。
    *   **返回值:** `ResponseEntity<Result<List<ForwardingBiddingVO>>>` - 竞价列表。

*   `ResponseEntity<Result<ForwardingBiddingDetailResponse>> selectWinner(@PathVariable Long biddingId)`:
    *   **HTTP 方法:** `POST`
    *   **路径:** `/api/v1/forwarding/bidding/{biddingId}/select-winner`
    *   **摘要:** 选择中标货代
    *   **权限:** `hasAnyAuthority('admin', 'seller')`
    *   **参数:** `biddingId` (Long, `@PathVariable`): 竞价ID。
    *   **返回值:** `ResponseEntity<Result<ForwardingBiddingDetailResponse>>` - 中标竞价详情。

*   `ResponseEntity<Result<IPage<ForwardingBiddingVO>>> getBiddingPage(@RequestParam Integer current, @RequestParam Integer size, @RequestParam Long requirementId, @RequestParam Long forwarderId, @RequestParam String status)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/bidding`
    *   **摘要:** 分页获取竞价列表
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `current` (Integer), `size` (Integer), `requirementId` (Long, 可选), `forwarderId` (Long, 可选), `status` (String, 可选)。
    *   **返回值:** `ResponseEntity<Result<IPage<ForwardingBiddingVO>>>` - 竞价分页列表。

*   `ResponseEntity<Result<IPage<ForwardingBiddingVO>>> getAllBiddingsForAdmin(@RequestParam Integer current, @RequestParam Integer size, @RequestParam Long requirementId, @RequestParam Long forwarderId, @RequestParam String status, @RequestParam String startDate, @RequestParam String endDate, @RequestParam String orderBy, @RequestParam Boolean ascending, @RequestParam Long purchaseOrderId, @RequestParam Double minPrice, @RequestParam Double maxPrice, @RequestParam String keyword)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/bidding/admin/all`
    *   **摘要:** 管理员获取所有货代竞价记录（增强版分页查询）
    *   **权限:** `hasAuthority('admin')`
    *   **参数:** 包含多种筛选和排序参数。
    *   **返回值:** `ResponseEntity<Result<IPage<ForwardingBiddingVO>>>` - 竞价分页列表。

*   `ResponseEntity<Result<ForwardingBiddingDetailResponse>> updateTrackingInfo(@PathVariable Long biddingId, @RequestBody Map<String, String> trackingInfo)`:
    *   **HTTP 方法:** `POST`
    *   **路径:** `/api/v1/forwarding/bidding/{biddingId}/tracking`
    *   **摘要:** 更新物流跟踪信息
    *   **权限:** `hasAnyAuthority('admin', 'forwarder')`
    *   **参数:** `biddingId` (Long, `@PathVariable`), `trackingInfo` (Map<String, String>, `@RequestBody`): 包含 `trackingNumber` 和 `trackingUrl`。
    *   **返回值:** `ResponseEntity<Result<ForwardingBiddingDetailResponse>>` - 更新后的竞价详情。

*   `ResponseEntity<Result<ForwardingBiddingDetailResponse>> getWinnerByRequirementId(@PathVariable Long requirementId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/bidding/requirement/{requirementId}/winner`
    *   **摘要:** 获取需求的中标竞价
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `requirementId` (Long, `@PathVariable`): 需求ID。
    *   **返回值:** `ResponseEntity<Result<ForwardingBiddingDetailResponse>>` - 中标竞价详情。

*   `ResponseEntity<Result<Integer>> getBiddingCount(@PathVariable Long requirementId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/bidding/requirement/{requirementId}/count`
    *   **摘要:** 获取需求的竞价数量
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `requirementId` (Long, `@PathVariable`): 需求ID。
    *   **返回值:** `ResponseEntity<Result<Integer>>` - 竞价数量。

*   `ResponseEntity<Result<ForwardingBiddingDetailResponse>> acceptBidding(@PathVariable Long biddingId)`:
    *   **HTTP 方法:** `POST`
    *   **路径:** `/api/v1/forwarding/bidding/{biddingId}/accept`
    *   **摘要:** 接受竞价
    *   **权限:** `hasAnyAuthority('admin', 'buyer', 'seller')`
    *   **参数:** `biddingId` (Long, `@PathVariable`): 竞价ID。
    *   **返回值:** `ResponseEntity<Result<ForwardingBiddingDetailResponse>>` - 接受后的竞价详情。

*   `ResponseEntity<Result<ForwardingBiddingDetailResponse>> rejectBidding(@PathVariable Long biddingId)`:
    *   **HTTP 方法:** `POST`
    *   **路径:** `/api/v1/forwarding/bidding/{biddingId}/reject`
    *   **摘要:** 拒绝竞价
    *   **权限:** `hasAnyAuthority('admin', 'buyer', 'seller')`
    *   **参数:** `biddingId` (Long, `@PathVariable`): 竞价ID。
    *   **返回值:** `ResponseEntity<Result<ForwardingBiddingDetailResponse>>` - 拒绝后的竞价详情。

*   `ResponseEntity<Result<ForwardingBiddingDetailResponse>> auditBidding(@RequestBody @Validated ForwardingBiddingAuditRequest request)`:
    *   **HTTP 方法:** `POST`
    *   **路径:** `/api/v1/forwarding/bidding/audit`
    *   **摘要:** 管理员审核竞价
    *   **权限:** `hasAuthority('admin')`
    *   **参数:** `request` (ForwardingBiddingAuditRequest, `@RequestBody`, `@Validated`): 审核请求DTO。
    *   **返回值:** `ResponseEntity<Result<ForwardingBiddingDetailResponse>>` - 审核后的竞价详情。

*   `ResponseEntity<Result<IPage<ForwardingBiddingVO>>> getPendingAuditBiddings(@RequestParam Integer current, @RequestParam Integer size)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/bidding/pending-audit`
    *   **摘要:** 获取待审核的竞价列表
    *   **权限:** `hasAuthority('admin')`
    *   **参数:** `current` (Integer), `size` (Integer)。
    *   **返回值:** `ResponseEntity<Result<IPage<ForwardingBiddingVO>>>` - 待审核竞价分页列表。

*   `ResponseEntity<Result<List<ForwardingBiddingVO>>> getVisibleBiddingsByRequirementId(@PathVariable Long requirementId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/bidding/visible/requirement/{requirementId}`
    *   **摘要:** 获取可见的竞价列表（审核通过的竞价和自己的竞价）
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `requirementId` (Long, `@PathVariable`): 需求ID。
    *   **返回值:** `ResponseEntity<Result<List<ForwardingBiddingVO>>>` - 可见的竞价列表。

## 使用示例 (Usage Examples)
```java
// 前端调用示例 (JavaScript/TypeScript)

// 货代创建竞价
async function createForwardingBidding(biddingData) {
    const response = await fetch('/api/v1/forwarding/bidding', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer YOUR_FORWARDER_JWT_TOKEN' },
        body: JSON.stringify(biddingData)
    });
    const data = await response.json();
    console.log('创建竞价结果:', data);
}

// 获取采购订单ID为123的竞价列表
async function getBiddingsByPurchaseOrderId(purchaseOrderId) {
    const response = await fetch(`/api/v1/forwarding/bidding/purchase-order/${purchaseOrderId}`);
    const data = await response.json();
    console.log(`采购订单 ${purchaseOrderId} 的竞价列表:`, data.data);
}

// 管理员批准竞价ID为456的竞价
async function adminAuditBidding(biddingId, approved, remark) {
    const response = await fetch('/api/v1/forwarding/bidding/audit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer YOUR_ADMIN_JWT_TOKEN' },
        body: JSON.stringify({ biddingId, approved, remark })
    });
    const data = await response.json();
    console.log('审核竞价结果:', data);
}

// 买家接受竞价ID为789的竞价
async function acceptBidding(biddingId) {
    const response = await fetch(`/api/v1/forwarding/bidding/${biddingId}/accept`, {
        method: 'POST',
        headers: { 'Authorization': 'Bearer YOUR_BUYER_JWT_TOKEN' }
    });
    const data = await response.json();
    console.log('接受竞价结果:', data);
}
```

## 注意事项 (Notes)
*   **权限控制:** 广泛使用了 Spring Security 的 `@PreAuthorize` 注解进行方法级别的权限控制，确保只有具备相应角色的用户才能访问。在实际部署中，需要确保 Spring Security 配置正确，以使这些注解生效。
*   **统一响应:** 所有接口都返回 `ResponseEntity<Result<T>>` 或 `ResponseEntity<Void>`，提供统一的响应格式和 HTTP 状态码控制。
*   **参数验证:** `createBidding`, `updateBidding`, `auditBidding` 接口使用了 `@Validated` 和 `@RequestBody` 对 DTO 进行验证。
*   **日志记录:** 尽管文件中没有显式使用 `Slf4j` 的 `log` 实例，但通常会在控制器中注入 `Logger` 进行日志记录，以追踪业务流程和问题排查。
*   **业务异常处理:** 对于 `updateTrackingInfo` 中的 `BusinessException`，控制器直接抛出，通常由全局异常处理器捕获并返回统一错误响应。
*   **Swagger 注解:** 尽管文件中使用了 `io.swagger.annotations` 包的注解，但通常建议使用 `io.swagger.v3.oas.annotations` 包的注解（如 `@Tag`, `@Operation`, `@Parameter`）以支持 OpenAPI 3 规范。
*   **`getCurrentUserId()` 辅助方法:** 尽管该控制器没有直接包含 `getCurrentUserId()` 方法，但其依赖的服务层或更上层通常会提供此功能，以获取当前登录用户 ID 进行权限判断或业务处理。
