package com.purchase.forwarding.bidding.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流状态视图对象
 */
@Data
public class ShippingStatusVO {
    
    private Long id;
    
    private String statusCode;
    
    private String statusName;
    
    private String location;
    
    private String description;
    
    private LocalDateTime occurTime;
    
    private LocalDateTime estimatedArrival;
    
    private Boolean exceptionFlag;
    
    private String exceptionReason;
    
    private List<String> proofImages;
    
    private String operatorName;
} 