package com.purchase.forwarding.bidding.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.purchase.forwarding.bidding.entity.ForwardingBidding;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 货代竞价Mapper接口
 */
@Mapper
public interface ForwardingBiddingMapper extends BaseMapper<ForwardingBidding> {
    
    /**
     * 根据需求ID查询竞价记录
     */
    @Select("SELECT * FROM forwarding_bidding WHERE forwarding_requirement_id = #{requirementId} AND deleted = '0'")
    List<ForwardingBidding> findByRequirementId(@Param("requirementId") Long requirementId);
    
    /**
     * 根据采购订单ID查询竞价记录
     */
    @Select("SELECT * FROM forwarding_bidding WHERE purchase_order_id = #{purchaseOrderId} AND deleted = '0'")
    List<ForwardingBidding> findByPurchaseOrderId(@Param("purchaseOrderId") Long purchaseOrderId);
    
    /**
     * 根据货代ID查询竞价记录
     */
    @Select("SELECT * FROM forwarding_bidding WHERE forwarder_id = #{forwarderId} AND deleted = '0'")
    List<ForwardingBidding> findByForwarderId(@Param("forwarderId") Long forwarderId);
    
    /**
     * 查询特定需求下是否已有该货代的竞价
     */
    @Select("SELECT COUNT(*) FROM forwarding_bidding WHERE forwarding_requirement_id = #{requirementId} AND forwarder_id = #{forwarderId} AND deleted = '0'")
    int countByRequirementIdAndForwarderId(@Param("requirementId") Long requirementId, @Param("forwarderId") Long forwarderId);
    
    /**
     * 查询需求的中标竞价
     */
    @Select("SELECT * FROM forwarding_bidding WHERE forwarding_requirement_id = #{requirementId} AND is_winner = 1 AND deleted = '0' LIMIT 1")
    ForwardingBidding findWinnerByRequirementId(@Param("requirementId") Long requirementId);
    
    /**
     * 将需求的所有竞价设置为非中标
     */
    @Update("UPDATE forwarding_bidding SET is_winner = 0 WHERE forwarding_requirement_id = #{requirementId} AND deleted = '0'")
    int resetWinnerByRequirementId(@Param("requirementId") Long requirementId);
    
    /**
     * 将指定竞价设置为中标
     */
    @Update("UPDATE forwarding_bidding SET is_winner = 1, status = 'accepted' WHERE id = #{biddingId} AND deleted = '0'")
    int setAsWinner(@Param("biddingId") Long biddingId);
    
    /**
     * 查询需求下的竞价数量
     */
    @Select("SELECT COUNT(*) FROM forwarding_bidding WHERE forwarding_requirement_id = #{requirementId} AND deleted = '0'")
    int countByRequirementId(@Param("requirementId") Long requirementId);
    
    /**
     * 更新竞价的物流跟踪信息
     */
    @Update("UPDATE forwarding_bidding SET tracking_number = #{trackingNumber}, tracking_url = #{trackingUrl} WHERE id = #{biddingId} AND deleted = '0'")
    int updateTrackingInfo(@Param("biddingId") Long biddingId, @Param("trackingNumber") String trackingNumber, @Param("trackingUrl") String trackingUrl);


    @Select("SELECT * FROM forwarding_bidding WHERE id = #{biddingId} AND deleted = '0'")
    ForwardingBidding findByBiddingId(@Param("biddingId") Long biddingId);
    
    /**
     * 检查需求下是否已存在已接受的竞价
     * @return true表示存在，false表示不存在
     */
    @Select("SELECT EXISTS (SELECT 1 FROM forwarding_bidding " +
            "WHERE forwarding_requirement_id = #{requirementId} " +
            "AND status = 'accepted' " + 
            "AND deleted = '0')")
    boolean existsAcceptedBidding(@Param("requirementId") Long requirementId);
    
    /**
     * 获取货代参与过竞价的需求ID列表
     */
    @Select("SELECT DISTINCT forwarding_requirement_id FROM forwarding_bidding WHERE forwarder_id = #{forwarderId} AND deleted = '0'")
    List<Long> findRequirementIdsByForwarderId(@Param("forwarderId") Long forwarderId);
} 