# ForwardingBiddingService.md

## 1. 文件概述

`ForwardingBiddingService` 是货代模块中的一个服务接口，位于 `com.purchase.forwarding.bidding.service` 包中。它定义了货代竞价的完整生命周期管理，从创建、更新、取消，到查询、接受、拒绝以及中标选择等一系列操作。该接口旨在为 `ForwardingBiddingController` 提供清晰、独立的业务逻辑，并通过DTO和VO模式，实现了内外模型的分离。它是货代竞价业务的核心逻辑层。

## 2. 核心功能

*   **竞价生命周期管理**: 提供了创建 (`createBidding`)、更新 (`updateBidding`)、取消 (`cancelBidding`) 竞价的功能。
*   **多维度查询**: 支持根据ID、订单ID、采购订单ID、货代ID、需求ID等多种条件查询竞价详情或列表。
*   **中标管理**: 提供了 `selectWinner`（选择中标货代）、`getWinnerByRequirementId`（获取中标竞价）等方法，支持竞价的中标逻辑。
*   **状态变更**: 提供了 `acceptBidding`（接受竞价）、`rejectBidding`（拒绝竞价）、`auditBidding`（审核竞价）等方法，用于管理竞价的状态流转。
*   **分页查询**: 支持分页获取竞价列表，并为管理员提供了增强版的分页查询功能，支持更多筛选和排序条件。
*   **物流信息更新**: 提供了 `updateTrackingInfo` 方法，用于更新竞价的物流跟踪号和跟踪URL。
*   **竞价数量统计**: 提供了 `getBiddingCount` 方法，用于获取某个需求的竞价数量。
*   **可见性控制**: 提供了 `getVisibleBiddingsByRequirementId` 方法，根据用户权限返回可见的竞价列表。

## 3. 接口说明

### 3.1 竞价操作

#### createBidding - 创建竞价
*   **方法签名**: `ForwardingBiddingDetailResponse createBidding(ForwardingBiddingCreateRequest request)`
*   **描述**: 根据请求创建一个新的货代竞价。
*   **参数**:
    *   `request` (ForwardingBiddingCreateRequest): 包含创建竞价所需信息的DTO。
*   **返回值**: `ForwardingBiddingDetailResponse` - 创建成功后的竞价详情。

#### updateBidding - 更新竞价
*   **方法签名**: `ForwardingBiddingDetailResponse updateBidding(ForwardingBiddingUpdateRequest request)`
*   **描述**: 根据请求更新一个已存在的货代竞价。
*   **参数**:
    *   `request` (ForwardingBiddingUpdateRequest): 包含更新竞价所需信息的DTO。
*   **返回值**: `ForwardingBiddingDetailResponse` - 更新后的竞价详情。

#### cancelBidding - 取消竞价
*   **方法签名**: `void cancelBidding(Long biddingId)`
*   **描述**: 取消指定ID的竞价。
*   **参数**:
    *   `biddingId` (Long): 竞价ID。
*   **返回值**: `void`

#### selectWinner - 选择中标货代
*   **方法签名**: `ForwardingBiddingDetailResponse selectWinner(Long biddingId)`
*   **描述**: 将指定竞价设置为中标，并处理相关业务逻辑（如拒绝其他竞价）。
*   **参数**:
    *   `biddingId` (Long): 竞价ID。
*   **返回值**: `ForwardingBiddingDetailResponse` - 中标后的竞价详情。

#### acceptBidding - 接受竞价
*   **方法签名**: `ForwardingBiddingDetailResponse acceptBidding(Long biddingId)`
*   **描述**: 接受指定竞价，将其状态设置为已接受。
*   **参数**:
    *   `biddingId` (Long): 竞价ID。
*   **返回值**: `ForwardingBiddingDetailResponse` - 接受后的竞价详情。

#### rejectBidding - 拒绝竞价
*   **方法签名**: `ForwardingBiddingDetailResponse rejectBidding(Long biddingId)`
*   **描述**: 拒绝指定竞价，将其状态设置为已拒绝。
*   **参数**:
    *   `biddingId` (Long): 竞价ID。
*   **返回值**: `ForwardingBiddingDetailResponse` - 拒绝后的竞价详情。

#### auditBidding - 管理员审核竞价
*   **方法签名**: `ForwardingBiddingDetailResponse auditBidding(Long biddingId, boolean approved, String remark)`
*   **描述**: 管理员对指定竞价进行审核。
*   **参数**:
    *   `biddingId` (Long): 竞价ID。
    *   `approved` (boolean): 是否通过审核。
    *   `remark` (String): 审核备注。
*   **返回值**: `ForwardingBiddingDetailResponse` - 审核后的竞价详情。

#### updateTrackingInfo - 更新物流跟踪信息
*   **方法签名**: `ForwardingBiddingDetailResponse updateTrackingInfo(Long biddingId, String trackingNumber, String trackingUrl)`
*   **描述**: 更新指定竞价的物流跟踪号和跟踪URL。
*   **参数**:
    *   `biddingId` (Long): 竞价ID。
    *   `trackingNumber` (String): 物流跟踪号。
    *   `trackingUrl` (String): 物流跟踪URL。
*   **返回值**: `ForwardingBiddingDetailResponse` - 更新后的竞价详情。

### 3.2 竞价查询

#### getBiddingById - 根据ID获取竞价详情
*   **方法签名**: `ForwardingBiddingDetailResponse getBiddingById(Long biddingId)`
*   **描述**: 根据竞价ID获取竞价的详细信息。
*   **参数**:
    *   `biddingId` (Long): 竞价ID。
*   **返回值**: `ForwardingBiddingDetailResponse` - 竞价详情。

#### getBiddingsByRequirementId - 获取需求的所有竞价
*   **方法签名**: `List<ForwardingBiddingVO> getBiddingsByRequirementId(Long requirementId)`
*   **描述**: 获取指定需求下的所有竞价列表。
*   **参数**:
    *   `requirementId` (Long): 需求ID。
*   **返回值**: `List<ForwardingBiddingVO>` - 竞价列表。

#### getWinnerByRequirementId - 获取需求的中标竞价
*   **方法签名**: `ForwardingBiddingDetailResponse getWinnerByRequirementId(Long requirementId)`
*   **描述**: 获取指定需求下已中标的竞价。
*   **参数**:
    *   `requirementId` (Long): 需求ID。
*   **返回值**: `ForwardingBiddingDetailResponse` - 中标竞价详情。

#### getBiddingCount - 获取需求的竞价数量
*   **方法签名**: `int getBiddingCount(Long requirementId)`
*   **描述**: 获取指定需求下的竞价总数。
*   **参数**:
    *   `requirementId` (Long): 需求ID。
*   **返回值**: `int` - 竞价数量。

#### getBiddingPage - 分页获取竞价列表
*   **方法签名**: `IPage<ForwardingBiddingVO> getBiddingPage(Integer current, Integer size, Long requirementId, Long forwarderId, String status)`
*   **描述**: 分页获取竞价列表，支持按需求ID、货代ID和状态筛选。
*   **参数**:
    *   `current` (Integer): 当前页码。
    *   `size` (Integer): 每页大小。
    *   `requirementId` (Long): 需求ID（可选）。
    *   `forwarderId` (Long): 货代ID（可选）。
    *   `status` (String): 状态（可选）。
*   **返回值**: `IPage<ForwardingBiddingVO>` - 竞价分页列表。

#### getAllBiddingsForAdmin - 管理员获取所有货代竞价记录（增强版分页查询）
*   **方法签名**: `IPage<ForwardingBiddingVO> getAllBiddingsForAdmin(Integer current, Integer size, Long requirementId, Long forwarderId, String status, String startDate, String endDate, String orderBy, Boolean ascending, Long purchaseOrderId, Double minPrice, Double maxPrice, String keyword)`
*   **描述**: 管理员专用的高级分页查询接口，支持更多筛选和排序条件。
*   **参数**: 包含分页、筛选、排序等多种参数。
*   **返回值**: `IPage<ForwardingBiddingVO>` - 竞价分页列表。

#### getPendingAuditBiddings - 获取待审核的竞价列表
*   **方法签名**: `IPage<ForwardingBiddingVO> getPendingAuditBiddings(Integer current, Integer size)`
*   **描述**: 分页获取所有待审核的竞价列表。
*   **参数**:
    *   `current` (Integer): 当前页码。
    *   `size` (Integer): 每页大小。
*   **返回值**: `IPage<ForwardingBiddingVO>` - 待审核竞价分页列表。

#### getVisibleBiddingsByRequirementId - 根据需求ID获取可见的竞价列表
*   **方法签名**: `List<ForwardingBiddingVO> getVisibleBiddingsByRequirementId(Long requirementId)`
*   **描述**: 获取指定需求下对当前用户可见的竞价列表（通常是审核通过的竞价和用户自己的竞价）。
*   **参数**:
    *   `requirementId` (Long): 需求ID。
*   **返回值**: `List<ForwardingBiddingVO>` - 可见的竞价列表。

## 4. 业务规则

*   **竞价生命周期**: 竞价的状态流转（如从 `pending` 到 `accepted` 或 `rejected`）应遵循严格的业务规则和状态机。
*   **中标唯一性**: 一个需求通常只能有一个中标竞价。当一个竞价被接受为中标时，其他竞价应被自动拒绝或标记为非中标。
*   **权限控制**: 服务的实现需要严格校验调用者的权限，确保只有授权用户才能执行相应的操作（例如，只有卖家能提交和修改自己的竞价，只有买家能接受和拒绝竞价，只有管理员能执行强制操作）。
*   **数据一致性**: 所有写操作（创建、更新、接受、拒绝等）都必须是事务性的，以保证数据的一致性。
*   **数据转换**: 服务层负责将从仓储获取的 `ForwardingBidding` 实体转换为 `ForwardingBiddingDetailResponse` 或 `ForwardingBiddingVO` 等DTO/VO对象，并进行必要的业务逻辑处理（如脱敏）。
*   **异常处理**: 服务应定义并抛出具体的业务异常（如 `BiddingNotFoundException`, `PermissionDeniedException`, `InvalidBiddingStatusException`），而不是直接抛出通用异常。

## 5. 使用示例

```java
// 1. 在 ForwardingBiddingController 中调用 ForwardingBiddingService
@RestController
@RequestMapping("/api/v1/forwarding-biddings")
public class ForwardingBiddingController {
    @Autowired
    private ForwardingBiddingService forwardingBiddingService;

    @PostMapping
    @PreAuthorize("hasAuthority('forwarder')")
    public Result<ForwardingBiddingDetailResponse> createBidding(@Valid @RequestBody ForwardingBiddingCreateRequest request) {
        // 从SecurityContext获取当前货代ID并设置到request中
        // request.setForwarderId(SecurityContextUtil.getCurrentUserId());
        ForwardingBiddingDetailResponse response = forwardingBiddingService.createBidding(request);
        return Result.success("竞价创建成功", response);
    }

    @PutMapping("/{biddingId}/accept")
    @PreAuthorize("hasAuthority('buyer')")
    public Result<ForwardingBiddingDetailResponse> acceptBidding(@PathVariable Long biddingId) {
        // 业务逻辑：验证买家权限，确保该竞价属于买家需求
        ForwardingBiddingDetailResponse response = forwardingBiddingService.acceptBidding(biddingId);
        return Result.success("竞价接受成功", response);
    }

    @GetMapping("/requirement/{requirementId}")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")
    public Result<List<ForwardingBiddingVO>> getBiddingsByRequirement(@PathVariable Long requirementId) {
        List<ForwardingBiddingVO> biddings = forwardingBiddingService.getBiddingsByRequirementId(requirementId);
        return Result.success(biddings);
    }
}

// 2. 测试示例
@SpringBootTest
class ForwardingBiddingServiceTest {
    @Autowired
    private ForwardingBiddingService forwardingBiddingService;

    @MockBean
    private ForwardingBiddingMapper forwardingBiddingMapper;
    @MockBean
    private ForwardingRequirementService forwardingRequirementService;

    @Test
    @Transactional
    void testCreateBidding_Success() {
        ForwardingBiddingCreateRequest request = new ForwardingBiddingCreateRequest();
        request.setRequirementId(1L);
        request.setForwarderId(10L);
        request.setPrice(new BigDecimal("100.00"));
        // ... set other fields ...

        // 模拟Mapper行为
        when(forwardingBiddingMapper.countByRequirementIdAndForwarderId(anyLong(), anyLong())).thenReturn(0);
        doAnswer(invocation -> {
            ForwardingBidding bidding = invocation.getArgument(0);
            bidding.setId(100L); // 模拟ID生成
            return 1;
        }).when(forwardingBiddingMapper).insert(any(ForwardingBidding.class));
        when(forwardingBiddingMapper.selectById(100L)).thenReturn(new ForwardingBidding()); // 模拟查询

        ForwardingBiddingDetailResponse response = forwardingBiddingService.createBidding(request);

        assertThat(response).isNotNull();
        assertThat(response.getId()).isEqualTo(100L);
        verify(forwardingBiddingMapper, times(1)).insert(any(ForwardingBidding.class));
    }

    @Test
    @Transactional
    void testAcceptBidding_Success() {
        Long biddingId = 1L;
        Long requirementId = 10L;
        ForwardingBidding mockBidding = new ForwardingBidding();
        mockBidding.setId(biddingId);
        mockBidding.setForwardingRequirementId(requirementId);
        mockBidding.setStatus("pending");
        mockBidding.setAuditStatus("approved");
        mockBidding.setDeleted("0");

        when(forwardingBiddingMapper.selectById(biddingId)).thenReturn(mockBidding);
        when(forwardingBiddingMapper.resetWinnerByRequirementId(requirementId)).thenReturn(1);
        when(forwardingBiddingMapper.setAsWinner(biddingId)).thenReturn(1);

        ForwardingBiddingDetailResponse response = forwardingBiddingService.acceptBidding(biddingId);

        assertThat(response).isNotNull();
        assertThat(response.getStatus()).isEqualTo("accepted");
        verify(forwardingBiddingMapper, times(1)).resetWinnerByRequirementId(requirementId);
        verify(forwardingBiddingMapper, times(1)).setAsWinner(biddingId);
    }
}
```

## 6. 注意事项

*   **DDD分层**: `ForwardingBiddingService` 明确属于领域服务或应用服务层，其职责是封装业务逻辑，协调领域对象和仓储。
*   **事务管理**: 所有写操作（`create`, `update`, `cancel`, `selectWinner`, `accept`, `reject`, `audit`, `updateTrackingInfo`）都必须声明为事务性的 (`@Transactional`)，以保证数据操作的原子性和一致性。
*   **权限校验**: 服务的实现需要严格校验调用者的权限，确保只有授权用户才能执行相应的操作。这通常通过 `SecurityContextUtil` 获取当前用户ID和角色，并与业务数据进行比对。
*   **数据转换**: 服务层负责将从仓储获取的 `ForwardingBidding` 实体转换为 `ForwardingBiddingDetailResponse` 或 `ForwardingBiddingVO` 等DTO/VO对象，并进行必要的业务逻辑处理（如脱敏）。
*   **异常处理**: 服务应定义并抛出具体的业务异常（如 `BiddingNotFoundException`, `PermissionDeniedException`, `InvalidBiddingStatusException`），而不是直接抛出通用异常，以便上层调用者能够进行针对性的处理。
*   **业务流程**: 竞价的接受和拒绝等操作会触发复杂的业务流程，例如更新需求状态、生成订单、发送通知等，这些都应在服务内部进行协调。
*   **性能优化**: 对于分页查询和复杂筛选，服务层应确保底层仓储和Mapper的查询效率，例如使用合适的索引。
