## 类用途
物流状态视图对象，用于展示运输过程的时间线和地理位置

## 核心字段
| 字段名 | 类型 | 前端组件 | 描述 |
|-------|------|--------|-----|
| status | String | 状态标签 | 当前物流状态 |
| timestamp | Instant | 时间轴 | 状态变更时间 |
| location | GeoPoint | 地图标记 | 经纬度坐标 |
| operator | String | 操作日志 | 触发该状态的操作人 |
| images | List<String> | 图片画廊 | 状态凭证图片URL |

## 数据结构
```mermaid
classDiagram
    class ShippingStatusVO {
        +String status
        +Instant timestamp
        +GeoPoint location
        +String operator
        +List<String> images
        +getLocationName() String
        +getTimeAgo() String
    }
```

## 前端集成
```javascript
// 时间轴示例
<Timeline>
    <Timeline.Item 
        v-for="item in statusList" 
        :time="item.timeAgo" 
        :title="item.status">
        <MapMarker v-if="item.location" :point="item.location"/>
        <ImageGallery :images="item.images"/>
    </Timeline.Item>
</Timeline>

// 地图集成示例
<GMap :markers="statusList.map(i => i.location)"/>
```

## 业务规则
- 坐标数据从物流系统同步获取
- 操作人信息脱敏处理（如：张**）
- 图片需压缩后上传（最大5MB）
- 时间显示使用相对时间（如：2小时前）

## 注意事项
- 生产环境建议添加地图API调用限流
- 敏感图片需设置访问权限
- 移动端需优化图片加载