## 类用途
投标视图对象，用于前端列表和详情展示

## 核心字段
| 字段名 | 类型 | 前端展示 | 权限控制 | 描述 |
|-------|------|--------|---------|-----|
| biddingId | String | 表格列 | 所有角色 | 投标单ID |
| requirementTitle | String | 详情标题 | 所有角色 | 关联需求标题 |
| quotedAmount | BigDecimal | 加粗显示 | 采购员+转发商 | 报价金额 |
| status | String | 状态标签 | 所有角色 | 投标状态 |
| latestShippingStatus | ShippingStatusVO | 物流卡片 | 所有角色 | 最新物流状态 |
| canOperate | Boolean | 操作按钮 | 按角色动态 | 是否可操作 |

## 数据聚合
```mermaid
flowchart LR
    A[投标单] -->|获取主体| B(ForwardingBiddingVO)
    C[采购需求] -->|加载标题| B
    D[物流记录] -->|最新状态| B
    E[用户权限] -->|操作权限| B
```

## 前端示例
```javascript
// 表格列示例
columns: [
    {field: 'biddingId', label: '编号'},
    {field: 'requirementTitle', label: '需求'},
    {field: 'quotedAmount', label: '金额', format: 'money'},
    {field: 'status', label: '状态', badge: true}
]

// 详情展示示例
<BiddingDetail 
    :id="vo.biddingId"
    :title="vo.requirementTitle"
    :amount="vo.quotedAmount"
    :shipping="vo.latestShippingStatus"/>
```

## 注意事项
- 金额字段需统一格式化显示
- 状态标签需匹配多语言
- 生产环境建议添加数据缓存