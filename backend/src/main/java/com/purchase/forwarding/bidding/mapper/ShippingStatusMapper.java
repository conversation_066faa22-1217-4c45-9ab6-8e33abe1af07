package com.purchase.forwarding.bidding.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.purchase.forwarding.bidding.entity.ShippingStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 物流状态Mapper接口
 */
@Mapper
public interface ShippingStatusMapper extends BaseMapper<ShippingStatus> {
    
    /**
     * 根据竞价ID查询物流状态
     */
    @Select("SELECT * FROM shipping_status WHERE forwarding_bidding_id = #{biddingId} ORDER BY occur_time DESC")
    List<ShippingStatus> findByBiddingId(@Param("biddingId") Long biddingId);
    
    /**
     * 根据订单ID查询物流状态
     */
    @Select("SELECT * FROM shipping_status WHERE order_id = #{orderId} ORDER BY occur_time DESC")
    List<ShippingStatus> findByOrderId(@Param("orderId") Long orderId);
    
    /**
     * 根据需求ID查询物流状态
     */
    @Select("SELECT * FROM shipping_status WHERE forwarding_requirement_id = #{requirementId} ORDER BY occur_time DESC")
    List<ShippingStatus> findByRequirementId(@Param("requirementId") Long requirementId);
    
    /**
     * 查询最新的物流状态
     */
    @Select("SELECT * FROM shipping_status WHERE forwarding_bidding_id = #{biddingId} ORDER BY occur_time DESC LIMIT 1")
    ShippingStatus findLatestStatusByBiddingId(@Param("biddingId") Long biddingId);
    
    /**
     * 查询异常的物流状态
     */
    @Select("SELECT * FROM shipping_status WHERE forwarding_bidding_id = #{biddingId} AND exception_flag = 1 ORDER BY occur_time DESC")
    List<ShippingStatus> findExceptionStatusByBiddingId(@Param("biddingId") Long biddingId);
} 