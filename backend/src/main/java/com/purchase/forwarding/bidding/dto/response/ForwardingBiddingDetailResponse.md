# ForwardingBiddingDetailResponse.java

## 文件概述 (File Overview)
`ForwardingBiddingDetailResponse.java` 是一个数据传输对象（DTO），用于封装和传输货代竞价的详细信息，作为后端 API 响应给前端的载体。它提供了竞价的完整视图，包括竞价的基本信息、关联的需求和订单信息、货代公司详情、报价、运输细节、费用构成、状态、审核信息、中标状态、物流跟踪信息以及时间戳。该 DTO 旨在为前端提供一个全面的竞价详情展示。

## 核心功能 (Core Functionality)
*   **竞价信息展示:** 聚合了竞价的所有关键属性，包括其唯一标识、关联实体、货代信息、报价和运输方式。
*   **费用明细:** 包含了多种国际贸易术语（Incoterms）下可能涉及的详细费用项目，以及自定义费用项目。
*   **状态与审核:** 提供了 `status`（竞价状态）、`auditStatus`（审核状态）和 `auditRemark`（审核备注）字段，清晰地反映竞价的当前状态和审批流程。
*   **中标与跟踪:** 包含 `winner`（是否中标）、`trackingNumber`（物流跟踪号）和 `trackingUrl`（物流跟踪 URL）字段，用于展示竞价的最终结果和物流进度。
*   **关联订单:** 包含 `orderId` 和 `orderNumber`，用于关联竞价与最终生成的货代订单。
*   **时间戳:** 包含 `createdAt` 和 `updatedAt` 字段，用于追踪竞价的生命周期。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `Long id`: 竞价的唯一标识 ID。
*   `Long forwardingRequirementId`: 关联的货代需求 ID。
*   `Long purchaseOrderId`: 关联的采购订单 ID。
*   `String requirementTitle`: 关联的需求标题。
*   `Long forwarderId`: 货代用户 ID。
*   `String forwarderName`: 货代联系人姓名。
*   `String forwarderCompanyName`: 货代公司名称。
*   `BigDecimal biddingPrice`: 竞价金额。
*   `String currency`: 货币类型。
*   `String shippingMethod`: 运输方式（例如：海运、空运、陆运）。
*   `String shippingRoute`: 运输路线。
*   `Integer estimatedDays`: 预计运输天数。
*   `String description`: 竞价描述。
*   `List<String> documents`: 附件文档的 URL 列表。
*   `Boolean insuranceIncluded`: 是否包含保险。
*   `BigDecimal insuranceAmount`: 保险金额。
*   `Boolean customsService`: 是否提供清关服务。
*   `String needCertification`: 是否需要产品认证（`0`-不需要，`1`-需要）。
*   `String needFumigation`: 是否需要货物熏蒸（`0`-不需要，`1`-需要）。
*   **费用字段 (BigDecimal):**
    *   `costExportPacking`: 出口包装费。
    *   `costOriginInlandHaulage`: 起运地内陆运输费。
    *   `costExportCustomsDocs`: 出口报关及单证费。
    *   `costOriginHandling`: 起运地操作费。
    *   `costMainFreight`: 主运费。
    *   `costFreightSurcharges`: 运费附加费。
    *   `costCargoInsurance`: 货物运输保险费。
    *   `costDestinationHandlingBySeller`: 目的地操作费 (卖方承担)。
    *   `costDestinationUnloadingBySeller`: 目的地卸货费 (卖方承担)。
    *   `costDestinationInlandHaulageBySeller`: 目的地内陆运输费 (卖方承担)。
    *   `costImportCustomsDocsBySeller`: 进口报关及单证费 (卖方承担-DDP)。
    *   `costImportDutiesTaxesBySeller`: 进口关税及税费 (卖方承担-DDP)。
    *   `costForwarderServiceFee`: 货代服务费/操作费 (卖方承担)。
*   `String deliveryTerms`: 交货条款/国际贸易术语 (例如: FOB, CIF)。
*   `String status`: 竞价状态（例如：`pending`, `submitted`, `accepted`, `rejected`, `cancelled`）。
*   `String auditStatus`: 审核状态（例如：`pending`, `approved`, `rejected`）。
*   `String auditRemark`: 审核备注。
*   `Boolean winner`: 是否为中标竞价。
*   `String trackingNumber`: 物流跟踪号。
*   `String trackingUrl`: 物流跟踪 URL。
*   `Long orderId`: 关联的货代订单 ID。
*   `String orderNumber`: 关联的货代订单号。
*   `LocalDateTime createdAt`: 创建时间。
*   `LocalDateTime updatedAt`: 更新时间。
*   `String customedCosts`: 自定义费用项目 JSON 字符串。
*   `LocalDateTime estimatedPickupDate`: 预计提货日期。
*   `LocalDateTime estimatedDeliveryDate`: 预计送达日期。

## 使用示例 (Usage Examples)

```java
// 在Service层将ForwardingBidding实体转换为ForwardingBiddingDetailResponse
@Service
public class ForwardingBiddingService {
    @Autowired
    private ForwardingBiddingMapper biddingMapper;
    @Autowired
    private PurchaseRequirementMapper requirementMapper; // 假设有需求Mapper
    @Autowired
    private UserMapper userMapper; // 假设有用户Mapper

    public ForwardingBiddingDetailResponse getBiddingById(Long biddingId) {
        ForwardingBidding bidding = biddingMapper.selectById(biddingId);
        if (bidding == null) {
            return null; // 或者抛出异常
        }

        ForwardingBiddingDetailResponse response = new ForwardingBiddingDetailResponse();
        BeanUtils.copyProperties(bidding, response);

        // 填充关联信息
        if (bidding.getForwardingRequirementId() != null) {
            PurchaseRequirement requirement = requirementMapper.selectById(bidding.getForwardingRequirementId());
            if (requirement != null) {
                response.setRequirementTitle(requirement.getTitle());
            }
        }
        if (bidding.getForwarderId() != null) {
            User forwarder = userMapper.selectById(bidding.getForwarderId());
            if (forwarder != null) {
                response.setForwarderName(forwarder.getUsername());
                response.setForwarderCompanyName(forwarder.getCompany());
            }
        }
        // ... 填充其他字段

        return response;
    }
}

// 在Controller中返回ForwardingBiddingDetailResponse
@GetMapping("/api/v1/forwarding/bidding/{biddingId}")
public ResponseEntity<Result<ForwardingBiddingDetailResponse>> getBiddingById(@PathVariable Long biddingId) {
    ForwardingBiddingDetailResponse response = forwardingBiddingService.getBiddingById(biddingId);
    if (response == null) {
        return ResponseEntity.notFound().build();
    }
    return ResponseEntity.ok(Result.success(response));
}

// 前端接收到的JSON响应示例
/*
{
    "id": 123,
    "forwardingRequirementId": 101,
    "purchaseOrderId": 201,
    "requirementTitle": "采购一批电子元件",
    "forwarderId": 301,
    "forwarderName": "李明",
    "forwarderCompanyName": "全球物流",
    "biddingPrice": 1500.00,
    "currency": "USD",
    "shippingMethod": "海运",
    "shippingRoute": "上海港-洛杉矶港",
    "estimatedDays": 30,
    "description": "提供门到港服务，包含基本保险",
    "documents": ["url1", "url2"],
    "insuranceIncluded": true,
    "insuranceAmount": 50.00,
    "customsService": true,
    "needCertification": "0",
    "needFumigation": "1",
    "costExportPacking": 100.00,
    "costOriginInlandHaulage": 50.00,
    "costExportCustomsDocs": 30.00,
    "costOriginHandling": 20.00,
    "costMainFreight": 1200.00,
    "costFreightSurcharges": 10.00,
    "costCargoInsurance": 50.00,
    "costDestinationHandlingBySeller": 0.00,
    "costDestinationUnloadingBySeller": 0.00,
    "costDestinationInlandHaulageBySeller": 0.00,
    "costImportCustomsDocsBySeller": 0.00,
    "costImportDutiesTaxesBySeller": 0.00,
    "costForwarderServiceFee": 0.00,
    "deliveryTerms": "FOB",
    "status": "submitted",
    "auditStatus": "pending",
    "auditRemark": null,
    "winner": false,
    "trackingNumber": null,
    "trackingUrl": null,
    "orderId": null,
    "orderNumber": null,
    "createdAt": "2025-07-26T10:00:00",
    "updatedAt": "2025-07-26T10:00:00",
    "customedCosts": "[{\"name\":\"文件费\",\"amount\":20,\"currency\":\"USD\"}]",
    "estimatedPickupDate": "2025-08-01T09:00:00",
    "estimatedDeliveryDate": "2025-08-31T17:00:00"
}
*/
```

## 注意事项 (Notes)
*   **Lombok `@Data`:** 该类使用了 Lombok 的 `@Data` 注解，自动生成了 getter、setter、`equals`、`hashCode` 和 `toString` 方法，减少了样板代码。
*   **数据完整性:** 该 DTO 包含了竞价的几乎所有详细信息，适用于需要完整数据展示的场景。
*   **金额精度:** 使用 `BigDecimal` 类型来处理金额，可以避免浮点数计算的精度问题，这对于金融相关的操作至关重要。
*   **日期类型:** `LocalDateTime` 用于表示日期和时间，不包含时区信息。如果需要处理时区，应考虑使用 `ZonedDateTime` 或 `OffsetDateTime`。
*   **附件和自定义费用:** `documents` 字段是字符串列表，`customedCosts` 是 JSON 字符串，这意味着后端在构建此 DTO 时需要进行相应的序列化和反序列化处理。
*   **状态字段:** `status` 和 `auditStatus` 字段通常会映射到后端的一个枚举类型，在 DTO 中使用 `String` 类型是为了方便前端展示，但在业务逻辑层应使用枚举进行处理。
*   **关联数据填充:** `requirementTitle`, `forwarderName`, `forwarderCompanyName`, `orderNumber` 等字段需要通过关联查询或服务调用来填充，这通常在 Service 层完成。

```