# ForwardingBiddingAuditRequest.java

## 文件概述 (File Overview)
`ForwardingBiddingAuditRequest.java` 是一个数据传输对象（DTO），用于封装管理员审核货代竞价时提交的请求数据。它包含了待审核竞价的 ID、审核结果（批准或拒绝）以及可选的备注信息。该 DTO 使用 JSR 303/349（Bean Validation）注解对必填字段进行了非空验证，确保审核请求的合法性。

## 核心功能 (Core Functionality)
*   封装货代竞价审核所需的竞价 ID、审核结果和备注。
*   对竞价 ID 和审核结果进行非空验证。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `Long biddingId`:
    *   **说明:** 待审核竞价的唯一标识 ID。
    *   **验证:** `@NotNull(message = "竞价ID不能为空")` - 确保竞价 ID 不为 `null`。

*   `Boolean approved`:
    *   **说明:** 审核结果。`true` 表示批准，`false` 表示拒绝。
    *   **验证:** `@NotNull(message = "审核结果不能为空")` - 确保审核结果不为 `null`。

*   `String remark`:
    *   **说明:** 审核备注。例如，批准理由或拒绝原因。可选字段。

## 使用示例 (Usage Examples)
```java
// 在Controller中接收ForwardingBiddingAuditRequest作为请求体
@PostMapping("/api/v1/forwarding/bidding/audit")
@PreAuthorize("hasAuthority('admin')")
public ResponseEntity<Result<ForwardingBiddingDetailResponse>> auditBidding(
        @RequestBody @Validated ForwardingBiddingAuditRequest request) {
    // request.getBiddingId() 获取竞价ID
    // request.getApproved() 获取审核结果
    // request.getRemark() 获取备注
    // ... 业务逻辑处理
    return forwardingBiddingService.auditBidding(
            request.getBiddingId(), request.getApproved(), request.getRemark());
}

// 前端发送的JSON请求体示例 (批准竞价)
/*
{
    "biddingId": 123,
    "approved": true,
    "remark": "价格合理，符合要求"
}
*/

// 前端发送的JSON请求体示例 (拒绝竞价)
/*
{
    "biddingId": 456,
    "approved": false,
    "remark": "报价过高"
}
*/
```

## 注意事项 (Notes)
*   `ForwardingBiddingAuditRequest` 仅用于数据传输，不包含任何业务逻辑。
*   `@NotNull` 注解确保了 `biddingId` 和 `approved` 字段值不能为 `null`。
*   `remark` 字段是可选的，但对于拒绝操作，通常建议强制填写拒绝原因。
*   在 Controller 层使用 `@Validated` 注解来触发对 `ForwardingBiddingAuditRequest` 的验证。
*   该 DTO 通常与管理员权限结合使用，确保只有授权用户才能执行审核操作。