# ShippingStatus.md

## 1. 文件概述

`ShippingStatus` 是货代模块中的一个实体（Entity），位于 `com.purchase.forwarding.bidding.entity` 包中。它直接映射到数据库的 `shipping_status` 表，用于持久化存储物流状态的每一个节点。这个实体包含了物流状态的所有核心属性，如关联的竞价ID、订单ID、需求ID、状态码、状态名称、位置、描述、发生时间、预计到达时间、操作人、凭证图片、异常标记和异常原因等。它是物流跟踪系统核心业务流程的数据载体，通过MyBatis-Plus的注解实现了与数据库表的自动化ORM映射。

## 2. 核心功能

*   **数据持久化**: 定义了物流状态在数据库中的存储结构，是物流跟踪领域模型在持久化层的具体表现。
*   **多业务关联**: 通过 `forwardingBiddingId`, `orderId`, `forwardingRequirementId` 字段，实现了物流状态与竞价、订单、需求等多个业务实体的关联，便于追溯和查询。
*   **状态描述**: 包含了 `statusCode` 和 `statusName`，用于标准化和描述物流状态。
*   **时间追踪**: 记录了 `occurTime`（发生时间）和 `estimatedArrival`（预计到达时间），对于物流时间线和时效性分析至关重要。
*   **异常标记**: `exceptionFlag` 和 `exceptionReason` 字段用于标记和记录物流异常情况，便于问题排查和处理。
*   **MyBatis-Plus集成**: 通过 `@TableName`, `@TableId`, `@TableField`, `@FieldFill` 等注解与MyBatis-Plus框架深度集成，实现了主键自增长、字段映射和自动填充时间戳等功能，极大地简化了数据库操作。

## 3. 属性说明

- **`id` (Long)**: 物流状态记录的主键ID，自增长。
- **`forwardingBiddingId` (Long)**: 关联的货代竞价ID。
- **`orderId` (Long)**: 关联的订单ID。
- **`forwardingRequirementId` (Long)**: 关联的货代需求ID。
- **`statusCode` (String)**: 物流状态码（例如：`IN_TRANSIT`, `DELIVERED`, `EXCEPTION`）。
- **`statusName` (String)**: 物流状态名称（例如：`运输中`, `已送达`, `异常`）。
- **`location` (String)**: 物流发生时的位置信息。
- **`description` (String)**: 物流状态的详细描述。
- **`occurTime` (LocalDateTime)**: 物流状态发生的实际时间。
- **`estimatedArrival` (LocalDateTime)**: 预计到达时间。
- **`operatorId` (Long)**: 操作该物流状态的用户ID。
- **`proofImages` (String)**: 凭证图片路径，多张图片用逗号分隔。
- **`exceptionFlag` (Boolean)**: 异常标记，`true` 表示该状态为异常状态。
- **`exceptionReason` (String)**: 异常原因的详细描述。
- **`createdAt` (LocalDateTime)**: 记录创建时间，插入时自动填充。
- **`updatedAt` (LocalDateTime)**: 记录更新时间，插入和更新时自动填充。

## 4. 业务规则

*   **多关联**: 一个物流状态记录可以同时关联到货代竞价、订单和货代需求，但通常在特定业务流程中，只会有一个主关联ID。
*   **时间顺序**: 物流状态的记录应严格按照 `occurTime` 的时间顺序进行，以确保物流时间线的正确性。
*   **异常处理**: `exceptionFlag` 和 `exceptionReason` 字段用于标记和记录物流异常，这对于后续的异常处理和分析至关重要。
*   **数据完整性**: 关键字段（如 `statusCode`, `statusName`, `occurTime`）在创建时必须提供。

## 5. 使用示例

```java
// 1. 在 ShippingStatusMapper 接口中定义对 ShippingStatus 的操作
@Mapper
public interface ShippingStatusMapper extends BaseMapper<ShippingStatus> {
    // 继承 BaseMapper 提供了基本的 CRUD 方法
    // 也可以在此定义自定义查询方法，例如：
    @Select("SELECT * FROM shipping_status WHERE order_id = #{orderId} ORDER BY occur_time ASC")
    List<ShippingStatus> findByOrderIdOrderByOccurTimeAsc(@Param("orderId") Long orderId);

    @Select("SELECT * FROM shipping_status WHERE forwarding_bidding_id = #{biddingId} ORDER BY occur_time DESC LIMIT 1")
    ShippingStatus findLatestByBiddingId(@Param("biddingId") Long biddingId);
}

// 2. 在 ShippingStatusService 实现中创建和查询 ShippingStatus
@Service
public class ShippingStatusServiceImpl implements ShippingStatusService {
    @Autowired
    private ShippingStatusMapper shippingStatusMapper;

    @Transactional
    public ShippingStatusVO addShippingStatus(ShippingStatusUpdateRequest request) {
        ShippingStatus status = new ShippingStatus();
        // ... 将 request 中的数据复制到 status ...
        status.setForwardingBiddingId(request.getForwardingBiddingId());
        status.setOrderId(request.getOrderId());
        status.setForwardingRequirementId(request.getForwardingRequirementId());
        status.setStatusCode(request.getStatusCode());
        status.setStatusName(request.getStatusName());
        status.setLocation(request.getLocation());
        status.setDescription(request.getDescription());
        status.setOccurTime(request.getOccurTime() != null ? request.getOccurTime() : LocalDateTime.now());
        status.setExceptionFlag(false); // 默认非异常

        shippingStatusMapper.insert(status);
        return convertToVO(status);
    }

    public List<ShippingStatusVO> getStatusListByOrderId(Long orderId) {
        List<ShippingStatus> entities = shippingStatusMapper.findByOrderIdOrderByOccurTimeAsc(orderId);
        return entities.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    public ShippingStatusVO getLatestStatusByBiddingId(Long biddingId) {
        ShippingStatus entity = shippingStatusMapper.findLatestByBiddingId(biddingId);
        return entity != null ? convertToVO(entity) : null;
    }
}

// 3. 测试示例
@SpringBootTest
class ShippingStatusTest {
    @Autowired
    private ShippingStatusMapper shippingStatusMapper;

    @Test
    @Transactional
    void testInsertAndRetrieve() {
        ShippingStatus status = new ShippingStatus();
        status.setForwardingBiddingId(1L);
        status.setOrderId(10L);
        status.setStatusCode("IN_TRANSIT");
        status.setStatusName("运输中");
        status.setLocation("上海");
        status.setDescription("货物已从上海发出");
        status.setOccurTime(LocalDateTime.now());
        status.setExceptionFlag(false);

        int result = shippingStatusMapper.insert(status);
        assertThat(result).isEqualTo(1);
        assertThat(status.getId()).isNotNull();
        assertThat(status.getCreatedAt()).isNotNull();
        assertThat(status.getUpdatedAt()).isNotNull();

        ShippingStatus retrievedStatus = shippingStatusMapper.selectById(status.getId());
        assertThat(retrievedStatus).isNotNull();
        assertThat(retrievedStatus.getStatusCode()).isEqualTo("IN_TRANSIT");
        assertThat(retrievedStatus.getLocation()).isEqualTo("上海");
    }

    @Test
    @Transactional
    void testUpdateExceptionFlag() {
        ShippingStatus status = new ShippingStatus();
        status.setForwardingBiddingId(2L);
        status.setStatusCode("DELIVERED");
        status.setStatusName("已送达");
        status.setOccurTime(LocalDateTime.now());
        status.setExceptionFlag(false);
        shippingStatusMapper.insert(status);

        status.setExceptionFlag(true);
        status.setExceptionReason("客户拒收");
        shippingStatusMapper.updateById(status);

        ShippingStatus updatedStatus = shippingStatusMapper.selectById(status.getId());
        assertThat(updatedStatus.getExceptionFlag()).isTrue();
        assertThat(updatedStatus.getExceptionReason()).isEqualTo("客户拒收");
    }
}
```

## 6. 注意事项

*   **领域实体**: `ShippingStatus` 是一个典型的领域实体，它包含了数据和行为，并且其行为（如状态变更）直接反映了业务概念。
*   **MyBatis-Plus集成**: 充分利用MyBatis-Plus的注解和功能，可以大大简化数据访问层的开发。
*   **时间戳**: `createdAt` 和 `updatedAt` 的自动填充依赖于MyBatis-Plus的配置（如 `MyMetaObjectHandler`），确保了记录的创建和更新时间被准确捕获。
*   **多关联ID**: 实体中包含多个关联ID（`forwardingBiddingId`, `orderId`, `forwardingRequirementId`），这表明一个物流状态可能与多个业务流程相关联。在业务逻辑中需要明确其主次关系。
*   **数据类型**: `LocalDateTime` 用于时间字段，确保了时间的精确性和时区处理的便利性。
*   **图片路径**: `proofImages` 字段存储的是图片路径字符串，如果有多张，则使用逗号分隔。在业务逻辑中需要进行字符串解析和处理。
*   **异常标记**: `exceptionFlag` 和 `exceptionReason` 字段对于物流异常的追踪和处理至关重要。
*   **可扩展性**: 如果未来需要增加更多物流相关的属性，可以在实体中添加相应的字段。