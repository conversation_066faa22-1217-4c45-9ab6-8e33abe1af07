package com.purchase.forwarding.bidding.controller;

import com.purchase.common.exception.BusinessException;
import com.purchase.forwarding.bidding.dto.request.ShippingStatusUpdateRequest;
import com.purchase.forwarding.bidding.service.ShippingStatusService;
import com.purchase.forwarding.bidding.vo.ShippingStatusVO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 物流状态控制器
 */
@RestController
@RequestMapping("/api/v1/forwarding/shipping-status")
@RequiredArgsConstructor
public class ShippingStatusController {
    
    private final ShippingStatusService shippingStatusService;
    
    /**
     * 添加物流状态
     */
    @PostMapping
    @PreAuthorize("hasAnyAuthority('admin', 'forwarder')")
    public ResponseEntity<ShippingStatusVO> addShippingStatus(@RequestBody @Validated ShippingStatusUpdateRequest request) {
        return ResponseEntity.ok(shippingStatusService.addShippingStatus(request));
    }
    
    /**
     * 根据ID获取物流状态
     */
    @GetMapping("/{statusId}")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<ShippingStatusVO> getShippingStatusById(@PathVariable Long statusId) {
        return ResponseEntity.ok(shippingStatusService.getShippingStatusById(statusId));
    }
    
    /**
     * 根据竞价ID获取物流状态列表
     */
    @GetMapping("/bidding/{biddingId}")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<List<ShippingStatusVO>> getStatusListByBiddingId(@PathVariable Long biddingId) {
        return ResponseEntity.ok(shippingStatusService.getStatusListByBiddingId(biddingId));
    }
    
    /**
     * 根据订单ID获取物流状态列表
     */
    @GetMapping("/order/{orderId}")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<List<ShippingStatusVO>> getStatusListByOrderId(@PathVariable Long orderId) {
        return ResponseEntity.ok(shippingStatusService.getStatusListByOrderId(orderId));
    }
    
    /**
     * 根据需求ID获取物流状态列表
     */
    @GetMapping("/requirement/{requirementId}")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<List<ShippingStatusVO>> getStatusListByRequirementId(@PathVariable Long requirementId) {
        return ResponseEntity.ok(shippingStatusService.getStatusListByRequirementId(requirementId));
    }
    
    /**
     * 获取竞价的最新物流状态
     */
    @GetMapping("/bidding/{biddingId}/latest")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<ShippingStatusVO> getLatestStatusByBiddingId(@PathVariable Long biddingId) {
        return ResponseEntity.ok(shippingStatusService.getLatestStatusByBiddingId(biddingId));
    }
    
    /**
     * 获取竞价的异常状态列表
     */
    @GetMapping("/bidding/{biddingId}/exception")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<List<ShippingStatusVO>> getExceptionStatusByBiddingId(@PathVariable Long biddingId) {
        return ResponseEntity.ok(shippingStatusService.getExceptionStatusByBiddingId(biddingId));
    }
    
    /**
     * 标记状态为异常
     */
    @PostMapping("/{statusId}/mark-exception")
    @PreAuthorize("hasAnyAuthority('admin', 'forwarder')")
    public ResponseEntity<ShippingStatusVO> markAsException(@PathVariable Long statusId, @RequestBody Map<String, String> exceptionInfo) {
        String exceptionReason = exceptionInfo.get("exceptionReason");
        
        if (exceptionReason == null || exceptionReason.trim().isEmpty()) {
            throw new BusinessException(400, "异常原因不能为空");
        }
        
        return ResponseEntity.ok(shippingStatusService.markAsException(statusId, exceptionReason));
    }
    
    /**
     * 获取最新的状态名称
     */
    @GetMapping("/bidding/{biddingId}/latest-name")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<String> getLatestStatusName(@PathVariable Long biddingId) {
        return ResponseEntity.ok(shippingStatusService.getLatestStatusName(biddingId));
    }
} 