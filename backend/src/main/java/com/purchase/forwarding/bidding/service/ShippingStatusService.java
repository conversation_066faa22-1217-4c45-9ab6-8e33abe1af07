package com.purchase.forwarding.bidding.service;

import com.purchase.forwarding.bidding.dto.request.ShippingStatusUpdateRequest;
import com.purchase.forwarding.bidding.entity.ShippingStatus;
import com.purchase.forwarding.bidding.vo.ShippingStatusVO;

import java.util.List;

/**
 * 物流状态服务接口
 */
public interface ShippingStatusService {
    
    /**
     * 添加物流状态
     */
    ShippingStatusVO addShippingStatus(ShippingStatusUpdateRequest request);
    
    /**
     * 根据ID获取物流状态
     */
    ShippingStatusVO getShippingStatusById(Long statusId);
    
    /**
     * 根据竞价ID获取物流状态列表
     */
    List<ShippingStatusVO> getStatusListByBiddingId(Long biddingId);
    
    /**
     * 根据订单ID获取物流状态列表
     */
    List<ShippingStatusVO> getStatusListByOrderId(Long orderId);
    
    /**
     * 根据需求ID获取物流状态列表
     */
    List<ShippingStatusVO> getStatusListByRequirementId(Long requirementId);
    
    /**
     * 获取竞价的最新物流状态
     */
    ShippingStatusVO getLatestStatusByBiddingId(Long biddingId);
    
    /**
     * 获取竞价的异常状态列表
     */
    List<ShippingStatusVO> getExceptionStatusByBiddingId(Long biddingId);
    
    /**
     * 标记状态为异常
     */
    ShippingStatusVO markAsException(Long statusId, String exceptionReason);
    
    /**
     * 获取最新的状态名称
     */
    String getLatestStatusName(Long biddingId);
} 