# ForwardingBiddingCreateRequest.java

## 文件概述 (File Overview)
`ForwardingBiddingCreateRequest.java` 是一个数据传输对象（DTO），用于封装货代创建新的竞价时提交的请求数据。它包含了竞价所属的货代需求 ID、采购订单 ID、竞价金额、货代公司信息、运输方式、预计运输天数、描述、附件、保险、清关服务以及详细的费用构成和预计日期。该 DTO 使用 JSR 303/349（Bean Validation）注解对必填字段进行了非空、正数和长度验证，确保创建竞价请求的合法性。

## 核心功能 (Core Functionality)
*   **竞价信息封装:** 聚合了创建货代竞价所需的所有关键信息，包括与需求/订单的关联、报价、运输细节、费用明细等。
*   **费用明细支持:** 包含了多种国际贸易术语（Incoterms）下可能涉及的费用项目，如出口包装费、内陆运输费、报关费、主运费、保险费、目的地操作费、关税等，以及自定义费用项目。
*   **时间信息:** 包含预计提货日期和预计送达日期。
*   **字段验证:** 对 `forwardingRequirementId`、`biddingPrice`、`forwarderCompanyName`、`shippingMethod`、`estimatedDays` 进行非空、正数和长度验证。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `Long forwardingRequirementId`:
    *   **说明:** 关联的货代需求 ID。
    *   **验证:** `@NotNull(message = "货代需求ID不能为空")`。

*   `Long purchaseOrderId`: 关联的采购订单 ID。可选字段。

*   `BigDecimal biddingPrice`:
    *   **说明:** 竞价金额。
    *   **验证:** `@NotNull(message = "竞价金额不能为空")`，`@Positive(message = "竞价金额必须大于0")`。

*   `String forwarderCompanyName`:
    *   **说明:** 货代公司名称。
    *   **验证:** `@NotBlank(message = "公司名称不能为空")`。

*   `String forwarderName`: 货代联系人姓名。可选字段。

*   `String currency`: 货币类型。可选字段。

*   `String shippingMethod`:
    *   **说明:** 运输方式（例如：海运、空运、陆运）。
    *   **验证:** `@NotNull(message = "运输方式不能为空")`，`@Size(max = 100, message = "运输方式长度不能超过100")`。

*   `String shippingRoute`: 运输路线。可选字段。

*   `Integer estimatedDays`:
    *   **说明:** 预计运输天数。
    *   **验证:** `@NotNull(message = "预计运输天数不能为空")`，`@Positive(message = "预计运输天数必须大于0")`。

*   `String description`: 竞价描述。可选字段。

*   `String[] documents`: 附件文档的 URL 数组。可选字段。

*   `Boolean insuranceIncluded`: 是否包含保险。可选字段。

*   `BigDecimal insuranceAmount`: 保险金额。可选字段。

*   `Boolean customsService`: 是否提供清关服务。可选字段。

*   `String needCertification`: 是否需要产品认证（`0`-不需要，`1`-需要）。可选字段。

*   `String needFumigation`: 是否需要货物熏蒸（`0`-不需要，`1`-需要）。可选字段。

*   **费用字段 (BigDecimal):**
    *   `costExportPacking`: 出口包装费。
    *   `costOriginInlandHaulage`: 起运地内陆运输费。
    *   `costExportCustomsDocs`: 出口报关及单证费。
    *   `costOriginHandling`: 起运地操作费。
    *   `costMainFreight`: 主运费。
    *   `costFreightSurcharges`: 运费附加费。
    *   `costCargoInsurance`: 货物运输保险费。
    *   `costDestinationHandlingBySeller`: 目的地操作费 (卖方承担)。
    *   `costDestinationUnloadingBySeller`: 目的地卸货费 (卖方承担)。
    *   `costDestinationInlandHaulageBySeller`: 目的地内陆运输费 (卖方承担)。
    *   `costImportCustomsDocsBySeller`: 进口报关及单证费 (卖方承担-DDP)。
    *   `costImportDutiesTaxesBySeller`: 进口关税及税费 (卖方承担-DDP)。
    *   `costForwarderServiceFee`: 货代服务费/操作费 (卖方承担)。

*   `String deliveryTerms`:
    *   **说明:** 交货条款/国际贸易术语（例如: FOB, CIF）。
    *   **验证:** `@Size(max = 50, message = "交货条款长度不能超过50")`。

*   `String customedCosts`:
    *   **说明:** 自定义费用项目 JSON 字符串。格式: `[{"name":"费用名称","amount":金额,"description":"费用说明","currency":"USD"}]`。

*   `LocalDateTime estimatedPickupDate`: 预计提货日期。

*   `LocalDateTime estimatedDeliveryDate`: 预计送达日期。

## 使用示例 (Usage Examples)
```java
// 在Controller中接收ForwardingBiddingCreateRequest作为请求体
@PostMapping("/api/v1/forwarding/bidding")
@PreAuthorize("hasAuthority('forwarder')")
public ResponseEntity<Result<ForwardingBiddingDetailResponse>> createBidding(
        @RequestBody @Validated ForwardingBiddingCreateRequest request) {
    // request.getForwardingRequirementId() 获取货代需求ID
    // request.getBiddingPrice() 获取竞价金额
    // ... 业务逻辑处理
    return forwardingBiddingService.createBidding(request);
}

// 前端发送的JSON请求体示例
/*
{
    "forwardingRequirementId": 101,
    "purchaseOrderId": 201,
    "biddingPrice": 1500.00,
    "forwarderCompanyName": "全球物流",
    "forwarderName": "李明",
    "currency": "USD",
    "shippingMethod": "海运",
    "shippingRoute": "上海港-洛杉矶港",
    "estimatedDays": 30,
    "description": "提供门到港服务，包含基本保险",
    "documents": ["url1", "url2"],
    "insuranceIncluded": true,
    "insuranceAmount": 50.00,
    "customsService": true,
    "needCertification": "0",
    "needFumigation": "1",
    "costExportPacking": 100.00,
    "costMainFreight": 1200.00,
    "deliveryTerms": "FOB",
    "customedCosts": "[{\"name\":\"文件费\",\"amount\":20,\"currency\":\"USD\"}]",
    "estimatedPickupDate": "2025-08-01T09:00:00",
    "estimatedDeliveryDate": "2025-08-31T17:00:00"
}
*/
```

## 注意事项 (Notes)
*   `ForwardingBiddingCreateRequest` 仅用于数据传输，不包含任何业务逻辑。
*   **条件必填字段:** 尽管许多费用字段和特定服务字段是可选的，但在业务逻辑层（Service 层）需要根据 `shippingMethod` 或 `deliveryTerms` 等字段对这些字段进行条件验证，确保相应运输条款下的必填信息不缺失。
*   **金额精度:** 使用 `BigDecimal` 类型来处理金额，可以避免浮点数计算的精度问题，这对于金融相关的操作至关重要。
*   **日期类型:** `LocalDateTime` 用于表示日期和时间，不包含时区信息。如果需要处理时区，应考虑使用 `ZonedDateTime` 或 `OffsetDateTime`。
*   **附件和自定义费用:** `documents` 字段是字符串数组，`customedCosts` 是 JSON 字符串，这意味着后端需要进行相应的序列化和反序列化处理。
*   在 Controller 层使用 `@Validated` 注解来触发对 `ForwardingBiddingCreateRequest` 的验证。