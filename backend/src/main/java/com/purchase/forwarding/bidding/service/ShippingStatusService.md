# ShippingStatusService.md

## 1. 文件概述

`ShippingStatusService.java` 是货代模块中负责管理物流状态的核心服务接口，位于 `com.purchase.forwarding.bidding.service` 包中。该接口定义了物流跟踪信息的完整生命周期管理，从添加新的状态节点，到根据不同业务实体（竞价、订单、需求）查询状态列表，再到获取最新状态和处理异常状态。它是实现货物运输过程透明化、可追踪的关键业务逻辑层，为前端展示物流时间线和后端监控物流状态提供了核心支持。

## 2. 核心功能

*   **状态添加**: 提供 `addShippingStatus` 方法，用于在物流时间线上新增一个状态节点，如“已揽收”、“运输中”、“已签收”等。
*   **多维度查询**: 支持通过竞价ID (`BiddingId`)、订单ID (`OrderId`) 或需求ID (`RequirementId`) 来查询一个完整的物流状态列表，体现了物流信息与多个业务实体的关联性。
*   **最新状态获取**: 提供了 `getLatestStatusByBiddingId` 和 `getLatestStatusName` 两个便捷方法，用于快速获取某个业务流程的最新物流进展，常用于在列表页或摘要视图中展示。
*   **异常处理**: 支持查询指定业务流程中的所有异常状态记录 (`getExceptionStatusByBiddingId`)，并能将某个正常状态标记为异常 (`markAsException`)，便于客服和运营人员介入处理。
*   **数据转换**: 接口返回类型为 `ShippingStatusVO`，意味着其实现类需要负责将数据库实体 `ShippingStatus` 转换为对前端友好的视图对象。
*   **业务关联**: 接口的设计体现了物流状态是连接货代需求、竞价和最终订单的纽带，是整个货代流程的重要组成部分。

## 3. 接口说明

### 物流状态操作接口

#### addShippingStatus - 添加物流状态
*   **方法签名**: `ShippingStatusVO addShippingStatus(ShippingStatusUpdateRequest request)`
*   **参数**:
    *   `request` (ShippingStatusUpdateRequest): 一个DTO，包含了创建新状态所需的信息，如关联的竞价ID、状态名称、描述、发生时间等。
*   **返回值**: `ShippingStatusVO` - 创建成功后的物流状态视图对象。
*   **业务逻辑**: 服务实现需要验证请求的合法性（如竞价ID是否存在），然后创建一个 `ShippingStatus` 实体并持久化到数据库，最后将实体转换为VO返回。

#### getStatusListByBiddingId - 根据竞价ID获取物流状态列表
*   **方法签名**: `List<ShippingStatusVO> getStatusListByBiddingId(Long biddingId)`
*   **参数**:
    *   `biddingId` (Long): 竞价ID。
*   **返回值**: `List<ShippingStatusVO>` - 与该竞价关联的所有物流状态记录，通常按发生时间排序。
*   **业务逻辑**: 查询数据库中所有 `bidding_id` 匹配的记录，并将它们转换为VO列表。

#### getLatestStatusByBiddingId - 获取竞价的最新物流状态
*   **方法签名**: `ShippingStatusVO getLatestStatusByBiddingId(Long biddingId)`
*   **参数**:
    *   `biddingId` (Long): 竞价ID。
*   **返回值**: `ShippingStatusVO` - 最新的那条物流状态记录的视图对象。
*   **业务逻辑**: 查询数据库中指定 `bidding_id` 的记录，并按时间戳（`created_at` 或 `event_time`）降序排列，取第一条记录进行转换。

#### markAsException - 标记状态为异常
*   **方法签名**: `ShippingStatusVO markAsException(Long statusId, String exceptionReason)`
*   **参数**:
    *   `statusId` (Long): 需要标记为异常的状态记录的ID。
    *   `exceptionReason` (String): 异常原因的文字描述。
*   **返回值**: `ShippingStatusVO` - 更新后的物流状态视图对象。
*   **业务逻辑**: 找到指定ID的状态记录，将其中的某个字段（如 `is_exception`）标记为 `true`，并填充 `exception_reason` 字段，然后保存更新。

## 4. 业务规则

*   **时间线排序**: 所有返回列表的方法（如 `getStatusListBy...`）的实现，都应确保返回的物流状态是按照事件发生时间（`event_time`）升序排列的，以正确地构成物流时间线。
*   **数据一致性**: `addShippingStatus` 的实现需要确保关联的业务ID（`biddingId`, `orderId` 等）是真实有效的，防止产生孤立的物流数据。
*   **状态定义**: 系统中应有一套预定义的、标准的物流状态名称（如 `PENDING`, `IN_TRANSIT`, `DELIVERED`, `EXCEPTION`），`addShippingStatus` 在接收请求时最好能校验状态名称的有效性。
*   **异常状态**: 被标记为异常的状态记录，在前端展示时应有明显的视觉区别（如红色高亮），并可能触发通知或报警给相关人员。

## 5. 使用示例

```java
// 1. 服务实现类示例
@Service
@Transactional
public class ShippingStatusServiceImpl implements ShippingStatusService {
    @Autowired
    private ShippingStatusRepository statusRepository;
    @Autowired
    private BiddingRepository biddingRepository; // 用于校验

    @Override
    public ShippingStatusVO addShippingStatus(ShippingStatusUpdateRequest request) {
        // 1. 校验竞价是否存在
        biddingRepository.findById(request.getBiddingId()).orElseThrow(() -> new EntityNotFoundException("竞价不存在"));

        // 2. 创建实体
        ShippingStatus status = new ShippingStatus();
        BeanUtils.copyProperties(request, status);
        status.setEventTime(request.getEventTime() != null ? request.getEventTime() : LocalDateTime.now());
        status.setIsException(false);

        // 3. 持久化
        ShippingStatus savedStatus = statusRepository.save(status);

        // 4. 转换并返回VO
        return convertToVO(savedStatus);
    }
    // ... 其他方法的实现 ...
}

// 2. 在货代操作的业务逻辑中调用
@Service
public class ForwarderOperationService {
    @Autowired
    private ShippingStatusService shippingStatusService;

    // 货代更新了物流状态
    public void updateShipmentProgress(ShippingStatusUpdateRequest update) {
        // 直接调用服务添加新的状态节点
        shippingStatusService.addShippingStatus(update);
        
        // 可能还需要发送通知给买家
        // notificationService.notifyBuyer(...);
    }
}

// 3. 在前端展示物流时间线的Controller中调用
@RestController
@RequestMapping("/api/v1/orders/{orderId}/shipping-status")
public class OrderShippingController {
    @Autowired
    private ShippingStatusService shippingStatusService;

    @GetMapping
    public Result<List<ShippingStatusVO>> getTimeline(@PathVariable Long orderId) {
        List<ShippingStatusVO> timeline = shippingStatusService.getStatusListByOrderId(orderId);
        return Result.success(timeline);
    }
}

// 4. 测试示例
@SpringBootTest
class ShippingStatusServiceTest {
    @Autowired
    private ShippingStatusService shippingStatusService;
    @MockBean
    private ShippingStatusRepository statusRepository;

    @Test
    void testGetLatestStatusByBiddingId() {
        // 准备模拟数据
        ShippingStatus oldStatus = new ShippingStatus(); // ... 设置旧状态
        ShippingStatus latestStatus = new ShippingStatus(); // ... 设置新状态
        List<ShippingStatus> mockList = Arrays.asList(latestStatus, oldStatus);

        // 模拟Repository的行为
        when(statusRepository.findByBiddingIdOrderByEventTimeDesc(1L)).thenReturn(mockList);

        // 调用服务方法
        ShippingStatusVO result = shippingStatusService.getLatestStatusByBiddingId(1L);

        // 断言结果
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(latestStatus.getId());
    }
}
```

## 6. 注意事项

*   **事务管理**: `addShippingStatus` 和 `markAsException` 等写操作方法，在其实现类中必须声明为事务性的 (`@Transactional`)。
*   **权限控制**: 虽然接口本身未定义权限，但在其实现或调用方（如Controller）中，必须对操作进行权限校验，确保只有相关的货代或管理员才能更新物流状态。
*   **查询性能**: `getStatusListBy...` 方法的实现需要关注性能。应在 `shipping_status` 表中为 `bidding_id`, `order_id`, `requirement_id` 等用于查询的字段建立索引。
*   **VO转换**: `convertToVO` 的逻辑应封装在实现类中，避免在多个地方重复编写。转换时可能需要关联查询其他信息（如操作人名称），要注意避免N+1问题。
*   **空值处理**: 当查询结果为空时（如 `getLatestStatusByBiddingId` 未找到任何状态），服务应返回 `null` 或一个表示“无状态”的特定对象，调用方需要进行判空处理。
*   **异常与错误**: `markAsException` 是业务定义的“异常状态”，与系统运行时抛出的`Exception`是两个不同层面的概念，需要清晰地区分。
*   **接口粒度**: 接口设计粒度适中，既有获取完整列表的方法，也有获取最新单个状态的便捷方法，满足了不同场景的需求。
*   **命名一致性**: 接口方法命名清晰，如 `getStatusListBy...`，`getLatestStatusBy...`，易于理解其功能。