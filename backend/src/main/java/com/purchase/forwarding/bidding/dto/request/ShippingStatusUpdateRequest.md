# ShippingStatusUpdateRequest.java

## 文件概述 (File Overview)
`ShippingStatusUpdateRequest.java` 是一个数据传输对象（DTO），用于封装货代或管理员添加/更新物流状态时提交的请求数据。它包含了关联的货代竞价 ID、状态代码、位置、描述、状态发生时间、预计到达时间、证明图片、异常标志和异常原因。该 DTO 使用 JSR 303/349（Bean Validation）注解对必填字段进行了非空验证，确保物流状态更新请求的合法性。

## 核心功能 (Core Functionality)
*   **物流状态信息封装:** 聚合了物流状态的所有关键属性，包括与竞价的关联、状态详情、时间信息和异常信息。
*   **时间戳:** 包含 `occurTime`（状态发生时间）和 `estimatedArrival`（预计到达时间）。
*   **异常处理:** 包含 `exceptionFlag`（异常标志）和 `exceptionReason`（异常原因），用于记录物流异常情况。
*   **证明图片:** `proofImages` 字段用于存储与状态更新相关的证明图片 URL 数组。
*   **字段验证:** 对 `forwardingBiddingId`、`statusCode` 和 `occurTime` 进行非空验证。

## 接口说明 (Interface Description)

### 字段 (Fields)
*   `Long forwardingBiddingId`:
    *   **说明:** 关联的货代竞价 ID。
    *   **验证:** `@NotNull(message = "竞价ID不能为空")`。

*   `String statusCode`:
    *   **说明:** 物流状态代码（例如：`IN_TRANSIT`, `DELIVERED`, `EXCEPTION`）。
    *   **验证:** `@NotBlank(message = "状态代码不能为空")`。

*   `String location`: 状态发生地点。可选字段。

*   `String description`: 状态描述。可选字段。

*   `LocalDateTime occurTime`:
    *   **说明:** 状态发生时间。
    *   **验证:** `@NotNull(message = "状态发生时间不能为空")`。

*   `LocalDateTime estimatedArrival`: 预计到达时间。可选字段。

*   `String[] proofImages`: 证明图片 URL 数组。可选字段。

*   `Boolean exceptionFlag`: 是否为异常状态。默认值为 `false`。

*   `String exceptionReason`: 异常原因。仅当 `exceptionFlag` 为 `true` 时相关。可选字段。

## 使用示例 (Usage Examples)
```java
// 在Controller中接收ShippingStatusUpdateRequest作为请求体
@PostMapping("/api/v1/forwarding/shipping-status")
@PreAuthorize("hasAnyAuthority('admin', 'forwarder')")
public ResponseEntity<ShippingStatusVO> addShippingStatus(
        @RequestBody @Validated ShippingStatusUpdateRequest request) {
    // request.getForwardingBiddingId() 获取竞价ID
    // request.getStatusCode() 获取状态代码
    // request.getOccurTime() 获取状态发生时间
    // ... 业务逻辑处理
    return ResponseEntity.ok(shippingStatusService.addShippingStatus(request));
}

// 前端发送的JSON请求体示例 (添加正常状态)
/*
{
    "forwardingBiddingId": 123,
    "statusCode": "IN_TRANSIT",
    "location": "上海仓库",
    "description": "货物已从仓库发出，正在运往港口",
    "occurTime": "2025-07-27T10:00:00",
    "estimatedArrival": "2025-08-10T18:00:00",
    "proofImages": ["http://example.com/proof1.jpg"]
}
*/

// 前端发送的JSON请求体示例 (添加异常状态)
/*
{
    "forwardingBiddingId": 123,
    "statusCode": "EXCEPTION",
    "location": "海关",
    "description": "货物在海关被扣留",
    "occurTime": "2025-07-27T15:30:00",
    "exceptionFlag": true,
    "exceptionReason": "文件不全，等待补充材料"
}
*/
```

## 注意事项 (Notes)
*   `ShippingStatusUpdateRequest` 仅用于数据传输，不包含任何业务逻辑。
*   **时间类型:** `LocalDateTime` 用于表示日期和时间，不包含时区信息。如果需要处理时区，应考虑使用 `ZonedDateTime` 或 `OffsetDateTime`。
*   **状态代码:** `statusCode` 字段通常会映射到后端的一个枚举类型，在 DTO 中使用 `String` 类型是为了方便前端传输，但在业务逻辑层应使用枚举进行处理。
*   **异常字段:** `exceptionFlag` 和 `exceptionReason` 字段的逻辑关联（即当 `exceptionFlag` 为 `true` 时 `exceptionReason` 必填）通常需要在业务逻辑层（Service 层）进行条件验证，DTO 层面无法直接强制。
*   **证明图片:** `proofImages` 字段是字符串数组，这意味着后端需要进行相应的序列化和反序列化处理，并且这些 URL 通常指向对象存储服务。
*   在 Controller 层使用 `@Validated` 注解来触发对 `ShippingStatusUpdateRequest` 的验证。