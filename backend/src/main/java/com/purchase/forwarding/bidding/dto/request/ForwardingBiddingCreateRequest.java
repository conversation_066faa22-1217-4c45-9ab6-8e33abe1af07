package com.purchase.forwarding.bidding.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 货代竞价创建请求
 */
@Data
public class ForwardingBiddingCreateRequest {
    
    @NotNull(message = "货代需求ID不能为空")
    private Long forwardingRequirementId;
    
    private Long purchaseOrderId;
    
    @NotNull(message = "竞价金额不能为空")
    @Positive(message = "竞价金额必须大于0")
    private BigDecimal biddingPrice;

    @NotBlank(message = "公司名称不能为空")
    private String forwarderCompanyName;
    private String forwarderName;
    
    private String currency;
    
    @NotNull(message = "运输方式不能为空")
    @Size(max = 100, message = "运输方式长度不能超过100")
    private String shippingMethod;
    
    private String shippingRoute;
    
    @NotNull(message = "预计运输天数不能为空")
    @Positive(message = "预计运输天数必须大于0")
    private Integer estimatedDays;
    
    private String description;
    
    private String[] documents;
    
    private Boolean insuranceIncluded;
    
    private BigDecimal insuranceAmount;
    
    private Boolean customsService;

    /**
     * 是否需要产品认证：0-不需要，1-需要
     */
    private String needCertification;

    /**
     * 是否需要货物熏蒸：0-不需要，1-需要
     */
    private String needFumigation;

    // 新增费用字段 - 根据运输条款可选填写
    private BigDecimal costExportPacking; // 出口包装费 (卖方承担)
    
    private BigDecimal costOriginInlandHaulage; // 起运地内陆运输费 (卖方承担)
    
    private BigDecimal costExportCustomsDocs; // 出口报关及单证费 (卖方承担)
    
    private BigDecimal costOriginHandling; // 起运地操作费 (卖方承担)
    
    private BigDecimal costMainFreight; // 主运费 (卖方承担部分)
    
    private BigDecimal costFreightSurcharges; // 运费附加费 (卖方承担部分)
    
    private BigDecimal costCargoInsurance; // 货物运输保险费 (卖方支付的保费)
    
    private BigDecimal costDestinationHandlingBySeller; // 目的地操作费 (卖方承担)
    
    private BigDecimal costDestinationUnloadingBySeller; // 目的地卸货费 (卖方承担)
    
    private BigDecimal costDestinationInlandHaulageBySeller; // 目的地内陆运输费 (卖方承担)
    
    private BigDecimal costImportCustomsDocsBySeller; // 进口报关及单证费 (卖方承担-DDP)
    
    private BigDecimal costImportDutiesTaxesBySeller; // 进口关税及税费 (卖方承担-DDP)
    
    private BigDecimal costForwarderServiceFee; // 货代服务费/操作费 (卖方承担)
    
    @Size(max = 50, message = "交货条款长度不能超过50")
    private String deliveryTerms; // 交货条款/国际贸易术语 (例如: FOB, CIF)
    
    /**
     * 自定义费用项目 JSON字符串
     * 格式: [{"name":"费用名称","amount":金额,"description":"费用说明","currency":"USD"}]
     */
    private String customedCosts;

    private LocalDateTime estimatedPickupDate; // 预计提货日期

    private LocalDateTime estimatedDeliveryDate; // 预计送达日期
} 