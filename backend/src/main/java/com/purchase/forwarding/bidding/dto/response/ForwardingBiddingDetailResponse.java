package com.purchase.forwarding.bidding.dto.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 货代竞价详情响应
 */
@Data
public class ForwardingBiddingDetailResponse {
    
    private Long id;
    
    private Long forwardingRequirementId;

    private Long purchaseOrderId;
    
    private String requirementTitle;
    
    private Long forwarderId;
    
    private String forwarderName;
    
    private String forwarderCompanyName;
    
    private BigDecimal biddingPrice;
    
    private String currency;
    
    private String shippingMethod;
    
    private String shippingRoute;
    
    private Integer estimatedDays;
    
    private String description;
    
    private List<String> documents;
    
    private Boolean insuranceIncluded;
    
    private BigDecimal insuranceAmount;
    
    private Boolean customsService;

    /**
     * 是否需要产品认证：0-不需要，1-需要
     */
    private String needCertification;

    /**
     * 是否需要货物熏蒸：0-不需要，1-需要
     */
    private String needFumigation;

    // 新增费用字段 - 根据运输条款可选填写
    private BigDecimal costExportPacking; // 出口包装费 (卖方承担)
    
    private BigDecimal costOriginInlandHaulage; // 起运地内陆运输费 (卖方承担)
    
    private BigDecimal costExportCustomsDocs; // 出口报关及单证费 (卖方承担)
    
    private BigDecimal costOriginHandling; // 起运地操作费 (卖方承担)
    
    private BigDecimal costMainFreight; // 主运费 (卖方承担部分)
    
    private BigDecimal costFreightSurcharges; // 运费附加费 (卖方承担部分)
    
    private BigDecimal costCargoInsurance; // 货物运输保险费 (卖方支付的保费)
    
    private BigDecimal costDestinationHandlingBySeller; // 目的地操作费 (卖方承担)
    
    private BigDecimal costDestinationUnloadingBySeller; // 目的地卸货费 (卖方承担)
    
    private BigDecimal costDestinationInlandHaulageBySeller; // 目的地内陆运输费 (卖方承担)
    
    private BigDecimal costImportCustomsDocsBySeller; // 进口报关及单证费 (卖方承担-DDP)
    
    private BigDecimal costImportDutiesTaxesBySeller; // 进口关税及税费 (卖方承担-DDP)
    
    private BigDecimal costForwarderServiceFee; // 货代服务费/操作费 (卖方承担)
    
    private String deliveryTerms; // 交货条款/国际贸易术语 (例如: FOB, CIF)
    
    private String status;
    
    private String auditStatus;
    
    private String auditRemark;
    
    private Boolean winner;
    
    private String trackingNumber;
    
    private String trackingUrl;
    
    private Long orderId;
    
    private String orderNumber;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;

    private String customedCosts;

    private LocalDateTime estimatedPickupDate; // 预计提货日期

    private LocalDateTime estimatedDeliveryDate; // 预计送达日期
} 