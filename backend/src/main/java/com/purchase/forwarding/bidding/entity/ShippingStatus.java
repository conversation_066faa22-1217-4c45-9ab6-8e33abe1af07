package com.purchase.forwarding.bidding.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 物流状态实体类
 */
@Data
@TableName("shipping_status")
public class ShippingStatus {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long forwardingBiddingId;
    
    private Long orderId;
    
    private Long forwardingRequirementId;
    
    private String statusCode;
    
    private String statusName;
    
    private String location;
    
    private String description;
    
    private LocalDateTime occurTime;
    
    private LocalDateTime estimatedArrival;
    
    private Long operatorId;
    
    private String proofImages;
    
    private Boolean exceptionFlag;
    
    private String exceptionReason;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}