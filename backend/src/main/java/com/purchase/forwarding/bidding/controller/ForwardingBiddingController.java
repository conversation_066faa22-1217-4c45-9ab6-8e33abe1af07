package com.purchase.forwarding.bidding.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.common.response.Result;
import com.purchase.common.exception.BusinessException;
import com.purchase.forwarding.bidding.dto.request.ForwardingBiddingCreateRequest;
import com.purchase.forwarding.bidding.dto.request.ForwardingBiddingUpdateRequest;
import com.purchase.forwarding.bidding.dto.request.ForwardingBiddingAuditRequest;
import com.purchase.forwarding.bidding.dto.response.ForwardingBiddingDetailResponse;
import com.purchase.forwarding.bidding.service.ForwardingBiddingService;
import com.purchase.forwarding.bidding.vo.ForwardingBiddingVO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 货代竞价控制器
 */
@RestController
@RequestMapping("/api/v1/forwarding/bidding")
@RequiredArgsConstructor
public class ForwardingBiddingController {
    
    private final ForwardingBiddingService forwardingBiddingService;
    
    /**
     * 根据采购订单ID获取货代竞价列表
     *
     * @param purchaseOrderId 采购订单ID
     * @return 竞价列表
     */
    @GetMapping("/purchase-order/{purchaseOrderId}")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'buyer', 'forwarder')")
    public ResponseEntity<Result<List<ForwardingBiddingVO>>> getBiddingsByPurchaseOrderId(@PathVariable Long purchaseOrderId) {
        List<ForwardingBiddingVO> biddings = forwardingBiddingService.getBiddingsByPurchaseOrderId(purchaseOrderId);
        return ResponseEntity.ok(Result.success(biddings));
    }
    /**
     * 创建竞价
     */
    @PostMapping
    @PreAuthorize("hasAuthority('forwarder')")
    public ResponseEntity<Result<ForwardingBiddingDetailResponse>> createBidding(@RequestBody @Validated ForwardingBiddingCreateRequest request) {
        ForwardingBiddingDetailResponse response = forwardingBiddingService.createBidding(request);
        return ResponseEntity.ok(Result.success(response));
    }
    
    /**
     * 更新竞价
     */
    @PutMapping
    @PreAuthorize("hasAuthority('forwarder')")
    public ResponseEntity<Result<ForwardingBiddingDetailResponse>> updateBidding(@RequestBody @Validated ForwardingBiddingUpdateRequest request) {
        ForwardingBiddingDetailResponse response = forwardingBiddingService.updateBidding(request);
        return ResponseEntity.ok(Result.success(response));
    }
    
    /**
     * 取消竞价
     */
    @DeleteMapping("/{biddingId}")
    @PreAuthorize("hasAuthority('forwarder')")
    public ResponseEntity<Void> cancelBidding(@PathVariable Long biddingId) {
        forwardingBiddingService.cancelBidding(biddingId);
        return ResponseEntity.ok().build();
    }
    
    /**
     * 获取竞价详情
     */
    @GetMapping("/{biddingId}")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<Result<ForwardingBiddingDetailResponse>> getBiddingById(@PathVariable Long biddingId) {
        ForwardingBiddingDetailResponse response = forwardingBiddingService.getBiddingById(biddingId);
        return ResponseEntity.ok(Result.success(response));
    }
    
    /**
     * 获取需求的所有竞价
     */
    @GetMapping("/requirement/{requirementId}")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<Result<List<ForwardingBiddingVO>>> getBiddingsByRequirementId(@PathVariable Long requirementId) {
        List<ForwardingBiddingVO> biddings = forwardingBiddingService.getBiddingsByRequirementId(requirementId);
        return ResponseEntity.ok(Result.success(biddings));
    }
    // 增加一个接口，根据订单id获取所有的竞价
    @GetMapping("/order/{orderId}")
    @PreAuthorize("hasAnyAuthority('admin','seller', 'buyer')")
    public ResponseEntity<Result<List<ForwardingBiddingVO>>> getBiddingsByOrderId(@PathVariable Long orderId) {
        List<ForwardingBiddingVO> biddings = forwardingBiddingService.getBiddingsByOrderId(orderId);
        return ResponseEntity.ok(Result.success(biddings)); 
    }
    
    
    /**
     * 获取货代的所有竞价
     */
    @GetMapping("/forwarder/{forwarderId}")
    @PreAuthorize("hasAnyAuthority('admin', 'forwarder')")
    public ResponseEntity<Result<List<ForwardingBiddingVO>>> getBiddingsByForwarderId(@PathVariable Long forwarderId) {
        List<ForwardingBiddingVO> biddings = forwardingBiddingService.getBiddingsByForwarderId(forwarderId);
        return ResponseEntity.ok(Result.success(biddings));
    }
    
    /**
     * 获取当前货代的所有竞价
     */
    @GetMapping("/my")
    @PreAuthorize("hasAuthority('forwarder')")
    public ResponseEntity<Result<List<ForwardingBiddingVO>>> getMyBiddings() {
        List<ForwardingBiddingVO> biddings = forwardingBiddingService.getBiddingsByForwarderId(null);
        return ResponseEntity.ok(Result.success(biddings));
    }
    
    /**
     * 选择中标货代
     */
    @PostMapping("/{biddingId}/select-winner")
    @PreAuthorize("hasAnyAuthority('admin', 'seller')")
    public ResponseEntity<Result<ForwardingBiddingDetailResponse>> selectWinner(@PathVariable Long biddingId) {
        ForwardingBiddingDetailResponse response = forwardingBiddingService.selectWinner(biddingId);
        return ResponseEntity.ok(Result.success(response));
    }
    
    /**
     * 分页获取竞价列表
     */
    @GetMapping
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<Result<IPage<ForwardingBiddingVO>>> getBiddingPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long requirementId,
            @RequestParam(required = false) Long forwarderId,
            @RequestParam(required = false) String status) {
        IPage<ForwardingBiddingVO> page = forwardingBiddingService.getBiddingPage(current, size, requirementId, forwarderId, status);
        return ResponseEntity.ok(Result.success(page));
    }

    /**
     * 管理员获取所有货代竞价记录（增强版分页查询）
     * 
     * 提供丰富的过滤和排序选项，专为管理员设计
     * 
     * @param current 当前页码
     * @param size 每页大小
     * @param requirementId 需求ID（可选）
     * @param forwarderId 货代ID（可选）
     * @param status 状态（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param orderBy 排序字段（可选，默认为创建时间）
     * @param ascending 是否升序（可选，默认为降序）
     * @param purchaseOrderId 采购订单ID（可选）
     * @param minPrice 最低价格（可选）
     * @param maxPrice 最高价格（可选）
     * @param keyword 关键词搜索（可选，用于搜索描述或货代名称）
     * @return 分页竞价记录
     */
    @GetMapping("/admin/all")
    @PreAuthorize("hasAuthority('admin')")
    public ResponseEntity<Result<IPage<ForwardingBiddingVO>>> getAllBiddingsForAdmin(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long requirementId,
            @RequestParam(required = false) Long forwarderId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false, defaultValue = "created_at") String orderBy,
            @RequestParam(required = false, defaultValue = "false") Boolean ascending,
            @RequestParam(required = false) Long purchaseOrderId,
            @RequestParam(required = false) Double minPrice,
            @RequestParam(required = false) Double maxPrice,
            @RequestParam(required = false) String keyword) {
        
        IPage<ForwardingBiddingVO> page = forwardingBiddingService.getAllBiddingsForAdmin(
                current, size, requirementId, forwarderId, status, startDate, endDate,
                orderBy, ascending, purchaseOrderId, minPrice, maxPrice, keyword);
        
        return ResponseEntity.ok(Result.success(page));
    }
    
    /**
     * 更新物流跟踪信息
     */
    @PostMapping("/{biddingId}/tracking")
    @PreAuthorize("hasAnyAuthority('admin', 'forwarder')")
    public ResponseEntity<Result<ForwardingBiddingDetailResponse>> updateTrackingInfo(
            @PathVariable Long biddingId,
            @RequestBody Map<String, String> trackingInfo) {
        String trackingNumber = trackingInfo.get("trackingNumber");
        String trackingUrl = trackingInfo.get("trackingUrl");
        
        if (trackingNumber == null || trackingNumber.trim().isEmpty()) {
            throw new BusinessException(400, "物流跟踪号不能为空");
        }
        
        ForwardingBiddingDetailResponse response = forwardingBiddingService.updateTrackingInfo(biddingId, trackingNumber, trackingUrl);
        return ResponseEntity.ok(Result.success(response));
    }
    
    /**
     * 获取需求的中标竞价
     */
    @GetMapping("/requirement/{requirementId}/winner")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<Result<ForwardingBiddingDetailResponse>> getWinnerByRequirementId(@PathVariable Long requirementId) {
        ForwardingBiddingDetailResponse response = forwardingBiddingService.getWinnerByRequirementId(requirementId);
        return ResponseEntity.ok(Result.success(response));
    }
    
    /**
     * 获取需求的竞价数量
     */
    @GetMapping("/requirement/{requirementId}/count")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<Result<Integer>> getBiddingCount(@PathVariable Long requirementId) {
        Integer count = forwardingBiddingService.getBiddingCount(requirementId);
        return ResponseEntity.ok(Result.success(count));
    }
    
    /**
     * 接受竞价
     * 
     * 执行步骤:
     * 1. 根据ID获取竞价记录，验证竞价是否存在
     * 2. 获取当前用户ID和角色，进行权限验证
     * 3. 获取竞价关联的货代需求，并验证需求是否存在
     * 4. 验证用户权限 - 允许以下人员接受竞价:
     *    - 管理员
     *    - 需求创建者(买家/卖家)
     *    - 关联采购订单的买家
     *    - 关联采购订单的卖家
     * 5. 进行状态检查 - 只有pending或submitted状态的竞价可以被接受
     * 6. 检查是否已存在已接受的竞价，一个需求只能接受一个竞价
     * 7. 更新竞价状态为已接受(accepted)
     * 8. 更新需求状态为已完成(completed)
     * 9. 返回更新后的竞价详情
     * 
     * 自动流程:
     * - 接受竞价后会自动创建货代订单(在前端实现)
     * - 如果自动创建失败，可以在BiddingList组件中手动创建订单
     * 
     * @param biddingId 竞价ID
     * @return 更新后的竞价详情
     */
    @PostMapping("/{biddingId}/accept")
    @PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller')")
    public ResponseEntity<Result<ForwardingBiddingDetailResponse>> acceptBidding(@PathVariable Long biddingId) {
        ForwardingBiddingDetailResponse response = forwardingBiddingService.acceptBidding(biddingId);
        return ResponseEntity.ok(Result.success(response));
    }
    
    /**
     * 拒绝竞价
     */
    @PostMapping("/{biddingId}/reject")
    @PreAuthorize("hasAnyAuthority('admin', 'buyer', 'seller')")
    public ResponseEntity<Result<ForwardingBiddingDetailResponse>> rejectBidding(@PathVariable Long biddingId) {
        ForwardingBiddingDetailResponse response = forwardingBiddingService.rejectBidding(biddingId);
        return ResponseEntity.ok(Result.success(response));
    }
    
    /**
     * 管理员审核竞价
     * 
     * @param request 审核请求
     * @return 竞价详情
     */
    @PostMapping("/audit")
    @PreAuthorize("hasAuthority('admin')")
    public ResponseEntity<Result<ForwardingBiddingDetailResponse>> auditBidding(
            @RequestBody @Validated ForwardingBiddingAuditRequest request) {
        ForwardingBiddingDetailResponse response = forwardingBiddingService.auditBidding(
                request.getBiddingId(), request.getApproved(), request.getRemark());
        return ResponseEntity.ok(Result.success(response));
    }
    
    /**
     * 获取待审核的竞价列表
     * 
     * @param current 当前页码
     * @param size 每页大小
     * @return 待审核竞价分页列表
     */
    @GetMapping("/pending-audit")
    @PreAuthorize("hasAuthority('admin')")
    public ResponseEntity<Result<IPage<ForwardingBiddingVO>>> getPendingAuditBiddings(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        IPage<ForwardingBiddingVO> page = forwardingBiddingService.getPendingAuditBiddings(current, size);
        return ResponseEntity.ok(Result.success(page));
    }
    
    /**
     * 获取可见的竞价列表（审核通过的竞价和自己的竞价）
     * 
     * @param requirementId 需求ID
     * @return 可见的竞价列表
     */
    @GetMapping("/visible/requirement/{requirementId}")
    @PreAuthorize("hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')")
    public ResponseEntity<Result<List<ForwardingBiddingVO>>> getVisibleBiddingsByRequirementId(
            @PathVariable Long requirementId) {
        List<ForwardingBiddingVO> biddings = forwardingBiddingService.getVisibleBiddingsByRequirementId(requirementId);
        return ResponseEntity.ok(Result.success(biddings));
    }
} 