package com.purchase.forwarding.bidding.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.forwarding.bidding.dto.request.ForwardingBiddingCreateRequest;
import com.purchase.forwarding.bidding.dto.request.ForwardingBiddingUpdateRequest;
import com.purchase.forwarding.bidding.dto.response.ForwardingBiddingDetailResponse;
import com.purchase.forwarding.bidding.entity.ForwardingBidding;
import com.purchase.forwarding.bidding.vo.ForwardingBiddingVO;

import java.util.List;

/**
 * 货代竞价服务接口
 */
public interface ForwardingBiddingService {
    
    /**
     * 创建竞价
     */
    ForwardingBiddingDetailResponse createBidding(ForwardingBiddingCreateRequest request);
    
    /**
     * 更新竞价
     */
    ForwardingBiddingDetailResponse updateBidding(ForwardingBiddingUpdateRequest request);
    
    /**
     * 取消竞价
     */
    void cancelBidding(Long biddingId);
    
    /**
     * 根据ID获取竞价详情
     */
    ForwardingBiddingDetailResponse getBiddingById(Long biddingId);
    
    /**
     * 根据订单ID获取竞价详情
     */
    List<ForwardingBiddingVO> getBiddingsByOrderId(Long orderId);
    
    /**
     * 根据采购订单ID获取竞价列表
     */
    List<ForwardingBiddingVO> getBiddingsByPurchaseOrderId(Long purchaseOrderId);
    
    /**
     * 获取需求的所有竞价
     */
    List<ForwardingBiddingVO> getBiddingsByRequirementId(Long requirementId);
    
    /**
     * 获取货代的所有竞价
     */
    List<ForwardingBiddingVO> getBiddingsByForwarderId(Long forwarderId);
    
    /**
     * 选择中标货代
     */
    ForwardingBiddingDetailResponse selectWinner(Long biddingId);
    
    /**
     * 分页获取竞价列表
     */
    IPage<ForwardingBiddingVO> getBiddingPage(Integer current, Integer size, Long requirementId, Long forwarderId, String status);
    
    /**
     * 管理员获取所有货代竞价记录（增强版分页查询）
     * 
     * @param current 当前页码
     * @param size 每页大小
     * @param requirementId 需求ID（可选）
     * @param forwarderId 货代ID（可选）
     * @param status 状态（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param orderBy 排序字段（可选，默认为创建时间）
     * @param ascending 是否升序（可选，默认为降序）
     * @param purchaseOrderId 采购订单ID（可选）
     * @param minPrice 最低价格（可选）
     * @param maxPrice 最高价格（可选）
     * @param keyword 关键词搜索（可选，用于搜索描述或货代名称）
     * @return 分页竞价记录
     */
    IPage<ForwardingBiddingVO> getAllBiddingsForAdmin(
            Integer current, 
            Integer size, 
            Long requirementId, 
            Long forwarderId, 
            String status,
            String startDate,
            String endDate,
            String orderBy,
            Boolean ascending,
            Long purchaseOrderId,
            Double minPrice,
            Double maxPrice,
            String keyword);
    
    /**
     * 更新物流跟踪信息
     */
    ForwardingBiddingDetailResponse updateTrackingInfo(Long biddingId, String trackingNumber, String trackingUrl);
    
    /**
     * 获取需求的中标竞价
     */
    ForwardingBiddingDetailResponse getWinnerByRequirementId(Long requirementId);
    
    /**
     * 获取需求的竞价数量
     */
    int getBiddingCount(Long requirementId);

    /**
     * 根据ID获取竞价实体
     */
    ForwardingBidding getById(Long biddingId);

    /**
     * 接受竞价
     * @param biddingId 竞价ID
     * @return 竞价详情
     */
    ForwardingBiddingDetailResponse acceptBidding(Long biddingId);
    
    /**
     * 拒绝竞价
     * @param biddingId 竞价ID
     * @return 竞价详情
     */
    ForwardingBiddingDetailResponse rejectBidding(Long biddingId);
    
    /**
     * 管理员审核竞价
     * @param biddingId 竞价ID
     * @param approved 是否通过审核
     * @param remark 审核备注
     * @return 竞价详情
     */
    ForwardingBiddingDetailResponse auditBidding(Long biddingId, boolean approved, String remark);
    
    /**
     * 获取待审核的竞价列表
     * @param current 当前页码
     * @param size 每页大小
     * @return 待审核竞价分页列表
     */
    IPage<ForwardingBiddingVO> getPendingAuditBiddings(Integer current, Integer size);
    
    /**
     * 根据需求ID获取可见的竞价列表（审核通过的竞价和自己的竞价）
     * @param requirementId 需求ID
     * @return 可见的竞价列表
     */
    List<ForwardingBiddingVO> getVisibleBiddingsByRequirementId(Long requirementId);
} 