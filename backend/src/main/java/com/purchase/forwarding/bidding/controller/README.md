# com.purchase.forwarding.bidding.controller 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.forwarding.bidding.controller` 包包含了货代竞价模块的 RESTful API 控制器。这些控制器作为应用程序的对外接口，负责接收来自前端的 HTTP 请求，将请求数据绑定到 DTOs，调用应用服务层（`com.purchase.forwarding.bidding.service`）执行业务逻辑，并最终将处理结果封装为统一的 API 响应返回给客户端。该包提供了货代竞价的创建、更新、取消、查询、选择中标货代、更新物流跟踪信息、接受/拒绝竞价以及管理员审核等功能，确保了 API 的安全性和健壮性。

## 目录结构概览 (Directory Structure Overview)
*   `ForwardingBiddingController.java`: 货代竞价的 RESTful API 控制器，处理竞价的增删改查和状态管理。
*   `ShippingStatusController.java`: 物流状态的 RESTful API 控制器，处理物流状态的添加和查询。
*   `ForwardingBiddingController.md`: `ForwardingBiddingController.java` 的文档。
*   `ShippingStatusController.md`: `ShippingStatusController.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.forwarding.bidding.controller` 包中的控制器与以下组件紧密协作，共同实现了货代竞价模块的对外功能：

1.  **应用服务层 (`com.purchase.forwarding.bidding.service`):**
    *   `ForwardingBiddingController` 和 `ShippingStatusController` 通过 `@RequiredArgsConstructor` 自动注入 `ForwardingBiddingService` 和 `ShippingStatusService` 的实例。它们是控制器与后端业务逻辑之间的主要交互点。
    *   控制器将接收到的请求 DTO（来自 `com.purchase.forwarding.bidding.dto` 包）直接传递给应用服务层的方法，并接收应用服务层返回的响应 DTO 或 VO。

2.  **数据传输对象 (DTOs) 和视图对象 (VOs):** (`com.purchase.forwarding.bidding.dto` 和 `com.purchase.forwarding.bidding.vo`)
    *   控制器方法使用 `ForwardingBiddingCreateRequest`, `ForwardingBiddingUpdateRequest`, `ForwardingBiddingAuditRequest`, `ShippingStatusUpdateRequest` 等 DTO 作为 `@RequestBody` 参数来接收前端的请求数据。这些 DTO 通常会进行 JSR 303/349（Bean Validation）验证。
    *   控制器方法返回 `ForwardingBiddingDetailResponse`, `ForwardingBiddingVO`, `ShippingStatusVO` 等 DTO/VO，这些对象封装了业务处理结果，并通过 `org.springframework.http.ResponseEntity` 和 `com.purchase.common.response.Result` 进行统一包装。

3.  **Spring Security 权限控制:**
    *   控制器方法广泛使用 `@PreAuthorize` 注解来定义访问权限。例如，创建和更新竞价通常只允许 `forwarder` 角色，而审核竞价只允许 `admin` 角色，查询接口则可能对 `admin`, `seller`, `buyer`, `forwarder` 开放。
    *   这确保了只有经过认证且具有相应权限的用户才能访问特定的 API 资源。

4.  **统一响应格式:**
    *   所有控制器方法都返回 `ResponseEntity<Result<T>>` 或 `ResponseEntity<T>`。`Result`（来自 `com.purchase.common.response` 包）提供了一个标准化的响应结构，包含 `code`、`message` 和 `data`，便于前端统一处理。`ResponseEntity` 允许控制器灵活控制 HTTP 状态码。

**协作流程总结:**

*   **请求接收与验证:** 前端发送 HTTP 请求到 `controller` 包中的某个控制器。Spring MVC 将请求体绑定到相应的 DTO，并触发 DTO 中定义的验证规则。如果验证失败，请求会在进入控制器方法之前被拦截并返回错误响应。
*   **权限检查:** Spring Security 的 `@PreAuthorize` 注解在方法执行前进行权限检查。
*   **调用应用服务:** 验证和权限检查通过后，控制器从 DTO 中提取必要的数据，并调用 `service` 包中的应用服务方法执行业务逻辑。
*   **处理业务结果与异常:** 应用服务返回业务处理结果（响应 DTO/VO），或者抛出业务异常。如果抛出异常，通常由全局异常处理器（如 `com.purchase.common.exception.GlobalExceptionHandler`）捕获并处理。
*   **封装响应:** 控制器将响应 DTO/VO 封装在 `Result` 中，并结合 `ResponseEntity` 设置适当的 HTTP 状态码。
*   **响应返回:** 最终，一个统一格式的 HTTP 响应（无论是成功数据还是错误信息）被返回给前端。

这种接口层设计确保了 API 的清晰性、安全性、可维护性，并为前端提供了友好且一致的交互体验，同时将业务逻辑与外部交互细节分离。
