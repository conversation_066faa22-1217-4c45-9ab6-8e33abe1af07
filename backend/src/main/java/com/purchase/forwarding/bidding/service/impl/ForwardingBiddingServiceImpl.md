# ForwardingBiddingServiceImpl.md

## 1. 文件概述

`ForwardingBiddingServiceImpl.java` 是货代模块中 `ForwardingBiddingService` 接口的实现类，位于 `com.purchase.forwarding.bidding.service.impl` 包中。它继承自MyBatis-Plus的 `ServiceImpl`，提供了货代竞价业务逻辑的具体实现。该服务负责处理货代竞价的创建、更新、取消、查询、接受、拒绝、审核以及物流信息更新等核心业务流程。它协调多个领域服务和仓储（如 `ForwardingBiddingMapper`, `ForwardingRequirementService`, `UserService`, `NotificationService`），并严格执行权限校验和业务规则，确保竞价流程的正确性和数据一致性。

## 2. 核心功能

*   **竞价生命周期管理**: 实现了竞价的提交 (`createBidding`)、更新 (`updateBidding`)、取消 (`cancelBidding`)、接受 (`acceptBidding`) 和拒绝 (`rejectBidding`) 等核心操作。
*   **权限与角色校验**: 在所有关键操作中，严格校验当前用户的角色和权限，确保只有授权用户才能执行相应操作（例如，只有货代可以提交竞价，只有需求创建者或管理员可以接受/拒绝竞价）。
*   **多维度查询与过滤**: 提供了多种查询方法，支持按需求ID、货代ID、采购订单ID、状态等条件进行查询，并根据用户角色进行数据过滤。
*   **竞价审核**: 实现了管理员对竞价的审核功能 (`auditBidding`)，包括审核通过和拒绝，并触发相应的通知。
*   **物流信息更新**: 支持更新竞价的物流跟踪号和跟踪URL (`updateTrackingInfo`)。
*   **通知机制**: 在竞价提交、接受、拒绝和审核等关键节点，异步发送通知给相关用户（需求创建者、货代、管理员），确保信息及时触达。
*   **数据转换**: 负责将请求DTO转换为实体，以及将实体转换为详情响应DTO或VO。
*   **业务异常处理**: 捕获并抛出 `BusinessException`，提供明确的错误信息。

## 3. 接口说明

### 3.1 竞价操作实现

#### createBidding - 创建竞价
*   **方法签名**: `ForwardingBiddingDetailResponse createBidding(ForwardingBiddingCreateRequest request)`
*   **描述**: 实现了货代提交新竞价的逻辑。包括权限校验、需求状态检查、重复提交检查、数据复制、状态设置、文件处理、货代信息填充以及通知发送。
*   **业务逻辑**: 
    1.  验证当前用户是否为货代或管理员。
    2.  检查关联的需求是否存在且状态为 `open`。
    3.  检查该货代是否已对该需求提交过竞价。
    4.  将请求DTO属性复制到 `ForwardingBidding` 实体，设置默认状态（`pending`, `pending_audit`, `winner=false`）。
    5.  从需求中复制认证、熏蒸、保险、报关服务需求。
    6.  处理关联的采购订单ID。
    7.  处理文件列表。
    8.  填充货代名称和公司名称。
    9.  保存竞价实体。
    10. 异步发送竞价提交通知给需求创建者和管理员。
    11. 转换为 `ForwardingBiddingDetailResponse` 返回。

#### updateBidding - 更新竞价信息
*   **方法签名**: `ForwardingBiddingDetailResponse updateBidding(ForwardingBiddingUpdateRequest request)`
*   **描述**: 实现了货代修改其竞价的逻辑。包括权限校验、竞价状态检查（只能修改 `pending` 或 `cancelled` 状态的竞价）、数据更新和文件处理。
*   **业务逻辑**: 
    1.  获取并验证竞价记录是否存在。
    2.  验证当前用户是否为竞价的货代或管理员。
    3.  检查竞价状态是否允许修改。
    4.  根据请求更新竞价的各个字段。
    5.  更新时间戳。
    6.  保存更新。
    7.  转换为 `ForwardingBiddingDetailResponse` 返回。

#### cancelBidding - 取消竞价
*   **方法签名**: `void cancelBidding(Long biddingId)`
*   **描述**: 实现了货代取消竞价的逻辑。包括权限校验和状态检查（只能取消 `pending` 状态的竞价）。
*   **业务逻辑**: 
    1.  获取并验证竞价记录是否存在。
    2.  验证当前用户是否为竞价的货代或管理员。
    3.  检查竞价状态是否允许取消。
    4.  将竞价状态更新为 `cancelled`。
    5.  保存更新。

#### selectWinner - 选择中标货代
*   **方法签名**: `ForwardingBiddingDetailResponse selectWinner(Long biddingId)`
*   **描述**: 实现了选择中标货代的复杂业务逻辑。包括权限校验、需求和竞价状态检查、重置其他竞价的中标状态、设置当前竞价为中标、更新需求状态以及拒绝其他竞价。
*   **业务逻辑**: 
    1.  获取并验证竞价记录和关联需求是否存在。
    2.  验证当前用户是否为需求创建者或管理员。
    3.  检查需求和竞价状态是否允许选择中标。
    4.  调用 `forwardingBiddingMapper.resetWinnerByRequirementId` 将该需求下所有竞价设置为非中标。
    5.  调用 `forwardingBiddingMapper.setAsWinner` 将当前竞价设置为中标。
    6.  更新关联需求的状态为 `in_progress` 或 `completed`。
    7.  拒绝该需求下所有其他待处理的竞价。
    8.  重新获取更新后的竞价记录并返回。

#### acceptBidding - 接受竞价
*   **方法签名**: `ForwardingBiddingDetailResponse acceptBidding(Long biddingId)`
*   **描述**: 实现了买家接受竞价的逻辑。包括权限校验、审核状态检查、重复接受检查、更新竞价状态和发送通知。
*   **业务逻辑**: 
    1.  获取并验证竞价记录和关联需求是否存在。
    2.  检查竞价的审核状态（只有 `approved` 才能被接受）。
    3.  验证当前用户是否为需求创建者、关联订单的买家/卖家或管理员。
    4.  检查竞价状态是否允许接受（只能接受 `pending` 或 `submitted` 状态）。
    5.  检查该需求是否已存在已接受的竞价。
    6.  更新竞价状态为 `accepted`。
    7.  异步发送竞价接受通知给货代和管理员。
    8.  更新关联需求的状态为 `completed`。
    9.  转换为 `ForwardingBiddingDetailResponse` 返回。

#### rejectBidding - 拒绝竞价
*   **方法签名**: `ForwardingBiddingDetailResponse rejectBidding(Long biddingId)`
*   **描述**: 实现了买家拒绝竞价的逻辑。包括权限校验、审核状态检查、更新竞价状态和发送通知。
*   **业务逻辑**: 
    1.  获取并验证竞价记录和关联需求是否存在。
    2.  检查竞价的审核状态（只有 `approved` 才能被拒绝）。
    3.  验证当前用户是否为需求创建者或管理员。
    4.  检查竞价状态是否允许拒绝（只能拒绝 `pending` 或 `submitted` 状态）。
    5.  更新竞价状态为 `rejected`。
    6.  异步发送竞价拒绝通知给货代和管理员。
    7.  转换为 `ForwardingBiddingDetailResponse` 返回。

#### auditBidding - 管理员审核竞价
*   **方法签名**: `ForwardingBiddingDetailResponse auditBidding(Long biddingId, boolean approved, String remark)`
*   **描述**: 实现了管理员审核竞价的逻辑。包括权限校验、状态检查、更新审核状态和发送通知。
*   **业务逻辑**: 
    1.  验证当前用户是否为管理员。
    2.  获取并验证竞价记录是否存在。
    3.  检查竞价的审核状态（只能审核 `pending_audit` 状态）。
    4.  更新审核状态为 `approved` 或 `rejected`，并设置审核备注。
    5.  保存更新。
    6.  异步发送竞价审核通知给货代和需求创建者（如果审核通过）。
    7.  转换为 `ForwardingBiddingDetailResponse` 返回。

#### updateTrackingInfo - 更新物流跟踪信息
*   **方法签名**: `ForwardingBiddingDetailResponse updateTrackingInfo(Long biddingId, String trackingNumber, String trackingUrl)`
*   **描述**: 实现了更新竞价物流跟踪信息的逻辑。包括权限校验和状态检查（只能更新已中标竞价的物流信息）。
*   **业务逻辑**: 
    1.  获取并验证竞价记录是否存在。
    2.  验证当前用户是否为竞价的货代或管理员。
    3.  检查竞价状态是否允许更新物流信息（只能是 `accepted` 状态）。
    4.  更新 `trackingNumber` 和 `trackingUrl` 字段。
    5.  保存更新。
    6.  转换为 `ForwardingBiddingDetailResponse` 返回。

### 3.2 竞价查询实现

#### getBiddingById - 根据ID获取竞价详情
*   **方法签名**: `ForwardingBiddingDetailResponse getBiddingById(Long biddingId)`
*   **描述**: 实现了根据竞价ID获取竞价详情的逻辑，并根据用户角色进行权限校验和数据过滤。
*   **业务逻辑**: 
    1.  获取并验证竞价记录是否存在。
    2.  根据当前用户角色（管理员、货代、买家、卖家）进行权限判断。
    3.  如果无权查看，抛出 `BusinessException`。
    4.  转换为 `ForwardingBiddingDetailResponse` 返回。

#### getBiddingsByOrderId - 根据订单ID获取竞价详情
*   **方法签名**: `List<ForwardingBiddingVO> getBiddingsByOrderId(Long orderId)`
*   **描述**: 实现了根据采购订单ID获取关联货代竞价列表的逻辑。包括订单存在性验证和权限过滤。
*   **业务逻辑**: 
    1.  验证采购订单是否存在。
    2.  通过 `forwardingRequirementService` 获取关联的货代需求ID。
    3.  调用 `forwardingBiddingMapper.findByRequirementId` 查询竞价列表。
    4.  根据当前用户角色（货代、买家、卖家、管理员）过滤竞价列表。
    5.  转换为 `ForwardingBiddingVO` 列表返回。

#### getBiddingsByRequirementId - 获取需求的所有竞价
*   **方法签名**: `List<ForwardingBiddingVO> getBiddingsByRequirementId(Long requirementId)`
*   **描述**: 实现了获取指定需求下所有竞价列表的逻辑，并根据用户角色进行权限过滤。
*   **业务逻辑**: 
    1.  验证需求是否存在。
    2.  验证当前用户是否为需求创建者或管理员。
    3.  调用 `forwardingBiddingMapper.findByRequirementId` 查询竞价列表。
    4.  根据当前用户角色（货代、买家、卖家、管理员）过滤竞价列表。
    5.  转换为 `ForwardingBiddingVO` 列表返回。

#### getBiddingsByForwarderId - 获取货代的所有竞价
*   **方法签名**: `List<ForwardingBiddingVO> getBiddingsByForwarderId(Long forwarderId)`
*   **描述**: 实现了获取指定货代所有竞价列表的逻辑，并进行权限校验。
*   **业务逻辑**: 
    1.  验证 `forwarderId` 参数，如果为空则使用当前用户ID。
    2.  验证当前用户是否为该货代或管理员。
    3.  调用 `forwardingBiddingMapper.findByForwarderId` 查询竞价列表。
    4.  转换为 `ForwardingBiddingVO` 列表返回。

#### getBiddingPage - 分页获取竞价列表
*   **方法签名**: `IPage<ForwardingBiddingVO> getBiddingPage(Integer current, Integer size, Long requirementId, Long forwarderId, String status)`
*   **描述**: 实现了分页查询竞价列表的逻辑，支持按需求ID、货代ID和状态过滤，并根据用户角色进行权限过滤。
*   **业务逻辑**: 
    1.  构建 `LambdaQueryWrapper`，设置逻辑删除条件。
    2.  根据 `requirementId`, `forwarderId`, `status` 添加过滤条件。
    3.  根据当前用户角色（货代、买家、卖家）添加权限过滤条件（例如，货代只能看自己的竞价，买家/卖家只能看审核通过的竞价）。
    4.  按创建时间降序排序。
    5.  执行分页查询。
    6.  转换为 `IPage<ForwardingBiddingVO>` 返回。

#### getAllBiddingsForAdmin - 管理员获取所有货代竞价记录（增强版分页查询）
*   **方法签名**: `IPage<ForwardingBiddingVO> getAllBiddingsForAdmin(Integer current, Integer size, Long requirementId, Long forwarderId, String status, String startDate, String endDate, String orderBy, Boolean ascending, Long purchaseOrderId, Double minPrice, Double maxPrice, String keyword)`
*   **描述**: 实现了管理员专用的高级分页查询逻辑，支持多种筛选、排序和关键词搜索。
*   **业务逻辑**: 
    1.  验证当前用户是否为管理员。
    2.  构建 `LambdaQueryWrapper`，添加各种筛选条件（需求ID、货代ID、状态、采购订单ID、价格范围、日期范围、关键词）。
    3.  根据 `orderBy` 和 `ascending` 设置排序。
    4.  执行分页查询。
    5.  转换为 `IPage<ForwardingBiddingVO>` 返回。

#### getWinnerByRequirementId - 获取需求的中标竞价
*   **方法签名**: `ForwardingBiddingDetailResponse getWinnerByRequirementId(Long requirementId)`
*   **描述**: 实现了获取指定需求下已中标竞价的逻辑。
*   **业务逻辑**: 
    1.  验证需求是否存在。
    2.  调用 `forwardingBiddingMapper.findWinnerByRequirementId` 查询中标竞价。
    3.  如果未找到，抛出 `BusinessException`。
    4.  转换为 `ForwardingBiddingDetailResponse` 返回。

#### getBiddingCount - 获取需求的竞价数量
*   **方法签名**: `int getBiddingCount(Long requirementId)`
*   **描述**: 实现了获取指定需求下竞价总数的逻辑。
*   **业务逻辑**: 调用 `forwardingBiddingMapper.countByRequirementId`。

#### getPendingAuditBiddings - 获取待审核的竞价列表
*   **方法签名**: `IPage<ForwardingBiddingVO> getPendingAuditBiddings(Integer current, Integer size)`
*   **描述**: 实现了获取待审核竞价列表的逻辑，仅管理员可访问。
*   **业务逻辑**: 
    1.  验证当前用户是否为管理员。
    2.  构建查询条件，过滤 `pending_audit` 状态和未删除的竞价。
    3.  执行分页查询。
    4.  转换为 `IPage<ForwardingBiddingVO>` 返回。

#### getVisibleBiddingsByRequirementId - 根据需求ID获取可见的竞价列表
*   **方法签名**: `List<ForwardingBiddingVO> getVisibleBiddingsByRequirementId(Long requirementId)`
*   **描述**: 实现了获取指定需求下对当前用户可见的竞价列表的逻辑，并根据用户角色进行权限过滤。
*   **业务逻辑**: 
    1.  验证需求是否存在。
    2.  获取当前用户信息。
    3.  构建查询条件，根据用户角色（货代、管理员、买家/卖家）添加不同的可见性过滤逻辑。
    4.  执行查询。
    5.  转换为 `List<ForwardingBiddingVO>` 返回。

### 3.3 辅助方法

#### convertToDetailResponse - 辅助方法：将实体转换为详情响应
*   **方法签名**: `private ForwardingBiddingDetailResponse convertToDetailResponse(ForwardingBidding bidding)`
*   **描述**: 将 `ForwardingBidding` 实体转换为 `ForwardingBiddingDetailResponse` DTO，并填充关联的需求标题、订单ID和货代名称等信息。

#### convertToVO - 辅助方法：将实体转换为视图对象
*   **方法签名**: `private ForwardingBiddingVO convertToVO(ForwardingBidding bidding)`
*   **描述**: 将 `ForwardingBidding` 实体转换为 `ForwardingBiddingVO`，并填充关联的需求标题、货代名称和最新的物流状态。

#### getUserName - 辅助方法：获取用户名
*   **方法签名**: `private String getUserName(Long userId)`
*   **描述**: 通过 `userService` 获取用户名称，如果获取失败则返回默认值。

#### getUserCompany - 辅助方法：获取用户公司
*   **方法签名**: `private String getUserCompany(Long userId)`
*   **描述**: 通过 `userService` 获取用户公司名称，如果获取失败则返回空字符串。

#### getLatestShippingStatus - 辅助方法：获取最新的物流状态
*   **方法签名**: `private String getLatestShippingStatus(Long biddingId)`
*   **描述**: 获取指定竞价的最新物流状态。当前为简化实现，实际应调用 `ShippingStatusService`。

#### sendForwardingBiddingSubmitNotifications - 发送货代竞价提交通知
*   **方法签名**: `private void sendForwardingBiddingSubmitNotifications(ForwardingBidding bidding, ForwardingRequirement requirement)`
*   **描述**: 异步发送竞价提交通知给需求创建者和所有管理员。

#### sendForwardingBiddingAcceptedNotification - 发送货代竞价接受通知
*   **方法签名**: `private void sendForwardingBiddingAcceptedNotification(ForwardingBidding bidding)`
*   **描述**: 异步发送竞价接受通知给货代和所有管理员。

#### sendForwardingBiddingRejectedNotification - 发送货代竞价拒绝通知
*   **方法签名**: `private void sendForwardingBiddingRejectedNotification(ForwardingBidding bidding)`
*   **描述**: 异步发送竞价拒绝通知给货代和所有管理员。

#### sendForwardingBiddingAuditNotification - 发送货代竞价审核通知
*   **方法签名**: `private void sendForwardingBiddingAuditNotification(ForwardingBidding bidding, boolean approved)`
*   **描述**: 异步发送竞价审核通知给货代和需求创建者（如果审核通过）。

## 4. 业务规则

*   **权限控制**: 服务层通过 `SecurityContextUtil` 获取当前用户ID和角色，并根据业务逻辑进行细粒度的权限校验。例如，只有货代可以提交竞价，只有需求创建者或管理员可以接受/拒绝竞价。
*   **事务管理**: 所有涉及数据修改的方法都使用 `@Transactional` 注解，确保操作的原子性和数据一致性。
*   **状态机**: 竞价的状态流转（`status` 和 `auditStatus`）遵循严格的业务规则。例如，只有 `pending` 或 `submitted` 状态的竞价才能被接受或拒绝。
*   **通知机制**: 在竞价生命周期的关键节点（提交、接受、拒绝、审核），系统会异步发送通知给相关用户，确保信息及时触达。通知发送是异步的，避免阻塞主业务流程。
*   **数据转换**: 服务层负责将领域实体 `ForwardingBidding` 转换为前端所需的 `ForwardingBiddingDetailResponse` 或 `ForwardingBiddingVO`。这通常涉及从其他服务（如 `ForwardingRequirementService`, `UserService`）获取关联数据。
*   **异常处理**: 服务在遇到业务规则冲突或数据不存在时，会抛出 `BusinessException`，并包含明确的错误码和信息，便于上层处理。
*   **逻辑删除**: 竞价记录支持逻辑删除，查询时会自动过滤已删除的记录。
*   **增强查询**: `getAllBiddingsForAdmin` 方法展示了如何构建复杂的动态查询，包括多条件过滤、日期范围、价格范围和关键词搜索，并支持动态排序。

## 5. 使用示例

```java
// 1. 在 ForwardingBiddingController 中调用 ForwardingBiddingService
@RestController
@RequestMapping("/api/v1/forwarding-biddings")
public class ForwardingBiddingController {
    @Autowired
    private ForwardingBiddingService forwardingBiddingService;

    @PostMapping
    @PreAuthorize("hasAuthority('forwarder')")
    public Result<ForwardingBiddingDetailResponse> createBidding(@Valid @RequestBody ForwardingBiddingCreateRequest request) {
        // SecurityContextUtil.getCurrentUserId() 在这里被调用，获取当前登录货代ID
        ForwardingBiddingDetailResponse response = forwardingBiddingService.createBidding(request);
        return Result.success("竞价创建成功", response);
    }

    @PutMapping("/{biddingId}/accept")
    @PreAuthorize("hasAuthority('buyer')")
    public Result<ForwardingBiddingDetailResponse> acceptBidding(@PathVariable Long biddingId) {
        // SecurityContextUtil.getCurrentUserId() 在这里被调用，获取当前登录买家ID
        ForwardingBiddingDetailResponse response = forwardingBiddingService.acceptBidding(biddingId);
        return Result.success("竞价接受成功", response);
    }

    @GetMapping("/admin/all")
    @PreAuthorize("hasAuthority('admin')")
    public Result<IPage<ForwardingBiddingVO>> getAllBiddingsForAdmin(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        IPage<ForwardingBiddingVO> biddings = forwardingBiddingService.getAllBiddingsForAdmin(
            page, size, null, null, status, startDate, endDate, "createdAt", false, null, null, null, null);
        return Result.success(biddings);
    }
}

// 2. 测试示例
@SpringBootTest
class ForwardingBiddingServiceImplTest {
    @Autowired
    private ForwardingBiddingService forwardingBiddingService;

    @MockBean
    private ForwardingBiddingMapper forwardingBiddingMapper;
    @MockBean
    private ForwardingRequirementService forwardingRequirementService;
    @MockBean
    private UserService userService;
    @MockBean
    private NotificationService notificationService;
    @MockBean
    private UnifiedOrderService unifiedOrderService;
    @MockBean
    private UserMapper userMapper;

    @BeforeEach
    void setUp() {
        // 模拟 SecurityContextUtil 返回一个用户ID和角色
        try (MockedStatic<SecurityContextUtil> mocked = Mockito.mockStatic(SecurityContextUtil.class)) {
            mocked.when(SecurityContextUtil::getCurrentUserId).thenReturn(10L);
            mocked.when(SecurityContextUtil::getCurrentUserRole).thenReturn("forwarder");
            mocked.when(() -> SecurityContextUtil.hasRole("admin")).thenReturn(false);
            mocked.when(() -> SecurityContextUtil.hasRole("forwarder")).thenReturn(true);
            mocked.when(() -> SecurityContextUtil.hasRole("buyer")).thenReturn(false);
            mocked.when(() -> SecurityContextUtil.hasRole("seller")).thenReturn(false);
        }

        // 模拟 NotificationService
        doNothing().when(notificationService).createNotification(any(Notification.class));

        // 模拟 UserMapper
        when(userMapper.selectAllAdmins()).thenReturn(new ArrayList<>());

        // 模拟 UserService
        when(userService.getUsernameById(anyLong())).thenReturn("Test User");
        when(userService.getCompanyById(anyLong())).thenReturn("Test Company");
    }

    @Test
    @Transactional
    void testCreateBidding_Success() {
        ForwardingBiddingCreateRequest request = new ForwardingBiddingCreateRequest();
        request.setForwardingRequirementId(1L);
        request.setPrice(new BigDecimal("100.00"));
        // ... set other fields ...

        ForwardingRequirement mockRequirement = new ForwardingRequirement();
        mockRequirement.setId(1L);
        mockRequirement.setStatus("open");
        mockRequirement.setDeleted("0");
        when(forwardingRequirementService.getById(1L)).thenReturn(mockRequirement);
        when(forwardingBiddingMapper.countByRequirementIdAndForwarderId(anyLong(), anyLong())).thenReturn(0);
        doAnswer(invocation -> {
            ForwardingBidding bidding = invocation.getArgument(0);
            bidding.setId(100L); // 模拟ID生成
            return 1;
        }).when(forwardingBiddingMapper).insert(any(ForwardingBidding.class));
        when(forwardingBiddingMapper.selectById(100L)).thenReturn(new ForwardingBidding()); // 模拟查询

        ForwardingBiddingDetailResponse response = forwardingBiddingService.createBidding(request);

        assertThat(response).isNotNull();
        assertThat(response.getId()).isEqualTo(100L);
        verify(forwardingBiddingMapper, times(1)).insert(any(ForwardingBidding.class));
        verify(notificationService, times(anyInt())).createNotification(any(Notification.class));
    }

    @Test
    @Transactional
    void testAcceptBidding_Success() {
        Long biddingId = 1L;
        Long requirementId = 10L;
        ForwardingBidding mockBidding = new ForwardingBidding();
        mockBidding.setId(biddingId);
        mockBidding.setForwardingRequirementId(requirementId);
        mockBidding.setStatus("pending");
        mockBidding.setAuditStatus("approved");
        mockBidding.setDeleted("0");
        mockBidding.setForwarderId(20L); // 模拟货代ID

        ForwardingRequirement mockRequirement = new ForwardingRequirement();
        mockRequirement.setId(requirementId);
        mockRequirement.setStatus("open");
        mockRequirement.setCreatedBy(10L); // 模拟买家ID
        mockRequirement.setDeleted("0");

        // 模拟SecurityContextUtil为买家
        try (MockedStatic<SecurityContextUtil> mocked = Mockito.mockStatic(SecurityContextUtil.class)) {
            mocked.when(SecurityContextUtil::getCurrentUserId).thenReturn(10L);
            mocked.when(SecurityContextUtil::getCurrentUserRole).thenReturn("buyer");
            mocked.when(() -> SecurityContextUtil.hasRole("admin")).thenReturn(false);
            mocked.when(() -> SecurityContextUtil.hasRole("forwarder")).thenReturn(false);
            mocked.when(() -> SecurityContextUtil.hasRole("buyer")).thenReturn(true);
            mocked.when(() -> SecurityContextUtil.hasRole("seller")).thenReturn(false);
        }

        when(forwardingBiddingMapper.selectById(biddingId)).thenReturn(mockBidding);
        when(forwardingRequirementService.getById(requirementId)).thenReturn(mockRequirement);
        when(forwardingBiddingMapper.existsAcceptedBidding(requirementId)).thenReturn(false);
        when(forwardingBiddingMapper.resetWinnerByRequirementId(requirementId)).thenReturn(1);
        when(forwardingBiddingMapper.setAsWinner(biddingId)).thenReturn(1);
        when(forwardingRequirementService.updateStatus(requirementId, "completed")).thenReturn(true);
        when(forwardingBiddingMapper.selectById(biddingId)).thenReturn(mockBidding); // 再次模拟，因为会被重新获取

        ForwardingBiddingDetailResponse response = forwardingBiddingService.acceptBidding(biddingId);

        assertThat(response).isNotNull();
        assertThat(response.getStatus()).isEqualTo("accepted");
        verify(forwardingBiddingMapper, times(1)).resetWinnerByRequirementId(requirementId);
        verify(forwardingBiddingMapper, times(1)).setAsWinner(biddingId);
        verify(forwardingRequirementService, times(1)).updateStatus(requirementId, "completed");
        verify(notificationService, times(anyInt())).createNotification(any(Notification.class));
    }
}
```

## 6. 注意事项

*   **DDD分层**: `ForwardingBiddingServiceImpl` 明确属于应用服务层，其职责是协调领域对象和仓储，实现业务用例。它不包含核心领域逻辑，而是委托给领域实体和领域服务。
*   **事务管理**: 所有涉及数据修改的方法都使用 `@Transactional` 注解，确保操作的原子性和数据一致性。特别是 `acceptBidding` 和 `selectWinner` 等复杂流程，需要确保所有相关数据库操作在一个事务中完成。
*   **权限校验**: 服务层通过 `SecurityContextUtil` 获取当前用户ID和角色，并根据业务逻辑进行细粒度的权限校验。这是确保系统安全的关键。
*   **业务异常**: 服务在遇到业务规则冲突或数据不存在时，会抛出 `BusinessException`，并包含明确的错误码和信息，便于上层处理。
*   **数据转换**: 服务层负责将请求DTO转换为领域实体，以及将领域实体转换为前端所需的响应DTO或VO。`convertToDetailResponse` 和 `convertToVO` 辅助方法承担了这一职责。
*   **通知机制**: 在竞价生命周期的关键节点（提交、接受、拒绝、审核），系统会异步发送通知给相关用户。`@Async` 注解确保了通知发送不会阻塞主业务流程。
*   **依赖注入**: 服务通过构造函数注入其依赖（`ForwardingBiddingMapper`, `ForwardingRequirementService`, `UserService`, `NotificationService`, `UnifiedOrderService`, `UserMapper`），遵循了依赖倒置原则。
*   **复杂查询**: `getAllBiddingsForAdmin` 方法展示了如何构建复杂的动态查询，包括多条件过滤、日期范围、价格范围和关键词搜索，并支持动态排序。其实现中对排序字段的映射 (`getFieldByName`) 和关键词搜索的二次过滤 (`if (keyword != null && !keyword.isEmpty())`) 值得注意。
*   **日志记录**: 服务中使用了 `log.info` 和 `log.error` 记录关键操作和异常信息，这对于监控和问题排查非常重要。
*   **简化实现**: 某些辅助方法（如 `getLatestShippingStatus`）当前是简化实现，在生产环境中需要替换为真实的业务逻辑。