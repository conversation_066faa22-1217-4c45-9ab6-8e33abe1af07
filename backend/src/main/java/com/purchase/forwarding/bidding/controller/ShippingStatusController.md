# ShippingStatusController.java

## 文件概述 (File Overview)
`ShippingStatusController.java` 是一个 Spring REST 控制器，负责处理所有与物流状态相关的 HTTP 请求。它提供了物流状态的添加、查询（根据 ID、竞价 ID、订单 ID、需求 ID）、获取最新状态、获取异常状态以及标记状态为异常等功能。该控制器通过与 `ShippingStatusService` 交互来处理业务逻辑，并利用 Spring Security 的 `@PreAuthorize` 注解进行严格的权限控制。

## 核心功能 (Core Functionality)
*   **添加物流状态:** 允许管理员或货代为竞价添加新的物流状态记录。
*   **查询物流状态:**
    *   根据状态 ID 获取单个物流状态详情。
    *   根据竞价 ID、订单 ID、需求 ID 获取物流状态列表。
    *   获取竞价的最新物流状态和最新状态名称。
    *   获取竞价的异常状态列表。
*   **标记状态为异常:** 允许管理员或货代将某个物流状态标记为异常，并提供异常原因。
*   **权限控制:** 所有接口都通过 `@PreAuthorize` 注解进行严格的权限验证。

## 接口说明 (Interface Description)

### 构造函数
*   通过 `@RequiredArgsConstructor` 自动注入 `ShippingStatusService`。

### 公共方法 (Public Methods)
*   `ResponseEntity<ShippingStatusVO> addShippingStatus(@RequestBody @Validated ShippingStatusUpdateRequest request)`:
    *   **HTTP 方法:** `POST`
    *   **路径:** `/api/v1/forwarding/shipping-status`
    *   **摘要:** 添加物流状态
    *   **权限:** `hasAnyAuthority('admin', 'forwarder')`
    *   **参数:** `request` (ShippingStatusUpdateRequest, `@RequestBody`, `@Validated`): 包含物流状态详细信息的 DTO。
    *   **返回值:** `ResponseEntity<ShippingStatusVO>` - 添加成功后返回的物流状态信息。
    *   **业务逻辑:** 调用 `shippingStatusService.addShippingStatus()` 添加物流状态。

*   `ResponseEntity<ShippingStatusVO> getShippingStatusById(@PathVariable Long statusId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/shipping-status/{statusId}`
    *   **摘要:** 根据ID获取物流状态
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `statusId` (Long, `@PathVariable`): 物流状态 ID。
    *   **返回值:** `ResponseEntity<ShippingStatusVO>` - 物流状态详情。

*   `ResponseEntity<List<ShippingStatusVO>> getStatusListByBiddingId(@PathVariable Long biddingId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/shipping-status/bidding/{biddingId}`
    *   **摘要:** 根据竞价ID获取物流状态列表
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `biddingId` (Long, `@PathVariable`): 竞价 ID。
    *   **返回值:** `ResponseEntity<List<ShippingStatusVO>>` - 物流状态列表。

*   `ResponseEntity<List<ShippingStatusVO>> getStatusListByOrderId(@PathVariable Long orderId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/shipping-status/order/{orderId}`
    *   **摘要:** 根据订单ID获取物流状态列表
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `orderId` (Long, `@PathVariable`): 订单 ID。
    *   **返回值:** `ResponseEntity<List<ShippingStatusVO>>` - 物流状态列表。

*   `ResponseEntity<List<ShippingStatusVO>> getStatusListByRequirementId(@PathVariable Long requirementId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/shipping-status/requirement/{requirementId}`
    *   **摘要:** 根据需求ID获取物流状态列表
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `requirementId` (Long, `@PathVariable`): 需求 ID。
    *   **返回值:** `ResponseEntity<List<ShippingStatusVO>>` - 物流状态列表。

*   `ResponseEntity<ShippingStatusVO> getLatestStatusByBiddingId(@PathVariable Long biddingId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/shipping-status/bidding/{biddingId}/latest`
    *   **摘要:** 获取竞价的最新物流状态
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `biddingId` (Long, `@PathVariable`): 竞价 ID。
    *   **返回值:** `ResponseEntity<ShippingStatusVO>` - 最新物流状态。

*   `ResponseEntity<List<ShippingStatusVO>> getExceptionStatusByBiddingId(@PathVariable Long biddingId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/shipping-status/bidding/{biddingId}/exception`
    *   **摘要:** 获取竞价的异常状态列表
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `biddingId` (Long, `@PathVariable`): 竞价 ID。
    *   **返回值:** `ResponseEntity<List<ShippingStatusVO>>` - 异常状态列表。

*   `ResponseEntity<ShippingStatusVO> markAsException(@PathVariable Long statusId, @RequestBody Map<String, String> exceptionInfo)`:
    *   **HTTP 方法:** `POST`
    *   **路径:** `/api/v1/forwarding/shipping-status/{statusId}/mark-exception`
    *   **摘要:** 标记状态为异常
    *   **权限:** `hasAnyAuthority('admin', 'forwarder')`
    *   **参数:** `statusId` (Long, `@PathVariable`), `exceptionInfo` (Map<String, String>, `@RequestBody`): 包含 `exceptionReason`。
    *   **返回值:** `ResponseEntity<ShippingStatusVO>` - 更新后的物流状态信息。

*   `ResponseEntity<String> getLatestStatusName(@PathVariable Long biddingId)`:
    *   **HTTP 方法:** `GET`
    *   **路径:** `/api/v1/forwarding/shipping-status/bidding/{biddingId}/latest-name`
    *   **摘要:** 获取最新的状态名称
    *   **权限:** `hasAnyAuthority('admin', 'seller', 'forwarder', 'buyer')`
    *   **参数:** `biddingId` (Long, `@PathVariable`): 竞价 ID。
    *   **返回值:** `ResponseEntity<String>` - 最新状态名称。

## 使用示例 (Usage Examples)
```java
// 前端调用示例 (JavaScript/TypeScript)

// 货代添加物流状态
async function addShippingStatus(statusData) {
    const response = await fetch('/api/v1/forwarding/shipping-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer YOUR_FORWARDER_JWT_TOKEN' },
        body: JSON.stringify(statusData)
    });
    const data = await response.json();
    console.log('添加物流状态结果:', data);
}

// 获取竞价ID为123的物流状态列表
async function getShippingStatusList(biddingId) {
    const response = await fetch(`/api/v1/forwarding/shipping-status/bidding/${biddingId}`);
    const data = await response.json();
    console.log(`竞价 ${biddingId} 的物流状态列表:`, data.data);
}

// 标记状态ID为456的物流状态为异常
async function markStatusAsException(statusId, reason) {
    const response = await fetch(`/api/v1/forwarding/shipping-status/${statusId}/mark-exception`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer YOUR_ADMIN_OR_FORWARDER_JWT_TOKEN' },
        body: JSON.stringify({ exceptionReason: reason })
    });
    const data = await response.json();
    console.log('标记异常结果:', data);
}
```

## 注意事项 (Notes)
*   **权限控制:** 广泛使用了 Spring Security 的 `@PreAuthorize` 注解进行方法级别的权限控制，确保只有具备相应角色的用户才能访问。在实际部署中，需要确保 Spring Security 配置正确，以使这些注解生效。
*   **统一响应:** 所有接口都返回 `ResponseEntity<T>`，其中 `T` 可以是 `ShippingStatusVO` 列表、单个 `ShippingStatusVO` 或 `String`。在实际项目中，通常会使用 `com.purchase.common.response.Result` 或 `ApiResponse` 进行统一包装。
*   **参数验证:** `addShippingStatus` 和 `markAsException` 接口使用了 `@Validated` 和 `@RequestBody` 对 DTO 或 Map 进行验证。
*   **日志记录:** 尽管文件中没有显式使用 `Slf4j` 的 `log` 实例，但通常会在控制器中注入 `Logger` 进行日志记录，以追踪业务流程和问题排查。
*   **业务异常处理:** 对于 `markAsException` 中的 `BusinessException`，控制器直接抛出，通常由全局异常处理器捕获并返回统一错误响应。
*   **关联查询:** 提供了根据竞价 ID、订单 ID、需求 ID 查询物流状态列表的接口，表明物流状态与这些业务实体之间存在关联。