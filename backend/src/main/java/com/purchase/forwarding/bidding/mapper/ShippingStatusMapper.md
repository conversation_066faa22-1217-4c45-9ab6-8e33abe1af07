# ShippingStatusMapper.md

## 1. 文件概述

`ShippingStatusMapper.java` 是货代模块中的一个MyBatis-Plus Mapper接口，位于 `com.purchase.forwarding.bidding.mapper` 包中。它继承自MyBatis-Plus的 `BaseMapper<ShippingStatus>` 接口，并在此基础上定义了多个自定义的查询方法。`ShippingStatus` 实体代表了物流过程中的每一个状态节点。该Mapper接口是物流状态服务层与数据库进行交互的桥梁，负责将业务对象的操作转换为SQL语句，实现物流状态的持久化管理和多维度查询。

## 2. 核心功能

*   **基础CRUD操作**: 继承 `BaseMapper`，自动拥有对 `ShippingStatus` 实体进行插入（`insert`）、根据ID查询（`selectById`）、根据条件查询列表（`selectList`）、更新（`updateById`）和删除（`deleteById`）等基础的增删改查功能。
*   **多业务关联查询**: 提供了 `findByBiddingId`（按竞价ID）、`findByOrderId`（按订单ID）和 `findByRequirementId`（按需求ID）等多种查询方法，支持从不同业务维度获取物流状态记录。
*   **最新状态查询**: 提供了 `findLatestStatusByBiddingId` 方法，用于快速获取某个竞价的最新物流状态。
*   **异常状态查询**: 提供了 `findExceptionStatusByBiddingId` 方法，用于查询某个竞价下的所有异常物流状态。
*   **排序**: 所有查询结果都按 `occur_time` 降序排列，确保返回的是最新的状态在前。

## 3. 接口说明

`ShippingStatusMapper` 在继承 `BaseMapper` 的基础上，定义了以下自定义方法：

### 3.1 查询方法

#### findByBiddingId - 根据竞价ID查询物流状态
*   **方法签名**: `List<ShippingStatus> findByBiddingId(@Param("biddingId") Long biddingId)`
*   **描述**: 查询指定货代竞价下的所有物流状态记录，按发生时间降序排列。
*   **参数**:
    *   `biddingId` (Long): 货代竞价ID。
*   **返回值**: `List<ShippingStatus>` - 匹配的物流状态实体列表。

#### findByOrderId - 根据订单ID查询物流状态
*   **方法签名**: `List<ShippingStatus> findByOrderId(@Param("orderId") Long orderId)`
*   **描述**: 查询指定订单下的所有物流状态记录，按发生时间降序排列。
*   **参数**:
    *   `orderId` (Long): 订单ID。
*   **返回值**: `List<ShippingStatus>` - 匹配的物流状态实体列表。

#### findByRequirementId - 根据需求ID查询物流状态
*   **方法签名**: `List<ShippingStatus> findByRequirementId(@Param("requirementId") Long requirementId)`
*   **描述**: 查询指定货代需求下的所有物流状态记录，按发生时间降序排列。
*   **参数**:
    *   `requirementId` (Long): 货代需求ID。
*   **返回值**: `List<ShippingStatus>` - 匹配的物流状态实体列表。

#### findLatestStatusByBiddingId - 查询最新的物流状态
*   **方法签名**: `ShippingStatus findLatestStatusByBiddingId(@Param("biddingId") Long biddingId)`
*   **描述**: 查询指定货代竞价的最新一条物流状态记录。
*   **参数**:
    *   `biddingId` (Long): 货代竞价ID。
*   **返回值**: `ShippingStatus` - 最新的物流状态实体，如果不存在则返回 `null`。

#### findExceptionStatusByBiddingId - 查询异常的物流状态
*   **方法签名**: `List<ShippingStatus> findExceptionStatusByBiddingId(@Param("biddingId") Long biddingId)`
*   **描述**: 查询指定货代竞价下所有被标记为异常的物流状态记录。
*   **参数**:
    *   `biddingId` (Long): 货代竞价ID。
*   **返回值**: `List<ShippingStatus>` - 异常物流状态实体列表。

## 4. 业务规则

*   **时间顺序**: 所有查询结果都按 `occur_time` 降序排列，这意味着最新的状态会排在列表的最前面。
*   **多关联**: 物流状态可以与竞价、订单、需求等多个业务实体关联，Mapper提供了灵活的查询入口。
*   **异常标记**: `exception_flag = 1` 用于筛选异常状态的记录。

## 5. 使用示例

```java
// 1. 在 ShippingStatusService 实现中查询物流状态
@Service
public class ShippingStatusServiceImpl implements ShippingStatusService {
    @Autowired
    private ShippingStatusMapper shippingStatusMapper;

    public List<ShippingStatusVO> getStatusListByOrderId(Long orderId) {
        List<ShippingStatus> entities = shippingStatusMapper.findByOrderId(orderId);
        return entities.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    public ShippingStatusVO getLatestStatusByBiddingId(Long biddingId) {
        ShippingStatus entity = shippingStatusMapper.findLatestStatusByBiddingId(biddingId);
        return entity != null ? convertToVO(entity) : null;
    }

    public List<ShippingStatusVO> getExceptionStatusByBiddingId(Long biddingId) {
        List<ShippingStatus> entities = shippingStatusMapper.findExceptionStatusByBiddingId(biddingId);
        return entities.stream().map(this::convertToVO).collect(Collectors.toList());
    }
}

// 2. 测试示例
@SpringBootTest
class ShippingStatusMapperTest {
    @Autowired
    private ShippingStatusMapper shippingStatusMapper;

    @Test
    @Transactional
    void testInsertAndFindByBiddingId() {
        ShippingStatus status = new ShippingStatus();
        status.setForwardingBiddingId(1L);
        status.setStatusCode("IN_TRANSIT");
        status.setStatusName("运输中");
        status.setOccurTime(LocalDateTime.now());
        status.setExceptionFlag(false);
        shippingStatusMapper.insert(status);

        assertThat(status.getId()).isNotNull();

        List<ShippingStatus> foundStatuses = shippingStatusMapper.findByBiddingId(1L);
        assertThat(foundStatuses).hasSize(1);
        assertThat(foundStatuses.get(0).getStatusCode()).isEqualTo("IN_TRANSIT");
    }

    @Test
    @Transactional
    void testFindLatestStatusByBiddingId() {
        // 插入多条状态，确保时间顺序
        ShippingStatus status1 = new ShippingStatus();
        status1.setForwardingBiddingId(2L); status1.setStatusCode("PENDING"); status1.setOccurTime(LocalDateTime.now().minusHours(2));
        shippingStatusMapper.insert(status1);

        ShippingStatus status2 = new ShippingStatus();
        status2.setForwardingBiddingId(2L); status2.setStatusCode("IN_TRANSIT"); status2.setOccurTime(LocalDateTime.now().minusHours(1));
        shippingStatusMapper.insert(status2);

        ShippingStatus latestStatus = shippingStatusMapper.findLatestStatusByBiddingId(2L);
        assertThat(latestStatus).isNotNull();
        assertThat(latestStatus.getStatusCode()).isEqualTo("IN_TRANSIT");
    }

    @Test
    @Transactional
    void testFindExceptionStatusByBiddingId() {
        // 插入正常和异常状态
        ShippingStatus status1 = new ShippingStatus();
        status1.setForwardingBiddingId(3L); status1.setStatusCode("IN_TRANSIT"); status1.setOccurTime(LocalDateTime.now()); status1.setExceptionFlag(false);
        shippingStatusMapper.insert(status1);

        ShippingStatus status2 = new ShippingStatus();
        status2.setForwardingBiddingId(3L); status2.setStatusCode("EXCEPTION"); status2.setOccurTime(LocalDateTime.now().plusMinutes(1)); status2.setExceptionFlag(true); status2.setExceptionReason("货物损坏");
        shippingStatusMapper.insert(status2);

        List<ShippingStatus> exceptionStatuses = shippingStatusMapper.findExceptionStatusByBiddingId(3L);
        assertThat(exceptionStatuses).hasSize(1);
        assertThat(exceptionStatuses.get(0).getStatusCode()).isEqualTo("EXCEPTION");
        assertThat(exceptionStatuses.get(0).getExceptionReason()).isEqualTo("货物损坏");
    }
}
```

## 6. 注意事项

*   **MyBatis-Plus集成**: 继承 `BaseMapper` 使得该Mapper自动拥有强大的CRUD能力，减少了重复代码。
*   **注解SQL**: 所有自定义方法都使用了 `@Select` 注解直接编写SQL。这使得SQL逻辑清晰可见，但对于非常复杂的查询，可能导致注解过长，难以维护。此时可以考虑将SQL提取到XML文件中。
*   **参数绑定**: 使用 `@Param` 注解将Java方法参数绑定到SQL中的命名参数，有效防止SQL注入。
*   **性能优化**: 考虑到物流状态数据量可能较大，Mapper中的SQL查询必须高度优化。应为 `forwarding_bidding_id`, `order_id`, `forwarding_requirement_id`, `occur_time`, `exception_flag` 等常用查询字段建立合适的索引。
*   **事务管理**: 在服务层调用Mapper方法时，必须确保在事务中执行，以保证数据操作的原子性和一致性。
*   **时间顺序**: 所有查询结果都按 `occur_time` 降序排列，这对于展示物流时间线非常重要。
*   **多关联**: 物流状态可以与竞价、订单、需求等多个业务实体关联，Mapper提供了灵活的查询入口。
*   **可读性**: 自定义查询方法的命名清晰，准确反映了其查询目的。