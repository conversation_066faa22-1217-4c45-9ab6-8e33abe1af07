package com.purchase.forwarding.bidding.entity;

import lombok.Getter;

/**
 * 物流状态代码枚举
 */
@Getter
public enum ShippingStatusCode {
    PENDING("待处理", "货物等待安排运输"),
    PICKUP_ARRANGED("已安排提货", "货代已安排取货"),
    PICKED_UP("已提货", "货物已被提取"),
    DEPARTURE_WAREHOUSE("已入发货仓库", "货物已到达发货仓库"),
    CUSTOMS_EXPORT("出口清关中", "货物正在办理出口清关"),
    EXPORT_CLEARED("出口清关完成", "出口清关手续已完成"),
    IN_TRANSIT("运输中", "货物正在运输途中"),
    ARRIVED_DESTINATION_COUNTRY("已到达目的国", "货物已到达目的国"),
    CUSTOMS_IMPORT("进口清关中", "货物正在办理进口清关"),
    IMPORT_CLEARED("进口清关完成", "进口清关手续已完成"),
    LOCAL_DELIVERY("本地配送中", "货物正在本地配送"),
    DELIVERED("已送达", "货物已成功送达收货人"),
    EXCEPTION("运输异常", "运输过程中出现异常");
    
    private final String name;
    private final String description;
    
    ShippingStatusCode(String name, String description) {
        this.name = name;
        this.description = description;
    }
} 