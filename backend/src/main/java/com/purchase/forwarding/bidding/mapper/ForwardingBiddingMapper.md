# ForwardingBiddingMapper.md

## 1. 文件概述

`ForwardingBiddingMapper.java` 是货代模块中的一个MyBatis-Plus Mapper接口，位于 `com.purchase.forwarding.bidding.mapper` 包中。它继承自MyBatis-Plus的 `BaseMapper<ForwardingBidding>` 接口，并在此基础上定义了多个自定义的查询和更新方法。`ForwardingBidding` 实体代表了货代对货运需求提交的竞价记录。该Mapper接口是货代竞价服务层与数据库进行交互的桥梁，负责将业务对象的操作转换为SQL语句，实现货代竞价的持久化管理和多维度查询。

## 2. 核心功能

*   **基础CRUD操作**: 继承 `BaseMapper`，自动拥有对 `ForwardingBidding` 实体进行插入（`insert`）、根据ID查询（`selectById`）、根据条件查询列表（`selectList`）、更新（`updateById`）和删除（`deleteById`）等基础的增删改查功能。
*   **多维度查询**: 提供了 `findByRequirementId`（按需求ID）、`findByPurchaseOrderId`（按采购订单ID）、`findByForwarderId`（按货代ID）等多种查询方法，支持从不同业务维度获取竞价记录。
*   **竞价计数**: 提供了 `countByRequirementIdAndForwarderId` 和 `countByRequirementId` 方法，用于统计特定需求下某个货代的竞价数量或总竞价数量。
*   **中标管理**: 提供了 `findWinnerByRequirementId`（查询中标竞价）、`resetWinnerByRequirementId`（重置需求下所有竞价为非中标）和 `setAsWinner`（设置指定竞价为中标）等方法，支持竞价的中标逻辑。
*   **物流信息更新**: 提供了 `updateTrackingInfo` 方法，用于更新竞价的物流跟踪号和跟踪URL。
*   **逻辑删除过滤**: 所有查询都包含了 `AND deleted = '0'` 条件，确保只查询未被逻辑删除的记录。
*   **竞价状态检查**: 提供了 `existsAcceptedBidding` 方法，用于检查某个需求下是否已经存在已接受的竞价。
*   **需求ID列表查询**: 提供了 `findRequirementIdsByForwarderId` 方法，用于查询某个货代参与过竞价的所有需求ID。

## 3. 接口说明

`ForwardingBiddingMapper` 在继承 `BaseMapper` 的基础上，定义了以下自定义方法：

### 3.1 查询方法

#### findByRequirementId - 根据需求ID查询竞价记录
*   **方法签名**: `List<ForwardingBidding> findByRequirementId(@Param("requirementId") Long requirementId)`
*   **描述**: 查询指定货运需求下的所有未逻辑删除的竞价记录。
*   **参数**:
    *   `requirementId` (Long): 货运需求ID。
*   **返回值**: `List<ForwardingBidding>` - 匹配的货代竞价实体列表。

#### findWinnerByRequirementId - 查询需求的中标竞价
*   **方法签名**: `ForwardingBidding findWinnerByRequirementId(@Param("requirementId") Long requirementId)`
*   **描述**: 查询指定货运需求下已中标的竞价记录。通常一个需求只有一个中标竞价。
*   **参数**:
    *   `requirementId` (Long): 货运需求ID。
*   **返回值**: `ForwardingBidding` - 中标的货代竞价实体，如果不存在或已删除则返回 `null`。

#### countByRequirementIdAndForwarderId - 查询特定需求下是否已有该货代的竞价
*   **方法签名**: `int countByRequirementIdAndForwarderId(@Param("requirementId") Long requirementId, @Param("forwarderId") Long forwarderId)`
*   **描述**: 统计指定需求下，某个货代提交的未逻辑删除的竞价数量。通常用于判断货代是否已对某个需求提交过竞价。
*   **参数**:
    *   `requirementId` (Long): 货运需求ID。
    *   `forwarderId` (Long): 货代用户ID。
*   **返回值**: `int` - 匹配的竞价数量。

#### existsAcceptedBidding - 检查需求下是否已存在已接受的竞价
*   **方法签名**: `boolean existsAcceptedBidding(@Param("requirementId") Long requirementId)`
*   **描述**: 检查指定需求下是否已经存在状态为 `accepted` 且未逻辑删除的竞价。
*   **参数**:
    *   `requirementId` (Long): 货运需求ID。
*   **返回值**: `boolean` - 如果存在，返回 `true`；否则返回 `false`。

### 3.2 更新方法

#### resetWinnerByRequirementId - 将需求的所有竞价设置为非中标
*   **方法签名**: `int resetWinnerByRequirementId(@Param("requirementId") Long requirementId)`
*   **描述**: 将指定货运需求下所有未逻辑删除的竞价的 `is_winner` 字段设置为 `0`（非中标）。
*   **参数**:
    *   `requirementId` (Long): 货运需求ID。
*   **返回值**: `int` - 影响的行数。

#### setAsWinner - 将指定竞价设置为中标
*   **方法签名**: `int setAsWinner(@Param("biddingId") Long biddingId)`
*   **描述**: 将指定竞价的 `is_winner` 字段设置为 `1`（中标），并将其 `status` 字段设置为 `accepted`。
*   **参数**:
    *   `biddingId` (Long): 竞价ID。
*   **返回值**: `int` - 影响的行数。

#### updateTrackingInfo - 更新竞价的物流跟踪信息
*   **方法签名**: `int updateTrackingInfo(@Param("biddingId") Long biddingId, @Param("trackingNumber") String trackingNumber, @Param("trackingUrl") String trackingUrl)`
*   **描述**: 更新指定竞价的物流跟踪号和跟踪URL。
*   **参数**:
    *   `biddingId` (Long): 竞价ID。
    *   `trackingNumber` (String): 物流跟踪号。
    *   `trackingUrl` (String): 物流跟踪URL。
*   **返回值**: `int` - 影响的行数。

## 4. 业务规则

*   **逻辑删除**: 所有的查询都默认排除了 `deleted = '1'` 的记录，这意味着系统采用了逻辑删除策略，而不是物理删除。
*   **唯一性**: `forwarding_bidding` 表中通常会有一个唯一索引，确保 `(forwarding_requirement_id, forwarder_id)` 的组合是唯一的，即一个货代对一个需求只能提交一个竞价。
*   **中标逻辑**: `resetWinnerByRequirementId` 和 `setAsWinner` 组合使用，确保一个需求下只有一个中标竞价。
*   **状态管理**: 竞价的状态（`status`）和审核状态（`audit_status`）是其生命周期的关键，不同的状态对应不同的业务处理流程。

## 5. 使用示例

```java
// 1. 在 ForwardingBiddingService 实现中提交竞价
@Service
public class ForwardingBiddingServiceImpl implements ForwardingBiddingService {
    @Autowired
    private ForwardingBiddingMapper biddingMapper;

    @Transactional
    public ForwardingBiddingDetailResponse createBidding(ForwardingBiddingCreateRequest request) {
        // 业务校验：检查货代是否已对该需求提交过竞价
        if (biddingMapper.countByRequirementIdAndForwarderId(request.getRequirementId(), request.getForwarderId()) > 0) {
            throw new BusinessException("您已对该需求提交过竞价");
        }

        ForwardingBidding bidding = new ForwardingBidding();
        // ... 将 request 中的数据复制到 bidding ...
        bidding.setForwardingRequirementId(request.getRequirementId());
        bidding.setForwarderId(request.getForwarderId());
        bidding.setStatus("pending");
        bidding.setAuditStatus("pending_audit");
        bidding.setDeleted("0");

        biddingMapper.insert(bidding);
        return convertToDetailResponse(bidding);
    }

    @Transactional
    public ForwardingBiddingDetailResponse acceptBidding(Long biddingId, Long requirementId) {
        // 确保只有一个中标竞价
        biddingMapper.resetWinnerByRequirementId(requirementId);
        biddingMapper.setAsWinner(biddingId);

        ForwardingBidding acceptedBidding = biddingMapper.selectById(biddingId);
        return convertToDetailResponse(acceptedBidding);
    }
}

// 2. 测试示例
@SpringBootTest
class ForwardingBiddingMapperTest {
    @Autowired
    private ForwardingBiddingMapper biddingMapper;

    @Test
    @Transactional
    void testInsertAndFindByRequirementId() {
        ForwardingBidding bidding = new ForwardingBidding();
        bidding.setForwardingRequirementId(1L);
        bidding.setForwarderId(10L);
        bidding.setStatus("pending");
        bidding.setAuditStatus("pending_audit");
        bidding.setDeleted("0");
        // ... 设置其他必要属性 ...
        biddingMapper.insert(bidding);

        assertThat(bidding.getId()).isNotNull();

        List<ForwardingBidding> foundBiddings = biddingMapper.findByRequirementId(1L);
        assertThat(foundBiddings).hasSize(1);
        assertThat(foundBiddings.get(0).getForwarderId()).isEqualTo(10L);
    }

    @Test
    @Transactional
    void testWinnerLogic() {
        // 插入两个竞价
        ForwardingBidding bidding1 = new ForwardingBidding();
        bidding1.setForwardingRequirementId(2L); bidding1.setForwarderId(20L); bidding1.setStatus("pending"); bidding1.setAuditStatus("approved"); bidding1.setDeleted("0");
        biddingMapper.insert(bidding1);

        ForwardingBidding bidding2 = new ForwardingBidding();
        bidding2.setForwardingRequirementId(2L); bidding2.setForwarderId(21L); bidding2.setStatus("pending"); bidding2.setAuditStatus("approved"); bidding2.setDeleted("0");
        biddingMapper.insert(bidding2);

        // 设置 bidding1 为中标
        biddingMapper.setAsWinner(bidding1.getId());

        ForwardingBidding winner = biddingMapper.findWinnerByRequirementId(2L);
        assertThat(winner).isNotNull();
        assertThat(winner.getId()).isEqualTo(bidding1.getId());
        assertThat(winner.getStatus()).isEqualTo("accepted");

        // 重置所有中标状态
        biddingMapper.resetWinnerByRequirementId(2L);
        winner = biddingMapper.findWinnerByRequirementId(2L);
        assertThat(winner).isNull();
    }
}
```

## 6. 注意事项

*   **MyBatis-Plus集成**: 继承 `BaseMapper` 使得该Mapper自动拥有强大的CRUD能力，减少了重复代码。
*   **注解SQL**: 大量使用了 `@Select` 和 `@Update` 注解直接编写SQL。这使得SQL逻辑清晰可见，但对于非常复杂的查询，可能导致注解过长，难以维护。此时可以考虑将SQL提取到XML文件中。
*   **参数绑定**: 使用 `@Param` 注解将Java方法参数绑定到SQL中的命名参数，有效防止SQL注入。
*   **性能优化**: 考虑到竞价数据量可能较大，Mapper中的SQL查询必须高度优化。应为 `forwarding_requirement_id`, `forwarder_id`, `purchase_order_id`, `is_winner`, `status`, `deleted` 等常用查询字段建立合适的索引。
*   **事务管理**: 在服务层调用Mapper方法时，必须确保在事务中执行，以保证数据操作的原子性和一致性。
*   **逻辑删除**: 所有查询和更新都显式地包含了 `AND deleted = '0'` 条件，这要求在实体中有一个 `deleted` 字段，并且在插入时默认设置为 `0`。
*   **中标逻辑**: `resetWinnerByRequirementId` 和 `setAsWinner` 的组合是实现“一个需求只有一个中标竞价”的关键，需要确保其原子性和正确性。
*   **可读性**: 自定义查询方法的命名清晰，准确反映了其查询目的。