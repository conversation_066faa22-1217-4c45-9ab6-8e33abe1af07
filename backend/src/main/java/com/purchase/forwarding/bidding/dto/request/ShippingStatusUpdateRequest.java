package com.purchase.forwarding.bidding.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 物流状态更新请求
 */
@Data
public class ShippingStatusUpdateRequest {
    
    @NotNull(message = "竞价ID不能为空")
    private Long forwardingBiddingId;
    
    @NotBlank(message = "状态代码不能为空")
    private String statusCode;
    
    private String location;
    
    private String description;
    
    @NotNull(message = "状态发生时间不能为空")
    private LocalDateTime occurTime;
    
    private LocalDateTime estimatedArrival;
    
    private String[] proofImages;
    
    private Boolean exceptionFlag = false;
    
    private String exceptionReason;
} 