package com.purchase.forwarding.bidding.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.purchase.common.exception.BusinessException;
import com.purchase.common.util.SecurityContextUtil;
import com.purchase.forwarding.bidding.dto.request.ShippingStatusUpdateRequest;
import com.purchase.forwarding.bidding.entity.ForwardingBidding;
import com.purchase.forwarding.bidding.entity.ShippingStatus;
import com.purchase.forwarding.bidding.entity.ShippingStatusCode;
import com.purchase.forwarding.bidding.mapper.ShippingStatusMapper;
import com.purchase.forwarding.bidding.service.ForwardingBiddingService;
import com.purchase.forwarding.bidding.service.ShippingStatusService;
import com.purchase.forwarding.bidding.vo.ShippingStatusVO;
import com.purchase.forwarding.requirement.entity.ForwardingRequirement;
import com.purchase.forwarding.requirement.service.ForwardingRequirementService;
import com.purchase.user.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物流状态服务实现类
 */
@Service
@RequiredArgsConstructor
public class ShippingStatusServiceImpl extends ServiceImpl<ShippingStatusMapper, ShippingStatus>
        implements ShippingStatusService {
    
    private final ShippingStatusMapper shippingStatusMapper;
    private final ForwardingBiddingService forwardingBiddingService;
    private final ForwardingRequirementService forwardingRequirementService;
    private final UserService userService;
    
    @Override
    @Transactional
    public ShippingStatusVO addShippingStatus(ShippingStatusUpdateRequest request) {
        // 验证当前用户角色
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        if (!"forwarder".equals(role) && !"admin".equals(role)) {
            throw new BusinessException(403, "只有货代角色可以更新物流状态");
        }
        
        // 验证竞价是否存在
        ForwardingBidding bidding = forwardingBiddingService.getById(request.getForwardingBiddingId());
        if (bidding == null || "1".equals(bidding.getDeleted())) {
            throw new BusinessException(404, "货代竞价不存在");
        }
        
        // 验证权限
        if (!"admin".equals(role) && !bidding.getForwarderId().equals(currentUserId)) {
            throw new BusinessException(403, "只有中标货代可以更新此物流状态");
        }
        
        // 验证状态
        if (!"accepted".equals(bidding.getStatus())) {
            throw new BusinessException(400, "只有已中标的竞价才能更新物流状态");
        }
        
        // 获取需求信息
        ForwardingRequirement requirement = forwardingRequirementService.getById(bidding.getForwardingRequirementId());

        if (requirement == null) {
            throw new BusinessException(404, "关联的货代需求不存在");
        }
        
        // 验证状态代码
        try {
            ShippingStatusCode.valueOf(request.getStatusCode());
        } catch (IllegalArgumentException e) {
            throw new BusinessException(400, "无效的状态代码");
        }
        
        // 创建状态记录
        ShippingStatus status = new ShippingStatus();
        BeanUtils.copyProperties(request, status);
        
        // 设置冗余字段和其他信息
        status.setOrderId(requirement.getOrderId());
        status.setForwardingRequirementId(requirement.getId());
        status.setStatusName(ShippingStatusCode.valueOf(request.getStatusCode()).getName());
        status.setOperatorId(currentUserId);
        
        // 处理图片
        if (request.getProofImages() != null && request.getProofImages().length > 0) {
            status.setProofImages(String.join(",", request.getProofImages()));
        }
        
        // 保存状态
        save(status);
        
        // 判断是否需要更新需求状态
        if (ShippingStatusCode.DELIVERED.name().equals(request.getStatusCode())) {
            // 如果是已送达状态，更新需求状态为已完成
            forwardingRequirementService.updateStatus(requirement.getId(), "completed");
        }
        
        // 返回VO
        return convertToVO(status);
    }
    
    @Override
    public ShippingStatusVO getShippingStatusById(Long statusId) {
        ShippingStatus status = getById(statusId);
        if (status == null) {
            throw new BusinessException(404, "物流状态记录不存在");
        }
        
        // 验证权限
        checkViewPermission(status);
        
        return convertToVO(status);
    }
    
    @Override
    public List<ShippingStatusVO> getStatusListByBiddingId(Long biddingId) {
        // 验证竞价是否存在
        ForwardingBidding bidding = forwardingBiddingService.getById(biddingId);
        if (bidding == null || "1".equals(bidding.getDeleted())) {
            throw new BusinessException(404, "货代竞价不存在");
        }
        
        // 验证权限
        checkBiddingViewPermission(bidding);
        
        // 查询状态列表
        List<ShippingStatus> statusList = shippingStatusMapper.findByBiddingId(biddingId);
        
        // 转换为VO
        return statusList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ShippingStatusVO> getStatusListByOrderId(Long orderId) {
        // 验证订单关联的需求中是否有中标货代
        List<ShippingStatus> statusList = shippingStatusMapper.findByOrderId(orderId);
        if (statusList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 验证权限
        checkOrderViewPermission(orderId);
        
        // 转换为VO
        return statusList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ShippingStatusVO> getStatusListByRequirementId(Long requirementId) {
        // 验证需求是否存在
        ForwardingRequirement requirement = forwardingRequirementService.getById(requirementId);
        if (requirement == null || "1".equals(requirement.getDeleted())) {
            throw new BusinessException(404, "货代需求不存在");
        }
        
        // 验证权限
        checkRequirementViewPermission(requirement);
        
        // 查询状态列表
        List<ShippingStatus> statusList = shippingStatusMapper.findByRequirementId(requirementId);
        
        // 转换为VO
        return statusList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
    
    @Override
    public ShippingStatusVO getLatestStatusByBiddingId(Long biddingId) {
        // 验证竞价是否存在
        ForwardingBidding bidding = forwardingBiddingService.getById(biddingId);
        if (bidding == null || "1".equals(bidding.getDeleted())) {
            throw new BusinessException(404, "货代竞价不存在");
        }
        
        // 验证权限
        checkBiddingViewPermission(bidding);
        
        // 查询最新状态
        ShippingStatus status = shippingStatusMapper.findLatestStatusByBiddingId(biddingId);
        if (status == null) {
            throw new BusinessException(404, "尚未有物流状态记录");
        }
        
        return convertToVO(status);
    }
    
    @Override
    public List<ShippingStatusVO> getExceptionStatusByBiddingId(Long biddingId) {
        // 验证竞价是否存在
        ForwardingBidding bidding = forwardingBiddingService.getById(biddingId);
        if (bidding == null || "1".equals(bidding.getDeleted())) {
            throw new BusinessException(404, "货代竞价不存在");
        }
        
        // 验证权限
        checkBiddingViewPermission(bidding);
        
        // 查询异常状态
        List<ShippingStatus> exceptionList = shippingStatusMapper.findExceptionStatusByBiddingId(biddingId);
        
        // 转换为VO
        return exceptionList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public ShippingStatusVO markAsException(Long statusId, String exceptionReason) {
        // 获取状态记录
        ShippingStatus status = getById(statusId);
        if (status == null) {
            throw new BusinessException(404, "物流状态记录不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        
        ForwardingBidding bidding = forwardingBiddingService.getById(status.getForwardingBiddingId());
        if (bidding == null) {
            throw new BusinessException(404, "关联的货代竞价不存在");
        }
        
        if (!"admin".equals(role) && !bidding.getForwarderId().equals(currentUserId)) {
            throw new BusinessException(403, "只有中标货代可以标记物流状态异常");
        }
        
        // 更新为异常状态
        status.setExceptionFlag(true);
        status.setExceptionReason(exceptionReason);
        status.setUpdatedAt(LocalDateTime.now());
        
        // 保存更新
        updateById(status);
        
        return convertToVO(status);
    }
    
    @Override
    public String getLatestStatusName(Long biddingId) {
        try {
            ShippingStatus status = shippingStatusMapper.findLatestStatusByBiddingId(biddingId);
            return status != null ? status.getStatusName() : "尚未更新物流";
        } catch (Exception e) {
            return "尚未更新物流";
        }
    }
    
    // 辅助方法：检查查看物流状态的权限
    private void checkViewPermission(ShippingStatus status) {
        String role = SecurityContextUtil.getCurrentUserRole();
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        
        // 管理员有全部权限
        if ("admin".equals(role)) {
            return;
        }
        
        // 获取相关的竞价和需求
        ForwardingBidding bidding = forwardingBiddingService.getById(status.getForwardingBiddingId());
        if (bidding == null) {
            throw new BusinessException(404, "关联的货代竞价不存在");
        }
        
        ForwardingRequirement requirement = forwardingRequirementService.getById(status.getForwardingRequirementId());
        if (requirement == null) {
            throw new BusinessException(404, "关联的货代需求不存在");
        }
        
        // 货代只能查看自己中标的物流
        if ("forwarder".equals(role) && !bidding.getForwarderId().equals(currentUserId)) {
            throw new BusinessException(403, "无权查看此物流状态");
        }
        
        // 卖家只能查看自己需求的物流
        if ("seller".equals(role) && !requirement.getCreatedBy().equals(currentUserId)) {
            throw new BusinessException(403, "无权查看此物流状态");
        }
        
        // 买家只能查看自己订单相关的物流
        // 这里简化处理，实际应调用orderService验证
        if ("buyer".equals(role)) {
            // TODO: 验证买家是否有权限查看此订单的物流
        }
    }
    
    // 辅助方法：检查查看竞价物流状态的权限
    private void checkBiddingViewPermission(ForwardingBidding bidding) {
        String role = SecurityContextUtil.getCurrentUserRole();
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        
        // 管理员有全部权限
        if ("admin".equals(role)) {
            return;
        }
        
        // 货代只能查看自己中标的物流
        if ("forwarder".equals(role) && !bidding.getForwarderId().equals(currentUserId)) {
            throw new BusinessException(403, "无权查看此竞价的物流状态");
        }
        
        // 卖家和买家需要检查与需求的关联
        ForwardingRequirement requirement = forwardingRequirementService.getById(bidding.getForwardingRequirementId());
        if (requirement == null) {
            throw new BusinessException(404, "关联的货代需求不存在");
        }
        
        // 卖家只能查看自己需求的物流
        if ("seller".equals(role) && !requirement.getCreatedBy().equals(currentUserId)) {
            throw new BusinessException(403, "无权查看此物流状态");
        }
        
        // 买家只能查看自己订单相关的物流
        // 这里简化处理，实际应调用orderService验证
        if ("buyer".equals(role)) {
            // TODO: 验证买家是否有权限查看此订单的物流
        }
    }
    
    // 辅助方法：检查查看订单物流状态的权限
    private void checkOrderViewPermission(Long orderId) {
        // 实际项目中，应调用orderService验证用户是否有权限查看此订单
        // 这里简化处理
    }
    
    // 辅助方法：检查查看需求物流状态的权限
    private void checkRequirementViewPermission(ForwardingRequirement requirement) {
        String role = SecurityContextUtil.getCurrentUserRole();
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        
        // 管理员有全部权限
        if ("admin".equals(role)) {
            return;
        }
        
        // 货代有查看权限（简化处理，实际应检查是否是中标货代）
        if ("forwarder".equals(role)) {
            return;
        }
        
        // 卖家只能查看自己需求的物流
        if ("seller".equals(role) && !requirement.getCreatedBy().equals(currentUserId)) {
            throw new BusinessException(403, "无权查看此需求的物流状态");
        }
        
        // 买家只能查看自己订单相关的物流
        // 这里简化处理，实际应调用orderService验证
        if ("buyer".equals(role)) {
            // TODO: 验证买家是否有权限查看此订单的物流
        }
    }
    
    // 辅助方法：将实体转换为VO
    private ShippingStatusVO convertToVO(ShippingStatus status) {
        ShippingStatusVO vo = new ShippingStatusVO();
        BeanUtils.copyProperties(status, vo);
        
        // 处理图片列表
        if (status.getProofImages() != null && !status.getProofImages().isEmpty()) {
            vo.setProofImages(Arrays.asList(status.getProofImages().split(",")));
        } else {
            vo.setProofImages(new ArrayList<>());
        }
        
        // 获取操作人名称
        if (status.getOperatorId() != null) {
            vo.setOperatorName(getUserName(status.getOperatorId()));
        }
        
        return vo;
    }
    
    // 辅助方法：获取用户名
    private String getUserName(Long userId) {
        try {
            return userService.getUsernameById(userId);
        } catch (Exception e) {
            return "User-" + userId;
        }
    }
} 