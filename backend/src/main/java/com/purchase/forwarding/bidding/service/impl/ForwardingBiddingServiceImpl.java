package com.purchase.forwarding.bidding.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.purchase.common.exception.BusinessException;
import com.purchase.common.util.SecurityContextUtil;
import com.purchase.forwarding.bidding.dto.request.ForwardingBiddingCreateRequest;
import com.purchase.forwarding.bidding.dto.request.ForwardingBiddingUpdateRequest;
import com.purchase.forwarding.bidding.dto.response.ForwardingBiddingDetailResponse;
import com.purchase.forwarding.bidding.entity.ForwardingBidding;
import com.purchase.forwarding.bidding.mapper.ForwardingBiddingMapper;
import com.purchase.forwarding.bidding.service.ForwardingBiddingService;
import com.purchase.forwarding.bidding.vo.ForwardingBiddingVO;
import com.purchase.forwarding.requirement.entity.ForwardingRequirement;
import com.purchase.forwarding.requirement.service.ForwardingRequirementService;

import com.purchase.user.service.UserService;
import com.purchase.order.service.UnifiedOrderService;
import com.purchase.message.notification.service.NotificationService;
import com.purchase.message.notification.enums.NotificationType;
import com.purchase.message.notification.entity.Notification;
import com.purchase.user.entity.User;
import com.purchase.user.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 货代竞价服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ForwardingBiddingServiceImpl extends ServiceImpl<ForwardingBiddingMapper, ForwardingBidding>
        implements ForwardingBiddingService {

    // 注入Unified_OrderServiceImpl
    private final UnifiedOrderService unifiedOrderService;
    
    private final ForwardingBiddingMapper forwardingBiddingMapper;
    private final ForwardingRequirementService forwardingRequirementService;
    private final UserService userService;
    private final NotificationService notificationService;
    private final UserMapper userMapper;

  
    @Override
    @Transactional
    public ForwardingBiddingDetailResponse createBidding(ForwardingBiddingCreateRequest request) {
        // 验证当前用户角色是否为货代
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        if (!"forwarder".equals(role) && !"admin".equals(role)) {
            throw new BusinessException(403, "只有货代角色可以提交竞价");
        }
        
        // 检查需求是否存在
        ForwardingRequirement requirement = forwardingRequirementService.getById(request.getForwardingRequirementId());
        if (requirement == null || "1".equals(requirement.getDeleted())) {
            throw new BusinessException(404, "货代需求不存在");
        }
        
        // 检查需求状态是否为open
        if (!"open".equals(requirement.getStatus())) {
            throw new BusinessException(400, "只能对开放状态的需求提交竞价");
        }
        
        // 检查是否已经提交过竞价
        int existingCount = forwardingBiddingMapper.countByRequirementIdAndForwarderId(
                request.getForwardingRequirementId(), currentUserId);
        if (existingCount > 0) {
            throw new BusinessException(400, "您已经对该需求提交过竞价，请勿重复提交");
        }
        
        // 创建竞价记录
        ForwardingBidding bidding = new ForwardingBidding();
        BeanUtils.copyProperties(request, bidding);
        
        // 设置货代ID和状态
        bidding.setForwarderId(currentUserId);
        bidding.setStatus("pending");
        bidding.setWinner(false);
        
        // 设置审核状态为待审核
        bidding.setAuditStatus("pending_audit");
        
        // 从货代需求中复制认证和熏蒸需求
        bidding.setNeedCertification(requirement.getNeedCertification());
        bidding.setNeedFumigation(requirement.getNeedFumigation());
        
        // 从货代需求中复制保险和报关服务需求
        bidding.setInsuranceIncluded(requirement.getInsuranceIncluded() != null && requirement.getInsuranceIncluded() == 1);
        bidding.setCustomsService(requirement.getCustomsService() != null && requirement.getCustomsService() == 1);
        
        // 如果需求关联了采购订单，则设置purchaseOrderId
        if (requirement.getOrderId() != null) {
            bidding.setPurchaseOrderId(requirement.getOrderId());
            log.info("竞价关联了采购订单ID: {}", requirement.getOrderId());
        } else if (request.getPurchaseOrderId() != null) {
            // 如果请求中直接包含采购订单ID，也可以设置
            bidding.setPurchaseOrderId(request.getPurchaseOrderId());
            log.info("从请求中获取到采购订单ID: {}", request.getPurchaseOrderId());
        }
        
        // 处理文件
        if (request.getDocuments() != null && request.getDocuments().length > 0) {
            bidding.setDocuments(String.join(",", request.getDocuments()));
        }
        
        // 设置货代名称和公司名称
        bidding.setForwarderName(getUserName(currentUserId));
        // 如果请求中未提供公司名称，则从用户配置文件中获取
        if (bidding.getForwarderCompanyName() == null || bidding.getForwarderCompanyName().trim().isEmpty()) {
            bidding.setForwarderCompanyName(getUserCompany(currentUserId));
        }
        
        // 保存到数据库
        save(bidding);
        
        // 发送货代竞价提交通知
        sendForwardingBiddingSubmitNotifications(bidding, requirement);
        
        // 返回详情
        return convertToDetailResponse(bidding);
    }
    
    @Override
    @Transactional
    public ForwardingBiddingDetailResponse updateBidding(ForwardingBiddingUpdateRequest request) {
        // 获取竞价记录
        ForwardingBidding bidding = getById(request.getId());
        if (bidding == null || "1".equals(bidding.getDeleted())) {
            throw new BusinessException(404, "竞价记录不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        if (!"admin".equals(role) && !bidding.getForwarderId().equals(currentUserId)) {
            throw new BusinessException(403, "无权修改此竞价");
        }
        
        // 允许编辑pending和cancelled状态的竞价
        String biddingStatus = bidding.getStatus();
        if (!"pending".equals(biddingStatus) && !"cancelled".equals(biddingStatus)) {
            throw new BusinessException(400, "只能修改待处理或已撤回状态的竞价");
        }
        
        // 如果是已撤回状态，编辑后改为待处理状态
        if ("cancelled".equals(bidding.getStatus())) {
            bidding.setStatus("pending");
        }
        
        // 更新竞价信息
        if (request.getBiddingPrice() != null) bidding.setBiddingPrice(request.getBiddingPrice());
        if (request.getCurrency() != null) bidding.setCurrency(request.getCurrency());
        if (request.getShippingMethod() != null) bidding.setShippingMethod(request.getShippingMethod());
        if (request.getShippingRoute() != null) bidding.setShippingRoute(request.getShippingRoute());
        if (request.getEstimatedDays() != null) bidding.setEstimatedDays(request.getEstimatedDays());
        if (request.getDescription() != null) bidding.setDescription(request.getDescription());
        if (request.getInsuranceIncluded() != null) bidding.setInsuranceIncluded(request.getInsuranceIncluded());
        if (request.getInsuranceAmount() != null) bidding.setInsuranceAmount(request.getInsuranceAmount());
        if (request.getCustomsService() != null) bidding.setCustomsService(request.getCustomsService());
        if (request.getNeedCertification() != null) bidding.setNeedCertification(request.getNeedCertification());
        if (request.getNeedFumigation() != null) bidding.setNeedFumigation(request.getNeedFumigation());
        
        // 更新新增的费用字段
        if (request.getCostExportPacking() != null) bidding.setCostExportPacking(request.getCostExportPacking());
        if (request.getCostOriginInlandHaulage() != null) bidding.setCostOriginInlandHaulage(request.getCostOriginInlandHaulage());
        if (request.getCostExportCustomsDocs() != null) bidding.setCostExportCustomsDocs(request.getCostExportCustomsDocs());
        if (request.getCostOriginHandling() != null) bidding.setCostOriginHandling(request.getCostOriginHandling());
        if (request.getCostMainFreight() != null) bidding.setCostMainFreight(request.getCostMainFreight());
        if (request.getCostFreightSurcharges() != null) bidding.setCostFreightSurcharges(request.getCostFreightSurcharges());
        if (request.getCostCargoInsurance() != null) bidding.setCostCargoInsurance(request.getCostCargoInsurance());
        if (request.getCostDestinationHandlingBySeller() != null) bidding.setCostDestinationHandlingBySeller(request.getCostDestinationHandlingBySeller());
        if (request.getCostDestinationUnloadingBySeller() != null) bidding.setCostDestinationUnloadingBySeller(request.getCostDestinationUnloadingBySeller());
        if (request.getCostDestinationInlandHaulageBySeller() != null) bidding.setCostDestinationInlandHaulageBySeller(request.getCostDestinationInlandHaulageBySeller());
        if (request.getCostImportCustomsDocsBySeller() != null) bidding.setCostImportCustomsDocsBySeller(request.getCostImportCustomsDocsBySeller());
        if (request.getCostImportDutiesTaxesBySeller() != null) bidding.setCostImportDutiesTaxesBySeller(request.getCostImportDutiesTaxesBySeller());
        if (request.getCostForwarderServiceFee() != null) bidding.setCostForwarderServiceFee(request.getCostForwarderServiceFee());
        if (request.getDeliveryTerms() != null) bidding.setDeliveryTerms(request.getDeliveryTerms());
        
        // 更新时间相关字段
        if (request.getEstimatedPickupDate() != null) bidding.setEstimatedPickupDate(request.getEstimatedPickupDate());
        if (request.getEstimatedDeliveryDate() != null) bidding.setEstimatedDeliveryDate(request.getEstimatedDeliveryDate());
        
        // 处理文件
        if (request.getDocuments() != null && request.getDocuments().length > 0) {
            bidding.setDocuments(String.join(",", request.getDocuments()));
        }
        
        // 更新时间
        bidding.setUpdatedAt(LocalDateTime.now());
        
        // 保存更新
        updateById(bidding);
        
        // 返回更新后的详情
        return convertToDetailResponse(bidding);
    }
    
    @Override
    @Transactional
    public void cancelBidding(Long biddingId) {
        // 获取竞价记录
        ForwardingBidding bidding = getById(biddingId);
        if (bidding == null || "1".equals(bidding.getDeleted())) {
            throw new BusinessException(404, "竞价记录不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        if (!"admin".equals(role) && !bidding.getForwarderId().equals(currentUserId)) {
            throw new BusinessException(403, "无权取消此竞价");
        }
        
        // 状态检查
        if (!"pending".equals(bidding.getStatus())) {
            throw new BusinessException(400, "只能取消待处理状态的竞价");
        }
        
        // 更新状态
        bidding.setStatus("cancelled");
        bidding.setUpdatedAt(LocalDateTime.now());
        
        // 保存更新
        updateById(bidding);
    }
    
    @Override
    public ForwardingBiddingDetailResponse getBiddingById(Long biddingId) {
        ForwardingBidding bidding = getById(biddingId);
        if (bidding == null || "1".equals(bidding.getDeleted())) {
            throw new BusinessException(404, "竞价记录不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        
        // 管理员有全部权限
        if ("admin".equals(role)) {
            return convertToDetailResponse(bidding);
        }
        
        // 货代只能查看自己的竞价
        if ("forwarder".equals(role) && !bidding.getForwarderId().equals(currentUserId)) {
            throw new BusinessException(403, "无权查看此竞价");
        }
        
        // 买家和卖家需要验证与订单的关联
        ForwardingRequirement requirement = forwardingRequirementService.getById(bidding.getForwardingRequirementId());
        if (requirement == null) {
            throw new BusinessException(404, "关联的货代需求不存在");
        }
        
        // 卖家只能查看自己需求的竞价
        if ("seller".equals(role) && !requirement.getCreatedBy().equals(currentUserId)) {
            throw new BusinessException(403, "无权查看此竞价");
        }
        
        return convertToDetailResponse(bidding);
    }
    
    @Override
    public List<ForwardingBiddingVO> getBiddingsByOrderId(Long orderId) {
        // 验证订单是否存在
        if (unifiedOrderService.existsById(orderId)) {
            // 先查询订单的货代需求ID
            Long requirementId = forwardingRequirementService.getRequirementIdByOrderId(orderId);

            if (requirementId != null) {
                // 查询该需求下的所有竞价
                List<ForwardingBidding> biddings = forwardingBiddingMapper.findByRequirementId(requirementId);
                return biddings.stream()
                      .map(this::convertToVO)
                      .collect(Collectors.toList()); 
            } else {
                throw new BusinessException(404, "未找到关联的货代需求");
            }
        } else {
            throw new BusinessException(404, "订单不存在");
        }
    }
    @Override
    public List<ForwardingBiddingVO> getBiddingsByRequirementId(Long requirementId) {
        // 验证需求是否存在
        ForwardingRequirement requirement = forwardingRequirementService.getById(requirementId);
        if (requirement == null || "1".equals(requirement.getDeleted())) {
            throw new BusinessException(404, "货代需求不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        
        // 卖家只能查看自己需求的竞价
        if ("seller".equals(role) && !requirement.getCreatedBy().equals(currentUserId)) {
            throw new BusinessException(403, "无权查看此需求的竞价");
        }
        
        // 查询竞价列表
        List<ForwardingBidding> biddings = forwardingBiddingMapper.findByRequirementId(requirementId);
        
        // 货代只能看到自己的竞价和审核通过的公开信息
        if ("forwarder".equals(role)) {
            return biddings.stream()
                    .filter(b -> b.getForwarderId().equals(currentUserId) 
                            || (Boolean.TRUE.equals(b.getWinner()) && "approved".equals(b.getAuditStatus())))
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
        }
        
        // 买家和卖家只能看到审核通过的竞价
        if ("buyer".equals(role) || "seller".equals(role)) {
            return biddings.stream()
                    .filter(b -> "approved".equals(b.getAuditStatus()))
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
        }
        
        // 管理员可以看到所有竞价
        return biddings.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ForwardingBiddingVO> getBiddingsByForwarderId(Long forwarderId) {
        // 验证参数
        if (forwarderId == null) {
            forwarderId = SecurityContextUtil.getCurrentUserId();
        }
        
        // 当前用户角色
        String role = SecurityContextUtil.getCurrentUserRole();
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        
        // 货代只能查看自己的竞价
        if ("forwarder".equals(role) && !currentUserId.equals(forwarderId)) {
            return new ArrayList<>();
        }
        
        // 查询竞价
        List<ForwardingBidding> biddings = forwardingBiddingMapper.findByForwarderId(forwarderId);
        
        // 转换结果
        return biddings.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public ForwardingBiddingDetailResponse selectWinner(Long biddingId) {
        // 获取竞价记录
        ForwardingBidding bidding = getById(biddingId);
        if (bidding == null || "1".equals(bidding.getDeleted())) {
            throw new BusinessException(404, "竞价记录不存在");
        }
        
        // 获取需求
        ForwardingRequirement requirement = forwardingRequirementService.getById(bidding.getForwardingRequirementId());
        if (requirement == null) {
            throw new BusinessException(404, "关联的货代需求不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        if (!"admin".equals(role) && !requirement.getCreatedBy().equals(currentUserId)) {
            throw new BusinessException(403, "只有需求创建者可以选择中标货代");
        }
        
        // 状态检查
        if (!"open".equals(requirement.getStatus()) && !"in_progress".equals(requirement.getStatus())) {
            throw new BusinessException(400, "只能在开放或进行中状态选择中标货代");
        }
        
        if (!"pending".equals(bidding.getStatus())) {
            throw new BusinessException(400, "只能选择待处理状态的竞价");
        }
        
        // 重置该需求下所有竞价的中标状态
        forwardingBiddingMapper.resetWinnerByRequirementId(bidding.getForwardingRequirementId());
        
        // 设置当前竞价为中标
        forwardingBiddingMapper.setAsWinner(biddingId);
        
        // 更新需求状态为进行中
        if ("open".equals(requirement.getStatus())) {
            forwardingRequirementService.updateStatus(requirement.getId(), "in_progress");
        }
        
        // 拒绝其他竞价
        LambdaQueryWrapper<ForwardingBidding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForwardingBidding::getForwardingRequirementId, bidding.getForwardingRequirementId())
                .eq(ForwardingBidding::getStatus, "pending")
                .ne(ForwardingBidding::getId, biddingId);
        
        List<ForwardingBidding> otherBiddings = list(queryWrapper);
        for (ForwardingBidding other : otherBiddings) {
            other.setStatus("rejected");
            other.setUpdatedAt(LocalDateTime.now());
        }
        
        if (!otherBiddings.isEmpty()) {
            updateBatchById(otherBiddings);
        }
        
        // 重新获取更新后的竞价记录
        bidding = getById(biddingId);
        
        // 返回中标详情
        return convertToDetailResponse(bidding);
    }
    
    @Override
    public IPage<ForwardingBiddingVO> getBiddingPage(Integer current, Integer size, Long requirementId, Long forwarderId, String status) {
        // 构建查询条件
        LambdaQueryWrapper<ForwardingBidding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForwardingBidding::getDeleted, "0");
        
        // 根据需求ID过滤
        if (requirementId != null) {
            queryWrapper.eq(ForwardingBidding::getForwardingRequirementId, requirementId);
        }
        
        // 根据角色过滤
        String role = SecurityContextUtil.getCurrentUserRole();
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        
        if ("forwarder".equals(role)) {
            // 货代只能查看自己的竞价
            queryWrapper.eq(ForwardingBidding::getForwarderId, currentUserId);
        } else if (forwarderId != null) {
            queryWrapper.eq(ForwardingBidding::getForwarderId, forwarderId);
        }
        
        // 状态过滤
        if (status != null && !status.isEmpty()) {
            queryWrapper.eq(ForwardingBidding::getStatus, status);
        }
        
        // 审核状态过滤 - 买家和卖家只能看到审核通过的竞价
        if ("buyer".equals(role) || "seller".equals(role)) {
            queryWrapper.eq(ForwardingBidding::getAuditStatus, "approved");
        }
        
        // 排序
        queryWrapper.orderByDesc(ForwardingBidding::getCreatedAt);
        
        // 分页查询
        Page<ForwardingBidding> page = new Page<>(current, size);
        IPage<ForwardingBidding> resultPage = page(page, queryWrapper);
        
        // 转换结果
        return resultPage.convert(this::convertToVO);
    }
    
    @Override
    @Transactional
    public ForwardingBiddingDetailResponse updateTrackingInfo(Long biddingId, String trackingNumber, String trackingUrl) {
        // 获取竞价记录
        ForwardingBidding bidding = getById(biddingId);
        if (bidding == null || "1".equals(bidding.getDeleted())) {
            throw new BusinessException(404, "竞价记录不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        if (!"admin".equals(role) && !bidding.getForwarderId().equals(currentUserId)) {
            throw new BusinessException(403, "无权更新此竞价的物流信息");
        }
        
        // 状态检查
        if (!"accepted".equals(bidding.getStatus())) {
            throw new BusinessException(400, "只能更新已中标的竞价物流信息");
        }
        
        // 更新物流信息
        bidding.setTrackingNumber(trackingNumber);
        bidding.setTrackingUrl(trackingUrl);
        bidding.setUpdatedAt(LocalDateTime.now());
        
        // 保存更新
        updateById(bidding);
        
        // 返回更新后的详情
        return convertToDetailResponse(bidding);
    }
    
    @Override
    public ForwardingBiddingDetailResponse getWinnerByRequirementId(Long requirementId) {
        // 验证需求是否存在
        ForwardingRequirement requirement = forwardingRequirementService.getById(requirementId);
        if (requirement == null || "1".equals(requirement.getDeleted())) {
            throw new BusinessException(404, "货代需求不存在");
        }
        
        // 查询中标竞价
        ForwardingBidding winner = forwardingBiddingMapper.findWinnerByRequirementId(requirementId);
        if (winner == null) {
            throw new BusinessException(404, "此需求尚未有中标货代");
        }
        
        // 返回中标详情
        return convertToDetailResponse(winner);
    }
    
    @Override
    public int getBiddingCount(Long requirementId) {
        return forwardingBiddingMapper.countByRequirementId(requirementId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ForwardingBiddingDetailResponse acceptBidding(Long biddingId) {
        log.info("开始接受竞价，竞价ID: {}", biddingId);
        
        // 获取竞价记录
        ForwardingBidding bidding = getById(biddingId);
        if (bidding == null || "1".equals(bidding.getDeleted())) {
            throw new BusinessException(404, "竞价记录不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        
        // 获取需求
        ForwardingRequirement requirement = forwardingRequirementService.getById(bidding.getForwardingRequirementId());
        if (requirement == null) {
            throw new BusinessException(404, "关联的货代需求不存在");
        }
        
        // 检查审核状态 - 只有审核通过的竞价才能被接受
        if (!"admin".equals(role) && !"approved".equals(bidding.getAuditStatus())) {
            throw new BusinessException(400, "只有审核通过的竞价才能被接受");
        }
        
        // 验证权限 - 允许关联采购订单的买家、卖家或管理员接受竞价
        boolean hasPermission = false;
        
        // 管理员始终有权限
        if ("admin".equals(role)) {
            hasPermission = true;
        } 
        // 需求创建者有权限
        else if (requirement.getCreatedBy().equals(currentUserId)) {
            hasPermission = true;
        }
        // 检查是否为关联订单的买家或卖家
        else if (("buyer".equals(role) || "seller".equals(role)) && requirement.getOrderId() != null) {
            // 通过订单ID获取订单信息
            try {
                // 检查当前用户是否为订单的买家或卖家
                boolean isRelatedToOrder = unifiedOrderService.isUserRelatedToOrder(
                    requirement.getOrderId(), 
                    currentUserId, 
                    Arrays.asList("buyer", "seller")
                );
                
                if (isRelatedToOrder) {
                    hasPermission = true;
                    log.info("用户 {} 作为订单 {} 的{}, 有权接受货代竞价", 
                           currentUserId, requirement.getOrderId(), role);
                }
            } catch (Exception e) {
                log.error("验证订单权限时出错: {}", e.getMessage(), e);
            }
        }
        
        // 如果没有权限，抛出异常
        if (!hasPermission) {
            throw new BusinessException(403, "无权接受此竞价，只有需求创建者、订单相关的买家/卖家或管理员可以接受");
        }
        
        // 状态检查 - 只有pending或submitted状态可以接受
        String status = bidding.getStatus().toLowerCase();
        if (!"pending".equals(status) && !"submitted".equals(status)) {
            throw new BusinessException(400, "只能接受待处理或已提交状态的竞价");
        }
        
        // 检查是否已存在已接受的竞价
        boolean hasAccepted = baseMapper.existsAcceptedBidding(bidding.getForwardingRequirementId());
        if (hasAccepted) {
            log.warn("该需求已有已接受的竞价，需求ID: {}", bidding.getForwardingRequirementId());
            throw new BusinessException(400, "该需求已有已接受的竞价，不能同时接受多个竞价");
        }
        
        // 防止重复接受同一个竞价
        if ("accepted".equals(bidding.getStatus())) {
            log.info("竞价已经被接受，直接返回结果，竞价ID: {}", biddingId);
            return convertToDetailResponse(bidding);
        }
        
        try {
            // 更新竞价状态为已接受
            bidding.setStatus("accepted");
            bidding.setUpdatedAt(LocalDateTime.now());
            int updateResult = updateById(bidding) ? 1 : 0;
            
            if (updateResult <= 0) {
                throw new BusinessException(500, "更新竞价状态失败");
            }
            
            log.info("竞价状态已更新为已接受，竞价ID: {}", biddingId);
            
            // 发送货代竞价接受通知
            sendForwardingBiddingAcceptedNotification(bidding);
            
            // 更新需求状态为已完成
            if (!"completed".equals(requirement.getStatus())) {
                boolean statusUpdated = forwardingRequirementService.updateStatus(requirement.getId(), "completed");
                if (!statusUpdated) {
                    log.warn("接受竞价成功，但更新需求状态失败，需求ID: {}", requirement.getId());
                } else {
                    log.info("需求状态已更新为已完成，需求ID: {}", requirement.getId());
                }
            }
            
            // 返回更新后的详情
            return convertToDetailResponse(bidding);
            
        } catch (Exception e) {
            log.error("接受竞价时发生异常，竞价ID: {}", biddingId, e);
            throw new BusinessException(500, "接受竞价失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public ForwardingBiddingDetailResponse rejectBidding(Long biddingId) {
        // 获取竞价记录
        ForwardingBidding bidding = getById(biddingId);
        if (bidding == null || "1".equals(bidding.getDeleted())) {
            throw new BusinessException(404, "竞价记录不存在");
        }
        
        // 验证权限
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        
        // 获取需求
        ForwardingRequirement requirement = forwardingRequirementService.getById(bidding.getForwardingRequirementId());
        if (requirement == null) {
            throw new BusinessException(404, "关联的货代需求不存在");
        }
        
        // 检查审核状态 - 只有审核通过的竞价才能被拒绝
        if (!"admin".equals(role) && !"approved".equals(bidding.getAuditStatus())) {
            throw new BusinessException(400, "只有审核通过的竞价才能被拒绝");
        }
        
        // 验证权限 - 只有需求创建者(买家)或管理员可以拒绝竞价
        if (!"admin".equals(role) && !requirement.getCreatedBy().equals(currentUserId)) {
            throw new BusinessException(403, "无权拒绝此竞价");
        }
        
        // 状态检查 - 只有pending或submitted状态可以拒绝
        String status = bidding.getStatus().toLowerCase();
        if (!"pending".equals(status) && !"submitted".equals(status)) {
            throw new BusinessException(400, "只能拒绝待处理或已提交状态的竞价");
        }
        
        // 更新竞价状态为已拒绝
        bidding.setStatus("rejected");
        bidding.setUpdatedAt(LocalDateTime.now());
        updateById(bidding);
        
        // 发送货代竞价拒绝通知
        sendForwardingBiddingRejectedNotification(bidding);
        
        // 返回更新后的详情
        return convertToDetailResponse(bidding);
    }
    
    @Override
    public List<ForwardingBiddingVO> getBiddingsByPurchaseOrderId(Long purchaseOrderId) {
        // 验证采购订单ID
        if (purchaseOrderId == null) {
            throw new BusinessException(400, "采购订单ID不能为空");
        }
        
        // 验证采购订单是否存在
        if (!unifiedOrderService.existsById(purchaseOrderId)) {
            throw new BusinessException(404, "采购订单不存在");
        }
        
        // 获取当前用户角色和ID
        String role = SecurityContextUtil.getCurrentUserRole();
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        
        // 验证权限
        // 这里可以根据业务需求增加更详细的权限检查
        // 例如只有买家、卖家和管理员可以查看相关竞价
        // 货代只能查看自己的竞价
        
        // 查询竞价列表
        List<ForwardingBidding> biddings = forwardingBiddingMapper.findByPurchaseOrderId(purchaseOrderId);
        
        // 货代只能看到自己的竞价
        if ("forwarder".equals(role)) {
            return biddings.stream()
                    .filter(b -> b.getForwarderId().equals(currentUserId))
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
        }
        
        // 买家和卖家只能看到审核通过的竞价
        if ("buyer".equals(role) || "seller".equals(role)) {
            return biddings.stream()
                    .filter(b -> "approved".equals(b.getAuditStatus()))
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
        }
        
        // 管理员可以看到所有竞价
        return biddings.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
    
    /**
     * 管理员获取所有货代竞价记录（增强版分页查询）
     */
    @Override
    public IPage<ForwardingBiddingVO> getAllBiddingsForAdmin(
            Integer current, 
            Integer size, 
            Long requirementId, 
            Long forwarderId, 
            String status,
            String startDate,
            String endDate,
            String orderBy,
            Boolean ascending,
            Long purchaseOrderId,
            Double minPrice,
            Double maxPrice,
            String keyword) {
        
        // 验证当前用户是否为管理员
        String role = SecurityContextUtil.getCurrentUserRole();
        if (!"admin".equals(role)) {
            throw new BusinessException(403, "只有管理员可以访问此接口");
        }
        
        // 构建查询条件
        LambdaQueryWrapper<ForwardingBidding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForwardingBidding::getDeleted, "0");
        
        // 根据需求ID过滤
        if (requirementId != null) {
            queryWrapper.eq(ForwardingBidding::getForwardingRequirementId, requirementId);
        }
        
        // 根据货代ID过滤
        if (forwarderId != null) {
            queryWrapper.eq(ForwardingBidding::getForwarderId, forwarderId);
        }
        
        // 根据状态过滤
        if (status != null && !status.isEmpty()) {
            queryWrapper.eq(ForwardingBidding::getStatus, status);
        }
        
        // 根据采购订单ID过滤
        if (purchaseOrderId != null) {
            queryWrapper.eq(ForwardingBidding::getPurchaseOrderId, purchaseOrderId);
        }
        
        // 根据价格范围过滤
        if (minPrice != null) {
            queryWrapper.ge(ForwardingBidding::getBiddingPrice, minPrice);
        }
        if (maxPrice != null) {
            queryWrapper.le(ForwardingBidding::getBiddingPrice, maxPrice);
        }
        
        // 根据日期范围过滤
        if (startDate != null && !startDate.isEmpty()) {
            try {
                LocalDateTime startDateTime = LocalDateTime.parse(startDate + "T00:00:00");
                queryWrapper.ge(ForwardingBidding::getCreatedAt, startDateTime);
            } catch (Exception e) {
                log.warn("解析开始日期失败: {}", e.getMessage());
            }
        }
        if (endDate != null && !endDate.isEmpty()) {
            try {
                LocalDateTime endDateTime = LocalDateTime.parse(endDate + "T23:59:59");
                queryWrapper.le(ForwardingBidding::getCreatedAt, endDateTime);
            } catch (Exception e) {
                log.warn("解析结束日期失败: {}", e.getMessage());
            }
        }
        
        // 关键词搜索（描述字段）
        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.and(wrapper -> 
                wrapper.like(ForwardingBidding::getDescription, keyword)
                    .or()
                    .like(ForwardingBidding::getShippingMethod, keyword)
                    .or()
                    .like(ForwardingBidding::getShippingRoute, keyword)
            );
        }
        
        // 排序
        String sortField = "createdAt"; // 默认排序字段
        switch (orderBy.toLowerCase()) {
            case "price":
                sortField = "biddingPrice";
                break;
            case "updated_at":
                sortField = "updatedAt";
                break;
            case "status":
                sortField = "status";
                break;
            case "requirement_id":
                sortField = "forwardingRequirementId";
                break;
            case "forwarder_id":
                sortField = "forwarderId";
                break;
            case "estimated_days":
                sortField = "estimatedDays";
                break;
            default:
                sortField = "createdAt";
                break;
        }
        
        // 获取对应的字段并设置排序
        if (Boolean.TRUE.equals(ascending)) {
            queryWrapper.orderByAsc(getFieldByName(sortField));
        } else {
            queryWrapper.orderByDesc(getFieldByName(sortField));
        }
        
        // 分页查询
        Page<ForwardingBidding> page = new Page<>(current, size);
        IPage<ForwardingBidding> resultPage = page(page, queryWrapper);
        
        // 转换结果
        IPage<ForwardingBiddingVO> voPage = resultPage.convert(this::convertToVO);
        
        // 如果需要额外处理关键词搜索（例如货代名称搜索等需要关联查询的内容）
        if (keyword != null && !keyword.isEmpty()) {
            // 这里可以根据需求添加额外的处理，例如二次筛选结果
            // 示例：如果要搜索货代名称，可以在这里对结果进行二次过滤
            List<ForwardingBiddingVO> filteredRecords = voPage.getRecords().stream()
                .filter(vo -> vo.getForwarderName() != null && vo.getForwarderName().contains(keyword))
                .collect(Collectors.toList());
            
            // 如果有匹配结果，替换原记录
            if (!filteredRecords.isEmpty()) {
                // 注意：这样处理会丢失分页信息，实际项目中可能需要更复杂的实现
                voPage.setRecords(filteredRecords);
            }
        }
        
        return voPage;
    }
    
    // 辅助方法：根据字段名获取对应的字段函数
    private com.baomidou.mybatisplus.core.toolkit.support.SFunction<ForwardingBidding, ?> getFieldByName(String fieldName) {
        switch (fieldName) {
            case "biddingPrice":
                return ForwardingBidding::getBiddingPrice;
            case "updatedAt":
                return ForwardingBidding::getUpdatedAt;
            case "status":
                return ForwardingBidding::getStatus;
            case "forwardingRequirementId":
                return ForwardingBidding::getForwardingRequirementId;
            case "forwarderId":
                return ForwardingBidding::getForwarderId;
            case "estimatedDays":
                return ForwardingBidding::getEstimatedDays;
            default:
                return ForwardingBidding::getCreatedAt;
        }
    }
    
    // 辅助方法：将实体转换为详情响应
    private ForwardingBiddingDetailResponse convertToDetailResponse(ForwardingBidding bidding) {
        ForwardingBiddingDetailResponse response = new ForwardingBiddingDetailResponse();
        BeanUtils.copyProperties(bidding, response);
        
        // 处理文件列表
        if (bidding.getDocuments() != null && !bidding.getDocuments().isEmpty()) {
            response.setDocuments(Arrays.asList(bidding.getDocuments().split(",")));
        } else {
            response.setDocuments(new ArrayList<>());
        }
        
        // 获取需求信息
        ForwardingRequirement requirement = forwardingRequirementService.getById(bidding.getForwardingRequirementId());
        if (requirement != null) {
            response.setRequirementTitle(requirement.getTitle());
            response.setOrderId(requirement.getOrderId());
        }
        
        // 确保purchaseOrderId被正确设置
        response.setPurchaseOrderId(bidding.getPurchaseOrderId());
        
        // 如果竞价直接关联了采购订单，也设置到响应中
        if (bidding.getPurchaseOrderId() != null) {
            response.setOrderId(bidding.getPurchaseOrderId());
        }
        
        // 获取货代信息
        response.setForwarderName(getUserName(bidding.getForwarderId()));
        
        return response;
    }
    
    // 辅助方法：将实体转换为视图对象
    private ForwardingBiddingVO convertToVO(ForwardingBidding bidding) {
        ForwardingBiddingVO vo = new ForwardingBiddingVO();
        BeanUtils.copyProperties(bidding, vo);
        
        // 显式处理 winner 字段，避免 NullPointerException
        vo.setWinner(bidding.getWinner() != null && bidding.getWinner());
        
        // 获取需求信息
        ForwardingRequirement requirement = forwardingRequirementService.getById(bidding.getForwardingRequirementId());
        if (requirement != null) {
            vo.setRequirementTitle(requirement.getTitle());
        }
        
        // 获取货代信息
        vo.setForwarderName(getUserName(bidding.getForwarderId()));
        
        // 获取最新的物流状态
        vo.setLatestStatus(getLatestShippingStatus(bidding.getId()));
        
        return vo;
    }
    
    // 辅助方法：获取用户名
    private String getUserName(Long userId) {
        try {
            return userService.getUsernameById(userId);
        } catch (Exception e) {
            return "User-" + userId;
        }
    }
    
    // 辅助方法：获取用户公司
    private String getUserCompany(Long userId) {
        try {
            return userService.getCompanyById(userId);
        } catch (Exception e) {
            return "";
        }
    }
    
    // 辅助方法：获取最新的物流状态
    private String getLatestShippingStatus(Long biddingId) {
        // 这里应该调用ShippingStatusService获取最新状态
        // 简化实现，实际项目中需要替换为真实的状态服务调用
        return "";
    }

    @Override
    public ForwardingBidding getById(Long biddingId) {
        return forwardingBiddingMapper.findByBiddingId(biddingId);
    }
    
    /**
     * 管理员审核竞价
     */
    @Override
    @Transactional
    public ForwardingBiddingDetailResponse auditBidding(Long biddingId, boolean approved, String remark) {
        // 验证当前用户是否为管理员
        String role = SecurityContextUtil.getCurrentUserRole();
        if (!"admin".equals(role)) {
            throw new BusinessException(403, "只有管理员可以审核竞价");
        }
        
        // 获取竞价记录
        ForwardingBidding bidding = getById(biddingId);
        if (bidding == null) {
            throw new BusinessException(404, "竞价记录不存在");
        }
        
        // 只能审核待审核状态的竞价
        if (!"pending_audit".equals(bidding.getAuditStatus())) {
            throw new BusinessException(400, "只能审核待审核状态的竞价");
        }
        
        // 更新审核状态
        bidding.setAuditStatus(approved ? "approved" : "rejected");
        bidding.setAuditRemark(remark);
        bidding.setUpdatedAt(LocalDateTime.now());
        
        // 保存更新
        updateById(bidding);
        
        // 发送货代竞价审核通知
        sendForwardingBiddingAuditNotification(bidding, approved);
        
        // 返回更新后的详情
        return convertToDetailResponse(bidding);
    }
    
    /**
     * 获取待审核的竞价列表
     */
    @Override
    public IPage<ForwardingBiddingVO> getPendingAuditBiddings(Integer current, Integer size) {
        // 验证当前用户是否为管理员
        String role = SecurityContextUtil.getCurrentUserRole();
        if (!"admin".equals(role)) {
            throw new BusinessException(403, "只有管理员可以查看待审核竞价");
        }
        
        // 构建查询条件
        LambdaQueryWrapper<ForwardingBidding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForwardingBidding::getAuditStatus, "pending_audit")
                    .eq(ForwardingBidding::getDeleted, "0")
                    .orderByDesc(ForwardingBidding::getCreatedAt);
        
        // 分页查询
        Page<ForwardingBidding> page = new Page<>(current, size);
        Page<ForwardingBidding> resultPage = page(page, queryWrapper);
        
        // 转换为VO
        IPage<ForwardingBiddingVO> voPage = resultPage.convert(this::convertToVO);
        
        return voPage;
    }
    
    /**
     * 获取可见的竞价列表（审核通过的竞价和自己的竞价）
     */
    @Override
    public List<ForwardingBiddingVO> getVisibleBiddingsByRequirementId(Long requirementId) {
        // 验证需求是否存在
        ForwardingRequirement requirement = forwardingRequirementService.getById(requirementId);
        if (requirement == null || "1".equals(requirement.getDeleted())) {
            throw new BusinessException(404, "货代需求不存在");
        }
        
        // 获取当前用户信息
        Long currentUserId = SecurityContextUtil.getCurrentUserId();
        String role = SecurityContextUtil.getCurrentUserRole();
        
        // 构建查询条件
        LambdaQueryWrapper<ForwardingBidding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForwardingBidding::getForwardingRequirementId, requirementId)
                    .eq(ForwardingBidding::getDeleted, "0");
        
        // 如果是货代，只能看到审核通过的竞价和自己的竞价
        if ("forwarder".equals(role)) {
            queryWrapper.and(wrapper -> wrapper
                .eq(ForwardingBidding::getAuditStatus, "approved")
                .or()
                .eq(ForwardingBidding::getForwarderId, currentUserId)
            );
        } 
        // 如果是管理员，可以看到所有竞价
        else if ("admin".equals(role)) {
            // 管理员可以看到所有竞价，不需要额外条件
        } 
        // 如果是买家或卖家，只能看到审核通过的竞价
        else {
            queryWrapper.eq(ForwardingBidding::getAuditStatus, "approved");
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(ForwardingBidding::getCreatedAt);
        
        // 查询数据
        List<ForwardingBidding> biddings = list(queryWrapper);
        
        // 转换为VO
        return biddings.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    // ==================== 货代竞价通知发送方法 ====================
    
    /**
     * 发送货代竞价提交通知
     */
    @Async("taskExecutor")
    private void sendForwardingBiddingSubmitNotifications(ForwardingBidding bidding, ForwardingRequirement requirement) {
        try {
            // 通知需求创建者有新的货代竞价
            Notification creatorNotification = new Notification();
            creatorNotification.setType(NotificationType.FORWARDING_BIDDING_SUBMIT.getCode());
            creatorNotification.setTitle("您有新的货代竞价");
            creatorNotification.setContent("您发布的货代需求收到了新的竞价");
            creatorNotification.setReceiverId(requirement.getCreatedBy());
            creatorNotification.setReceiverRole("buyer"); // 需求创建者通常是买家
            creatorNotification.setRelatedId(bidding.getId());
            notificationService.createNotification(creatorNotification);
            
            // 通知所有管理员有新的货代竞价待审核
            List<User> adminUsers = userMapper.selectAllAdmins();
            for (User admin : adminUsers) {
                Notification adminNotification = new Notification();
                adminNotification.setType(NotificationType.FORWARDING_BIDDING_SUBMIT.getCode());
                adminNotification.setTitle("有新的货代竞价待审核");
                adminNotification.setContent("有新的货代竞价需要审核");
                adminNotification.setReceiverId(admin.getId());
                adminNotification.setReceiverRole("admin");
                adminNotification.setRelatedId(bidding.getId());
                notificationService.createNotification(adminNotification);
            }
            
            log.info("货代竞价提交通知发送成功，竞价ID: {}", bidding.getId());
        } catch (Exception e) {
            log.error("发送货代竞价提交通知失败，竞价ID: {}", bidding.getId(), e);
        }
    }
    
    /**
     * 发送货代竞价接受通知
     */
    @Async("taskExecutor")
    private void sendForwardingBiddingAcceptedNotification(ForwardingBidding bidding) {
        try {
            // 通知货代竞价被接受
            Notification forwarderNotification = new Notification();
            forwarderNotification.setType(NotificationType.FORWARDING_BIDDING_ACCEPTED.getCode());
            forwarderNotification.setTitle("您的货代竞价已被接受");
            forwarderNotification.setContent("恭喜！您的货代竞价已被接受，请准备安排物流服务");
            forwarderNotification.setReceiverId(bidding.getForwarderId());
            forwarderNotification.setReceiverRole("forwarder");
            forwarderNotification.setRelatedId(bidding.getId());
            notificationService.createNotification(forwarderNotification);
            
            // 通知所有管理员有货代竞价被接受
            List<User> adminUsers = userMapper.selectAllAdmins();
            for (User admin : adminUsers) {
                Notification adminNotification = new Notification();
                adminNotification.setType(NotificationType.FORWARDING_BIDDING_ACCEPTED.getCode());
                adminNotification.setTitle("货代竞价已被接受");
                adminNotification.setContent("有货代竞价已被客户接受，请关注后续物流服务进展");
                adminNotification.setReceiverId(admin.getId());
                adminNotification.setReceiverRole("admin");
                adminNotification.setRelatedId(bidding.getId());
                notificationService.createNotification(adminNotification);
            }
            
            log.info("货代竞价接受通知发送成功，竞价ID: {}", bidding.getId());
        } catch (Exception e) {
            log.error("发送货代竞价接受通知失败，竞价ID: {}", bidding.getId(), e);
        }
    }
    
    /**
     * 发送货代竞价拒绝通知
     */
    @Async("taskExecutor")
    private void sendForwardingBiddingRejectedNotification(ForwardingBidding bidding) {
        try {
            // 通知货代竞价被拒绝
            Notification forwarderNotification = new Notification();
            forwarderNotification.setType(NotificationType.FORWARDING_BIDDING_REJECTED.getCode());
            forwarderNotification.setTitle("您的货代竞价已被拒绝");
            forwarderNotification.setContent("很遗憾，您的货代竞价已被拒绝");
            forwarderNotification.setReceiverId(bidding.getForwarderId());
            forwarderNotification.setReceiverRole("forwarder");
            forwarderNotification.setRelatedId(bidding.getId());
            notificationService.createNotification(forwarderNotification);
            
            // 通知所有管理员有货代竞价被拒绝
            List<User> adminUsers = userMapper.selectAllAdmins();
            for (User admin : adminUsers) {
                Notification adminNotification = new Notification();
                adminNotification.setType(NotificationType.FORWARDING_BIDDING_REJECTED.getCode());
                adminNotification.setTitle("货代竞价已被拒绝");
                adminNotification.setContent("有货代竞价已被客户拒绝");
                adminNotification.setReceiverId(admin.getId());
                adminNotification.setReceiverRole("admin");
                adminNotification.setRelatedId(bidding.getId());
                notificationService.createNotification(adminNotification);
            }
            
            log.info("货代竞价拒绝通知发送成功，竞价ID: {}", bidding.getId());
        } catch (Exception e) {
            log.error("发送货代竞价拒绝通知失败，竞价ID: {}", bidding.getId(), e);
        }
    }
    
    /**
     * 发送货代竞价审核通知
     */
    @Async("taskExecutor")
    private void sendForwardingBiddingAuditNotification(ForwardingBidding bidding, boolean approved) {
        try {
            Notification notification = new Notification();
            notification.setType(NotificationType.FORWARDING_BIDDING_AUDIT.getCode());
            
            if (approved) {
                notification.setTitle("您的货代竞价已通过审核");
                notification.setContent("您的货代竞价已通过审核，现在可以被客户查看");
            } else {
                notification.setTitle("您的货代竞价审核未通过");
                notification.setContent("很遗憾，您的货代竞价审核未通过");
                if (bidding.getAuditRemark() != null && !bidding.getAuditRemark().trim().isEmpty()) {
                    notification.setContent(notification.getContent() + "，原因：" + bidding.getAuditRemark());
                }
            }
            
            notification.setReceiverId(bidding.getForwarderId());
            notification.setReceiverRole("forwarder");
            notification.setRelatedId(bidding.getId());
            notificationService.createNotification(notification);
            
            // 如果审核通过，还要通知需求创建者
            if (approved) {
                ForwardingRequirement requirement = forwardingRequirementService.getById(bidding.getForwardingRequirementId());
                if (requirement != null) {
                    Notification creatorNotification = new Notification();
                    creatorNotification.setType(NotificationType.FORWARDING_BIDDING_AUDIT.getCode());
                    creatorNotification.setTitle("您的货代需求有竞价已通过审核");
                    creatorNotification.setContent("您发布的货代需求有新的竞价已通过审核");
                    creatorNotification.setReceiverId(requirement.getCreatedBy());
                    creatorNotification.setReceiverRole("buyer");
                    creatorNotification.setRelatedId(bidding.getId());
                    notificationService.createNotification(creatorNotification);
                }
            }
            
            log.info("货代竞价审核通知发送成功，竞价ID: {}, 审核结果: {}", bidding.getId(), approved ? "通过" : "拒绝");
        } catch (Exception e) {
            log.error("发送货代竞价审核通知失败，竞价ID: {}", bidding.getId(), e);
        }
    }

} 