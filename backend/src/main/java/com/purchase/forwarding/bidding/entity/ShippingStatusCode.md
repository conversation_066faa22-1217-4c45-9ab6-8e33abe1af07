# ShippingStatusCode.md

## 1. 文件概述

`ShippingStatusCode` 是货代模块中的一个枚举类型，位于 `com.purchase.forwarding.bidding.entity` 包中。它定义了物流过程中所有可能的标准化状态代码及其对应的名称和描述。这个枚举旨在提供一个清晰、类型安全的方式来表示和管理物流状态，确保系统内部和外部（如与第三方物流系统集成）对物流状态的理解一致性。它是构建物流跟踪功能的基础。

## 2. 核心功能

*   **状态标准化**: 定义了一系列预定义的物流状态代码，避免了使用自由文本或魔法字符串来表示状态，提高了数据的一致性和可维护性。
*   **语义丰富**: 每个枚举常量都包含 `name`（状态名称）和 `description`（状态描述），提供了更丰富的语义信息，便于理解和展示。
*   **类型安全**: 作为枚举，它提供了编译时类型检查，避免了因拼写错误导致的运行时问题。
*   **可读性**: 使用有意义的名称来表示不同的物流状态，使得代码更易于理解和维护。

## 3. 属性说明

`ShippingStatusCode` 枚举的每个常量都包含以下属性：

- **`name` (String)**: 物流状态的中文名称（例如：`待处理`, `运输中`, `已送达`）。
- **`description` (String)**: 物流状态的详细描述，解释了该状态的含义。

### 3.1 枚举常量列表

- **`PENDING`**: 待处理 - 货物等待安排运输。
- **`PICKUP_ARRANGED`**: 已安排提货 - 货代已安排取货。
- **`PICKED_UP`**: 已提货 - 货物已被提取。
- **`DEPARTURE_WAREHOUSE`**: 已入发货仓库 - 货物已到达发货仓库。
- **`CUSTOMS_EXPORT`**: 出口清关中 - 货物正在办理出口清关。
- **`EXPORT_CLEARED`**: 出口清关完成 - 出口清关手续已完成。
- **`IN_TRANSIT`**: 运输中 - 货物正在运输途中。
- **`ARRIVED_DESTINATION_COUNTRY`**: 已到达目的国 - 货物已到达目的国。
- **`CUSTOMS_IMPORT`**: 进口清关中 - 货物正在办理进口清关。
- **`IMPORT_CLEARED`**: 进口清关完成 - 进口清关手续已完成。
- **`LOCAL_DELIVERY`**: 本地配送中 - 货物正在本地配送。
- **`DELIVERED`**: 已送达 - 货物已成功送达收货人。
- **`EXCEPTION`**: 运输异常 - 运输过程中出现异常。

## 4. 业务规则

*   **状态流转**: 物流状态的流转应遵循预定义的业务流程。例如，货物不能直接从 `PENDING` 跳到 `DELIVERED`，必须经过中间状态。
*   **唯一性**: 每个枚举常量都是唯一的，代表一个特定的物流状态。
*   **扩展性**: 如果未来需要增加新的物流状态，可以直接在此枚举中添加新的常量，而无需修改已有的代码。
*   **与数据库映射**: 在数据库中，物流状态通常存储为字符串（`VARCHAR`），与此枚举的 `name()` 或 `description` 对应。

## 5. 使用示例

```java
// 1. 在 ShippingStatus 实体中使用 ShippingStatusCode
public class ShippingStatus {
    private String statusCode; // 存储枚举的name()
    private String statusName; // 存储枚举的name
    // ...
    public void setStatusCode(ShippingStatusCode code) {
        this.statusCode = code.name();
        this.statusName = code.getName();
    }
    public ShippingStatusCode getStatusCodeEnum() {
        return ShippingStatusCode.valueOf(statusCode);
    }
}

// 2. 在 ShippingStatusService 中更新物流状态
@Service
public class ShippingStatusServiceImpl implements ShippingStatusService {
    @Autowired
    private ShippingStatusMapper shippingStatusMapper;

    @Transactional
    public ShippingStatusVO addShippingStatus(ShippingStatusUpdateRequest request) {
        ShippingStatus status = new ShippingStatus();
        status.setForwardingBiddingId(request.getForwardingBiddingId());
        status.setOrderId(request.getOrderId());
        status.setForwardingRequirementId(request.getForwardingRequirementId());
        
        // 使用枚举设置状态
        ShippingStatusCode code = ShippingStatusCode.valueOf(request.getStatusCode());
        status.setStatusCode(code.name());
        status.setStatusName(code.getName());
        status.setDescription(request.getDescription());
        status.setOccurTime(request.getOccurTime());
        status.setExceptionFlag(code == ShippingStatusCode.EXCEPTION); // 如果是异常状态码，则标记异常
        if (code == ShippingStatusCode.EXCEPTION) {
            status.setExceptionReason(request.getExceptionReason());
        }

        shippingStatusMapper.insert(status);
        return convertToVO(status);
    }
}

// 3. 在前端展示时，根据状态码获取描述
// 假设后端返回的 ShippingStatusVO 包含 statusCode 和 statusName
/*
function displayShippingStatus(statusCode) {
  switch (statusCode) {
    case 'PENDING': return '待处理';
    case 'IN_TRANSIT': return '运输中';
    case 'DELIVERED': return '已送达';
    case 'EXCEPTION': return '运输异常';
    default: return '未知状态';
  }
}
*/

// 4. 测试示例
@SpringBootTest
class ShippingStatusCodeTest {
    @Test
    void testEnumProperties() {
        ShippingStatusCode pending = ShippingStatusCode.PENDING;
        assertThat(pending.name()).isEqualTo("PENDING");
        assertThat(pending.getName()).isEqualTo("待处理");
        assertThat(pending.getDescription()).isEqualTo("货物等待安排运输");

        ShippingStatusCode delivered = ShippingStatusCode.DELIVERED;
        assertThat(delivered.name()).isEqualTo("DELIVERED");
        assertThat(delivered.getName()).isEqualTo("已送达");
        assertThat(delivered.getDescription()).isEqualTo("货物已成功送达收货人");
    }

    @Test
    void testValueOf() {
        assertThat(ShippingStatusCode.valueOf("IN_TRANSIT")).isEqualTo(ShippingStatusCode.IN_TRANSIT);
    }

    @Test
    void testInvalidValue() {
        assertThrows(IllegalArgumentException.class, () -> {
            ShippingStatusCode.valueOf("UNKNOWN_STATUS");
        });
    }
}
```

## 6. 注意事项

*   **类型安全**: 使用枚举代替字符串可以避免因拼写错误导致的运行时问题，提高代码的健壮性。
*   **可读性与可维护性**: 枚举名称、名称和描述清晰地表达了其含义，使得代码更易于理解和维护。
*   **扩展性**: 当需要引入新的物流状态时，只需在枚举中添加新的常量，而无需修改大量使用字符串的地方。
*   **与数据库映射**: 在数据库中，枚举值通常存储为字符串（`VARCHAR`）。在实体类中，可以将枚举的 `name()` 或 `description` 存储到数据库字段中。
*   **Lombok `@Getter`**: 使用 `@Getter` 简化了Getter方法的编写。
*   **业务逻辑**: 实体类或服务层应根据 `ShippingStatusCode` 的值来触发不同的业务逻辑，例如当状态为 `EXCEPTION` 时，可能需要发送警报或创建异常工单。
