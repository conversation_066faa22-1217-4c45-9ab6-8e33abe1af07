# com.purchase.forwarding.bidding.dto.request 包

## 包的整体介绍 (Overall Package Introduction)
`com.purchase.forwarding.bidding.dto.request` 包包含了货代竞价模块中用于接收前端请求的数据传输对象（DTO）。这些 DTO 旨在封装和标准化货代竞价和物流状态相关的各种请求数据，包括竞价的创建、更新、审核以及物流状态的更新。通过使用这些 DTO，该包实现了数据传输的清晰性、类型安全性和验证机制，同时确保了请求数据的合法性。

## 目录结构概览 (Directory Structure Overview)
*   `ForwardingBiddingAuditRequest.java`: 货代竞价审核请求的数据封装。
*   `ForwardingBiddingCreateRequest.java`: 货代竞价创建请求的数据封装。
*   `ForwardingBiddingUpdateRequest.java`: 货代竞价更新请求的数据封装。
*   `ShippingStatusUpdateRequest.java`: 物流状态更新请求的数据封装。
*   `ForwardingBiddingAuditRequest.md`: `ForwardingBiddingAuditRequest.java` 的文档。
*   `ForwardingBiddingCreateRequest.md`: `ForwardingBiddingCreateRequest.java` 的文档。
*   `ForwardingBiddingUpdateRequest.md`: `ForwardingBiddingUpdateRequest.java` 的文档。
*   `ShippingStatusUpdateRequest.md`: `ShippingStatusUpdateRequest.java` 的文档。

## 详细说明 (Detailed Explanation of Component Collaboration)
`com.purchase.forwarding.bidding.dto.request` 包中的 DTOs 主要作为数据载体，在前端与后端控制器层（`com.purchase.forwarding.bidding.controller`）之间传递数据。它们与以下组件紧密协作：

1.  **控制器层 (`com.purchase.forwarding.bidding.controller`):**
    *   控制器方法（如 `ForwardingBiddingController` 和 `ShippingStatusController` 中的 `POST` 或 `PUT` 方法）使用这些 DTO 作为 `@RequestBody` 参数来接收前端的请求数据。
    *   控制器通常会使用 `@Validated` 或 `@Valid` 注解来触发对这些 DTO 中字段的验证。

2.  **JSR 303/349 (Bean Validation):**
    *   所有 DTO 都广泛使用了 Bean Validation 注解（如 `@NotNull`, `@NotBlank`, `@Positive`, `@Size`）对字段进行非空、数值范围和长度等基本验证。
    *   这些验证在请求进入控制器方法之前执行，确保了请求数据的初步合法性。

3.  **应用服务层 (`com.purchase.forwarding.bidding.service`):**
    *   控制器将验证通过的 DTO 传递给应用服务层的方法。应用服务层会进一步处理这些 DTO，可能将其转换为领域实体，并执行更复杂的业务逻辑和条件验证。

**协作流程总结:**

*   **前端发送请求:** 前端向控制器发送包含 JSON 数据的 HTTP 请求（例如，创建竞价或添加物流状态）。
*   **数据绑定与验证:** Spring MVC 自动将请求体中的 JSON 数据绑定到相应的 DTO 对象上（例如 `ForwardingBiddingCreateRequest`）。在此过程中，DTO 中定义的 Bean Validation 规则会被触发。如果验证失败，请求会在进入控制器方法之前被拦截，并返回一个包含验证错误信息的响应。
*   **控制器调用服务:** 验证通过后，控制器将 DTO 传递给 `ForwardingBiddingService` 或 `ShippingStatusService` 的方法。
*   **服务层处理:** 服务层接收 DTO，执行业务逻辑，这可能包括将 DTO 转换为领域实体，进行更深层次的业务规则验证（例如，根据账户类型验证特定字段），并与数据访问层交互。

这种请求 DTO 设计模式确保了数据在应用程序不同层级之间传输时的结构化、类型安全和初步验证，从而提高了 API 的健壮性和开发效率。
