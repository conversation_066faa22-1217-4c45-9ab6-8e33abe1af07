package com.purchase.forwarding.bidding.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 货代竞价视图对象
 */
@Data
public class ForwardingBiddingVO {
    
    private Long id;
    
    private Long forwardingRequirementId;
    
    private Long purchaseOrderId;
    
    private String requirementTitle;
    
    private Long forwarderId;
    
    private String forwarderName;
    
    private String forwarderCompanyName;
    
    private BigDecimal biddingPrice;
    
    private String currency;
    
    private String shippingMethod;
    
    private Integer estimatedDays;
    
    private Boolean insuranceIncluded;
    
    private Boolean customsService;
    
    /**
     * 是否需要产品认证：0-不需要，1-需要
     */
    private String needCertification;

    /**
     * 是否需要货物熏蒸：0-不需要，1-需要
     */
    private String needFumigation;
    
    // 新增费用字段 - 根据运输条款可选填写
    private BigDecimal costExportPacking; // 出口包装费 (卖方承担)
    
    private BigDecimal costOriginInlandHaulage; // 起运地内陆运输费 (卖方承担)
    
    private BigDecimal costExportCustomsDocs; // 出口报关及单证费 (卖方承担)
    
    private BigDecimal costOriginHandling; // 起运地操作费 (卖方承担)
    
    private BigDecimal costMainFreight; // 主运费 (卖方承担部分)
    
    private BigDecimal costFreightSurcharges; // 运费附加费 (卖方承担部分)
    
    private BigDecimal costCargoInsurance; // 货物运输保险费 (卖方支付的保费)
    
    private BigDecimal costDestinationHandlingBySeller; // 目的地操作费 (卖方承担)
    
    private BigDecimal costDestinationUnloadingBySeller; // 目的地卸货费 (卖方承担)
    
    private BigDecimal costDestinationInlandHaulageBySeller; // 目的地内陆运输费 (卖方承担)
    
    private BigDecimal costImportCustomsDocsBySeller; // 进口报关及单证费 (卖方承担-DDP)
    
    private BigDecimal costImportDutiesTaxesBySeller; // 进口关税及税费 (卖方承担-DDP)
    
    private BigDecimal costForwarderServiceFee; // 货代服务费/操作费 (卖方承担)
    
    private String deliveryTerms; // 交货条款/国际贸易术语 (例如: FOB, CIF)
    
    private String status;
    
    private String auditStatus;
    
    private String auditRemark;
    
    private Boolean winner;
    
    private String trackingNumber;
    
    private String latestStatus;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
} 