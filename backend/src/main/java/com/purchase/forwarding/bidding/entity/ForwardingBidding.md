# ForwardingBidding.java

## 文件概述 (File Overview)
`ForwardingBidding.java` 是货代竞价实体类，位于 `com.purchase.forwarding.bidding.entity` 包中，使用MyBatis-Plus注解进行ORM映射。该实体类定义了货代竞价的完整数据模型，是竞价业务领域的核心聚合根，包含竞价基本信息、价格信息、服务承诺、竞价状态等核心属性。通过完善的字段设计和业务逻辑，支持货代竞价的全生命周期管理，实现了竞价数据的标准化存储和高效查询，并提供了完善的竞价评估和选择机制。

## 核心功能 (Core Functionality)
*   **竞价信息管理**: 存储货代竞价的完整信息，包括价格、服务承诺、时效保证等
*   **状态流转控制**: 管理竞价状态的流转，包括待审核、已提交、中标、落标等状态
*   **价格信息存储**: 详细记录各项费用明细，包括运费、保险费、杂费等
*   **服务承诺定义**: 包含服务质量承诺、时效承诺、安全保障等服务详情
*   **竞价者信息**: 记录竞价货代的身份信息、资质证明、历史评价等
*   **时间管理**: 管理竞价的提交时间、有效期、响应时间等时间节点
*   **竞争分析**: 支持竞价比较和竞争分析功能
*   **风险评估**: 提供竞价风险评估和信用评级功能
*   **合规检查**: 确保竞价符合相关法规和业务规范
*   **关联关系维护**: 与货代需求、订单等建立关联关系
*   **评估算法支持**: 提供竞价评估算法所需的关键数据
*   **业务规则验证**: 内置业务规则验证，确保竞价合法性

## DDD分析 (DDD Analysis)

### 限界上下文识别
*   **所属上下文**: 货代竞价管理上下文（Forwarding Bidding Context）
*   **上下文边界**: 与需求上下文、订单上下文、用户上下文、支付上下文的交互边界
*   **领域语言**: 货代竞价、竞价价格、服务承诺、中标、落标、竞价评估等核心业务术语

### 聚合设计分析
*   **聚合根判断**: ✅ 是聚合根 - 货代竞价是独立的业务概念，具有完整的生命周期
*   **聚合边界**: 包含竞价基本信息、价格明细、服务承诺、时间约束等
*   **业务不变量**: 
    *   竞价状态流转必须符合业务规则（待提交→已提交→评估中→中标/落标）
    *   价格信息必须完整且合理（总价=各项费用之和）
    *   竞价时间必须在需求的有效期内
    *   竞价者必须具备相应的资质和能力
    *   同一货代对同一需求只能提交一次竞价（可修改）
*   **状态一致性**: 聚合内部状态变更必须保持一致性，价格变更需要重新计算总价

### 领域概念分析
*   **核心领域概念**: 货代竞价是货代服务商对运输需求的响应和报价
*   **业务价值**: 实现价格发现机制，促进公平竞争，提高服务质量
*   **生命周期**: 创建→提交→评估→中标/落标→执行的完整业务流程
*   **状态机**: 
    *   DRAFT（草稿）→ SUBMITTED（已提交）
    *   SUBMITTED → UNDER_EVALUATION（评估中）
    *   UNDER_EVALUATION → WINNING（中标）
    *   UNDER_EVALUATION → LOSING（落标）
    *   任意状态 → WITHDRAWN（已撤回）

### 值对象候选
*   **价格明细**: PriceDetail（运费、保险费、杂费、总价、货币类型）
*   **服务承诺**: ServiceCommitment（时效承诺、质量保证、安全保障）
*   **竞价者信息**: BidderInfo（公司名称、资质证明、联系方式、信用评级）
*   **时间约束**: BiddingTimeConstraint（提交时间、有效期、响应时间）
*   **评估结果**: EvaluationResult（评分、排名、中标概率、评估意见）

## 业务规则 (Business Rules)
*   **竞价时效规则**: 竞价必须在需求的有效期内提交
*   **价格合理性**: 竞价价格必须在合理范围内，不能明显偏离市场价格
*   **资质要求**: 竞价者必须具备相应的运输资质和能力证明
*   **唯一性约束**: 同一货代对同一需求只能有一个有效竞价
*   **修改限制**: 竞价提交后只能在特定时间窗口内修改
*   **撤回规则**: 竞价撤回需要符合特定条件，可能产生信用影响
*   **评估标准**: 竞价评估基于价格、服务质量、历史表现等多维度标准
*   **中标条件**: 中标需要满足综合评估的最优条件

## 注意事项 (Notes)
*   **聚合根设计**: 作为聚合根，需要控制对聚合内部对象的访问
*   **状态一致性**: 状态变更需要通过聚合根的方法进行，确保业务规则得到执行
*   **领域事件**: 状态变更时应该发布相应的领域事件，如竞价提交事件、中标事件等
*   **数据完整性**: 使用Bean Validation注解确保数据完整性
*   **索引设计**: 合理设计数据库索引，支持高效的竞价查询和排序
*   **并发控制**: 竞价状态更新需要考虑并发控制，防止状态冲突
*   **软删除**: 建议使用软删除机制，保留竞价历史数据
*   **审计追踪**: 记录竞价数据的变更历史，便于业务分析和争议处理
*   **性能优化**: 大量竞价数据需要考虑分库分表等性能优化策略
*   **安全防护**: 防止恶意竞价和价格操纵行为
