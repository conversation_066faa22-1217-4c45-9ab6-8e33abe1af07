package com.purchase.forwarding.controller;

import com.purchase.common.response.Result;
import com.purchase.common.util.SecurityContextUtil;
import com.purchase.forwarding.dto.ForwarderDashboardStatisticsDTO;
import com.purchase.forwarding.order.vo.ForwarderOrderVO;
import com.purchase.forwarding.service.ForwarderDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 货代仪表盘控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/forwarder/dashboard")
@RequiredArgsConstructor
public class ForwarderDashboardController {
    
    private final ForwarderDashboardService forwarderDashboardService;
    
    /**
     * 获取货代仪表盘统计数据
     *
     * @return 统计数据
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('forwarder')")
    public ResponseEntity<Result<ForwarderDashboardStatisticsDTO>> getDashboardStatistics() {
        try {
            Long currentForwarderId = SecurityContextUtil.getCurrentUserId();
            log.info("获取货代仪表盘统计数据请求, forwarderId: {}", currentForwarderId);
            
            ForwarderDashboardStatisticsDTO statistics = forwarderDashboardService.getDashboardStatistics(currentForwarderId);
            
            return ResponseEntity.ok(Result.success(statistics));
        } catch (Exception e) {
            log.error("获取货代仪表盘统计数据失败", e);
            return ResponseEntity.ok(Result.error("获取统计数据失败"));
        }
    }
    
    /**
     * 获取货代最近订单列表
     *
     * @param limit 限制数量，默认5条，最多20条
     * @return 最近订单列表
     */
    @GetMapping("/recent-orders")
    @PreAuthorize("hasAuthority('forwarder')")
    public ResponseEntity<Result<List<ForwarderOrderVO>>> getRecentOrders(
            @RequestParam(defaultValue = "5") Integer limit) {
        try {
            Long currentForwarderId = SecurityContextUtil.getCurrentUserId();
            log.info("获取货代最近订单列表请求, forwarderId: {}, limit: {}", currentForwarderId, limit);
            
            List<ForwarderOrderVO> recentOrders = forwarderDashboardService.getRecentOrders(currentForwarderId, limit);
            
            return ResponseEntity.ok(Result.success(recentOrders));
        } catch (Exception e) {
            log.error("获取货代最近订单列表失败", e);
            return ResponseEntity.ok(Result.error("获取最近订单失败"));
        }
    }
    
    /**
     * 管理员获取指定货代的仪表盘统计数据
     *
     * @param forwarderId 货代ID
     * @return 统计数据
     */
    @GetMapping("/statistics/{forwarderId}")
    @PreAuthorize("hasAuthority('admin')")
    public ResponseEntity<Result<ForwarderDashboardStatisticsDTO>> getDashboardStatisticsForAdmin(
            @PathVariable Long forwarderId) {
        try {
            log.info("管理员获取货代仪表盘统计数据请求, forwarderId: {}", forwarderId);
            
            ForwarderDashboardStatisticsDTO statistics = forwarderDashboardService.getDashboardStatistics(forwarderId);
            
            return ResponseEntity.ok(Result.success(statistics));
        } catch (Exception e) {
            log.error("管理员获取货代仪表盘统计数据失败, forwarderId: {}", forwarderId, e);
            return ResponseEntity.ok(Result.error("获取统计数据失败"));
        }
    }
    
    /**
     * 管理员获取指定货代的最近订单列表
     *
     * @param forwarderId 货代ID
     * @param limit 限制数量，默认5条，最多20条
     * @return 最近订单列表
     */
    @GetMapping("/recent-orders/{forwarderId}")
    @PreAuthorize("hasAuthority('admin')")
    public ResponseEntity<Result<List<ForwarderOrderVO>>> getRecentOrdersForAdmin(
            @PathVariable Long forwarderId,
            @RequestParam(defaultValue = "5") Integer limit) {
        try {
            log.info("管理员获取货代最近订单列表请求, forwarderId: {}, limit: {}", forwarderId, limit);
            
            List<ForwarderOrderVO> recentOrders = forwarderDashboardService.getRecentOrders(forwarderId, limit);
            
            return ResponseEntity.ok(Result.success(recentOrders));
        } catch (Exception e) {
            log.error("管理员获取货代最近订单列表失败, forwarderId: {}", forwarderId, e);
            return ResponseEntity.ok(Result.error("获取最近订单失败"));
        }
    }
} 