# ForwarderDashboardController.md

## 1. 文件概述

`ForwarderDashboardController.java` 是货代模块中专门为货代用户仪表盘提供数据接口的控制器，位于 `com.purchase.forwarding.controller` 包中。它作为货代仪表盘功能的API入口，负责处理所有与货代统计数据和近期订单查询相关的HTTP请求。该控制器通过依赖注入 `ForwarderDashboardService` 来执行具体的业务逻辑，并利用Spring Security的 `@PreAuthorize` 注解进行严格的权限控制，确保只有货代或管理员才能访问相关数据。

## 2. 核心功能

*   **货代仪表盘统计**: 提供 `/statistics` 接口，允许货代用户获取其业务概览统计数据，如进行中的订单数、待处理的竞价数等。
*   **货代近期订单**: 提供 `/recent-orders` 接口，允许货代用户获取其最近的订单列表，方便快速查看和处理。
*   **管理员视图**: 提供了管理员专用的接口（`/statistics/{forwarderId}` 和 `/recent-orders/{forwarderId}`），允许管理员查询任何指定货代的仪表盘数据，便于平台运营和监控。
*   **权限控制**: 所有接口都通过 `@PreAuthorize` 注解进行严格的权限控制，确保只有授权用户才能访问。
*   **统一响应**: 所有接口都返回一个标准化的 `ResponseEntity<Result<...>>` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **日志记录**: 对所有接收到的请求都记录了详细的日志，包括请求参数，便于系统监控和调试。

## 3. 接口说明

### 3.1 货代用户接口

#### getDashboardStatistics - 获取货代仪表盘统计数据
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarder/dashboard/statistics`
*   **权限**: `hasAuthority('forwarder')`
*   **参数**: 无（货代ID从安全上下文中获取）。
*   **返回值**: `ResponseEntity<Result<ForwarderDashboardStatisticsDTO>>` - 包含货代仪表盘统计数据的响应对象。
*   **业务逻辑**: 获取当前登录货代的ID，然后调用 `forwarderDashboardService.getDashboardStatistics`。

#### getRecentOrders - 获取货代最近订单列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarder/dashboard/recent-orders`
*   **权限**: `hasAuthority('forwarder')`
*   **参数**:
    *   `limit` (Integer, query, optional, default=5): 返回结果的数量限制。
*   **返回值**: `ResponseEntity<Result<List<ForwarderOrderVO>>>` - 包含货代最近订单列表的响应对象。
*   **业务逻辑**: 获取当前登录货代的ID，然后调用 `forwarderDashboardService.getRecentOrders`。

### 3.2 管理员接口

#### getDashboardStatisticsForAdmin - 管理员获取指定货代的仪表盘统计数据
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarder/dashboard/statistics/{forwarderId}`
*   **权限**: `hasAuthority('admin')`
*   **参数**:
    *   `forwarderId` (Long, path, required): 货代ID。
*   **返回值**: `ResponseEntity<Result<ForwarderDashboardStatisticsDTO>>` - 包含指定货代仪表盘统计数据的响应对象。
*   **业务逻辑**: 调用 `forwarderDashboardService.getDashboardStatistics`，传入路径中的 `forwarderId`。

#### getRecentOrdersForAdmin - 管理员获取指定货代的最近订单列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarder/dashboard/recent-orders/{forwarderId}`
*   **权限**: `hasAuthority('admin')`
*   **参数**:
    *   `forwarderId` (Long, path, required): 货代ID。
    *   `limit` (Integer, query, optional, default=5): 返回结果的数量限制。
*   **返回值**: `ResponseEntity<Result<List<ForwarderOrderVO>>>` - 包含指定货代最近订单列表的响应对象。
*   **业务逻辑**: 调用 `forwarderDashboardService.getRecentOrders`，传入路径中的 `forwarderId`。

## 4. 业务规则

*   **权限控制**: 严格区分货代用户和管理员的访问权限。货代只能查看自己的仪表盘数据，而管理员可以查看所有货代的。
*   **数据聚合**: 仪表盘数据通常是聚合计算的结果，可能涉及多个数据源的查询和汇总。
*   **实时性**: 仪表盘数据可能需要较高的实时性，服务层应考虑缓存或优化查询性能。
*   **统一响应**: 所有接口都使用 `ResponseEntity<Result<...>>` 封装响应，提供了统一的错误处理机制。

## 5. 使用示例

```java
// 1. 前端 (React) 货代仪表盘页面加载数据
async function fetchForwarderDashboardData() {
  try {
    const statsResponse = await axios.get('/api/v1/forwarder/dashboard/statistics', {
      headers: { Authorization: `Bearer ${forwarderToken}` }
    });
    const ordersResponse = await axios.get('/api/v1/forwarder/dashboard/recent-orders', {
      params: { limit: 10 },
      headers: { Authorization: `Bearer ${forwarderToken}` }
    });

    console.log('仪表盘统计:', statsResponse.data.data);
    console.log('最近订单:', ordersResponse.data.data);
  } catch (error) {
    console.error('获取仪表盘数据失败:', error.response.data);
  }
}

// 2. 管理员后台查询特定货代仪表盘数据
async function fetchAdminForwarderDashboardData(forwarderId) {
  try {
    const statsResponse = await axios.get(`/api/v1/forwarder/dashboard/statistics/${forwarderId}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    const ordersResponse = await axios.get(`/api/v1/forwarder/dashboard/recent-orders/${forwarderId}`, {
      params: { limit: 5 },
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    console.log(`货代 ${forwarderId} 的仪表盘统计:`, statsResponse.data.data);
    console.log(`货代 ${forwarderId} 的最近订单:`, ordersResponse.data.data);
  } catch (error) {
    console.error('管理员获取仪表盘数据失败:', error.response.data);
  }
}

// 3. 测试示例 (MockMvc)
@SpringBootTest
@AutoConfigureMockMvc
class ForwarderDashboardControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ForwarderDashboardService forwarderDashboardService;

    @BeforeEach
    void setUp() {
        // 模拟 SecurityContextUtil 返回一个货代用户ID
        try (MockedStatic<SecurityContextUtil> mocked = Mockito.mockStatic(SecurityContextUtil.class)) {
            mocked.when(SecurityContextUtil::getCurrentUserId).thenReturn(100L);
            mocked.when(SecurityContextUtil::getCurrentUserRole).thenReturn("forwarder");
            mocked.when(() -> SecurityContextUtil.hasAuthority("forwarder")).thenReturn(true);
            mocked.when(() -> SecurityContextUtil.hasAuthority("admin")).thenReturn(false);
        }
    }

    @Test
    @WithMockUser(authorities = "forwarder")
    void testGetDashboardStatistics_Forwarder() throws Exception {
        ForwarderDashboardStatisticsDTO mockStats = new ForwarderDashboardStatisticsDTO();
        mockStats.setOngoingOrdersCount(5);
        when(forwarderDashboardService.getDashboardStatistics(anyLong())).thenReturn(mockStats);

        mockMvc.perform(get("/api/v1/forwarder/dashboard/statistics"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.ongoingOrdersCount").value(5));
    }

    @Test
    @WithMockUser(authorities = "admin")
    void testGetDashboardStatisticsForAdmin_Success() throws Exception {
        // 模拟 SecurityContextUtil 返回一个管理员用户ID
        try (MockedStatic<SecurityContextUtil> mocked = Mockito.mockStatic(SecurityContextUtil.class)) {
            mocked.when(SecurityContextUtil::getCurrentUserId).thenReturn(1L);
            mocked.when(SecurityContextUtil::getCurrentUserRole).thenReturn("admin");
            mocked.when(() -> SecurityContextUtil.hasAuthority("forwarder")).thenReturn(false);
            mocked.when(() -> SecurityContextUtil.hasAuthority("admin")).thenReturn(true);
        }

        ForwarderDashboardStatisticsDTO mockStats = new ForwarderDashboardStatisticsDTO();
        mockStats.setOngoingOrdersCount(10);
        when(forwarderDashboardService.getDashboardStatistics(eq(200L))).thenReturn(mockStats);

        mockMvc.perform(get("/api/v1/forwarder/dashboard/statistics/200"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.ongoingOrdersCount").value(10));
    }

    @Test
    @WithMockUser(authorities = "buyer") // 非授权用户
    void testGetDashboardStatistics_AccessDenied() throws Exception {
        mockMvc.perform(get("/api/v1/forwarder/dashboard/statistics"))
                .andExpect(status().isForbidden());
    }
}
```

## 6. 注意事项

*   **权限控制**: 所有接口都通过 `@PreAuthorize` 进行严格的权限控制，确保只有授权用户才能访问。这是后台管理系统安全的关键。
*   **日志记录**: 控制器中使用了 `log.info` 记录请求参数，这对于监控和问题排查非常重要。
*   **统一响应**: 所有接口都返回 `ResponseEntity<Result<...>>` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **职责分离**: 控制器仅负责请求的接收、参数的初步校验和转发，具体的业务逻辑和数据聚合委托给 `ForwarderDashboardService`。
*   **安全上下文**: 控制器通过 `SecurityContextUtil.getCurrentUserId()` 获取当前登录用户ID，确保操作的归属和权限。
*   **性能**: 仪表盘数据通常是聚合计算的结果，其生成可能涉及复杂的查询。服务层应考虑缓存或优化查询性能，以保证仪表盘的快速加载。
*   **参数默认值**: `limit` 参数设置了默认值，提高了接口的易用性。