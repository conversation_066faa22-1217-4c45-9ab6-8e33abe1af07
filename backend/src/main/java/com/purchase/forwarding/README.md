# Forwarding 转发模块文档

## 模块概述

转发模块是采购系统的重要业务模块，专门处理货代转发业务。该模块支持多种角色（买家、卖家、货代、管理员）创建转发需求，货代可以对需求进行竞价投标，提供完整的物流运输解决方案。模块涵盖了从需求发布、竞价投标到订单执行的完整业务流程。

## 目录结构概览

```
com.purchase.forwarding/
├── controller/                    # 主控制器
│   └── ForwarderDashboardController.java    # 货代仪表板控制器
├── dto/                          # 主DTO
│   └── ForwarderDashboardStatisticsDTO.java # 货代仪表板统计DTO
├── service/                      # 主服务
│   ├── ForwarderDashboardService.java       # 货代仪表板服务
│   └── impl/                               # 服务实现
├── requirement/                  # 转发需求子模块
│   ├── controller/                         # 需求控制器
│   ├── dto/                               # 需求DTO
│   ├── entity/
│   │   └── ForwardingRequirement.java     # 转发需求实体
│   ├── mapper/                            # 需求数据访问层
│   ├── service/                           # 需求服务层
│   └── vo/                                # 需求视图对象
├── bidding/                      # 转发竞价子模块
│   ├── controller/                         # 竞价控制器
│   ├── dto/                               # 竞价DTO
│   ├── entity/
│   │   ├── ForwardingBidding.java         # 转发竞价实体
│   │   ├── ShippingStatus.java            # 运输状态实体
│   │   └── ShippingStatusCode.java        # 运输状态代码实体
│   ├── mapper/                            # 竞价数据访问层
│   ├── service/                           # 竞价服务层
│   └── vo/                                # 竞价视图对象
└── order/                        # 转发订单子模块
    ├── controller/                         # 订单控制器
    ├── dto/                               # 订单DTO
    ├── entity/                            # 订单实体
    ├── mapper/                            # 订单数据访问层
    ├── service/                           # 订单服务层
    └── vo/                                # 订单视图对象
```

## 核心功能详述

### 1. 转发需求管理 (Requirement)

#### 主要功能
- **需求创建**: 多角色可创建转发需求
- **需求管理**: 需求的查询、更新、状态管理
- **货物信息**: 详细的货物描述和物理属性
- **运输要求**: 港口、路线、时间等运输配置
- **增值服务**: 报关、保险、认证等服务需求

#### 核心文件
- `ForwardingRequirement.java`: 转发需求实体
- 支持多种运输类型（LCL拼柜、FCL整柜）
- 完整的港口和贸易条款信息
- 灵活的货物分类和特殊要求

#### 业务流程
1. **需求发布**: 用户创建转发需求 → 待竞价状态
2. **需求展示**: 货代查看可竞价需求
3. **需求管理**: 需求状态跟踪和更新
4. **需求完成**: 选择中标货代后需求完成

### 2. 转发竞价管理 (Bidding)

#### 主要功能
- **竞价投标**: 货代对需求进行竞价
- **费用明细**: 详细的运输费用分解
- **服务方案**: 完整的物流解决方案
- **竞价审核**: 管理员审核竞价合规性
- **中标管理**: 选择最优竞价方案

#### 核心文件
- `ForwardingBidding.java`: 转发竞价实体
- `ShippingStatus.java`: 运输状态实体
- `ShippingStatusCode.java`: 运输状态代码实体

#### 费用结构
- **出口费用**: 包装、内陆运输、报关、操作费
- **运输费用**: 主运费、附加费、保险费
- **目的地费用**: 操作费、卸货费、内陆运输
- **进口费用**: 报关费、关税（DDP条款）
- **服务费用**: 货代服务费、自定义费用

#### 业务流程
1. **竞价提交**: 货代提交竞价方案 → 待审核状态
2. **竞价审核**: 管理员审核竞价 → 审核通过/拒绝
3. **竞价选择**: 需求方选择最优方案 → 中标
4. **订单生成**: 中标后生成转发订单

### 3. 转发订单管理 (Order)

#### 主要功能
- **订单创建**: 基于中标竞价创建订单
- **订单执行**: 跟踪订单执行进度
- **状态管理**: 完整的订单状态流转
- **文档管理**: 运输相关文档处理

#### 业务流程
1. **订单创建**: 中标后自动创建转发订单
2. **订单确认**: 双方确认订单详情
3. **执行跟踪**: 跟踪货物运输状态
4. **订单完成**: 货物送达后订单完成

### 4. 货代仪表板 (Dashboard)

#### 主要功能
- **数据统计**: 货代业务数据统计
- **业务概览**: 需求、竞价、订单概览
- **性能分析**: 货代服务质量分析
- **趋势展示**: 业务发展趋势

#### 核心文件
- `ForwarderDashboardController.java`: 仪表板控制器
- `ForwarderDashboardService.java`: 仪表板服务
- `ForwarderDashboardStatisticsDTO.java`: 统计数据DTO

## 数据模型说明

### ForwardingRequirement 转发需求

#### 核心字段
- **基础信息**: id, orderId, createdBy, creatorRole, title
- **货物信息**: productName, hsCode, cargoType, cargoWeight, cargoVolume
- **地址信息**: originCountry, originAddress, destinationCountry, destinationAddress
- **港口信息**: portOfLoadingName, portOfLoadingCode, portOfDestinationName
- **运输配置**: shipmentType, containerSize, containerQty, deliveryTerms
- **服务需求**: needCertification, needFumigation, customsService, insuranceIncluded

### ForwardingBidding 转发竞价

#### 核心字段
- **竞价信息**: forwardingRequirementId, forwarderId, biddingPrice, currency
- **运输方案**: shippingMethod, shippingRoute, estimatedDays
- **费用明细**: 详细的费用分解字段
- **服务配置**: insuranceIncluded, customsService, deliveryTerms
- **状态管理**: status, auditStatus, winner
- **跟踪信息**: trackingNumber, trackingUrl

## API 接口概览

### 转发需求接口
- `POST /api/v1/forwarding/requirements` - 创建转发需求
- `GET /api/v1/forwarding/requirements` - 获取需求列表
- `GET /api/v1/forwarding/requirements/{id}` - 获取需求详情
- `PUT /api/v1/forwarding/requirements/{id}` - 更新需求
- `PUT /api/v1/forwarding/requirements/{id}/status` - 更新需求状态

### 转发竞价接口
- `POST /api/v1/forwarding/biddings` - 提交竞价
- `GET /api/v1/forwarding/biddings` - 获取竞价列表
- `GET /api/v1/forwarding/biddings/{id}` - 获取竞价详情
- `PUT /api/v1/forwarding/biddings/{id}` - 更新竞价
- `PUT /api/v1/forwarding/biddings/{id}/audit` - 审核竞价
- `PUT /api/v1/forwarding/biddings/{id}/select` - 选择中标竞价

### 转发订单接口
- `GET /api/v1/forwarding/orders` - 获取订单列表
- `GET /api/v1/forwarding/orders/{id}` - 获取订单详情
- `PUT /api/v1/forwarding/orders/{id}/status` - 更新订单状态
- `PUT /api/v1/forwarding/orders/{id}/tracking` - 更新跟踪信息

### 货代仪表板接口
- `GET /api/v1/forwarding/dashboard/statistics` - 获取统计数据
- `GET /api/v1/forwarding/dashboard/overview` - 获取业务概览

## 业务规则

### 转发需求规则
1. 多种角色可以创建转发需求
2. 需求创建后处于待竞价状态
3. 需求必须包含完整的货物和运输信息
4. 选择中标货代后需求状态变为已完成

### 转发竞价规则
1. 只有货代可以提交竞价
2. 竞价需要审核通过才能参与选择
3. 每个需求可以有多个竞价
4. 选择中标者后其他竞价自动失效

### 费用计算规则
1. 根据贸易条款确定费用承担方
2. 提供详细的费用分解便于比较
3. 支持多种货币计价
4. 自定义费用项目灵活配置

### 权限规则
1. 需求创建者可以管理自己的需求
2. 货代只能查看和竞价公开需求
3. 管理员可以审核和管理所有竞价
4. 敏感信息需要权限控制

## 集成说明

### 与其他模块的关系
- **订单模块**: 转发需求可能基于采购订单创建
- **用户模块**: 关联买家、卖家、货代用户信息
- **消息模块**: 发送转发相关通知
- **佣金模块**: 转发完成后计算货代佣金

### 事件发布
- 转发需求创建事件
- 转发竞价提交事件
- 竞价审核完成事件
- 中标选择事件
- 订单状态变更事件

## 注意事项

### 开发注意事项
1. **数据一致性**: 需求、竞价、订单数据保持一致
2. **状态管理**: 严格按照状态流转规则
3. **权限控制**: 每个操作都要验证权限
4. **费用计算**: 确保费用计算的准确性

### 性能优化
1. **分页查询**: 所有列表查询都支持分页
2. **索引优化**: 为查询字段建立索引
3. **缓存策略**: 缓存频繁查询的数据
4. **批量操作**: 大量数据使用批量处理

### 安全考虑
1. **数据验证**: 严格验证所有输入数据
2. **权限控制**: 基于角色的访问控制
3. **敏感信息**: 保护商业敏感信息
4. **操作日志**: 记录重要操作

### 国际化支持
1. **多货币**: 支持多种货币计价
2. **港口代码**: 使用国际标准港口代码
3. **贸易条款**: 支持国际贸易条款
4. **文档标准**: 符合国际物流文档标准
