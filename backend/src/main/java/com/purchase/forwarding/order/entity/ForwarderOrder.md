# ForwarderOrder.md

## 1. 文件概述

`ForwarderOrder` 是货代模块中的一个实体（Entity），位于 `com.purchase.forwarding.order.entity` 包中。它直接映射到数据库的 `forwarder_order` 表，用于持久化存储货代订单的详细信息。这个实体包含了货代订单从创建到完成的整个生命周期中的所有关键属性，包括关联的需求和竞价、货代和创建者信息、货物详情、物流信息、费用明细以及订单状态等。它是货代订单业务流程的核心数据载体，通过MyBatis-Plus的注解实现了与数据库表的自动化ORM映射。

## 2. 核心功能

*   **数据持久化**: 定义了货代订单在数据库中的存储结构，是货代订单领域模型在持久化层的具体表现。
*   **多业务关联**: 通过 `requirementId`, `biddingId`, `purchaseOrderId` 字段，实现了货代订单与货代需求、竞价和采购订单等多个业务实体的关联，便于追溯和查询。
*   **详细物流信息**: 包含了丰富的物流相关字段，如起运港、目的港、船名/航班号、预计/实际时间、提单号、跟踪号等，支持全面的物流跟踪。
*   **费用明细**: 提供了详细的费用明细字段，涵盖了出口包装费、内陆运输费、清关费、主运费、保险费等，便于财务结算和成本分析。
*   **状态管理**: 包含了 `orderStatus` 和 `paymentStatus` 字段，支持货代订单的完整生命周期管理和业务流程控制。
*   **MyBatis-Plus集成**: 通过 `@TableName`, `@TableId`, `@TableField`, `@TableLogic`, `@Builder`, `@NoArgsConstructor`, `@AllArgsConstructor` 等注解与MyBatis-Plus框架深度集成，实现了主键生成、字段映射、逻辑删除和自动填充时间戳等功能，极大地简化了数据库操作。
*   **逻辑删除**: `deleted` 字段配合 `@TableLogic` 注解实现了逻辑删除功能，确保数据不会被物理删除，便于数据恢复和审计。

## 3. 属性说明

### 3.1 订单基本信息

- **`id` (Long)**: 主键ID，使用雪花算法生成。
- **`orderNumber` (String)**: 订单编号。
- **`requirementId` (Long)**: 货代需求ID。
- **`biddingId` (Long)**: 竞价ID。
- **`purchaseOrderId` (Long)**: 采购订单ID。

### 3.2 参与方信息

- **`forwarderId` (Long)**: 货代ID。
- **`forwarderName` (String)**: 货代用户名。
- **`forwarderCompanyName` (String)**: 货代公司名称。
- **`creatorId` (Long)**: 创建者ID。
- **`creatorName` (String)**: 创建者名称。
- **`creatorRole` (String)**: 创建者角色。

### 3.3 货物与需求信息

- **`title` (String)**: 需求标题。
- **`productName` (String)**: 产品名称。
- **`hsCode` (String)**: HS编码。
- **`destinationCountry` (String)**: 目的国家。
- **`destinationAddress` (String)**: 目的地址。
- **`contactPerson` (String)**: 联系人。
- **`contactPhone` (String)**: 联系电话。
- **`expectedDeliveryDate` (LocalDate)**: 预期交付日期。
- **`description` (String)**: 需求描述。
- **`cargoType` (String)**: 货物类型。
- **`cargoWeight` (BigDecimal)**: 货物重量(kg)。
- **`cargoVolume` (BigDecimal)**: 货物体积(m³)。
- **`packageCount` (Integer)**: 包装数量。
- **`specialRequirements` (String)**: 特殊要求。
- **`documents` (String)**: 文档(JSON格式或逗号分隔的URL)。
- **`productImages` (String)**: 产品图片(JSON格式或逗号分隔的URL)。
- **`productVideos` (String)**: 产品视频(JSON格式或逗号分隔的URL)。

### 3.4 物流与运输信息

- **`portOfLoadingName` (String)**: 起运港名称。
- **`portOfLoadingCode` (String)**: 起运港代码。
- **`portOfDestinationName` (String)**: 目的港名称。
- **`portOfDestinationCode` (String)**: 目的港代码。
- **`needCertification` (String)**: 需要认证(1是0否)。
- **`needFumigation` (String)**: 需要熏蒸(1是0否)。
- **`shipmentType` (String)**: 装运类型(FCL整柜/LCL拼箱)。
- **`containerSize` (String)**: 集装箱尺寸。
- **`containerQty` (Integer)**: 集装箱数量。
- **`deliveryTerms` (String)**: 交付条款(FOB/CIF/EXW等)。
- **`vesselName` (String)**: 船名/航班号。
- **`voyageNumber` (String)**: 航次号。
- **`etd` (LocalDateTime)**: 预计离港时间(ETD)。
- **`eta` (LocalDateTime)**: 预计到港时间(ETA)。
- **`billOfLadingNumber` (String)**: 提单号。
- **`billOfLadingType` (String)**: 提单类型(电放/正本)。
- **`trackingNumber` (String)**: 物流跟踪号。
- **`trackingUrl` (String)**: 物流跟踪URL。
- **`estimatedPickupDate` (LocalDateTime)**: 预计提货日期。
- **`estimatedDeliveryDate` (LocalDateTime)**: 预计送达日期。
- **`actualPickupDate` (LocalDateTime)**: 实际提货日期。
- **`actualDeliveryDate` (LocalDateTime)**: 实际送达日期。

### 3.5 竞价与费用信息

- **`biddingPrice` (BigDecimal)**: 报价金额。
- **`currency` (String)**: 货币单位。
- **`shippingMethod` (String)**: 运输方式(SEA/AIR/RAIL/ROAD)。
- **`shippingRoute` (String)**: 运输路线。
- **`estimatedDays` (Integer)**: 预计运输天数。
- **`biddingDescription` (String)**: 竞价描述。
- **`insuranceIncluded` (Boolean)**: 是否包含保险。
- **`insuranceAmount` (BigDecimal)**: 保险金额。
- **`customsService` (Boolean)**: 是否提供清关服务。

### 3.6 订单状态与时间

- **`orderStatus` (String)**: 订单状态。
- **`paymentStatus` (String)**: 支付状态。
- **`totalPrice` (BigDecimal)**: 订单总金额。
- **`paymentTime` (LocalDateTime)**: 支付时间。
- **`completionTime` (LocalDateTime)**: 完成时间。
- **`remarks` (String)**: 备注。

### 3.7 费用明细字段

- **`costExportPacking` (BigDecimal)**: 出口包装费。
- **`costOriginInlandHaulage` (BigDecimal)**: 起运地内陆运输费。
- **`costExportCustomsDocs` (BigDecimal)**: 出口报关及单证费。
- **`costOriginHandling` (BigDecimal)**: 起运地操作费。
- **`costMainFreight` (BigDecimal)**: 主运费。
- **`costFreightSurcharges` (BigDecimal)**: 运费附加费。
- **`costCargoInsurance` (BigDecimal)**: 货物运输保险费。
- **`costDestinationHandlingBySeller` (BigDecimal)**: 目的地操作费 (卖方承担)。
- **`costDestinationUnloadingBySeller` (BigDecimal)**: 目的地卸货费 (卖方承担)。
- **`costDestinationInlandHaulageBySeller` (BigDecimal)**: 目的地内陆运输费 (卖方承担)。
- **`costImportCustomsDocsBySeller` (BigDecimal)**: 进口报关及单证费 (卖方承担-DDP)。
- **`costImportDutiesTaxesBySeller` (BigDecimal)**: 进口关税及税费 (卖方承担-DDP)。
- **`costForwarderServiceFee` (BigDecimal)**: 货代服务费/操作费 (卖方承担)。
- **`customedCosts` (String)**: 自定义费用项目 JSON数组。

### 3.8 系统字段

- **`deleted` (String)**: 逻辑删除标志（`0`-未删除，`1`-已删除）。
- **`createdAt` (LocalDateTime)**: 创建时间。
- **`updatedAt` (LocalDateTime)**: 更新时间。

### 3.9 内部常量类

- **`OrderStatus`**: 定义了订单状态常量（`PENDING`, `PROCESSING`, `COMPLETED`, `CANCELLED`）。
- **`PaymentStatus`**: 定义了支付状态常量（`UNPAID`, `PAID`, `REFUNDED`）。

## 4. 业务规则

*   **多关联**: `ForwarderOrder` 与 `ForwardingRequirement`, `ForwardingBidding`, `UnifiedOrder` 等多个业务实体通过ID进行关联，形成复杂的业务网络。
*   **状态流转**: `orderStatus` 和 `paymentStatus` 字段的更新应遵循预定义的状态机规则，确保订单流程的正确性。
*   **费用明细**: 详细的费用字段支持复杂的物流成本核算和分摊。
*   **逻辑删除**: `deleted` 字段配合 `@TableLogic` 注解，实现了数据的逻辑删除，查询时会自动过滤已删除的记录。
*   **数据类型**: `BigDecimal` 用于金额和重量/体积计算，以避免浮点数精度问题。`LocalDate` 和 `LocalDateTime` 用于日期时间字段，确保了时间的精确性和时区处理的便利性。
*   **JSON字段**: `documents`, `productImages`, `productVideos`, `customedCosts` 字段存储JSON字符串，这意味着在业务逻辑中需要进行JSON的序列化和反序列化。
*   **主键生成**: `@TableId(type = IdType.INPUT)` 表示主键ID由业务逻辑层（通常是雪花算法）生成并传入，而不是由数据库自增长。

## 5. 使用示例

```java
// 1. 在 ForwarderOrderMapper 接口中定义对 ForwarderOrder 的操作
@Mapper
public interface ForwarderOrderMapper extends BaseMapper<ForwarderOrder> {
    // 继承 BaseMapper 提供了基本的 CRUD 方法
    // 也可以在此定义自定义查询方法，例如：
    @Select("SELECT * FROM forwarder_order WHERE order_number = #{orderNumber} AND deleted = '0'")
    ForwarderOrder findByOrderNumber(@Param("orderNumber") String orderNumber);

    @Select("SELECT * FROM forwarder_order WHERE forwarder_id = #{forwarderId} AND deleted = '0' ORDER BY created_at DESC")
    List<ForwarderOrder> findByForwarderId(@Param("forwarderId") Long forwarderId);
}

// 2. 在 ForwarderOrderService 实现中创建和更新 ForwarderOrder
@Service
public class ForwarderOrderServiceImpl extends ServiceImpl<ForwarderOrderMapper, ForwarderOrder>
        implements ForwarderOrderService {
    @Autowired
    private SnowflakeIdGenerator idGenerator;

    @Transactional
    public ForwarderOrderVO createOrder(CreateForwarderOrderRequest request) {
        ForwarderOrder order = new ForwarderOrder();
        order.setId(idGenerator.nextId()); // 使用雪花算法生成ID
        order.setOrderNumber("FWD" + System.currentTimeMillis()); // 生成订单号
        // ... 将 request 中的数据复制到 order ...
        order.setOrderStatus(ForwarderOrder.OrderStatus.PENDING); // 默认待处理
        order.setPaymentStatus(ForwarderOrder.PaymentStatus.UNPAID); // 默认未支付
        order.setDeleted("0");

        save(order); // 调用ServiceImpl的save方法
        return convertToVO(order);
    }

    @Transactional
    public ForwarderOrderVO updateOrder(UpdateForwarderOrderRequest request) {
        ForwarderOrder existingOrder = getById(request.getId());
        if (existingOrder == null || "1".equals(existingOrder.getDeleted())) {
            throw new BusinessException("货代订单不存在");
        }
        // ... 将 request 中的可更新数据复制到 existingOrder ...
        existingOrder.setOrderStatus(request.getOrderStatus());
        existingOrder.setTotalPrice(request.getTotalPrice());
        // ... 更新其他字段 ...
        updateById(existingOrder); // 调用ServiceImpl的updateById方法
        return convertToVO(existingOrder);
    }
}

// 3. 测试示例
@SpringBootTest
class ForwarderOrderTest {
    @Autowired
    private ForwarderOrderMapper forwarderOrderMapper;

    @Test
    @Transactional
    void testInsertAndRetrieve() {
        ForwarderOrder order = ForwarderOrder.builder()
            .id(12345L)
            .orderNumber("TEST-FWO-001")
            .requirementId(1L)
            .forwarderId(10L)
            .creatorId(20L)
            .productName("测试货物")
            .cargoWeight(new BigDecimal("100.50"))
            .orderStatus(ForwarderOrder.OrderStatus.PENDING)
            .paymentStatus(ForwarderOrder.PaymentStatus.UNPAID)
            .deleted("0")
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        int result = forwarderOrderMapper.insert(order);
        assertThat(result).isEqualTo(1);

        ForwarderOrder retrievedOrder = forwarderOrderMapper.selectById(order.getId());
        assertThat(retrievedOrder).isNotNull();
        assertThat(retrievedOrder.getOrderNumber()).isEqualTo("TEST-FWO-001");
        assertThat(retrievedOrder.getProductName()).isEqualTo("测试货物");
    }

    @Test
    @Transactional
    void testLogicalDelete() {
        ForwarderOrder order = ForwarderOrder.builder()
            .id(12346L)
            .orderNumber("TEST-FWO-002")
            .requirementId(2L)
            .forwarderId(11L)
            .creatorId(21L)
            .productName("另一个货物")
            .orderStatus(ForwarderOrder.OrderStatus.PENDING)
            .paymentStatus(ForwarderOrder.PaymentStatus.UNPAID)
            .deleted("0")
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();
        forwarderOrderMapper.insert(order);

        // 执行逻辑删除
        forwarderOrderMapper.deleteById(order.getId());

        // 再次查询，应该找不到（因为逻辑删除）
        ForwarderOrder deletedOrder = forwarderOrderMapper.selectById(order.getId());
        assertThat(deletedOrder).isNull();
    }
}
```

## 6. 注意事项

*   **领域实体**: `ForwarderOrder` 是一个典型的领域实体，它包含了数据和行为，并且其行为（如状态变更）直接反映了业务概念。
*   **MyBatis-Plus集成**: 充分利用MyBatis-Plus的注解和功能，可以大大简化数据访问层的开发。特别是 `@TableLogic` 对于逻辑删除的实现非常方便。
*   **Lombok注解**: `@Data`, `@Builder`, `@NoArgsConstructor`, `@AllArgsConstructor` 极大地简化了实体类的编写，减少了样板代码。
*   **主键生成**: `@TableId(type = IdType.INPUT)` 表明ID由外部（如雪花算法）生成。确保ID生成策略的唯一性和高性能。
*   **状态管理**: 实体内部的 `OrderStatus` 和 `PaymentStatus` 常量类定义了订单和支付的状态，这是一种良好的实践，可以避免使用魔法字符串，提高代码可读性和可维护性。
*   **多关联ID**: 实体中包含多个关联ID（`requirementId`, `biddingId`, `purchaseOrderId`），这表明货代订单与多个业务流程相关联。在业务逻辑中需要明确其主次关系和数据一致性。
*   **数据类型**: `BigDecimal` 用于金额和重量/体积计算，以避免浮点数精度问题。`LocalDate` 和 `LocalDateTime` 用于日期时间字段，确保了时间的精确性和时区处理的便利性。
*   **JSON字段**: `documents`, `productImages`, `productVideos`, `customedCosts` 字段存储JSON字符串，这意味着在业务逻辑中需要进行JSON的序列化和反序列化。
*   **可扩展性**: 如果未来需要增加更多货代订单相关的属性，可以在实体中添加相应的字段。