package com.purchase.forwarding.order.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 更新货代订单请求
 */
@Data
public class UpdateForwarderOrderRequest {
    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long id;
    
    /**
     * 采购订单ID - 关联的采购订单
     */
    private Long purchaseOrderId;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * HS编码
     */
    private String hsCode;
    
    /**
     * 货物类型
     */
    private String cargoType;
    
    /**
     * 货物重量(kg)
     */
    private BigDecimal cargoWeight;
    
    /**
     * 货物体积(m³)
     */
    private BigDecimal cargoVolume;
    
    /**
     * 包装数量
     */
    private Integer packageCount;
    
    /**
     * 特殊要求
     */
    private String specialRequirements;
    
    /**
     * 起运港名称
     */
    private String portOfLoadingName;
    
    /**
     * 起运港代码
     */
    private String portOfLoadingCode;
    
    /**
     * 目的港名称
     */
    private String portOfDestinationName;
    
    /**
     * 目的港代码
     */
    private String portOfDestinationCode;
    
    /**
     * 交付条款(FOB/CIF/EXW等)
     */
    private String deliveryTerms;
    
    /**
     * 船名/航班号
     */
    private String vesselName;
    
    /**
     * 航次号
     */
    private String voyageNumber;
    
    /**
     * 预计离港时间(ETD)
     */
    private LocalDateTime etd;
    
    /**
     * 预计到港时间(ETA)
     */
    private LocalDateTime eta;
    
    /**
     * 提单号
     */
    private String billOfLadingNumber;
    
    /**
     * 提单类型(电放/正本)
     */
    private String billOfLadingType;
    
    /**
     * 货币单位
     */
    private String currency;
    
    /**
     * 运输方式(SEA/AIR/RAIL/ROAD)
     */
    private String shippingMethod;
    
    /**
     * 运输路线
     */
    private String shippingRoute;
    
    /**
     * 预计运输天数
     */
    private Integer estimatedDays;
    
    /**
     * 是否包含保险
     */
    private Boolean insuranceIncluded;
    
    /**
     * 保险金额
     */
    private BigDecimal insuranceAmount;
    
    /**
     * 是否提供清关服务
     */
    private Boolean customsService;
    
    /**
     * 物流跟踪号
     */
    private String trackingNumber;
    
    /**
     * 物流跟踪URL
     */
    private String trackingUrl;
    
    /**
     * 预计提货日期
     */
    private LocalDateTime estimatedPickupDate;
    
    /**
     * 预计送达日期
     */
    private LocalDateTime estimatedDeliveryDate;
    
    /**
     * 实际提货日期
     */
    private LocalDateTime actualPickupDate;
    
    /**
     * 实际送达日期
     */
    private LocalDateTime actualDeliveryDate;
    
    /**
     * 订单状态
     */
    private String orderStatus;
    
    /**
     * 支付状态
     */
    private String paymentStatus;
    
    /**
     * 订单总金额
     */
    private BigDecimal totalPrice;
    
    /**
     * 备注
     */
    private String remarks;
    
    // ========== 费用明细字段 ==========
    
    /**
     * 出口包装费 (卖方承担)
     */
    private BigDecimal costExportPacking;
    
    /**
     * 起运地内陆运输费 (卖方承担)
     */
    private BigDecimal costOriginInlandHaulage;
    
    /**
     * 出口报关及单证费 (卖方承担)
     */
    private BigDecimal costExportCustomsDocs;
    
    /**
     * 起运地操作费 (卖方承担)
     */
    private BigDecimal costOriginHandling;
    
    /**
     * 主运费 (卖方承担部分)
     */
    private BigDecimal costMainFreight;
    
    /**
     * 运费附加费 (卖方承担部分)
     */
    private BigDecimal costFreightSurcharges;
    
    /**
     * 货物运输保险费 (卖方支付的保费)
     */
    private BigDecimal costCargoInsurance;
    
    /**
     * 目的地操作费 (卖方承担)
     */
    private BigDecimal costDestinationHandlingBySeller;
    
    /**
     * 目的地卸货费 (卖方承担)
     */
    private BigDecimal costDestinationUnloadingBySeller;
    
    /**
     * 目的地内陆运输费 (卖方承担)
     */
    private BigDecimal costDestinationInlandHaulageBySeller;
    
    /**
     * 进口报关及单证费 (卖方承担-DDP)
     */
    private BigDecimal costImportCustomsDocsBySeller;
    
    /**
     * 进口关税及税费 (卖方承担-DDP)
     */
    private BigDecimal costImportDutiesTaxesBySeller;
    
    /**
     * 货代服务费/操作费 (卖方承担)
     */
    private BigDecimal costForwarderServiceFee;
    
    /**
     * 自定义费用项目 JSON数组
     * 格式: [{"name":"费用名称","amount":金额,"description":"费用说明","currency":"货币"}]
     */
    private String customedCosts;
} 