package com.purchase.forwarding.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.common.exception.BusinessException;
import com.purchase.common.response.Result;
import com.purchase.common.util.SecurityContextUtil;
import com.purchase.forwarding.order.dto.CreateForwarderOrderRequest;
import com.purchase.forwarding.order.dto.UpdateForwarderOrderRequest;
import com.purchase.forwarding.order.service.ForwarderOrderService;
import com.purchase.forwarding.order.vo.ForwarderOrderVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.Optional;

/**
 * 货代订单控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/forwarder/orders")
@RequiredArgsConstructor
public class ForwarderOrderController {
    
    private final ForwarderOrderService forwarderOrderService;
    
    /**
     * 创建货代订单
     *
     * @param request 创建请求
     * @return 订单信息
     */
    @PostMapping
    @PreAuthorize("hasAnyAuthority('forwarder', 'admin', 'seller', 'buyer')")
    public Result<ForwarderOrderVO> createOrder(@RequestBody @Validated CreateForwarderOrderRequest request) {
        try {
            log.info("创建货代订单请求: {}", request);
            ForwarderOrderVO order = forwarderOrderService.createOrder(request);
            log.info("货代订单创建成功, 订单号: {}", order.getOrderNumber());
            return Result.success(order);
        } catch (BusinessException e) {
            log.error("创建货代订单失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("创建货代订单时发生未知异常", e);
            return Result.error("创建订单时发生系统错误");
        }
    }
    
    /**
     * 获取货代订单列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param forwarderId 货代ID (可选)
     * @param creatorId 创建者ID (可选)
     * @param status 订单状态 (可选)
     * @return 订单列表
     */
    @GetMapping
    @PreAuthorize("hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')")
    public Result<IPage<ForwarderOrderVO>> getOrderList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Long forwarderId,
            @RequestParam(required = false) Long creatorId,
            @RequestParam(required = false) String status) {
        
        // 参数校验
        if (page < 1) {
            return Result.error("页码必须大于0");
        }
        if (size < 1 || size > 100) {
            return Result.error("每页大小必须在1-100之间");
        }
        
        try {
            IPage<ForwarderOrderVO> orderPage = forwarderOrderService.getOrderPage(page, size, forwarderId, creatorId, status);
            return Result.success(orderPage);
        } catch (Exception e) {
            log.error("获取货代订单列表失败", e);
            return Result.error("获取订单列表失败");
        }
    }
    
    /**
     * 获取当前登录货代的订单列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param status 订单状态 (可选)
     * @return 订单列表
     */
    @GetMapping("/my")
    @PreAuthorize("hasAuthority('forwarder')")
    public Result<IPage<ForwarderOrderVO>> getMyOrders(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {
        
        // 参数校验
        if (page < 1) {
            return Result.error("页码必须大于0");
        }
        if (size < 1 || size > 100) {
            return Result.error("每页大小必须在1-100之间");
        }
        
        try {
            // 获取当前登录用户ID作为货代ID
            Long currentForwarderId = SecurityContextUtil.getCurrentUserId();
            IPage<ForwarderOrderVO> orderPage = forwarderOrderService.getOrderPage(page, size, currentForwarderId, null, status);
            return Result.success(orderPage);
        } catch (Exception e) {
            log.error("获取当前货代订单列表失败", e);
            return Result.error("获取订单列表失败");
        }
    }
    
    /**
     * 获取当前登录用户创建的订单列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param status 订单状态 (可选)
     * @return 订单列表
     */
    @GetMapping("/created")
    @PreAuthorize("hasAnyAuthority('buyer', 'seller', 'admin')")
    public Result<IPage<ForwarderOrderVO>> getMyCreatedOrders(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {
        
        // 参数校验
        if (page < 1) {
            return Result.error("页码必须大于0");
        }
        if (size < 1 || size > 100) {
            return Result.error("每页大小必须在1-100之间");
        }
        
        try {
            // 获取当前登录用户ID作为创建者ID
            Long currentUserId = SecurityContextUtil.getCurrentUserId();
            IPage<ForwarderOrderVO> orderPage = forwarderOrderService.getOrderPage(page, size, null, currentUserId, status);
            return Result.success(orderPage);
        } catch (Exception e) {
            log.error("获取当前用户创建的货代订单列表失败", e);
            return Result.error("获取订单列表失败");
        }
    }
    
    /**
     * 获取货代订单详情
     *
     * @param id 订单ID
     * @return 订单详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')")
    public Result<ForwarderOrderVO> getOrderById(@PathVariable Long id) {
        if (id == null) {
            return Result.error("订单ID不能为空");
        }
        
        try {
            ForwarderOrderVO order = forwarderOrderService.getOrderById(id);
            return Optional.ofNullable(order)
                    .map(Result::success)
                    .orElseGet(() -> Result.error("订单不存在"));
        } catch (Exception e) {
            log.error("获取货代订单详情失败, 订单ID: {}", id, e);
            return Result.error("获取订单详情失败");
        }
    }
    
    /**
     * 获取订单号对应的订单
     *
     * @param orderNumber 订单编号
     * @return 订单详情
     */
    @GetMapping("/number/{orderNumber}")
    @PreAuthorize("hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')")
    public Result<ForwarderOrderVO> getOrderByNumber(@PathVariable String orderNumber) {
        if (!StringUtils.hasText(orderNumber)) {
            return Result.error("订单编号不能为空");
        }
        
        try {
            ForwarderOrderVO order = forwarderOrderService.getOrderByNumber(orderNumber);
            return Optional.ofNullable(order)
                    .map(Result::success)
                    .orElseGet(() -> Result.error("订单不存在"));
        } catch (Exception e) {
            log.error("根据订单编号获取订单失败, 订单编号: {}", orderNumber, e);
            return Result.error("获取订单详情失败");
        }
    }
    
    /**
     * 根据需求ID获取订单
     *
     * @param requirementId 需求ID
     * @return 订单详情
     */
    @GetMapping("/requirement/{requirementId}")
    @PreAuthorize("hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')")
    public Result<ForwarderOrderVO> getOrderByRequirementId(@PathVariable Long requirementId) {
        if (requirementId == null) {
            return Result.error("需求ID不能为空");
        }
        
        try {
            ForwarderOrderVO order = forwarderOrderService.getOrderByRequirementId(requirementId);
            return Optional.ofNullable(order)
                    .map(Result::success)
                    .orElseGet(() -> Result.error("未找到与该需求关联的订单"));
        } catch (Exception e) {
            log.error("根据需求ID获取订单失败, 需求ID: {}", requirementId, e);
            return Result.error("获取订单详情失败");
        }
    }
    
    /**
     * 根据竞价ID获取订单
     *
     * @param biddingId 竞价ID
     * @return 订单详情
     */
    @GetMapping("/bidding/{biddingId}")
    @PreAuthorize("hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')")
    public Result<ForwarderOrderVO> getOrderByBiddingId(@PathVariable Long biddingId) {
        if (biddingId == null) {
            return Result.error("竞价ID不能为空");
        }
        
        try {
            ForwarderOrderVO order = forwarderOrderService.getOrderByBiddingId(biddingId);
            return Optional.ofNullable(order)
                    .map(Result::success)
                    .orElseGet(() -> Result.error("未找到与该竞价关联的订单"));
        } catch (Exception e) {
            log.error("根据竞价ID获取订单失败, 竞价ID: {}", biddingId, e);
            return Result.error("获取订单详情失败");
        }
    }
    
    /**
     * 根据采购订单ID获取货代订单
     *
     * @param purchaseOrderId 采购订单ID
     * @return 订单详情
     */
    @GetMapping("/purchase/{purchaseOrderId}")
    @PreAuthorize("hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')")
    public Result<ForwarderOrderVO> getOrderByPurchaseOrderId(@PathVariable Long purchaseOrderId) {
        if (purchaseOrderId == null) {
            return Result.error("采购订单ID不能为空");
        }
        
        try {
            ForwarderOrderVO order = forwarderOrderService.getOrderByPurchaseOrderId(purchaseOrderId);
            return Optional.ofNullable(order)
                    .map(Result::success)
                    .orElseGet(() -> Result.error("未找到与该采购订单关联的货代订单"));
        } catch (Exception e) {
            log.error("根据采购订单ID获取货代订单失败, 采购订单ID: {}", purchaseOrderId, e);
            return Result.error("获取货代订单详情失败");
        }
    }
    
    /**
     * 更新货代订单
     *
     * @param request 更新请求
     * @return 更新后的订单
     */
    @PutMapping
    @PreAuthorize("hasAnyAuthority('forwarder', 'admin')")
    public Result<ForwarderOrderVO> updateOrder(@RequestBody @Validated UpdateForwarderOrderRequest request) {
        try {
            log.info("更新货代订单请求: {}", request);
            ForwarderOrderVO updatedOrder = forwarderOrderService.updateOrder(request);
            log.info("货代订单更新成功, 订单号: {}", updatedOrder.getOrderNumber());
            return Result.success(updatedOrder);
        } catch (BusinessException e) {
            log.error("更新货代订单失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新货代订单时发生未知异常", e);
            return Result.error("更新订单时发生系统错误");
        }
    }
    
    /**
     * 更新订单状态
     *
     * @param id 订单ID
     * @param status 新状态
     * @return 更新后的订单
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("hasAnyAuthority('forwarder', 'admin')")
    public Result<ForwarderOrderVO> updateOrderStatus(
            @PathVariable Long id,
            @RequestParam String status) {
        
        if (id == null) {
            return Result.error("订单ID不能为空");
        }
        
        if (!StringUtils.hasText(status)) {
            return Result.error("订单状态不能为空");
        }
        
        try {
            log.info("更新货代订单状态请求, 订单ID: {}, 新状态: {}", id, status);
            ForwarderOrderVO order = forwarderOrderService.updateOrderStatus(id, status);
            log.info("货代订单状态更新成功, 订单号: {}, 新状态: {}", order.getOrderNumber(), status);
            return Result.success(order);
        } catch (BusinessException e) {
            log.error("更新货代订单状态失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新货代订单状态时发生未知异常", e);
            return Result.error("更新订单状态时发生系统错误");
        }
    }
    
    /**
     * 删除货代订单
     *
     * @param id 订单ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('forwarder', 'admin')")
    public Result<Boolean> deleteOrder(@PathVariable Long id) {
        if (id == null) {
            return Result.error("订单ID不能为空");
        }
        
        try {
            log.info("删除货代订单请求, 订单ID: {}", id);
            boolean result = forwarderOrderService.deleteOrder(id);
            log.info("货代订单删除{}, 订单ID: {}", result ? "成功" : "失败", id);
            return Result.success(result);
        } catch (BusinessException e) {
            log.error("删除货代订单失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除货代订单时发生未知异常", e);
            return Result.error("删除订单时发生系统错误");
        }
    }
} 