package com.purchase.forwarding.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.purchase.commission.domain.event.CommissionCalculationEvent;
import com.purchase.commission.domain.valueobject.Money;
import com.purchase.common.exception.BusinessException;
import com.purchase.common.util.SnowflakeIdGenerator;
import com.purchase.forwarding.bidding.entity.ForwardingBidding;
import com.purchase.forwarding.bidding.dto.response.ForwardingBiddingDetailResponse;
import com.purchase.forwarding.bidding.service.ForwardingBiddingService;
import com.purchase.forwarding.order.dto.CreateForwarderOrderRequest;
import com.purchase.forwarding.order.dto.UpdateForwarderOrderRequest;
import com.purchase.forwarding.order.entity.ForwarderOrder;
import com.purchase.forwarding.order.mapper.ForwarderOrderMapper;
import com.purchase.forwarding.order.service.ForwarderOrderService;
import com.purchase.forwarding.order.vo.ForwarderOrderVO;
import com.purchase.forwarding.requirement.entity.ForwardingRequirement;
import com.purchase.forwarding.requirement.service.ForwardingRequirementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.context.event.EventListener;
import com.purchase.order.event.UnifiedOrderStatusChangedEvent;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 货代订单服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ForwarderOrderServiceImpl extends ServiceImpl<ForwarderOrderMapper, ForwarderOrder> implements ForwarderOrderService {
    
    private final ForwarderOrderMapper forwarderOrderMapper;
    @Lazy
    private final ForwardingRequirementService requirementService;
    private final ForwardingBiddingService biddingService;
    private final SnowflakeIdGenerator idGenerator;
    private final ApplicationEventPublisher eventPublisher;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ForwarderOrderVO createOrder(CreateForwarderOrderRequest request) {
        log.info("开始创建货代订单，需求ID: {}, 竞价ID: {}", request.getRequirementId(), request.getBiddingId());
        
        // 1. 验证货代需求和竞价信息
        ForwardingRequirement requirement = Optional.ofNullable(requirementService.getById(request.getRequirementId()))
                .orElseThrow(() -> new BusinessException("货代需求不存在"));
        
        ForwardingBidding bidding = Optional.ofNullable(biddingService.getById(request.getBiddingId()))
                .orElseThrow(() -> new BusinessException("竞价信息不存在"));
        
        // 2. 验证竞价状态 - 只有已接受的竞价才能创建订单
        if (!"accepted".equals(bidding.getStatus())) {
            log.warn("竞价状态不正确，当前状态: {}, 预期状态: accepted", bidding.getStatus());
            throw new BusinessException("只有已接受的竞价才能创建订单，当前竞价状态: " + bidding.getStatus());
        }
        
        // 3. 使用乐观锁方式检查是否已存在相关订单
        ForwarderOrder existingOrder = forwarderOrderMapper.selectByRequirementId(request.getRequirementId());
        if (existingOrder != null) {
            log.warn("该需求已存在关联订单，需求ID: {}, 现有订单ID: {}", request.getRequirementId(), existingOrder.getId());
            // 如果已存在订单，直接返回现有订单而不是抛出异常
            return convertToVO(existingOrder);
        }
        
        // 4. 双重检查：通过竞价ID检查是否已有订单
        ForwarderOrder existingOrderByBidding = forwarderOrderMapper.selectByBiddingId(request.getBiddingId());
        if (existingOrderByBidding != null) {
            log.warn("该竞价已存在关联订单，竞价ID: {}, 现有订单ID: {}", request.getBiddingId(), existingOrderByBidding.getId());
            return convertToVO(existingOrderByBidding);
        }
        
        try {
            // 5. 创建订单对象并使用Builder模式设置属性
            Long orderId = idGenerator.nextId();
            ForwarderOrder order = ForwarderOrder.builder()
                    .id(orderId)
                    .orderNumber(generateOrderNumber())
                    // 关联信息
                    .requirementId(requirement.getId())
                    .biddingId(bidding.getId())
                    .purchaseOrderId(requirement.getOrderId())
                    .forwarderId(bidding.getForwarderId())
                    .forwarderName(StringUtils.hasText(bidding.getForwarderName()) ? bidding.getForwarderName() : "未知")
                    .forwarderCompanyName(StringUtils.hasText(bidding.getForwarderCompanyName()) ? bidding.getForwarderCompanyName() : "未知")
                    .creatorId(request.getCreatorId())
                    .creatorName(request.getCreatorName())
                    .creatorRole(request.getCreatorRole())
                    // 订单状态
                    .orderStatus(ForwarderOrder.OrderStatus.PENDING)
                    .paymentStatus(ForwarderOrder.PaymentStatus.UNPAID)
                    .totalPrice(bidding.getBiddingPrice())
                    // 备注
                    .remarks(request.getRemarks())
                    // 高优先级字段
                    .vesselName(request.getVesselName())
                    .voyageNumber(request.getVoyageNumber())
                    .etd(request.getEtd())
                    .eta(request.getEta())
                    .billOfLadingNumber(request.getBillOfLadingNumber())
                    .billOfLadingType(request.getBillOfLadingType())
                    .build();
            
            // 从需求复制属性
            copyRequirementProperties(requirement, order);
            
            // 从竞价复制属性
            copyBiddingProperties(bidding, order);
            
            // 6. 保存订单
            int insertResult = forwarderOrderMapper.insert(order);
            if (insertResult <= 0) {
                throw new BusinessException("创建货代订单失败，数据库插入异常");
            }
            
            log.info("货代订单创建成功，订单ID: {}, 订单号: {}", order.getId(), order.getOrderNumber());
            
            // 7. 转换为VO返回
            return convertToVO(order);
            
        } catch (org.springframework.dao.DuplicateKeyException e) {
            // 处理数据库唯一性约束冲突
            log.warn("检测到并发创建订单，尝试返回已存在的订单，需求ID: {}", request.getRequirementId());
            
            // 再次查询已存在的订单
            ForwarderOrder existingOrderAfterConflict = forwarderOrderMapper.selectByRequirementId(request.getRequirementId());
            if (existingOrderAfterConflict != null) {
                log.info("返回已存在的货代订单，订单ID: {}", existingOrderAfterConflict.getId());
                return convertToVO(existingOrderAfterConflict);
            } else {
                // 如果仍然找不到，说明可能是其他字段的唯一性冲突
                throw new BusinessException("创建货代订单失败，存在数据冲突");
            }
        } catch (Exception e) {
            log.error("创建货代订单时发生异常，需求ID: {}, 竞价ID: {}", request.getRequirementId(), request.getBiddingId(), e);
            throw new BusinessException("创建货代订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 从需求中复制属性到订单
     */
    private void copyRequirementProperties(ForwardingRequirement requirement, ForwarderOrder order) {
        order.setTitle(requirement.getTitle());
        order.setProductName(requirement.getProductName());
        order.setHsCode(requirement.getHsCode());
        order.setDestinationCountry(requirement.getDestinationCountry());
        order.setDestinationAddress(requirement.getDestinationAddress());
        order.setContactPerson(requirement.getContactPerson());
        order.setContactPhone(requirement.getContactPhone());
        order.setExpectedDeliveryDate(requirement.getExpectedDeliveryDate());
        order.setDescription(requirement.getDescription());
        order.setCargoType(requirement.getCargoType());
        order.setCargoWeight(requirement.getCargoWeight());
        order.setCargoVolume(requirement.getCargoVolume());
        order.setPackageCount(requirement.getPackageCount());
        order.setSpecialRequirements(requirement.getSpecialRequirements());
        order.setDocuments(null);
        order.setProductImages(requirement.getProductImages());
        order.setProductVideos(requirement.getProductVideos());
        
        // 港口信息
        order.setPortOfLoadingName(requirement.getPortOfLoadingName());
        order.setPortOfLoadingCode(requirement.getPortOfLoadingCode());
        order.setPortOfDestinationName(requirement.getPortOfDestinationName());
        order.setPortOfDestinationCode(requirement.getPortOfDestinationCode());
        
        // 特殊需求信息
        order.setNeedCertification(requirement.getNeedCertification());
        order.setNeedFumigation(requirement.getNeedFumigation());
        order.setShipmentType(requirement.getShipmentType());
        order.setContainerSize(requirement.getContainerSize());
        order.setContainerQty(requirement.getContainerQty());
        order.setDeliveryTerms(requirement.getDeliveryTerms());
    }
    
    /**
     * 从竞价中复制属性到订单
     */
    private void copyBiddingProperties(ForwardingBidding bidding, ForwarderOrder order) {
        order.setBiddingPrice(bidding.getBiddingPrice());
        order.setCurrency(bidding.getCurrency());
        order.setShippingMethod(bidding.getShippingMethod());
        order.setShippingRoute(bidding.getShippingRoute());
        order.setEstimatedDays(bidding.getEstimatedDays());
        order.setBiddingDescription(bidding.getDescription());
        order.setInsuranceIncluded(bidding.getInsuranceIncluded());
        order.setInsuranceAmount(bidding.getInsuranceAmount());
        order.setCustomsService(bidding.getCustomsService());
        
        // 物流跟踪信息
        order.setTrackingNumber(bidding.getTrackingNumber());
        order.setTrackingUrl(bidding.getTrackingUrl());
        order.setEstimatedPickupDate(bidding.getEstimatedPickupDate());
        order.setEstimatedDeliveryDate(bidding.getEstimatedDeliveryDate());
        
        // 费用明细字段复制
        order.setCostExportPacking(bidding.getCostExportPacking());
        order.setCostOriginInlandHaulage(bidding.getCostOriginInlandHaulage());
        order.setCostExportCustomsDocs(bidding.getCostExportCustomsDocs());
        order.setCostOriginHandling(bidding.getCostOriginHandling());
        order.setCostMainFreight(bidding.getCostMainFreight());
        order.setCostFreightSurcharges(bidding.getCostFreightSurcharges());
        order.setCostCargoInsurance(bidding.getCostCargoInsurance());
        order.setCostDestinationHandlingBySeller(bidding.getCostDestinationHandlingBySeller());
        order.setCostDestinationUnloadingBySeller(bidding.getCostDestinationUnloadingBySeller());
        order.setCostDestinationInlandHaulageBySeller(bidding.getCostDestinationInlandHaulageBySeller());
        order.setCostImportCustomsDocsBySeller(bidding.getCostImportCustomsDocsBySeller());
        order.setCostImportDutiesTaxesBySeller(bidding.getCostImportDutiesTaxesBySeller());
        order.setCostForwarderServiceFee(bidding.getCostForwarderServiceFee());
        order.setCustomedCosts(bidding.getCustomedCosts());
    }
    
    /**
     * 更新需求和竞价状态
     */
    private void updateRequirementAndBiddingStatus(ForwardingRequirement requirement, ForwardingBidding bidding) {
        // 只更新需求状态，不再尝试接受竞价
        boolean requirementUpdated = requirementService.updateStatus(requirement.getId(), "in_progress");
        log.info("Requirement status updated to 'in_progress' for ID: {}, Update successful: {}", requirement.getId(), requirementUpdated);
        
        // 不再调用acceptBidding方法，避免"无权接受此竞价"错误
        // 注：创建订单时假定竞价已经被接受，或者订单创建不依赖于竞价的接受状态
        log.info("Creating order with bidding ID: {}, current bidding status: {}", bidding.getId(), bidding.getStatus());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ForwarderOrderVO updateOrder(UpdateForwarderOrderRequest request) {
        // 验证订单是否存在
        ForwarderOrder order = Optional.ofNullable(getById(request.getId()))
                .orElseThrow(() -> new BusinessException("订单不存在"));
        
        // 使用Builder构建更新
        ForwarderOrder updatedOrder = updateOrderFromRequest(order, request);
        
        // 更新订单
        updateById(updatedOrder);
        
        // 返回更新后的订单
        return convertToVO(updatedOrder);
    }
    
    /**
     * 根据请求更新订单对象
     */
    private ForwarderOrder updateOrderFromRequest(ForwarderOrder order, UpdateForwarderOrderRequest request) {
        // 基础信息 - 产品信息
        Optional.ofNullable(request.getPurchaseOrderId()).ifPresent(order::setPurchaseOrderId);
        Optional.ofNullable(request.getProductName()).ifPresent(order::setProductName);
        Optional.ofNullable(request.getHsCode()).ifPresent(order::setHsCode);
        Optional.ofNullable(request.getCargoType()).ifPresent(order::setCargoType);
        Optional.ofNullable(request.getCargoWeight()).ifPresent(order::setCargoWeight);
        Optional.ofNullable(request.getCargoVolume()).ifPresent(order::setCargoVolume);
        Optional.ofNullable(request.getPackageCount()).ifPresent(order::setPackageCount);
        Optional.ofNullable(request.getSpecialRequirements()).ifPresent(order::setSpecialRequirements);
        
        // 港口信息
        Optional.ofNullable(request.getPortOfLoadingName()).ifPresent(order::setPortOfLoadingName);
        Optional.ofNullable(request.getPortOfLoadingCode()).ifPresent(order::setPortOfLoadingCode);
        Optional.ofNullable(request.getPortOfDestinationName()).ifPresent(order::setPortOfDestinationName);
        Optional.ofNullable(request.getPortOfDestinationCode()).ifPresent(order::setPortOfDestinationCode);
        Optional.ofNullable(request.getDeliveryTerms()).ifPresent(order::setDeliveryTerms);
        
        // 航运信息
        Optional.ofNullable(request.getVesselName()).ifPresent(order::setVesselName);
        Optional.ofNullable(request.getVoyageNumber()).ifPresent(order::setVoyageNumber);
        Optional.ofNullable(request.getEtd()).ifPresent(order::setEtd);
        Optional.ofNullable(request.getEta()).ifPresent(order::setEta);
        Optional.ofNullable(request.getBillOfLadingNumber()).ifPresent(order::setBillOfLadingNumber);
        Optional.ofNullable(request.getBillOfLadingType()).ifPresent(order::setBillOfLadingType);
        
        // 运输信息
        Optional.ofNullable(request.getCurrency()).ifPresent(order::setCurrency);
        Optional.ofNullable(request.getShippingMethod()).ifPresent(order::setShippingMethod);
        Optional.ofNullable(request.getShippingRoute()).ifPresent(order::setShippingRoute);
        Optional.ofNullable(request.getEstimatedDays()).ifPresent(order::setEstimatedDays);
        Optional.ofNullable(request.getInsuranceIncluded()).ifPresent(order::setInsuranceIncluded);
        Optional.ofNullable(request.getInsuranceAmount()).ifPresent(order::setInsuranceAmount);
        Optional.ofNullable(request.getCustomsService()).ifPresent(order::setCustomsService);
        
        // 物流跟踪信息
        Optional.ofNullable(request.getTrackingNumber()).ifPresent(order::setTrackingNumber);
        Optional.ofNullable(request.getTrackingUrl()).ifPresent(order::setTrackingUrl);
        Optional.ofNullable(request.getEstimatedPickupDate()).ifPresent(order::setEstimatedPickupDate);
        Optional.ofNullable(request.getEstimatedDeliveryDate()).ifPresent(order::setEstimatedDeliveryDate);
        Optional.ofNullable(request.getActualPickupDate()).ifPresent(order::setActualPickupDate);
        Optional.ofNullable(request.getActualDeliveryDate()).ifPresent(order::setActualDeliveryDate);
        
        // 支付信息
        Optional.ofNullable(request.getPaymentStatus()).ifPresent(paymentStatus -> {
            // 如果从未支付变为已支付，设置支付时间
            if ("paid".equals(paymentStatus) && !"paid".equals(order.getPaymentStatus())) {
                order.setPaymentTime(LocalDateTime.now());
            }
            order.setPaymentStatus(paymentStatus);
        });
        
        Optional.ofNullable(request.getTotalPrice()).ifPresent(order::setTotalPrice);
        
        // 备注
        Optional.ofNullable(request.getRemarks()).ifPresent(order::setRemarks);
        
        // 费用明细字段更新
        Optional.ofNullable(request.getCostExportPacking()).ifPresent(order::setCostExportPacking);
        Optional.ofNullable(request.getCostOriginInlandHaulage()).ifPresent(order::setCostOriginInlandHaulage);
        Optional.ofNullable(request.getCostExportCustomsDocs()).ifPresent(order::setCostExportCustomsDocs);
        Optional.ofNullable(request.getCostOriginHandling()).ifPresent(order::setCostOriginHandling);
        Optional.ofNullable(request.getCostMainFreight()).ifPresent(order::setCostMainFreight);
        Optional.ofNullable(request.getCostFreightSurcharges()).ifPresent(order::setCostFreightSurcharges);
        Optional.ofNullable(request.getCostCargoInsurance()).ifPresent(order::setCostCargoInsurance);
        Optional.ofNullable(request.getCostDestinationHandlingBySeller()).ifPresent(order::setCostDestinationHandlingBySeller);
        Optional.ofNullable(request.getCostDestinationUnloadingBySeller()).ifPresent(order::setCostDestinationUnloadingBySeller);
        Optional.ofNullable(request.getCostDestinationInlandHaulageBySeller()).ifPresent(order::setCostDestinationInlandHaulageBySeller);
        Optional.ofNullable(request.getCostImportCustomsDocsBySeller()).ifPresent(order::setCostImportCustomsDocsBySeller);
        Optional.ofNullable(request.getCostImportDutiesTaxesBySeller()).ifPresent(order::setCostImportDutiesTaxesBySeller);
        Optional.ofNullable(request.getCostForwarderServiceFee()).ifPresent(order::setCostForwarderServiceFee);
        Optional.ofNullable(request.getCustomedCosts()).ifPresent(order::setCustomedCosts);
        
        // 更新订单状态（如果状态发生变化）
        Optional.ofNullable(request.getOrderStatus())
                .filter(status -> !status.equals(order.getOrderStatus()))
                .ifPresent(newStatus -> {
                    // 验证状态转换是否合法
                    validateStatusTransition(order.getOrderStatus(), newStatus);
                    String oldStatus = order.getOrderStatus();
                    order.setOrderStatus(newStatus);

                    // 处理状态变更后的逻辑
                    handleOrderStatusChanged(order, oldStatus, newStatus);
                });
        
        return order;
    }
    
    @Override
    public ForwarderOrderVO getOrderById(Long id) {
        return Optional.ofNullable(getById(id))
                .map(this::convertToVO)
                .orElse(null);
    }
    
    @Override
    public ForwarderOrderVO getOrderByNumber(String orderNumber) {
        return Optional.ofNullable(forwarderOrderMapper.selectByOrderNumber(orderNumber))
                .map(this::convertToVO)
                .orElse(null);
    }
    
    @Override
    public ForwarderOrderVO getOrderByRequirementId(Long requirementId) {
        return Optional.ofNullable(forwarderOrderMapper.selectByRequirementId(requirementId))
                .map(this::convertToVO)
                .orElse(null);
    }
    
    @Override
    public ForwarderOrderVO getOrderByBiddingId(Long biddingId) {
        return Optional.ofNullable(forwarderOrderMapper.selectByBiddingId(biddingId))
                .map(this::convertToVO)
                .orElse(null);
    }
    
    @Override
    public ForwarderOrderVO getOrderByPurchaseOrderId(Long purchaseOrderId) {
        return Optional.ofNullable(forwarderOrderMapper.selectByPurchaseOrderId(purchaseOrderId))
                .map(this::convertToVO)
                .orElse(null);
    }
    
    @Override
    public IPage<ForwarderOrderVO> getOrderPage(int page, int size, Long forwarderId, Long creatorId, String status) {
        Page<ForwarderOrder> pageParam = new Page<>(page, size);
        IPage<ForwarderOrder> orderPage = forwarderOrderMapper.selectOrderPage(pageParam, forwarderId, creatorId, status);
        
        // 转换为VO并使用Stream API处理
        Page<ForwarderOrderVO> voPage = new Page<>(orderPage.getCurrent(), orderPage.getSize(), orderPage.getTotal());
        voPage.setRecords(orderPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList()));
        
        return voPage;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ForwarderOrderVO updateOrderStatus(Long id, String status) {
        // 验证订单是否存在
        ForwarderOrder order = Optional.ofNullable(getById(id))
                .orElseThrow(() -> new BusinessException("订单不存在"));
        
        // 验证状态转换是否合法
        validateStatusTransition(order.getOrderStatus(), status);

        // 记录旧状态
        String oldStatus = order.getOrderStatus();

        // 更新订单状态
        order.setOrderStatus(status);

        // 处理状态变更后的逻辑
        handleOrderStatusChanged(order, oldStatus, status);
        
        // 更新订单
        updateById(order);
        
        // 返回更新后的订单
        return convertToVO(order);
    }
    
    /**
     * 更新需求状态
     */
    private void updateRequirementStatus(Long requirementId, String status) {
        Optional.ofNullable(requirementId)
                .ifPresent(id -> {
                    boolean updated = requirementService.updateStatus(id, status);
                    log.info("Requirement status updated to '{}' for ID: {}, Update successful: {}", status, id, updated);
                });
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteOrder(Long id) {
        // 验证订单是否存在
        ForwarderOrder order = Optional.ofNullable(getById(id))
                .orElseThrow(() -> new BusinessException("订单不存在"));
        
        // 仅允许删除待处理状态的订单
        if (!ForwarderOrder.OrderStatus.PENDING.equals(order.getOrderStatus())) {
            throw new BusinessException("只能删除待处理状态的订单");
        }
        
        // 逻辑删除订单
        return removeById(id);
    }
    
    @Override
    public ForwarderOrderVO convertToVO(ForwarderOrder entity) {
        if (entity == null) {
            return null;
        }
        
        // 使用Builder模式创建VO对象
        ForwarderOrderVO vo = ForwarderOrderVO.builder().build();
        
        // 使用BeanUtils复制属性
        BeanUtils.copyProperties(entity, vo);
        
        return vo;
    }
    
    /**
     * 生成货代订单编号
     * 格式：FWD + 年月日 + 6位雪花算法序列号
     */
    private String generateOrderNumber() {
        LocalDateTime now = LocalDateTime.now();
        String dateStr = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return "FWD" + dateStr + String.format("%06d", idGenerator.nextId() % 1000000);
    }
    
    /**
     * 验证状态转换是否合法
     */
    private void validateStatusTransition(String currentStatus, String newStatus) {
        if (Objects.equals(currentStatus, newStatus)) {
            return; // 状态未变化，无需校验
        }
        
        boolean isValid = false;
        
        switch (currentStatus) {
            case ForwarderOrder.OrderStatus.PENDING:
                // 待处理 -> 处理中、已取消
                isValid = ForwarderOrder.OrderStatus.PROCESSING.equals(newStatus) ||
                        ForwarderOrder.OrderStatus.CANCELLED.equals(newStatus);
                break;
            case ForwarderOrder.OrderStatus.PROCESSING:
                // 处理中 -> 已完成、已取消
                isValid = ForwarderOrder.OrderStatus.COMPLETED.equals(newStatus) ||
                        ForwarderOrder.OrderStatus.CANCELLED.equals(newStatus);
                break;
            case ForwarderOrder.OrderStatus.COMPLETED:
            case ForwarderOrder.OrderStatus.CANCELLED:
                // 已完成/已取消状态不能再变更
                isValid = false;
                break;
            default:
                isValid = false;
                break;
        }
        
        if (!isValid) {
            throw new BusinessException(String.format("订单状态无法从 %s 变更为 %s", currentStatus, newStatus));
        }
    }

    /**
     * 监听统一订单状态变更事件
     * 当统一订单状态变更时，同步更新关联的货代订单状态
     */
    @EventListener
    @Transactional
    public void handleUnifiedOrderStatusChanged(UnifiedOrderStatusChangedEvent event) {
        log.info("收到统一订单状态变更事件: {}", event);

        try {
            // 查找关联的货代订单
            ForwarderOrderVO forwarderOrder = getOrderByPurchaseOrderId(event.getOrderId());
            if (forwarderOrder != null) {
                log.info("找到关联的货代订单，开始同步状态，货代订单ID: {}", forwarderOrder.getId());

                // 根据统一订单的新状态决定货代订单的状态
                String forwarderOrderStatus = mapUnifiedOrderStatusToForwarderStatus(event.getNewStatus());
                if (forwarderOrderStatus != null) {
                    updateOrderStatus(forwarderOrder.getId(), forwarderOrderStatus);
                    log.info("货代订单状态已同步更新为{}，货代订单ID: {}", forwarderOrderStatus, forwarderOrder.getId());
                }
            } else {
                log.info("未找到关联的货代订单，统一订单ID: {}", event.getOrderId());
            }
        } catch (Exception e) {
            log.error("同步货代订单状态失败，统一订单ID: {}", event.getOrderId(), e);
            // 不抛出异常，避免影响其他事件监听器
        }
    }

    /**
     * 将统一订单状态映射为货代订单状态
     */
    private String mapUnifiedOrderStatusToForwarderStatus(String unifiedOrderStatus) {
        switch (unifiedOrderStatus) {
            case "completed":
                return "completed";
            case "cancelled":
                return "cancelled";
            case "processing":
                return "in_progress";
            default:
                return null; // 其他状态不需要同步
        }
    }

    /**
     * 处理订单状态变更后的逻辑
     * 统一处理状态变更后的业务逻辑，避免重复代码
     *
     * @param order 订单对象
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     */
    private void handleOrderStatusChanged(ForwarderOrder order, String oldStatus, String newStatus) {
        log.info("货代订单状态变更: 订单ID={}, 状态: {} -> {}", order.getId(), oldStatus, newStatus);

        // 如果状态变为已完成，设置完成时间并发布事件
        if (ForwarderOrder.OrderStatus.COMPLETED.equals(newStatus)) {
            LocalDateTime completionTime = LocalDateTime.now();
            order.setCompletionTime(completionTime);

            // 发布订单完成事件，用于佣金计算
            // 注意：creatorId可能是买家或卖家，佣金计算只关心邀请关系
            Money orderAmount = Money.of(order.getTotalPrice() != null ? order.getTotalPrice() : BigDecimal.ZERO);
            CommissionCalculationEvent event = new CommissionCalculationEvent(
                    order.getId(),                    // 货代订单ID
                    order.getCreatorId(),            // 发起人ID（可能是买家或卖家）
                    orderAmount,                     // 订单金额
                    "logistics",                     // 订单类型：货代订单
                    completionTime                   // 完成时间
            );
            eventPublisher.publishEvent(event);
            log.info("货代订单完成事件已发布: 订单ID={}, 发起人ID={}, 金额={}, 类型={}",
                    order.getId(), order.getCreatorId(), orderAmount, "logistics");
        }

        // 如果状态变为已取消，可以在这里添加取消相关的逻辑
        if (ForwarderOrder.OrderStatus.CANCELLED.equals(newStatus)) {
            log.info("货代订单已取消: 订单ID={}", order.getId());
            // 可以在这里添加取消订单的业务逻辑，比如释放资源、发送通知等
        }
    }
}