package com.purchase.forwarding.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.purchase.forwarding.order.entity.ForwarderOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 货代订单Mapper接口
 */
@Mapper
public interface ForwarderOrderMapper extends BaseMapper<ForwarderOrder> {
    
    /**
     * 分页查询货代订单列表
     *
     * @param page 分页参数
     * @param forwarderId 货代ID（可选）
     * @param creatorId 创建者ID（可选）
     * @param status 订单状态（可选）
     * @return 分页结果
     */
    IPage<ForwarderOrder> selectOrderPage(
            Page<ForwarderOrder> page,
            @Param("forwarderId") Long forwarderId,
            @Param("creatorId") Long creatorId,
            @Param("status") String status
    );
    
    /**
     * 根据货代需求ID查询相关订单
     *
     * @param requirementId 货代需求ID
     * @return 订单对象，如果不存在则返回null
     */
    ForwarderOrder selectByRequirementId(@Param("requirementId") Long requirementId);
    
    /**
     * 根据竞价ID查询相关订单
     *
     * @param biddingId 竞价ID
     * @return 订单对象，如果不存在则返回null
     */
    ForwarderOrder selectByBiddingId(@Param("biddingId") Long biddingId);
    
    /**
     * 根据订单编号查询订单
     *
     * @param orderNumber 订单编号
     * @return 订单对象，如果不存在则返回null
     */
    ForwarderOrder selectByOrderNumber(@Param("orderNumber") String orderNumber);
    
    /**
     * 根据采购订单ID查询相关货代订单
     *
     * @param purchaseOrderId 采购订单ID
     * @return 货代订单对象，如果不存在则返回null
     */
    ForwarderOrder selectByPurchaseOrderId(@Param("purchaseOrderId") Long purchaseOrderId);
} 