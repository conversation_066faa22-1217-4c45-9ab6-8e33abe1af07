package com.purchase.forwarding.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.purchase.forwarding.order.dto.CreateForwarderOrderRequest;
import com.purchase.forwarding.order.dto.UpdateForwarderOrderRequest;
import com.purchase.forwarding.order.entity.ForwarderOrder;
import com.purchase.forwarding.order.vo.ForwarderOrderVO;

/**
 * 货代订单服务接口
 */
public interface ForwarderOrderService extends IService<ForwarderOrder> {
    
    /**
     * 创建货代订单
     *
     * @param request 创建请求
     * @return 订单视图对象
     */
    ForwarderOrderVO createOrder(CreateForwarderOrderRequest request);
    
    /**
     * 更新货代订单
     *
     * @param request 更新请求
     * @return 订单视图对象
     */
    ForwarderOrderVO updateOrder(UpdateForwarderOrderRequest request);
    
    /**
     * 获取货代订单详情
     *
     * @param id 订单ID
     * @return 订单视图对象
     */
    ForwarderOrderVO getOrderById(Long id);
    
    /**
     * 根据订单编号获取订单
     *
     * @param orderNumber 订单编号
     * @return 订单视图对象
     */
    ForwarderOrderVO getOrderByNumber(String orderNumber);
    
    /**
     * 根据需求ID获取订单
     *
     * @param requirementId 需求ID
     * @return 订单视图对象
     */
    ForwarderOrderVO getOrderByRequirementId(Long requirementId);
    
    /**
     * 根据竞价ID获取订单
     *
     * @param biddingId 竞价ID
     * @return 订单视图对象
     */
    ForwarderOrderVO getOrderByBiddingId(Long biddingId);
    
    /**
     * 根据采购订单ID获取货代订单
     *
     * @param purchaseOrderId 采购订单ID
     * @return 订单视图对象
     */
    ForwarderOrderVO getOrderByPurchaseOrderId(Long purchaseOrderId);
    
    /**
     * 分页查询货代订单
     *
     * @param page 页码
     * @param size 每页大小
     * @param forwarderId 货代ID（可选）
     * @param creatorId 创建者ID（可选）
     * @param status 订单状态（可选）
     * @return 分页结果
     */
    IPage<ForwarderOrderVO> getOrderPage(int page, int size, Long forwarderId, Long creatorId, String status);
    
    /**
     * 更新订单状态
     *
     * @param id 订单ID
     * @param status 新状态
     * @return 更新后的订单视图对象
     */
    ForwarderOrderVO updateOrderStatus(Long id, String status);
    
    /**
     * 删除货代订单
     *
     * @param id 订单ID
     * @return 是否成功
     */
    boolean deleteOrder(Long id);
    
    /**
     * 转换实体对象到视图对象
     *
     * @param entity 实体对象
     * @return 视图对象
     */
    ForwarderOrderVO convertToVO(ForwarderOrder entity);
} 