# ForwarderOrderService.java

## 文件概述 (File Overview)
`ForwarderOrderService.java` 是货代订单管理的核心业务服务接口，位于 `com.purchase.forwarding.order.service` 包中，继承了MyBatis-Plus的 `IService<ForwarderOrder>` 接口。该接口定义了货代订单的完整业务规范，提供了订单创建、更新、查询、删除以及状态管理等全生命周期功能。通过DTO和VO模式实现内外模型分离，支持多角色（买家、卖家）发起货代需求，集成事件驱动机制与佣金系统解耦，并提供了完善的并发控制和状态流转管理。该接口是货代业务领域的核心抽象，为控制器层提供清晰的业务逻辑支持。

## 核心功能 (Core Functionality)
*   **订单创建管理**: 支持通过CreateForwarderOrderRequest创建货代订单，包含完整的业务验证
*   **订单更新服务**: 提供UpdateForwarderOrderRequest方式的订单信息更新功能
*   **多维度查询**: 支持按ID、订单号、需求ID、竞价ID、采购订单ID等多种方式查询
*   **分页查询优化**: 提供灵活的分页查询，支持货代ID、创建者ID、状态等过滤条件
*   **状态流转管理**: 专门的订单状态更新方法，确保状态变更的合法性和一致性
*   **订单删除控制**: 提供安全的订单删除功能，包含业务规则验证
*   **模型转换服务**: 标准化的实体对象到视图对象的转换方法
*   **多角色支持**: 支持买家和卖家都可以发起货代订单的业务场景
*   **事件驱动集成**: 与佣金系统通过事件机制解耦集成
*   **并发安全保障**: 提供防重复创建和乐观锁机制
*   **业务规则验证**: 完整的订单创建和状态变更业务规则检查
*   **数据一致性保证**: 确保订单数据与相关业务对象的一致性

## 接口说明 (Interface Description)

### 订单创建和更新方法

#### createOrder - 创建货代订单
*   **方法签名**: `ForwarderOrderVO createOrder(CreateForwarderOrderRequest request)`
*   **功能**: 根据创建请求对象创建新的货代订单
*   **参数**: `request` (CreateForwarderOrderRequest) - 包含订单创建所需的所有信息
*   **返回值**: `ForwarderOrderVO` - 创建成功后的订单视图对象
*   **业务逻辑**: 
    *   验证货代需求和竞价信息的存在性和有效性
    *   检查是否已存在相关订单，防止重复创建
    *   从需求和竞价对象复制属性到订单实体
    *   设置订单状态为pending，生成唯一订单编号
    *   使用乐观锁机制防止并发创建冲突
    *   创建成功后发布OrderCreatedEvent事件

#### updateOrder - 更新货代订单
*   **方法签名**: `ForwarderOrderVO updateOrder(UpdateForwarderOrderRequest request)`
*   **功能**: 根据更新请求对象更新现有的货代订单
*   **参数**: `request` (UpdateForwarderOrderRequest) - 包含订单更新所需的信息
*   **返回值**: `ForwarderOrderVO` - 更新后的订单视图对象
*   **业务逻辑**: 
    *   验证订单ID的存在性和用户权限
    *   检查订单状态是否允许更新
    *   更新允许修改的字段信息
    *   记录更新时间和操作人信息
    *   返回更新后的订单视图对象

#### updateOrderStatus - 更新订单状态
*   **方法签名**: `ForwarderOrderVO updateOrderStatus(Long id, String status)`
*   **功能**: 更新指定订单的状态
*   **参数**: 
    *   `id` (Long) - 订单ID
    *   `status` (String) - 新的订单状态
*   **返回值**: `ForwarderOrderVO` - 更新后的订单视图对象
*   **业务逻辑**: 
    *   验证订单ID的有效性
    *   检查状态转换的合法性（状态机验证）
    *   更新订单状态和相关时间字段
    *   如果状态为completed，发布OrderCompletedEvent事件
    *   同步更新相关需求的状态

### 查询方法

#### getOrderById - 根据ID获取订单详情
*   **方法签名**: `ForwarderOrderVO getOrderById(Long id)`
*   **功能**: 根据订单ID获取订单的详细信息
*   **参数**: `id` (Long) - 订单ID
*   **返回值**: `ForwarderOrderVO` - 订单视图对象
*   **业务逻辑**: 
    *   验证订单ID的有效性
    *   查询订单实体信息
    *   转换为视图对象并填充关联信息
    *   返回完整的订单详情

#### getOrderByNumber - 根据订单编号获取订单
*   **方法签名**: `ForwarderOrderVO getOrderByNumber(String orderNumber)`
*   **功能**: 根据订单编号获取订单信息
*   **参数**: `orderNumber` (String) - 订单编号
*   **返回值**: `ForwarderOrderVO` - 订单视图对象
*   **业务逻辑**: 
    *   验证订单编号的格式和有效性
    *   根据订单编号查询订单实体
    *   转换为视图对象返回

#### getOrderByRequirementId - 根据需求ID获取订单
*   **方法签名**: `ForwarderOrderVO getOrderByRequirementId(Long requirementId)`
*   **功能**: 根据货代需求ID获取关联的订单
*   **参数**: `requirementId` (Long) - 货代需求ID
*   **返回值**: `ForwarderOrderVO` - 订单视图对象
*   **业务逻辑**: 
    *   验证需求ID的有效性
    *   查询与需求关联的订单
    *   转换为视图对象返回

#### getOrderByBiddingId - 根据竞价ID获取订单
*   **方法签名**: `ForwarderOrderVO getOrderByBiddingId(Long biddingId)`
*   **功能**: 根据竞价ID获取关联的订单
*   **参数**: `biddingId` (Long) - 竞价ID
*   **返回值**: `ForwarderOrderVO` - 订单视图对象
*   **业务逻辑**: 
    *   验证竞价ID的有效性
    *   查询与竞价关联的订单
    *   转换为视图对象返回

#### getOrderByPurchaseOrderId - 根据采购订单ID获取货代订单
*   **方法签名**: `ForwarderOrderVO getOrderByPurchaseOrderId(Long purchaseOrderId)`
*   **功能**: 根据采购订单ID获取关联的货代订单
*   **参数**: `purchaseOrderId` (Long) - 采购订单ID
*   **返回值**: `ForwarderOrderVO` - 订单视图对象
*   **业务逻辑**: 
    *   验证采购订单ID的有效性
    *   查询与采购订单关联的货代订单
    *   转换为视图对象返回

#### getOrderPage - 分页查询货代订单
*   **方法签名**: `IPage<ForwarderOrderVO> getOrderPage(int page, int size, Long forwarderId, Long creatorId, String status)`
*   **功能**: 根据指定条件分页查询货代订单列表
*   **参数**: 
    *   `page` (int) - 页码
    *   `size` (int) - 每页大小
    *   `forwarderId` (Long, 可选) - 货代ID
    *   `creatorId` (Long, 可选) - 创建者ID
    *   `status` (String, 可选) - 订单状态
*   **返回值**: `IPage<ForwarderOrderVO>` - 分页结果
*   **业务逻辑**: 
    *   构建动态查询条件
    *   执行分页查询
    *   批量转换为视图对象
    *   返回分页结果

### 删除和转换方法

#### deleteOrder - 删除货代订单
*   **方法签名**: `boolean deleteOrder(Long id)`
*   **功能**: 删除指定的货代订单
*   **参数**: `id` (Long) - 订单ID
*   **返回值**: `boolean` - 删除是否成功
*   **业务逻辑**: 
    *   验证订单是否存在
    *   检查订单状态是否允许删除（仅允许删除待处理状态的订单）
    *   执行逻辑删除操作
    *   返回删除结果

#### convertToVO - 转换实体对象到视图对象
*   **方法签名**: `ForwarderOrderVO convertToVO(ForwarderOrder entity)`
*   **功能**: 将货代订单实体对象转换为视图对象
*   **参数**: `entity` (ForwarderOrder) - 实体对象
*   **返回值**: `ForwarderOrderVO` - 视图对象
*   **业务逻辑**: 
    *   复制实体对象的基本属性到视图对象
    *   填充关联对象的信息（如创建者名称、货代公司名称等）
    *   格式化日期和金额字段
    *   返回完整的视图对象

## 业务规则 (Business Rules)
*   **多角色支持**: 买家和卖家都可以发起货代需求，生成货代订单
*   **订单唯一性**: 每个货代需求只能生成一个货代订单，防止重复创建
*   **状态流转控制**: 订单状态变更需遵循业务规则，不能随意跳转
*   **删除权限限制**: 只能删除待处理状态的订单，其他状态的订单不允许删除
*   **事件驱动集成**: 订单完成时自动发布事件，与佣金系统解耦集成
*   **身份无关性**: 佣金计算不区分发起人是买家还是卖家，只关心邀请关系
*   **并发安全控制**: 使用乐观锁和双重检查防止并发创建订单
*   **数据一致性要求**: 订单数据与需求、竞价、采购订单等相关数据保持一致

## 使用示例 (Usage Examples)

```java
// 1. 服务实现类示例
@Service
@Transactional
public class ForwarderOrderServiceImpl extends ServiceImpl<ForwarderOrderMapper, ForwarderOrder>
    implements ForwarderOrderService {

    @Autowired
    private ForwardingRequirementService requirementService;

    @Autowired
    private ForwardingBiddingService biddingService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ForwarderOrderVO createOrder(CreateForwarderOrderRequest request) {
        log.info("创建货代订单 - requirementId: {}, biddingId: {}, creatorId: {}",
            request.getRequirementId(), request.getBiddingId(), request.getCreatorId());

        // 验证需求和竞价信息
        ForwardingRequirement requirement = requirementService.getById(request.getRequirementId());
        if (requirement == null) {
            throw new BusinessException("货代需求不存在");
        }

        ForwardingBidding bidding = biddingService.getById(request.getBiddingId());
        if (bidding == null) {
            throw new BusinessException("竞价信息不存在");
        }

        // 检查是否已存在订单
        ForwarderOrder existingOrder = baseMapper.selectByRequirementId(request.getRequirementId());
        if (existingOrder != null) {
            log.warn("货代订单已存在 - requirementId: {}, orderId: {}",
                request.getRequirementId(), existingOrder.getId());
            return convertToVO(existingOrder);
        }

        // 创建订单实体
        ForwarderOrder order = new ForwarderOrder();
        order.setId(idGenerator.nextId());
        order.setOrderNumber(generateOrderNumber());
        order.setRequirementId(request.getRequirementId());
        order.setBiddingId(request.getBiddingId());
        order.setCreatorId(request.getCreatorId());
        order.setCreatorName(request.getCreatorName());
        order.setCreatorRole(request.getCreatorRole());
        order.setOrderStatus(ForwarderOrder.OrderStatus.PENDING);

        // 从需求复制属性
        BeanUtils.copyProperties(requirement, order, "id", "createdAt", "updatedAt");

        // 从竞价复制属性
        order.setForwarderId(bidding.getForwarderId());
        order.setForwarderName(bidding.getForwarderName());
        order.setTotalPrice(bidding.getTotalPrice());

        // 保存订单
        baseMapper.insert(order);

        // 发布事件
        eventPublisher.publishEvent(new OrderCreatedEvent(order));

        log.info("货代订单创建成功 - orderId: {}", order.getId());
        return convertToVO(order);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ForwarderOrderVO updateOrderStatus(Long id, String status) {
        log.info("更新订单状态 - orderId: {}, status: {}", id, status);

        // 查询订单
        ForwarderOrder order = baseMapper.selectById(id);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        // 验证状态转换
        validateStatusTransition(order.getOrderStatus(), status);

        // 更新状态
        order.setOrderStatus(status);
        if (ForwarderOrder.OrderStatus.COMPLETED.equals(status)) {
            order.setCompletionTime(LocalDateTime.now());

            // 发布订单完成事件
            OrderCompletedEvent event = new OrderCompletedEvent();
            event.setOrderId(order.getId());
            event.setBuyerId(order.getCreatorId()); // creatorId作为buyerId传递给佣金系统
            event.setOrderType("logistics");
            event.setOrderAmount(order.getTotalPrice());

            eventPublisher.publishEvent(event);
        }

        baseMapper.updateById(order);

        log.info("订单状态更新成功 - orderId: {}, newStatus: {}", id, status);
        return convertToVO(order);
    }

    @Override
    public ForwarderOrderVO convertToVO(ForwarderOrder entity) {
        if (entity == null) {
            return null;
        }

        ForwarderOrderVO vo = new ForwarderOrderVO();
        BeanUtils.copyProperties(entity, vo);

        // 格式化金额
        if (entity.getTotalPrice() != null) {
            vo.setTotalPriceFormatted(entity.getTotalPrice().toString());
        }

        // 格式化日期
        if (entity.getExpectedDeliveryDate() != null) {
            vo.setExpectedDeliveryDateFormatted(
                entity.getExpectedDeliveryDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }

        return vo;
    }
}

// 2. Java客户端调用示例
@Service
public class ForwarderOrderClientService {

    @Autowired
    private ForwarderOrderService forwarderOrderService;

    @Autowired
    private NotificationService notificationService;

    // 创建货代订单
    public ForwarderOrderVO createForwarderOrder(Long requirementId, Long biddingId,
            Long creatorId, String creatorName, String creatorRole) {

        try {
            CreateForwarderOrderRequest request = CreateForwarderOrderRequest.builder()
                .requirementId(requirementId)
                .biddingId(biddingId)
                .creatorId(creatorId)
                .creatorName(creatorName)
                .creatorRole(creatorRole)
                .build();

            ForwarderOrderVO order = forwarderOrderService.createOrder(request);

            // 发送通知给相关方
            notificationService.notifyOrderCreated(order);

            log.info("货代订单创建成功 - orderId: {}", order.getId());
            return order;

        } catch (Exception e) {
            log.error("创建货代订单失败 - requirementId: {}, biddingId: {}", requirementId, biddingId, e);
            throw new SystemException("创建货代订单失败", e);
        }
    }

    // 查询订单列表
    public IPage<ForwarderOrderVO> getOrderList(int page, int size, Long forwarderId, String status) {
        try {
            return forwarderOrderService.getOrderPage(page, size, forwarderId, null, status);
        } catch (Exception e) {
            log.error("查询货代订单列表失败", e);
            throw new SystemException("查询订单列表失败", e);
        }
    }
}

// 3. 业务服务集成示例
@Service
public class ForwarderOrderWorkflowService {

    @Autowired
    private ForwarderOrderService forwarderOrderService;

    // 竞价成功后自动创建订单
    @EventListener
    @Async
    public void handleBiddingAccepted(BiddingAcceptedEvent event) {
        try {
            CreateForwarderOrderRequest request = CreateForwarderOrderRequest.builder()
                .requirementId(event.getRequirementId())
                .biddingId(event.getBiddingId())
                .creatorId(event.getCreatorId())
                .creatorName(event.getCreatorName())
                .creatorRole(event.getCreatorRole())
                .build();

            ForwarderOrderVO order = forwarderOrderService.createOrder(request);

            log.info("竞价成功后自动创建货代订单 - orderId: {}", order.getId());

        } catch (Exception e) {
            log.error("竞价成功后创建订单失败 - biddingId: {}", event.getBiddingId(), e);
        }
    }
}

// 4. 定时任务示例
@Component
public class ForwarderOrderScheduledTasks {

    @Autowired
    private ForwarderOrderService forwarderOrderService;

    // 清理过期的待处理订单
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点
    public void cleanupExpiredOrders() {
        log.info("开始清理过期的货代订单");

        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30);
            List<ForwarderOrder> expiredOrders = forwarderOrderService.getExpiredPendingOrders(cutoffDate);

            for (ForwarderOrder order : expiredOrders) {
                forwarderOrderService.updateOrderStatus(order.getId(), "CANCELLED");
            }

            log.info("过期货代订单清理完成，处理数量: {}", expiredOrders.size());

        } catch (Exception e) {
            log.error("清理过期货代订单失败", e);
        }
    }
}

// 5. 测试示例
@SpringBootTest
class ForwarderOrderServiceTest {

    @Autowired
    private ForwarderOrderService forwarderOrderService;

    @Test
    void testCreateOrder() {
        // 准备测试数据
        CreateForwarderOrderRequest request = CreateForwarderOrderRequest.builder()
            .requirementId(123L)
            .biddingId(456L)
            .creatorId(789L)
            .creatorName("测试用户")
            .creatorRole("buyer")
            .build();

        // 执行测试
        ForwarderOrderVO result = forwarderOrderService.createOrder(request);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getRequirementId()).isEqualTo(123L);
        assertThat(result.getBiddingId()).isEqualTo(456L);
        assertThat(result.getCreatorId()).isEqualTo(789L);
        assertThat(result.getOrderStatus()).isEqualTo("PENDING");
    }

    @Test
    void testUpdateOrderStatus() {
        // 准备测试数据
        Long orderId = 123L;
        String newStatus = "COMPLETED";

        // 执行测试
        ForwarderOrderVO result = forwarderOrderService.updateOrderStatus(orderId, newStatus);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getOrderStatus()).isEqualTo(newStatus);
    }
}
```

## 注意事项 (Notes)
*   **继承IService**: 接口继承了MyBatis-Plus的IService，实现类将自动拥有丰富的CRUD基础方法
*   **DTO/VO模式**: 严格使用DTO接收请求和VO返回响应，实现内外模型隔离，提高代码可维护性
*   **事务管理**: createOrder、updateOrder、deleteOrder等写操作方法的实现必须加上@Transactional注解
*   **权限控制**: 接口定义本身不包含权限逻辑，但实现类或调用方必须进行严格的权限校验
*   **参数校验**: 实现类需要对传入的request对象进行详细的业务规则校验
*   **状态机**: updateOrderStatus的实现应包含状态机逻辑，验证状态转换是否合法
*   **空值处理**: 查询方法的实现需要妥善处理未找到结果的情况，通常返回null或空集合
*   **关联查询**: convertToVO的实现可能需要进行关联查询，要注意避免N+1查询问题
*   **命名规范**: 接口方法命名清晰，准确地描述了其功能，如getOrderBy...系列方法
*   **单一职责**: 接口专注于货代订单的核心业务，没有混入其他不相关的逻辑
*   **可扩展性**: 接口设计良好，如果未来需要新增查询条件或业务逻辑，可以通过增加新方法来扩展
*   **性能考虑**: 分页查询方法的实现需要考虑性能，避免全表扫描，应合理使用索引
*   **日志记录**: 关键操作（如创建、更新、删除）的实现应包含详细的日志记录
*   **异常处理**: 实现类需要定义清晰的异常处理策略，对于业务异常和系统异常应有不同的处理方式
*   **测试覆盖**: 应为所有接口方法编写全面的单元测试，确保业务逻辑的正确性
*   **并发控制**: 在高并发场景下，需要考虑订单创建和状态更新的并发控制
*   **事件发布**: 订单状态变更时需要发布相应的事件，与其他系统模块解耦集成
