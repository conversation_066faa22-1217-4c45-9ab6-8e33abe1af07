package com.purchase.forwarding.order.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 货代订单视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForwarderOrderVO {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 订单编号
     */
    private String orderNumber;
    
    /**
     * 货代需求ID
     */
    private Long requirementId;
    
    /**
     * 竞价ID
     */
    private Long biddingId;
    
    /**
     * 采购订单ID - 关联的采购订单
     */
    private Long purchaseOrderId;
    
    /**
     * 货代ID
     */
    private Long forwarderId;
    
    /**
     * 货代用户名
     */
    private String forwarderName;
    
    /**
     * 货代公司名称
     */
    private String forwarderCompanyName;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
    
    /**
     * 创建者名称
     */
    private String creatorName;
    
    /**
     * 创建者角色
     */
    private String creatorRole;
    
    /**
     * 需求标题
     */
    private String title;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * HS编码
     */
    private String hsCode;
    
    /**
     * 目的国家
     */
    private String destinationCountry;
    
    /**
     * 目的地址
     */
    private String destinationAddress;
    
    /**
     * 联系人
     */
    private String contactPerson;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 预期交付日期
     */
    private LocalDate expectedDeliveryDate;
    
    /**
     * 需求描述
     */
    private String description;
    
    /**
     * 货物类型
     */
    private String cargoType;
    
    /**
     * 货物重量(kg)
     */
    private BigDecimal cargoWeight;
    
    /**
     * 货物体积(m³)
     */
    private BigDecimal cargoVolume;
    
    /**
     * 包装数量
     */
    private Integer packageCount;
    
    /**
     * 特殊要求
     */
    private String specialRequirements;
    
    /**
     * 文档(JSON)
     */
    private String documents;
    
    /**
     * 产品图片(JSON)
     */
    private String productImages;
    
    /**
     * 产品视频(JSON)
     */
    private String productVideos;
    
    /**
     * 起运港名称
     */
    private String portOfLoadingName;
    
    /**
     * 起运港代码
     */
    private String portOfLoadingCode;
    
    /**
     * 目的港名称
     */
    private String portOfDestinationName;
    
    /**
     * 目的港代码
     */
    private String portOfDestinationCode;
    
    /**
     * 需要认证(1是0否)
     */
    private String needCertification;
    
    /**
     * 需要熏蒸(1是0否)
     */
    private String needFumigation;
    
    /**
     * 装运类型(FCL整柜/LCL拼箱)
     */
    private String shipmentType;
    
    /**
     * 集装箱尺寸
     */
    private String containerSize;
    
    /**
     * 集装箱数量
     */
    private Integer containerQty;
    
    /**
     * 交付条款(FOB/CIF/EXW等)
     */
    private String deliveryTerms;
    
    /**
     * 船名/航班号
     */
    private String vesselName;
    
    /**
     * 航次号
     */
    private String voyageNumber;
    
    /**
     * 预计离港时间(ETD)
     */
    private LocalDateTime etd;
    
    /**
     * 预计到港时间(ETA)
     */
    private LocalDateTime eta;
    
    /**
     * 提单号
     */
    private String billOfLadingNumber;
    
    /**
     * 提单类型(电放/正本)
     */
    private String billOfLadingType;
    
    /**
     * 报价金额
     */
    private BigDecimal biddingPrice;
    
    /**
     * 货币单位
     */
    private String currency;
    
    /**
     * 运输方式(SEA/AIR/RAIL/ROAD)
     */
    private String shippingMethod;
    
    /**
     * 运输路线
     */
    private String shippingRoute;
    
    /**
     * 预计运输天数
     */
    private Integer estimatedDays;
    
    /**
     * 竞价描述
     */
    private String biddingDescription;
    
    /**
     * 是否包含保险
     */
    private Boolean insuranceIncluded;
    
    /**
     * 保险金额
     */
    private BigDecimal insuranceAmount;
    
    /**
     * 是否提供清关服务
     */
    private Boolean customsService;
    
    /**
     * 物流跟踪号
     */
    private String trackingNumber;
    
    /**
     * 物流跟踪URL
     */
    private String trackingUrl;
    
    /**
     * 预计提货日期
     */
    private LocalDateTime estimatedPickupDate;
    
    /**
     * 预计送达日期
     */
    private LocalDateTime estimatedDeliveryDate;
    
    /**
     * 实际提货日期
     */
    private LocalDateTime actualPickupDate;
    
    /**
     * 实际送达日期
     */
    private LocalDateTime actualDeliveryDate;
    
    /**
     * 订单状态
     */
    private String orderStatus;
    
    /**
     * 支付状态
     */
    private String paymentStatus;
    
    /**
     * 订单总金额
     */
    private BigDecimal totalPrice;
    
    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completionTime;
    
    /**
     * 备注
     */
    private String remarks;
    
    // ========== 费用明细字段 ==========
    
    /**
     * 出口包装费 (卖方承担)
     */
    private BigDecimal costExportPacking;
    
    /**
     * 起运地内陆运输费 (卖方承担)
     */
    private BigDecimal costOriginInlandHaulage;
    
    /**
     * 出口报关及单证费 (卖方承担)
     */
    private BigDecimal costExportCustomsDocs;
    
    /**
     * 起运地操作费 (卖方承担)
     */
    private BigDecimal costOriginHandling;
    
    /**
     * 主运费 (卖方承担部分)
     */
    private BigDecimal costMainFreight;
    
    /**
     * 运费附加费 (卖方承担部分)
     */
    private BigDecimal costFreightSurcharges;
    
    /**
     * 货物运输保险费 (卖方支付的保费)
     */
    private BigDecimal costCargoInsurance;
    
    /**
     * 目的地操作费 (卖方承担)
     */
    private BigDecimal costDestinationHandlingBySeller;
    
    /**
     * 目的地卸货费 (卖方承担)
     */
    private BigDecimal costDestinationUnloadingBySeller;
    
    /**
     * 目的地内陆运输费 (卖方承担)
     */
    private BigDecimal costDestinationInlandHaulageBySeller;
    
    /**
     * 进口报关及单证费 (卖方承担-DDP)
     */
    private BigDecimal costImportCustomsDocsBySeller;
    
    /**
     * 进口关税及税费 (卖方承担-DDP)
     */
    private BigDecimal costImportDutiesTaxesBySeller;
    
    /**
     * 货代服务费/操作费 (卖方承担)
     */
    private BigDecimal costForwarderServiceFee;
    
    /**
     * 自定义费用项目 JSON数组
     * 格式: [{"name":"费用名称","amount":金额,"description":"费用说明","currency":"货币"}]
     */
    private String customedCosts;
    
    // ========== 系统字段 ==========
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 