# ForwarderOrderController.md

## 1. 文件概述

`ForwarderOrderController.java` 是货代模块中专门处理货代订单的控制器，位于 `com.purchase.forwarding.order.controller` 包中。它提供了与货代订单相关的RESTful API接口，涵盖了货代订单的创建、查询、更新、状态变更和删除等全生命周期管理。该控制器通过依赖注入 `ForwarderOrderService` 来执行具体的业务逻辑，并利用Spring Security的 `@PreAuthorize` 注解进行严格的权限控制，确保不同角色（货代、买家、卖家、管理员）的用户只能执行其被授权的操作。

## 2. 核心功能

*   **货代订单创建**: 允许货代、管理员、卖家、买家创建货代订单。
*   **货代订单查询**: 提供多维度查询接口，支持按订单ID、订单编号、需求ID、竞价ID、采购订单ID等条件获取订单详情。同时支持分页查询订单列表，并根据用户角色（货代、创建者）进行数据过滤。
*   **货代订单更新**: 允许货代和管理员更新货代订单的详细信息。
*   **货代订单状态变更**: 允许货代和管理员更新货代订单的状态。
*   **货代订单删除**: 允许货代和管理员逻辑删除货代订单。
*   **权限控制**: 所有接口都通过 `@PreAuthorize` 注解进行严格的权限控制，确保只有授权用户才能访问和操作。
*   **统一响应**: 所有接口都返回一个标准化的 `Result` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **日志记录**: 对所有接收到的请求都记录了详细的日志，包括请求参数，便于系统监控和调试。

## 3. 接口说明

### 3.1 货代订单操作接口

#### createOrder - 创建货代订单
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/forwarder/orders`
*   **权限**: `hasAnyAuthority('forwarder', 'admin', 'seller', 'buyer')`
*   **参数**:
    *   `request` (CreateForwarderOrderRequest, body, required): 包含创建货代订单所需信息的请求体。
*   **返回值**: `Result<ForwarderOrderVO>` - 创建成功的货代订单信息。
*   **业务逻辑**: 接收创建请求，调用 `forwarderOrderService.createOrder` 处理业务逻辑，并返回结果。捕获 `BusinessException` 和其他异常，返回统一的错误信息。

#### updateOrder - 更新货代订单
*   **HTTP方法**: `PUT`
*   **路径**: `/api/v1/forwarder/orders`
*   **权限**: `hasAnyAuthority('forwarder', 'admin')`
*   **参数**:
    *   `request` (UpdateForwarderOrderRequest, body, required): 包含更新货代订单所需信息的请求体。
*   **返回值**: `Result<ForwarderOrderVO>` - 更新后的货代订单信息。
*   **业务逻辑**: 接收更新请求，调用 `forwarderOrderService.updateOrder` 处理业务逻辑，并返回结果。捕获 `BusinessException` 和其他异常，返回统一的错误信息。

#### updateOrderStatus - 更新订单状态
*   **HTTP方法**: `PUT`
*   **路径**: `/api/v1/forwarder/orders/{id}/status`
*   **权限**: `hasAnyAuthority('forwarder', 'admin')`
*   **参数**:
    *   `id` (Long, path, required): 订单ID。
    *   `status` (String, query, required): 新的订单状态。
*   **返回值**: `Result<ForwarderOrderVO>` - 更新状态后的货代订单信息。
*   **业务逻辑**: 校验参数，调用 `forwarderOrderService.updateOrderStatus` 处理业务逻辑，并返回结果。捕获 `BusinessException` 和其他异常，返回统一的错误信息。

#### deleteOrder - 删除货代订单
*   **HTTP方法**: `DELETE`
*   **路径**: `/api/v1/forwarder/orders/{id}`
*   **权限**: `hasAnyAuthority('forwarder', 'admin')`
*   **参数**:
    *   `id` (Long, path, required): 订单ID。
*   **返回值**: `Result<Boolean>` - 删除结果。
*   **业务逻辑**: 校验参数，调用 `forwarderOrderService.deleteOrder` 处理业务逻辑，并返回结果。捕获 `BusinessException` 和其他异常，返回统一的错误信息。

### 3.2 货代订单查询接口

#### getOrderList - 获取货代订单列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarder/orders`
*   **权限**: `hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')`
*   **参数**:
    *   `page` (int, query, optional, default=1): 页码。
    *   `size` (int, query, optional, default=10): 每页大小。
    *   `forwarderId` (Long, query, optional): 货代ID。
    *   `creatorId` (Long, query, optional): 创建者ID。
    *   `status` (String, query, optional): 订单状态。
*   **返回值**: `Result<IPage<ForwarderOrderVO>>` - 货代订单分页列表。
*   **业务逻辑**: 校验分页参数，调用 `forwarderOrderService.getOrderPage` 获取订单列表。

#### getMyOrders - 获取当前登录货代的订单列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarder/orders/my`
*   **权限**: `hasAuthority('forwarder')`
*   **参数**:
    *   `page` (int, query, optional, default=1): 页码。
    *   `size` (int, query, optional, default=10): 每页大小。
    *   `status` (String, query, optional): 订单状态。
*   **返回值**: `Result<IPage<ForwarderOrderVO>>` - 货代订单分页列表。
*   **业务逻辑**: 获取当前登录货代的ID，调用 `forwarderOrderService.getOrderPage` 获取订单列表。

#### getMyCreatedOrders - 获取当前登录用户创建的订单列表
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarder/orders/created`
*   **权限**: `hasAnyAuthority('buyer', 'seller', 'admin')`
*   **参数**:
    *   `page` (int, query, optional, default=1): 页码。
    *   `size` (int, query, optional, default=10): 每页大小。
    *   `status` (String, query, optional): 订单状态。
*   **返回值**: `Result<IPage<ForwarderOrderVO>>` - 货代订单分页列表。
*   **业务逻辑**: 获取当前登录用户ID，调用 `forwarderOrderService.getOrderPage` 获取订单列表。

#### getOrderById - 获取货代订单详情
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarder/orders/{id}`
*   **权限**: `hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')`
*   **参数**:
    *   `id` (Long, path, required): 订单ID。
*   **返回值**: `Result<ForwarderOrderVO>` - 货代订单详情。
*   **业务逻辑**: 校验参数，调用 `forwarderOrderService.getOrderById` 获取订单详情。

#### getOrderByNumber - 获取订单号对应的订单
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarder/orders/number/{orderNumber}`
*   **权限**: `hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')`
*   **参数**:
    *   `orderNumber` (String, path, required): 订单编号。
*   **返回值**: `Result<ForwarderOrderVO>` - 货代订单详情。
*   **业务逻辑**: 校验参数，调用 `forwarderOrderService.getOrderByNumber` 获取订单详情。

#### getOrderByRequirementId - 根据需求ID获取订单
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarder/orders/requirement/{requirementId}`
*   **权限**: `hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')`
*   **参数**:
    *   `requirementId` (Long, path, required): 需求ID。
*   **返回值**: `Result<ForwarderOrderVO>` - 货代订单详情。
*   **业务逻辑**: 校验参数，调用 `forwarderOrderService.getOrderByRequirementId` 获取订单详情。

#### getOrderByBiddingId - 根据竞价ID获取订单
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarder/orders/bidding/{biddingId}`
*   **权限**: `hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')`
*   **参数**:
    *   `biddingId` (Long, path, required): 竞价ID。
*   **返回值**: `Result<ForwarderOrderVO>` - 货代订单详情。
*   **业务逻辑**: 校验参数，调用 `forwarderOrderService.getOrderByBiddingId` 获取订单详情。

#### getOrderByPurchaseOrderId - 根据采购订单ID获取货代订单
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/forwarder/orders/purchase/{purchaseOrderId}`
*   **权限**: `hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')`
*   **参数**:
    *   `purchaseOrderId` (Long, path, required): 采购订单ID。
*   **返回值**: `Result<ForwarderOrderVO>` - 货代订单详情。
*   **业务逻辑**: 校验参数，调用 `forwarderOrderService.getOrderByPurchaseOrderId` 获取订单详情。

## 4. 业务规则

*   **权限控制**: 所有接口都通过 `@PreAuthorize` 注解进行严格的权限控制，确保不同角色用户只能访问其被授权的资源和操作。
*   **参数校验**: 对分页参数（`page`, `size`）和路径变量进行了基本的非空和范围校验。
*   **统一响应**: 所有接口都返回 `Result` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **日志记录**: 对所有接收到的请求都记录了详细的日志，包括请求参数，便于系统监控和调试。
*   **职责分离**: 控制器仅负责请求的接收、参数的初步校验和转发，具体的业务逻辑和数据处理委托给 `ForwarderOrderService`。
*   **安全上下文**: 控制器通过 `SecurityContextUtil.getCurrentUserId()` 获取当前登录用户ID，确保操作的归属和权限。

## 5. 使用示例

```java
// 1. 前端 (React) 创建货代订单
async function createForwarderOrder(orderData) {
  try {
    const response = await axios.post('/api/v1/forwarder/orders', orderData, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('货代订单创建成功:', response.data.data);
  } catch (error) {
    console.error('货代订单创建失败:', error.response.data);
  }
}

// 2. 货代获取自己的订单列表
async function fetchMyForwarderOrders() {
  try {
    const response = await axios.get('/api/v1/forwarder/orders/my', {
      params: { page: 1, size: 10, status: 'processing' },
      headers: { Authorization: `Bearer ${forwarderToken}` }
    });
    console.log('我的货代订单:', response.data.data);
  } catch (error) {
    console.error('获取我的货代订单失败:', error.response.data);
  }
}

// 3. 管理员更新货代订单状态
async function updateForwarderOrderStatus(orderId, newStatus) {
  try {
    const response = await axios.put(`/api/v1/forwarder/orders/${orderId}/status`, null, {
      params: { status: newStatus },
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('货代订单状态更新成功:', response.data.data);
  } catch (error) {
    console.error('货代订单状态更新失败:', error.response.data);
  }
}

// 4. 测试示例 (MockMvc)
@SpringBootTest
@AutoConfigureMockMvc
class ForwarderOrderControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ForwarderOrderService forwarderOrderService;

    @BeforeEach
    void setUp() {
        // 模拟 SecurityContextUtil 返回一个货代用户ID
        try (MockedStatic<SecurityContextUtil> mocked = Mockito.mockStatic(SecurityContextUtil.class)) {
            mocked.when(SecurityContextUtil::getCurrentUserId).thenReturn(100L);
            mocked.when(SecurityContextUtil::getCurrentUserRole).thenReturn("forwarder");
            mocked.when(() -> SecurityContextUtil.hasAuthority(anyString())).thenReturn(true);
        }
    }

    @Test
    @WithMockUser(authorities = "forwarder")
    void testCreateOrder_Success() throws Exception {
        CreateForwarderOrderRequest request = new CreateForwarderOrderRequest();
        request.setRequirementId(1L);
        request.setForwarderId(100L);
        // ... set other fields ...

        ForwarderOrderVO mockOrderVO = new ForwarderOrderVO();
        mockOrderVO.setOrderNumber("FWO-001");
        when(forwarderOrderService.createOrder(any(CreateForwarderOrderRequest.class))).thenReturn(mockOrderVO);

        mockMvc.perform(post("/api/v1/forwarder/orders")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.orderNumber").value("FWO-001"));
    }

    @Test
    @WithMockUser(authorities = "forwarder")
    void testGetOrderList_Success() throws Exception {
        IPage<ForwarderOrderVO> mockPage = new Page<>();
        when(forwarderOrderService.getOrderPage(anyInt(), anyInt(), any(), any(), anyString())).thenReturn(mockPage);

        mockMvc.perform(get("/api/v1/forwarder/orders")
                .param("page", "1").param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(authorities = "admin")
    void testUpdateOrderStatus_Success() throws Exception {
        Long orderId = 123L;
        String newStatus = "completed";
        ForwarderOrderVO mockOrderVO = new ForwarderOrderVO();
        mockOrderVO.setId(orderId);
        mockOrderVO.setStatus(newStatus);
        when(forwarderOrderService.updateOrderStatus(eq(orderId), eq(newStatus))).thenReturn(mockOrderVO);

        mockMvc.perform(put("/api/v1/forwarder/orders/{id}/status", orderId)
                .param("status", newStatus))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.status").value(newStatus));
    }
}
```

## 6. 注意事项

*   **权限控制**: 所有接口都通过 `@PreAuthorize` 进行严格的权限控制，确保只有授权用户才能访问和操作。这是系统安全的关键。
*   **参数校验**: 对分页参数（`page`, `size`）和路径变量进行了基本的非空和范围校验。请求体中的DTO通过 `@Validated` 注解进行JSR-303校验。
*   **统一响应**: 所有接口都返回 `Result` 对象，统一了成功和失败的响应格式，便于前端处理。
*   **日志记录**: 控制器中使用了 `log.info` 记录请求参数，这对于监控和问题排查非常重要。
*   **职责分离**: 控制器仅负责请求的接收、参数的初步校验和转发，具体的业务逻辑和数据处理委托给 `ForwarderOrderService`。
*   **安全上下文**: 控制器通过 `SecurityContextUtil.getCurrentUserId()` 获取当前登录用户ID，确保操作的归属和权限。
*   **异常处理**: 控制器捕获了 `BusinessException` 和其他通用异常，并返回统一的错误信息，提高了API的健壮性。
*   **多维度查询**: 提供了多种查询入口（按ID、编号、需求ID、竞价ID、采购订单ID），满足了不同业务场景的需求。
