# ForwarderOrderMapper.md

## 1. 文件概述

`ForwarderOrderMapper.java` 是货代模块中的一个MyBatis-Plus Mapper接口，位于 `com.purchase.forwarding.order.mapper` 包中。它继承自MyBatis-Plus的 `BaseMapper<ForwarderOrder>` 接口，并在此基础上定义了多个自定义的查询方法。`ForwarderOrder` 实体代表了货代订单。该Mapper接口是货代订单服务层与数据库进行交互的桥梁，负责将业务对象的操作转换为SQL语句，实现货代订单的持久化管理和多维度查询。

## 2. 核心功能

*   **基础CRUD操作**: 继承 `BaseMapper`，自动拥有对 `ForwarderOrder` 实体进行插入（`insert`）、根据ID查询（`selectById`）、根据条件查询列表（`selectList`）、更新（`updateById`）和删除（`deleteById`）等基础的增删改查功能。
*   **分页查询**: 提供了 `selectOrderPage` 方法，支持按货代ID、创建者ID和订单状态进行分页查询。
*   **多维度关联查询**: 提供了 `selectByRequirementId`（按货代需求ID）、`selectByBiddingId`（按竞价ID）、`selectByOrderNumber`（按订单编号）和 `selectByPurchaseOrderId`（按采购订单ID）等多种查询方法，支持从不同业务维度获取货代订单记录。
*   **逻辑删除过滤**: 继承 `BaseMapper` 后，如果 `ForwarderOrder` 实体配置了 `@TableLogic`，则所有查询都会自动过滤逻辑删除的记录。

## 3. 接口说明

`ForwarderOrderMapper` 在继承 `BaseMapper` 的基础上，定义了以下自定义方法：

### 3.1 查询方法

#### selectOrderPage - 分页查询货代订单列表
*   **方法签名**: `IPage<ForwarderOrder> selectOrderPage(Page<ForwarderOrder> page, @Param("forwarderId") Long forwarderId, @Param("creatorId") Long creatorId, @Param("status") String status)`
*   **描述**: 根据货代ID、创建者ID和订单状态进行分页查询货代订单列表。
*   **参数**:
    *   `page` (Page<ForwarderOrder>): MyBatis-Plus提供的分页对象。
    *   `forwarderId` (Long, optional): 货代用户ID。
    *   `creatorId` (Long, optional): 订单创建者用户ID。
    *   `status` (String, optional): 订单状态。
*   **返回值**: `IPage<ForwarderOrder>` - 包含分页信息和 `ForwarderOrder` 实体列表的MyBatis-Plus分页对象。
*   **SQL**: 通常需要在XML Mapper文件中编写动态SQL，根据传入参数构建查询条件。

#### selectByRequirementId - 根据货代需求ID查询相关订单
*   **方法签名**: `ForwarderOrder selectByRequirementId(@Param("requirementId") Long requirementId)`
*   **描述**: 根据货代需求ID查询关联的货代订单。通常一个货代需求只对应一个货代订单。
*   **参数**:
    *   `requirementId` (Long): 货代需求ID。
*   **返回值**: `ForwarderOrder` - 匹配的货代订单实体，如果不存在则返回 `null`。
*   **SQL**: `@Select("SELECT * FROM forwarder_order WHERE requirement_id = #{requirementId}")`

#### selectByBiddingId - 根据竞价ID查询相关订单
*   **方法签名**: `ForwarderOrder selectByBiddingId(@Param("biddingId") Long biddingId)`
*   **描述**: 根据竞价ID查询关联的货代订单。通常一个竞价只对应一个货代订单。
*   **参数**:
    *   `biddingId` (Long): 竞价ID。
*   **返回值**: `ForwarderOrder` - 匹配的货代订单实体，如果不存在则返回 `null`。
*   **SQL**: `@Select("SELECT * FROM forwarder_order WHERE bidding_id = #{biddingId}")`

#### selectByOrderNumber - 根据订单编号查询订单
*   **方法签名**: `ForwarderOrder selectByOrderNumber(@Param("orderNumber") String orderNumber)`
*   **描述**: 根据订单编号查询货代订单。
*   **参数**:
    *   `orderNumber` (String): 订单编号。
*   **返回值**: `ForwarderOrder` - 匹配的货代订单实体，如果不存在则返回 `null`。
*   **SQL**: `@Select("SELECT * FROM forwarder_order WHERE order_number = #{orderNumber}")`

#### selectByPurchaseOrderId - 根据采购订单ID查询相关货代订单
*   **方法签名**: `ForwarderOrder selectByPurchaseOrderId(@Param("purchaseOrderId") Long purchaseOrderId)`
*   **描述**: 根据采购订单ID查询关联的货代订单。通常一个采购订单可能关联一个或多个货代订单。
*   **参数**:
    *   `purchaseOrderId` (Long): 采购订单ID。
*   **返回值**: `ForwarderOrder` - 匹配的货代订单实体，如果不存在则返回 `null`。
*   **SQL**: `@Select("SELECT * FROM forwarder_order WHERE purchase_order_id = #{purchaseOrderId}")`

## 4. 业务规则

*   **MyBatis-Plus集成**: 继承 `BaseMapper` 使得该Mapper自动拥有强大的CRUD能力，减少了重复代码。
*   **参数绑定**: 使用 `@Param` 注解将Java方法参数绑定到SQL中的命名参数，有效防止SQL注入。
*   **性能优化**: 考虑到货代订单数据量可能较大，Mapper中的SQL查询必须高度优化。应为 `requirement_id`, `bidding_id`, `order_number`, `purchase_order_id`, `forwarder_id`, `creator_id`, `status` 等常用查询字段建立合适的索引。
*   **事务管理**: 在服务层调用Mapper方法时，必须确保在事务中执行，以保证数据操作的原子性和一致性。
*   **逻辑删除**: 如果 `ForwarderOrder` 实体配置了 `@TableLogic`，则所有通过 `BaseMapper` 和自定义SQL查询都会自动过滤逻辑删除的记录。
*   **唯一性**: `order_number` 字段在 `forwarder_order` 表中通常应该是唯一的，以确保订单编号的唯一性。

## 5. 使用示例

```java
// 1. 在 ForwarderOrderService 实现中查询货代订单
@Service
public class ForwarderOrderServiceImpl extends ServiceImpl<ForwarderOrderMapper, ForwarderOrder>
        implements ForwarderOrderService {
    @Autowired
    private ForwarderOrderMapper forwarderOrderMapper;

    @Override
    public IPage<ForwarderOrderVO> getOrderPage(int page, int size, Long forwarderId, Long creatorId, String status) {
        Page<ForwarderOrder> mpPage = new Page<>(page, size);
        IPage<ForwarderOrder> resultPage = forwarderOrderMapper.selectOrderPage(mpPage, forwarderId, creatorId, status);
        return resultPage.convert(this::convertToVO);
    }

    @Override
    public ForwarderOrderVO getOrderByNumber(String orderNumber) {
        ForwarderOrder order = forwarderOrderMapper.selectByOrderNumber(orderNumber);
        return order != null ? convertToVO(order) : null;
    }

    @Override
    public ForwarderOrderVO getOrderByRequirementId(Long requirementId) {
        ForwarderOrder order = forwarderOrderMapper.selectByRequirementId(requirementId);
        return order != null ? convertToVO(order) : null;
    }
}

// 2. 对应的 XML Mapper 文件 (ForwarderOrderMapper.xml) 示例
<mapper namespace="com.purchase.forwarding.order.mapper.ForwarderOrderMapper">
    <select id="selectOrderPage" resultType="com.purchase.forwarding.order.entity.ForwarderOrder">
        SELECT * FROM forwarder_order
        <where>
            deleted = '0'
            <if test="forwarderId != null">
                AND forwarder_id = #{forwarderId}
            </if>
            <if test="creatorId != null">
                AND creator_id = #{creatorId}
            </if>
            <if test="status != null and status != ''">
                AND order_status = #{status}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>
</mapper>

// 3. 测试示例
@SpringBootTest
class ForwarderOrderMapperTest {
    @Autowired
    private ForwarderOrderMapper forwarderOrderMapper;

    @Test
    @Transactional
    void testSelectOrderPage() {
        // 插入测试数据
        ForwarderOrder order1 = new ForwarderOrder(); order1.setId(1L); order1.setOrderNumber("FWO001"); order1.setForwarderId(10L); order1.setCreatorId(100L); order1.setOrderStatus("pending"); order1.setDeleted("0"); forwarderOrderMapper.insert(order1);
        ForwarderOrder order2 = new ForwarderOrder(); order2.setId(2L); order2.setOrderNumber("FWO002"); order2.setForwarderId(10L); order2.setCreatorId(101L); order2.setOrderStatus("completed"); order2.setDeleted("0"); forwarderOrderMapper.insert(order2);

        Page<ForwarderOrder> page = new Page<>(1, 10);
        IPage<ForwarderOrder> result = forwarderOrderMapper.selectOrderPage(page, 10L, null, "pending");
        assertThat(result.getRecords()).hasSize(1);
        assertThat(result.getRecords().get(0).getOrderNumber()).isEqualTo("FWO001");
    }

    @Test
    @Transactional
    void testSelectByOrderNumber() {
        ForwarderOrder order = new ForwarderOrder(); order.setId(3L); order.setOrderNumber("FWO003"); order.setDeleted("0"); forwarderOrderMapper.insert(order);
        ForwarderOrder found = forwarderOrderMapper.selectByOrderNumber("FWO003");
        assertThat(found).isNotNull();
        assertThat(found.getId()).isEqualTo(3L);
    }
}
```

## 6. 注意事项

*   **MyBatis-Plus集成**: 继承 `BaseMapper` 使得该Mapper自动拥有强大的CRUD能力，减少了重复代码。
*   **XML与注解**: `selectOrderPage` 方法通常在XML文件中实现，以支持复杂的动态SQL。其他简单的查询则可以直接使用 `@Select` 注解。
*   **参数绑定**: 使用 `@Param` 注解将Java方法参数绑定到SQL中的命名参数，有效防止SQL注入。
*   **性能优化**: 考虑到货代订单数据量可能较大，Mapper中的SQL查询必须高度优化。应为 `requirement_id`, `bidding_id`, `order_number`, `purchase_order_id`, `forwarder_id`, `creator_id`, `order_status` 等常用查询字段建立合适的索引。
*   **事务管理**: 在服务层调用Mapper方法时，必须确保在事务中执行，以保证数据操作的原子性和一致性。
*   **逻辑删除**: 所有查询都应考虑逻辑删除，确保只查询未删除的记录。
*   **唯一性**: `order_number` 字段在数据库中应建立唯一索引。