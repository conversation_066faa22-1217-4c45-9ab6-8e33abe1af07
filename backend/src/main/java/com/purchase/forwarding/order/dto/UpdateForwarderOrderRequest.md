## 类用途
更新转发订单请求DTO，用于修改已创建订单的可变字段

## 字段说明
| 字段名 | 类型 | 必填 | 校验规则 | 描述 |
|-------|------|-----|---------|-----|
| orderId | String | 是 | 长度≤32 | 订单ID |
| expectedDeliveryDate | LocalDate | 否 | 未来日期 | 新交付日期 |
| paymentTerms | String | 否 | 长度≤100 | 更新付款条款 |
| version | Integer | 是 | ≥1 | 乐观锁版本号 |
| updateReason | String | 是 | 长度≤200 | 变更原因说明 |

## 业务规则
- 仅允许更新状态为CREATED或APPROVED的订单
- 交付日期修改需触发物流计划重算
- 每次更新必须提供version防止并发冲突
- 记录完整的变更历史

## 状态限制
```mermaid
stateDiagram-v2
    [*] --> CREATED: 允许更新
    CREATED --> APPROVED: 允许更新
    APPROVED --> PROCESSING: 仅更新物流
    PROCESSING --> DELIVERED: 不可更新
```

## 使用示例
```java
UpdateForwarderOrderRequest request = new UpdateForwarderOrderRequest(
    "order789",
    LocalDate.now().plusDays(7),
    "现金付款",
    2,
    "供应商要求提前交付");
```

## 注意事项
- 重要字段变更需审批流程
- 生产环境建议添加修改频率限制
- 需验证订单归属权限