package com.purchase.forwarding.order.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 创建货代订单请求
 */
@Data
public class CreateForwarderOrderRequest {
    /**
     * 货代需求ID
     */
    @NotNull(message = "货代需求ID不能为空")
    private Long requirementId;
    
    /**
     * 竞价ID
     */
    @NotNull(message = "竞价ID不能为空")
    private Long biddingId;
    
    /**
     * 采购订单ID - 关联的采购订单
     */
    private Long purchaseOrderId;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
    
    /**
     * 创建者名称
     */
    private String creatorName;
    
    /**
     * 创建者角色
     */
    private String creatorRole;
    
    /**
     * 船名/航班号
     */
    private String vesselName;
    
    /**
     * 航次号
     */
    private String voyageNumber;
    
    /**
     * 预计离港时间(ETD)
     */
    private LocalDateTime etd;
    
    /**
     * 预计到港时间(ETA)
     */
    private LocalDateTime eta;
    
    /**
     * 提单号
     */
    private String billOfLadingNumber;
    
    /**
     * 提单类型(电放/正本)
     */
    private String billOfLadingType;
    
    /**
     * 备注
     */
    private String remarks;
    
    // ========== 费用明细字段 ==========
    
    /**
     * 出口包装费 (卖方承担)
     */
    private BigDecimal costExportPacking;
    
    /**
     * 起运地内陆运输费 (卖方承担)
     */
    private BigDecimal costOriginInlandHaulage;
    
    /**
     * 出口报关及单证费 (卖方承担)
     */
    private BigDecimal costExportCustomsDocs;
    
    /**
     * 起运地操作费 (卖方承担)
     */
    private BigDecimal costOriginHandling;
    
    /**
     * 主运费 (卖方承担部分)
     */
    private BigDecimal costMainFreight;
    
    /**
     * 运费附加费 (卖方承担部分)
     */
    private BigDecimal costFreightSurcharges;
    
    /**
     * 货物运输保险费 (卖方支付的保费)
     */
    private BigDecimal costCargoInsurance;
    
    /**
     * 目的地操作费 (卖方承担)
     */
    private BigDecimal costDestinationHandlingBySeller;
    
    /**
     * 目的地卸货费 (卖方承担)
     */
    private BigDecimal costDestinationUnloadingBySeller;
    
    /**
     * 目的地内陆运输费 (卖方承担)
     */
    private BigDecimal costDestinationInlandHaulageBySeller;
    
    /**
     * 进口报关及单证费 (卖方承担-DDP)
     */
    private BigDecimal costImportCustomsDocsBySeller;
    
    /**
     * 进口关税及税费 (卖方承担-DDP)
     */
    private BigDecimal costImportDutiesTaxesBySeller;
    
    /**
     * 货代服务费/操作费 (卖方承担)
     */
    private BigDecimal costForwarderServiceFee;
    
    /**
     * 自定义费用项目 JSON数组
     * 格式: [{"name":"费用名称","amount":金额,"description":"费用说明","currency":"货币"}]
     */
    private String customedCosts;
} 