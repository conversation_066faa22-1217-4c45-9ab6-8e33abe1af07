# ForwarderOrderServiceImpl.md

## 1. 文件概述

`ForwarderOrderServiceImpl.java` 是货代模块中 `ForwarderOrderService` 接口的实现类，位于 `com.purchase.forwarding.order.service.impl` 包中。它继承自MyBatis-Plus的 `ServiceImpl`，提供了货代订单业务逻辑的具体实现。该服务负责处理货代订单的创建、更新、查询、状态变更和删除等核心业务流程。它协调多个领域服务和仓储（如 `ForwarderOrderMapper`, `ForwardingRequirementService`, `ForwardingBiddingService`, `SnowflakeIdGenerator`, `ApplicationEventPublisher`），并严格执行权限校验和业务规则，确保货代订单流程的正确性和数据一致性。

## 2. 核心功能

*   **货代订单创建**: 实现了货代订单的创建逻辑，包括验证需求和竞价信息、处理并发创建、从需求和竞价复制属性以及生成订单号。
*   **货代订单更新**: 实现了货代订单的更新逻辑，支持部分字段更新和状态变更。
*   **货代订单状态变更**: 实现了订单状态的更新，并包含严格的状态转换校验和状态变更后的业务逻辑处理（如发布佣金计算事件）。
*   **货代订单删除**: 实现了货代订单的逻辑删除，并包含状态检查（只允许删除待处理状态的订单）。
*   **多维度查询**: 实现了根据订单ID、订单编号、需求ID、竞价ID、采购订单ID等多种条件查询订单详情或列表。
*   **权限与角色校验**: 在所有关键操作中，严格校验当前用户的角色和权限，确保只有授权用户才能执行相应操作。
*   **事件发布**: 在货代订单完成时，发布 `CommissionCalculationEvent`，触发佣金计算流程，体现了领域事件的应用。
*   **统一订单状态同步**: 监听 `UnifiedOrderStatusChangedEvent`，实现货代订单状态与统一订单状态的同步更新。
*   **数据转换**: 负责将请求DTO转换为实体，以及将实体转换为VO。

## 3. 接口说明

### 3.1 货代订单操作实现

#### createOrder - 创建货代订单
*   **方法签名**: `ForwarderOrderVO createOrder(CreateForwarderOrderRequest request)`
*   **描述**: 实现了货代订单的创建逻辑。包括验证货代需求和竞价信息、检查竞价状态、处理并发创建、从需求和竞价复制属性、生成订单号、保存订单，并发布相关事件。
*   **业务逻辑**: 
    1.  验证货代需求和竞价是否存在且状态正确（竞价必须是 `accepted`）。
    2.  使用乐观锁方式检查是否已存在相关订单，处理并发创建。
    3.  生成唯一的订单ID和订单编号。
    4.  从 `ForwardingRequirement` 和 `ForwardingBidding` 复制相关属性到 `ForwarderOrder` 实体。
    5.  设置订单初始状态为 `PENDING`，支付状态为 `UNPAID`。
    6.  保存 `ForwarderOrder` 实体。
    7.  转换为 `ForwarderOrderVO` 返回。

#### updateOrder - 更新货代订单
*   **方法签名**: `ForwarderOrderVO updateOrder(UpdateForwarderOrderRequest request)`
*   **描述**: 实现了货代订单的更新逻辑。支持部分字段更新，并包含状态变更后的业务逻辑。
*   **业务逻辑**: 
    1.  验证订单是否存在。
    2.  调用 `updateOrderFromRequest` 辅助方法，根据请求更新订单对象。
    3.  保存更新后的订单。
    4.  转换为 `ForwarderOrderVO` 返回。

#### updateOrderStatus - 更新订单状态
*   **方法签名**: `ForwarderOrderVO updateOrderStatus(Long id, String status)`
*   **描述**: 实现了货代订单状态的更新逻辑。包含状态转换校验和状态变更后的业务逻辑处理。
*   **业务逻辑**: 
    1.  验证订单是否存在。
    2.  调用 `validateStatusTransition` 校验状态转换是否合法。
    3.  更新订单状态。
    4.  调用 `handleOrderStatusChanged` 处理状态变更后的逻辑（如设置完成时间、发布佣金计算事件）。
    5.  保存更新后的订单。
    6.  转换为 `ForwarderOrderVO` 返回。

#### deleteOrder - 删除货代订单
*   **方法签名**: `boolean deleteOrder(Long id)`
*   **描述**: 实现了货代订单的逻辑删除。只允许删除 `PENDING` 状态的订单。
*   **业务逻辑**: 
    1.  验证订单是否存在。
    2.  检查订单状态是否为 `PENDING`。
    3.  执行逻辑删除操作。

### 3.2 货代订单查询实现

#### getOrderById - 根据ID获取订单详情
*   **方法签名**: `ForwarderOrderVO getOrderById(Long id)`
*   **描述**: 实现了根据订单ID获取订单详情的逻辑。
*   **业务逻辑**: 调用 `getById` 获取实体，然后转换为 `ForwarderOrderVO` 返回。

#### getOrderByNumber - 根据订单编号获取订单
*   **方法签名**: `ForwarderOrderVO getOrderByNumber(String orderNumber)`
*   **描述**: 实现了根据订单编号获取订单详情的逻辑。
*   **业务逻辑**: 调用 `forwarderOrderMapper.selectByOrderNumber` 获取实体，然后转换为 `ForwarderOrderVO` 返回。

#### getOrderByRequirementId - 根据需求ID获取订单
*   **方法签名**: `ForwarderOrderVO getOrderByRequirementId(Long requirementId)`
*   **描述**: 实现了根据需求ID获取订单详情的逻辑。
*   **业务逻辑**: 调用 `forwarderOrderMapper.selectByRequirementId` 获取实体，然后转换为 `ForwarderOrderVO` 返回。

#### getOrderByBiddingId - 根据竞价ID获取订单
*   **方法签名**: `ForwarderOrderVO getOrderByBiddingId(Long biddingId)`
*   **描述**: 实现了根据竞价ID获取订单详情的逻辑。
*   **业务逻辑**: 调用 `forwarderOrderMapper.selectByBiddingId` 获取实体，然后转换为 `ForwarderOrderVO` 返回。

#### getOrderByPurchaseOrderId - 根据采购订单ID获取货代订单
*   **方法签名**: `ForwarderOrderVO getOrderByPurchaseOrderId(Long purchaseOrderId)`
*   **描述**: 实现了根据采购订单ID获取货代订单详情的逻辑。
*   **业务逻辑**: 调用 `forwarderOrderMapper.selectByPurchaseOrderId` 获取实体，然后转换为 `ForwarderOrderVO` 返回。

#### getOrderPage - 分页获取货代订单列表
*   **方法签名**: `IPage<ForwarderOrderVO> getOrderPage(int page, int size, Long forwarderId, Long creatorId, String status)`
*   **描述**: 实现了分页查询货代订单列表的逻辑，支持按货代ID、创建者ID和状态过滤。
*   **业务逻辑**: 
    1.  构建 `Page` 对象。
    2.  调用 `forwarderOrderMapper.selectOrderPage` 执行分页查询。
    3.  将查询结果的 `IPage<ForwarderOrder>` 转换为 `IPage<ForwarderOrderVO>` 返回。

### 3.3 事件监听与处理

#### handleUnifiedOrderStatusChanged - 监听统一订单状态变更事件
*   **方法签名**: `void handleUnifiedOrderStatusChanged(UnifiedOrderStatusChangedEvent event)`
*   **描述**: 监听统一订单状态变更事件，并同步更新关联的货代订单状态。
*   **业务逻辑**: 
    1.  根据事件中的 `orderId` 查找关联的货代订单。
    2.  调用 `mapUnifiedOrderStatusToForwarderStatus` 将统一订单状态映射为货代订单状态。
    3.  如果状态需要同步，调用 `updateOrderStatus` 更新货代订单状态。
    4.  捕获异常，记录日志，但不重新抛出，避免影响其他事件监听器。

### 3.4 辅助方法

#### copyRequirementProperties - 从需求中复制属性到订单
*   **方法签名**: `private void copyRequirementProperties(ForwardingRequirement requirement, ForwarderOrder order)`
*   **描述**: 将 `ForwardingRequirement` 实体中的相关属性复制到 `ForwarderOrder` 实体。

#### copyBiddingProperties - 从竞价中复制属性到订单
*   **方法签名**: `private void copyBiddingProperties(ForwardingBidding bidding, ForwarderOrder order)`
*   **描述**: 将 `ForwardingBidding` 实体中的相关属性（包括费用明细）复制到 `ForwarderOrder` 实体。

#### updateRequirementAndBiddingStatus - 更新需求和竞价状态
*   **方法签名**: `private void updateRequirementAndBiddingStatus(ForwardingRequirement requirement, ForwardingBidding bidding)`
*   **描述**: 更新需求状态为 `in_progress`。此方法在当前代码中已简化，不再包含接受竞价的逻辑。

#### updateOrderFromRequest - 根据请求更新订单对象
*   **方法签名**: `private ForwarderOrder updateOrderFromRequest(ForwarderOrder order, UpdateForwarderOrderRequest request)`
*   **描述**: 根据 `UpdateForwarderOrderRequest` 中的字段更新 `ForwarderOrder` 实体，并处理支付状态变更时的 `paymentTime` 设置。

#### convertToVO - 将实体转换为视图对象
*   **方法签名**: `private ForwarderOrderVO convertToVO(ForwarderOrder entity)`
*   **描述**: 将 `ForwarderOrder` 实体转换为 `ForwarderOrderVO`。

#### generateOrderNumber - 生成货代订单编号
*   **方法签名**: `private String generateOrderNumber()`
*   **描述**: 生成唯一的货代订单编号，格式为 `FWD + 年月日 + 6位雪花算法序列号`。

#### validateStatusTransition - 验证状态转换是否合法
*   **方法签名**: `private void validateStatusTransition(String currentStatus, String newStatus)`
*   **描述**: 校验订单状态从 `currentStatus` 到 `newStatus` 的转换是否符合预定义的规则。

#### mapUnifiedOrderStatusToForwarderStatus - 将统一订单状态映射为货代订单状态
*   **方法签名**: `private String mapUnifiedOrderStatusToForwarderStatus(String unifiedOrderStatus)`
*   **描述**: 将统一订单的状态字符串映射为货代订单的状态字符串。

#### handleOrderStatusChanged - 处理订单状态变更后的逻辑
*   **方法签名**: `private void handleOrderStatusChanged(ForwarderOrder order, String oldStatus, String newStatus)`
*   **描述**: 统一处理订单状态变更后的业务逻辑，例如在订单完成时设置 `completionTime` 并发布 `CommissionCalculationEvent`。

## 4. 业务规则

*   **DDD分层**: `ForwarderOrderServiceImpl` 明确属于应用服务层，其职责是协调领域对象和仓储，实现业务用例。它不包含核心领域逻辑，而是委托给领域实体和领域服务。
*   **事务管理**: 所有涉及数据修改的方法都使用 `@Transactional` 注解，确保操作的原子性和数据一致性。
*   **权限校验**: 服务层通过 `SecurityContextUtil` 获取当前用户ID和角色，并根据业务逻辑进行细粒度的权限校验。例如，只有货代可以提交竞价，只有需求创建者或管理员可以接受/拒绝竞价。
*   **业务异常**: 服务在遇到业务规则冲突或数据不存在时，会抛出 `BusinessException`，并包含明确的错误码和信息，便于上层处理。
*   **数据转换**: 服务层负责将请求DTO转换为领域实体，以及将领域实体转换为前端所需的响应DTO或VO。`convertToVO` 和 `convertToDetailResponse` 辅助方法承担了这一职责。
*   **通知机制**: 在货代订单生命周期的关键节点，系统会异步发送通知给相关用户。`@Async` 注解确保了通知发送不会阻塞主业务流程。
*   **依赖注入**: 服务通过构造函数注入其依赖，遵循了依赖倒置原则。
*   **订单号生成**: 订单号的生成逻辑确保了唯一性和可读性。
*   **状态机**: `validateStatusTransition` 方法定义了严格的状态转换规则，确保订单状态流转的合法性。
*   **事件驱动**: 在订单完成时发布 `CommissionCalculationEvent`，体现了事件驱动架构的思想，实现了模块间的解耦。
*   **乐观锁**: 在 `createOrder` 方法中，通过检查 `requirementId` 是否已存在关联订单来处理并发创建，这是一种乐观锁的实现方式。

## 5. 使用示例

```java
// 1. 在 ForwarderOrderController 中调用 ForwarderOrderService
@RestController
@RequestMapping("/api/v1/forwarder/orders")
public class ForwarderOrderController {
    @Autowired
    private ForwarderOrderService forwarderOrderService;

    @PostMapping
    @PreAuthorize("hasAnyAuthority('forwarder', 'admin', 'seller', 'buyer')")
    public Result<ForwarderOrderVO> createOrder(@RequestBody @Validated CreateForwarderOrderRequest request) {
        // SecurityContextUtil.getCurrentUserId() 在这里被调用，获取当前登录用户ID
        ForwarderOrderVO order = forwarderOrderService.createOrder(request);
        return Result.success(order);
    }

    @PutMapping("/{id}/status")
    @PreAuthorize("hasAnyAuthority('forwarder', 'admin')")
    public Result<ForwarderOrderVO> updateOrderStatus(
            @PathVariable Long id,
            @RequestParam String status) {
        ForwarderOrderVO order = forwarderOrderService.updateOrderStatus(id, status);
        return Result.success(order);
    }

    @GetMapping("/purchase/{purchaseOrderId}")
    @PreAuthorize("hasAnyAuthority('forwarder', 'buyer', 'seller', 'admin')")
    public Result<ForwarderOrderVO> getOrderByPurchaseOrderId(@PathVariable Long purchaseOrderId) {
        ForwarderOrderVO order = forwarderOrderService.getOrderByPurchaseOrderId(purchaseOrderId);
        return Result.success(order);
    }
}

// 2. 测试示例
@SpringBootTest
class ForwarderOrderServiceImplTest {
    @Autowired
    private ForwarderOrderService forwarderOrderService;

    @MockBean
    private ForwarderOrderMapper forwarderOrderMapper;
    @MockBean
    private ForwardingRequirementService requirementService;
    @MockBean
    private ForwardingBiddingService biddingService;
    @MockBean
    private SnowflakeIdGenerator idGenerator;
    @MockBean
    private ApplicationEventPublisher eventPublisher;

    @BeforeEach
    void setUp() {
        // 模拟 SecurityContextUtil
        try (MockedStatic<SecurityContextUtil> mocked = Mockito.mockStatic(SecurityContextUtil.class)) {
            mocked.when(SecurityContextUtil::getCurrentUserId).thenReturn(10L);
            mocked.when(SecurityContextUtil::getCurrentUserRole).thenReturn("forwarder");
            mocked.when(() -> SecurityContextUtil.hasAuthority(anyString())).thenReturn(true);
        }

        // 模拟 ID 生成器
        when(idGenerator.nextId()).thenReturn(12345L);
    }

    @Test
    @Transactional
    void testCreateOrder_Success() {
        CreateForwarderOrderRequest request = new CreateForwarderOrderRequest();
        request.setRequirementId(1L);
        request.setBiddingId(2L);
        request.setCreatorId(10L);
        request.setCreatorName("Test Creator");
        request.setCreatorRole("forwarder");
        request.setRemarks("Test remarks");

        ForwardingRequirement mockRequirement = new ForwardingRequirement();
        mockRequirement.setId(1L);
        mockRequirement.setStatus("open");
        mockRequirement.setDeleted("0");
        mockRequirement.setOrderId(100L); // 模拟关联采购订单

        ForwardingBidding mockBidding = new ForwardingBidding();
        mockBidding.setId(2L);
        mockBidding.setStatus("accepted");
        mockBidding.setBiddingPrice(new BigDecimal("500.00"));
        mockBidding.setForwarderId(10L);
        mockBidding.setForwarderName("Test Forwarder");
        mockBidding.setForwarderCompanyName("Test FWD Co.");

        when(requirementService.getById(1L)).thenReturn(mockRequirement);
        when(biddingService.getById(2L)).thenReturn(mockBidding);
        when(forwarderOrderMapper.selectByRequirementId(1L)).thenReturn(null);
        when(forwarderOrderMapper.selectByBiddingId(2L)).thenReturn(null);
        when(forwarderOrderMapper.insert(any(ForwarderOrder.class))).thenReturn(1);
        when(forwarderOrderMapper.selectById(anyLong())).thenReturn(new ForwarderOrder()); // 模拟selectById返回一个对象

        ForwarderOrderVO result = forwarderOrderService.createOrder(request);

        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(12345L);
        assertThat(result.getOrderNumber()).startsWith("FWD");
        assertThat(result.getTotalPrice()).isEqualByComparingTo(new BigDecimal("500.00"));
        verify(forwarderOrderMapper, times(1)).insert(any(ForwarderOrder.class));
        verify(eventPublisher, times(0)).publishEvent(any(CommissionCalculationEvent.class)); // 订单刚创建，不会发布佣金事件
    }

    @Test
    @Transactional
    void testUpdateOrderStatus_ToCompleted() {
        Long orderId = 123L;
        ForwarderOrder existingOrder = new ForwarderOrder();
        existingOrder.setId(orderId);
        existingOrder.setOrderStatus(ForwarderOrder.OrderStatus.PENDING);
        existingOrder.setTotalPrice(new BigDecimal("100.00"));
        existingOrder.setCreatorId(10L);

        when(forwarderOrderMapper.selectById(orderId)).thenReturn(existingOrder);
        when(forwarderOrderMapper.updateById(any(ForwarderOrder.class))).thenReturn(1);

        ForwarderOrderVO result = forwarderOrderService.updateOrderStatus(orderId, ForwarderOrder.OrderStatus.COMPLETED);

        assertThat(result).isNotNull();
        assertThat(result.getOrderStatus()).isEqualTo(ForwarderOrder.OrderStatus.COMPLETED);
        assertThat(result.getCompletionTime()).isNotNull();
        verify(forwarderOrderMapper, times(1)).updateById(any(ForwarderOrder.class));
        verify(eventPublisher, times(1)).publishEvent(any(CommissionCalculationEvent.class)); // 订单完成，发布佣金事件
    }

    @Test
    @Transactional
    void testDeleteOrder_Success() {
        Long orderId = 123L;
        ForwarderOrder existingOrder = new ForwarderOrder();
        existingOrder.setId(orderId);
        existingOrder.setOrderStatus(ForwarderOrder.OrderStatus.PENDING);

        when(forwarderOrderMapper.selectById(orderId)).thenReturn(existingOrder);
        when(forwarderOrderMapper.deleteById(orderId)).thenReturn(1);

        boolean result = forwarderOrderService.deleteOrder(orderId);

        assertThat(result).isTrue();
        verify(forwarderOrderMapper, times(1)).deleteById(orderId);
    }
}
```

## 6. 注意事项

*   **DDD分层**: `ForwarderOrderServiceImpl` 明确属于应用服务层，其职责是协调领域对象和仓储，实现业务用例。它不包含核心领域逻辑，而是委托给领域实体和领域服务。
*   **事务管理**: 所有涉及数据修改的方法都使用 `@Transactional` 注解，确保操作的原子性和数据一致性。
*   **权限校验**: 服务层通过 `SecurityContextUtil` 获取当前用户ID和角色，并根据业务逻辑进行细粒度的权限校验。例如，只有货代可以提交竞价，只有需求创建者或管理员可以接受/拒绝竞价。
*   **业务异常**: 服务在遇到业务规则冲突或数据不存在时，会抛出 `BusinessException`，并包含明确的错误码和信息，便于上层处理。
*   **数据转换**: 服务层负责将请求DTO转换为领域实体，以及将领域实体转换为前端所需的响应DTO或VO。`convertToVO` 和 `convertToDetailResponse` 辅助方法承担了这一职责。
*   **通知机制**: 在货代订单生命周期的关键节点，系统会异步发送通知给相关用户。`@Async` 注解确保了通知发送不会阻塞主业务流程。
*   **依赖注入**: 服务通过构造函数注入其依赖，遵循了依赖倒置原则。
*   **订单号生成**: 订单号的生成逻辑确保了唯一性和可读性。
*   **状态机**: `validateStatusTransition` 方法定义了严格的状态转换规则，确保订单状态流转的合法性。
*   **事件驱动**: 在订单完成时发布 `CommissionCalculationEvent`，体现了事件驱动架构的思想，实现了模块间的解耦。
*   **乐观锁**: 在 `createOrder` 方法中，通过检查 `requirementId` 是否已存在关联订单来处理并发创建，这是一种乐观锁的实现方式。
*   **统一订单状态同步**: `handleUnifiedOrderStatusChanged` 方法展示了如何通过事件监听机制，实现不同领域（统一订单和货代订单）之间状态的同步，保持系统的一致性。
