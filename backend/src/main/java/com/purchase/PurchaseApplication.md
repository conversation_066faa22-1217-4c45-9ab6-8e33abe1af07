# PurchaseApplication.java

## 文件概述 (File Overview)
`PurchaseApplication.java` 是采购系统的Spring Boot主启动类，位于 `com.purchase` 根包中。该类是整个采购系统应用程序的入口点，负责启动Spring Boot应用容器，初始化所有必要的组件和配置。它整合了原微服务架构中的所有功能模块，包括用户管理、订单处理、竞价系统、消息通讯、数据分析等，形成了一个统一的单体应用架构。

## 核心功能 (Core Functionality)
*   **应用程序启动:** 作为Spring Boot应用的主入口，负责启动整个应用程序。
*   **自动配置启用:** 通过 `@SpringBootApplication` 注解启用Spring Boot的自动配置机制。
*   **事务管理:** 通过 `@EnableTransactionManagement` 注解启用声明式事务管理。
*   **MyBatis映射扫描:** 通过 `@MapperScan` 注解自动扫描和注册MyBatis Mapper接口。
*   **配置属性启用:** 通过 `@EnableConfigurationProperties` 注解启用配置属性绑定。
*   **组件扫描:** 通过 `@ComponentScan` 注解扫描和注册Spring组件。
*   **多层级包扫描:** 支持多层级的Mapper接口扫描，适应复杂的包结构。

## 接口说明 (Interface Description)

### 主要注解配置 (Main Annotation Configuration)

#### @SpringBootApplication
*   **功能:** 组合注解，包含 `@Configuration`、`@EnableAutoConfiguration` 和 `@ComponentScan`
*   **作用:** 启用Spring Boot的自动配置、组件扫描和配置类功能

#### @EnableTransactionManagement
*   **功能:** 启用Spring的声明式事务管理
*   **作用:** 允许在方法上使用 `@Transactional` 注解进行事务控制

#### @MapperScan
*   **功能:** 扫描MyBatis Mapper接口
*   **扫描路径:**
    *   `com.purchase.*.mapper` - 二级包下的mapper
    *   `com.purchase.*.*.mapper` - 三级包下的mapper
    *   `com.purchase.*.*.*.mapper` - 四级包下的mapper
*   **作用:** 自动注册所有Mapper接口为Spring Bean

#### @EnableConfigurationProperties
*   **功能:** 启用配置属性绑定
*   **作用:** 允许使用 `@ConfigurationProperties` 注解绑定配置文件属性

#### @ComponentScan
*   **功能:** 指定组件扫描的基础包
*   **扫描路径:** `com.purchase`
*   **作用:** 扫描并注册所有Spring组件（@Component、@Service、@Repository、@Controller等）

### 主要方法 (Main Methods)

#### main(String[] args)
*   **参数:** `args` - 命令行参数数组
*   **返回值:** `void`
*   **功能:** 应用程序主入口方法
*   **业务逻辑:** 调用 `SpringApplication.run()` 启动Spring Boot应用

## 使用示例 (Usage Examples)

```java
// 1. 标准启动方式
public class PurchaseApplication {
    public static void main(String[] args) {
        SpringApplication.run(PurchaseApplication.class, args);
    }
}

// 2. 自定义启动配置
public class PurchaseApplication {
    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(PurchaseApplication.class);

        // 设置默认配置文件
        app.setDefaultProperties(Collections.singletonMap("server.port", "8080"));

        // 设置额外的配置文件位置
        app.setAdditionalProfiles("dev");

        // 启动应用
        app.run(args);
    }
}

// 3. 命令行启动示例
// 开发环境启动
java -jar purchase-system.jar --spring.profiles.active=dev --server.port=8080

// 生产环境启动
java -jar purchase-system.jar --spring.profiles.active=prod --server.port=80

// 4. Docker容器启动示例
// Dockerfile
FROM openjdk:11-jre-slim
COPY purchase-system.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]

// docker-compose.yml
version: '3.8'
services:
  purchase-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=***********************************
    depends_on:
      - mysql

// 5. 在IDE中启动
// IntelliJ IDEA: 右键点击PurchaseApplication类 -> Run 'PurchaseApplication'
// Eclipse: 右键点击PurchaseApplication类 -> Run As -> Java Application

// 6. Maven启动
mvn spring-boot:run

// 7. Gradle启动
./gradlew bootRun
```

## 注意事项 (Notes)
*   **包扫描范围:** `@MapperScan` 配置了多层级的包扫描路径，确保所有Mapper接口都能被正确扫描和注册。
*   **事务管理:** 启用了声明式事务管理，需要确保数据源配置正确，并在需要事务的方法上添加 `@Transactional` 注解。
*   **配置文件:** 应用依赖 `application.yml` 或 `application.properties` 配置文件，需要确保配置文件存在且配置正确。
*   **数据库连接:** 应用启动前需要确保数据库服务可用，并且数据库连接配置正确。
*   **端口占用:** 默认端口8080，如果端口被占用需要通过配置文件或命令行参数修改端口。
*   **内存配置:** 生产环境建议配置合适的JVM内存参数，如 `-Xms512m -Xmx2g`。
*   **日志配置:** 建议配置适当的日志级别和输出格式，便于问题排查和监控。
*   **健康检查:** 生产环境建议启用Spring Boot Actuator进行应用健康检查。
*   **优雅关闭:** 建议配置优雅关闭机制，确保应用停止时能正确处理正在进行的请求。
*   **环境隔离:** 不同环境（开发、测试、生产）应使用不同的配置文件，通过 `spring.profiles.active` 进行切换。
