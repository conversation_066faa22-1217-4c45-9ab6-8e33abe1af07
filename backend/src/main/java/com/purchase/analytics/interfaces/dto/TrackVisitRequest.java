package com.purchase.analytics.interfaces.dto;

/**
 * 跟踪访问请求DTO
 * 用于接收前端发送的访问跟踪数据
 */
public class TrackVisitRequest {

    /**
     * 用户ID（可选，已登录用户才有）
     */
    private Long userId;

    /**
     * 访问的页面URL
     */
    private String pageUrl;

    /**
     * 来源页面URL
     */
    private String referer;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 客户端信息
     */
    private ClientInfoDto clientInfo;
    
    // Getters and Setters
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public String getPageUrl() { return pageUrl; }
    public void setPageUrl(String pageUrl) { this.pageUrl = pageUrl; }
    
    public String getReferer() { return referer; }
    public void setReferer(String referer) { this.referer = referer; }
    
    public String getSessionId() { return sessionId; }
    public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    
    public ClientInfoDto getClientInfo() { return clientInfo; }
    public void setClientInfo(ClientInfoDto clientInfo) { this.clientInfo = clientInfo; }
    
    /**
     * 客户端信息DTO
     */
    public static class ClientInfoDto {
        private String userAgent;
        private String screenResolution;
        private String timezone;
        
        // Getters and Setters
        public String getUserAgent() { return userAgent; }
        public void setUserAgent(String userAgent) { this.userAgent = userAgent; }
        
        public String getScreenResolution() { return screenResolution; }
        public void setScreenResolution(String screenResolution) { this.screenResolution = screenResolution; }
        
        public String getTimezone() { return timezone; }
        public void setTimezone(String timezone) { this.timezone = timezone; }
    }
}
