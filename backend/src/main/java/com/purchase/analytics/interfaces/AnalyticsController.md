# AnalyticsController.md

## 1. 文件概述

`AnalyticsController.java` 是分析模块的API入口，位于 `com.purchase.analytics.interfaces` 包中。该控制器作为数据分析服务的前端接口，负责处理所有与用户行为跟踪和统计数据查询相关的HTTP请求。它通过依赖注入 `VisitTrackingApplicationService` 和 `StatisticsQueryApplicationService`，将复杂的访问记录和多维度统计分析逻辑暴露为安全、易于使用的RESTful API，为运营和产品团队提供用户行为洞察和业务数据支持。

## 2. 核心功能

*   **访问跟踪**: 提供 `/track-visit` 接口，用于记录用户的页面访问、点击等行为，支持从HTTP请求中提取IP地址和客户端信息。
*   **实时统计查询**: 提供 `/real-time-stats` 接口，用于查询当前系统的实时访问统计数据。
*   **历史统计查询**: 提供 `/historical-stats` 接口，支持按日期范围和粒度（如日、周、月）查询历史访问统计数据。
*   **来源分析**: 提供 `/source-analysis` 接口，用于分析用户访问的来源渠道。
*   **设备分析**: 提供 `/device-analysis` 接口，用于分析用户访问所使用的设备类型。
*   **页面排行**: 提供 `/page-ranking` 接口，用于查询访问量最高的页面排名。
*   **时段分析**: 提供 `/time-analysis` 接口，用于分析用户在不同时间段的访问行为模式。
*   **Dashboard统计**: 提供 `/dashboard-stats` 接口，用于获取聚合的仪表盘统计数据。
*   **地理统计**: 提供 `/geographic-stats` 接口，用于分析用户访问的地理分布。
*   **实时趋势**: 提供 `/real-time-trend` 接口，用于查询实时访问趋势数据，支持不同时间粒度。
*   **统一响应**: 所有接口都返回一个标准化的 `ApiResponse` 对象，其中包含成功或失败的状态以及相应的数据或错误信息。

## 3. 接口说明

### 访问跟踪接口

#### trackVisit - 跟踪访问
*   **HTTP方法**: `POST`
*   **路径**: `/api/v1/analytics/track-visit`
*   **权限**: 无显式权限，通常由API网关或Spring Security配置进行保护。
*   **参数**:
    *   `request` (TrackVisitRequest, body, required): 包含用户ID、会话ID、页面URL、Referer、客户端信息等。
    *   `httpRequest` (HttpServletRequest): Spring MVC自动注入，用于获取客户端IP地址。
*   **返回值**: `ApiResponse<TrackVisitResult>` - 包含访问记录结果的响应对象。
*   **业务逻辑**: 接收前端发送的访问事件数据，通过 `convertToCommand` 方法将 `TrackVisitRequest` 和 `HttpServletRequest` 转换为 `TrackVisitCommand`，然后调用 `visitTrackingApplicationService.trackVisit` 进行处理。

### 统计查询接口

#### getRealTimeStats - 查询实时统计
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/analytics/real-time-stats`
*   **权限**: 通常需要认证和授权。
*   **参数**: 无（日期默认为当前日期）。
*   **返回值**: `ApiResponse<RealTimeStatsResult>` - 包含实时统计数据的响应对象。
*   **业务逻辑**: 构建 `RealTimeStatsQuery` 并调用 `statisticsQueryService.queryRealTimeStats`。

#### getHistoricalStats - 查询历史统计
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/analytics/historical-stats`
*   **权限**: 通常需要认证和授权。
*   **参数**:
    *   `startDate` (String, query, required): 开始日期，格式 `yyyy-MM-dd`。
    *   `endDate` (String, query, required): 结束日期，格式 `yyyy-MM-dd`。
    *   `granularity` (String, query, optional, default="daily"): 统计粒度（`daily`, `weekly`, `monthly`）。
*   **返回值**: `ApiResponse<HistoricalStatsResult>` - 包含历史统计数据的响应对象。
*   **业务逻辑**: 解析日期参数，构建 `HistoricalStatsQuery` 并调用 `statisticsQueryService.queryHistoricalStats`。

#### getSourceAnalysis - 获取来源分析
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/analytics/source-analysis`
*   **权限**: 通常需要认证和授权。
*   **参数**:
    *   `startDate` (String, query, required): 开始日期，格式 `yyyy-MM-dd`。
    *   `endDate` (String, query, required): 结束日期，格式 `yyyy-MM-dd`。
*   **返回值**: `ApiResponse<SourceAnalysisResult>` - 包含来源分析数据的响应对象。
*   **业务逻辑**: 解析日期参数，构建 `SourceAnalysisQuery` 并调用 `statisticsQueryService.querySourceAnalysis`。

#### getDeviceAnalysis - 获取设备分析
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/analytics/device-analysis`
*   **权限**: 通常需要认证和授权。
*   **参数**:
    *   `startDate` (String, query, required): 开始日期，格式 `yyyy-MM-dd`。
    *   `endDate` (String, query, required): 结束日期，格式 `yyyy-MM-dd`。
*   **返回值**: `ApiResponse<DeviceAnalysisResult>` - 包含设备分析数据的响应对象。
*   **业务逻辑**: 解析日期参数，构建 `DeviceAnalysisQuery` 并调用 `statisticsQueryService.queryDeviceAnalysis`。

#### getPageRanking - 获取页面排行
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/analytics/page-ranking`
*   **权限**: 通常需要认证和授权。
*   **参数**:
    *   `startDate` (String, query, optional): 开始日期，格式 `yyyy-MM-dd`。默认最近30天。
    *   `endDate` (String, query, optional): 结束日期，格式 `yyyy-MM-dd`。默认最近30天。
    *   `limit` (String, query, optional, default="10"): 返回结果的数量限制。
*   **返回值**: `ApiResponse<PageRankingResult>` - 包含页面排行数据的响应对象。
*   **业务逻辑**: 解析日期和限制参数，构建 `PageRankingQuery` 并调用 `statisticsQueryService.queryPageRanking`。

#### getTimeAnalysis - 获取时段分析
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/analytics/time-analysis`
*   **权限**: 通常需要认证和授权。
*   **参数**:
    *   `startDate` (String, query, required): 开始日期，格式 `yyyy-MM-dd`。
    *   `endDate` (String, query, required): 结束日期，格式 `yyyy-MM-dd`。
*   **返回值**: `ApiResponse<TimeAnalysisResult>` - 包含时段分析数据的响应对象。
*   **业务逻辑**: 解析日期参数，构建 `TimeAnalysisQuery` 并调用 `statisticsQueryService.queryTimeAnalysis`。

#### getDashboardStats - 查询Dashboard统计数据
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/analytics/dashboard-stats`
*   **权限**: 通常需要认证和授权。
*   **参数**: 无。
*   **返回值**: `ApiResponse<DashboardStatsResult>` - 包含仪表盘统计数据的响应对象。
*   **业务逻辑**: 构建 `DashboardStatsQuery` 并调用 `statisticsQueryService.queryDashboardStats`。

#### getGeographicStats - 查询地理统计数据
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/analytics/geographic-stats`
*   **权限**: 通常需要认证和授权。
*   **参数**:
    *   `mapType` (String, query, optional, default="china"): 地图类型。
    *   `startDate` (String, query, optional): 开始日期，格式 `yyyy-MM-dd`。默认最近7天。
    *   `endDate` (String, query, optional): 结束日期，格式 `yyyy-MM-dd`。默认最近7天。
*   **返回值**: `ApiResponse<GeographicStatsResult>` - 包含地理统计数据的响应对象。
*   **业务逻辑**: 解析参数，设置默认日期范围，构建 `GeographicStatsQuery` 并调用 `statisticsQueryService.queryGeographicStats`。

#### getRealTimeTrend - 查询实时趋势数据
*   **HTTP方法**: `GET`
*   **路径**: `/api/v1/analytics/real-time-trend`
*   **权限**: 通常需要认证和授权。
*   **参数**:
    *   `granularity` (String, query, optional, default="hour"): 粒度（`minute`, `hour`, `day`）。
    *   `startTime` (String, query, optional): 开始时间，ISO或LocalDateTime格式。根据粒度设置默认值。
    *   `endTime` (String, query, optional): 结束时间，ISO或LocalDateTime格式。默认当前时间。
*   **返回值**: `ApiResponse<RealTimeTrendResult>` - 包含实时趋势数据的响应对象。
*   **业务逻辑**: 解析时间参数，设置默认时间范围，构建 `RealTimeTrendQuery` 并调用 `statisticsQueryService.queryRealTimeTrend`。

### 辅助方法

#### convertToCommand - 转换请求为应用层命令
*   **方法签名**: `private TrackVisitCommand convertToCommand(TrackVisitRequest request, HttpServletRequest httpRequest)`
*   **描述**: 将HTTP请求中的数据和 `TrackVisitRequest` 转换为 `TrackVisitCommand`，用于传递给应用服务。
*   **参数**:
    *   `request` (TrackVisitRequest): 访问跟踪请求DTO。
    *   `httpRequest` (HttpServletRequest): HTTP请求对象。
*   **返回值**: `TrackVisitCommand` - 转换后的命令对象。

#### getClientIpAddress - 获取客户端IP地址
*   **方法签名**: `private String getClientIpAddress(HttpServletRequest request)`
*   **描述**: 从 `HttpServletRequest` 中提取客户端的IP地址，优先从 `X-Forwarded-For` 和 `X-Real-IP` 头获取。
*   **参数**:
    *   `request` (HttpServletRequest): HTTP请求对象。
*   **返回值**: `String` - 客户端IP地址。

## 4. 业务规则

*   **数据源**: 所有统计数据均来源于 `VisitTrackingApplicationService` 记录的访问事件。
*   **日期格式**: 所有日期参数都要求 `yyyy-MM-dd` 格式，时间参数支持ISO或LocalDateTime格式。
*   **默认时间范围**: 多个统计接口提供了默认的时间范围，方便前端快速调用。
*   **错误处理**: 控制器对日期解析、参数格式错误以及服务层抛出的异常进行了统一捕获和处理，返回友好的错误信息。
*   **职责分离**: 控制器仅负责请求的接收、参数的初步校验、DTO到命令的转换以及服务调用的协调，具体的业务逻辑和数据处理委托给应用服务和领域服务。

## 5. 使用示例

```java
// 1. 前端 (JavaScript) 调用 track-visit 接口
async function trackUserVisit(userId, sessionId, pageUrl, referer, clientInfo) {
  try {
    const response = await axios.post('/api/v1/analytics/track-visit', {
      userId,
      sessionId,
      pageUrl,
      referer,
      clientInfo: {
        userAgent: navigator.userAgent,
        screenResolution: `${window.screen.width}x${window.screen.height}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      }
    });
    console.log('访问记录成功:', response.data);
  } catch (error) {
    console.error('访问记录失败:', error.response.data);
  }
}

// 2. 前端 (JavaScript) 调用 historical-stats 接口
async function getHistoricalStats(startDate, endDate, granularity) {
  try {
    const response = await axios.get('/api/v1/analytics/historical-stats', {
      params: { startDate, endDate, granularity }
    });
    console.log('历史统计数据:', response.data.data);
  } catch (error) {
    console.error('查询历史统计失败:', error.response.data);
  }
}

// 3. Java后端服务间调用 (RestTemplate)
@Service
public class AnalyticsClient {
    @Autowired
    private RestTemplate restTemplate;

    private final String BASE_URL = "http://localhost:8080/api/v1/analytics";

    public RealTimeStatsResult fetchRealTimeStats() {
        ResponseEntity<ApiResponse> response = restTemplate.getForEntity(BASE_URL + "/real-time-stats", ApiResponse.class);
        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null && response.getBody().isSuccess()) {
            return (RealTimeStatsResult) response.getBody().getData();
        }
        throw new RuntimeException("Failed to fetch real-time stats");
    }
}

// 4. 测试示例 (MockMvc)
@SpringBootTest
@AutoConfigureMockMvc
class AnalyticsControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private VisitTrackingApplicationService visitTrackingService;
    @MockBean
    private StatisticsQueryApplicationService statisticsQueryService;

    @Test
    void testTrackVisit_Success() throws Exception {
        TrackVisitRequest request = new TrackVisitRequest();
        request.setUserId(1L);
        request.setPageUrl("/home");
        // ... set other fields ...

        when(visitTrackingService.trackVisit(any(TrackVisitCommand.class)))
            .thenReturn(new TrackVisitResult(true, "Success"));

        mockMvc.perform(post("/api/v1/analytics/track-visit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    void testGetHistoricalStats_Success() throws Exception {
        HistoricalStatsResult mockResult = new HistoricalStatsResult(true, "Success");
        when(statisticsQueryService.queryHistoricalStats(any(HistoricalStatsQuery.class)))
            .thenReturn(mockResult);

        mockMvc.perform(get("/api/v1/analytics/historical-stats")
                .param("startDate", "2024-01-01")
                .param("endDate", "2024-01-31")
                .param("granularity", "daily"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }
}
```

## 6. 注意事项

*   **安全性**: 尽管 `trackVisit` 接口可能不对所有用户开放，但所有统计查询接口都应受到严格的认证和授权保护，确保只有授权用户才能访问敏感的业务数据。
*   **性能**: 统计查询，特别是涉及大量数据和复杂聚合的查询，是潜在的性能瓶颈。`StatisticsQueryApplicationService` 的实现需要高度优化，例如使用缓存、预计算、异步处理、数据库索引优化等。
*   **数据一致性**: 实时统计和历史统计之间可能存在数据延迟。需要明确告知用户数据的新鲜度。
*   **异常处理**: 控制器对各种异常（如日期格式错误、参数错误）进行了细致的捕获和处理，并返回统一的 `ApiResponse` 格式，这对于前端的错误处理非常友好。
*   **IP地址获取**: `getClientIpAddress` 方法考虑了代理服务器（`X-Forwarded-For`, `X-Real-IP`）的情况，这对于获取真实用户IP地址至关重要。
*   **时间解析**: `parseTimeString` 辅助方法能够处理多种时间字符串格式，增加了接口的灵活性。
*   **可扩展性**: 随着分析需求的增加，可以方便地在此控制器中添加新的统计接口，并扩展 `StatisticsQueryApplicationService` 来支持新的查询类型。
*   **日志记录**: 控制器中使用了 `log.info` 和 `log.error` 记录请求和异常信息，这对于监控和问题排查非常重要。
