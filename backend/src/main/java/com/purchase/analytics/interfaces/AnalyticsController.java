package com.purchase.analytics.interfaces;

import com.purchase.analytics.application.VisitTrackingApplicationService;
import com.purchase.analytics.application.StatisticsQueryApplicationService;
import com.purchase.analytics.application.dto.*;
import com.purchase.analytics.application.query.DeviceAnalysisQuery;
import com.purchase.analytics.application.query.PageRankingQuery;
import com.purchase.analytics.application.query.SourceAnalysisQuery;
import com.purchase.analytics.application.query.TimeAnalysisQuery;
import com.purchase.analytics.application.result.DeviceAnalysisResult;
import com.purchase.analytics.application.result.PageRankingResult;
import com.purchase.analytics.application.result.SourceAnalysisResult;
import com.purchase.analytics.application.result.TimeAnalysisResult;
import com.purchase.analytics.interfaces.dto.TrackVisitRequest;
import com.purchase.common.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;

/**
 * 分析统计控制器
 * 提供访问跟踪和统计查询的REST API接口
 */
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {

    @Autowired
    public VisitTrackingApplicationService visitTrackingService;

    @Autowired
    public StatisticsQueryApplicationService statisticsQueryService;
    
    /**
     * 跟踪访问
     */
    @PostMapping("/track-visit")
    public ApiResponse<TrackVisitResult> trackVisit(@RequestBody TrackVisitRequest request, 
                                                   HttpServletRequest httpRequest) {
        try {
            // 参数验证
            if (request == null) {
                return ApiResponse.error("请求参数不能为空");
            }
            
            // 转换为应用层命令
            TrackVisitCommand command = convertToCommand(request, httpRequest);
            
            // 调用应用服务
            TrackVisitResult result = visitTrackingService.trackVisit(command);
            
            if (result.isSuccess()) {
                return ApiResponse.success(result, "访问记录成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }
            
        } catch (Exception e) {
            return ApiResponse.error("访问跟踪失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询实时统计
     */
    @GetMapping("/real-time-stats")
    public ApiResponse<RealTimeStatsResult> getRealTimeStats() {
        try {
            RealTimeStatsQuery query = new RealTimeStatsQuery();
            query.setDate(LocalDate.now());
            
            RealTimeStatsResult result = statisticsQueryService.queryRealTimeStats(query);
            
            if (result.isSuccess()) {
                return ApiResponse.success(result, "查询成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }
            
        } catch (Exception e) {
            return ApiResponse.error("查询实时统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询历史统计
     */
    @GetMapping("/historical-stats")
    public ApiResponse<HistoricalStatsResult> getHistoricalStats(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "daily") String granularity) {
        try {
            // 参数验证和转换
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            
            HistoricalStatsQuery query = new HistoricalStatsQuery();
            query.setStartDate(start);
            query.setEndDate(end);
            query.setGranularity(granularity);
            
            HistoricalStatsResult result = statisticsQueryService.queryHistoricalStats(query);
            
            if (result.isSuccess()) {
                return ApiResponse.success(result, "查询成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }
            
        } catch (DateTimeParseException e) {
            return ApiResponse.error("日期格式错误，请使用 yyyy-MM-dd 格式");
        } catch (Exception e) {
            return ApiResponse.error("查询历史统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 转换请求为应用层命令
     */
    private TrackVisitCommand convertToCommand(TrackVisitRequest request, HttpServletRequest httpRequest) {
        TrackVisitCommand command = new TrackVisitCommand();
        
        // 基本信息
        command.setUserId(request.getUserId());
        command.setSessionId(request.getSessionId());
        command.setPageUrl(request.getPageUrl() != null ? request.getPageUrl() : "/");
        command.setReferer(request.getReferer());
        command.setVisitTime(LocalDateTime.now());
        
        // 从HTTP请求获取IP地址
        command.setIpAddress(getClientIpAddress(httpRequest));
        
        // 客户端信息
        if (request.getClientInfo() != null) {
            command.setUserAgent(request.getClientInfo().getUserAgent());
            command.setScreenResolution(request.getClientInfo().getScreenResolution());
            command.setTimezone(request.getClientInfo().getTimezone());
        }

        return command;
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "127.0.0.1";
        }
        
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 获取来源分析
     */
    @GetMapping("/source-analysis")
    public ApiResponse<SourceAnalysisResult> getSourceAnalysis(
            @RequestParam String startDate,
            @RequestParam String endDate) {

        try {
            // 解析日期参数
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            // 创建查询对象
            SourceAnalysisQuery query = new SourceAnalysisQuery(start, end);

            // 执行查询
            SourceAnalysisResult result = statisticsQueryService.querySourceAnalysis(query);

            return ApiResponse.success(result);

        } catch (DateTimeParseException e) {
            return ApiResponse.error("日期格式错误: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("查询来源分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取设备分析
     */
    @GetMapping("/device-analysis")
    public ApiResponse<DeviceAnalysisResult> getDeviceAnalysis(
            @RequestParam String startDate,
            @RequestParam String endDate) {

        try {
            // 解析日期参数
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            // 创建查询对象
            DeviceAnalysisQuery query = new DeviceAnalysisQuery(start, end);

            // 执行查询
            DeviceAnalysisResult result = statisticsQueryService.queryDeviceAnalysis(query);

            return ApiResponse.success(result);

        } catch (DateTimeParseException e) {
            return ApiResponse.error("日期格式错误: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("查询设备分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取页面排行
     */
    @GetMapping("/page-ranking")
    public ApiResponse<PageRankingResult> getPageRanking(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "10") String limit) {

        try {
            // 解析参数，如果为null则使用默认值
            LocalDate start, end;
            if (startDate != null && endDate != null) {
                start = LocalDate.parse(startDate);
                end = LocalDate.parse(endDate);
            } else {
                // 使用默认时间范围：最近30天
                end = LocalDate.now();
                start = end.minusDays(30);
            }
            int limitInt = Integer.parseInt(limit);

            // 创建查询对象
            PageRankingQuery query = new PageRankingQuery(start, end, limitInt);

            // 执行查询
            PageRankingResult result = statisticsQueryService.queryPageRanking(query);

            return ApiResponse.success(result);

        } catch (DateTimeParseException e) {
            return ApiResponse.error("日期格式错误: " + e.getMessage());
        } catch (NumberFormatException e) {
            return ApiResponse.error("限制数量格式错误: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("查询页面排行失败: " + e.getMessage());
        }
    }

    /**
     * 获取时段分析
     */
    @GetMapping("/time-analysis")
    public ApiResponse<TimeAnalysisResult> getTimeAnalysis(
            @RequestParam String startDate,
            @RequestParam String endDate) {

        try {
            // 解析日期参数
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            // 创建查询对象
            TimeAnalysisQuery query = new TimeAnalysisQuery(start, end);

            // 执行查询
            TimeAnalysisResult result = statisticsQueryService.queryTimeAnalysis(query);

            return ApiResponse.success(result);

        } catch (DateTimeParseException e) {
            return ApiResponse.error("日期格式错误: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("查询时段分析失败: " + e.getMessage());
        }
    }

    /**
     * 查询Dashboard统计数据
     */
    @GetMapping("/dashboard-stats")
    public ApiResponse<DashboardStatsResult> getDashboardStats() {
        try {
            DashboardStatsQuery query = new DashboardStatsQuery();

            DashboardStatsResult result = statisticsQueryService.queryDashboardStats(query);

            if (result.isSuccess()) {
                return ApiResponse.success(result, "查询成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }

        } catch (Exception e) {
            return ApiResponse.error("查询Dashboard统计失败: " + e.getMessage());
        }
    }

    /**
     * 查询地理统计数据
     */
    @GetMapping("/geographic-stats")
    public ApiResponse<GeographicStatsResult> getGeographicStats(
            @RequestParam(defaultValue = "china") String mapType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            GeographicStatsQuery query = new GeographicStatsQuery();
            query.setMapType(mapType);

            // 设置默认日期范围（最近7天）
            if (startDate != null && endDate != null) {
                query.setStartDate(LocalDate.parse(startDate));
                query.setEndDate(LocalDate.parse(endDate));
            } else {
                query.setStartDate(LocalDate.now().minusDays(7));
                query.setEndDate(LocalDate.now());
            }

            GeographicStatsResult result = statisticsQueryService.queryGeographicStats(query);

            if (result.isSuccess()) {
                return ApiResponse.success(result, "查询成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }

        } catch (Exception e) {
            return ApiResponse.error("查询地理统计失败: " + e.getMessage());
        }
    }

    /**
     * 查询实时趋势数据
     */
    @GetMapping("/real-time-trend")
    public ApiResponse<RealTimeTrendResult> getRealTimeTrend(
            @RequestParam(defaultValue = "hour") String granularity,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        try {
            RealTimeTrendQuery query = new RealTimeTrendQuery();
            query.setGranularity(granularity);

            // 设置默认时间范围
            if (startTime != null && endTime != null) {
                query.setStartTime(parseTimeString(startTime));
                query.setEndTime(parseTimeString(endTime));
            } else {
                // 根据粒度设置默认时间范围
                LocalDateTime now = LocalDateTime.now();
                switch (granularity.toLowerCase()) {
                    case "minute":
                        query.setStartTime(now.minusHours(1)); // 最近1小时
                        break;
                    case "hour":
                        query.setStartTime(now.minusHours(24)); // 最近24小时
                        break;
                    case "day":
                        query.setStartTime(now.minusDays(30)); // 最近30天
                        break;
                    default:
                        query.setStartTime(now.minusHours(24));
                }
                query.setEndTime(now);
            }

            RealTimeTrendResult result = statisticsQueryService.queryRealTimeTrend(query);

            if (result.isSuccess()) {
                return ApiResponse.success(result, "查询成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }

        } catch (Exception e) {
            return ApiResponse.error("查询实时趋势失败: " + e.getMessage());
        }
    }

    /**
     * 解析时间字符串，支持ISO格式和LocalDateTime格式
     */
    private LocalDateTime parseTimeString(String timeString) {
        try {
            // 尝试解析ISO格式时间（包含Z）
            if (timeString.contains("Z") || timeString.contains("+") || timeString.contains("T")) {
                Instant instant = Instant.parse(timeString);
                return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
            } else {
                // 尝试解析LocalDateTime格式
                return LocalDateTime.parse(timeString);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("无法解析时间字符串: " + timeString, e);
        }
    }
}
