package com.purchase.analytics.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.purchase.analytics.infrastructure.entity.VisitRecordEntity;
import com.purchase.analytics.infrastructure.dto.PageVisitStatsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 访问记录Mapper接口
 */
@Mapper
public interface VisitRecordMapper extends BaseMapper<VisitRecordEntity> {
    
    /**
     * 查找用户最近的访问记录
     */
    @Select("SELECT * FROM website_visit_log " +
            "WHERE user_id = #{userId} AND visit_time >= #{since} " +
            "ORDER BY visit_time DESC")
    List<VisitRecordEntity> findRecentVisitsByUser(@Param("userId") Long userId,
                                                   @Param("since") LocalDateTime since);
    
    /**
     * 根据IP地址和UserAgent查找最近的访问记录
     */
    @Select("SELECT * FROM website_visit_log " +
            "WHERE ip_address = #{ipAddress} AND user_agent = #{userAgent} " +
            "AND visit_time >= #{since} AND visitor_type = 'ANONYMOUS' " +
            "ORDER BY visit_time DESC")
    List<VisitRecordEntity> findRecentVisitsByIpAndUserAgent(@Param("ipAddress") String ipAddress,
                                                             @Param("userAgent") String userAgent,
                                                             @Param("since") LocalDateTime since);
    
    /**
     * 根据日期范围查找访问记录
     */
    @Select("SELECT * FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "ORDER BY visit_time DESC")
    List<VisitRecordEntity> findByDateRange(@Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的访问次数
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime}")
    long countByDateRange(@Param("startTime") LocalDateTime startTime,
                         @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的独立访客数
     */
    @Select("SELECT COUNT(DISTINCT CASE " +
            "WHEN visitor_type = 'REGISTERED' THEN user_id " +
            "WHEN visitor_type = 'ANONYMOUS' THEN CONCAT(ip_address, '|', user_agent) " +
            "END) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime}")
    long countUniqueVisitorsByDateRange(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的桌面设备访问量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "AND (user_agent NOT LIKE '%Mobile%' AND user_agent NOT LIKE '%Android%' " +
            "AND user_agent NOT LIKE '%iPhone%' AND user_agent NOT LIKE '%iPad%' " +
            "AND user_agent NOT LIKE '%Tablet%')")
    long countDesktopVisitsByDateRange(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的移动设备访问量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "AND (user_agent LIKE '%Mobile%' OR user_agent LIKE '%Android%' " +
            "OR user_agent LIKE '%iPhone%') " +
            "AND user_agent NOT LIKE '%iPad%' AND user_agent NOT LIKE '%Tablet%'")
    long countMobileVisitsByDateRange(@Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的平板设备访问量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "AND (user_agent LIKE '%iPad%' OR user_agent LIKE '%Tablet%')")
    long countTabletVisitsByDateRange(@Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定日期的总访问量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE DATE(visit_time) = #{date}")
    long countVisitsByDate(@Param("date") LocalDate date);

    /**
     * 统计指定日期的独立访客数
     */
    @Select("SELECT COUNT(DISTINCT CASE " +
            "WHEN visitor_type = 'REGISTERED' THEN user_id " +
            "WHEN visitor_type = 'ANONYMOUS' THEN CONCAT(ip_address, '|', user_agent) " +
            "END) FROM website_visit_log " +
            "WHERE DATE(visit_time) = #{date}")
    long countUniqueVisitorsByDate(@Param("date") LocalDate date);

    /**
     * 统计指定日期的注册用户访问量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE DATE(visit_time) = #{date} AND visitor_type = 'REGISTERED'")
    long countRegisteredVisitsByDate(@Param("date") LocalDate date);

    /**
     * 统计指定日期的匿名用户访问量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE DATE(visit_time) = #{date} AND visitor_type = 'ANONYMOUS'")
    long countAnonymousVisitsByDate(@Param("date") LocalDate date);

    /**
     * 统计指定时间范围内按referer模式匹配的访问量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "AND referer LIKE #{refererPattern}")
    long countByRefererPattern(@Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime,
                              @Param("refererPattern") String refererPattern);

    /**
     * 统计指定时间范围内的直接访问量（无referer）
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "AND (referer IS NULL OR referer = '')")
    long countDirectVisitsByDateRange(@Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的搜索引擎访问量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "AND referer LIKE CONCAT('%', #{searchEngine}, '%')")
    long countSearchEngineVisitsByDateRange(@Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime,
                                           @Param("searchEngine") String searchEngine);

    /**
     * 统计指定时间范围内的社交媒体访问量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "AND referer LIKE CONCAT('%', #{socialMedia}, '%')")
    long countSocialMediaVisitsByDateRange(@Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime,
                                          @Param("socialMedia") String socialMedia);

    /**
     * 统计指定时间范围内的引荐网站访问量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "AND referer LIKE CONCAT('%', #{domain}, '%')")
    long countReferralVisitsByDateRange(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime,
                                       @Param("domain") String domain);

    /**
     * 统计指定时间范围内的浏览器访问量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "AND LOWER(user_agent) LIKE CONCAT('%', LOWER(#{browserName}), '%')")
    long countBrowserVisitsByDateRange(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime,
                                      @Param("browserName") String browserName);

    /**
     * 统计指定时间范围内的操作系统访问量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "AND LOWER(user_agent) LIKE CONCAT('%', LOWER(#{osName}), '%')")
    long countOperatingSystemVisitsByDateRange(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime,
                                              @Param("osName") String osName);

    /**
     * 统计指定时间范围内的页面浏览量
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime}")
    long countPageViewsByDateRange(@Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 计算指定时间范围内的平均停留时间（简化实现，返回固定值）
     */
    @Select("SELECT COALESCE(AVG(TIMESTAMPDIFF(MINUTE, visit_time, " +
            "DATE_ADD(visit_time, INTERVAL 5 MINUTE))), 0) " +
            "FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime}")
    double calculateAvgStayTimeByDateRange(@Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 获取最近几天的访问量历史数据
     */
    @Select("SELECT COUNT(*) FROM website_visit_log " +
            "WHERE DATE(visit_time) = DATE_SUB(CURDATE(), INTERVAL #{dayOffset} DAY)")
    long getVisitCountByDayOffset(@Param("dayOffset") int dayOffset);

    /**
     * 获取最近几小时的在线用户数历史数据（简化实现）
     */
    @Select("SELECT COUNT(DISTINCT ip_address) FROM website_visit_log " +
            "WHERE visit_time >= DATE_SUB(NOW(), INTERVAL #{hourOffset} HOUR) " +
            "AND visit_time < DATE_SUB(NOW(), INTERVAL #{hourOffset} - 1 HOUR)")
    long getOnlineCountByHourOffset(@Param("hourOffset") int hourOffset);

    /**
     * 根据地理位置统计访问量（简化实现，基于IP地址前缀）
     */
    @Select("SELECT " +
            "CASE " +
            "  WHEN ip_address LIKE '192.168.%' OR ip_address LIKE '10.%' OR ip_address LIKE '172.%' THEN '本地' " +
            "  WHEN ip_address LIKE '1.%' OR ip_address LIKE '14.%' OR ip_address LIKE '27.%' THEN '北京' " +
            "  WHEN ip_address LIKE '58.%' OR ip_address LIKE '101.%' OR ip_address LIKE '112.%' THEN '上海' " +
            "  WHEN ip_address LIKE '113.%' OR ip_address LIKE '119.%' OR ip_address LIKE '183.%' THEN '广东' " +
            "  WHEN ip_address LIKE '115.%' OR ip_address LIKE '124.%' OR ip_address LIKE '125.%' THEN '浙江' " +
            "  WHEN ip_address LIKE '114.%' OR ip_address LIKE '117.%' OR ip_address LIKE '121.%' THEN '江苏' " +
            "  ELSE '其他' " +
            "END as region_name, " +
            "COUNT(*) as visit_count " +
            "FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "GROUP BY region_name " +
            "ORDER BY visit_count DESC")
    List<Object[]> getGeographicStatsByDateRange(@Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime,
                                                 @Param("mapType") String mapType);

    /**
     * 获取实时趋势数据（按小时聚合）
     */
    @Select("SELECT " +
            "hour_period as time_period, " +
            "UNIX_TIMESTAMP(hour_period) * 1000 as timestamp, " +
            "COUNT(*) as visits, " +
            "COUNT(DISTINCT ip_address) as unique_visitors, " +
            "COUNT(*) as page_views " +
            "FROM (" +
            "  SELECT *, DATE_FORMAT(visit_time, '%Y-%m-%d %H:00:00') as hour_period " +
            "  FROM website_visit_log " +
            "  WHERE visit_time >= #{startTime} AND visit_time <= #{endTime}" +
            ") t " +
            "GROUP BY hour_period " +
            "ORDER BY hour_period")
    List<Object[]> getRealTimeTrendDataByHour(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 获取实时趋势数据（按分钟聚合）
     */
    @Select("SELECT " +
            "minute_period as time_period, " +
            "UNIX_TIMESTAMP(minute_period) * 1000 as timestamp, " +
            "COUNT(*) as visits, " +
            "COUNT(DISTINCT ip_address) as unique_visitors, " +
            "COUNT(*) as page_views " +
            "FROM (" +
            "  SELECT *, DATE_FORMAT(visit_time, '%Y-%m-%d %H:%i:00') as minute_period " +
            "  FROM website_visit_log " +
            "  WHERE visit_time >= #{startTime} AND visit_time <= #{endTime}" +
            ") t " +
            "GROUP BY minute_period " +
            "ORDER BY minute_period")
    List<Object[]> getRealTimeTrendDataByMinute(@Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 获取实时趋势数据（按天聚合）
     */
    @Select("SELECT " +
            "day_period as time_period, " +
            "UNIX_TIMESTAMP(CONCAT(day_period, ' 00:00:00')) * 1000 as timestamp, " +
            "COUNT(*) as visits, " +
            "COUNT(DISTINCT ip_address) as unique_visitors, " +
            "COUNT(*) as page_views " +
            "FROM (" +
            "  SELECT *, DATE_FORMAT(visit_time, '%Y-%m-%d') as day_period " +
            "  FROM website_visit_log " +
            "  WHERE visit_time >= #{startTime} AND visit_time <= #{endTime}" +
            ") t " +
            "GROUP BY day_period " +
            "ORDER BY day_period")
    List<Object[]> getRealTimeTrendDataByDay(@Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 获取页面访问统计数据
     */
    @Select("SELECT " +
            "page_url, " +
            "CASE " +
            "  WHEN page_url = '/' THEN '首页' " +
            "  WHEN page_url LIKE '/admin%' THEN '管理页面' " +
            "  WHEN page_url LIKE '/user%' THEN '用户页面' " +
            "  WHEN page_url LIKE '/seller%' THEN '卖家页面' " +
            "  WHEN page_url LIKE '/buyer%' THEN '买家页面' " +
            "  WHEN page_url LIKE '/forwarder%' THEN '货代页面' " +
            "  ELSE '其他页面' " +
            "END as pageTitle, " +
            "CASE " +
            "  WHEN page_url = '/' THEN 'home' " +
            "  WHEN page_url LIKE '/admin%' THEN 'admin' " +
            "  WHEN page_url LIKE '/user%' THEN 'user' " +
            "  WHEN page_url LIKE '/seller%' THEN 'seller' " +
            "  WHEN page_url LIKE '/buyer%' THEN 'buyer' " +
            "  WHEN page_url LIKE '/forwarder%' THEN 'forwarder' " +
            "  ELSE 'other' " +
            "END as category, " +
            "COUNT(*) as visits, " +
            "COUNT(DISTINCT ip_address) as uniqueVisitors, " +
            "180 as avgStayTime " +
            "FROM website_visit_log " +
            "WHERE visit_time >= #{startTime} AND visit_time <= #{endTime} " +
            "GROUP BY page_url " +
            "ORDER BY visits DESC " +
            "LIMIT #{limit}")
    List<PageVisitStatsDTO> getPageVisitStatsByDateRange(@Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime,
                                                        @Param("limit") int limit);
}
