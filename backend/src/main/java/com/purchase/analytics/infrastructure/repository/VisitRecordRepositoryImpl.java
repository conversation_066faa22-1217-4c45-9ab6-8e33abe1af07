package com.purchase.analytics.infrastructure.repository;

import com.purchase.analytics.domain.model.VisitRecord;
import com.purchase.analytics.domain.model.VisitorType;
import com.purchase.analytics.domain.repository.VisitRecordRepository;
import com.purchase.analytics.domain.valueobject.ClientInfo;
import com.purchase.analytics.infrastructure.entity.VisitRecordEntity;
import com.purchase.analytics.infrastructure.mapper.VisitRecordMapper;
import com.purchase.analytics.infrastructure.dto.PageVisitStatsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 访问记录仓储实现
 */
@Repository
public class VisitRecordRepositoryImpl implements VisitRecordRepository {

    @Autowired
    private VisitRecordMapper visitRecordMapper;
    
    @Override
    public VisitRecord save(VisitRecord visitRecord) {
        VisitRecordEntity entity = convertToEntity(visitRecord);
        
        if (entity.getId() == null) {
            // 新增
            visitRecordMapper.insert(entity);
        } else {
            // 更新
            visitRecordMapper.updateById(entity);
        }
        
        // 返回保存后的记录
        VisitRecordEntity savedEntity = visitRecordMapper.selectById(entity.getId());
        return convertToDomain(savedEntity);
    }
    
    @Override
    public Optional<VisitRecord> findById(Long id) {
        VisitRecordEntity entity = visitRecordMapper.selectById(id);
        if (entity == null) {
            return Optional.empty();
        }
        return Optional.of(convertToDomain(entity));
    }
    
    @Override
    public List<VisitRecord> findRecentVisitsByUser(Long userId, LocalDateTime since) {
        List<VisitRecordEntity> entities = visitRecordMapper.findRecentVisitsByUser(userId, since);
        return entities.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<VisitRecord> findRecentVisitsByIpAndUserAgent(String ipAddress, String userAgent, LocalDateTime since) {
        List<VisitRecordEntity> entities = visitRecordMapper.findRecentVisitsByIpAndUserAgent(ipAddress, userAgent, since);
        return entities.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<VisitRecord> findByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        List<VisitRecordEntity> entities = visitRecordMapper.findByDateRange(startTime, endTime);
        return entities.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public long countByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        return visitRecordMapper.countByDateRange(startTime, endTime);
    }
    
    @Override
    public long countUniqueVisitorsByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        return visitRecordMapper.countUniqueVisitorsByDateRange(startTime, endTime);
    }

    @Override
    public long countDesktopVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        return visitRecordMapper.countDesktopVisitsByDateRange(startTime, endTime);
    }

    @Override
    public long countMobileVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        return visitRecordMapper.countMobileVisitsByDateRange(startTime, endTime);
    }

    @Override
    public long countTabletVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        return visitRecordMapper.countTabletVisitsByDateRange(startTime, endTime);
    }

    @Override
    public long countVisitsByDate(LocalDate date) {
        return visitRecordMapper.countVisitsByDate(date);
    }

    @Override
    public long countUniqueVisitorsByDate(LocalDate date) {
        return visitRecordMapper.countUniqueVisitorsByDate(date);
    }

    @Override
    public long countRegisteredVisitsByDate(LocalDate date) {
        return visitRecordMapper.countRegisteredVisitsByDate(date);
    }

    @Override
    public long countAnonymousVisitsByDate(LocalDate date) {
        return visitRecordMapper.countAnonymousVisitsByDate(date);
    }

    @Override
    public long countByRefererPattern(LocalDateTime startTime, LocalDateTime endTime, String refererPattern) {
        return visitRecordMapper.countByRefererPattern(startTime, endTime, refererPattern);
    }

    @Override
    public long countDirectVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        return visitRecordMapper.countDirectVisitsByDateRange(startTime, endTime);
    }

    @Override
    public long countSearchEngineVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String searchEngine) {
        return visitRecordMapper.countSearchEngineVisitsByDateRange(startTime, endTime, searchEngine);
    }

    @Override
    public long countSocialMediaVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String socialMedia) {
        return visitRecordMapper.countSocialMediaVisitsByDateRange(startTime, endTime, socialMedia);
    }

    @Override
    public long countReferralVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String domain) {
        return visitRecordMapper.countReferralVisitsByDateRange(startTime, endTime, domain);
    }

    @Override
    public long countBrowserVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String browserName) {
        return visitRecordMapper.countBrowserVisitsByDateRange(startTime, endTime, browserName);
    }

    @Override
    public long countOperatingSystemVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String osName) {
        return visitRecordMapper.countOperatingSystemVisitsByDateRange(startTime, endTime, osName);
    }

    @Override
    public long countPageViewsByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        return visitRecordMapper.countPageViewsByDateRange(startTime, endTime);
    }

    @Override
    public double calculateAvgStayTimeByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        return visitRecordMapper.calculateAvgStayTimeByDateRange(startTime, endTime);
    }

    @Override
    public List<Long> getVisitHistoryByDays(int days) {
        List<Long> history = new ArrayList<>();
        for (int i = days - 1; i >= 0; i--) {
            long count = visitRecordMapper.getVisitCountByDayOffset(i);
            history.add(count);
        }
        return history;
    }

    @Override
    public List<Long> getOnlineHistoryByHours(int hours) {
        List<Long> history = new ArrayList<>();
        for (int i = hours - 1; i >= 0; i--) {
            long count = visitRecordMapper.getOnlineCountByHourOffset(i);
            history.add(count);
        }
        return history;
    }

    @Override
    public List<Object[]> getGeographicStatsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String mapType) {
        return visitRecordMapper.getGeographicStatsByDateRange(startTime, endTime, mapType);
    }

    @Override
    public List<Object[]> getRealTimeTrendData(LocalDateTime startTime, LocalDateTime endTime, String granularity) {
        switch (granularity.toLowerCase()) {
            case "minute":
                return visitRecordMapper.getRealTimeTrendDataByMinute(startTime, endTime);
            case "hour":
                return visitRecordMapper.getRealTimeTrendDataByHour(startTime, endTime);
            case "day":
                return visitRecordMapper.getRealTimeTrendDataByDay(startTime, endTime);
            default:
                return visitRecordMapper.getRealTimeTrendDataByHour(startTime, endTime);
        }
    }

    /**
     * 领域对象转换为数据库实体
     */
    private VisitRecordEntity convertToEntity(VisitRecord visitRecord) {
        VisitRecordEntity entity = new VisitRecordEntity();

        entity.setUserId(visitRecord.getUserId());
        entity.setSessionId(visitRecord.getSessionId());
        entity.setIpAddress(visitRecord.getIpAddress());
        entity.setUserAgent(visitRecord.getUserAgent());
        entity.setPageUrl(visitRecord.getPageUrl());
        entity.setVisitorType(visitRecord.getVisitorType().name());
        entity.setVisitTime(visitRecord.getVisitTime());
        entity.setIsUniqueVisit(visitRecord.isUniqueVisit());

        // 这些字段在当前领域模型中不存在，设为null
        entity.setReferer(null);

        // 构建客户端信息JSON
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setUserAgent(visitRecord.getUserAgent());
        // 其他客户端信息字段可以根据需要从visitRecord中获取或设置默认值
        entity.setClientInfo(clientInfo.toJson());

        return entity;
    }
    
    /**
     * 数据库实体转换为领域对象
     */
    private VisitRecord convertToDomain(VisitRecordEntity entity) {
        VisitorType visitorType = VisitorType.valueOf(entity.getVisitorType());
        
        if (visitorType == VisitorType.REGISTERED) {
            return VisitRecord.createForRegisteredUser(
                entity.getUserId(),
                entity.getSessionId(),
                entity.getIpAddress(),
                entity.getPageUrl(),
                entity.getVisitTime()
            );
        } else {
            return VisitRecord.createForAnonymousUser(
                entity.getSessionId(),
                entity.getIpAddress(),
                entity.getUserAgent(),
                entity.getPageUrl(),
                entity.getVisitTime()
            );
        }
    }

    @Override
    public List<PageVisitStatsDTO> getPageVisitStatsByDateRange(LocalDateTime startTime, LocalDateTime endTime, int limit) {
        return visitRecordMapper.getPageVisitStatsByDateRange(startTime, endTime, limit);
    }
}
