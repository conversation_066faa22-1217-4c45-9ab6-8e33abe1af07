# VisitRecordMapper.md

## 1. 文件概述

`VisitRecordMapper.java` 是分析模块中的一个MyBatis-Plus Mapper接口，位于 `com.purchase.analytics.infrastructure.mapper` 包中。它继承自MyBatis-Plus的 `BaseMapper<VisitRecordEntity>` 接口，并在此基础上定义了大量自定义的查询方法。该Mapper接口是 `VisitRecordRepository` 实现与数据库进行交互的桥梁，负责将对 `VisitRecordEntity` 的操作转换为SQL语句，实现访问记录的持久化管理和多维度统计查询。

## 2. 核心功能

*   **基础CRUD操作**: 继承 `BaseMapper`，自动拥有对 `VisitRecordEntity` 实体进行插入（`insert`）、根据ID查询（`selectById`）、根据条件查询列表（`selectList`）、更新（`updateById`）和删除（`deleteById`）等基础的增删改查功能。
*   **去重辅助查询**: 提供了 `findRecentVisitsByUser` 和 `findRecentVisitsByIpAndUserAgent` 等方法，用于支持领域服务中的访问去重逻辑。
*   **多维度统计查询**: 提供了大量统计方法，涵盖了按日期范围统计访问次数、独立访客数、设备类型（桌面、移动、平板）、访问来源（直接、搜索引擎、社交媒体、引荐网站）、浏览器、操作系统等多种维度。
*   **趋势数据获取**: 提供了 `getVisitHistoryByDays` 和 `getOnlineHistoryByHours` 等方法，用于获取历史访问量和在线用户数的趋势数据。
*   **地理统计与实时趋势**: 提供了 `getGeographicStatsByDateRange` 和 `getRealTimeTrendData` 等方法，支持更高级的地理位置统计和实时趋势分析。
*   **页面访问统计**: 提供了 `getPageVisitStatsByDateRange` 方法，用于获取页面访问的详细统计数据，包括访问量、独立访客、平均停留时间等。
*   **SQL注解**: 大部分自定义查询都使用了 `@Select` 注解直接编写SQL，使得SQL逻辑清晰可见。

## 3. 接口说明

`VisitRecordMapper` 在继承 `BaseMapper` 的基础上，定义了以下自定义方法：

### 3.1 去重辅助查询

#### findRecentVisitsByUser - 查找用户最近的访问记录
*   **方法签名**: `List<VisitRecordEntity> findRecentVisitsByUser(@Param("userId") Long userId, @Param("since") LocalDateTime since)`
*   **描述**: 查找指定用户在给定时间点之后的所有访问记录，按访问时间降序排列。

#### findRecentVisitsByIpAndUserAgent - 根据IP地址和UserAgent查找最近的访问记录
*   **方法签名**: `List<VisitRecordEntity> findRecentVisitsByIpAndUserAgent(@Param("ipAddress") String ipAddress, @Param("userAgent") String userAgent, @Param("since") LocalDateTime since)`
*   **描述**: 查找指定IP地址和UserAgent在给定时间点之后的所有匿名访问记录，按访问时间降序排列。

### 3.2 统计查询方法

#### countByDateRange - 统计指定时间范围内的访问次数
*   **方法签名**: `long countByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的总访问次数。

#### countUniqueVisitorsByDateRange - 统计指定时间范围内的独立访客数
*   **方法签名**: `long countUniqueVisitorsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的独立访客数。对于注册用户基于 `user_id`，对于匿名用户基于 `ip_address` 和 `user_agent` 的组合进行去重。

#### countDesktopVisitsByDateRange - 统计指定时间范围内的桌面设备访问量
*   **方法签名**: `long countDesktopVisitsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的桌面设备访问量，通过User-Agent判断。

#### countMobileVisitsByDateRange - 统计指定时间范围内的移动设备访问量
*   **方法签名**: `long countMobileVisitsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的移动设备访问量，通过User-Agent判断。

#### countTabletVisitsByDateRange - 统计指定时间范围内的平板设备访问量
*   **方法签名**: `long countTabletVisitsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的平板设备访问量，通过User-Agent判断。

#### countVisitsByDate - 统计指定日期的总访问量
*   **方法签名**: `long countVisitsByDate(@Param("date") LocalDate date)`
*   **描述**: 统计指定日期的总访问量。

#### countUniqueVisitorsByDate - 统计指定日期的独立访客数
*   **方法签名**: `long countUniqueVisitorsByDate(@Param("date") LocalDate date)`
*   **描述**: 统计指定日期的独立访客数。

#### countRegisteredVisitsByDate - 统计指定日期的注册用户访问量
*   **方法签名**: `long countRegisteredVisitsByDate(@Param("date") LocalDate date)`
*   **描述**: 统计指定日期的注册用户访问量。

#### countAnonymousVisitsByDate - 统计指定日期的匿名用户访问量
*   **方法签名**: `long countAnonymousVisitsByDate(@Param("date") LocalDate date)`
*   **描述**: 统计指定日期的匿名用户访问量。

#### countByRefererPattern - 统计指定时间范围内按referer模式匹配的访问量
*   **方法签名**: `long countByRefererPattern(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("refererPattern") String refererPattern)`
*   **描述**: 统计在指定时间范围内，referer匹配特定模式的访问量。

#### countDirectVisitsByDateRange - 统计指定时间范围内的直接访问量
*   **方法签名**: `long countDirectVisitsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的直接访问量（referer为空或null）。

#### countSearchEngineVisitsByDateRange - 统计指定时间范围内的搜索引擎访问量
*   **方法签名**: `long countSearchEngineVisitsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("searchEngine") String searchEngine)`
*   **描述**: 统计在指定时间范围内，来自特定搜索引擎的访问量。

#### countSocialMediaVisitsByDateRange - 统计指定时间范围内的社交媒体访问量
*   **方法签名**: `long countSocialMediaVisitsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("socialMedia") String socialMedia)`
*   **描述**: 统计在指定时间范围内，来自特定社交媒体的访问量。

#### countReferralVisitsByDateRange - 统计指定时间范围内的引荐网站访问量
*   **方法签名**: `long countReferralVisitsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("domain") String domain)`
*   **描述**: 统计在指定时间范围内，来自特定引荐网站的访问量。

#### countBrowserVisitsByDateRange - 统计指定时间范围内的浏览器访问量
*   **方法签名**: `long countBrowserVisitsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("browserName") String browserName)`
*   **描述**: 统计在指定时间范围内，来自特定浏览器的访问量。

#### countOperatingSystemVisitsByDateRange - 统计指定时间范围内的操作系统访问量
*   **方法签名**: `long countOperatingSystemVisitsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("osName") String osName)`
*   **描述**: 统计在指定时间范围内，来自特定操作系统的访问量。

#### countPageViewsByDateRange - 统计指定时间范围内的页面浏览量
*   **方法签名**: `long countPageViewsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的页面浏览量（总访问次数）。

#### calculateAvgStayTimeByDateRange - 计算指定时间范围内的平均停留时间
*   **方法签名**: `double calculateAvgStayTimeByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime)`
*   **描述**: 计算在指定时间范围内的平均用户停留时间。当前实现为简化版本，可能返回固定值或基于简单计算。

#### getVisitHistoryByDays - 获取最近几天的访问量历史数据
*   **方法签名**: `long getVisitCountByDayOffset(@Param("dayOffset") int dayOffset)`
*   **描述**: 获取指定天数偏移量（例如0为今天，1为昨天）的访问量。

#### getOnlineHistoryByHours - 获取最近几小时的在线用户数历史数据
*   **方法签名**: `long getOnlineCountByHourOffset(@Param("hourOffset") int hourOffset)`
*   **描述**: 获取指定小时偏移量（例如0为当前小时，1为上一小时）的在线用户数。

#### getGeographicStatsByDateRange - 根据地理位置统计访问量
*   **方法签名**: `List<Object[]> getGeographicStatsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("mapType") String mapType)`
*   **描述**: 根据地理位置统计在指定时间范围内的访问量。返回的数据是 `Object[]` 数组，需要手动映射。

#### getRealTimeTrendData - 获取实时趋势数据
*   **方法签名**: `List<Object[]> getRealTimeTrendDataByHour(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime)`
*   **描述**: 获取按小时聚合的实时趋势数据。返回的数据是 `Object[]` 数组，需要手动映射。
*   **方法签名**: `List<Object[]> getRealTimeTrendDataByMinute(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime)`
*   **描述**: 获取按分钟聚合的实时趋势数据。返回的数据是 `Object[]` 数组，需要手动映射。
*   **方法签名**: `List<Object[]> getRealTimeTrendDataByDay(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime)`
*   **描述**: 获取按天聚合的实时趋势数据。返回的数据是 `Object[]` 数组，需要手动映射。

#### getPageVisitStatsByDateRange - 获取页面访问统计数据
*   **方法签名**: `List<com.purchase.analytics.infrastructure.dto.PageVisitStatsDTO> getPageVisitStatsByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("limit") int limit)`
*   **描述**: 获取在指定时间范围内的页面访问统计数据，并可限制返回数量。返回 `PageVisitStatsDTO` 列表。

## 4. 业务规则

*   **SQL注解**: 所有自定义查询都使用了 `@Select` 注解直接编写SQL。这使得SQL逻辑清晰可见，但对于非常复杂的查询，可能需要考虑使用XML配置。
*   **参数绑定**: 使用 `@Param` 注解将Java方法参数绑定到SQL中的命名参数，有效防止SQL注入。
*   **性能优化**: 考虑到访问日志数据量庞大且查询频繁，Mapper中的SQL查询必须高度优化，包括但不限于：
    *   为 `visit_time`, `user_id`, `session_id`, `ip_address`, `user_agent`, `referer`, `visitor_type` 等常用查询字段建立合适的索引。
    *   `COUNT(DISTINCT ...)` 操作可能性能开销较大，需要根据实际数据量和数据库类型进行优化。
    *   `LIKE CONCAT('%', ..., '%')` 模糊查询通常无法利用索引，可能导致全表扫描，应谨慎使用或考虑全文索引。
*   **数据类型映射**: 确保Java实体中的数据类型与SQL查询结果的字段类型正确映射。
*   **复杂SQL**: 某些查询（如 `countUniqueVisitorsByDateRange`, `getGeographicStatsByDateRange`, `getRealTimeTrendData`）涉及复杂的SQL逻辑，包括 `CASE WHEN`, `GROUP BY`, `DATE_FORMAT`, `UNIX_TIMESTAMP` 等，需要确保其正确性和性能。
*   **简化实现**: 某些统计方法（如 `calculateAvgStayTimeByDateRange`, `getOnlineCountByHourOffset`）在当前代码中是简化实现或模拟数据，在生产环境中需要替换为真实的、更精确的计算逻辑。

## 5. 使用示例

```java
// 1. 在 VisitRecordRepositoryImpl 中调用 VisitRecordMapper
@Repository
public class VisitRecordRepositoryImpl implements VisitRecordRepository {
    @Autowired
    private VisitRecordMapper visitRecordMapper;

    @Override
    public long countByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        return visitRecordMapper.countByDateRange(startTime, endTime);
    }

    @Override
    public long countUniqueVisitorsByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        return visitRecordMapper.countUniqueVisitorsByDateRange(startTime, endTime);
    }

    @Override
    public List<com.purchase.analytics.infrastructure.dto.PageVisitStatsDTO> getPageVisitStatsByDateRange(LocalDateTime startTime, LocalDateTime endTime, int limit) {
        return visitRecordMapper.getPageVisitStatsByDateRange(startTime, endTime, limit);
    }

    @Override
    public List<Long> getVisitHistoryByDays(int days) {
        List<Long> history = new ArrayList<>();
        for (int i = days - 1; i >= 0; i--) {
            history.add(visitRecordMapper.getVisitCountByDayOffset(i));
        }
        return history;
    }

    @Override
    public List<Long> getOnlineHistoryByHours(int hours) {
        List<Long> history = new ArrayList<>();
        for (int i = hours - 1; i >= 0; i--) {
            history.add(visitRecordMapper.getOnlineCountByHourOffset(i));
        }
        return history;
    }

    @Override
    public List<Object[]> getGeographicStatsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String mapType) {
        return visitRecordMapper.getGeographicStatsByDateRange(startTime, endTime, mapType);
    }

    @Override
    public List<Object[]> getRealTimeTrendData(LocalDateTime startTime, LocalDateTime endTime, String granularity) {
        switch (granularity.toLowerCase()) {
            case "minute":
                return visitRecordMapper.getRealTimeTrendDataByMinute(startTime, endTime);
            case "hour":
                return visitRecordMapper.getRealTimeTrendDataByHour(startTime, endTime);
            case "day":
                return visitRecordMapper.getRealTimeTrendDataByDay(startTime, endTime);
            default:
                throw new IllegalArgumentException("不支持的时间粒度: " + granularity);
        }
    }
}

// 2. 测试示例
@SpringBootTest
class VisitRecordMapperTest {
    @Autowired
    private VisitRecordMapper visitRecordMapper;

    @Test
    @Transactional
    void testCountByDateRange() {
        // 插入测试数据
        VisitRecordEntity entity1 = new VisitRecordEntity(); entity1.setVisitTime(LocalDateTime.now().minusHours(1)); visitRecordMapper.insert(entity1);
        VisitRecordEntity entity2 = new VisitRecordEntity(); entity2.setVisitTime(LocalDateTime.now().minusMinutes(30)); visitRecordMapper.insert(entity2);

        LocalDateTime start = LocalDateTime.now().minusHours(2);
        LocalDateTime end = LocalDateTime.now();
        long count = visitRecordMapper.countByDateRange(start, end);
        assertThat(count).isGreaterThanOrEqualTo(2L);
    }

    @Test
    @Transactional
    void testCountUniqueVisitorsByDateRange() {
        // 插入测试数据
        VisitRecordEntity entity1 = new VisitRecordEntity(); entity1.setVisitTime(LocalDateTime.now()); entity1.setUserId(1L); entity1.setVisitorType("REGISTERED"); visitRecordMapper.insert(entity1);
        VisitRecordEntity entity2 = new VisitRecordEntity(); entity2.setVisitTime(LocalDateTime.now()); entity2.setUserId(1L); entity1.setVisitorType("REGISTERED"); visitRecordMapper.insert(entity2);
        VisitRecordEntity entity3 = new VisitRecordEntity(); entity3.setVisitTime(LocalDateTime.now()); entity3.setIpAddress("*******"); entity3.setUserAgent("UA1"); entity3.setVisitorType("ANONYMOUS"); visitRecordMapper.insert(entity3);

        LocalDateTime start = LocalDateTime.now().minusHours(1);
        LocalDateTime end = LocalDateTime.now().plusHours(1);
        long uniqueCount = visitRecordMapper.countUniqueVisitorsByDateRange(start, end);
        assertThat(uniqueCount).isGreaterThanOrEqualTo(2L); // 1个注册用户 + 1个匿名用户
    }

    @Test
    @Transactional
    void testGetPageVisitStatsByDateRange() {
        // 插入测试数据
        VisitRecordEntity entity1 = new VisitRecordEntity(); entity1.setVisitTime(LocalDateTime.now()); entity1.setPageUrl("/home"); visitRecordMapper.insert(entity1);
        VisitRecordEntity entity2 = new VisitRecordEntity(); entity2.setVisitTime(LocalDateTime.now()); entity2.setPageUrl("/home"); visitRecordMapper.insert(entity2);
        VisitRecordEntity entity3 = new VisitRecordEntity(); entity3.setVisitTime(LocalDateTime.now()); entity3.setPageUrl("/products"); visitRecordMapper.insert(entity3);

        LocalDateTime start = LocalDateTime.now().minusHours(1);
        LocalDateTime end = LocalDateTime.now().plusHours(1);
        List<PageVisitStatsDTO> stats = visitRecordMapper.getPageVisitStatsByDateRange(start, end, 10);
        assertThat(stats).isNotEmpty();
        assertThat(stats.stream().anyMatch(s -> s.getPageUrl().equals("/home"))).isTrue();
    }
}
```

## 6. 注意事项

*   **MyBatis-Plus集成**: 继承 `BaseMapper` 使得该Mapper自动拥有强大的CRUD能力，减少了重复代码。
*   **SQL注解**: 大量使用了 `@Select` 注解直接编写SQL。这使得SQL逻辑清晰可见，但对于非常复杂的查询，可能导致注解过长，难以维护。此时可以考虑将SQL提取到XML文件中。
*   **参数绑定**: 使用 `@Param` 注解将Java方法参数绑定到SQL中的命名参数，有效防止SQL注入。
*   **性能优化**: 访问日志数据量庞大且查询频繁，Mapper中的SQL查询必须高度优化。这包括但不限于：
    *   为 `visit_time`, `user_id`, `session_id`, `ip_address`, `user_agent`, `referer`, `visitor_type` 等常用查询字段建立合适的索引。
    *   `COUNT(DISTINCT ...)` 操作可能性能开销较大，需要根据实际数据量和数据库类型进行优化。
    *   `LIKE CONCAT('%', ..., '%')` 模糊查询通常无法利用索引，可能导致全表扫描，应谨慎使用或考虑全文索引。
    *   `calculateAvgStayTimeByDateRange` 和 `getOnlineCountByHourOffset` 等方法中的SQL逻辑是简化的，在生产环境中需要更精确的实现。
*   **数据类型映射**: 确保Java方法参数和返回类型与SQL查询结果的字段类型正确映射。
*   **复杂SQL**: 某些查询（如 `countUniqueVisitorsByDateRange`, `getGeographicStatsByDateRange`, `getRealTimeTrendData`）涉及复杂的SQL逻辑，包括 `CASE WHEN`, `GROUP BY`, `DATE_FORMAT`, `UNIX_TIMESTAMP` 等，需要确保其正确性和性能。
*   **返回类型 `Object[]`**: 某些方法返回 `List<Object[]>`，这意味着在仓储实现层需要手动将 `Object[]` 转换为领域实体或DTO，增加了转换的复杂性。
*   **可读性**: 尽管SQL直接写在注解中，但其逻辑清晰，易于理解。