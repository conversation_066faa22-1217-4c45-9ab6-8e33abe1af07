# VisitRecordEntity.md

## 1. 文件概述

`VisitRecordEntity` 是分析模块中的一个数据库实体（PO），位于 `com.purchase.analytics.infrastructure.entity` 包中。它直接映射到数据库的 `website_visit_log` 表，用于持久化存储用户访问记录。该实体包含了访问记录的所有详细信息，如用户ID、会话ID、IP地址、用户代理、访问页面、引用页、访问者类型、访问时间、是否为独立访问以及客户端信息等。它是基础设施层与数据库交互的数据载体，通过MyBatis-Plus的注解实现了与数据库表的自动化ORM映射。

## 2. 核心功能

*   **数据持久化**: 定义了访问记录在数据库中的存储结构，是 `VisitRecord` 领域模型在持久化层的具体表现。
*   **MyBatis-Plus集成**: 通过 `@TableName`, `@TableId`, `@TableField`, `@FieldFill` 等注解与MyBatis-Plus框架深度集成，实现了主键自增长、字段映射和自动填充时间戳等功能，极大地简化了数据库操作。
*   **详细记录**: 包含了丰富的字段，能够全面记录用户访问的上下文信息，为后续的数据分析提供详尽的原始数据。
*   **时间戳管理**: `createdAt` 和 `updatedAt` 字段通过MyBatis-Plus的 `@FieldFill` 自动管理，确保了记录的创建和更新时间被准确捕获。
*   **访问者类型与唯一性**: 记录了 `visitorType` 和 `isUniqueVisit` 字段，支持对访问者进行分类和去重分析。

## 3. 属性说明

- **`id` (Long)**: 访问记录的唯一标识符，主键，由数据库自增长（`IdType.AUTO`）。
- **`userId` (Long)**: 访问用户的ID。如果用户未登录，可能为 `null`。
- **`sessionId` (String)**: 用户的会话ID，用于追踪用户在一段时间内的连续行为。
- **`ipAddress` (String)**: 客户端的IP地址。
- **`userAgent` (String)**: 客户端的用户代理字符串（User-Agent），包含浏览器和操作系统信息。
- **`pageUrl` (String)**: 用户访问的页面URL。
- **`referer` (String)**: 引导用户访问当前页面的上一页URL。
- **`visitorType` (String)**: 访问者类型（例如：`REGISTERED`, `ANONYMOUS`），通常存储枚举的字符串名称。
- **`visitTime` (LocalDateTime)**: 访问发生的时间。
- **`isUniqueVisit` (Boolean)**: 标记此次访问是否被识别为独立访问（`true` 为独立访问，`false` 为重复访问）。
- **`clientInfo` (String)**: 客户端的额外信息，通常以JSON字符串形式存储，例如屏幕分辨率、时区等。
- **`createdAt` (LocalDateTime)**: 记录创建时间，由MyBatis-Plus自动填充。
- **`updatedAt` (LocalDateTime)**: 记录最后更新时间，由MyBatis-Plus自动填充。

## 4. 业务规则

*   **数据映射**: `VisitRecordEntity` 的字段与 `website_visit_log` 数据库表的列一一对应。任何对实体字段的修改都需要确保与数据库表结构的一致性。
*   **自动填充**: `createdAt` 和 `updatedAt` 字段的自动填充依赖于MyBatis-Plus的配置（如 `MyMetaObjectHandler`），确保了时间戳的准确性。
*   **类型转换**: `visitorType` 字段在数据库中存储为字符串，与领域层的 `VisitorType` 枚举进行转换。`isUniqueVisit` 存储为布尔值。
*   **JSON字段**: `clientInfo` 字段存储JSON字符串，其解析和构建逻辑应在仓储实现层或更上层进行处理。
*   **唯一访问逻辑**: `isUniqueVisit` 字段的值由领域服务（如 `VisitDeduplicationService`）在持久化前计算并设置，反映了业务去重规则。

## 5. 使用示例

```java
// 1. 在 VisitRecordMapper 接口中定义对 VisitRecordEntity 的操作
@Mapper
public interface VisitRecordMapper extends BaseMapper<VisitRecordEntity> {
    // 继承 BaseMapper 提供了基本的 CRUD 方法
    // 也可以在此定义自定义查询方法，例如：
    @Select("SELECT COUNT(*) FROM website_visit_log WHERE visit_time BETWEEN #{startTime} AND #{endTime}")
    long countByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}

// 2. 在 VisitRecordRepositoryImpl 中将领域实体转换为 PO 进行持久化
@Repository
public class VisitRecordRepositoryImpl implements VisitRecordRepository {
    @Autowired
    private VisitRecordMapper visitRecordMapper;

    @Override
    public VisitRecord save(VisitRecord domainRecord) {
        VisitRecordEntity entity = new VisitRecordEntity();
        // 将领域实体 VisitRecord 的属性复制到 VisitRecordEntity
        entity.setId(domainRecord.getId()); // 如果是更新操作，ID需要设置
        entity.setUserId(domainRecord.getUserId());
        entity.setSessionId(domainRecord.getSessionId());
        entity.setIpAddress(domainRecord.getIpAddress());
        entity.setUserAgent(domainRecord.getUserAgent());
        entity.setPageUrl(domainRecord.getPageUrl());
        entity.setReferer(domainRecord.getReferer());
        entity.setVisitorType(domainRecord.getVisitorType().name()); // 枚举转字符串
        entity.setVisitTime(domainRecord.getVisitTime().getValue()); // 值对象取值
        entity.setIsUniqueVisit(domainRecord.isUniqueVisit());
        // entity.setClientInfo(JSON.toJSONString(domainRecord.getClientInfo())); // 如果ClientInfo是复杂对象

        if (entity.getId() == null) {
            visitRecordMapper.insert(entity);
        } else {
            visitRecordMapper.updateById(entity);
        }
        // 将数据库生成的ID（如果是插入操作）设置回领域实体
        domainRecord.setId(entity.getId()); // 假设VisitRecord有setId方法
        return domainRecord;
    }

    @Override
    public Optional<VisitRecord> findById(Long id) {
        VisitRecordEntity entity = visitRecordMapper.selectById(id);
        if (entity == null) {
            return Optional.empty();
        }
        // 将 VisitRecordEntity 转换为领域实体 VisitRecord
        VisitRecord domainRecord = VisitRecord.createForRegisteredUser(
            entity.getUserId(), entity.getSessionId(), entity.getIpAddress(), 
            entity.getPageUrl(), entity.getVisitTime()
        ); // 简化创建，实际应根据visitorType调用不同工厂方法
        domainRecord.setId(entity.getId());
        if (Boolean.FALSE.equals(entity.getIsUniqueVisit())) {
            domainRecord.markAsNonUniqueVisit();
        }
        // ... 更多属性转换 ...
        return Optional.of(domainRecord);
    }
}

// 3. 测试示例
@SpringBootTest
class VisitRecordEntityTest {
    @Autowired
    private VisitRecordMapper visitRecordMapper;

    @Test
    @Transactional
    void testInsertAndRetrieve() {
        VisitRecordEntity entity = new VisitRecordEntity();
        entity.setUserId(100L);
        entity.setSessionId("test_session_123");
        entity.setIpAddress("***********");
        entity.setUserAgent("TestAgent");
        entity.setPageUrl("/test/page");
        entity.setReferer("http://example.com");
        entity.setVisitorType("REGISTERED");
        entity.setVisitTime(LocalDateTime.now());
        entity.setIsUniqueVisit(true);
        entity.setClientInfo("{\"screen\":\"1920x1080\"}");

        int result = visitRecordMapper.insert(entity);
        assertThat(result).isEqualTo(1);
        assertThat(entity.getId()).isNotNull();
        assertThat(entity.getCreatedAt()).isNotNull();
        assertThat(entity.getUpdatedAt()).isNotNull();

        VisitRecordEntity retrievedEntity = visitRecordMapper.selectById(entity.getId());
        assertThat(retrievedEntity).isNotNull();
        assertThat(retrievedEntity.getUserId()).isEqualTo(100L);
        assertThat(retrievedEntity.getVisitorType()).isEqualTo("REGISTERED");
    }
}
```

## 6. 注意事项

*   **PO与领域实体**: `VisitRecordEntity` 是基础设施层的持久化对象，其主要职责是与数据库表进行映射。它与领域层的 `VisitRecord` 聚合根是不同的概念，两者之间需要进行明确的转换。
*   **MyBatis-Plus**: 充分利用MyBatis-Plus的注解和功能，可以大大简化数据访问层的开发。特别是 `@FieldFill` 对于 `createdAt` 和 `updatedAt` 的自动填充非常方便。
*   **性能**: 访问日志数据量通常非常大，因此 `website_visit_log` 表的设计和索引优化至关重要。例如，为 `visit_time`, `user_id`, `session_id`, `ip_address` 等常用查询字段建立索引。
*   **数据类型映射**: 确保Java实体中的数据类型与数据库表中的字段类型正确映射。例如，`LocalDateTime` 映射到数据库的 `DATETIME` 或 `TIMESTAMP` 类型。
*   **JSON字段**: `clientInfo` 字段存储JSON字符串，这意味着在Java代码中需要使用JSON库（如Jackson, Gson, Fastjson）进行序列化和反序列化。
*   **数据安全**: 访问日志可能包含敏感信息（如IP地址、用户代理），在存储和展示时需要考虑数据脱敏和访问控制。
*   **可扩展性**: 如果未来需要记录更多访问细节，可以在 `VisitRecordEntity` 中添加相应的字段，并更新Mapper和Repository的实现。