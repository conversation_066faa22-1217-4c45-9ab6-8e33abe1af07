# VisitRecordRepositoryImpl.md

## 1. 文件概述

`VisitRecordRepositoryImpl.java` 是分析模块中 `VisitRecordRepository` 仓储接口的实现类，位于 `com.purchase.analytics.infrastructure.repository` 包中。它作为基础设施层的一部分，负责将领域层的 `VisitRecord` 聚合根与底层数据存储（通过 `VisitRecordMapper`）进行桥接。该实现类封装了领域对象（Domain Object）与持久化对象（PO，即 `VisitRecordEntity`）之间的转换逻辑，并委托 `VisitRecordMapper` 执行具体的数据库操作，从而实现了领域层与数据持久化技术的解耦。

## 2. 核心功能

*   **仓储接口实现**: 实现了 `VisitRecordRepository` 接口中定义的所有持久化和查询方法。
*   **领域对象与PO转换**: 负责将领域层的 `VisitRecord` 对象转换为基础设施层的 `VisitRecordEntity` 进行数据库操作，并在从数据库读取数据后，将 `VisitRecordEntity` 转换回 `VisitRecord` 领域对象。
*   **数据持久化**: 通过调用 `VisitRecordMapper` 的方法，执行 `VisitRecord` 聚合根的插入、更新、查询和统计等数据库操作。
*   **查询委托**: 将所有复杂的查询逻辑委托给 `VisitRecordMapper`，自身主要负责数据转换和结果的组装。
*   **解耦**: 作为基础设施层的一部分，它将领域模型从具体的数据库实现细节中解放出来，使得领域模型更加纯粹和专注于业务逻辑。

## 3. 接口说明

`VisitRecordRepositoryImpl` 实现了 `VisitRecordRepository` 接口的所有方法。以下是其主要方法的实现细节：

### 3.1 持久化操作

#### save - 保存访问记录
*   **方法签名**: `VisitRecord save(VisitRecord visitRecord)`
*   **描述**: 将 `VisitRecord` 领域对象转换为 `VisitRecordEntity`，然后通过 `visitRecordMapper` 进行插入或更新操作。如果 `visitRecord` 包含ID，则执行更新；否则执行插入。
*   **参数**:
    *   `visitRecord` (VisitRecord): 待保存的访问记录领域对象。
*   **返回值**: `VisitRecord` - 保存后的访问记录领域对象（包含数据库生成的ID）。
*   **业务逻辑**: 
    1.  调用 `convertToEntity` 方法将 `VisitRecord` 转换为 `VisitRecordEntity`。
    2.  根据 `entity.getId()` 是否为 `null` 判断是执行 `insert` 还是 `updateById`。
    3.  插入操作后，通过 `visitRecordMapper.selectById(entity.getId())` 重新查询以获取完整的实体（包括数据库生成的ID和自动填充的时间戳）。
    4.  调用 `convertToDomain` 方法将保存后的 `VisitRecordEntity` 转换回 `VisitRecord` 领域对象并返回。

### 3.2 查询操作

#### findById - 根据ID查找访问记录
*   **方法签名**: `Optional<VisitRecord> findById(Long id)`
*   **描述**: 通过 `visitRecordMapper` 根据ID查询 `VisitRecordEntity`，然后将其转换为 `VisitRecord` 领域对象。
*   **参数**:
    *   `id` (Long): 访问记录的ID。
*   **返回值**: `Optional<VisitRecord>` - 包含匹配的访问记录，如果不存在则为 `Optional.empty()`。

#### findRecentVisitsByUser - 查找用户最近的访问记录
*   **方法签名**: `List<VisitRecord> findRecentVisitsByUser(Long userId, LocalDateTime since)`
*   **描述**: 调用 `visitRecordMapper.findRecentVisitsByUser` 获取 `VisitRecordEntity` 列表，然后将每个实体转换为 `VisitRecord` 领域对象。
*   **参数**:
    *   `userId` (Long): 用户ID。
    *   `since` (LocalDateTime): 起始时间。
*   **返回值**: `List<VisitRecord>` - 匹配的访问记录领域对象列表。

#### countByDateRange - 统计指定时间范围内的访问次数
*   **方法签名**: `long countByDateRange(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 直接委托 `visitRecordMapper.countByDateRange` 执行统计查询。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
*   **返回值**: `long` - 访问次数。

#### getRealTimeTrendData - 获取实时趋势数据
*   **方法签名**: `List<Object[]> getRealTimeTrendData(LocalDateTime startTime, LocalDateTime endTime, String granularity)`
*   **描述**: 根据时间粒度委托 `visitRecordMapper` 调用不同的趋势查询方法（`getRealTimeTrendDataByMinute`, `getRealTimeTrendDataByHour`, `getRealTimeTrendDataByDay`）。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
    *   `granularity` (String): 时间粒度（`minute`, `hour`, `day`）。
*   **返回值**: `List<Object[]>` - 实时趋势数据列表。

### 3.3 辅助转换方法

#### convertToEntity - 领域对象转换为数据库实体
*   **方法签名**: `private VisitRecordEntity convertToEntity(VisitRecord visitRecord)`
*   **描述**: 将 `VisitRecord` 领域对象转换为 `VisitRecordEntity` 持久化对象。负责处理字段映射和类型转换（如枚举到字符串）。

#### convertToDomain - 数据库实体转换为领域对象
*   **方法签名**: `private VisitRecord convertToDomain(VisitRecordEntity entity)`
*   **描述**: 将 `VisitRecordEntity` 持久化对象转换为 `VisitRecord` 领域对象。负责处理字段映射和类型转换（如字符串到枚举），并根据 `visitorType` 调用 `VisitRecord` 的不同工厂方法。

## 4. 业务规则

*   **领域-基础设施转换**: `VisitRecordRepositoryImpl` 的核心职责是实现领域层 `VisitRecordRepository` 接口，并处理领域对象与基础设施层持久化对象之间的转换。这种转换是仓储模式的关键。
*   **数据一致性**: 在 `save` 方法中，通过重新查询数据库来获取保存后的实体，确保返回的领域对象包含了数据库生成的ID和自动填充的时间戳，保证了数据的一致性。
*   **异常处理**: 仓储实现应捕获底层数据访问异常，并转换为领域层可理解的异常类型，避免底层技术细节泄露到领域层。
*   **性能考量**: 尽管仓储本身不直接执行复杂的业务逻辑，但其对Mapper的调用和数据转换的效率会直接影响整体性能。对于批量操作和高并发场景，需要确保底层Mapper和数据库的性能优化。

## 5. 使用示例

```java
// 1. 在 VisitTrackingApplicationService 中调用 VisitRecordRepositoryImpl
@Service
public class VisitTrackingApplicationServiceImpl implements VisitTrackingApplicationService {
    @Autowired
    private VisitRecordRepository visitRecordRepository;
    @Autowired
    private VisitDeduplicationService deduplicationService;

    @Override
    @Transactional // 事务管理在应用服务层
    public TrackVisitResult trackVisit(TrackVisitCommand command) {
        VisitRecord newVisit;
        if (command.getUserId() != null) {
            newVisit = VisitRecord.createForRegisteredUser(
                command.getUserId(), command.getSessionId(), command.getIpAddress(), 
                command.getPageUrl(), command.getVisitTime()
            );
        } else {
            newVisit = VisitRecord.createForAnonymousUser(
                command.getSessionId(), command.getIpAddress(), command.getUserAgent(), 
                command.getPageUrl(), command.getVisitTime()
            );
        }

        // 领域服务判断是否为唯一访问
        boolean isUnique = deduplicationService.isUniqueVisit(newVisit, 
            visitRecordRepository.findRecentVisitsByUser(newVisit.getUserId(), newVisit.getVisitTime().getValue().minusMinutes(5)) // 简化获取最近访问
        );
        if (!isUnique) {
            newVisit.markAsNonUniqueVisit();
        }

        // 通过仓储保存领域对象
        VisitRecord savedRecord = visitRecordRepository.save(newVisit);
        return TrackVisitResult.success(savedRecord.getId().toString(), isUnique);
    }
}

// 2. 在 StatisticsQueryApplicationService 中调用 VisitRecordRepositoryImpl
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private VisitRecordRepository visitRecordRepository;

    public RealTimeStatsResult queryRealTimeStats(RealTimeStatsQuery query) {
        // ... 参数验证 ...
        LocalDateTime startOfDay = query.getDate().atStartOfDay();
        LocalDateTime endOfDay = query.getDate().atTime(LocalTime.MAX);

        long todayVisits = visitRecordRepository.countByDateRange(startOfDay, endOfDay);
        long uniqueVisitors = visitRecordRepository.countUniqueVisitorsByDateRange(startOfDay, endOfDay);

        return RealTimeStatsResult.success(
            (int) todayVisits, (int) visitRecordRepository.countByDateRange(LocalDateTime.now().withHour(LocalDateTime.now().getHour()).withMinute(0).withSecond(0).withNano(0), LocalDateTime.now().withHour(LocalDateTime.now().getHour()).withMinute(59).withSecond(59).withNano(999999999)), (int) uniqueVisitors,
            (int) visitRecordRepository.countRegisteredVisitsByDate(query.getDate()), (int) visitRecordRepository.countAnonymousVisitsByDate(query.getDate())
        );
    }
}

// 3. 测试示例
@SpringBootTest
class VisitRecordRepositoryImplTest {
    @MockBean
    private VisitRecordMapper visitRecordMapper;

    @Autowired
    private VisitRecordRepository visitRecordRepository;

    @Test
    void testSaveNewVisitRecord() {
        VisitRecord domainRecord = VisitRecord.createForRegisteredUser(1L, "s1", "1.1.1.1", "/home", LocalDateTime.now());
        VisitRecordEntity mockEntity = new VisitRecordEntity();
        mockEntity.setId(100L); // 模拟数据库生成ID
        // 模拟mapper的insert方法，当传入任何VisitRecordEntity时，返回1，并设置mockEntity的ID
        doAnswer(invocation -> {
            VisitRecordEntity arg = invocation.getArgument(0);
            arg.setId(100L);
            return 1;
        }).when(visitRecordMapper).insert(any(VisitRecordEntity.class));
        
        // 模拟mapper的selectById方法，返回带有ID的mockEntity
        when(visitRecordMapper.selectById(100L)).thenReturn(mockEntity);

        VisitRecord savedRecord = visitRecordRepository.save(domainRecord);

        assertThat(savedRecord.getId()).isEqualTo(100L);
        verify(visitRecordMapper, times(1)).insert(any(VisitRecordEntity.class));
        verify(visitRecordMapper, times(1)).selectById(100L);
    }

    @Test
    void testFindById() {
        VisitRecordEntity mockEntity = new VisitRecordEntity();
        mockEntity.setId(1L);
        mockEntity.setUserId(10L);
        mockEntity.setVisitorType("REGISTERED");
        mockEntity.setPageUrl("/test");
        mockEntity.setVisitTime(LocalDateTime.now());
        when(visitRecordMapper.selectById(1L)).thenReturn(mockEntity);

        Optional<VisitRecord> foundRecord = visitRecordRepository.findById(1L);

        assertThat(foundRecord).isPresent();
        assertThat(foundRecord.get().getId()).isEqualTo(1L);
        assertThat(foundRecord.get().getUserId()).isEqualTo(10L);
    }
}
```

## 6. 注意事项

*   **DDD分层**: `VisitRecordRepositoryImpl` 明确属于基础设施层，其职责是实现领域层定义的仓储接口，并处理与具体持久化技术相关的细节（如MyBatis-Plus的Mapper调用和PO转换）。
*   **领域对象与PO的转换**: 这是该实现类的核心和复杂之处。需要确保 `convertToEntity` 和 `convertToDomain` 方法能够准确、完整地进行数据映射，特别是对于枚举、值对象和复杂类型。
*   **事务管理**: 仓储方法本身不应包含事务逻辑，事务应由调用它的应用服务层进行管理（通过 `@Transactional` 注解）。
*   **性能优化**: 仓储实现应充分利用底层Mapper提供的批量操作和优化查询，以应对大量访问记录的读写需求。例如，`save` 方法在插入后重新查询以获取ID，这在某些场景下可能引入额外的性能开销，可以考虑在Mapper中直接返回自增ID。
*   **异常处理**: 仓储实现应捕获由Mapper或数据库抛出的底层异常，并将其转换为领域层可理解的异常类型，避免技术细节泄露。
*   **测试**: 仓储实现应该进行集成测试，以验证其与数据库的正确交互。单元测试可以通过Mock `VisitRecordMapper` 来进行。
*   **依赖注入**: 通过Spring的 `@Autowired` 注入 `VisitRecordMapper`，体现了依赖倒置原则。