package com.purchase.analytics.infrastructure.dto;

import java.math.BigDecimal;

/**
 * 页面访问统计DTO
 * 用于接收MyBatis查询结果
 */
public class PageVisitStatsDTO {
    private String pageUrl;
    private String pageTitle;
    private String category;
    private Long visits;
    private Long uniqueVisitors;
    private BigDecimal avgStayTime;
    
    // 默认构造函数
    public PageVisitStatsDTO() {
    }
    
    // 全参构造函数
    public PageVisitStatsDTO(String pageUrl, String pageTitle, String category, 
                           Long visits, Long uniqueVisitors, BigDecimal avgStayTime) {
        this.pageUrl = pageUrl;
        this.pageTitle = pageTitle;
        this.category = category;
        this.visits = visits;
        this.uniqueVisitors = uniqueVisitors;
        this.avgStayTime = avgStayTime;
    }
    
    // Getter和Setter方法
    public String getPageUrl() {
        return pageUrl;
    }
    
    public void setPageUrl(String pageUrl) {
        this.pageUrl = pageUrl;
    }
    
    public String getPageTitle() {
        return pageTitle;
    }
    
    public void setPageTitle(String pageTitle) {
        this.pageTitle = pageTitle;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public Long getVisits() {
        return visits;
    }
    
    public void setVisits(Long visits) {
        this.visits = visits;
    }
    
    public Long getUniqueVisitors() {
        return uniqueVisitors;
    }
    
    public void setUniqueVisitors(Long uniqueVisitors) {
        this.uniqueVisitors = uniqueVisitors;
    }
    
    public BigDecimal getAvgStayTime() {
        return avgStayTime;
    }
    
    public void setAvgStayTime(BigDecimal avgStayTime) {
        this.avgStayTime = avgStayTime;
    }
    
    @Override
    public String toString() {
        return "PageVisitStatsDTO{" +
                "pageUrl='" + pageUrl + '\'' +
                ", pageTitle='" + pageTitle + '\'' +
                ", category='" + category + '\'' +
                ", visits=" + visits +
                ", uniqueVisitors=" + uniqueVisitors +
                ", avgStayTime=" + avgStayTime +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        PageVisitStatsDTO that = (PageVisitStatsDTO) o;
        
        if (!pageUrl.equals(that.pageUrl)) return false;
        if (!pageTitle.equals(that.pageTitle)) return false;
        if (!category.equals(that.category)) return false;
        if (!visits.equals(that.visits)) return false;
        if (!uniqueVisitors.equals(that.uniqueVisitors)) return false;
        return avgStayTime.equals(that.avgStayTime);
    }
    
    @Override
    public int hashCode() {
        int result = pageUrl.hashCode();
        result = 31 * result + pageTitle.hashCode();
        result = 31 * result + category.hashCode();
        result = 31 * result + visits.hashCode();
        result = 31 * result + uniqueVisitors.hashCode();
        result = 31 * result + avgStayTime.hashCode();
        return result;
    }
}
