# GeographicStatsQuery.md

## 1. 文件概述

`GeographicStatsQuery` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.dto` 包中。它用于封装查询地理统计数据时的请求参数。这个DTO包含了查询的日期范围（开始日期和结束日期）以及地图类型（例如 `china` 或 `world`），旨在为前端提供灵活的地理数据可视化查询能力。它的核心作用是作为应用服务层接收前端或上层服务请求的标准化输入，确保查询参数的类型安全和结构清晰。

## 2. 核心功能

*   **参数封装**: 封装了查询地理统计数据所需的日期范围和地图类型参数。
*   **灵活性**: 支持通过设置不同的 `startDate`, `endDate` 和 `mapType` 来查询不同时间段和不同区域的地理统计数据。
*   **类型安全**: 使用 `LocalDate` 类型确保日期数据的正确性。
*   **简洁性**: 设计轻量级，只包含必要的查询字段，避免了不必要的复杂性。

## 3. 属性说明

- **`startDate` (LocalDate)**: 查询地理统计数据的开始日期。
- **`endDate` (LocalDate)**: 查询地理统计数据的结束日期。
- **`mapType` (String)**: 地图类型，用于指定统计数据的地理范围，例如 `china`（中国）或 `world`（全球）。

## 4. 业务规则

*   **日期范围有效性**: `startDate` 必须早于或等于 `endDate`。在Controller或Application Service层接收到此DTO后，应进行此项校验。
*   **地图类型枚举**: `mapType` 字段的值应限定在预定义的枚举或常量中，例如 `china`, `world`。在服务层应进行校验。
*   **数据粒度**: 地理统计通常是按天聚合的，因此 `LocalDate` 字段足以满足需求。

## 5. 使用示例

```java
// 1. 在 Controller 中构建 GeographicStatsQuery
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/geographic-stats")
    public ApiResponse<GeographicStatsResult> getGeographicStats(
            @RequestParam(defaultValue = "china") String mapType,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            GeographicStatsQuery query = new GeographicStatsQuery();
            query.setMapType(mapType);

            // 设置默认日期范围（最近7天）
            if (startDate != null && endDate != null) {
                query.setStartDate(startDate);
                query.setEndDate(endDate);
            } else {
                query.setStartDate(LocalDate.now().minusDays(7));
                query.setEndDate(LocalDate.now());
            }

            GeographicStatsResult result = statisticsQueryService.queryGeographicStats(query);

            if (result.isSuccess()) {
                return ApiResponse.success(result, "查询成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }

        } catch (Exception e) {
            return ApiResponse.error("查询地理统计失败: " + e.getMessage());
        }
    }
}

// 2. 在 StatisticsQueryApplicationService 中使用 GeographicStatsQuery
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private GeographicStatisticsRepository geographicStatisticsRepository;

    public GeographicStatsResult queryGeographicStats(GeographicStatsQuery query) {
        // 业务逻辑：根据query的日期范围和mapType从数据库或缓存中获取地理统计数据
        // 假设这里调用一个领域服务或仓储来获取数据
        List<GeographicData> data = geographicStatisticsRepository.findGeographicData(
            query.getStartDate(), query.getEndDate(), query.getMapType()
        );

        // 将领域数据转换为GeographicStatsResult DTO
        GeographicStatsResult result = new GeographicStatsResult();
        result.setSuccess(true);
        result.setMessage("查询成功");
        result.setMapType(query.getMapType());
        result.setData(data.stream().map(d -> new GeographicStatsResult.LocationStats(d.getRegion(), d.getVisits())).collect(Collectors.toList()));
        return result;
    }
}

// 3. 测试示例
@SpringBootTest
class GeographicStatsQueryTest {
    @Test
    void testConstructorAndGetters() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 31);
        String mapType = "world";
        GeographicStatsQuery query = new GeographicStatsQuery(startDate, endDate, mapType);

        assertThat(query.getStartDate()).isEqualTo(startDate);
        assertThat(query.getEndDate()).isEqualTo(endDate);
        assertThat(query.getMapType()).isEqualTo(mapType);
    }

    @Test
    void testSetters() {
        GeographicStatsQuery query = new GeographicStatsQuery();
        LocalDate newStartDate = LocalDate.of(2024, 2, 1);
        LocalDate newEndDate = LocalDate.of(2024, 2, 29);
        String newMapType = "china";

        query.setStartDate(newStartDate);
        query.setEndDate(newEndDate);
        query.setMapType(newMapType);

        assertThat(query.getStartDate()).isEqualTo(newStartDate);
        assertThat(query.getEndDate()).isEqualTo(newEndDate);
        assertThat(query.getMapType()).isEqualTo(newMapType);
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `GeographicStatsQuery` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 尽管当前DTO有Setter方法，但对于查询参数DTO，如果可能，最好设计为不可变对象（所有字段 `final`，只提供全参构造函数），以增强线程安全性和数据一致性。
*   **日期处理**: 使用 `java.time.LocalDate` 是处理日期的最佳实践，避免了 `java.util.Date` 的时区问题。
*   **参数校验**: 尽管DTO本身没有JSR-303注解，但在Controller或Application Service层接收到此DTO后，仍需对 `startDate` 和 `endDate` 的逻辑关系以及 `mapType` 的有效性进行业务校验。
*   **可扩展性**: 如果未来地理统计需要更多查询条件（如按城市、按省份等），可以在此DTO中添加相应属性。