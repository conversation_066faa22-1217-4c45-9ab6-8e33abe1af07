package com.purchase.analytics.application.result;

import java.util.List;

/**
 * 时段分析结果
 */
public class TimeAnalysisResult {
    private final List<HourlyData> hourlyData;
    private final List<WeeklyData> weeklyData;
    private final List<Integer> peakHours;
    
    public TimeAnalysisResult(List<HourlyData> hourlyData, 
                             List<WeeklyData> weeklyData,
                             List<Integer> peakHours) {
        this.hourlyData = hourlyData;
        this.weeklyData = weeklyData;
        this.peakHours = peakHours;
    }
    
    public List<HourlyData> getHourlyData() {
        return hourlyData;
    }
    
    public List<WeeklyData> getWeeklyData() {
        return weeklyData;
    }
    
    public List<Integer> getPeakHours() {
        return peakHours;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        TimeAnalysisResult that = (TimeAnalysisResult) o;
        
        if (!hourlyData.equals(that.hourlyData)) return false;
        if (!weeklyData.equals(that.weeklyData)) return false;
        return peakHours.equals(that.peakHours);
    }
    
    @Override
    public int hashCode() {
        int result = hourlyData.hashCode();
        result = 31 * result + weeklyData.hashCode();
        result = 31 * result + peakHours.hashCode();
        return result;
    }
    
    /**
     * 小时数据
     */
    public static class HourlyData {
        private final int hour;
        private final int visits;
        private final int uniqueVisitors;
        
        public HourlyData(int hour, int visits, int uniqueVisitors) {
            this.hour = hour;
            this.visits = visits;
            this.uniqueVisitors = uniqueVisitors;
        }
        
        public int getHour() {
            return hour;
        }
        
        public int getVisits() {
            return visits;
        }
        
        public int getUniqueVisitors() {
            return uniqueVisitors;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            
            HourlyData that = (HourlyData) o;
            
            if (hour != that.hour) return false;
            if (visits != that.visits) return false;
            return uniqueVisitors == that.uniqueVisitors;
        }
        
        @Override
        public int hashCode() {
            int result = hour;
            result = 31 * result + visits;
            result = 31 * result + uniqueVisitors;
            return result;
        }
    }
    
    /**
     * 周数据
     */
    public static class WeeklyData {
        private final int dayOfWeek; // 1=Monday, 7=Sunday
        private final String dayName;
        private final int visits;
        private final int uniqueVisitors;
        
        public WeeklyData(int dayOfWeek, String dayName, int visits, int uniqueVisitors) {
            this.dayOfWeek = dayOfWeek;
            this.dayName = dayName;
            this.visits = visits;
            this.uniqueVisitors = uniqueVisitors;
        }
        
        public int getDayOfWeek() {
            return dayOfWeek;
        }
        
        public String getDayName() {
            return dayName;
        }
        
        public int getVisits() {
            return visits;
        }
        
        public int getUniqueVisitors() {
            return uniqueVisitors;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            
            WeeklyData that = (WeeklyData) o;
            
            if (dayOfWeek != that.dayOfWeek) return false;
            if (visits != that.visits) return false;
            if (uniqueVisitors != that.uniqueVisitors) return false;
            return dayName.equals(that.dayName);
        }
        
        @Override
        public int hashCode() {
            int result = dayOfWeek;
            result = 31 * result + dayName.hashCode();
            result = 31 * result + visits;
            result = 31 * result + uniqueVisitors;
            return result;
        }
    }
}
