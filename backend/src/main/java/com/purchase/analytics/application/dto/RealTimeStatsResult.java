package com.purchase.analytics.application.dto;

import java.time.LocalDateTime;

/**
 * 实时统计结果DTO
 */
public class RealTimeStatsResult {
    
    private boolean success;
    private Integer todayVisits;
    private Integer currentHourVisits;
    private Integer uniqueVisitors;
    private Integer registeredUserVisits;
    private Integer anonymousVisits;
    private Integer peakHour;
    private LocalDateTime lastUpdated;
    private String message;
    
    public RealTimeStatsResult() {}
    
    public static RealTimeStatsResult success(Integer todayVisits, Integer currentHourVisits, 
                                            Integer uniqueVisitors, Integer registeredUserVisits, 
                                            Integer anonymousVisits) {
        RealTimeStatsResult result = new RealTimeStatsResult();
        result.success = true;
        result.todayVisits = todayVisits;
        result.currentHourVisits = currentHourVisits;
        result.uniqueVisitors = uniqueVisitors;
        result.registeredUserVisits = registeredUserVisits;
        result.anonymousVisits = anonymousVisits;
        result.lastUpdated = LocalDateTime.now();
        result.message = "查询成功";
        return result;
    }
    
    public static RealTimeStatsResult failure(String message) {
        RealTimeStatsResult result = new RealTimeStatsResult();
        result.success = false;
        result.message = message;
        return result;
    }
    
    // Getters and Setters
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public Integer getTodayVisits() { return todayVisits; }
    public void setTodayVisits(Integer todayVisits) { this.todayVisits = todayVisits; }
    
    public Integer getCurrentHourVisits() { return currentHourVisits; }
    public void setCurrentHourVisits(Integer currentHourVisits) { this.currentHourVisits = currentHourVisits; }
    
    public Integer getUniqueVisitors() { return uniqueVisitors; }
    public void setUniqueVisitors(Integer uniqueVisitors) { this.uniqueVisitors = uniqueVisitors; }
    
    public Integer getRegisteredUserVisits() { return registeredUserVisits; }
    public void setRegisteredUserVisits(Integer registeredUserVisits) { this.registeredUserVisits = registeredUserVisits; }
    
    public Integer getAnonymousVisits() { return anonymousVisits; }
    public void setAnonymousVisits(Integer anonymousVisits) { this.anonymousVisits = anonymousVisits; }
    
    public Integer getPeakHour() { return peakHour; }
    public void setPeakHour(Integer peakHour) { this.peakHour = peakHour; }
    
    public LocalDateTime getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
}
