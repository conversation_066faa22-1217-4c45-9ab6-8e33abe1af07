package com.purchase.analytics.application.result;

import java.util.List;

/**
 * 设备分析结果
 */
public class DeviceAnalysisResult {
    private final int desktop;
    private final int mobile;
    private final int tablet;
    private final List<BrowserInfo> browsers;
    private final List<OperatingSystemInfo> operatingSystems;
    
    public DeviceAnalysisResult(int desktop, 
                               int mobile, 
                               int tablet,
                               List<BrowserInfo> browsers,
                               List<OperatingSystemInfo> operatingSystems) {
        this.desktop = desktop;
        this.mobile = mobile;
        this.tablet = tablet;
        this.browsers = browsers;
        this.operatingSystems = operatingSystems;
    }
    
    public int getDesktop() {
        return desktop;
    }
    
    public int getMobile() {
        return mobile;
    }
    
    public int getTablet() {
        return tablet;
    }
    
    public List<BrowserInfo> getBrowsers() {
        return browsers;
    }
    
    public List<OperatingSystemInfo> getOperatingSystems() {
        return operatingSystems;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        DeviceAnalysisResult that = (DeviceAnalysisResult) o;
        
        if (desktop != that.desktop) return false;
        if (mobile != that.mobile) return false;
        if (tablet != that.tablet) return false;
        if (!browsers.equals(that.browsers)) return false;
        return operatingSystems.equals(that.operatingSystems);
    }
    
    @Override
    public int hashCode() {
        int result = desktop;
        result = 31 * result + mobile;
        result = 31 * result + tablet;
        result = 31 * result + browsers.hashCode();
        result = 31 * result + operatingSystems.hashCode();
        return result;
    }
    
    /**
     * 浏览器信息
     */
    public static class BrowserInfo {
        private final String name;
        private final String version;
        private final int value;
        private final String color;
        
        public BrowserInfo(String name, String version, int value, String color) {
            this.name = name;
            this.version = version;
            this.value = value;
            this.color = color;
        }
        
        public String getName() {
            return name;
        }
        
        public String getVersion() {
            return version;
        }
        
        public int getValue() {
            return value;
        }
        
        public String getColor() {
            return color;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            
            BrowserInfo that = (BrowserInfo) o;
            
            if (value != that.value) return false;
            if (!name.equals(that.name)) return false;
            if (!version.equals(that.version)) return false;
            return color.equals(that.color);
        }
        
        @Override
        public int hashCode() {
            int result = name.hashCode();
            result = 31 * result + version.hashCode();
            result = 31 * result + value;
            result = 31 * result + color.hashCode();
            return result;
        }
    }
    
    /**
     * 操作系统信息
     */
    public static class OperatingSystemInfo {
        private final String name;
        private final String version;
        private final int value;
        private final String color;
        
        public OperatingSystemInfo(String name, String version, int value, String color) {
            this.name = name;
            this.version = version;
            this.value = value;
            this.color = color;
        }
        
        public String getName() {
            return name;
        }
        
        public String getVersion() {
            return version;
        }
        
        public int getValue() {
            return value;
        }
        
        public String getColor() {
            return color;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            
            OperatingSystemInfo that = (OperatingSystemInfo) o;
            
            if (value != that.value) return false;
            if (!name.equals(that.name)) return false;
            if (!version.equals(that.version)) return false;
            return color.equals(that.color);
        }
        
        @Override
        public int hashCode() {
            int result = name.hashCode();
            result = 31 * result + version.hashCode();
            result = 31 * result + value;
            result = 31 * result + color.hashCode();
            return result;
        }
    }
}
