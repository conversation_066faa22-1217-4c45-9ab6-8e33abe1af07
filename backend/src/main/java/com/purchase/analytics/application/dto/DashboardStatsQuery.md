# DashboardStatsQuery.md

## 1. 文件概述

`DashboardStatsQuery` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.dto` 包中。它用于封装查询仪表盘统计数据时的请求参数。这个DTO设计简洁，主要包含一个日期字段，用于指定查询哪一天的仪表盘数据。它的核心作用是作为应用服务层接收前端或上层服务请求的标准化输入，确保查询参数的类型安全和结构清晰。

## 2. 核心功能

*   **参数封装**: 封装了查询仪表盘统计数据所需的日期参数。
*   **默认值**: 提供了无参构造函数，默认将查询日期设置为当前日期，方便快速查询今日数据。
*   **类型安全**: 使用 `LocalDate` 类型确保日期数据的正确性。
*   **简洁性**: 设计非常轻量级，只包含必要的查询字段，避免了不必要的复杂性。

## 3. 属性说明

- **`date` (LocalDate)**: 查询仪表盘统计数据的目标日期。如果通过无参构造函数创建，默认为当前日期。

## 4. 业务规则

*   **日期范围**: 尽管当前DTO只包含单个日期，但在实际业务中，仪表盘统计可能需要支持日期范围查询。如果需要，可以在此DTO中扩展 `startDate` 和 `endDate` 字段。
*   **数据粒度**: 仪表盘统计通常是按天聚合的，因此 `date` 字段足以满足需求。如果需要更细粒度（如小时）或更粗粒度（如月度）的统计，则需要调整或扩展此DTO。

## 5. 使用示例

```java
// 1. 在 Controller 中构建 DashboardStatsQuery
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/dashboard-stats")
    public ApiResponse<DashboardStatsResult> getDashboardStats(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        try {
            DashboardStatsQuery query;
            if (date != null) {
                query = new DashboardStatsQuery(date);
            } else {
                query = new DashboardStatsQuery(); // 默认查询今天
            }

            DashboardStatsResult result = statisticsQueryService.queryDashboardStats(query);

            if (result.isSuccess()) {
                return ApiResponse.success(result, "查询成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }

        } catch (Exception e) {
            return ApiResponse.error("查询Dashboard统计失败: " + e.getMessage());
        }
    }
}

// 2. 在 StatisticsQueryApplicationService 中使用 DashboardStatsQuery
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private DashboardStatisticsRepository dashboardStatisticsRepository;

    public DashboardStatsResult queryDashboardStats(DashboardStatsQuery query) {
        // 业务逻辑：根据query.getDate()从数据库或缓存中获取统计数据
        // 假设这里调用一个领域服务或仓储来获取数据
        DashboardStatistics stats = dashboardStatisticsRepository.findByDate(query.getDate());

        if (stats != null) {
            // 将领域实体转换为DashboardStatsResult DTO
            DashboardStatsResult result = new DashboardStatsResult();
            result.setTotalVisits(stats.getTotalVisits());
            result.setNewUsers(stats.getNewUsers());
            result.setActiveUsers(stats.getActiveUsers());
            result.setSuccess(true);
            result.setMessage("查询成功");
            return result;
        } else {
            return new DashboardStatsResult(false, "未找到该日期的统计数据");
        }
    }
}

// 3. 测试示例
@SpringBootTest
class DashboardStatsQueryTest {
    @Test
    void testDefaultConstructor() {
        DashboardStatsQuery query = new DashboardStatsQuery();
        assertThat(query.getDate()).isEqualTo(LocalDate.now());
    }

    @Test
    void testParameterizedConstructor() {
        LocalDate specificDate = LocalDate.of(2023, 1, 15);
        DashboardStatsQuery query = new DashboardStatsQuery(specificDate);
        assertThat(query.getDate()).isEqualTo(specificDate);
    }

    @Test
    void testSetter() {
        DashboardStatsQuery query = new DashboardStatsQuery();
        LocalDate newDate = LocalDate.of(2024, 5, 10);
        query.setDate(newDate);
        assertThat(query.getDate()).isEqualTo(newDate);
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `DashboardStatsQuery` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 尽管当前DTO有Setter方法，但对于查询参数DTO，如果可能，最好设计为不可变对象（所有字段 `final`，只提供全参构造函数），以增强线程安全性和数据一致性。
*   **日期处理**: 使用 `java.time.LocalDate` 是处理日期的最佳实践，避免了 `java.util.Date` 的时区问题。
*   **参数校验**: 虽然DTO本身没有JSR-303注解，但在Controller或Application Service层接收到此DTO后，仍需对 `date` 字段进行业务校验，例如确保日期在有效范围内。
*   **可扩展性**: 如果未来仪表盘需要更多查询条件（如按用户角色、地域等），可以在此DTO中添加相应属性。