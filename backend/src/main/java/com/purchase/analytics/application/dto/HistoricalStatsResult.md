# HistoricalStatsResult.md

## 1. 文件概述

`HistoricalStatsResult` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.dto` 包中。它用于封装历史统计查询的返回结果。这个DTO设计旨在提供一个全面且易于前端展示的聚合数据视图，包含了按时间粒度划分的访问统计数据列表 (`statistics`) 和整个查询时间段的汇总统计信息 (`summary`)。通过静态工厂方法 `success` 和 `failure` 提供了便捷的创建方式，确保了返回结果的清晰性和一致性。

## 2. 核心功能

*   **数据聚合**: 聚合了按时间粒度划分的访问统计数据（`VisitStatistics` 列表）和整体的汇总统计信息（`StatisticsSummary`）。
*   **状态指示**: 包含 `success` 字段和 `message` 字段，明确指示查询操作是否成功以及相关的提示信息。
*   **工厂方法**: 通过 `success()` 和 `failure()` 静态方法，简化了DTO的创建过程，并确保了返回结果的规范性。
*   **嵌套DTO**: 内部定义了 `StatisticsSummary` 静态类，用于表示整个查询时间段的汇总统计数据，使得数据结构更加清晰。

## 3. 属性说明

- **`success` (boolean)**: 表示查询操作是否成功。
- **`message` (String)**: 查询结果的描述信息，成功时通常为“查询成功”，失败时为错误信息。
- **`statistics` (List<VisitStatistics>)**: 按时间粒度划分的访问统计数据列表。每个 `VisitStatistics` 对象通常包含日期、访问量、独立访客数等。
- **`summary` (StatisticsSummary)**: 整个查询时间段的汇总统计信息，包含：
    - **`totalVisits` (int)**: 总访问量。
    - **`totalUniqueVisitors` (int)**: 总独立访客数。
    - **`avgDailyVisits` (int)**: 平均每日访问量。
    - **`peakDay` (String)**: 访问量最高的日期。

## 4. 业务规则

*   **数据来源**: `HistoricalStatsResult` 中的数据通常由 `StatisticsQueryApplicationService` 协调领域服务或数据仓储计算和聚合而来。
*   **数据时效性**: 历史统计数据通常基于预计算或定时任务生成，其时效性取决于数据更新频率。
*   **粒度一致性**: `statistics` 列表中的数据粒度（例如，是每天、每周、每月）应与查询请求中的 `granularity` 参数一致。
*   **汇总计算**: `StatisticsSummary` 中的数据应是 `statistics` 列表中数据的正确聚合结果。

## 5. 使用示例

```java
// 1. 在 StatisticsQueryApplicationService 中构建 HistoricalStatsResult
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private HistoricalStatisticsRepository historicalStatisticsRepository;

    public HistoricalStatsResult queryHistoricalStats(HistoricalStatsQuery query) {
        try {
            // 业务逻辑：根据query的日期范围和粒度从数据库或缓存中获取历史统计数据
            List<VisitStatistics> statsList = historicalStatisticsRepository.findHistoricalData(
                query.getStartDate(), query.getEndDate(), query.getGranularity()
            );

            // 计算汇总数据
            int totalVisits = statsList.stream().mapToInt(VisitStatistics::getVisits).sum();
            int totalUniqueVisitors = statsList.stream().mapToInt(VisitStatistics::getUniqueVisitors).sum();
            int avgDailyVisits = statsList.isEmpty() ? 0 : totalVisits / statsList.size();
            String peakDay = statsList.stream()
                .max(Comparator.comparingInt(VisitStatistics::getVisits))
                .map(s -> s.getDate().toString())
                .orElse("N/A");

            HistoricalStatsResult.StatisticsSummary summary = 
                new HistoricalStatsResult.StatisticsSummary(totalVisits, totalUniqueVisitors, avgDailyVisits, peakDay);

            return HistoricalStatsResult.success(statsList, summary);
        } catch (Exception e) {
            return HistoricalStatsResult.failure("查询历史统计失败: " + e.getMessage());
        }
    }
}

// 2. 在 Controller 中返回 HistoricalStatsResult
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/historical-stats")
    public ApiResponse<HistoricalStatsResult> getHistoricalStats(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "daily") String granularity) {
        
        HistoricalStatsQuery query = new HistoricalStatsQuery();
        query.setStartDate(LocalDate.parse(startDate));
        query.setEndDate(LocalDate.parse(endDate));
        query.setGranularity(granularity);

        HistoricalStatsResult result = statisticsQueryService.queryHistoricalStats(query);
        
        if (result.isSuccess()) {
            return ApiResponse.success(result, result.getMessage());
        } else {
            return ApiResponse.error(result.getMessage());
        }
    }
}

// 3. 前端 (React + ECharts) 接收并展示历史统计数据
/*
import React, { useEffect, useState } from 'react';
import axios from 'axios';
import ReactECharts from 'echarts-for-react';

function HistoricalStatsChart({ startDate, endDate, granularity }) {
  const [chartOption, setChartOption] = useState({});
  const [summary, setSummary] = useState(null);

  useEffect(() => {
    axios.get('/api/v1/analytics/historical-stats', {
      params: { startDate, endDate, granularity }
    })
      .then(response => {
        if (response.data.success) {
          const data = response.data.data.statistics;
          const dates = data.map(item => item.date);
          const visits = data.map(item => item.visits);
          const uniqueVisitors = data.map(item => item.uniqueVisitors);

          setChartOption({
            xAxis: { type: 'category', data: dates },
            yAxis: { type: 'value' },
            series: [
              { name: '访问量', type: 'line', data: visits },
              { name: '独立访客', type: 'line', data: uniqueVisitors }
            ]
            // ... 其他图表配置
          });
          setSummary(response.data.data.summary);
        } else {
          console.error('获取历史统计失败:', response.data.message);
        }
      })
      .catch(error => {
        console.error('请求失败:', error);
      });
  }, [startDate, endDate, granularity]);

  return (
    <div>
      {summary && (
        <div>
          <p>总访问量: {summary.totalVisits}</p>
          <p>总独立访客: {summary.totalUniqueVisitors}</p>
          <p>平均每日访问: {summary.avgDailyVisits}</p>
          <p>高峰日期: {summary.peakDay}</p>
        </div>
      )}
      <ReactECharts option={chartOption} style={{ height: '400px', width: '100%' }} />
    </div>
  );
}
*/

// 4. 测试示例
@SpringBootTest
class HistoricalStatsResultTest {
    @Test
    void testSuccessFactoryMethod() {
        VisitStatistics stats1 = new VisitStatistics(LocalDate.of(2024, 1, 1), 100, 50);
        VisitStatistics stats2 = new VisitStatistics(LocalDate.of(2024, 1, 2), 120, 60);
        List<VisitStatistics> statistics = List.of(stats1, stats2);
        HistoricalStatsResult.StatisticsSummary summary = 
            new HistoricalStatsResult.StatisticsSummary(220, 110, 110, "2024-01-02");

        HistoricalStatsResult result = HistoricalStatsResult.success(statistics, summary);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("查询成功");
        assertThat(result.getStatistics()).hasSize(2);
        assertThat(result.getSummary().getTotalVisits()).isEqualTo(220);
    }

    @Test
    void testFailureFactoryMethod() {
        HistoricalStatsResult result = HistoricalStatsResult.failure("数据处理异常");

        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).isEqualTo("数据处理异常");
        assertThat(result.getStatistics()).isNull();
        assertThat(result.getSummary()).isNull();
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `HistoricalStatsResult` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 尽管当前DTO有Setter方法，但对于结果DTO，如果可能，最好设计为不可变对象（所有字段 `final`，只提供全参构造函数），以增强线程安全性和数据一致性。
*   **数据类型**: 确保数值类型（`int`, `long`）能够准确表示统计数据。
*   **数据来源**: `VisitStatistics` 是一个领域模型，它应该由领域服务或仓储层提供。`HistoricalStatsResult` 负责将其封装并提供给上层。
*   **可扩展性**: 如果未来历史统计需要展示更多维度的数据，可以在 `VisitStatistics` 或 `StatisticsSummary` 中添加相应属性。
*   **前端适配**: DTO的结构应尽可能与前端图表组件（如ECharts）的期望数据格式匹配，减少前端的数据转换工作。
*   **性能**: 历史统计数据通常是聚合计算的结果，其生成可能涉及复杂的查询。在服务层构建此DTO时，应特别关注性能优化，例如使用缓存、预计算等。