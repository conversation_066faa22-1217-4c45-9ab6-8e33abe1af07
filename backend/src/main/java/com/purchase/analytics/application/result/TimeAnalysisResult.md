# TimeAnalysisResult.md

## 1. 文件概述

`TimeAnalysisResult` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.result` 包中。它用于封装时段分析查询的返回结果。这个DTO设计旨在提供一个清晰、结构化的用户访问时间分布视图，包含了按小时 (`hourlyData`) 和按周几 (`weeklyData`) 划分的访问统计数据，以及访问高峰时段 (`peakHours`)。它是一个不可变对象，确保了数据的一致性和线程安全。

## 2. 核心功能

*   **小时级统计**: `hourlyData` 列表包含了每天24小时的访问量和独立访客数，用于分析一天内的访问高峰和低谷。
*   **周级统计**: `weeklyData` 列表包含了每周七天的访问量和独立访客数，用于分析一周内的访问模式。
*   **高峰时段**: `peakHours` 列表直接指明了访问量最高的几个小时，便于快速识别关键时间点。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。
*   **值语义**: 重写了 `equals` 和 `hashCode` 方法，确保了当所有属性值相等时，两个 `TimeAnalysisResult` 对象被认为是相等的。
*   **嵌套DTO**: 内部定义了 `HourlyData` 和 `WeeklyData` 两个静态类，用于表示不同时间粒度下的统计数据，使得数据结构更加清晰。

## 3. 属性说明

- **`hourlyData` (List<HourlyData>)**: 按小时划分的访问统计数据列表。每个 `HourlyData` 包含：
    - **`hour` (int)**: 小时（0-23）。
    - **`visits` (int)**: 该小时的访问量。
    - **`uniqueVisitors` (int)**: 该小时的独立访客数。
- **`weeklyData` (List<WeeklyData>)**: 按周几划分的访问统计数据列表。每个 `WeeklyData` 包含：
    - **`dayOfWeek` (int)**: 周几（1=Monday, 7=Sunday）。
    - **`dayName` (String)**: 周几的名称（例如：`周一`, `Monday`）。
    - **`visits` (int)**: 该天的访问量。
    - **`uniqueVisitors` (int)**: 该天的独立访客数。
- **`peakHours` (List<Integer>)**: 访问量最高的几个小时（例如：`[10, 14]`）。

## 4. 业务规则

*   **数据来源**: `TimeAnalysisResult` 中的数据通常由 `StatisticsQueryApplicationService` 协调领域服务或数据仓储计算和聚合而来。
*   **数据时效性**: 时段分析数据可能基于预计算或定时任务生成，其时效性取决于数据更新频率。
*   **时间粒度**: `hourlyData` 和 `weeklyData` 中的数据粒度应与前端展示需求一致。
*   **高峰计算**: `peakHours` 的计算逻辑需要明确定义，例如是取访问量最高的N个时段。

## 5. 使用示例

```java
// 1. 在 StatisticsQueryApplicationService 中构建 TimeAnalysisResult
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private TimeAnalysisRepository timeAnalysisRepository;

    public TimeAnalysisResult queryTimeAnalysis(TimeAnalysisQuery query) {
        // 业务逻辑：根据query的日期范围从数据库或缓存中获取时段分析数据
        List<HourlyStats> rawHourlyData = timeAnalysisRepository.findHourlyStats(
            query.getStartDate(), query.getEndDate()
        );
        List<WeeklyStats> rawWeeklyData = timeAnalysisRepository.findWeeklyStats(
            query.getStartDate(), query.getEndDate()
        );

        // 将原始数据转换为 TimeAnalysisResult.HourlyData 列表
        List<TimeAnalysisResult.HourlyData> hourlyData = rawHourlyData.stream()
            .map(d -> new TimeAnalysisResult.HourlyData(d.getHour(), d.getVisits(), d.getUniqueVisitors()))
            .collect(Collectors.toList());

        // 将原始数据转换为 TimeAnalysisResult.WeeklyData 列表
        List<TimeAnalysisResult.WeeklyData> weeklyData = rawWeeklyData.stream()
            .map(d -> new TimeAnalysisResult.WeeklyData(d.getDayOfWeek(), d.getDayName(), d.getVisits(), d.getUniqueVisitors()))
            .collect(Collectors.toList());

        // 计算高峰时段
        List<Integer> peakHours = hourlyData.stream()
            .sorted(Comparator.comparingInt(TimeAnalysisResult.HourlyData::getVisits).reversed())
            .limit(3) // 取访问量最高的3个时段
            .map(TimeAnalysisResult.HourlyData::getHour)
            .collect(Collectors.toList());

        return new TimeAnalysisResult(hourlyData, weeklyData, peakHours);
    }
}

// 2. 在 Controller 中返回 TimeAnalysisResult
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/time-analysis")
    public ApiResponse<TimeAnalysisResult> getTimeAnalysis(
            @RequestParam String startDate,
            @RequestParam String endDate) {

        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            TimeAnalysisQuery query = new TimeAnalysisQuery(start, end);

            TimeAnalysisResult result = statisticsQueryService.queryTimeAnalysis(query);

            return ApiResponse.success(result);

        } catch (DateTimeParseException e) {
            return ApiResponse.error("日期格式错误: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("查询时段分析失败: " + e.getMessage());
        }
    }
}

// 3. 前端 (React + ECharts) 接收并展示时段分析数据
/*
import React, { useEffect, useState } from 'react';
import axios from 'axios';
import ReactECharts from 'echarts-for-react';

function TimeAnalysisChart({ startDate, endDate }) {
  const [timeData, setTimeData] = useState(null);

  useEffect(() => {
    axios.get('/api/v1/analytics/time-analysis', {
      params: { startDate, endDate }
    })
      .then(response => {
        if (response.data.success) {
          setTimeData(response.data.data);
        } else {
          console.error('获取时段分析失败:', response.data.message);
        }
      })
      .catch(error => {
        console.error('请求失败:', error);
      });
  }, [startDate, endDate]);

  if (!timeData) {
    return <div>加载中...</div>;
  }

  const hourlyOption = {
    xAxis: { type: 'category', data: timeData.hourlyData.map(d => d.hour + '点') },
    yAxis: { type: 'value' },
    series: [
      { name: '访问量', type: 'bar', data: timeData.hourlyData.map(d => d.visits) }
    ]
  };

  const weeklyOption = {
    xAxis: { type: 'category', data: timeData.weeklyData.map(d => d.dayName) },
    yAxis: { type: 'value' },
    series: [
      { name: '访问量', type: 'bar', data: timeData.weeklyData.map(d => d.visits) }
    ]
  };

  return (
    <div>
      <h2>小时访问分布</h2>
      <ReactECharts option={hourlyOption} style={{ height: '300px' }} />
      <h2>周访问分布</h2>
      <ReactECharts option={weeklyOption} style={{ height: '300px' }} />
      <p>高峰时段: {timeData.peakHours.join('点, ')}点</p>
    </div>
  );
}
*/

// 4. 测试示例
@SpringBootTest
class TimeAnalysisResultTest {
    @Test
    void testConstructorAndGetters() {
        List<TimeAnalysisResult.HourlyData> hourly = List.of(
            new TimeAnalysisResult.HourlyData(9, 100, 50),
            new TimeAnalysisResult.HourlyData(10, 120, 60)
        );
        List<TimeAnalysisResult.WeeklyData> weekly = List.of(
            new TimeAnalysisResult.WeeklyData(1, "周一", 500, 200),
            new TimeAnalysisResult.WeeklyData(2, "周二", 600, 250)
        );
        List<Integer> peakHours = List.of(10, 14);

        TimeAnalysisResult result = new TimeAnalysisResult(hourly, weekly, peakHours);

        assertThat(result.getHourlyData()).hasSize(2);
        assertThat(result.getWeeklyData()).hasSize(2);
        assertThat(result.getPeakHours()).isEqualTo(peakHours);
    }

    @Test
    void testEqualsAndHashCode() {
        List<TimeAnalysisResult.HourlyData> hourly1 = List.of(new TimeAnalysisResult.HourlyData(9, 100, 50));
        List<TimeAnalysisResult.WeeklyData> weekly1 = List.of(new TimeAnalysisResult.WeeklyData(1, "周一", 500, 200));
        List<Integer> peakHours1 = List.of(9);
        TimeAnalysisResult result1 = new TimeAnalysisResult(hourly1, weekly1, peakHours1);

        List<TimeAnalysisResult.HourlyData> hourly2 = List.of(new TimeAnalysisResult.HourlyData(9, 100, 50));
        List<TimeAnalysisResult.WeeklyData> weekly2 = List.of(new TimeAnalysisResult.WeeklyData(1, "周一", 500, 200));
        List<Integer> peakHours2 = List.of(9);
        TimeAnalysisResult result2 = new TimeAnalysisResult(hourly2, weekly2, peakHours2);

        assertThat(result1).isEqualTo(result2);
        assertThat(result1.hashCode()).isEqualTo(result2.hashCode());
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `TimeAnalysisResult` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。这使得对象在多线程环境中是安全的。
*   **数据类型**: 确保数值类型（`int`）能够准确表示统计数据。
*   **嵌套DTO**: 内部的 `HourlyData` 和 `WeeklyData` 也是不可变的值对象，进一步保证了数据结构的清晰和一致性。
*   **前端适配**: DTO的结构应尽可能与前端图表组件（如ECharts）的期望数据格式匹配，减少前端的数据转换工作。
*   **性能**: 时段分析数据通常是聚合计算的结果，其生成可能涉及复杂的查询。在服务层构建此DTO时，应特别关注性能优化，例如使用缓存、预计算等。