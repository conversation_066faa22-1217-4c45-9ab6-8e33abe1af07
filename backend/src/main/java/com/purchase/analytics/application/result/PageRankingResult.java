package com.purchase.analytics.application.result;

import java.util.List;

/**
 * 页面排行结果
 */
public class PageRankingResult {
    private final List<PageInfo> pages;
    
    public PageRankingResult(List<PageInfo> pages) {
        this.pages = pages;
    }
    
    public List<PageInfo> getPages() {
        return pages;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        PageRankingResult that = (PageRankingResult) o;
        
        return pages.equals(that.pages);
    }
    
    @Override
    public int hashCode() {
        return pages.hashCode();
    }
    
    /**
     * 页面信息
     */
    public static class PageInfo {
        private final String url;
        private final String title;
        private final String category;
        private final int visits;
        private final int uniqueVisitors;
        private final int avgStayTime;
        private final double trend;
        private final double bounceRate;
        private final double conversionRate;
        
        public PageInfo(String url, String title, String category, int visits, 
                       int uniqueVisitors, int avgStayTime, double trend, 
                       double bounceRate, double conversionRate) {
            this.url = url;
            this.title = title;
            this.category = category;
            this.visits = visits;
            this.uniqueVisitors = uniqueVisitors;
            this.avgStayTime = avgStayTime;
            this.trend = trend;
            this.bounceRate = bounceRate;
            this.conversionRate = conversionRate;
        }
        
        public String getUrl() {
            return url;
        }
        
        public String getTitle() {
            return title;
        }
        
        public String getCategory() {
            return category;
        }
        
        public int getVisits() {
            return visits;
        }
        
        public int getUniqueVisitors() {
            return uniqueVisitors;
        }
        
        public int getAvgStayTime() {
            return avgStayTime;
        }
        
        public double getTrend() {
            return trend;
        }
        
        public double getBounceRate() {
            return bounceRate;
        }
        
        public double getConversionRate() {
            return conversionRate;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            
            PageInfo pageInfo = (PageInfo) o;
            
            if (visits != pageInfo.visits) return false;
            if (uniqueVisitors != pageInfo.uniqueVisitors) return false;
            if (avgStayTime != pageInfo.avgStayTime) return false;
            if (Double.compare(pageInfo.trend, trend) != 0) return false;
            if (Double.compare(pageInfo.bounceRate, bounceRate) != 0) return false;
            if (Double.compare(pageInfo.conversionRate, conversionRate) != 0) return false;
            if (!url.equals(pageInfo.url)) return false;
            if (!title.equals(pageInfo.title)) return false;
            return category.equals(pageInfo.category);
        }
        
        @Override
        public int hashCode() {
            int result;
            long temp;
            result = url.hashCode();
            result = 31 * result + title.hashCode();
            result = 31 * result + category.hashCode();
            result = 31 * result + visits;
            result = 31 * result + uniqueVisitors;
            result = 31 * result + avgStayTime;
            temp = Double.doubleToLongBits(trend);
            result = 31 * result + (int) (temp ^ (temp >>> 32));
            temp = Double.doubleToLongBits(bounceRate);
            result = 31 * result + (int) (temp ^ (temp >>> 32));
            temp = Double.doubleToLongBits(conversionRate);
            result = 31 * result + (int) (temp ^ (temp >>> 32));
            return result;
        }
    }
}
