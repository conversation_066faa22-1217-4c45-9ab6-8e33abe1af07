# DeviceAnalysisResult.md

## 1. 文件概述

`DeviceAnalysisResult` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.result` 包中。它用于封装设备分析查询的返回结果。这个DTO设计旨在提供一个全面且易于前端展示的设备使用情况视图，包含了按设备类型（桌面、移动、平板）的统计，以及按浏览器和操作系统划分的详细数据。它是一个不可变对象，确保了数据的一致性和线程安全。

## 2. 核心功能

*   **设备类型统计**: 提供了 `desktop`, `mobile`, `tablet` 三个字段，直接反映了不同设备类型的访问量。
*   **浏览器分布**: `browsers` 列表包含了按浏览器名称、版本、访问量和颜色（用于图表展示）划分的详细数据。
*   **操作系统分布**: `operatingSystems` 列表包含了按操作系统名称、版本、访问量和颜色划分的详细数据。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。
*   **值语义**: 重写了 `equals` 和 `hashCode` 方法，确保了当所有属性值相等时，两个 `DeviceAnalysisResult` 对象被认为是相等的。
*   **嵌套DTO**: 内部定义了 `BrowserInfo` 和 `OperatingSystemInfo` 两个静态类，用于表示浏览器和操作系统的详细统计数据，使得数据结构更加清晰。

## 3. 属性说明

- **`desktop` (int)**: 桌面设备访问量。
- **`mobile` (int)**: 移动设备访问量。
- **`tablet` (int)**: 平板设备访问量。
- **`browsers` (List<BrowserInfo>)**: 浏览器统计信息列表。每个 `BrowserInfo` 包含：
    - **`name` (String)**: 浏览器名称（例如：`Chrome`, `Firefox`）。
    - **`version` (String)**: 浏览器版本。
    - **`value` (int)**: 该浏览器的访问量。
    - **`color` (String)**: 用于前端图表展示的颜色代码。
- **`operatingSystems` (List<OperatingSystemInfo>)**: 操作系统统计信息列表。每个 `OperatingSystemInfo` 包含：
    - **`name` (String)**: 操作系统名称（例如：`Windows`, `macOS`, `Android`）。
    - **`version` (String)**: 操作系统版本。
    - **`value` (int)**: 该操作系统的访问量。
    - **`color` (String)**: 用于前端图表展示的颜色代码。

## 4. 业务规则

*   **数据来源**: `DeviceAnalysisResult` 中的数据通常由 `StatisticsQueryApplicationService` 协调领域服务或数据仓储计算和聚合而来。
*   **数据时效性**: 设备分析数据可能基于预计算或定时任务生成，其时效性取决于数据更新频率。
*   **分类标准**: 设备类型、浏览器和操作系统的分类标准应明确，并与数据采集和分析工具保持一致。

## 5. 使用示例

```java
// 1. 在 StatisticsQueryApplicationService 中构建 DeviceAnalysisResult
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private DeviceAnalysisRepository deviceAnalysisRepository;

    public DeviceAnalysisResult queryDeviceAnalysis(DeviceAnalysisQuery query) {
        // 业务逻辑：根据query的日期范围从数据库或缓存中获取设备分析数据
        // 假设这里调用一个领域服务或仓储来获取数据
        int desktopVisits = deviceAnalysisRepository.countVisitsByDeviceType(query.getStartDate(), query.getEndDate(), "desktop");
        int mobileVisits = deviceAnalysisRepository.countVisitsByDeviceType(query.getStartDate(), query.getEndDate(), "mobile");
        int tabletVisits = deviceAnalysisRepository.countVisitsByDeviceType(query.getStartDate(), query.getEndDate(), "tablet");

        List<DeviceAnalysisResult.BrowserInfo> browserStats = deviceAnalysisRepository.getBrowserStats(query.getStartDate(), query.getEndDate());
        List<DeviceAnalysisResult.OperatingSystemInfo> osStats = deviceAnalysisRepository.getOperatingSystemStats(query.getStartDate(), query.getEndDate());

        return new DeviceAnalysisResult(desktopVisits, mobileVisits, tabletVisits, browserStats, osStats);
    }
}

// 2. 在 Controller 中返回 DeviceAnalysisResult
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/device-analysis")
    public ApiResponse<DeviceAnalysisResult> getDeviceAnalysis(
            @RequestParam String startDate,
            @RequestParam String endDate) {

        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            DeviceAnalysisQuery query = new DeviceAnalysisQuery(start, end);

            DeviceAnalysisResult result = statisticsQueryService.queryDeviceAnalysis(query);

            return ApiResponse.success(result);

        } catch (DateTimeParseException e) {
            return ApiResponse.error("日期格式错误: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("查询设备分析失败: " + e.getMessage());
        }
    }
}

// 3. 前端 (React + ECharts) 接收并展示设备分析数据
/*
import React, { useEffect, useState } from 'react';
import axios from 'axios';
import ReactECharts from 'echarts-for-react';

function DeviceAnalysisChart({ startDate, endDate }) {
  const [deviceData, setDeviceData] = useState(null);

  useEffect(() => {
    axios.get('/api/v1/analytics/device-analysis', {
      params: { startDate, endDate }
    })
      .then(response => {
        if (response.data.success) {
          setDeviceData(response.data.data);
        } else {
          console.error('获取设备分析失败:', response.data.message);
        }
      })
      .catch(error => {
        console.error('请求失败:', error);
      });
  }, [startDate, endDate]);

  if (!deviceData) {
    return <div>加载中...</div>;
  }

  const deviceTypeOption = {
    series: [
      {
        type: 'pie',
        data: [
          { value: deviceData.desktop, name: '桌面' },
          { value: deviceData.mobile, name: '移动' },
          { value: deviceData.tablet, name: '平板' }
        ]
      }
    ]
  };

  const browserOption = {
    series: [
      {
        type: 'pie',
        data: deviceData.browsers.map(b => ({ value: b.value, name: b.name, itemStyle: { color: b.color } }))
      }
    ]
  };

  return (
    <div>
      <h2>设备类型分布</h2>
      <ReactECharts option={deviceTypeOption} style={{ height: '300px' }} />
      <h2>浏览器分布</h2>
      <ReactECharts option={browserOption} style={{ height: '300px' }} />
      {/* ... 更多图表 */}
    </div>
  );
}
*/

// 4. 测试示例
@SpringBootTest
class DeviceAnalysisResultTest {
    @Test
    void testConstructorAndGetters() {
        List<DeviceAnalysisResult.BrowserInfo> browsers = List.of(
            new DeviceAnalysisResult.BrowserInfo("Chrome", "100", 1000, "#FF0000"),
            new DeviceAnalysisResult.BrowserInfo("Firefox", "90", 500, "#00FF00")
        );
        List<DeviceAnalysisResult.OperatingSystemInfo> oss = List.of(
            new DeviceAnalysisResult.OperatingSystemInfo("Windows", "10", 1200, "#0000FF"),
            new DeviceAnalysisResult.OperatingSystemInfo("macOS", "12", 300, "#FFFF00")
        );

        DeviceAnalysisResult result = new DeviceAnalysisResult(800, 700, 100, browsers, oss);

        assertThat(result.getDesktop()).isEqualTo(800);
        assertThat(result.getMobile()).isEqualTo(700);
        assertThat(result.getTablet()).isEqualTo(100);
        assertThat(result.getBrowsers()).hasSize(2);
        assertThat(result.getOperatingSystems()).hasSize(2);
        assertThat(result.getBrowsers().get(0).getName()).isEqualTo("Chrome");
    }

    @Test
    void testEqualsAndHashCode() {
        List<DeviceAnalysisResult.BrowserInfo> browsers1 = List.of(new DeviceAnalysisResult.BrowserInfo("Chrome", "100", 1000, "#FF0000"));
        List<DeviceAnalysisResult.OperatingSystemInfo> oss1 = List.of(new DeviceAnalysisResult.OperatingSystemInfo("Windows", "10", 1200, "#0000FF"));
        DeviceAnalysisResult result1 = new DeviceAnalysisResult(800, 700, 100, browsers1, oss1);

        List<DeviceAnalysisResult.BrowserInfo> browsers2 = List.of(new DeviceAnalysisResult.BrowserInfo("Chrome", "100", 1000, "#FF0000"));
        List<DeviceAnalysisResult.OperatingSystemInfo> oss2 = List.of(new DeviceAnalysisResult.OperatingSystemInfo("Windows", "10", 1200, "#0000FF"));
        DeviceAnalysisResult result2 = new DeviceAnalysisResult(800, 700, 100, browsers2, oss2);

        assertThat(result1).isEqualTo(result2);
        assertThat(result1.hashCode()).isEqualTo(result2.hashCode());
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `DeviceAnalysisResult` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。这使得对象在多线程环境中是安全的。
*   **数据类型**: 确保数值类型（`int`）能够准确表示统计数据。`color` 字段用于前端图表展示，应遵循前端图表库的颜色格式要求。
*   **嵌套DTO**: 内部的 `BrowserInfo` 和 `OperatingSystemInfo` 也是不可变的值对象，进一步保证了数据结构的清晰和一致性。
*   **前端适配**: DTO的结构应尽可能与前端图表组件（如ECharts）的期望数据格式匹配，减少前端的数据转换工作。
*   **性能**: 设备分析数据通常是聚合计算的结果，其生成可能涉及复杂的查询。在服务层构建此DTO时，应特别关注性能优化，例如使用缓存、预计算等。