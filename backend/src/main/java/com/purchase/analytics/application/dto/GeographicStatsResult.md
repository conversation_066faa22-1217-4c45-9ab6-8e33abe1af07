# GeographicStatsResult.md

## 1. 文件概述

`GeographicStatsResult` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.dto` 包中。它用于封装地理统计查询的返回结果。这个DTO设计旨在提供一个清晰、结构化的地理数据视图，通常用于在地图上展示用户访问的地域分布。它包含一个 `GeographicData` 列表，每个元素代表一个地理区域及其对应的统计值。通过静态工厂方法 `success` 和 `failure` 提供了便捷的创建方式，确保了返回结果的清晰性和一致性。

## 2. 核心功能

*   **数据封装**: 封装了地理统计数据，主要是一个 `GeographicData` 列表，每个 `GeographicData` 包含地域名称和统计值。
*   **状态指示**: 包含 `success` 字段和 `message` 字段，明确指示查询操作是否成功以及相关的提示信息。
*   **时间戳**: `lastUpdated` 字段记录了数据生成的时间，有助于前端判断数据的时效性。
*   **工厂方法**: 通过 `success()` 和 `failure()` 静态方法，简化了DTO的创建过程，并确保了返回结果的规范性。
*   **嵌套DTO**: 内部定义了 `GeographicData` 静态类，用于表示单个地理区域的统计数据，使得数据结构更加清晰。

## 3. 属性说明

- **`success` (boolean)**: 表示查询操作是否成功。
- **`message` (String)**: 查询结果的描述信息，成功时通常为“查询成功”，失败时为错误信息。
- **`data` (List<GeographicData>)**: 地理统计数据列表。每个 `GeographicData` 对象包含：
    - **`name` (String)**: 地理区域的名称（例如：`北京`, `上海`, `广东` 或 `USA`, `China`）。
    - **`value` (long)**: 该地理区域对应的统计值（例如：访问量、用户数）。
- **`lastUpdated` (LocalDateTime)**: 数据最后更新的时间戳。

## 4. 业务规则

*   **数据来源**: `GeographicStatsResult` 中的数据通常由 `StatisticsQueryApplicationService` 协调领域服务或数据仓储计算和聚合而来。
*   **数据时效性**: 地理统计数据可能基于预计算或定时任务生成，其时效性取决于数据更新频率。
*   **地域粒度**: `GeographicData` 中的 `name` 字段的粒度（省份、城市、国家）应与前端地图组件的展示需求一致。

## 5. 使用示例

```java
// 1. 在 StatisticsQueryApplicationService 中构建 GeographicStatsResult
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private GeographicStatisticsRepository geographicStatisticsRepository;

    public GeographicStatsResult queryGeographicStats(GeographicStatsQuery query) {
        try {
            // 业务逻辑：根据query的日期范围和mapType从数据库或缓存中获取地理统计数据
            // 假设这里调用一个领域服务或仓储来获取数据
            List<GeographicData> rawData = geographicStatisticsRepository.findGeographicData(
                query.getStartDate(), query.getEndDate(), query.getMapType()
            );

            // 将原始数据转换为 GeographicStatsResult.GeographicData 列表
            List<GeographicStatsResult.GeographicData> formattedData = rawData.stream()
                .map(d -> new GeographicStatsResult.GeographicData(d.getRegionName(), d.getVisitCount()))
                .collect(Collectors.toList());

            return GeographicStatsResult.success(formattedData);
        } catch (Exception e) {
            return GeographicStatsResult.failure("查询地理统计失败: " + e.getMessage());
        }
    }
}

// 2. 在 Controller 中返回 GeographicStatsResult
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/geographic-stats")
    public ApiResponse<GeographicStatsResult> getGeographicStats(
            @RequestParam(defaultValue = "china") String mapType,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        GeographicStatsQuery query = new GeographicStatsQuery();
        query.setMapType(mapType);
        // ... 设置日期范围 ...

        GeographicStatsResult result = statisticsQueryService.queryGeographicStats(query);
        
        if (result.isSuccess()) {
            return ApiResponse.success(result, result.getMessage());
        } else {
            return ApiResponse.error(result.getMessage());
        }
    }
}

// 3. 前端 (React + ECharts) 接收并展示地理统计数据
/*
import React, { useEffect, useState } from 'react';
import axios from 'axios';
import ReactECharts from 'echarts-for-react';

function GeographicMap() {
  const [chartOption, setChartOption] = useState({});

  useEffect(() => {
    axios.get('/api/v1/analytics/geographic-stats', {
      params: { mapType: 'china', startDate: '2024-01-01', endDate: '2024-01-31' }
    })
      .then(response => {
        if (response.data.success) {
          const data = response.data.data.data; // 获取 GeographicData 列表
          setChartOption({
            series: [
              {
                type: 'map',
                map: 'china',
                data: data.map(item => ({ name: item.name, value: item.value }))
              }
            ]
            // ... 其他图表配置
          });
        } else {
          console.error('获取地理统计失败:', response.data.message);
        }
      })
      .catch(error => {
        console.error('请求失败:', error);
      });
  }, []);

  return (
    <ReactECharts option={chartOption} style={{ height: '500px', width: '100%' }} />
  );
}
*/

// 4. 测试示例
@SpringBootTest
class GeographicStatsResultTest {
    @Test
    void testSuccessFactoryMethod() {
        List<GeographicStatsResult.GeographicData> data = List.of(
            new GeographicStatsResult.GeographicData("北京", 100L),
            new GeographicStatsResult.GeographicData("上海", 150L)
        );
        GeographicStatsResult result = GeographicStatsResult.success(data);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("查询成功");
        assertThat(result.getData()).hasSize(2);
        assertThat(result.getData().get(0).getName()).isEqualTo("北京");
        assertThat(result.getData().get(0).getValue()).isEqualTo(100L);
        assertThat(result.getLastUpdated()).isNotNull();
    }

    @Test
    void testFailureFactoryMethod() {
        GeographicStatsResult result = GeographicStatsResult.failure("数据源不可用");

        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).isEqualTo("数据源不可用");
        assertThat(result.getData()).isNull(); // 失败时数据应为null
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `GeographicStatsResult` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 尽管当前DTO有Setter方法，但对于结果DTO，如果可能，最好设计为不可变对象（所有字段 `final`，只提供全参构造函数），以增强线程安全性和数据一致性。
*   **数据类型**: `value` 字段使用 `long` 类型，以支持较大的统计数值。
*   **时间戳**: `lastUpdated` 字段对于前端判断数据新鲜度非常重要，应在数据生成时准确设置。
*   **可扩展性**: 如果未来地理统计需要展示更多维度的数据（如不同用户角色在不同地域的分布），可以在 `GeographicData` 中添加相应属性。
*   **前端适配**: DTO的结构应尽可能与前端地图组件（如ECharts）的期望数据格式匹配，减少前端的数据转换工作。
*   **性能**: 地理统计数据通常是聚合计算的结果，其生成可能涉及复杂的查询。在服务层构建此DTO时，应特别关注性能优化，例如使用缓存、预计算等。