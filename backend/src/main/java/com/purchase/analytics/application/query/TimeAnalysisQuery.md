# TimeAnalysisQuery.md

## 1. 文件概述

`TimeAnalysisQuery` 是分析模块中的一个查询对象（Query Object），位于 `com.purchase.analytics.application.query` 包中。它用于封装查询时段分析数据时的请求参数。这个对象设计简洁，主要包含一个日期范围（开始日期和结束日期），旨在为前端提供灵活的用户访问时间分布分析能力。它的核心作用是作为应用服务层接收前端或上层服务请求的标准化输入，确保查询参数的类型安全和结构清晰。

## 2. 核心功能

*   **参数封装**: 封装了查询时段分析数据所需的日期范围参数。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改，增强了线程安全性和数据一致性。
*   **类型安全**: 使用 `LocalDate` 类型确保日期数据的正确性。
*   **值语义**: 重写了 `equals` 和 `hashCode` 方法，确保了当所有属性值相等时，两个 `TimeAnalysisQuery` 对象被认为是相等的，符合查询对象的特性。
*   **简洁性**: 设计轻量级，只包含必要的查询字段，避免了不必要的复杂性。

## 3. 属性说明

- **`startDate` (LocalDate)**: 查询时段分析数据的开始日期。
- **`endDate` (LocalDate)**: 查询时段分析数据的结束日期。

## 4. 业务规则

*   **日期范围有效性**: `startDate` 必须早于或等于 `endDate`。在Controller或Application Service层接收到此Query Object后，应进行此项校验。
*   **数据粒度**: 时段分析通常是按小时或按天聚合的，日期范围用于限定统计的时间窗口。

## 5. 使用示例

```java
// 1. 在 Controller 中构建 TimeAnalysisQuery
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/time-analysis")
    public ApiResponse<TimeAnalysisResult> getTimeAnalysis(
            @RequestParam String startDate,
            @RequestParam String endDate) {

        try {
            // 解析日期参数
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            // 创建查询对象
            TimeAnalysisQuery query = new TimeAnalysisQuery(start, end);

            // 执行查询
            TimeAnalysisResult result = statisticsQueryService.queryTimeAnalysis(query);

            return ApiResponse.success(result);

        } catch (DateTimeParseException e) {
            return ApiResponse.error("日期格式错误: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("查询时段分析失败: " + e.getMessage());
        }
    }
}

// 2. 在 StatisticsQueryApplicationService 中使用 TimeAnalysisQuery
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private TimeAnalysisRepository timeAnalysisRepository;

    public TimeAnalysisResult queryTimeAnalysis(TimeAnalysisQuery query) {
        // 业务逻辑：根据query的日期范围从数据库或缓存中获取时段分析数据
        // 假设这里调用一个领域服务或仓储来获取数据
        List<TimeStats> rawData = timeAnalysisRepository.findTimeStats(
            query.getStartDate(), query.getEndDate()
        );

        // 将原始数据转换为 TimeAnalysisResult DTO
        TimeAnalysisResult result = new TimeAnalysisResult();
        result.setSuccess(true);
        result.setMessage("查询成功");
        result.setData(rawData.stream().map(d -> new TimeAnalysisResult.TimeStatsEntry(d.getHourOfDay(), d.getVisits())).collect(Collectors.toList()));
        return result;
    }
}

// 3. 测试示例
@SpringBootTest
class TimeAnalysisQueryTest {
    @Test
    void testConstructorAndGetters() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 31);
        TimeAnalysisQuery query = new TimeAnalysisQuery(startDate, endDate);

        assertThat(query.getStartDate()).isEqualTo(startDate);
        assertThat(query.getEndDate()).isEqualTo(endDate);
    }

    @Test
    void testEqualsAndHashCode() {
        LocalDate startDate1 = LocalDate.of(2023, 1, 1);
        LocalDate endDate1 = LocalDate.of(2023, 1, 31);
        TimeAnalysisQuery query1 = new TimeAnalysisQuery(startDate1, endDate1);

        LocalDate startDate2 = LocalDate.of(2023, 1, 1);
        LocalDate endDate2 = LocalDate.of(2023, 1, 31);
        TimeAnalysisQuery query2 = new TimeAnalysisQuery(startDate2, endDate2);

        LocalDate startDate3 = LocalDate.of(2023, 2, 1);
        LocalDate endDate3 = LocalDate.of(2023, 2, 28);
        TimeAnalysisQuery query3 = new TimeAnalysisQuery(startDate3, endDate3);

        assertThat(query1).isEqualTo(query2);
        assertThat(query1.hashCode()).isEqualTo(query2.hashCode());
        assertThat(query1).isNotEqualTo(query3);
    }
}
```

## 6. 注意事项

*   **查询对象特性**: `TimeAnalysisQuery` 是一个典型的查询对象。它没有唯一标识，其相等性基于所有属性的值。它应该是不可变的，并且在创建时进行所有必要的验证。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。这使得对象在多线程环境中是安全的。
*   **构造函数验证**: 尽管当前DTO没有显式验证，但在Controller或Application Service层接收到此Query Object后，仍需对 `startDate` 和 `endDate` 的逻辑关系进行业务校验。
*   **日期处理**: 使用 `java.time.LocalDate` 是处理日期的最佳实践，避免了 `java.util.Date` 的时区问题。
*   **可扩展性**: 如果未来时段分析需要更多查询条件（如按用户角色、设备类型等），可以在此Query Object中添加相应属性。