# VisitTrackingApplicationService.md

## 1. 文件概述

`VisitTrackingApplicationService.java` 是分析模块中的一个应用服务接口，位于 `com.purchase.analytics.application` 包中。该服务专注于用户访问行为的跟踪和记录。它定义了记录用户访问事件的核心功能，例如页面浏览、特定资源访问等。作为应用服务，它协调领域模型和基础设施层，确保用户访问数据的准确捕获和持久化，为后续的数据分析和用户行为洞察提供基础数据支持。

## 2. 核心功能

*   **访问事件记录**: 提供 `recordVisit` 方法，用于捕获和记录用户的每一次访问行为，包括访问的用户、访问的资源类型和ID等关键信息。
*   **数据持久化**: 确保捕获到的访问事件数据能够被可靠地存储，为后续的分析提供数据源。
*   **业务逻辑封装**: 作为应用服务，它封装了记录访问事件的业务逻辑，例如可能包含对用户身份的解析、对访问资源合法性的初步校验等。
*   **解耦**: 将访问跟踪的逻辑从其他业务流程中分离出来，使得核心业务服务更加专注于其主要职责，提高了系统的模块化程度。

## 3. 接口说明

### 访问跟踪接口

#### recordVisit - 记录用户访问事件
*   **方法签名**: `void recordVisit(Long userId, String userRole, String entityType, Long entityId, String visitType)`
*   **参数**:
    *   `userId` (Long): 访问用户的ID。
    *   `userRole` (String): 访问用户的角色（例如：`BUYER`, `SELLER`, `ADMIN`）。
    *   `entityType` (String): 被访问的实体类型（例如：`ORDER`, `PRODUCT`, `REQUIREMENT`）。
    *   `entityId` (Long): 被访问实体的ID。
    *   `visitType` (String): 访问类型（例如：`VIEW`, `CLICK`, `DOWNLOAD`）。
*   **返回值**: `void`
*   **业务逻辑**: 服务的实现会根据传入的参数构建一个访问记录实体（如 `VisitLog`），并将其持久化到数据库。在记录之前，可能会进行一些基本的参数校验，例如 `userId` 和 `entityId` 是否为空。

## 4. 业务规则

*   **数据完整性**: 记录访问事件时，必须确保所有关键字段（如 `userId`, `entityType`, `entityId`, `visitType`）的完整性，以便后续进行准确的分析。
*   **异步处理**: 考虑到访问跟踪通常是高并发操作，且不应阻塞主业务流程，`recordVisit` 的实现应考虑异步化处理（例如，使用消息队列将访问事件发送到独立的消费者进行处理），以提高系统吞吐量。
*   **数据脱敏**: 如果访问事件中包含敏感信息，应在记录前进行适当的脱敏处理，以符合数据隐私法规。
*   **事件类型标准化**: `entityType` 和 `visitType` 应该使用预定义的枚举或常量，以确保数据的一致性和可分析性。

## 5. 使用示例

```java
// 1. 服务实现类示例
@Service
public class VisitTrackingApplicationServiceImpl implements VisitTrackingApplicationService {
    @Autowired
    private VisitLogRepository visitLogRepository;

    @Override
    @Async // 标记为异步方法，避免阻塞主业务流程
    public void recordVisit(Long userId, String userRole, String entityType, Long entityId, String visitType) {
        // 参数校验
        if (userId == null || entityType == null || entityId == null || visitType == null) {
            log.warn("无效的访问记录参数: userId={}, entityType={}, entityId={}, visitType={}", userId, entityType, entityId, visitType);
            return;
        }

        VisitLog visitLog = new VisitLog();
        visitLog.setUserId(userId);
        visitLog.setUserRole(userRole);
        visitLog.setEntityType(entityType);
        visitLog.setEntityId(entityId);
        visitLog.setVisitType(visitType);
        visitLog.setVisitTime(LocalDateTime.now());
        // 可以在这里添加更多上下文信息，如IP地址、User-Agent等

        visitLogRepository.save(visitLog);
        log.debug("记录访问事件: userId={}, entityType={}, entityId={}, visitType={}", userId, entityType, entityId, visitType);
    }
}

// 2. 在Controller中调用，记录页面访问
@RestController
@RequestMapping("/api/v1/products")
public class ProductController {
    @Autowired
    private ProductService productService;
    @Autowired
    private VisitTrackingApplicationService visitTrackingService;

    @GetMapping("/{productId}")
    public Result<ProductDTO> getProductDetail(@PathVariable Long productId) {
        ProductDTO product = productService.getProductById(productId);
        if (product != null) {
            // 假设从安全上下文中获取当前用户ID和角色
            Long currentUserId = getCurrentUserId(); 
            String currentUserRole = getCurrentUserRole();
            visitTrackingService.recordVisit(currentUserId, currentUserRole, "PRODUCT", productId, "VIEW");
        }
        return Result.success(product);
    }
}

// 3. 在业务服务中调用，记录特定操作
@Service
public class OrderService {
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private VisitTrackingApplicationService visitTrackingService;

    @Transactional
    public Order createOrder(OrderRequest request) {
        Order newOrder = orderRepository.save(request.toEntity());
        // 假设从安全上下文中获取当前用户ID和角色
        Long currentUserId = getCurrentUserId();
        String currentUserRole = getCurrentUserRole();
        visitTrackingService.recordVisit(currentUserId, currentUserRole, "ORDER", newOrder.getId(), "CREATE");
        return newOrder;
    }
}

// 4. 测试示例
@SpringBootTest
class VisitTrackingApplicationServiceTest {
    @Autowired
    private VisitTrackingApplicationService visitTrackingService;
    @MockBean
    private VisitLogRepository visitLogRepository;

    @Test
    void testRecordVisit() {
        Long userId = 1L;
        String userRole = "BUYER";
        String entityType = "PRODUCT";
        Long entityId = 100L;
        String visitType = "VIEW";

        visitTrackingService.recordVisit(userId, userRole, entityType, entityId, visitType);

        // 验证 save 方法是否被调用，并捕获传入的 VisitLog 对象
        ArgumentCaptor<VisitLog> captor = ArgumentCaptor.forClass(VisitLog.class);
        verify(visitLogRepository, times(1)).save(captor.capture());

        VisitLog capturedLog = captor.getValue();
        assertThat(capturedLog.getUserId()).isEqualTo(userId);
        assertThat(capturedLog.getEntityType()).isEqualTo(entityType);
        assertThat(capturedLog.getEntityId()).isEqualTo(entityId);
        assertThat(capturedLog.getVisitType()).isEqualTo(visitType);
        assertThat(capturedLog.getVisitTime()).isNotNull();
    }
}
```

## 6. 注意事项

*   **异步处理**: 强烈建议将 `recordVisit` 方法的实现标记为 `@Async`，并配置相应的线程池，以确保访问记录操作不会影响主业务流程的响应时间。同时，需要考虑异步操作的异常处理机制。
*   **数据量**: 访问跟踪会产生大量数据。需要考虑数据库的存储能力、索引优化以及数据归档策略。可以考虑使用专门的日志分析系统（如ELK Stack）或大数据平台来处理和存储这些数据。
*   **性能优化**: 如果直接写入关系型数据库，需要确保 `VisitLog` 表的写入性能。可以考虑批量插入、分区表等优化手段。
*   **数据分析**: 收集到的访问数据是进行用户行为分析、产品优化、精准营销的基础。服务设计时应考虑如何方便地与数据分析工具集成。
*   **用户隐私**: 在记录用户访问行为时，必须遵守相关的隐私法规（如GDPR、CCPA）。确保只收集必要的数据，并对敏感信息进行匿名化或脱敏处理。
*   **错误处理**: 即使是异步操作，也需要有适当的错误处理机制，例如记录失败的访问事件到死信队列，以便后续重试或人工干预。
*   **可扩展性**: 随着业务发展，可能需要跟踪更多维度的信息（如设备信息、地理位置、会话ID等）。`VisitLog` 实体和 `recordVisit` 方法应具备良好的可扩展性。
*   **日志与埋点**: 访问跟踪通常与前端的“埋点”相结合。前端负责收集用户行为数据并发送到后端，后端服务负责接收和记录。
*   **幂等性**: 记录访问事件通常不需要严格的幂等性，因为重复记录一次访问通常不会造成严重问题，但如果需要，可以通过生成唯一的事件ID来保证。