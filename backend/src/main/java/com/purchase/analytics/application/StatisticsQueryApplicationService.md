# StatisticsQueryApplicationService.md

## 1. 文件概述

`StatisticsQueryApplicationService.java` 是分析模块中的一个应用服务，位于 `com.purchase.analytics.application` 包中。它负责处理所有与统计数据查询相关的应用层逻辑。该服务不直接执行复杂的业务规则，而是作为协调者，调用底层的仓储（Repository）来获取原始数据，并对数据进行必要的转换和聚合，最终封装成各种DTO（Data Transfer Object）返回给上层（如Controller）。它是分析仪表盘和报表功能的数据提供核心，确保了数据查询的灵活性、准确性和高效性。

## 2. 核心功能

*   **实时统计查询**: 提供 `queryRealTimeStats` 方法，用于查询当前系统的实时访问统计数据，包括今日访问量、当前小时访问量、独立访客数等。
*   **历史统计查询**: 提供 `queryHistoricalStats` 方法，支持按日期范围和粒度（如日、周、月）查询历史访问统计数据，并计算汇总信息。
*   **来源分析**: 提供 `querySourceAnalysis` 方法，用于分析用户访问的来源渠道，如搜索引擎、直接访问、引荐网站和社交媒体。
*   **设备分析**: 提供 `queryDeviceAnalysis` 方法，用于分析用户访问所使用的设备类型，包括桌面、移动、平板以及浏览器和操作系统分布。
*   **页面排行**: 提供 `queryPageRanking` 方法，用于查询访问量最高的页面排名，并包含访问量、独立访客、平均停留时间、跳出率和转化率等指标。
*   **时段分析**: 提供 `queryTimeAnalysis` 方法，用于分析用户在不同时间段的访问行为模式，包括小时和周的访问分布，以及高峰时段。
*   **Dashboard统计**: 提供 `queryDashboardStats` 方法，用于获取聚合的仪表盘统计数据，如实时在线、页面浏览量、平均停留时间、增长率等。
*   **地理统计**: 提供 `queryGeographicStats` 方法，用于分析用户访问的地理分布。
*   **实时趋势**: 提供 `queryRealTimeTrend` 方法，用于查询实时访问趋势数据，支持不同时间粒度。
*   **参数校验**: 包含多个私有方法，用于对各种查询请求的参数进行严格的校验，确保数据的合法性。
*   **数据转换与聚合**: 负责将从仓储获取的原始数据转换为前端所需的DTO格式，并进行必要的聚合计算。

## 3. 接口说明

### 3.1 统计查询方法

#### queryRealTimeStats - 查询实时统计
*   **方法签名**: `RealTimeStatsResult queryRealTimeStats(RealTimeStatsQuery query)`
*   **描述**: 根据查询条件获取实时访问统计数据。
*   **参数**:
    *   `query` (RealTimeStatsQuery): 实时统计查询参数，包含日期。
*   **返回值**: `RealTimeStatsResult` - 实时统计结果DTO。
*   **业务逻辑**: 验证查询参数，从 `visitRecordRepository` 查询今日总访问量、当前小时访问量、独立访客数等，并组装成 `RealTimeStatsResult`。

#### queryHistoricalStats - 查询历史统计
*   **方法签名**: `HistoricalStatsResult queryHistoricalStats(HistoricalStatsQuery query)`
*   **描述**: 根据日期范围和粒度查询历史访问统计数据。
*   **参数**:
    *   `query` (HistoricalStatsQuery): 历史统计查询参数，包含开始日期、结束日期和粒度。
*   **返回值**: `HistoricalStatsResult` - 历史统计结果DTO。
*   **业务逻辑**: 验证查询参数，通过循环日期范围从 `visitRecordRepository` 获取每日统计数据，并计算汇总信息。

#### querySourceAnalysis - 查询来源分析
*   **方法签名**: `SourceAnalysisResult querySourceAnalysis(SourceAnalysisQuery query)`
*   **描述**: 根据日期范围查询用户访问来源分析数据。
*   **参数**:
    *   `query` (SourceAnalysisQuery): 来源分析查询参数，包含开始日期和结束日期。
*   **返回值**: `SourceAnalysisResult` - 来源分析结果DTO。
*   **业务逻辑**: 验证查询参数，从 `visitRecordRepository` 查询搜索引擎、直接访问、引荐网站和社交媒体的访问量，并组装成 `SourceAnalysisResult`。

#### queryDeviceAnalysis - 查询设备分析
*   **方法签名**: `DeviceAnalysisResult queryDeviceAnalysis(DeviceAnalysisQuery query)`
*   **描述**: 根据日期范围查询用户访问设备分析数据。
*   **参数**:
    *   `query` (DeviceAnalysisQuery): 设备分析查询参数，包含开始日期和结束日期。
*   **返回值**: `DeviceAnalysisResult` - 设备分析结果DTO。
*   **业务逻辑**: 验证查询参数，从 `visitRecordRepository` 查询桌面、移动、平板设备访问量，以及浏览器和操作系统统计数据，并组装成 `DeviceAnalysisResult`。

#### queryPageRanking - 查询页面排行
*   **方法签名**: `PageRankingResult queryPageRanking(PageRankingQuery query)`
*   **描述**: 根据日期范围和数量限制查询页面访问排名数据。
*   **参数**:
    *   `query` (PageRankingQuery): 页面排行查询参数，包含开始日期、结束日期和限制数量。
*   **返回值**: `PageRankingResult` - 页面排行结果DTO。
*   **业务逻辑**: 验证查询参数，从 `visitRecordRepository` 获取页面访问统计数据，并计算平均停留时间、跳出率、转化率等指标，组装成 `PageRankingResult`。

#### queryTimeAnalysis - 查询时段分析
*   **方法签名**: `TimeAnalysisResult queryTimeAnalysis(TimeAnalysisQuery query)`
*   **描述**: 根据日期范围查询用户访问时段分析数据。
*   **参数**:
    *   `query` (TimeAnalysisQuery): 时段分析查询参数，包含开始日期和结束日期。
*   **返回值**: `TimeAnalysisResult` - 时段分析结果DTO。
*   **业务逻辑**: 验证查询参数，模拟或从数据源获取小时和周的访问数据，并计算高峰时段，组装成 `TimeAnalysisResult`。

#### queryDashboardStats - 查询Dashboard统计数据
*   **方法签名**: `DashboardStatsResult queryDashboardStats(DashboardStatsQuery query)`
*   **描述**: 查询聚合的仪表盘统计数据。
*   **参数**:
    *   `query` (DashboardStatsQuery): 仪表盘统计查询参数，包含日期。
*   **返回值**: `DashboardStatsResult` - 仪表盘统计结果DTO。
*   **业务逻辑**: 验证查询参数，从 `visitRecordRepository` 查询实时在线用户数、今日页面浏览量、平均停留时间、页面浏览量增长率以及历史在线和访问趋势，并组装成 `DashboardStatsResult`。

#### queryGeographicStats - 查询地理统计数据
*   **方法签名**: `GeographicStatsResult queryGeographicStats(GeographicStatsQuery query)`
*   **描述**: 查询用户访问的地理统计数据。
*   **参数**:
    *   `query` (GeographicStatsQuery): 地理统计查询参数，包含地图类型和日期范围。
*   **返回值**: `GeographicStatsResult` - 地理统计结果DTO。
*   **业务逻辑**: 验证查询参数，从 `visitRecordRepository` 获取地理统计数据，并组装成 `GeographicStatsResult`。

#### queryRealTimeTrend - 查询实时趋势数据
*   **方法签名**: `RealTimeTrendResult queryRealTimeTrend(RealTimeTrendQuery query)`
*   **描述**: 查询实时访问趋势数据，支持不同时间粒度。
*   **参数**:
    *   `query` (RealTimeTrendQuery): 实时趋势查询参数，包含粒度、开始时间和结束时间。
*   **返回值**: `RealTimeTrendResult` - 实时趋势结果DTO。
*   **业务逻辑**: 验证查询参数，从 `visitRecordRepository` 获取实时趋势数据，并进行时间格式化，组装成 `RealTimeTrendResult`。

### 3.2 辅助方法

#### calculateSummary - 计算统计汇总
*   **方法签名**: `private HistoricalStatsResult.StatisticsSummary calculateSummary(List<VisitStatistics> statistics)`
*   **描述**: 根据历史访问统计数据列表计算总访问量、总独立访客、平均每日访问量和高峰日期。
*   **参数**:
    *   `statistics` (List<VisitStatistics>): 历史访问统计数据列表。
*   **返回值**: `HistoricalStatsResult.StatisticsSummary` - 统计汇总信息。

#### validateRealTimeQuery - 验证实时查询参数
*   **方法签名**: `private void validateRealTimeQuery(RealTimeStatsQuery query)`
*   **描述**: 验证实时统计查询参数的合法性。

#### validateHistoricalQuery - 验证历史查询参数
*   **方法签名**: `private void validateHistoricalQuery(HistoricalStatsQuery query)`
*   **描述**: 验证历史统计查询参数的合法性，包括日期范围。

#### validateSourceAnalysisQuery - 验证来源分析查询参数
*   **方法签名**: `private void validateSourceAnalysisQuery(SourceAnalysisQuery query)`
*   **描述**: 验证来源分析查询参数的合法性，包括日期范围。

#### validateDeviceAnalysisQuery - 验证设备分析查询参数
*   **方法签名**: `private void validateDeviceAnalysisQuery(DeviceAnalysisQuery query)`
*   **描述**: 验证设备分析查询参数的合法性，包括日期范围。

#### validatePageRankingQuery - 验证页面排行查询参数
*   **方法签名**: `private void validatePageRankingQuery(PageRankingQuery query)`
*   **描述**: 验证页面排行查询参数的合法性，包括日期范围和限制数量。

#### validateTimeAnalysisQuery - 验证时段分析查询参数
*   **方法签名**: `private void validateTimeAnalysisQuery(TimeAnalysisQuery query)`
*   **描述**: 验证时段分析查询参数的合法性，包括日期范围。

#### validateDateRange - 验证日期范围
*   **方法签名**: `private void validateDateRange(LocalDate startDate, LocalDate endDate)`
*   **描述**: 验证开始日期不能晚于结束日期。

#### queryRealHistoricalStatistics - 查询真实的历史统计数据
*   **方法签名**: `private List<VisitStatistics> queryRealHistoricalStatistics(HistoricalStatsQuery query)`
*   **描述**: 模拟从数据库查询每日访问统计数据。

#### querySearchEngineVisits - 查询搜索引擎访问量
*   **方法签名**: `private List<SourceAnalysisResult.SearchEngineSource> querySearchEngineVisits(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 模拟从数据库查询主要搜索引擎的访问量。

#### queryReferralSiteVisits - 查询引荐网站访问量
*   **方法签名**: `private List<SourceAnalysisResult.ReferralSource> queryReferralSiteVisits(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 模拟从数据库查询引荐网站访问量。

#### querySocialMediaVisits - 查询社交媒体访问量
*   **方法签名**: `private List<SourceAnalysisResult.SocialMediaSource> querySocialMediaVisits(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 模拟从数据库查询主要社交媒体的访问量。

#### queryBrowserStatistics - 查询浏览器统计数据
*   **方法签名**: `private List<DeviceAnalysisResult.BrowserInfo> queryBrowserStatistics(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 模拟从数据库查询主要浏览器的访问量。

#### queryOperatingSystemStatistics - 查询操作系统统计数据
*   **方法签名**: `private List<DeviceAnalysisResult.OperatingSystemInfo> queryOperatingSystemStatistics(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 模拟从数据库查询主要操作系统的访问量。

#### formatTimeForDisplay - 格式化时间显示
*   **方法签名**: `private String formatTimeForDisplay(String timeStr, String granularity)`
*   **描述**: 根据粒度格式化时间字符串，用于前端显示。

## 4. 业务规则

*   **参数校验**: 所有查询方法在调用底层仓储之前，都会对传入的查询参数进行严格的非空和逻辑校验（如日期范围、限制数量），确保数据的合法性。
*   **数据聚合**: 服务负责将从 `VisitRecordRepository` 获取的原始访问记录数据，根据不同的统计需求进行聚合和计算，生成各种统计指标。
*   **数据来源**: 统计数据主要来源于 `VisitRecordRepository`，该仓储负责访问底层的访问记录数据。
*   **模拟数据**: 在某些查询方法（如 `queryTimeAnalysis`）中，为了演示或测试目的，使用了模拟数据。在生产环境中，这些方法需要替换为真实的数据库查询逻辑。
*   **职责分离**: 该服务作为应用服务层，专注于协调和数据转换，将具体的业务逻辑（如去重、复杂计算）委托给领域服务或仓储层。

## 5. 使用示例

```java
// 1. 在 AnalyticsController 中调用 StatisticsQueryApplicationService
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    public StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/historical-stats")
    public ApiResponse<HistoricalStatsResult> getHistoricalStats(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "daily") String granularity) {
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            HistoricalStatsQuery query = new HistoricalStatsQuery();
            query.setStartDate(start);
            query.setEndDate(end);
            query.setGranularity(granularity);
            
            HistoricalStatsResult result = statisticsQueryService.queryHistoricalStats(query);
            
            if (result.isSuccess()) {
                return ApiResponse.success(result, "查询成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }
            
        } catch (DateTimeParseException e) {
            return ApiResponse.error("日期格式错误，请使用 yyyy-MM-dd 格式");
        } catch (Exception e) {
            return ApiResponse.error("查询历史统计失败: " + e.getMessage());
        }
    }

    @GetMapping("/dashboard-stats")
    public ApiResponse<DashboardStatsResult> getDashboardStats() {
        try {
            DashboardStatsQuery query = new DashboardStatsQuery();
            DashboardStatsResult result = statisticsQueryService.queryDashboardStats(query);
            if (result.isSuccess()) {
                return ApiResponse.success(result, "查询成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }
        } catch (Exception e) {
            return ApiResponse.error("查询Dashboard统计失败: " + e.getMessage());
        }
    }
}

// 2. 测试示例
@SpringBootTest
class StatisticsQueryApplicationServiceTest {
    @Autowired
    private StatisticsQueryApplicationService service;

    @MockBean
    private VisitRecordRepository visitRecordRepository;

    @Test
    void testQueryRealTimeStats_Success() {
        LocalDate today = LocalDate.now();
        when(visitRecordRepository.countByDateRange(any(LocalDateTime.class), any(LocalDateTime.class)))
            .thenReturn(100L); // 模拟今日访问量
        when(visitRecordRepository.countUniqueVisitorsByDateRange(any(LocalDateTime.class), any(LocalDateTime.class)))
            .thenReturn(50L); // 模拟独立访客

        RealTimeStatsQuery query = new RealTimeStatsQuery();
        query.setDate(today);

        RealTimeStatsResult result = service.queryRealTimeStats(query);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTodayVisits()).isEqualTo(100);
        assertThat(result.getUniqueVisitors()).isEqualTo(50);
    }

    @Test
    void testQueryHistoricalStats_Success() {
        LocalDate start = LocalDate.of(2024, 1, 1);
        LocalDate end = LocalDate.of(2024, 1, 3);

        // 模拟每日统计数据
        when(visitRecordRepository.countVisitsByDate(LocalDate.of(2024, 1, 1))).thenReturn(100L);
        when(visitRecordRepository.countUniqueVisitorsByDate(LocalDate.of(2024, 1, 1))).thenReturn(50L);
        when(visitRecordRepository.countVisitsByDate(LocalDate.of(2024, 1, 2))).thenReturn(120L);
        when(visitRecordRepository.countUniqueVisitorsByDate(LocalDate.of(2024, 1, 2))).thenReturn(60L);
        when(visitRecordRepository.countVisitsByDate(LocalDate.of(2024, 1, 3))).thenReturn(80L);
        when(visitRecordRepository.countUniqueVisitorsByDate(LocalDate.of(2024, 1, 3))).thenReturn(40L);

        HistoricalStatsQuery query = new HistoricalStatsQuery();
        query.setStartDate(start);
        query.setEndDate(end);
        query.setGranularity("daily");

        HistoricalStatsResult result = service.queryHistoricalStats(query);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getStatistics()).hasSize(3);
        assertThat(result.getSummary().getTotalVisits()).isEqualTo(300);
    }
}
```

## 6. 注意事项

*   **分层架构**: 该服务是典型的应用服务层，它协调领域服务和基础设施，但不包含核心业务逻辑。这种分层有助于保持代码的清晰度和可维护性。
*   **参数校验**: 在调用底层仓储之前，对输入参数进行严格的非空和逻辑校验是良好的实践，可以避免将无效数据传递到更深层次的业务逻辑中。
*   **数据源依赖**: 该服务高度依赖 `VisitRecordRepository` 来获取原始访问数据。`VisitRecordRepository` 的实现决定了数据查询的性能和准确性。
*   **性能优化**: 统计功能往往是性能瓶颈。在实现 `VisitRecordRepository` 时，需要特别关注数据库查询的优化，例如使用合适的索引、预计算、数据缓存等技术。
*   **模拟数据**: 在某些方法中使用了模拟数据（如 `queryTimeAnalysis`），这在开发和测试阶段是可接受的，但在生产环境中需要替换为真实的业务逻辑和数据源。
*   **错误处理**: 应用服务捕获了底层可能抛出的异常，并将其转换为友好的 `ApiResponse` 返回给调用方，避免了直接暴露内部异常。
*   **可扩展性**: 如果未来需要增加新的统计维度或分析类型，可以在此服务中添加新的查询方法，并扩展相应的DTO和底层数据获取逻辑。
*   **日志记录**: 在服务中添加适当的日志记录，可以帮助监控服务的调用情况和排查问题。
