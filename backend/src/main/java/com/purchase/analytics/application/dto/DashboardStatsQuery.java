package com.purchase.analytics.application.dto;

import java.time.LocalDate;

/**
 * Dashboard统计查询请求
 */
public class DashboardStatsQuery {
    
    private LocalDate date;
    
    public DashboardStatsQuery() {
        this.date = LocalDate.now();
    }
    
    public DashboardStatsQuery(LocalDate date) {
        this.date = date;
    }
    
    public LocalDate getDate() {
        return date;
    }
    
    public void setDate(LocalDate date) {
        this.date = date;
    }
}
