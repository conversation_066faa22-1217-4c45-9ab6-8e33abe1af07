# TrackVisitCommand.md

## 1. 文件概述

`TrackVisitCommand` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.dto` 包中。它用于封装用户访问跟踪的命令数据。这个DTO包含了用户ID、会话ID、IP地址、用户代理、访问页面URL、Referer、访问时间以及客户端屏幕分辨率和时区等详细信息。它的核心作用是作为应用服务层接收和处理用户访问事件的标准化输入，确保所有必要的上下文信息都被捕获，以便进行后续的分析和持久化。

## 2. 核心功能

*   **数据封装**: 封装了用户访问事件的所有相关数据，包括用户身份、会话信息、网络信息和客户端环境信息。
*   **数据标准化**: 将来自不同来源（如HTTP请求头、请求体）的原始数据统一封装到结构化的对象中，便于应用服务层进行处理。
*   **类型安全**: 使用Java的强类型特性，确保了每个字段的数据类型符合预期。
*   **可扩展性**: 易于添加新的字段来捕获更多维度的访问信息，以满足不断变化的分析需求。

## 3. 属性说明

- **`userId` (Long)**: 访问用户的ID。如果用户未登录，可能为 `null`。
- **`sessionId` (String)**: 用户的会话ID，用于追踪用户在一段时间内的连续行为。
- **`ipAddress` (String)**: 客户端的IP地址。
- **`userAgent` (String)**: 客户端的用户代理字符串（User-Agent），包含浏览器和操作系统信息。
- **`pageUrl` (String)**: 用户访问的页面URL。
- **`referer` (String)**: 引导用户访问当前页面的上一页URL。
- **`visitTime` (LocalDateTime)**: 访问发生的时间。
- **`screenResolution` (String)**: 客户端屏幕的分辨率（例如：`1920x1080`）。
- **`timezone` (String)**: 客户端所在的时区。

## 4. 业务规则

*   **数据来源**: `TrackVisitCommand` 中的数据通常由Controller层从HTTP请求中解析并组装而来。
*   **必填字段**: 尽管DTO中没有JSR-303注解，但在实际业务处理中，某些字段（如 `pageUrl`, `visitTime`）可能是必填的，需要在应用服务层进行校验。
*   **IP地址获取**: `ipAddress` 字段的获取需要考虑代理和负载均衡的情况，通常需要从 `X-Forwarded-For` 或 `X-Real-IP` 等HTTP头中提取。
*   **时间戳**: `visitTime` 字段应在后端接收到请求时生成，以确保时间戳的准确性。

## 5. 使用示例

```java
// 1. 在 Controller 中构建 TrackVisitCommand
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    public VisitTrackingApplicationService visitTrackingService;

    @PostMapping("/track-visit")
    public ApiResponse<TrackVisitResult> trackVisit(@RequestBody TrackVisitRequest request, 
                                                   HttpServletRequest httpRequest) {
        try {
            // 参数验证
            if (request == null) {
                return ApiResponse.error("请求参数不能为空");
            }
            
            // 转换为应用层命令
            TrackVisitCommand command = new TrackVisitCommand();
            command.setUserId(request.getUserId());
            command.setSessionId(request.getSessionId());
            command.setPageUrl(request.getPageUrl() != null ? request.getPageUrl() : "/");
            command.setReferer(request.getReferer());
            command.setVisitTime(LocalDateTime.now()); // 在后端生成访问时间
            command.setIpAddress(getClientIpAddress(httpRequest)); // 从HTTP请求获取IP
            
            if (request.getClientInfo() != null) {
                command.setUserAgent(request.getClientInfo().getUserAgent());
                command.setScreenResolution(request.getClientInfo().getScreenResolution());
                command.setTimezone(request.getClientInfo().getTimezone());
            }
            
            // 调用应用服务
            TrackVisitResult result = visitTrackingService.trackVisit(command);
            
            if (result.isSuccess()) {
                return ApiResponse.success(result, "访问记录成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }
            
        } catch (Exception e) {
            return ApiResponse.error("访问跟踪失败: " + e.getMessage());
        }
    }

    // 辅助方法，用于获取客户端IP地址
    private String getClientIpAddress(HttpServletRequest request) {
        // ... 实现获取IP地址的逻辑 ...
        return "127.0.0.1";
    }
}

// 2. 在 VisitTrackingApplicationService 中使用 TrackVisitCommand
@Service
public class VisitTrackingApplicationServiceImpl implements VisitTrackingApplicationService {
    @Autowired
    private VisitLogRepository visitLogRepository;

    @Override
    @Async // 异步处理，避免阻塞主线程
    public TrackVisitResult trackVisit(TrackVisitCommand command) {
        try {
            // 将命令转换为领域实体并持久化
            VisitLog visitLog = new VisitLog();
            visitLog.setUserId(command.getUserId());
            visitLog.setSessionId(command.getSessionId());
            visitLog.setIpAddress(command.getIpAddress());
            visitLog.setPageUrl(command.getPageUrl());
            visitLog.setVisitTime(command.getVisitTime());
            // ... 设置其他属性 ...

            visitLogRepository.save(visitLog);
            return new TrackVisitResult(true, "记录成功");
        } catch (Exception e) {
            return new TrackVisitResult(false, "记录失败: " + e.getMessage());
        }
    }
}

// 3. 测试示例
@SpringBootTest
class TrackVisitCommandTest {
    @Test
    void testSettersAndGetters() {
        TrackVisitCommand command = new TrackVisitCommand();
        Long userId = 123L;
        String sessionId = "abc-123";
        String ipAddress = "***********";
        String pageUrl = "/products/1";
        LocalDateTime visitTime = LocalDateTime.now();

        command.setUserId(userId);
        command.setSessionId(sessionId);
        command.setIpAddress(ipAddress);
        command.setPageUrl(pageUrl);
        command.setVisitTime(visitTime);

        assertThat(command.getUserId()).isEqualTo(userId);
        assertThat(command.getSessionId()).isEqualTo(sessionId);
        assertThat(command.getIpAddress()).isEqualTo(ipAddress);
        assertThat(command.getPageUrl()).isEqualTo(pageUrl);
        assertThat(command.getVisitTime()).isEqualTo(visitTime);
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `TrackVisitCommand` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 尽管当前DTO有Setter方法，但对于命令对象，如果可能，最好设计为不可变对象（所有字段 `final`，只提供全参构造函数），以增强线程安全性和数据一致性。
*   **数据来源**: 确保从HTTP请求中正确提取所有必要的信息，特别是IP地址和User-Agent等。
*   **时间戳**: `visitTime` 字段应在后端接收到请求时生成，而不是依赖前端传递，以防止客户端时间不准确导致的数据问题。
*   **异步处理**: 访问跟踪通常是高并发操作，且不应阻塞主业务流程。因此，处理 `TrackVisitCommand` 的应用服务方法应设计为异步的，以提高系统吞吐量。
*   **数据清洗与校验**: 在将 `TrackVisitCommand` 转换为持久化实体之前，可能需要进行更严格的数据清洗和校验，例如URL的合法性、IP地址的格式等。
*   **可扩展性**: 如果未来需要跟踪更多维度的信息，可以在此DTO中添加相应属性。