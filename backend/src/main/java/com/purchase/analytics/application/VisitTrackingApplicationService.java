package com.purchase.analytics.application;

import com.purchase.analytics.application.dto.TrackVisitCommand;
import com.purchase.analytics.application.dto.TrackVisitResult;
import com.purchase.analytics.domain.model.*;
import com.purchase.analytics.domain.repository.VisitRecordRepository;
import com.purchase.analytics.domain.service.VisitDeduplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.UUID;

/**
 * 访问跟踪应用服务
 * 负责处理访问跟踪相关的应用层逻辑
 */
@Service
public class VisitTrackingApplicationService {

    @Autowired
    private VisitDeduplicationService visitDeduplicationService;

    @Autowired
    private VisitRecordRepository visitRecordRepository;

    // 为测试提供setter方法
    public void setVisitDeduplicationService(VisitDeduplicationService visitDeduplicationService) {
        this.visitDeduplicationService = visitDeduplicationService;
    }

    public void setVisitRecordRepository(VisitRecordRepository visitRecordRepository) {
        this.visitRecordRepository = visitRecordRepository;
    }
    
    /**
     * 跟踪访问
     */
    public TrackVisitResult trackVisit(TrackVisitCommand command) {
        // 参数验证
        validateCommand(command);

        try {
            // 创建访问记录
            VisitRecord visitRecord = createVisitRecord(command);

            // 检查是否重复访问
            boolean isDuplicate = visitDeduplicationService.isDuplicateVisit(
                visitRecord, Collections.emptyList() // 简化实现，实际应查询最近访问记录
            );

            // 如果是重复访问，标记为非唯一访问
            if (isDuplicate) {
                visitRecord.markAsNonUniqueVisit();
            }

            // 保存访问记录到数据库
            VisitRecord savedRecord = visitRecordRepository.save(visitRecord);

            // 生成访问ID（简化实现）
            String visitId = generateVisitId();

            // 返回结果
            return TrackVisitResult.success(visitId, savedRecord.isUniqueVisit());

        } catch (Exception e) {
            return TrackVisitResult.failure("访问记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证命令参数
     */
    private void validateCommand(TrackVisitCommand command) {
        if (command == null) {
            throw new IllegalArgumentException("跟踪访问命令不能为空");
        }
        
        if (command.getSessionId() == null || command.getSessionId().trim().isEmpty()) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        
        if (command.getIpAddress() == null || command.getIpAddress().trim().isEmpty()) {
            throw new IllegalArgumentException("IP地址不能为空");
        }
        
        if (command.getPageUrl() == null || command.getPageUrl().trim().isEmpty()) {
            throw new IllegalArgumentException("页面URL不能为空");
        }
        
        if (command.getVisitTime() == null) {
            throw new IllegalArgumentException("访问时间不能为空");
        }
    }
    
    /**
     * 创建访问记录
     */
    private VisitRecord createVisitRecord(TrackVisitCommand command) {
        if (command.getUserId() != null) {
            // 已登录用户
            return VisitRecord.createForRegisteredUser(
                command.getUserId(),
                command.getSessionId(),
                command.getIpAddress(),
                command.getPageUrl(),
                command.getVisitTime()
            );
        } else {
            // 匿名用户
            return VisitRecord.createForAnonymousUser(
                command.getSessionId(),
                command.getIpAddress(),
                command.getUserAgent(),
                command.getPageUrl(),
                command.getVisitTime()
            );
        }
    }
    
    /**
     * 生成访问ID
     */
    private String generateVisitId() {
        return UUID.randomUUID().toString();
    }
}
