# DashboardStatsResult.md

## 1. 文件概述

`DashboardStatsResult` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.dto` 包中。它用于封装仪表盘统计查询的返回结果。这个DTO设计旨在提供一个全面且易于前端展示的聚合数据视图，包含了实时在线人数、页面浏览量、平均停留时间、页面浏览增长率以及历史在线和访问趋势等关键指标。它通过静态工厂方法 `success` 和 `failure` 提供了便捷的创建方式，确保了返回结果的清晰性和一致性。

## 2. 核心功能

*   **数据聚合**: 聚合了多种与用户行为和网站性能相关的统计指标，为仪表盘提供“一站式”数据。
*   **状态指示**: 包含 `success` 字段和 `message` 字段，明确指示查询操作是否成功以及相关的提示信息。
*   **趋势数据**: 提供了 `onlineHistory` 和 `visitHistory` 列表，用于展示历史趋势图表，帮助用户直观了解数据变化。
*   **时间戳**: `lastUpdated` 字段记录了数据生成的时间，有助于前端判断数据的时效性。
*   **工厂方法**: 通过 `success()` 和 `failure()` 静态方法，简化了DTO的创建过程，并确保了返回结果的规范性。

## 3. 属性说明

- **`success` (boolean)**: 表示查询操作是否成功。
- **`message` (String)**: 查询结果的描述信息，成功时通常为“查询成功”，失败时为错误信息。
- **`realtimeOnline` (int)**: 实时在线用户数量。
- **`pageViews` (int)**: 页面总浏览量。
- **`avgStayTime` (double)**: 平均用户停留时间（单位通常为秒）。
- **`pageViewGrowthRate` (double)**: 页面浏览量的增长率（例如，与上一周期相比）。
- **`onlineHistory` (List<Integer>)**: 历史在线人数列表，用于绘制趋势图。
- **`visitHistory` (List<Integer>)**: 历史访问量列表，用于绘制趋势图。
- **`lastUpdated` (LocalDateTime)**: 数据最后更新的时间戳。

## 4. 业务规则

*   **数据来源**: `DashboardStatsResult` 中的数据通常由 `StatisticsQueryApplicationService` 协调多个领域服务或数据仓储计算和聚合而来。
*   **数据时效性**: 实时数据（如 `realtimeOnline`）应尽可能接近实时，而历史数据则可能基于预计算或定时任务生成。
*   **增长率计算**: `pageViewGrowthRate` 的计算逻辑需要明确定义，例如是与昨日、上周或上月进行比较。
*   **历史数据粒度**: `onlineHistory` 和 `visitHistory` 中的数据粒度（例如，是每小时、每天）应与前端展示需求一致。

## 5. 使用示例

```java
// 1. 在 StatisticsQueryApplicationService 中构建 DashboardStatsResult
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private RealtimeStatsService realtimeStatsService;
    @Autowired
    private HistoricalStatsService historicalStatsService;

    public DashboardStatsResult queryDashboardStats(DashboardStatsQuery query) {
        try {
            // 模拟获取实时数据
            int realtimeOnline = realtimeStatsService.getRealtimeOnlineUsers();
            int pageViews = historicalStatsService.getTotalPageViewsToday();
            double avgStayTime = historicalStatsService.getAverageStayTimeToday();
            double pageViewGrowthRate = historicalStatsService.getPageViewGrowthRate();
            List<Integer> onlineHistory = historicalStatsService.getOnlineHistoryLast24Hours();
            List<Integer> visitHistory = historicalStatsService.getVisitHistoryLast24Hours();

            return DashboardStatsResult.success(
                realtimeOnline, pageViews, avgStayTime, 
                pageViewGrowthRate, onlineHistory, visitHistory
            );
        } catch (Exception e) {
            return DashboardStatsResult.failure("查询Dashboard统计失败: " + e.getMessage());
        }
    }
}

// 2. 在 Controller 中返回 DashboardStatsResult
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/dashboard-stats")
    public ApiResponse<DashboardStatsResult> getDashboardStats() {
        DashboardStatsQuery query = new DashboardStatsQuery(); // 默认查询今天
        DashboardStatsResult result = statisticsQueryService.queryDashboardStats(query);
        
        if (result.isSuccess()) {
            return ApiResponse.success(result, result.getMessage());
        } else {
            return ApiResponse.error(result.getMessage());
        }
    }
}

// 3. 前端 (React) 接收并展示仪表盘数据
/*
import React, { useEffect, useState } from 'react';
import axios from 'axios';

function Dashboard() {
  const [stats, setStats] = useState(null);

  useEffect(() => {
    axios.get('/api/v1/analytics/dashboard-stats')
      .then(response => {
        if (response.data.success) {
          setStats(response.data.data);
        } else {
          console.error('获取仪表盘数据失败:', response.data.message);
        }
      })
      .catch(error => {
        console.error('请求失败:', error);
      });
  }, []);

  if (!stats) {
    return <div>加载中...</div>;
  }

  return (
    <div>
      <h1>实时概览</h1>
      <p>实时在线: {stats.realtimeOnline}</p>
      <p>今日浏览量: {stats.pageViews}</p>
      <p>平均停留时间: {stats.avgStayTime.toFixed(2)}s</p>
      <p>浏览量增长率: {stats.pageViewGrowthRate.toFixed(2)}%</p>
      {/* 绘制图表 */}
      <p>最后更新: {new Date(stats.lastUpdated).toLocaleString()}</p>
    </div>
  );
}
*/

// 4. 测试示例
@SpringBootTest
class DashboardStatsResultTest {
    @Test
    void testSuccessFactoryMethod() {
        List<Integer> onlineHistory = List.of(10, 12, 15);
        List<Integer> visitHistory = List.of(100, 120, 150);
        DashboardStatsResult result = DashboardStatsResult.success(
            20, 500, 60.5, 10.2, onlineHistory, visitHistory
        );

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("查询成功");
        assertThat(result.getRealtimeOnline()).isEqualTo(20);
        assertThat(result.getPageViews()).isEqualTo(500);
        assertThat(result.getOnlineHistory()).isEqualTo(onlineHistory);
        assertThat(result.getLastUpdated()).isNotNull();
    }

    @Test
    void testFailureFactoryMethod() {
        DashboardStatsResult result = DashboardStatsResult.failure("数据库连接失败");

        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).isEqualTo("数据库连接失败");
        assertThat(result.getRealtimeOnline()).isEqualTo(0); // 默认值
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `DashboardStatsResult` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 尽管当前DTO有Setter方法，但对于结果DTO，如果可能，最好设计为不可变对象（所有字段 `final`，只提供全参构造函数），以增强线程安全性和数据一致性。
*   **数据类型**: 确保数值类型（`int`, `double`）能够准确表示统计数据，特别是百分比和平均值。
*   **时间戳**: `lastUpdated` 字段对于前端判断数据新鲜度非常重要，应在数据生成时准确设置。
*   **可扩展性**: 如果未来仪表盘需要展示更多统计指标，可以在此DTO中添加相应属性。
*   **前端适配**: DTO的结构应尽可能与前端组件的期望数据格式匹配，减少前端的数据转换工作。
*   **性能**: 仪表盘数据通常是聚合计算的结果，其生成可能涉及复杂的查询。在服务层构建此DTO时，应特别关注性能优化，例如使用缓存、预计算等。