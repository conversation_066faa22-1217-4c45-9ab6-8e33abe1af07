package com.purchase.analytics.application.result;

import java.util.List;

/**
 * 来源分析结果
 */
public class SourceAnalysisResult {
    private final List<SearchEngineSource> searchEngines;
    private final int directVisits;
    private final List<ReferralSource> referralSites;
    private final List<SocialMediaSource> socialMedia;
    
    public SourceAnalysisResult(List<SearchEngineSource> searchEngines, 
                               int directVisits,
                               List<ReferralSource> referralSites,
                               List<SocialMediaSource> socialMedia) {
        this.searchEngines = searchEngines;
        this.directVisits = directVisits;
        this.referralSites = referralSites;
        this.socialMedia = socialMedia;
    }
    
    public List<SearchEngineSource> getSearchEngines() {
        return searchEngines;
    }
    
    public int getDirectVisits() {
        return directVisits;
    }
    
    public List<ReferralSource> getReferralSites() {
        return referralSites;
    }
    
    public List<SocialMediaSource> getSocialMedia() {
        return socialMedia;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        SourceAnalysisResult that = (SourceAnalysisResult) o;
        
        if (directVisits != that.directVisits) return false;
        if (!searchEngines.equals(that.searchEngines)) return false;
        if (!referralSites.equals(that.referralSites)) return false;
        return socialMedia.equals(that.socialMedia);
    }
    
    @Override
    public int hashCode() {
        int result = searchEngines.hashCode();
        result = 31 * result + directVisits;
        result = 31 * result + referralSites.hashCode();
        result = 31 * result + socialMedia.hashCode();
        return result;
    }
    
    /**
     * 搜索引擎来源
     */
    public static class SearchEngineSource {
        private final String name;
        private final int visits;
        
        public SearchEngineSource(String name, int visits) {
            this.name = name;
            this.visits = visits;
        }
        
        public String getName() {
            return name;
        }
        
        public int getVisits() {
            return visits;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            
            SearchEngineSource that = (SearchEngineSource) o;
            
            if (visits != that.visits) return false;
            return name.equals(that.name);
        }
        
        @Override
        public int hashCode() {
            int result = name.hashCode();
            result = 31 * result + visits;
            return result;
        }
    }
    
    /**
     * 引荐网站来源
     */
    public static class ReferralSource {
        private final String domain;
        private final int visits;
        
        public ReferralSource(String domain, int visits) {
            this.domain = domain;
            this.visits = visits;
        }
        
        public String getDomain() {
            return domain;
        }
        
        public int getVisits() {
            return visits;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            
            ReferralSource that = (ReferralSource) o;
            
            if (visits != that.visits) return false;
            return domain.equals(that.domain);
        }
        
        @Override
        public int hashCode() {
            int result = domain.hashCode();
            result = 31 * result + visits;
            return result;
        }
    }
    
    /**
     * 社交媒体来源
     */
    public static class SocialMediaSource {
        private final String platform;
        private final int visits;
        
        public SocialMediaSource(String platform, int visits) {
            this.platform = platform;
            this.visits = visits;
        }
        
        public String getPlatform() {
            return platform;
        }
        
        public int getVisits() {
            return visits;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            
            SocialMediaSource that = (SocialMediaSource) o;
            
            if (visits != that.visits) return false;
            return platform.equals(that.platform);
        }
        
        @Override
        public int hashCode() {
            int result = platform.hashCode();
            result = 31 * result + visits;
            return result;
        }
    }
}
