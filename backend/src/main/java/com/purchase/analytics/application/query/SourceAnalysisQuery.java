package com.purchase.analytics.application.query;

import java.time.LocalDate;

/**
 * 来源分析查询对象
 */
public class SourceAnalysisQuery {
    private final LocalDate startDate;
    private final LocalDate endDate;
    
    public SourceAnalysisQuery(LocalDate startDate, LocalDate endDate) {
        this.startDate = startDate;
        this.endDate = endDate;
    }
    
    public LocalDate getStartDate() {
        return startDate;
    }
    
    public LocalDate getEndDate() {
        return endDate;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        SourceAnalysisQuery that = (SourceAnalysisQuery) o;
        
        if (!startDate.equals(that.startDate)) return false;
        return endDate.equals(that.endDate);
    }
    
    @Override
    public int hashCode() {
        int result = startDate.hashCode();
        result = 31 * result + endDate.hashCode();
        return result;
    }
    
    @Override
    public String toString() {
        return "SourceAnalysisQuery{" +
                "startDate=" + startDate +
                ", endDate=" + endDate +
                '}';
    }
}
