package com.purchase.analytics.application.query;

import java.time.LocalDate;

/**
 * 页面排行查询对象
 */
public class PageRankingQuery {
    private final LocalDate startDate;
    private final LocalDate endDate;
    private final int limit;
    
    public PageRankingQuery(LocalDate startDate, LocalDate endDate, int limit) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.limit = limit;
    }
    
    public LocalDate getStartDate() {
        return startDate;
    }
    
    public LocalDate getEndDate() {
        return endDate;
    }
    
    public int getLimit() {
        return limit;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        PageRankingQuery that = (PageRankingQuery) o;
        
        if (limit != that.limit) return false;
        if (!startDate.equals(that.startDate)) return false;
        return endDate.equals(that.endDate);
    }
    
    @Override
    public int hashCode() {
        int result = startDate.hashCode();
        result = 31 * result + endDate.hashCode();
        result = 31 * result + limit;
        return result;
    }
    
    @Override
    public String toString() {
        return "PageRankingQuery{" +
                "startDate=" + startDate +
                ", endDate=" + endDate +
                ", limit=" + limit +
                '}';
    }
}
