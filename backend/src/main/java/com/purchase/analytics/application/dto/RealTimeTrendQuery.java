package com.purchase.analytics.application.dto;

import java.time.LocalDateTime;

/**
 * 实时趋势查询请求
 */
public class RealTimeTrendQuery {
    
    private String granularity; // minute, hour, day
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    
    public RealTimeTrendQuery() {}
    
    public RealTimeTrendQuery(String granularity, LocalDateTime startTime, LocalDateTime endTime) {
        this.granularity = granularity;
        this.startTime = startTime;
        this.endTime = endTime;
    }
    
    public String getGranularity() {
        return granularity;
    }
    
    public void setGranularity(String granularity) {
        this.granularity = granularity;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
}
