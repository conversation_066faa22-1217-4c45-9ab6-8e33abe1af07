package com.purchase.analytics.application.query;

import java.time.LocalDate;

/**
 * 设备分析查询对象
 */
public class DeviceAnalysisQuery {
    private final LocalDate startDate;
    private final LocalDate endDate;
    
    public DeviceAnalysisQuery(LocalDate startDate, LocalDate endDate) {
        this.startDate = startDate;
        this.endDate = endDate;
    }
    
    public LocalDate getStartDate() {
        return startDate;
    }
    
    public LocalDate getEndDate() {
        return endDate;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        DeviceAnalysisQuery that = (DeviceAnalysisQuery) o;
        
        if (!startDate.equals(that.startDate)) return false;
        return endDate.equals(that.endDate);
    }
    
    @Override
    public int hashCode() {
        int result = startDate.hashCode();
        result = 31 * result + endDate.hashCode();
        return result;
    }
    
    @Override
    public String toString() {
        return "DeviceAnalysisQuery{" +
                "startDate=" + startDate +
                ", endDate=" + endDate +
                '}';
    }
}
