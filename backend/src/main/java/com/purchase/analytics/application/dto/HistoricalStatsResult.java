package com.purchase.analytics.application.dto;

import com.purchase.analytics.domain.model.VisitStatistics;
import java.util.List;

/**
 * 历史统计结果DTO
 */
public class HistoricalStatsResult {
    
    private boolean success;
    private List<VisitStatistics> statistics;
    private StatisticsSummary summary;
    private String message;
    
    public HistoricalStatsResult() {}
    
    public static HistoricalStatsResult success(List<VisitStatistics> statistics, StatisticsSummary summary) {
        HistoricalStatsResult result = new HistoricalStatsResult();
        result.success = true;
        result.statistics = statistics;
        result.summary = summary;
        result.message = "查询成功";
        return result;
    }
    
    public static HistoricalStatsResult failure(String message) {
        HistoricalStatsResult result = new HistoricalStatsResult();
        result.success = false;
        result.message = message;
        return result;
    }
    
    // Getters and Setters
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public List<VisitStatistics> getStatistics() { return statistics; }
    public void setStatistics(List<VisitStatistics> statistics) { this.statistics = statistics; }
    
    public StatisticsSummary getSummary() { return summary; }
    public void setSummary(StatisticsSummary summary) { this.summary = summary; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    
    /**
     * 统计汇总内部类
     */
    public static class StatisticsSummary {
        private int totalVisits;
        private int totalUniqueVisitors;
        private int avgDailyVisits;
        private String peakDay;
        
        public StatisticsSummary(int totalVisits, int totalUniqueVisitors, int avgDailyVisits, String peakDay) {
            this.totalVisits = totalVisits;
            this.totalUniqueVisitors = totalUniqueVisitors;
            this.avgDailyVisits = avgDailyVisits;
            this.peakDay = peakDay;
        }
        
        // Getters
        public int getTotalVisits() { return totalVisits; }
        public int getTotalUniqueVisitors() { return totalUniqueVisitors; }
        public int getAvgDailyVisits() { return avgDailyVisits; }
        public String getPeakDay() { return peakDay; }
    }
}
