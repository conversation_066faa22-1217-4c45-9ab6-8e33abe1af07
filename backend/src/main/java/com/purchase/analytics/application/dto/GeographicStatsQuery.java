package com.purchase.analytics.application.dto;

import java.time.LocalDate;

/**
 * 地理统计查询请求
 */
public class GeographicStatsQuery {
    
    private LocalDate startDate;
    private LocalDate endDate;
    private String mapType; // china, world
    
    public GeographicStatsQuery() {}
    
    public GeographicStatsQuery(LocalDate startDate, LocalDate endDate, String mapType) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.mapType = mapType;
    }
    
    public LocalDate getStartDate() {
        return startDate;
    }
    
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }
    
    public LocalDate getEndDate() {
        return endDate;
    }
    
    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }
    
    public String getMapType() {
        return mapType;
    }
    
    public void setMapType(String mapType) {
        this.mapType = mapType;
    }
}
