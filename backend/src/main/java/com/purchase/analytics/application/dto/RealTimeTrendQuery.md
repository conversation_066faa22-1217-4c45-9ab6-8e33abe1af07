# RealTimeTrendQuery.md

## 1. 文件概述

`RealTimeTrendQuery` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.dto` 包中。它用于封装查询实时趋势数据时的请求参数。这个DTO包含了查询的时间粒度（例如 `minute`, `hour`, `day`）以及时间范围（开始时间和结束时间），旨在为前端提供灵活的实时趋势图表查询能力。它的核心作用是作为应用服务层接收前端或上层服务请求的标准化输入，确保查询参数的类型安全和结构清晰。

## 2. 核心功能

*   **参数封装**: 封装了查询实时趋势数据所需的时间粒度、开始时间和结束时间参数。
*   **灵活性**: 支持通过设置不同的 `granularity`, `startTime` 和 `endTime` 来查询不同时间粒度和不同时间段的实时趋势数据。
*   **类型安全**: 使用 `LocalDateTime` 类型确保日期时间数据的正确性。
*   **简洁性**: 设计轻量级，只包含必要的查询字段，避免了不必要的复杂性。

## 3. 属性说明

- **`granularity` (String)**: 统计的时间粒度，用于指定数据聚合的级别，例如 `minute`（按分钟）、`hour`（按小时）或 `day`（按天）。
- **`startTime` (LocalDateTime)**: 查询实时趋势数据的开始时间。
- **`endTime` (LocalDateTime)**: 查询实时趋势数据的结束时间。

## 4. 业务规则

*   **时间范围有效性**: `startTime` 必须早于或等于 `endTime`。在Controller或Application Service层接收到此DTO后，应进行此项校验。
*   **粒度枚举**: `granularity` 字段的值应限定在预定义的枚举或常量中，例如 `minute`, `hour`, `day`。在服务层应进行校验。
*   **数据聚合**: 领域服务会根据 `granularity` 参数来聚合原始访问数据，生成不同粒度的趋势结果。

## 5. 使用示例

```java
// 1. 在 Controller 中构建 RealTimeTrendQuery
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/real-time-trend")
    public ApiResponse<RealTimeTrendResult> getRealTimeTrend(
            @RequestParam(defaultValue = "hour") String granularity,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        try {
            RealTimeTrendQuery query = new RealTimeTrendQuery();
            query.setGranularity(granularity);

            // 设置默认时间范围
            if (startTime != null && endTime != null) {
                query.setStartTime(parseTimeString(startTime));
                query.setEndTime(parseTimeString(endTime));
            } else {
                // 根据粒度设置默认时间范围
                LocalDateTime now = LocalDateTime.now();
                switch (granularity.toLowerCase()) {
                    case "minute":
                        query.setStartTime(now.minusHours(1)); // 最近1小时
                        break;
                    case "hour":
                        query.setStartTime(now.minusHours(24)); // 最近24小时
                        break;
                    case "day":
                        query.setStartTime(now.minusDays(30)); // 最近30天
                        break;
                    default:
                        query.setStartTime(now.minusHours(24));
                }
                query.setEndTime(now);
            }

            RealTimeTrendResult result = statisticsQueryService.queryRealTimeTrend(query);

            if (result.isSuccess()) {
                return ApiResponse.success(result, "查询成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }

        } catch (Exception e) {
            return ApiResponse.error("查询实时趋势失败: " + e.getMessage());
        }
    }

    // 辅助方法，用于解析时间字符串
    private LocalDateTime parseTimeString(String timeString) {
        try {
            // 尝试解析ISO格式时间（包含Z）
            if (timeString.contains("Z") || timeString.contains("+") || timeString.contains("T")) {
                Instant instant = Instant.parse(timeString);
                return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
            } else {
                // 尝试解析LocalDateTime格式
                return LocalDateTime.parse(timeString);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("无法解析时间字符串: " + timeString, e);
        }
    }
}

// 2. 在 StatisticsQueryApplicationService 中使用 RealTimeTrendQuery
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private RealtimeTrendRepository realtimeTrendRepository;

    public RealTimeTrendResult queryRealTimeTrend(RealTimeTrendQuery query) {
        // 业务逻辑：根据query的时间粒度、开始时间和结束时间从实时数据源获取趋势数据
        // 假设这里调用一个领域服务或仓储来获取数据
        List<TrendData> rawData = realtimeTrendRepository.findRealtimeTrendData(
            query.getGranularity(), query.getStartTime(), query.getEndTime()
        );

        // 将原始数据转换为 RealTimeTrendResult DTO
        RealTimeTrendResult result = new RealTimeTrendResult();
        result.setSuccess(true);
        result.setMessage("查询成功");
        result.setGranularity(query.getGranularity());
        result.setTrendData(rawData.stream().map(d -> new RealTimeTrendResult.TrendEntry(d.getTimestamp(), d.getValue())).collect(Collectors.toList()));
        return result;
    }
}

// 3. 测试示例
@SpringBootTest
class RealTimeTrendQueryTest {
    @Test
    void testConstructorAndGetters() {
        LocalDateTime startTime = LocalDateTime.of(2023, 1, 1, 10, 0);
        LocalDateTime endTime = LocalDateTime.of(2023, 1, 1, 11, 0);
        String granularity = "minute";
        RealTimeTrendQuery query = new RealTimeTrendQuery(granularity, startTime, endTime);

        assertThat(query.getGranularity()).isEqualTo(granularity);
        assertThat(query.getStartTime()).isEqualTo(startTime);
        assertThat(query.getEndTime()).isEqualTo(endTime);
    }

    @Test
    void testSetters() {
        RealTimeTrendQuery query = new RealTimeTrendQuery();
        LocalDateTime newStartTime = LocalDateTime.of(2024, 2, 1, 9, 0);
        LocalDateTime newEndTime = LocalDateTime.of(2024, 2, 1, 10, 0);
        String newGranularity = "hour";

        query.setStartTime(newStartTime);
        query.setEndTime(newEndTime);
        query.setGranularity(newGranularity);

        assertThat(query.getStartTime()).isEqualTo(newStartTime);
        assertThat(query.getEndTime()).isEqualTo(newEndTime);
        assertThat(query.getGranularity()).isEqualTo(newGranularity);
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `RealTimeTrendQuery` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 尽管当前DTO有Setter方法，但对于查询参数DTO，如果可能，最好设计为不可变对象（所有字段 `final`，只提供全参构造函数），以增强线程安全性和数据一致性。
*   **日期时间处理**: 使用 `java.time.LocalDateTime` 是处理日期时间的最佳实践，避免了 `java.util.Date` 的时区问题。
*   **参数校验**: 尽管DTO本身没有JSR-303注解，但在Controller或Application Service层接收到此DTO后，仍需对 `startTime` 和 `endTime` 的逻辑关系以及 `granularity` 的有效性进行业务校验。
*   **可扩展性**: 如果未来实时趋势需要更多查询条件（如按用户角色、地域等），可以在此DTO中添加相应属性。