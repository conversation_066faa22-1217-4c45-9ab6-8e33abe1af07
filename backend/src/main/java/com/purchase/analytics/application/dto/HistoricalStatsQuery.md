# HistoricalStatsQuery.md

## 1. 文件概述

`HistoricalStatsQuery` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.dto` 包中。它用于封装查询历史统计数据时的请求参数。这个DTO包含了查询的日期范围（开始日期和结束日期）以及统计粒度（例如 `daily`, `weekly`, `monthly`），旨在为前端提供灵活的历史数据查询能力。它的核心作用是作为应用服务层接收前端或上层服务请求的标准化输入，确保查询参数的类型安全和结构清晰。

## 2. 核心功能

*   **参数封装**: 封装了查询历史统计数据所需的日期范围和统计粒度参数。
*   **灵活性**: 支持通过设置不同的 `startDate`, `endDate` 和 `granularity` 来查询不同时间段和不同聚合粒度的历史统计数据。
*   **类型安全**: 使用 `LocalDate` 类型确保日期数据的正确性。
*   **简洁性**: 设计轻量级，只包含必要的查询字段，避免了不必要的复杂性。

## 3. 属性说明

- **`startDate` (LocalDate)**: 查询历史统计数据的开始日期。
- **`endDate` (LocalDate)**: 查询历史统计数据的结束日期。
- **`granularity` (String)**: 统计粒度，用于指定数据聚合的级别，例如 `daily`（按天）、`weekly`（按周）或 `monthly`（按月）。

## 4. 业务规则

*   **日期范围有效性**: `startDate` 必须早于或等于 `endDate`。在Controller或Application Service层接收到此DTO后，应进行此项校验。
*   **粒度枚举**: `granularity` 字段的值应限定在预定义的枚举或常量中，例如 `daily`, `weekly`, `monthly`。在服务层应进行校验。
*   **数据聚合**: 领域服务会根据 `granularity` 参数来聚合原始访问数据，生成不同粒度的统计结果。

## 5. 使用示例

```java
// 1. 在 Controller 中构建 HistoricalStatsQuery
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/historical-stats")
    public ApiResponse<HistoricalStatsResult> getHistoricalStats(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "daily") String granularity) {
        try {
            // 参数验证和转换
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            
            HistoricalStatsQuery query = new HistoricalStatsQuery();
            query.setStartDate(start);
            query.setEndDate(end);
            query.setGranularity(granularity);
            
            HistoricalStatsResult result = statisticsQueryService.queryHistoricalStats(query);
            
            if (result.isSuccess()) {
                return ApiResponse.success(result, "查询成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }
            
        } catch (DateTimeParseException e) {
            return ApiResponse.error("日期格式错误，请使用 yyyy-MM-dd 格式");
        } catch (Exception e) {
            return ApiResponse.error("查询历史统计失败: " + e.getMessage());
        }
    }
}

// 2. 在 StatisticsQueryApplicationService 中使用 HistoricalStatsQuery
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private HistoricalStatisticsRepository historicalStatisticsRepository;

    public HistoricalStatsResult queryHistoricalStats(HistoricalStatsQuery query) {
        // 业务逻辑：根据query的日期范围和粒度从数据库或缓存中获取历史统计数据
        // 假设这里调用一个领域服务或仓储来获取数据
        List<DailyStats> rawData = historicalStatisticsRepository.findHistoricalData(
            query.getStartDate(), query.getEndDate(), query.getGranularity()
        );

        // 将原始数据转换为 HistoricalStatsResult DTO
        HistoricalStatsResult result = new HistoricalStatsResult();
        result.setSuccess(true);
        result.setMessage("查询成功");
        result.setGranularity(query.getGranularity());
        result.setData(rawData.stream().map(d -> new HistoricalStatsResult.StatsEntry(d.getDate(), d.getVisits(), d.getUsers())).collect(Collectors.toList()));
        return result;
    }
}

// 3. 测试示例
@SpringBootTest
class HistoricalStatsQueryTest {
    @Test
    void testSettersAndGetters() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 31);
        String granularity = "monthly";
        HistoricalStatsQuery query = new HistoricalStatsQuery();
        query.setStartDate(startDate);
        query.setEndDate(endDate);
        query.setGranularity(granularity);

        assertThat(query.getStartDate()).isEqualTo(startDate);
        assertThat(query.getEndDate()).isEqualTo(endDate);
        assertThat(query.getGranularity()).isEqualTo(granularity);
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `HistoricalStatsQuery` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 尽管当前DTO有Setter方法，但对于查询参数DTO，如果可能，最好设计为不可变对象（所有字段 `final`，只提供全参构造函数），以增强线程安全性和数据一致性。
*   **日期处理**: 使用 `java.time.LocalDate` 是处理日期的最佳实践，避免了 `java.util.Date` 的时区问题。
*   **参数校验**: 尽管DTO本身没有JSR-303注解，但在Controller或Application Service层接收到此DTO后，仍需对 `startDate` 和 `endDate` 的逻辑关系以及 `granularity` 的有效性进行业务校验。
*   **可扩展性**: 如果未来历史统计需要更多查询条件（如按用户角色、地域等），可以在此DTO中添加相应属性。