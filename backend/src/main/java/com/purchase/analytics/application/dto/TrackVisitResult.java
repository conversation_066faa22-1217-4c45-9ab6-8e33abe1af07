package com.purchase.analytics.application.dto;

/**
 * 跟踪访问结果DTO
 */
public class TrackVisitResult {
    
    private boolean success;
    private String visitId;
    private boolean uniqueVisit;
    private String message;
    
    public TrackVisitResult() {}
    
    public TrackVisitResult(boolean success, String visitId, boolean uniqueVisit, String message) {
        this.success = success;
        this.visitId = visitId;
        this.uniqueVisit = uniqueVisit;
        this.message = message;
    }
    
    public static TrackVisitResult success(String visitId, boolean uniqueVisit) {
        return new TrackVisitResult(true, visitId, uniqueVisit, "访问记录成功");
    }
    
    public static TrackVisitResult failure(String message) {
        return new TrackVisitResult(false, null, false, message);
    }
    
    // Getters and Setters
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getVisitId() { return visitId; }
    public void setVisitId(String visitId) { this.visitId = visitId; }
    
    public boolean isUniqueVisit() { return uniqueVisit; }
    public void setUniqueVisit(boolean uniqueVisit) { this.uniqueVisit = uniqueVisit; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
}
