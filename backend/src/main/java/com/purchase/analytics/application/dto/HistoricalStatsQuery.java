package com.purchase.analytics.application.dto;

import java.time.LocalDate;

/**
 * 历史统计查询DTO
 */
public class HistoricalStatsQuery {
    
    private LocalDate startDate;
    private LocalDate endDate;
    private String granularity; // daily, weekly, monthly
    
    public LocalDate getStartDate() { return startDate; }
    public void setStartDate(LocalDate startDate) { this.startDate = startDate; }
    
    public LocalDate getEndDate() { return endDate; }
    public void setEndDate(LocalDate endDate) { this.endDate = endDate; }
    
    public String getGranularity() { return granularity; }
    public void setGranularity(String granularity) { this.granularity = granularity; }
}
