# RealTimeStatsQuery.md

## 1. 文件概述

`RealTimeStatsQuery` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.dto` 包中。它用于封装查询实时统计数据时的请求参数。这个DTO设计简洁，主要包含一个日期字段，用于指定查询哪一天的实时数据。它的核心作用是作为应用服务层接收前端或上层服务请求的标准化输入，确保查询参数的类型安全和结构清晰。

## 2. 核心功能

*   **参数封装**: 封装了查询实时统计数据所需的日期参数。
*   **类型安全**: 使用 `LocalDate` 类型确保日期数据的正确性。
*   **简洁性**: 设计非常轻量级，只包含必要的查询字段，避免了不必要的复杂性。

## 3. 属性说明

- **`date` (LocalDate)**: 查询实时统计数据的目标日期。通常在Controller中会默认设置为当前日期。

## 4. 业务规则

*   **日期范围**: 实时统计通常针对当前日期。如果需要查询历史某天的实时数据，此字段可以指定。在Controller或Application Service层接收到此DTO后，应进行此项校验。
*   **数据粒度**: 实时统计通常是针对当前时间点或非常短的时间窗口（如过去5分钟、1小时）的数据，`LocalDate` 字段用于指定是哪一天的数据。

## 5. 使用示例

```java
// 1. 在 Controller 中构建 RealTimeStatsQuery
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/real-time-stats")
    public ApiResponse<RealTimeStatsResult> getRealTimeStats(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        try {
            RealTimeStatsQuery query = new RealTimeStatsQuery();
            if (date != null) {
                query.setDate(date);
            } else {
                query.setDate(LocalDate.now()); // 默认查询今天
            }
            
            RealTimeStatsResult result = statisticsQueryService.queryRealTimeStats(query);
            
            if (result.isSuccess()) {
                return ApiResponse.success(result, "查询成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }
            
        } catch (Exception e) {
            return ApiResponse.error("查询实时统计失败: " + e.getMessage());
        }
    }
}

// 2. 在 StatisticsQueryApplicationService 中使用 RealTimeStatsQuery
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private RealtimeStatisticsRepository realtimeStatisticsRepository;

    public RealTimeStatsResult queryRealTimeStats(RealTimeStatsQuery query) {
        // 业务逻辑：根据query.getDate()从实时数据源（如Redis、内存）获取统计数据
        // 假设这里调用一个领域服务或仓储来获取数据
        long currentOnlineUsers = realtimeStatisticsRepository.countOnlineUsers(query.getDate());
        long currentActiveSessions = realtimeStatisticsRepository.countActiveSessions(query.getDate());
        
        // 模拟数据
        RealTimeStatsResult result = new RealTimeStatsResult();
        result.setSuccess(true);
        result.setMessage("查询成功");
        result.setOnlineUsers(currentOnlineUsers);
        result.setActiveSessions(currentActiveSessions);
        result.setPageViewsLastMinute(100);
        result.setNewUsersToday(50);
        result.setLastUpdated(LocalDateTime.now());
        return result;
    }
}

// 3. 测试示例
@SpringBootTest
class RealTimeStatsQueryTest {
    @Test
    void testSettersAndGetters() {
        LocalDate specificDate = LocalDate.of(2023, 1, 15);
        RealTimeStatsQuery query = new RealTimeStatsQuery();
        query.setDate(specificDate);
        assertThat(query.getDate()).isEqualTo(specificDate);
    }

    @Test
    void testDefaultDateIsToday() {
        RealTimeStatsQuery query = new RealTimeStatsQuery();
        assertThat(query.getDate()).isEqualTo(LocalDate.now());
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `RealTimeStatsQuery` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 尽管当前DTO有Setter方法，但对于查询参数DTO，如果可能，最好设计为不可变对象（所有字段 `final`，只提供全参构造函数），以增强线程安全性和数据一致性。
*   **日期处理**: 使用 `java.time.LocalDate` 是处理日期的最佳实践，避免了 `java.util.Date` 的时区问题。
*   **参数校验**: 尽管DTO本身没有JSR-303注解，但在Controller或Application Service层接收到此DTO后，仍需对 `date` 字段的有效性进行业务校验。
*   **可扩展性**: 如果未来实时统计需要更多查询条件（如按用户角色、地域等），可以在此DTO中添加相应属性。