# PageRankingResult.md

## 1. 文件概述

`PageRankingResult` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.result` 包中。它用于封装页面排行查询的返回结果。这个DTO设计旨在提供一个清晰、结构化的页面访问排名视图，包含了按访问量排序的页面信息列表。每个页面信息 (`PageInfo`) 不仅包含URL和标题，还包括访问量、独立访客数、平均停留时间、趋势、跳出率和转化率等详细指标。它是一个不可变对象，确保了数据的一致性和线程安全。

## 2. 核心功能

*   **页面排名列表**: 提供了 `pages` 列表，其中包含了按访问量或其他指标排序的页面详细信息。
*   **多维度页面指标**: 每个 `PageInfo` 对象包含了丰富的页面级指标，如访问量、独立访客、平均停留时间、趋势、跳出率和转化率，为页面优化提供了数据支持。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。
*   **值语义**: 重写了 `equals` 和 `hashCode` 方法，确保了当所有属性值相等时，两个 `PageRankingResult` 对象被认为是相等的。
*   **嵌套DTO**: 内部定义了 `PageInfo` 静态类，用于表示单个页面的详细统计数据，使得数据结构更加清晰。

## 3. 属性说明

- **`pages` (List<PageInfo>)**: 页面信息列表。每个 `PageInfo` 对象包含：
    - **`url` (String)**: 页面的URL。
    - **`title` (String)**: 页面的标题。
    - **`category` (String)**: 页面的分类。
    - **`visits` (int)**: 该页面的总访问量。
    - **`uniqueVisitors` (int)**: 该页面的独立访客数。
    - **`avgStayTime` (int)**: 该页面的平均停留时间（秒）。
    - **`trend` (double)**: 该页面的访问趋势（例如，与上一周期相比的增长率）。
    - **`bounceRate` (double)**: 该页面的跳出率。
    - **`conversionRate` (double)**: 该页面的转化率。

## 4. 业务规则

*   **数据来源**: `PageRankingResult` 中的数据通常由 `StatisticsQueryApplicationService` 协调领域服务或数据仓储计算和聚合而来。
*   **数据时效性**: 页面排行数据可能基于预计算或定时任务生成，其时效性取决于数据更新频率。
*   **指标计算**: `trend`, `bounceRate`, `conversionRate` 等指标的计算逻辑需要明确定义，并确保其准确性。
*   **排序逻辑**: `pages` 列表应按照查询请求中指定的排序规则进行排序（例如，按 `visits` 降序）。

## 5. 使用示例

```java
// 1. 在 StatisticsQueryApplicationService 中构建 PageRankingResult
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private PageRankingRepository pageRankingRepository;

    public PageRankingResult queryPageRanking(PageRankingQuery query) {
        // 业务逻辑：根据query的日期范围和limit从数据库或缓存中获取页面排行数据
        List<PageStats> rawData = pageRankingRepository.findTopPages(
            query.getStartDate(), query.getEndDate(), query.getLimit()
        );

        // 将原始数据转换为 PageRankingResult.PageInfo 列表
        List<PageRankingResult.PageInfo> pageInfos = rawData.stream()
            .map(d -> new PageRankingResult.PageInfo(
                d.getUrl(), d.getTitle(), d.getCategory(), d.getVisits(), 
                d.getUniqueVisitors(), d.getAvgStayTime(), d.getTrend(), 
                d.getBounceRate(), d.getConversionRate()
            ))
            .collect(Collectors.toList());

        return new PageRankingResult(pageInfos);
    }
}

// 2. 在 Controller 中返回 PageRankingResult
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/page-ranking")
    public ApiResponse<PageRankingResult> getPageRanking(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "10") String limit) {

        try {
            // ... 解析参数，构建 PageRankingQuery ...
            PageRankingQuery query = new PageRankingQuery(LocalDate.now().minusDays(30), LocalDate.now(), Integer.parseInt(limit));

            PageRankingResult result = statisticsQueryService.queryPageRanking(query);

            return ApiResponse.success(result);

        } catch (Exception e) {
            return ApiResponse.error("查询页面排行失败: " + e.getMessage());
        }
    }
}

// 3. 前端 (React + Table) 接收并展示页面排行数据
/*
import React, { useEffect, useState } from 'react';
import axios from 'axios';

function PageRankingTable({ startDate, endDate, limit }) {
  const [pageData, setPageData] = useState([]);

  useEffect(() => {
    axios.get('/api/v1/analytics/page-ranking', {
      params: { startDate, endDate, limit }
    })
      .then(response => {
        if (response.data.success) {
          setPageData(response.data.data.pages);
        } else {
          console.error('获取页面排行失败:', response.data.message);
        }
      })
      .catch(error => {
        console.error('请求失败:', error);
      });
  }, [startDate, endDate, limit]);

  return (
    <table>
      <thead>
        <tr>
          <th>URL</th>
          <th>标题</th>
          <th>访问量</th>
          <th>独立访客</th>
          <th>平均停留时间</th>
          <th>跳出率</th>
          <th>转化率</th>
        </tr>
      </thead>
      <tbody>
        {pageData.map((page, index) => (
          <tr key={index}>
            <td>{page.url}</td>
            <td>{page.title}</td>
            <td>{page.visits}</td>
            <td>{page.uniqueVisitors}</td>
            <td>{page.avgStayTime}s</td>
            <td>{(page.bounceRate * 100).toFixed(2)}%</td>
            <td>{(page.conversionRate * 100).toFixed(2)}%</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
*/

// 4. 测试示例
@SpringBootTest
class PageRankingResultTest {
    @Test
    void testConstructorAndGetters() {
        PageRankingResult.PageInfo page1 = new PageRankingResult.PageInfo(
            "/home", "首页", "General", 1000, 500, 60, 0.1, 0.3, 0.05
        );
        PageRankingResult.PageInfo page2 = new PageRankingResult.PageInfo(
            "/products", "产品列表", "Product", 800, 400, 90, 0.05, 0.2, 0.03
        );
        List<PageRankingResult.PageInfo> pages = List.of(page1, page2);

        PageRankingResult result = new PageRankingResult(pages);

        assertThat(result.getPages()).hasSize(2);
        assertThat(result.getPages().get(0).getUrl()).isEqualTo("/home");
        assertThat(result.getPages().get(0).getVisits()).isEqualTo(1000);
    }

    @Test
    void testEqualsAndHashCode() {
        PageRankingResult.PageInfo page1 = new PageRankingResult.PageInfo("a", "b", "c", 1, 2, 3, 0.1, 0.2, 0.3);
        PageRankingResult.PageInfo page2 = new PageRankingResult.PageInfo("a", "b", "c", 1, 2, 3, 0.1, 0.2, 0.3);
        PageRankingResult result1 = new PageRankingResult(List.of(page1));
        PageRankingResult result2 = new PageRankingResult(List.of(page2));

        assertThat(result1).isEqualTo(result2);
        assertThat(result1.hashCode()).isEqualTo(result2.hashCode());
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `PageRankingResult` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。这使得对象在多线程环境中是安全的。
*   **数据类型**: 确保数值类型（`int`, `double`）能够准确表示统计数据。百分比字段（`trend`, `bounceRate`, `conversionRate`）通常以小数形式表示，前端需要乘以100并格式化为百分比。
*   **嵌套DTO**: 内部的 `PageInfo` 也是不可变的值对象，进一步保证了数据结构的清晰和一致性。
*   **前端适配**: DTO的结构应尽可能与前端图表和表格组件的期望数据格式匹配，减少前端的数据转换工作。
*   **性能**: 页面排行数据通常是聚合计算的结果，其生成可能涉及复杂的查询。在服务层构建此DTO时，应特别关注性能优化，例如使用缓存、预计算等。