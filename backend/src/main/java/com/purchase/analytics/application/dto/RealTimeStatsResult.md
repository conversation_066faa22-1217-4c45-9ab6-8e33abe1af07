# RealTimeStatsResult.md

## 1. 文件概述

`RealTimeStatsResult` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.dto` 包中。它用于封装实时统计查询的返回结果。这个DTO设计旨在提供一个清晰、全面且易于前端展示的实时数据视图，包含了今日总访问量、当前小时访问量、独立访客数、注册用户访问量、匿名访问量以及数据最后更新时间等关键指标。它通过静态工厂方法 `success` 和 `failure` 提供了便捷的创建方式，确保了返回结果的清晰性和一致性。

## 2. 核心功能

*   **数据聚合**: 聚合了多种与用户行为和网站性能相关的实时统计指标。
*   **状态指示**: 包含 `success` 字段和 `message` 字段，明确指示查询操作是否成功以及相关的提示信息。
*   **时间戳**: `lastUpdated` 字段记录了数据生成的时间，有助于前端判断数据的时效性。
*   **工厂方法**: 通过 `success()` 和 `failure()` 静态方法，简化了DTO的创建过程，并确保了返回结果的规范性。
*   **用户类型细分**: 区分了注册用户访问量和匿名访问量，有助于更细致的用户行为分析。

## 3. 属性说明

- **`success` (boolean)**: 表示查询操作是否成功。
- **`message` (String)**: 查询结果的描述信息，成功时通常为“查询成功”，失败时为错误信息。
- **`todayVisits` (Integer)**: 今日总访问量。
- **`currentHourVisits` (Integer)**: 当前小时的访问量。
- **`uniqueVisitors` (Integer)**: 独立访客数量。
- **`registeredUserVisits` (Integer)**: 注册用户的访问量。
- **`anonymousVisits` (Integer)**: 匿名用户的访问量。
- **`peakHour` (Integer)**: 今日访问量最高的时段（小时数，例如14表示下午2点）。
- **`lastUpdated` (LocalDateTime)**: 数据最后更新的时间戳。

## 4. 业务规则

*   **数据来源**: `RealTimeStatsResult` 中的数据通常由 `StatisticsQueryApplicationService` 协调实时数据源（如Redis、内存数据库）计算和聚合而来。
*   **数据时效性**: 实时统计数据应尽可能接近实时，通常是每分钟或每秒更新一次。
*   **指标定义**: 各个统计指标的计算逻辑需要明确定义，例如“独立访客”是基于Cookie还是用户ID。

## 5. 使用示例

```java
// 1. 在 StatisticsQueryApplicationService 中构建 RealTimeStatsResult
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private RealtimeStatisticsRepository realtimeStatisticsRepository;

    public RealTimeStatsResult queryRealTimeStats(RealTimeStatsQuery query) {
        try {
            // 模拟获取实时数据
            Integer todayVisits = realtimeStatisticsRepository.getTodayTotalVisits(query.getDate());
            Integer currentHourVisits = realtimeStatisticsRepository.getCurrentHourVisits(query.getDate(), LocalDateTime.now().getHour());
            Integer uniqueVisitors = realtimeStatisticsRepository.getUniqueVisitors(query.getDate());
            Integer registeredUserVisits = realtimeStatisticsRepository.getRegisteredUserVisits(query.getDate());
            Integer anonymousVisits = realtimeStatisticsRepository.getAnonymousVisits(query.getDate());
            Integer peakHour = realtimeStatisticsRepository.getPeakHour(query.getDate());

            return RealTimeStatsResult.success(
                todayVisits, currentHourVisits, uniqueVisitors,
                registeredUserVisits, anonymousVisits
            );
        } catch (Exception e) {
            return RealTimeStatsResult.failure("查询实时统计失败: " + e.getMessage());
        }
    }
}

// 2. 在 Controller 中返回 RealTimeStatsResult
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/real-time-stats")
    public ApiResponse<RealTimeStatsResult> getRealTimeStats(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        
        RealTimeStatsQuery query = new RealTimeStatsQuery();
        if (date != null) {
            query.setDate(date);
        } else {
            query.setDate(LocalDate.now()); // 默认查询今天
        }

        RealTimeStatsResult result = statisticsQueryService.queryRealTimeStats(query);
        
        if (result.isSuccess()) {
            return ApiResponse.success(result, result.getMessage());
        } else {
            return ApiResponse.error(result.getMessage());
        }
    }
}

// 3. 前端 (React) 接收并展示实时统计数据
/*
import React, { useEffect, useState } from 'react';
import axios from 'axios';

function RealtimeDashboard() {
  const [stats, setStats] = useState(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await axios.get('/api/v1/analytics/real-time-stats');
        if (response.data.success) {
          setStats(response.data.data);
        } else {
          console.error('获取实时统计失败:', response.data.message);
        }
      } catch (error) {
        console.error('请求失败:', error);
      }
    };

    fetchStats();
    const intervalId = setInterval(fetchStats, 5000); // 每5秒刷新一次

    return () => clearInterval(intervalId);
  }, []);

  if (!stats) {
    return <div>加载中...</div>;
  }

  return (
    <div>
      <h1>实时统计</h1>
      <p>今日总访问: {stats.todayVisits}</p>
      <p>当前小时访问: {stats.currentHourVisits}</p>
      <p>独立访客: {stats.uniqueVisitors}</p>
      <p>注册用户访问: {stats.registeredUserVisits}</p>
      <p>匿名访问: {stats.anonymousVisits}</p>
      <p>高峰时段: {stats.peakHour}点</p>
      <p>最后更新: {new Date(stats.lastUpdated).toLocaleTimeString()}</p>
    </div>
  );
}
*/

// 4. 测试示例
@SpringBootTest
class RealTimeStatsResultTest {
    @Test
    void testSuccessFactoryMethod() {
        RealTimeStatsResult result = RealTimeStatsResult.success(
            1000, 50, 200, 150, 50
        );

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("查询成功");
        assertThat(result.getTodayVisits()).isEqualTo(1000);
        assertThat(result.getCurrentHourVisits()).isEqualTo(50);
        assertThat(result.getUniqueVisitors()).isEqualTo(200);
        assertThat(result.getRegisteredUserVisits()).isEqualTo(150);
        assertThat(result.getAnonymousVisits()).isEqualTo(50);
        assertThat(result.getLastUpdated()).isNotNull();
    }

    @Test
    void testFailureFactoryMethod() {
        RealTimeStatsResult result = RealTimeStatsResult.failure("数据源连接失败");

        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).isEqualTo("数据源连接失败");
        assertThat(result.getTodayVisits()).isNull(); // 失败时数据应为null
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `RealTimeStatsResult` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 尽管当前DTO有Setter方法，但对于结果DTO，如果可能，最好设计为不可变对象（所有字段 `final`，只提供全参构造函数），以增强线程安全性和数据一致性。
*   **数据类型**: 确保数值类型（`Integer`）能够准确表示统计数据。使用 `Integer` 而不是 `int` 可以更好地处理 `null` 值，表示数据缺失或未计算。
*   **时间戳**: `lastUpdated` 字段对于前端判断数据新鲜度非常重要，应在数据生成时准确设置。
*   **可扩展性**: 如果未来实时统计需要展示更多指标，可以在此DTO中添加相应属性。
*   **前端适配**: DTO的结构应尽可能与前端组件的期望数据格式匹配，减少前端的数据转换工作。
*   **性能**: 实时统计数据通常需要从高性能数据存储（如Redis）中快速获取。在服务层构建此DTO时，应特别关注查询和聚合的效率。