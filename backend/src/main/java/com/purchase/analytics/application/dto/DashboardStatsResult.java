package com.purchase.analytics.application.dto;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Dashboard统计结果
 */
public class DashboardStatsResult {
    
    private boolean success;
    private String message;
    private int realtimeOnline;
    private int pageViews;
    private double avgStayTime;
    private double pageViewGrowthRate;
    private List<Integer> onlineHistory;
    private List<Integer> visitHistory;
    private LocalDateTime lastUpdated;
    
    public DashboardStatsResult() {}
    
    public static DashboardStatsResult success(int realtimeOnline, int pageViews, double avgStayTime, 
                                             double pageViewGrowthRate, List<Integer> onlineHistory, 
                                             List<Integer> visitHistory) {
        DashboardStatsResult result = new DashboardStatsResult();
        result.success = true;
        result.message = "查询成功";
        result.realtimeOnline = realtimeOnline;
        result.pageViews = pageViews;
        result.avgStayTime = avgStayTime;
        result.pageViewGrowthRate = pageViewGrowthRate;
        result.onlineHistory = onlineHistory;
        result.visitHistory = visitHistory;
        result.lastUpdated = LocalDateTime.now();
        return result;
    }
    
    public static DashboardStatsResult failure(String message) {
        DashboardStatsResult result = new DashboardStatsResult();
        result.success = false;
        result.message = message;
        return result;
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public int getRealtimeOnline() {
        return realtimeOnline;
    }
    
    public void setRealtimeOnline(int realtimeOnline) {
        this.realtimeOnline = realtimeOnline;
    }
    
    public int getPageViews() {
        return pageViews;
    }
    
    public void setPageViews(int pageViews) {
        this.pageViews = pageViews;
    }
    
    public double getAvgStayTime() {
        return avgStayTime;
    }
    
    public void setAvgStayTime(double avgStayTime) {
        this.avgStayTime = avgStayTime;
    }
    
    public double getPageViewGrowthRate() {
        return pageViewGrowthRate;
    }
    
    public void setPageViewGrowthRate(double pageViewGrowthRate) {
        this.pageViewGrowthRate = pageViewGrowthRate;
    }
    
    public List<Integer> getOnlineHistory() {
        return onlineHistory;
    }
    
    public void setOnlineHistory(List<Integer> onlineHistory) {
        this.onlineHistory = onlineHistory;
    }
    
    public List<Integer> getVisitHistory() {
        return visitHistory;
    }
    
    public void setVisitHistory(List<Integer> visitHistory) {
        this.visitHistory = visitHistory;
    }
    
    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }
    
    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }
}
