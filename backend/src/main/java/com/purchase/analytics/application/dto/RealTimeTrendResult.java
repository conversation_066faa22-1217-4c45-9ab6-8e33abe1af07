package com.purchase.analytics.application.dto;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 实时趋势结果
 */
public class RealTimeTrendResult {
    
    private boolean success;
    private String message;
    private List<TrendDataPoint> data;
    private LocalDateTime lastUpdated;
    
    public RealTimeTrendResult() {}
    
    public static RealTimeTrendResult success(List<TrendDataPoint> data) {
        RealTimeTrendResult result = new RealTimeTrendResult();
        result.success = true;
        result.message = "查询成功";
        result.data = data;
        result.lastUpdated = LocalDateTime.now();
        return result;
    }
    
    public static RealTimeTrendResult failure(String message) {
        RealTimeTrendResult result = new RealTimeTrendResult();
        result.success = false;
        result.message = message;
        return result;
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public List<TrendDataPoint> getData() {
        return data;
    }
    
    public void setData(List<TrendDataPoint> data) {
        this.data = data;
    }
    
    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }
    
    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }
    
    /**
     * 趋势数据点
     */
    public static class TrendDataPoint {
        private String time;
        private long timestamp;
        private long visits;
        private long uniqueVisitors;
        private long pageViews;
        
        public TrendDataPoint() {}
        
        public TrendDataPoint(String time, long timestamp, long visits, long uniqueVisitors, long pageViews) {
            this.time = time;
            this.timestamp = timestamp;
            this.visits = visits;
            this.uniqueVisitors = uniqueVisitors;
            this.pageViews = pageViews;
        }
        
        public String getTime() {
            return time;
        }
        
        public void setTime(String time) {
            this.time = time;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
        
        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
        
        public long getVisits() {
            return visits;
        }
        
        public void setVisits(long visits) {
            this.visits = visits;
        }
        
        public long getUniqueVisitors() {
            return uniqueVisitors;
        }
        
        public void setUniqueVisitors(long uniqueVisitors) {
            this.uniqueVisitors = uniqueVisitors;
        }
        
        public long getPageViews() {
            return pageViews;
        }
        
        public void setPageViews(long pageViews) {
            this.pageViews = pageViews;
        }
    }
}
