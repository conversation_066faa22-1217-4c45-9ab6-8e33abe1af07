# RealTimeTrendResult.md

## 1. 文件概述

`RealTimeTrendResult` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.dto` 包中。它用于封装实时趋势查询的返回结果。这个DTO设计旨在提供一个清晰、结构化的时间序列数据视图，通常用于绘制实时访问趋势图表。它包含一个 `TrendDataPoint` 列表，每个元素代表一个时间点上的访问统计数据。通过静态工厂方法 `success` 和 `failure` 提供了便捷的创建方式，确保了返回结果的清晰性和一致性。

## 2. 核心功能

*   **数据封装**: 封装了实时趋势数据，主要是一个 `TrendDataPoint` 列表，每个 `TrendDataPoint` 包含时间、时间戳、访问量、独立访客数和页面浏览量。
*   **状态指示**: 包含 `success` 字段和 `message` 字段，明确指示查询操作是否成功以及相关的提示信息。
*   **时间戳**: `lastUpdated` 字段记录了数据生成的时间，有助于前端判断数据的时效性。
*   **工厂方法**: 通过 `success()` 和 `failure()` 静态方法，简化了DTO的创建过程，并确保了返回结果的规范性。
*   **嵌套DTO**: 内部定义了 `TrendDataPoint` 静态类，用于表示单个时间点上的统计数据，使得数据结构更加清晰。

## 3. 属性说明

- **`success` (boolean)**: 表示查询操作是否成功。
- **`message` (String)**: 查询结果的描述信息，成功时通常为“查询成功”，失败时为错误信息。
- **`data` (List<TrendDataPoint>)**: 实时趋势数据点列表。每个 `TrendDataPoint` 对象包含：
    - **`time` (String)**: 时间点的字符串表示（例如：`10:00`, `2024-07-27`）。
    - **`timestamp` (long)**: 时间点的时间戳（毫秒）。
    - **`visits` (long)**: 该时间点的访问量。
    - **`uniqueVisitors` (long)**: 该时间点的独立访客数。
    - **`pageViews` (long)**: 该时间点的页面浏览量。
- **`lastUpdated` (LocalDateTime)**: 数据最后更新的时间戳。

## 4. 业务规则

*   **数据来源**: `RealTimeTrendResult` 中的数据通常由 `StatisticsQueryApplicationService` 协调实时数据源（如Redis、内存数据库）计算和聚合而来。
*   **数据时效性**: 实时趋势数据应尽可能接近实时，通常是每分钟或每秒更新一次。
*   **粒度一致性**: `TrendDataPoint` 中的时间粒度（分钟、小时、天）应与查询请求中的 `granularity` 参数一致。
*   **时间戳**: `timestamp` 字段对于前端绘制时间序列图表非常重要，应确保其准确性。

## 5. 使用示例

```java
// 1. 在 StatisticsQueryApplicationService 中构建 RealTimeTrendResult
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private RealtimeTrendRepository realtimeTrendRepository;

    public RealTimeTrendResult queryRealTimeTrend(RealTimeTrendQuery query) {
        try {
            // 业务逻辑：根据query的时间粒度、开始时间和结束时间从实时数据源获取趋势数据
            List<TrendData> rawData = realtimeTrendRepository.findRealtimeTrendData(
                query.getGranularity(), query.getStartTime(), query.getEndTime()
            );

            // 将原始数据转换为 RealTimeTrendResult.TrendDataPoint 列表
            List<RealTimeTrendResult.TrendDataPoint> formattedData = rawData.stream()
                .map(d -> new RealTimeTrendResult.TrendDataPoint(
                    d.getTimeLabel(), d.getTimestamp(), d.getVisits(), d.getUniqueVisitors(), d.getPageViews()
                ))
                .collect(Collectors.toList());

            return RealTimeTrendResult.success(formattedData);
        } catch (Exception e) {
            return RealTimeTrendResult.failure("查询实时趋势失败: " + e.getMessage());
        }
    }
}

// 2. 在 Controller 中返回 RealTimeTrendResult
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/real-time-trend")
    public ApiResponse<RealTimeTrendResult> getRealTimeTrend(
            @RequestParam(defaultValue = "hour") String granularity,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        
        RealTimeTrendQuery query = new RealTimeTrendQuery();
        query.setGranularity(granularity);
        // ... 设置 startTime 和 endTime ...

        RealTimeTrendResult result = statisticsQueryService.queryRealTimeTrend(query);
        
        if (result.isSuccess()) {
            return ApiResponse.success(result, result.getMessage());
        } else {
            return ApiResponse.error(result.getMessage());
        }
    }
}

// 3. 前端 (React + ECharts) 接收并展示实时趋势数据
/*
import React, { useEffect, useState } from 'react';
import axios from 'axios';
import ReactECharts from 'echarts-for-react';

function RealtimeTrendChart({ granularity, startTime, endTime }) {
  const [chartOption, setChartOption] = useState({});

  useEffect(() => {
    axios.get('/api/v1/analytics/real-time-trend', {
      params: { granularity, startTime, endTime }
    })
      .then(response => {
        if (response.data.success) {
          const data = response.data.data.data; // 获取 TrendDataPoint 列表
          const times = data.map(item => item.time);
          const visits = data.map(item => item.visits);
          const uniqueVisitors = data.map(item => item.uniqueVisitors);

          setChartOption({
            xAxis: { type: 'category', data: times },
            yAxis: { type: 'value' },
            series: [
              { name: '访问量', type: 'line', data: visits },
              { name: '独立访客', type: 'line', data: uniqueVisitors }
            ]
            // ... 其他图表配置
          });
        } else {
          console.error('获取实时趋势失败:', response.data.message);
        }
      })
      .catch(error => {
        console.error('请求失败:', error);
      });
  }, [granularity, startTime, endTime]);

  return (
    <ReactECharts option={chartOption} style={{ height: '400px', width: '100%' }} />
  );
}
*/

// 4. 测试示例
@SpringBootTest
class RealTimeTrendResultTest {
    @Test
    void testSuccessFactoryMethod() {
        List<RealTimeTrendResult.TrendDataPoint> data = List.of(
            new RealTimeTrendResult.TrendDataPoint("10:00", 1672531200000L, 100, 50, 200),
            new RealTimeTrendResult.TrendDataPoint("10:01", 1672531260000L, 120, 60, 250)
        );
        RealTimeTrendResult result = RealTimeTrendResult.success(data);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("查询成功");
        assertThat(result.getData()).hasSize(2);
        assertThat(result.getData().get(0).getTime()).isEqualTo("10:00");
        assertThat(result.getData().get(0).getVisits()).isEqualTo(100);
        assertThat(result.getLastUpdated()).isNotNull();
    }

    @Test
    void testFailureFactoryMethod() {
        RealTimeTrendResult result = RealTimeTrendResult.failure("数据源连接失败");

        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).isEqualTo("数据源连接失败");
        assertThat(result.getData()).isNull(); // 失败时数据应为null
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `RealTimeTrendResult` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 尽管当前DTO有Setter方法，但对于结果DTO，如果可能，最好设计为不可变对象（所有字段 `final`，只提供全参构造函数），以增强线程安全性和数据一致性。
*   **数据类型**: 确保数值类型（`long`）能够准确表示统计数据。`timestamp` 字段使用 `long` 类型，通常表示Unix时间戳（毫秒）。
*   **时间戳**: `lastUpdated` 字段对于前端判断数据新鲜度非常重要，应在数据生成时准确设置。
*   **可扩展性**: 如果未来实时趋势需要展示更多指标，可以在 `TrendDataPoint` 中添加相应属性。
*   **前端适配**: DTO的结构应尽可能与前端图表组件（如ECharts）的期望数据格式匹配，减少前端的数据转换工作。
*   **性能**: 实时趋势数据通常需要从高性能数据存储（如Redis）中快速获取。在服务层构建此DTO时，应特别关注查询和聚合的效率。