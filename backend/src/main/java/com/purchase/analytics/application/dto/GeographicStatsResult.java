package com.purchase.analytics.application.dto;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 地理统计结果
 */
public class GeographicStatsResult {
    
    private boolean success;
    private String message;
    private List<GeographicData> data;
    private LocalDateTime lastUpdated;
    
    public GeographicStatsResult() {}
    
    public static GeographicStatsResult success(List<GeographicData> data) {
        GeographicStatsResult result = new GeographicStatsResult();
        result.success = true;
        result.message = "查询成功";
        result.data = data;
        result.lastUpdated = LocalDateTime.now();
        return result;
    }
    
    public static GeographicStatsResult failure(String message) {
        GeographicStatsResult result = new GeographicStatsResult();
        result.success = false;
        result.message = message;
        return result;
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public List<GeographicData> getData() {
        return data;
    }
    
    public void setData(List<GeographicData> data) {
        this.data = data;
    }
    
    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }
    
    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }
    
    /**
     * 地理数据项
     */
    public static class GeographicData {
        private String name;
        private long value;
        
        public GeographicData() {}
        
        public GeographicData(String name, long value) {
            this.name = name;
            this.value = value;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public long getValue() {
            return value;
        }
        
        public void setValue(long value) {
            this.value = value;
        }
    }
}
