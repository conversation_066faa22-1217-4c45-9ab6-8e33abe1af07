# SourceAnalysisResult.md

## 1. 文件概述

`SourceAnalysisResult` 是分析模块中的一个数据传输对象（DTO），位于 `com.purchase.analytics.application.result` 包中。它用于封装来源分析查询的返回结果。这个DTO设计旨在提供一个清晰、结构化的用户访问来源视图，包含了按搜索引擎、直接访问、引荐网站和社交媒体划分的详细数据。它是一个不可变对象，确保了数据的一致性和线程安全。

## 2. 核心功能

*   **多渠道来源统计**: 提供了 `searchEngines`（搜索引擎）、`directVisits`（直接访问）、`referralSites`（引荐网站）和 `socialMedia`（社交媒体）四个主要来源的统计数据。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。
*   **值语义**: 重写了 `equals` 和 `hashCode` 方法，确保了当所有属性值相等时，两个 `SourceAnalysisResult` 对象被认为是相等的。
*   **嵌套DTO**: 内部定义了 `SearchEngineSource`, `ReferralSource`, `SocialMediaSource` 三个静态类，用于表示不同来源的详细统计数据，使得数据结构更加清晰。

## 3. 属性说明

- **`searchEngines` (List<SearchEngineSource>)**: 搜索引擎来源统计信息列表。每个 `SearchEngineSource` 包含：
    - **`name` (String)**: 搜索引擎名称（例如：`Google`, `Baidu`）。
    - **`visits` (int)**: 该搜索引擎的访问量。
- **`directVisits` (int)**: 直接访问量（用户直接输入URL或通过书签访问）。
- **`referralSites` (List<ReferralSource>)**: 引荐网站来源统计信息列表。每个 `ReferralSource` 包含：
    - **`domain` (String)**: 引荐网站的域名。
    - **`visits` (int)**: 该引荐网站的访问量。
- **`socialMedia` (List<SocialMediaSource>)**: 社交媒体来源统计信息列表。每个 `SocialMediaSource` 包含：
    - **`platform` (String)**: 社交媒体平台名称（例如：`WeChat`, `Facebook`）。
    - **`visits` (int)**: 该社交媒体平台的访问量。

## 4. 业务规则

*   **数据来源**: `SourceAnalysisResult` 中的数据通常由 `StatisticsQueryApplicationService` 协调领域服务或数据仓储计算和聚合而来。
*   **数据时效性**: 来源分析数据可能基于预计算或定时任务生成，其时效性取决于数据更新频率。
*   **分类标准**: 访问来源的分类标准应明确，并与数据采集和分析工具保持一致。

## 5. 使用示例

```java
// 1. 在 StatisticsQueryApplicationService 中构建 SourceAnalysisResult
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private SourceAnalysisRepository sourceAnalysisRepository;

    public SourceAnalysisResult querySourceAnalysis(SourceAnalysisQuery query) {
        // 业务逻辑：根据query的日期范围从数据库或缓存中获取来源分析数据
        List<SourceStats> rawData = sourceAnalysisRepository.findSourceStats(
            query.getStartDate(), query.getEndDate()
        );

        // 假设 rawData 包含了所有来源类型的数据，需要进行分类和聚合
        List<SourceAnalysisResult.SearchEngineSource> searchEngines = new ArrayList<>();
        List<SourceAnalysisResult.ReferralSource> referralSites = new ArrayList<>();
        List<SourceAnalysisResult.SocialMediaSource> socialMedia = new ArrayList<>();
        int directVisits = 0;

        for (SourceStats stats : rawData) {
            if ("DIRECT".equals(stats.getType())) {
                directVisits = stats.getVisits();
            } else if ("SEARCH_ENGINE".equals(stats.getType())) {
                searchEngines.add(new SourceAnalysisResult.SearchEngineSource(stats.getName(), stats.getVisits()));
            } else if ("REFERRAL".equals(stats.getType())) {
                referralSites.add(new SourceAnalysisResult.ReferralSource(stats.getName(), stats.getVisits()));
            } else if ("SOCIAL_MEDIA".equals(stats.getType())) {
                socialMedia.add(new SourceAnalysisResult.SocialMediaSource(stats.getName(), stats.getVisits()));
            }
        }

        return new SourceAnalysisResult(searchEngines, directVisits, referralSites, socialMedia);
    }
}

// 2. 在 Controller 中返回 SourceAnalysisResult
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/source-analysis")
    public ApiResponse<SourceAnalysisResult> getSourceAnalysis(
            @RequestParam String startDate,
            @RequestParam String endDate) {

        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            SourceAnalysisQuery query = new SourceAnalysisQuery(start, end);

            SourceAnalysisResult result = statisticsQueryService.querySourceAnalysis(query);

            return ApiResponse.success(result);

        } catch (DateTimeParseException e) {
            return ApiResponse.error("日期格式错误: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("查询来源分析失败: " + e.getMessage());
        }
    }
}

// 3. 前端 (React + ECharts) 接收并展示来源分析数据
/*
import React, { useEffect, useState } from 'react';
import axios from 'axios';
import ReactECharts from 'echarts-for-react';

function SourceAnalysisChart({ startDate, endDate }) {
  const [sourceData, setSourceData] = useState(null);

  useEffect(() => {
    axios.get('/api/v1/analytics/source-analysis', {
      params: { startDate, endDate }
    })
      .then(response => {
        if (response.data.success) {
          setSourceData(response.data.data);
        } else {
          console.error('获取来源分析失败:', response.data.message);
        }
      })
      .catch(error => {
        console.error('请求失败:', error);
      });
  }, [startDate, endDate]);

  if (!sourceData) {
    return <div>加载中...</div>;
  }

  const pieOption = {
    series: [
      {
        type: 'pie',
        data: [
          ...sourceData.searchEngines.map(s => ({ name: s.name, value: s.visits })),
          { value: sourceData.directVisits, name: '直接访问' },
          ...sourceData.referralSites.map(r => ({ name: r.domain, value: r.visits })),
          ...sourceData.socialMedia.map(s => ({ name: s.platform, value: s.visits }))
        ]
      }
    ]
  };

  return (
    <div>
      <h2>访问来源分布</h2>
      <ReactECharts option={pieOption} style={{ height: '400px' }} />
    </div>
  );
}
*/

// 4. 测试示例
@SpringBootTest
class SourceAnalysisResultTest {
    @Test
    void testConstructorAndGetters() {
        List<SourceAnalysisResult.SearchEngineSource> searchEngines = List.of(
            new SourceAnalysisResult.SearchEngineSource("Google", 1000),
            new SourceAnalysisResult.SearchEngineSource("Baidu", 500)
        );
        List<SourceAnalysisResult.ReferralSource> referralSites = List.of(
            new SourceAnalysisResult.ReferralSource("example.com", 200)
        );
        List<SourceAnalysisResult.SocialMediaSource> socialMedia = List.of(
            new SourceAnalysisResult.SocialMediaSource("WeChat", 300)
        );

        SourceAnalysisResult result = new SourceAnalysisResult(searchEngines, 150, referralSites, socialMedia);

        assertThat(result.getSearchEngines()).hasSize(2);
        assertThat(result.getDirectVisits()).isEqualTo(150);
        assertThat(result.getReferralSites()).hasSize(1);
        assertThat(result.getSocialMedia()).hasSize(1);
    }

    @Test
    void testEqualsAndHashCode() {
        List<SourceAnalysisResult.SearchEngineSource> searchEngines1 = List.of(new SourceAnalysisResult.SearchEngineSource("Google", 100));
        List<SourceAnalysisResult.ReferralSource> referralSites1 = List.of(new SourceAnalysisResult.ReferralSource("site1.com", 50));
        List<SourceAnalysisResult.SocialMediaSource> socialMedia1 = List.of(new SourceAnalysisResult.SocialMediaSource("Weibo", 20));
        SourceAnalysisResult result1 = new SourceAnalysisResult(searchEngines1, 10, referralSites1, socialMedia1);

        List<SourceAnalysisResult.SearchEngineSource> searchEngines2 = List.of(new SourceAnalysisResult.SearchEngineSource("Google", 100));
        List<SourceAnalysisResult.ReferralSource> referralSites2 = List.of(new SourceAnalysisResult.ReferralSource("site1.com", 50));
        List<SourceAnalysisResult.SocialMediaSource> socialMedia2 = List.of(new SourceAnalysisResult.SocialMediaSource("Weibo", 20));
        SourceAnalysisResult result2 = new SourceAnalysisResult(searchEngines2, 10, referralSites2, socialMedia2);

        assertThat(result1).isEqualTo(result2);
        assertThat(result1.hashCode()).isEqualTo(result2.hashCode());
    }
}
```

## 6. 注意事项

*   **DTO的职责**: `SourceAnalysisResult` 严格遵循DTO的职责，只用于数据传输，不包含任何业务逻辑。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。这使得对象在多线程环境中是安全的。
*   **数据类型**: 确保数值类型（`int`）能够准确表示统计数据。
*   **嵌套DTO**: 内部的 `SearchEngineSource`, `ReferralSource`, `SocialMediaSource` 也是不可变的值对象，进一步保证了数据结构的清晰和一致性。
*   **前端适配**: DTO的结构应尽可能与前端图表组件（如ECharts）的期望数据格式匹配，减少前端的数据转换工作。
*   **性能**: 来源分析数据通常是聚合计算的结果，其生成可能涉及复杂的查询。在服务层构建此DTO时，应特别关注性能优化，例如使用缓存、预计算等。