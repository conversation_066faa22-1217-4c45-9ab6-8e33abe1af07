# VisitorType.md

## 1. 文件概述

`VisitorType` 是分析模块中的一个枚举类型，位于 `com.purchase.analytics.domain.model` 包中。它定义了系统识别的两种主要访问者类型：已注册用户和匿名用户。这个枚举旨在提供一个清晰、类型安全的方式来区分和引用不同类型的用户访问行为，是用户行为分析和统计的基础分类之一。

## 2. 核心功能

*   **类型定义**: 定义了两种核心的访问者类型：`REGISTERED`（已注册用户）和 `ANONYMOUS`（匿名用户）。
*   **类型安全**: 作为枚举，它提供了编译时类型检查，避免了使用字符串字面量可能导致的错误。
*   **可读性**: 使用有意义的名称来表示不同的访问者类型，提高了代码的可读性和可维护性。

## 3. 属性说明

`VisitorType` 枚举没有额外的属性，每个枚举常量本身即代表一种访问者类型。

- **`REGISTERED`**: 表示已在系统中注册并登录的用户。
- **`ANONYMOUS`**: 表示未登录或未注册的用户。

## 4. 业务规则

*   **唯一性**: 每个枚举常量都是唯一的，代表一个特定的访问者类型。
*   **扩展性**: 如果未来需要增加新的访问者类型（例如，`GUEST` 访客，`BOT` 机器人），可以直接在此枚举中添加新的常量，而无需修改已有的代码。
*   **数据来源**: 访问者的类型通常在用户登录状态或会话信息中确定。

## 5. 使用示例

```java
// 1. 在 VisitRecord 实体中使用 VisitorType
public class VisitRecord {
    private Long userId;
    private VisitorType visitorType; // 使用枚举代替String
    // ...
    public VisitorType getVisitorType() { return visitorType; }
    public void setVisitorType(VisitorType visitorType) { this.visitorType = visitorType; }
}

// 2. 在 VisitTrackingApplicationService 中根据用户状态设置 VisitorType
@Service
public class VisitTrackingApplicationServiceImpl implements VisitTrackingApplicationService {
    @Autowired
    private VisitLogRepository visitLogRepository;

    @Override
    public TrackVisitResult trackVisit(TrackVisitCommand command) {
        VisitLog visitLog = new VisitLog();
        visitLog.setUserId(command.getUserId());
        // 根据 userId 是否存在来判断访问者类型
        if (command.getUserId() != null) {
            visitLog.setVisitorType(VisitorType.REGISTERED);
        } else {
            visitLog.setVisitorType(VisitorType.ANONYMOUS);
        }
        // ... 其他属性赋值 ...
        visitLogRepository.save(visitLog);
        return TrackVisitResult.success(visitLog.getId().toString(), true);
    }
}

// 3. 在统计查询中根据 VisitorType 进行筛选
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private VisitRecordRepository visitRecordRepository;

    public RealTimeStatsResult queryRealTimeStats(RealTimeStatsQuery query) {
        // ...
        long registeredUserVisits = visitRecordRepository.countVisitsByVisitorType(query.getDate(), VisitorType.REGISTERED);
        long anonymousVisits = visitRecordRepository.countVisitsByVisitorType(query.getDate(), VisitorType.ANONYMOUS);
        // ...
        return RealTimeStatsResult.success(
            (int) todayVisits, (int) currentHourVisits, (int) uniqueVisitors,
            (int) registeredUserVisits, (int) anonymousVisits
        );
    }
}

// 4. 测试示例
@SpringBootTest
class VisitorTypeTest {
    @Test
    void testEnumValues() {
        assertThat(VisitorType.REGISTERED.name()).isEqualTo("REGISTERED");
        assertThat(VisitorType.ANONYMOUS.name()).isEqualTo("ANONYMOUS");
    }

    @Test
    void testValueOf() {
        assertThat(VisitorType.valueOf("REGISTERED")).isEqualTo(VisitorType.REGISTERED);
        assertThat(VisitorType.valueOf("ANONYMOUS")).isEqualTo(VisitorType.ANONYMOUS);
    }

    @Test
    void testInvalidValue() {
        assertThrows(IllegalArgumentException.class, () -> {
            VisitorType.valueOf("GUEST"); // 不存在的枚举值
        });
    }
}
```

## 6. 注意事项

*   **类型安全**: 使用枚举代替字符串可以避免因拼写错误导致的运行时问题，提高代码的健壮性。
*   **可读性与可维护性**: 枚举名称清晰地表达了其含义，使得代码更易于理解和维护。
*   **扩展性**: 当需要引入新的访问者类型时，只需在枚举中添加新的常量，而无需修改大量使用字符串的地方。
*   **与数据源映射**: 在数据库中，枚举值通常存储为字符串（`VARCHAR`）或整数（`INT`）。如果存储为字符串，应确保大小写一致性。
*   **业务逻辑**: 访问者类型的判断逻辑应在服务层或领域层进行，确保判断的准确性。