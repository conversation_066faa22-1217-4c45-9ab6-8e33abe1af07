package com.purchase.analytics.domain.model;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

/**
 * 访问时间值对象
 * 封装访问时间并提供业务相关的时间操作功能
 */
public class VisitTime {
    
    private static final int DEDUPLICATION_WINDOW_MINUTES = 5;
    
    private final LocalDateTime value;
    
    private VisitTime(LocalDateTime value) {
        this.value = Objects.requireNonNull(value, "访问时间不能为空");
    }
    
    /**
     * 创建访问时间值对象
     */
    public static VisitTime of(LocalDateTime dateTime) {
        return new VisitTime(dateTime);
    }
    
    /**
     * 获取时间值
     */
    public LocalDateTime getValue() {
        return value;
    }
    
    /**
     * 检查是否在去重时间窗口内（5分钟内）
     */
    public boolean isWithinDeduplicationWindow(VisitTime other) {
        if (other == null) {
            return false;
        }
        
        long minutesBetween = Math.abs(ChronoUnit.MINUTES.between(this.value, other.value));
        return minutesBetween <= DEDUPLICATION_WINDOW_MINUTES;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        VisitTime visitTime = (VisitTime) obj;
        return Objects.equals(value, visitTime.value);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
    
    @Override
    public String toString() {
        return "VisitTime{" +
                "value=" + value +
                '}';
    }
}
