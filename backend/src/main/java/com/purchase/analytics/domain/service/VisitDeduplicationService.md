# VisitDeduplicationService.md

## 1. 文件概述

`VisitDeduplicationService` 是分析模块中的一个领域服务（Domain Service），位于 `com.purchase.analytics.domain.service` 包中。它封装了识别和过滤重复访问记录的业务逻辑。在用户行为分析中，为了获得准确的独立访客数和页面浏览量，需要对原始访问数据进行去重。该服务提供了核心的去重算法，能够根据访问者类型（注册用户或匿名用户）和时间窗口来判断访问记录是否为重复访问。它不持有状态，专注于执行特定的领域操作。

## 2. 核心功能

*   **重复访问判断**: 提供了 `isDuplicateVisit` 方法，用于判断一个新的访问记录是否与已有的最近访问记录列表中的任何一个重复。这是去重逻辑的核心。
*   **唯一访问过滤**: 提供了 `filterUniqueVisits` 方法，能够从一个访问记录列表中筛选出所有唯一的访问记录，返回一个去重后的列表。
*   **去重策略**: 内部实现了根据访问者类型（注册用户基于 `userId`，匿名用户基于 `ipAddress` 和 `userAgent`）和时间窗口 (`VisitTime.isWithinDeduplicationWindow`) 进行去重的复杂逻辑。
*   **业务规则内聚**: 将去重这一复杂的业务规则封装在服务内部，使得外部调用者无需关心具体的去重算法。

## 3. 接口说明

作为领域服务，`VisitDeduplicationService` 主要通过其业务方法与外部交互。

### 3.1 核心业务方法

#### isDuplicateVisit - 检查是否为重复访问
*   **方法签名**: `boolean isDuplicateVisit(VisitRecord newVisit, List<VisitRecord> recentVisits)`
*   **描述**: 判断 `newVisit` 是否与 `recentVisits` 列表中的任何一个访问记录重复。重复的定义取决于访问者类型和时间窗口。
*   **参数**:
    *   `newVisit` (VisitRecord): 新的访问记录。
    *   `recentVisits` (List<VisitRecord>): 最近的访问记录列表，用于与 `newVisit` 进行比较。
*   **返回值**: `boolean` - 如果是重复访问，返回 `true`；否则返回 `false`。

#### filterUniqueVisits - 过滤唯一访问记录
*   **方法签名**: `List<VisitRecord> filterUniqueVisits(List<VisitRecord> allVisits)`
*   **描述**: 从给定的访问记录列表中筛选出所有唯一的访问记录。
*   **参数**:
    *   `allVisits` (List<VisitRecord>): 包含所有访问记录的列表。
*   **返回值**: `List<VisitRecord>` - 去重后的访问记录列表。

### 3.2 辅助方法

#### isDuplicateVisitPair - 检查两个访问记录是否为重复访问
*   **方法签名**: `private boolean isDuplicateVisitPair(VisitRecord newVisit, VisitRecord existingVisit, VisitTime newVisitTime)`
*   **描述**: 内部辅助方法，用于判断两个单独的访问记录是否构成重复。
*   **参数**:
    *   `newVisit` (VisitRecord): 新的访问记录。
    *   `existingVisit` (VisitRecord): 已存在的访问记录。
    *   `newVisitTime` (VisitTime): 新访问记录的时间值对象。
*   **返回值**: `boolean` - 如果两个记录重复，返回 `true`；否则返回 `false`。

## 4. 业务规则

*   **去重时间窗口**: 默认去重时间窗口为5分钟。这意味着在5分钟内，同一用户（或同一IP/UserAgent的匿名用户）对同一页面的多次访问可能被视为一次独立访问。
*   **注册用户去重**: 对于已注册用户，去重主要基于 `userId`。
*   **匿名用户去重**: 对于匿名用户，去重主要基于 `ipAddress` 和 `userAgent`。
*   **跨类型不重复**: 不同类型的访问者（注册用户和匿名用户）不会被视为重复访问。

## 5. 使用示例

```java
// 1. 在 VisitTrackingApplicationService 中使用 VisitDeduplicationService
@Service
public class VisitTrackingApplicationServiceImpl implements VisitTrackingApplicationService {
    @Autowired
    private VisitRecordRepository visitRecordRepository;
    @Autowired
    private VisitDeduplicationService deduplicationService;

    @Override
    @Transactional
    public TrackVisitResult trackVisit(TrackVisitCommand command) {
        VisitRecord newVisit;
        // ... 根据 command 构建 newVisit ...
        if (command.getUserId() != null) {
            newVisit = VisitRecord.createForRegisteredUser(
                command.getUserId(), command.getSessionId(), command.getIpAddress(), 
                command.getPageUrl(), command.getVisitTime()
            );
        } else {
            newVisit = VisitRecord.createForAnonymousUser(
                command.getSessionId(), command.getIpAddress(), command.getUserAgent(), 
                command.getPageUrl(), command.getVisitTime()
            );
        }

        // 查询最近的访问记录，用于去重判断
        List<VisitRecord> recentVisits;
        if (newVisit.getVisitorType() == VisitorType.REGISTERED) {
            recentVisits = visitRecordRepository.findRecentVisitsByUser(newVisit.getUserId(), newVisit.getVisitTime().getValue().minusMinutes(5));
        } else {
            recentVisits = visitRecordRepository.findRecentVisitsByIpAndUserAgent(newVisit.getIpAddress(), newVisit.getUserAgent(), newVisit.getVisitTime().getValue().minusMinutes(5));
        }

        boolean isUnique = !deduplicationService.isDuplicateVisit(newVisit, recentVisits);
        if (!isUnique) {
            newVisit.markAsNonUniqueVisit();
        }

        visitRecordRepository.save(newVisit);
        return TrackVisitResult.success(newVisit.getId().toString(), isUnique); // 假设VisitRecord有getId()
    }
}

// 2. 测试示例
@SpringBootTest
class VisitDeduplicationServiceTest {
    @Autowired
    private VisitDeduplicationService deduplicationService;

    @Test
    void testIsDuplicateVisit_RegisteredUser() {
        LocalDateTime now = LocalDateTime.now();
        VisitRecord newVisit = VisitRecord.createForRegisteredUser(1L, "s1", "ip1", "/page1", now);
        
        // 模拟最近的访问记录
        List<VisitRecord> recentVisits = List.of(
            VisitRecord.createForRegisteredUser(1L, "s2", "ip2", "/page1", now.minusMinutes(2)), // 同用户，在去重窗口内
            VisitRecord.createForRegisteredUser(2L, "s3", "ip3", "/page1", now.minusMinutes(1))
        );

        assertThat(deduplicationService.isDuplicateVisit(newVisit, recentVisits)).isTrue();
    }

    @Test
    void testIsDuplicateVisit_AnonymousUser() {
        LocalDateTime now = LocalDateTime.now();
        VisitRecord newVisit = VisitRecord.createForAnonymousUser("s1", "ip1", "ua1", "/page1", now);

        List<VisitRecord> recentVisits = List.of(
            VisitRecord.createForAnonymousUser("s2", "ip1", "ua1", "/page1", now.minusMinutes(3)), // 同IP/UA，在去重窗口内
            VisitRecord.createForAnonymousUser("s3", "ip2", "ua2", "/page1", now.minusMinutes(1))
        );

        assertThat(deduplicationService.isDuplicateVisit(newVisit, recentVisits)).isTrue();
    }

    @Test
    void testIsDuplicateVisit_DifferentPage() {
        LocalDateTime now = LocalDateTime.now();
        VisitRecord newVisit = VisitRecord.createForRegisteredUser(1L, "s1", "ip1", "/page2", now);

        List<VisitRecord> recentVisits = List.of(
            VisitRecord.createForRegisteredUser(1L, "s2", "ip2", "/page1", now.minusMinutes(2)) // 同用户，但不同页面
        );

        assertThat(deduplicationService.isDuplicateVisit(newVisit, recentVisits)).isFalse();
    }

    @Test
    void testFilterUniqueVisits() {
        LocalDateTime now = LocalDateTime.now();
        List<VisitRecord> allVisits = List.of(
            VisitRecord.createForRegisteredUser(1L, "s1", "ip1", "/page1", now.minusMinutes(5)),
            VisitRecord.createForRegisteredUser(1L, "s2", "ip1", "/page1", now.minusMinutes(3)), // 重复
            VisitRecord.createForAnonymousUser("s3", "ip2", "ua1", "/page2", now.minusMinutes(4)),
            VisitRecord.createForAnonymousUser("s4", "ip2", "ua1", "/page2", now.minusMinutes(1))  // 重复
        );

        List<VisitRecord> uniqueVisits = deduplicationService.filterUniqueVisits(allVisits);
        assertThat(uniqueVisits).hasSize(2);
        assertThat(uniqueVisits.get(0).getUserId()).isEqualTo(1L);
        assertThat(uniqueVisits.get(1).getPageUrl()).isEqualTo("/page2");
    }
}
```

## 6. 注意事项

*   **领域服务特性**: `VisitDeduplicationService` 是一个典型的领域服务，它封装了不属于任何一个实体但又与领域密切相关的业务逻辑（去重算法）。它不持有状态，专注于执行特定的领域操作。
*   **依赖注入**: 该服务依赖于 `VisitRecordRepository` 来获取历史访问记录，以便进行去重判断。
*   **性能优化**: `isDuplicateVisit` 方法的性能取决于 `recentVisits` 列表的大小。在实际应用中，`recentVisits` 应该只包含在去重时间窗口内的记录，并且数量不宜过大。可以考虑使用更高效的数据结构（如 `HashSet`）来存储已处理的唯一访问。
*   **去重策略**: 去重策略（基于用户ID或IP/UserAgent，以及时间窗口）是核心业务规则，应根据实际业务需求进行精确定义和调整。
*   **可测试性**: 领域服务通常是纯粹的业务逻辑，易于进行单元测试，确保去重算法的正确性。
*   **事务**: 去重服务本身通常不涉及持久化操作，因此不需要事务管理。事务管理应由调用它的应用服务层负责。