package com.purchase.analytics.domain.model;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Objects;

/**
 * 访问来源值对象
 * 封装访问来源信息，如来源页面、页面URL等
 */
public class VisitSource {
    
    private final String referer;
    private final String pageUrl;
    
    private VisitSource(String referer, String pageUrl) {
        this.referer = referer;
        this.pageUrl = Objects.requireNonNull(pageUrl, "页面URL不能为空");
    }
    
    /**
     * 创建访问来源值对象
     */
    public static VisitSource of(String referer, String pageUrl) {
        return new VisitSource(referer, pageUrl);
    }
    
    /**
     * 获取来源页面
     */
    public String getReferer() {
        return referer;
    }
    
    /**
     * 获取页面URL
     */
    public String getPageUrl() {
        return pageUrl;
    }
    
    /**
     * 是否为直接访问（无来源）
     */
    public boolean isDirectVisit() {
        return referer == null || referer.trim().isEmpty();
    }
    
    /**
     * 是否来自搜索引擎
     */
    public boolean isFromSearchEngine() {
        if (isDirectVisit()) {
            return false;
        }
        
        String lowerReferer = referer.toLowerCase();
        return lowerReferer.contains("google.com") ||
               lowerReferer.contains("baidu.com") ||
               lowerReferer.contains("bing.com") ||
               lowerReferer.contains("yahoo.com") ||
               lowerReferer.contains("sogou.com") ||
               lowerReferer.contains("so.com");
    }
    
    /**
     * 获取来源域名
     */
    public String getSourceDomain() {
        if (isDirectVisit()) {
            return null;
        }
        
        try {
            URL url = new URL(referer);
            return url.getHost();
        } catch (MalformedURLException e) {
            return null;
        }
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        VisitSource that = (VisitSource) obj;
        return Objects.equals(referer, that.referer) &&
               Objects.equals(pageUrl, that.pageUrl);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(referer, pageUrl);
    }
    
    @Override
    public String toString() {
        return "VisitSource{" +
                "referer='" + referer + '\'' +
                ", pageUrl='" + pageUrl + '\'' +
                '}';
    }
}
