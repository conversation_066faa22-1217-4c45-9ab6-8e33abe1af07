# VisitStatistics.md

## 1. 文件概述

`VisitStatistics` 是分析模块中的一个聚合根（Aggregate Root），位于 `com.purchase.analytics.domain.model` 包中。它封装了特定时间粒度（日或小时）的访问统计数据，并内聚了与这些统计数据相关的业务逻辑。这个实体不仅包含了访问量、独立访客数等核心指标，还提供了创建不同粒度统计对象的方法，以及更新统计数据和判断是否为高峰时段的行为。它是用户行为分析和数据统计的领域核心，负责管理统计数据的汇总和计算。

## 2. 核心功能

*   **数据封装**: 封装了特定日期和/或小时的访问统计数据，包括总访问量、独立访客数、注册用户访问量和匿名访问量。
*   **聚合根特性**: 作为聚合根，它确保了 `VisitStatistics` 内部数据的一致性，并通过工厂方法控制其创建过程。
*   **安全创建**: 提供了 `createDailyStatistics` 和 `createHourlyStatistics` 两个静态工厂方法，用于创建不同时间粒度的统计对象，确保了统计数据的业务含义和数据完整性。
*   **统计数据更新**: 提供了 `incrementVisits()` 和 `incrementUniqueVisitors()` 方法，用于在有新的访问数据时，原子性地更新统计计数。
*   **业务判断**: 提供了 `isPeakPeriod()` 方法，用于判断当前统计时段是否达到预设的访问量阈值，从而识别高峰时段。
*   **值对象使用**: 属性中使用了 `LocalDate` 和 `StatisticsType` 等值对象，确保了数据的类型安全和业务含义。
*   **值语义**: 重写了 `equals` 和 `hashCode` 方法，确保了当所有属性值（`visitDate`, `visitHour`, `statisticsType`）相等时，两个 `VisitStatistics` 对象被认为是相等的。

## 3. 接口说明

作为聚合根，`VisitStatistics` 主要通过其工厂方法和业务方法与外部交互。

### 3.1 静态工厂方法

#### createDailyStatistics - 创建日访问统计
*   **方法签名**: `static VisitStatistics createDailyStatistics(LocalDate visitDate, int totalVisits, int uniqueVisitors, int registeredUserVisits, int anonymousVisits)`
*   **描述**: 用于创建按天统计的访问记录。`visitHour` 为 `null`，`statisticsType` 为 `DAILY`。
*   **参数**:
    *   `visitDate` (LocalDate): 统计的日期。**必填**。
    *   `totalVisits` (int): 当日总访问量。
    *   `uniqueVisitors` (int): 当日独立访客数。
    *   `registeredUserVisits` (int): 当日注册用户访问量。
    *   `anonymousVisits` (int): 当日匿名用户访问量。
*   **返回值**: `VisitStatistics` - 一个新的日访问统计实例。

#### createHourlyStatistics - 创建小时访问统计
*   **方法签名**: `static VisitStatistics createHourlyStatistics(LocalDate visitDate, int visitHour, int totalVisits, int uniqueVisitors)`
*   **描述**: 用于创建按小时统计的访问记录。`statisticsType` 为 `HOURLY`。
*   **参数**:
    *   `visitDate` (LocalDate): 统计的日期。**必填**。
    *   `visitHour` (int): 统计的小时（0-23）。**必填**。
    *   `totalVisits` (int): 该小时总访问量。
    *   `uniqueVisitors` (int): 该小时独立访客数。
*   **返回值**: `VisitStatistics` - 一个新的小时访问统计实例。
*   **异常**: `IllegalArgumentException` - 如果 `visitHour` 不在0-23之间。

### 3.2 核心业务方法

#### incrementVisits - 增加访问次数
*   **方法签名**: `void incrementVisits(int increment)`
*   **描述**: 增加当前统计对象的总访问量。
*   **参数**:
    *   `increment` (int): 增加的数量。必须为非负数。
*   **返回值**: `void`
*   **异常**: `IllegalArgumentException` - 如果 `increment` 为负数。

#### incrementUniqueVisitors - 增加独立访客数
*   **方法签名**: `void incrementUniqueVisitors(int increment)`
*   **描述**: 增加当前统计对象的独立访客数。
*   **参数**:
    *   `increment` (int): 增加的数量。必须为非负数。
*   **返回值**: `void`
*   **异常**: `IllegalArgumentException` - 如果 `increment` 为负数。

#### isPeakPeriod - 检查是否为高峰时段
*   **方法签名**: `boolean isPeakPeriod(int threshold)`
*   **描述**: 判断当前统计对象的总访问量是否达到指定的阈值。
*   **参数**:
    *   `threshold` (int): 高峰访问量的阈值。
*   **返回值**: `boolean` - 如果 `totalVisits` 大于等于 `threshold`，返回 `true`。

## 4. 业务规则

*   **唯一性**: `VisitStatistics` 实例的唯一性由 `visitDate`, `visitHour` (如果存在) 和 `statisticsType` 共同决定。
*   **数据完整性**: 在创建时，必须提供所有必要的参数。例如，`visitDate` 和 `statisticsType` 不能为空。
*   **状态更新**: 统计数据（`totalVisits`, `uniqueVisitors` 等）只能通过 `increment` 方法进行增加，不能直接设置，确保了数据更新的原子性和业务含义。
*   **时间粒度**: `visitHour` 字段仅在 `HOURLY` 统计类型下有意义，在 `DAILY` 统计类型下应为 `null`。

## 5. 使用示例

```java
// 1. 在 VisitStatisticsRepositoryImpl 中创建和更新 VisitStatistics
@Repository
public class VisitStatisticsRepositoryImpl implements VisitStatisticsRepository {
    @Autowired
    private VisitStatisticsMapper visitStatisticsMapper;

    @Override
    @Transactional
    public VisitStatistics save(VisitStatistics stats) {
        // 查找现有统计记录
        VisitStatistics existingStats = visitStatisticsMapper.findByDateAndHourAndType(
            stats.getVisitDate(), stats.getVisitHour(), stats.getStatisticsType().name()
        );

        if (existingStats != null) {
            // 更新现有记录
            existingStats.incrementVisits(stats.getTotalVisits());
            existingStats.incrementUniqueVisitors(stats.getUniqueVisitors());
            // ... 更新其他字段 ...
            visitStatisticsMapper.updateById(existingStats);
            return existingStats;
        } else {
            // 插入新记录
            visitStatisticsMapper.insert(stats);
            return stats;
        }
    }
}

// 2. 在 VisitTrackingApplicationService 中更新 VisitStatistics
@Service
public class VisitTrackingApplicationServiceImpl implements VisitTrackingApplicationService {
    @Autowired
    private VisitStatisticsRepository visitStatisticsRepository;

    @Override
    @Transactional
    public TrackVisitResult trackVisit(TrackVisitCommand command) {
        // ... 创建 VisitRecord ...

        // 更新日统计
        VisitStatistics dailyStats = VisitStatistics.createDailyStatistics(
            command.getVisitTime().toLocalDate(), 1, 1, 
            command.getUserId() != null ? 1 : 0, command.getUserId() == null ? 1 : 0
        );
        visitStatisticsRepository.save(dailyStats);

        // 更新小时统计
        VisitStatistics hourlyStats = VisitStatistics.createHourlyStatistics(
            command.getVisitTime().toLocalDate(), command.getVisitTime().getHour(), 1, 1
        );
        visitStatisticsRepository.save(hourlyStats);

        return TrackVisitResult.success("someId", true);
    }
}

// 3. 测试示例
@SpringBootTest
class VisitStatisticsTest {
    @Test
    void testCreateDailyStatistics() {
        LocalDate date = LocalDate.of(2024, 7, 27);
        VisitStatistics stats = VisitStatistics.createDailyStatistics(date, 100, 50, 30, 20);

        assertThat(stats.getVisitDate()).isEqualTo(date);
        assertThat(stats.getVisitHour()).isNull();
        assertThat(stats.getTotalVisits()).isEqualTo(100);
        assertThat(stats.getUniqueVisitors()).isEqualTo(50);
        assertThat(stats.getRegisteredUserVisits()).isEqualTo(30);
        assertThat(stats.getAnonymousVisits()).isEqualTo(20);
        assertThat(stats.getStatisticsType()).isEqualTo(StatisticsType.DAILY);
    }

    @Test
    void testCreateHourlyStatistics() {
        LocalDate date = LocalDate.of(2024, 7, 27);
        VisitStatistics stats = VisitStatistics.createHourlyStatistics(date, 14, 20, 15);

        assertThat(stats.getVisitDate()).isEqualTo(date);
        assertThat(stats.getVisitHour()).isEqualTo(14);
        assertThat(stats.getTotalVisits()).isEqualTo(20);
        assertThat(stats.getUniqueVisitors()).isEqualTo(15);
        assertThat(stats.getStatisticsType()).isEqualTo(StatisticsType.HOURLY);
    }

    @Test
    void testIncrementVisits() {
        VisitStatistics stats = VisitStatistics.createDailyStatistics(LocalDate.now(), 100, 50, 30, 20);
        stats.incrementVisits(10);
        assertThat(stats.getTotalVisits()).isEqualTo(110);
    }

    @Test
    void testIsPeakPeriod() {
        VisitStatistics stats = VisitStatistics.createHourlyStatistics(LocalDate.now(), 10, 500, 200);
        assertThat(stats.isPeakPeriod(400)).isTrue();
        assertThat(stats.isPeakPeriod(600)).isFalse();
    }
}
```

## 6. 注意事项

*   **聚合根**: `VisitStatistics` 作为聚合根，其内部状态的改变应通过其自身的方法来完成，而不是直接通过Setter。这保证了业务规则的封装和一致性。
*   **不可变性**: 核心标识属性（`visitDate`, `visitHour`, `statisticsType`）被声明为 `final`，确保了统计对象的唯一性和不可变性。而统计数值（`totalVisits` 等）是可变的，通过 `increment` 方法进行更新。
*   **工厂方法**: 强制使用工厂方法创建实例，可以在对象创建时执行必要的校验和初始化，保证了对象的有效状态。
*   **持久化**: `VisitStatistics` 实体需要通过一个仓储（Repository）接口进行持久化操作，例如 `VisitStatisticsRepository`。
*   **并发**: 在更新统计数据时，需要考虑并发问题。实现 `incrementVisits` 等方法时，应确保原子性操作，例如在数据库层面使用 `UPDATE ... SET count = count + 1`。
*   **数据量**: 访问统计数据可能随着时间增长而变得庞大。需要考虑数据库的索引优化、分区表以及历史数据的归档策略。
*   **性能**: 统计数据的计算和更新通常是高并发操作。持久化层需要优化写入和读取性能。
*   **业务含义**: 实体内部的方法（如 `isPeakPeriod`）直接反映了业务概念，使得领域模型更加丰富和有表现力。
