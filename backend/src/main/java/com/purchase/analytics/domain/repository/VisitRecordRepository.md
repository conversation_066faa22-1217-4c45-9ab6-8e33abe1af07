# VisitRecordRepository.md

## 1. 文件概述

`VisitRecordRepository` 是分析模块中的一个仓储接口（Repository Interface），位于 `com.purchase.analytics.domain.repository` 包中。它定义了 `VisitRecord` 聚合根的持久化操作和查询契约。该接口旨在将领域层与基础设施层解耦，使得领域模型能够独立于具体的持久化技术（如数据库、缓存）进行设计。它提供了保存访问记录、根据ID查找、以及各种复杂的统计查询方法，是用户行为数据存储和分析的基础设施抽象。

## 2. 核心功能

*   **聚合根持久化**: 定义了 `save` 方法，用于持久化 `VisitRecord` 聚合根，确保了聚合根的原子性操作。
*   **按ID查找**: 提供了 `findById` 方法，用于根据唯一标识查找特定的访问记录。
*   **去重辅助查询**: 提供了 `findRecentVisitsByUser` 和 `findRecentVisitsByIpAndUserAgent` 等方法，用于支持访问去重逻辑，查找特定用户或IP/UserAgent组合的最近访问记录。
*   **多维度统计查询**: 提供了大量统计方法，涵盖了按日期范围统计访问次数、独立访客数、设备类型、访问来源（直接、搜索引擎、社交媒体、引荐网站）、浏览器、操作系统等多种维度。
*   **趋势数据获取**: 提供了 `getVisitHistoryByDays` 和 `getOnlineHistoryByHours` 等方法，用于获取历史访问量和在线用户数的趋势数据。
*   **地理统计与实时趋势**: 提供了 `getGeographicStatsByDateRange` 和 `getRealTimeTrendData` 等方法，支持更高级的地理位置统计和实时趋势分析。
*   **页面访问统计**: 提供了 `getPageVisitStatsByDateRange` 方法，用于获取页面访问的详细统计数据，包括访问量、独立访客、平均停留时间等。

## 3. 接口说明

### 3.1 持久化操作

#### save - 保存访问记录
*   **方法签名**: `VisitRecord save(VisitRecord visitRecord)`
*   **描述**: 持久化（插入或更新）一个 `VisitRecord` 聚合根。
*   **参数**:
    *   `visitRecord` (VisitRecord): 待保存的访问记录聚合根。
*   **返回值**: `VisitRecord` - 保存后的访问记录（可能包含数据库生成的ID）。

#### findById - 根据ID查找访问记录
*   **方法签名**: `Optional<VisitRecord> findById(Long id)`
*   **描述**: 根据访问记录的唯一ID查找对应的 `VisitRecord`。
*   **参数**:
    *   `id` (Long): 访问记录的ID。
*   **返回值**: `Optional<VisitRecord>` - 包含匹配的访问记录，如果不存在则为 `Optional.empty()`。

### 3.2 去重辅助查询

#### findRecentVisitsByUser - 查找用户最近的访问记录
*   **方法签名**: `List<VisitRecord> findRecentVisitsByUser(Long userId, LocalDateTime since)`
*   **描述**: 查找指定用户在给定时间点之后的所有访问记录。
*   **参数**:
    *   `userId` (Long): 用户ID。
    *   `since` (LocalDateTime): 起始时间。
*   **返回值**: `List<VisitRecord>` - 匹配的访问记录列表。

#### findRecentVisitsByIpAndUserAgent - 根据IP和UserAgent查找最近的访问记录
*   **方法签名**: `List<VisitRecord> findRecentVisitsByIpAndUserAgent(String ipAddress, String userAgent, LocalDateTime since)`
*   **描述**: 查找指定IP地址和UserAgent在给定时间点之后的所有访问记录，主要用于匿名用户去重。
*   **参数**:
    *   `ipAddress` (String): IP地址。
    *   `userAgent` (String): 用户代理字符串。
    *   `since` (LocalDateTime): 起始时间。
*   **返回值**: `List<VisitRecord>` - 匹配的访问记录列表。

### 3.3 统计查询方法

#### countByDateRange - 统计指定时间范围内的访问次数
*   **方法签名**: `long countByDateRange(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的总访问次数。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
*   **返回值**: `long` - 访问次数。

#### countUniqueVisitorsByDateRange - 统计指定时间范围内的独立访客数
*   **方法签名**: `long countUniqueVisitorsByDateRange(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的独立访客数量。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
*   **返回值**: `long` - 独立访客数。

#### countDesktopVisitsByDateRange - 统计指定时间范围内的桌面设备访问量
*   **方法签名**: `long countDesktopVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的桌面设备访问量。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
*   **返回值**: `long` - 桌面设备访问量。

#### countMobileVisitsByDateRange - 统计指定时间范围内的移动设备访问量
*   **方法签名**: `long countMobileVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的移动设备访问量。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
*   **返回值**: `long` - 移动设备访问量。

#### countTabletVisitsByDateRange - 统计指定时间范围内的平板设备访问量
*   **方法签名**: `long countTabletVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的平板设备访问量。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
*   **返回值**: `long` - 平板设备访问量。

#### countVisitsByDate - 统计指定日期的总访问量
*   **方法签名**: `long countVisitsByDate(LocalDate date)`
*   **描述**: 统计指定日期的总访问量。
*   **参数**:
    *   `date` (LocalDate): 日期。
*   **返回值**: `long` - 总访问量。

#### countUniqueVisitorsByDate - 统计指定日期的独立访客数
*   **方法签名**: `long countUniqueVisitorsByDate(LocalDate date)`
*   **描述**: 统计指定日期的独立访客数。
*   **参数**:
    *   `date` (LocalDate): 日期。
*   **返回值**: `long` - 独立访客数。

#### countRegisteredVisitsByDate - 统计指定日期的注册用户访问量
*   **方法签名**: `long countRegisteredVisitsByDate(LocalDate date)`
*   **描述**: 统计指定日期的注册用户访问量。
*   **参数**:
    *   `date` (LocalDate): 日期。
*   **返回值**: `long` - 注册用户访问量。

#### countAnonymousVisitsByDate - 统计指定日期的匿名用户访问量
*   **方法签名**: `long countAnonymousVisitsByDate(LocalDate date)`
*   **描述**: 统计指定日期的匿名用户访问量。
*   **参数**:
    *   `date` (LocalDate): 日期。
*   **返回值**: `long` - 匿名用户访问量。

#### countByRefererPattern - 统计指定时间范围内按referer模式匹配的访问量
*   **方法签名**: `long countByRefererPattern(LocalDateTime startTime, LocalDateTime endTime, String refererPattern)`
*   **描述**: 统计在指定时间范围内，referer匹配特定模式的访问量。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
    *   `refererPattern` (String): referer匹配模式（例如：`%google.com%`）。
*   **返回值**: `long` - 访问量。

#### countDirectVisitsByDateRange - 统计指定时间范围内的直接访问量
*   **方法签名**: `long countDirectVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的直接访问量（referer为空或null）。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
*   **返回值**: `long` - 直接访问量。

#### countSearchEngineVisitsByDateRange - 统计指定时间范围内的搜索引擎访问量
*   **方法签名**: `long countSearchEngineVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String searchEngine)`
*   **描述**: 统计在指定时间范围内，来自特定搜索引擎的访问量。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
    *   `searchEngine` (String): 搜索引擎名称（例如：`google`, `baidu`）。
*   **返回值**: `long` - 搜索引擎访问量。

#### countSocialMediaVisitsByDateRange - 统计指定时间范围内的社交媒体访问量
*   **方法签名**: `long countSocialMediaVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String socialMedia)`
*   **描述**: 统计在指定时间范围内，来自特定社交媒体的访问量。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
    *   `socialMedia` (String): 社交媒体名称（例如：`wechat`, `weibo`）。
*   **返回值**: `long` - 社交媒体访问量。

#### countReferralVisitsByDateRange - 统计指定时间范围内的引荐网站访问量
*   **方法签名**: `long countReferralVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String domain)`
*   **描述**: 统计在指定时间范围内，来自特定引荐网站的访问量。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
    *   `domain` (String): 引荐网站域名。
*   **返回值**: `long` - 引荐网站访问量。

#### countBrowserVisitsByDateRange - 统计指定时间范围内的浏览器访问量
*   **方法签名**: `long countBrowserVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String browserName)`
*   **描述**: 统计在指定时间范围内，来自特定浏览器的访问量。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
    *   `browserName` (String): 浏览器名称（例如：`chrome`, `safari`）。
*   **返回值**: `long` - 浏览器访问量。

#### countOperatingSystemVisitsByDateRange - 统计指定时间范围内的操作系统访问量
*   **方法签名**: `long countOperatingSystemVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String osName)`
*   **描述**: 统计在指定时间范围内，来自特定操作系统的访问量。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
    *   `osName` (String): 操作系统名称（例如：`windows`, `macos`）。
*   **返回值**: `long` - 操作系统访问量。

#### countPageViewsByDateRange - 统计指定时间范围内的页面浏览量
*   **方法签名**: `long countPageViewsByDateRange(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 统计在指定时间范围内的页面浏览量（总访问次数）。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
*   **返回值**: `long` - 页面浏览量。

#### calculateAvgStayTimeByDateRange - 计算指定时间范围内的平均停留时间
*   **方法签名**: `double calculateAvgStayTimeByDateRange(LocalDateTime startTime, LocalDateTime endTime)`
*   **描述**: 计算在指定时间范围内的平均用户停留时间。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
*   **返回值**: `double` - 平均停留时间（分钟）。

#### getVisitHistoryByDays - 获取最近几天的访问量历史数据
*   **方法签名**: `List<Long> getVisitHistoryByDays(int days)`
*   **描述**: 获取最近指定天数的每日总访问量历史数据。
*   **参数**:
    *   `days` (int): 需要获取的天数。
*   **返回值**: `List<Long>` - 每日访问量列表。

#### getOnlineHistoryByHours - 获取最近几小时的在线用户数历史数据
*   **方法签名**: `List<Long> getOnlineHistoryByHours(int hours)`
*   **描述**: 获取最近指定小时数的每小时在线用户数历史数据。
*   **参数**:
    *   `hours` (int): 需要获取的小时数。
*   **返回值**: `List<Long>` - 每小时在线用户数列表。

#### getGeographicStatsByDateRange - 根据地理位置统计访问量
*   **方法签名**: `List<Object[]> getGeographicStatsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String mapType)`
*   **描述**: 根据地理位置统计在指定时间范围内的访问量，并可指定地图类型（中国/世界）。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
    *   `mapType` (String): 地图类型（`china` 或 `world`）。
*   **返回值**: `List<Object[]>` - 地理位置访问统计数据列表，每个数组包含地区名称和访问量。

#### getRealTimeTrendData - 获取实时趋势数据
*   **方法签名**: `List<Object[]> getRealTimeTrendData(LocalDateTime startTime, LocalDateTime endTime, String granularity)`
*   **描述**: 获取实时访问趋势数据，支持不同时间粒度（分钟/小时/天）。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
    *   `granularity` (String): 时间粒度（`minute`, `hour`, `day`）。
*   **返回值**: `List<Object[]>` - 实时趋势数据列表，每个数组包含时间、访问量、独立访客数、页面浏览量等。

#### getPageVisitStatsByDateRange - 获取页面访问统计数据
*   **方法签名**: `List<com.purchase.analytics.infrastructure.dto.PageVisitStatsDTO> getPageVisitStatsByDateRange(LocalDateTime startTime, LocalDateTime endTime, int limit)`
*   **描述**: 获取在指定时间范围内的页面访问统计数据，并可限制返回数量。
*   **参数**:
    *   `startTime` (LocalDateTime): 开始时间。
    *   `endTime` (LocalDateTime): 结束时间。
    *   `limit` (int): 返回数量限制。
*   **返回值**: `List<com.purchase.analytics.infrastructure.dto.PageVisitStatsDTO>` - 页面访问统计数据列表。

## 4. 业务规则

*   **数据源**: 该仓储接口的实现将直接与底层数据存储（如关系型数据库、NoSQL数据库或数据仓库）进行交互，负责数据的读写。
*   **性能优化**: 考虑到访问记录数据量庞大且查询频繁，仓储的实现必须高度优化，包括但不限于：
    *   为查询字段建立合适的索引。
    *   使用批量插入和更新操作。
    *   利用数据库的分区、分表技术。
    *   引入缓存机制（如Redis）来缓存热点数据或统计结果。
    *   对于复杂统计，考虑使用物化视图或预计算。
*   **数据一致性**: 确保在保存和查询数据时，数据的一致性和准确性。
*   **异常处理**: 仓储实现应捕获底层数据访问异常，并转换为领域层可理解的异常类型。

## 5. 使用示例

```java
// 1. 在 VisitRecordRepositoryImpl 中实现 save 方法
@Repository
public class VisitRecordRepositoryImpl implements VisitRecordRepository {
    @Autowired
    private VisitRecordMapper visitRecordMapper;

    @Override
    public VisitRecord save(VisitRecord visitRecord) {
        // 将领域实体转换为PO（持久化对象）
        VisitRecordPO po = convertToPO(visitRecord);
        if (po.getId() == null) {
            visitRecordMapper.insert(po);
        } else {
            visitRecordMapper.updateById(po);
        }
        // 将PO转换回领域实体，并设置数据库生成的ID
        visitRecord.setId(po.getId()); // 假设VisitRecord有setId方法
        return visitRecord;
    }

    // 辅助方法：实体到PO的转换
    private VisitRecordPO convertToPO(VisitRecord domain) {
        VisitRecordPO po = new VisitRecordPO();
        po.setId(domain.getId());
        po.setUserId(domain.getUserId());
        po.setSessionId(domain.getSessionId());
        po.setIpAddress(domain.getIpAddress());
        po.setUserAgent(domain.getUserAgent());
        po.setPageUrl(domain.getPageUrl());
        po.setVisitTime(domain.getVisitTime());
        po.setVisitorType(domain.getVisitorType().name());
        po.setUniqueVisit(domain.isUniqueVisit());
        return po;
    }

    // ... 其他方法的实现 ...
}

// 2. 在 StatisticsQueryApplicationService 中调用仓储接口
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private VisitRecordRepository visitRecordRepository;

    public RealTimeStatsResult queryRealTimeStats(RealTimeStatsQuery query) {
        // ... 参数验证 ...
        LocalDate today = query.getDate();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(LocalTime.MAX);

        long todayVisits = visitRecordRepository.countByDateRange(startOfDay, endOfDay);
        long uniqueVisitors = visitRecordRepository.countUniqueVisitorsByDateRange(startOfDay, endOfDay);

        return RealTimeStatsResult.success(
            (int) todayVisits, (int) visitRecordRepository.countByDateRange(LocalDateTime.now().withMinute(0).withSecond(0).withNano(0), LocalDateTime.now().withMinute(59).withSecond(59).withNano(999999999)), (int) uniqueVisitors,
            (int) visitRecordRepository.countRegisteredVisitsByDate(today), (int) visitRecordRepository.countAnonymousVisitsByDate(today)
        );
    }

    public SourceAnalysisResult querySourceAnalysis(SourceAnalysisQuery query) {
        LocalDateTime startTime = query.getStartDate().atStartOfDay();
        LocalDateTime endTime = query.getEndDate().atTime(LocalTime.MAX);

        List<SourceAnalysisResult.SearchEngineSource> searchEngines = new ArrayList<>();
        searchEngines.add(new SourceAnalysisResult.SearchEngineSource("Google", (int) visitRecordRepository.countSearchEngineVisitsByDateRange(startTime, endTime, "google")));
        searchEngines.add(new SourceAnalysisResult.SearchEngineSource("Baidu", (int) visitRecordRepository.countSearchEngineVisitsByDateRange(startTime, endTime, "baidu")));

        long directVisits = visitRecordRepository.countDirectVisitsByDateRange(startTime, endTime);

        List<SourceAnalysisResult.ReferralSource> referralSites = new ArrayList<>();
        referralSites.add(new SourceAnalysisResult.ReferralSource("example.com", (int) visitRecordRepository.countReferralVisitsByDateRange(startTime, endTime, "example.com")));

        List<SourceAnalysisResult.SocialMediaSource> socialMedia = new ArrayList<>();
        socialMedia.add(new SourceAnalysisResult.SocialMediaSource("WeChat", (int) visitRecordRepository.countSocialMediaVisitsByDateRange(startTime, endTime, "wechat")));

        return new SourceAnalysisResult(searchEngines, (int) directVisits, referralSites, socialMedia);
    }
}

// 3. 测试示例 (使用Mocking)
@SpringBootTest
class VisitRecordRepositoryTest {
    @MockBean
    private VisitRecordMapper visitRecordMapper;

    @Autowired
    private VisitRecordRepository visitRecordRepository;

    @Test
    void testSaveNewVisitRecord() {
        VisitRecord record = VisitRecord.createForRegisteredUser(1L, "s1", "1.1.1.1", "/home", LocalDateTime.now());
        VisitRecordPO po = new VisitRecordPO();
        po.setId(100L); // 模拟数据库生成ID
        when(visitRecordMapper.insert(any(VisitRecordPO.class))).thenReturn(1);
        when(visitRecordMapper.selectById(100L)).thenReturn(po);

        VisitRecord savedRecord = visitRecordRepository.save(record);

        assertThat(savedRecord.getId()).isEqualTo(100L);
        verify(visitRecordMapper, times(1)).insert(any(VisitRecordPO.class));
    }

    @Test
    void testCountByDateRange() {
        LocalDateTime start = LocalDateTime.of(2024, 1, 1, 0, 0);
        LocalDateTime end = LocalDateTime.of(2024, 1, 1, 23, 59, 59);
        when(visitRecordMapper.countByDateRange(start, end)).thenReturn(150L);

        long count = visitRecordRepository.countByDateRange(start, end);
        assertThat(count).isEqualTo(150L);
    }
}
```

## 6. 注意事项

*   **DDD仓储模式**: `VisitRecordRepository` 是DDD中仓储模式的体现。它提供了一个集合的抽象，使得客户端（应用服务）可以像操作内存中的集合一样操作持久化对象，而无需关心底层存储细节。
*   **领域实体与PO转换**: 仓储的实现类（如 `VisitRecordRepositoryImpl`）负责将领域实体 `VisitRecord` 转换为持久化对象（PO，如 `VisitRecordPO`）进行存储，并在读取时将PO转换回领域实体。这种转换是仓储的职责。
*   **事务管理**: 仓储方法通常在应用服务层被调用，并由应用服务层进行事务管理，确保操作的原子性。
*   **性能优化**: 考虑到访问记录的写入和查询量都非常大，仓储的实现必须高度优化。这包括但不限于：
    *   使用批量操作（批量插入、批量更新）。
    *   为查询条件字段建立合适的索引。
    *   利用数据库的分区、分表技术。
    *   对于统计查询，可以考虑使用预计算、物化视图或数据仓库。
*   **异常处理**: 仓储实现应捕获底层数据访问异常（如 `SQLException`）并转换为领域层可理解的异常类型（如 `PersistenceException` 或自定义的业务异常），避免底层技术细节泄露到领域层。
*   **接口粒度**: 接口定义了非常细粒度的查询方法，这使得应用服务可以灵活地组合这些方法来满足各种统计需求。
*   **测试**: 仓储接口的实现应该通过集成测试来验证其与数据库的正确交互，而应用服务层可以通过Mock仓储接口来进行单元测试。