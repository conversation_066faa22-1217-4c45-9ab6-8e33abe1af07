package com.purchase.analytics.domain.model;

import java.time.LocalDate;
import java.util.Objects;

/**
 * 访问统计聚合根
 * 负责管理访问统计数据的汇总和计算
 */
public class VisitStatistics {
    
    private final LocalDate visitDate;
    private final Integer visitHour; // 小时统计时使用，日统计时为null
    private int totalVisits;
    private int uniqueVisitors;
    private int registeredUserVisits;
    private int anonymousVisits;
    private final StatisticsType statisticsType;
    
    private VisitStatistics(LocalDate visitDate, Integer visitHour, int totalVisits, 
                           int uniqueVisitors, int registeredUserVisits, int anonymousVisits,
                           StatisticsType statisticsType) {
        this.visitDate = Objects.requireNonNull(visitDate, "访问日期不能为空");
        this.visitHour = visitHour;
        this.totalVisits = totalVisits;
        this.uniqueVisitors = uniqueVisitors;
        this.registeredUserVisits = registeredUserVisits;
        this.anonymousVisits = anonymousVisits;
        this.statisticsType = Objects.requireNonNull(statisticsType, "统计类型不能为空");
    }
    
    /**
     * 创建日访问统计
     */
    public static VisitStatistics createDailyStatistics(LocalDate visitDate, int totalVisits, 
                                                       int uniqueVisitors, int registeredUserVisits, 
                                                       int anonymousVisits) {
        return new VisitStatistics(visitDate, null, totalVisits, uniqueVisitors, 
                                 registeredUserVisits, anonymousVisits, StatisticsType.DAILY);
    }
    
    /**
     * 创建小时访问统计
     */
    public static VisitStatistics createHourlyStatistics(LocalDate visitDate, int visitHour, 
                                                        int totalVisits, int uniqueVisitors) {
        if (visitHour < 0 || visitHour > 23) {
            throw new IllegalArgumentException("访问小时必须在0-23之间");
        }
        return new VisitStatistics(visitDate, visitHour, totalVisits, uniqueVisitors, 
                                 0, 0, StatisticsType.HOURLY);
    }
    
    /**
     * 增加访问次数
     */
    public void incrementVisits(int increment) {
        if (increment < 0) {
            throw new IllegalArgumentException("增量不能为负数");
        }
        this.totalVisits += increment;
    }
    
    /**
     * 增加独立访客数
     */
    public void incrementUniqueVisitors(int increment) {
        if (increment < 0) {
            throw new IllegalArgumentException("增量不能为负数");
        }
        this.uniqueVisitors += increment;
    }
    
    /**
     * 检查是否为高峰时段
     */
    public boolean isPeakPeriod(int threshold) {
        return totalVisits >= threshold;
    }
    
    // Getters
    public LocalDate getVisitDate() { return visitDate; }
    public Integer getVisitHour() { return visitHour; }
    public int getTotalVisits() { return totalVisits; }
    public int getUniqueVisitors() { return uniqueVisitors; }
    public int getRegisteredUserVisits() { return registeredUserVisits; }
    public int getAnonymousVisits() { return anonymousVisits; }
    public StatisticsType getStatisticsType() { return statisticsType; }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        VisitStatistics that = (VisitStatistics) obj;
        return Objects.equals(visitDate, that.visitDate) &&
               Objects.equals(visitHour, that.visitHour) &&
               statisticsType == that.statisticsType;
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(visitDate, visitHour, statisticsType);
    }
    
    @Override
    public String toString() {
        return "VisitStatistics{" +
                "visitDate=" + visitDate +
                ", visitHour=" + visitHour +
                ", totalVisits=" + totalVisits +
                ", uniqueVisitors=" + uniqueVisitors +
                ", statisticsType=" + statisticsType +
                '}';
    }
}
