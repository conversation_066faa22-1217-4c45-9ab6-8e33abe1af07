package com.purchase.analytics.domain.model;

import java.time.LocalDateTime;

/**
 * 访问记录聚合根
 */
public class VisitRecord {
    
    private Long userId;
    private String sessionId;
    private String ipAddress;
    private String userAgent;
    private String pageUrl;
    private LocalDateTime visitTime;
    private VisitorType visitorType;
    private boolean uniqueVisit;
    
    private VisitRecord() {
        this.uniqueVisit = true; // 默认为唯一访问
    }
    
    public static VisitRecord createForRegisteredUser(Long userId, String sessionId, 
                                                     String ipAddress, String pageUrl, 
                                                     LocalDateTime visitTime) {
        VisitRecord record = new VisitRecord();
        record.userId = userId;
        record.sessionId = sessionId;
        record.ipAddress = ipAddress;
        record.pageUrl = pageUrl;
        record.visitTime = visitTime;
        record.visitorType = VisitorType.REGISTERED;
        return record;
    }
    
    public static VisitRecord createForAnonymousUser(String sessionId, String ipAddress, 
                                                    String userAgent, String pageUrl, 
                                                    LocalDateTime visitTime) {
        VisitRecord record = new VisitRecord();
        record.sessionId = sessionId;
        record.ipAddress = ipAddress;
        record.userAgent = userAgent;
        record.pageUrl = pageUrl;
        record.visitTime = visitTime;
        record.visitorType = VisitorType.ANONYMOUS;
        return record;
    }
    
    public void markAsNonUniqueVisit() {
        this.uniqueVisit = false;
    }
    
    // Getters
    public Long getUserId() { return userId; }
    public String getSessionId() { return sessionId; }
    public String getIpAddress() { return ipAddress; }
    public String getUserAgent() { return userAgent; }
    public String getPageUrl() { return pageUrl; }
    public LocalDateTime getVisitTime() { return visitTime; }
    public VisitorType getVisitorType() { return visitorType; }
    public boolean isUniqueVisit() { return uniqueVisit; }
}