# VisitStatisticsRepository.md

## 1. 文件概述

`VisitStatisticsRepository` 是分析模块中的一个仓储接口（Repository Interface），位于 `com.purchase.analytics.domain.repository` 包中。它定义了 `VisitStatistics` 聚合根的持久化操作和查询契约。该接口旨在将领域层与基础设施层解耦，使得领域模型能够独立于具体的持久化技术（如数据库、缓存）进行设计。它提供了保存访问统计记录、根据日期和时间粒度查找、以及批量操作和删除等功能，是用户行为统计数据存储和分析的基础设施抽象。

## 2. 核心功能

*   **聚合根持久化**: 定义了 `save` 方法，用于持久化 `VisitStatistics` 聚合根，确保了聚合根的原子性操作。
*   **按时间粒度查找**: 提供了 `findDailyStatistics` 和 `findHourlyStatistics` 方法，用于根据日期或日期+小时查找特定粒度的统计记录。
*   **按日期范围查找**: 提供了 `findByDateRange` 方法，支持根据日期范围和统计类型查找统计记录列表。
*   **批量操作**: 提供了 `batchSave` 方法，用于批量保存统计记录，提高写入效率；以及 `deleteByDateRange` 方法，用于批量删除指定日期范围的统计记录。
*   **数据聚合**: 仓储的实现负责将原始访问记录聚合为 `VisitStatistics` 对象，并进行持久化。

## 3. 接口说明

### 3.1 持久化操作

#### save - 保存统计记录
*   **方法签名**: `VisitStatistics save(VisitStatistics visitStatistics)`
*   **描述**: 持久化（插入或更新）一个 `VisitStatistics` 聚合根。如果记录已存在，则更新；否则插入。
*   **参数**:
    *   `visitStatistics` (VisitStatistics): 待保存的访问统计聚合根。
*   **返回值**: `VisitStatistics` - 保存后的访问统计记录。

#### batchSave - 批量保存统计记录
*   **方法签名**: `int batchSave(List<VisitStatistics> statisticsList)`
*   **描述**: 批量保存（插入或更新）多个 `VisitStatistics` 聚合根，提高写入效率。
*   **参数**:
    *   `statisticsList` (List<VisitStatistics>): 待保存的访问统计记录列表。
*   **返回值**: `int` - 成功保存的记录数量。

#### deleteByDateRange - 删除指定日期范围的统计记录
*   **方法签名**: `int deleteByDateRange(LocalDate startDate, LocalDate endDate, StatisticsType type)`
*   **描述**: 删除指定日期范围和统计类型的所有统计记录。
*   **参数**:
    *   `startDate` (LocalDate): 开始日期。
    *   `endDate` (LocalDate): 结束日期。
    *   `type` (StatisticsType): 统计类型（`DAILY`, `HOURLY`）。
*   **返回值**: `int` - 成功删除的记录数量。

### 3.2 查询操作

#### findDailyStatistics - 查找日统计记录
*   **方法签名**: `Optional<VisitStatistics> findDailyStatistics(LocalDate date)`
*   **描述**: 查找指定日期的日访问统计记录。
*   **参数**:
    *   `date` (LocalDate): 日期。
*   **返回值**: `Optional<VisitStatistics>` - 包含匹配的日统计记录，如果不存在则为 `Optional.empty()`。

#### findHourlyStatistics - 查找小时统计记录
*   **方法签名**: `Optional<VisitStatistics> findHourlyStatistics(LocalDate date, int hour)`
*   **描述**: 查找指定日期和小时的小时访问统计记录。
*   **参数**:
    *   `date` (LocalDate): 日期。
    *   `hour` (int): 小时（0-23）。
*   **返回值**: `Optional<VisitStatistics>` - 包含匹配的小时统计记录，如果不存在则为 `Optional.empty()`。

#### findByDateRange - 根据日期范围查找统计记录
*   **方法签名**: `List<VisitStatistics> findByDateRange(LocalDate startDate, LocalDate endDate, StatisticsType type)`
*   **描述**: 查找指定日期范围和统计类型的所有统计记录。
*   **参数**:
    *   `startDate` (LocalDate): 开始日期。
    *   `endDate` (LocalDate): 结束日期。
    *   `type` (StatisticsType): 统计类型。
*   **返回值**: `List<VisitStatistics>` - 匹配的统计记录列表。

#### findHourlyStatisticsByDate - 查找指定日期的所有小时统计
*   **方法签名**: `List<VisitStatistics> findHourlyStatisticsByDate(LocalDate date)`
*   **描述**: 查找指定日期的所有小时访问统计记录。
*   **参数**:
    *   `date` (LocalDate): 日期。
*   **返回值**: `List<VisitStatistics>` - 匹配的小时统计记录列表。

## 4. 业务规则

*   **DDD仓储模式**: `VisitStatisticsRepository` 是DDD中仓储模式的体现。它提供了一个集合的抽象，使得客户端（应用服务）可以像操作内存中的集合一样操作持久化对象，而无需关心底层存储细节。
*   **领域实体与PO转换**: 仓储的实现类负责将领域实体 `VisitStatistics` 转换为持久化对象（PO）进行存储，并在读取时将PO转换回领域实体。这种转换是仓储的职责。
*   **事务管理**: 仓储方法通常在应用服务层被调用，并由应用服务层进行事务管理，确保操作的原子性。
*   **性能优化**: 考虑到访问统计数据的写入和查询量都可能很大，仓储的实现必须高度优化。这包括但不限于：
    *   为查询字段建立合适的索引（如 `visit_date`, `visit_hour`, `statistics_type`）。
    *   使用批量插入和更新操作。
    *   利用数据库的分区、分表技术。
    *   对于统计查询，可以考虑使用预计算或物化视图。
*   **异常处理**: 仓储实现应捕获底层数据访问异常（如 `SQLException`）并转换为领域层可理解的异常类型，避免底层技术细节泄露到领域层。

## 5. 使用示例

```java
// 1. 在 VisitStatisticsRepositoryImpl 中实现 save 方法
@Repository
public class VisitStatisticsRepositoryImpl implements VisitStatisticsRepository {
    @Autowired
    private VisitStatisticsMapper visitStatisticsMapper;

    @Override
    public VisitStatistics save(VisitStatistics stats) {
        // 将领域实体转换为PO（持久化对象）
        VisitStatisticsPO po = convertToPO(stats);
        // 尝试查找现有记录
        VisitStatisticsPO existingPo = visitStatisticsMapper.findByDateAndHourAndType(
            po.getVisitDate(), po.getVisitHour(), po.getStatisticsType()
        );

        if (existingPo != null) {
            // 更新现有记录
            existingPo.setTotalVisits(po.getTotalVisits());
            existingPo.setUniqueVisitors(po.getUniqueVisitors());
            existingPo.setRegisteredUserVisits(po.getRegisteredUserVisits());
            existingPo.setAnonymousVisits(po.getAnonymousVisits());
            visitStatisticsMapper.updateById(existingPo);
            // 将更新后的PO的ID设置回领域实体
            stats.setId(existingPo.getId()); // 假设VisitStatistics有setId方法
            return stats;
        } else {
            // 插入新记录
            visitStatisticsMapper.insert(po);
            // 将新插入的PO的ID设置回领域实体
            stats.setId(po.getId()); // 假设VisitStatistics有setId方法
            return stats;
        }
    }

    // 辅助方法：领域实体到PO的转换
    private VisitStatisticsPO convertToPO(VisitStatistics domain) {
        VisitStatisticsPO po = new VisitStatisticsPO();
        po.setId(domain.getId()); // 如果有ID的话
        po.setVisitDate(domain.getVisitDate());
        po.setVisitHour(domain.getVisitHour());
        po.setTotalVisits(domain.getTotalVisits());
        po.setUniqueVisitors(domain.getUniqueVisitors());
        po.setRegisteredUserVisits(domain.getRegisteredUserVisits());
        po.setAnonymousVisits(domain.getAnonymousVisits());
        po.setStatisticsType(domain.getStatisticsType().name());
        return po;
    }

    // 辅助方法：PO到领域实体的转换
    private VisitStatistics convertToDomain(VisitStatisticsPO po) {
        if (po == null) return null;
        // 根据statisticsType调用不同的工厂方法
        if (StatisticsType.DAILY.name().equals(po.getStatisticsType())) {
            return VisitStatistics.createDailyStatistics(
                po.getVisitDate(), po.getTotalVisits(), po.getUniqueVisitors(), 
                po.getRegisteredUserVisits(), po.getAnonymousVisits()
            );
        } else if (StatisticsType.HOURLY.name().equals(po.getStatisticsType())) {
            return VisitStatistics.createHourlyStatistics(
                po.getVisitDate(), po.getVisitHour(), po.getTotalVisits(), po.getUniqueVisitors()
            );
        }
        throw new IllegalArgumentException("未知的统计类型: " + po.getStatisticsType());
    }

    // ... 其他方法的实现 ...
}

// 2. 在 StatisticsQueryApplicationService 中调用仓储接口
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private VisitStatisticsRepository visitStatisticsRepository;

    public HistoricalStatsResult queryHistoricalStats(HistoricalStatsQuery query) {
        // ... 参数验证 ...
        List<VisitStatistics> statistics = visitStatisticsRepository.findByDateRange(
            query.getStartDate(), query.getEndDate(), StatisticsType.valueOf(query.getGranularity().toUpperCase())
        );
        // ... 计算汇总数据 ...
        return HistoricalStatsResult.success(statistics, calculateSummary(statistics));
    }
}

// 3. 测试示例 (使用Mocking)
@SpringBootTest
class VisitStatisticsRepositoryTest {
    @MockBean
    private VisitStatisticsMapper visitStatisticsMapper;

    @Autowired
    private VisitStatisticsRepository visitStatisticsRepository;

    @Test
    void testSaveNewDailyStatistics() {
        LocalDate date = LocalDate.now();
        VisitStatistics dailyStats = VisitStatistics.createDailyStatistics(date, 100, 50, 30, 20);
        VisitStatisticsPO mockPo = new VisitStatisticsPO();
        mockPo.setId(1L); // 模拟数据库生成ID
        when(visitStatisticsMapper.findByDateAndHourAndType(date, null, StatisticsType.DAILY.name())).thenReturn(null);
        when(visitStatisticsMapper.insert(any(VisitStatisticsPO.class))).thenReturn(1);

        VisitStatistics savedStats = visitStatisticsRepository.save(dailyStats);

        assertThat(savedStats.getId()).isEqualTo(1L);
        verify(visitStatisticsMapper, times(1)).insert(any(VisitStatisticsPO.class));
    }

    @Test
    void testFindDailyStatistics() {
        LocalDate date = LocalDate.now();
        VisitStatisticsPO mockPo = new VisitStatisticsPO();
        mockPo.setVisitDate(date);
        mockPo.setStatisticsType(StatisticsType.DAILY.name());
        mockPo.setTotalVisits(100);
        when(visitStatisticsMapper.findByDateAndHourAndType(date, null, StatisticsType.DAILY.name())).thenReturn(mockPo);

        Optional<VisitStatistics> foundStats = visitStatisticsRepository.findDailyStatistics(date);

        assertThat(foundStats).isPresent();
        assertThat(foundStats.get().getTotalVisits()).isEqualTo(100);
    }
}
```

## 6. 注意事项

*   **DDD仓储模式**: `VisitStatisticsRepository` 是DDD中仓储模式的体现。它提供了一个集合的抽象，使得客户端（应用服务）可以像操作内存中的集合一样操作持久化对象，而无需关心底层存储细节。
*   **领域实体与PO转换**: 仓储的实现类负责将领域实体 `VisitStatistics` 转换为持久化对象（PO，如 `VisitStatisticsPO`）进行存储，并在读取时将PO转换回领域实体。这种转换是仓储的核心职责。
*   **事务管理**: 仓储方法通常在应用服务层被调用，并由应用服务层进行事务管理，确保操作的原子性。
*   **性能优化**: 考虑到访问统计数据的写入和查询量都可能很大，仓储的实现必须高度优化。这包括但不限于：
    *   为查询字段建立合适的索引（如 `visit_date`, `visit_hour`, `statistics_type`）。
    *   使用批量插入和更新操作。
    *   利用数据库的分区、分表技术。
    *   对于统计查询，可以考虑使用预计算或物化视图。
*   **异常处理**: 仓储实现应捕获底层数据访问异常（如 `SQLException`）并转换为领域层可理解的异常类型，避免底层技术细节泄露到领域层。
*   **更新逻辑**: `save` 方法的实现需要判断是插入新记录还是更新现有记录，这通常通过查询是否存在来决定。
*   **工厂方法与PO**: 在将PO转换为领域实体时，应根据 `statisticsType` 调用 `VisitStatistics` 领域实体中相应的工厂方法，以确保领域对象的正确构建。