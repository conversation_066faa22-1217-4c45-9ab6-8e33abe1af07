package com.purchase.analytics.domain.model;

import java.util.Objects;

/**
 * 客户端信息值对象
 * 封装客户端相关信息，如用户代理、屏幕分辨率、时区等
 */
public class ClientInfo {
    
    private final String userAgent;
    private final String screenResolution;
    private final String timezone;
    
    private ClientInfo(String userAgent, String screenResolution, String timezone) {
        this.userAgent = Objects.requireNonNull(userAgent, "用户代理不能为空");
        this.screenResolution = screenResolution;
        this.timezone = timezone;
    }
    
    /**
     * 创建客户端信息值对象
     */
    public static ClientInfo of(String userAgent, String screenResolution, String timezone) {
        if (userAgent == null || userAgent.trim().isEmpty()) {
            throw new IllegalArgumentException("用户代理不能为空");
        }
        return new ClientInfo(userAgent, screenResolution, timezone);
    }
    
    /**
     * 获取用户代理
     */
    public String getUserAgent() {
        return userAgent;
    }
    
    /**
     * 获取屏幕分辨率
     */
    public String getScreenResolution() {
        return screenResolution;
    }
    
    /**
     * 获取时区
     */
    public String getTimezone() {
        return timezone;
    }
    
    /**
     * 判断是否为移动设备
     */
    public boolean isMobileDevice() {
        if (userAgent == null) {
            return false;
        }
        
        String lowerUserAgent = userAgent.toLowerCase();
        return lowerUserAgent.contains("mobile") ||
               lowerUserAgent.contains("iphone") ||
               lowerUserAgent.contains("android") ||
               lowerUserAgent.contains("ipad") ||
               lowerUserAgent.contains("tablet");
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ClientInfo that = (ClientInfo) obj;
        return Objects.equals(userAgent, that.userAgent) &&
               Objects.equals(screenResolution, that.screenResolution) &&
               Objects.equals(timezone, that.timezone);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(userAgent, screenResolution, timezone);
    }
    
    @Override
    public String toString() {
        return "ClientInfo{" +
                "userAgent='" + userAgent + '\'' +
                ", screenResolution='" + screenResolution + '\'' +
                ", timezone='" + timezone + '\'' +
                '}';
    }
}
