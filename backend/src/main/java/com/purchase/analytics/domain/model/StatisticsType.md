# StatisticsType.md

## 1. 文件概述

`StatisticsType` 是分析模块中的一个枚举类型，位于 `com.purchase.analytics.domain.model` 包中。它定义了系统支持的几种不同的统计数据类型或粒度。这个枚举旨在提供一个清晰、类型安全的方式来区分和引用不同维度的统计数据，例如按天、按小时或按用户进行统计。它是构建灵活统计查询和数据分析功能的基础。

## 2. 核心功能

*   **类型定义**: 定义了三种核心的统计类型：`DAILY`（日统计）、`HOURLY`（小时统计）和 `USER`（用户统计）。
*   **类型安全**: 作为枚举，它提供了编译时类型检查，避免了使用字符串字面量可能导致的错误。
*   **可读性**: 使用有意义的名称来表示不同的统计类型，提高了代码的可读性和可维护性。

## 3. 属性说明

`StatisticsType` 枚举没有额外的属性，每个枚举常量本身即代表一种统计类型。

- **`DAILY`**: 表示按天进行数据统计。
- **`HOURLY`**: 表示按小时进行数据统计。
- **`USER`**: 表示按用户进行数据统计。

## 4. 业务规则

*   **唯一性**: 每个枚举常量都是唯一的，代表一个特定的统计维度。
*   **扩展性**: 如果未来需要增加新的统计维度（例如按周、按月、按设备），可以直接在此枚举中添加新的常量，而无需修改已有的代码。

## 5. 使用示例

```java
// 1. 在查询对象中使用 StatisticsType
public class HistoricalStatsQuery {
    private LocalDate startDate;
    private LocalDate endDate;
    private StatisticsType granularity; // 使用枚举代替String
    
    public StatisticsType getGranularity() { return granularity; }
    public void setGranularity(StatisticsType granularity) { this.granularity = granularity; }
}

// 2. 在应用服务中根据 StatisticsType 执行不同的统计逻辑
@Service
public class StatisticsQueryApplicationService {
    @Autowired
    private VisitRecordRepository visitRecordRepository;

    public HistoricalStatsResult queryHistoricalStats(HistoricalStatsQuery query) {
        // ... 参数验证 ...

        List<VisitStatistics> statistics = new ArrayList<>();
        if (query.getGranularity() == StatisticsType.DAILY) {
            // 执行按日统计的逻辑
            LocalDate current = query.getStartDate();
            while (!current.isAfter(query.getEndDate())) {
                long totalVisits = visitRecordRepository.countVisitsByDate(current);
                long uniqueVisitors = visitRecordRepository.countUniqueVisitorsByDate(current);
                statistics.add(VisitStatistics.createDailyStatistics(current, (int) totalVisits, (int) uniqueVisitors, 0, 0));
                current = current.plusDays(1);
            }
        } else if (query.getGranularity() == StatisticsType.HOURLY) {
            // 执行按小时统计的逻辑
            // ...
        } else if (query.getGranularity() == StatisticsType.USER) {
            // 执行按用户统计的逻辑
            // ...
        }
        // ...
        return HistoricalStatsResult.success(statistics, calculateSummary(statistics));
    }
}

// 3. 在 Controller 中接收 String 参数并转换为 StatisticsType
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    private StatisticsQueryApplicationService statisticsQueryService;

    @GetMapping("/historical-stats")
    public ApiResponse<HistoricalStatsResult> getHistoricalStats(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "DAILY") String granularity) {
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            StatisticsType type = StatisticsType.valueOf(granularity.toUpperCase()); // 将String转换为枚举
            
            HistoricalStatsQuery query = new HistoricalStatsQuery();
            query.setStartDate(start);
            query.setEndDate(end);
            query.setGranularity(type);
            
            HistoricalStatsResult result = statisticsQueryService.queryHistoricalStats(query);
            
            return ApiResponse.success(result, "查询成功");
            
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("查询历史统计失败: " + e.getMessage());
        }
    }
}

// 4. 测试示例
@SpringBootTest
class StatisticsTypeTest {
    @Test
    void testEnumValues() {
        assertThat(StatisticsType.DAILY.name()).isEqualTo("DAILY");
        assertThat(StatisticsType.HOURLY.name()).isEqualTo("HOURLY");
        assertThat(StatisticsType.USER.name()).isEqualTo("USER");
    }

    @Test
    void testValueOf() {
        assertThat(StatisticsType.valueOf("DAILY")).isEqualTo(StatisticsType.DAILY);
        assertThat(StatisticsType.valueOf("HOURLY")).isEqualTo(StatisticsType.HOURLY);
        assertThat(StatisticsType.valueOf("USER")).isEqualTo(StatisticsType.USER);
    }

    @Test
    void testInvalidValue() {
        assertThrows(IllegalArgumentException.class, () -> {
            StatisticsType.valueOf("MONTHLY"); // 不存在的枚举值
        });
    }
}
```

## 6. 注意事项

*   **类型安全**: 使用枚举代替字符串可以避免因拼写错误导致的运行时问题，提高代码的健壮性。
*   **可读性与可维护性**: 枚举名称清晰地表达了其含义，使得代码更易于理解和维护。
*   **扩展性**: 当需要引入新的统计类型时，只需在枚举中添加新的常量，而无需修改大量使用字符串的地方。
*   **与前端交互**: 前端通常通过字符串来传递枚举值。在后端Controller层，需要将接收到的字符串转换为对应的枚举类型（例如使用 `StatisticsType.valueOf(string.toUpperCase())`）。
*   **数据库存储**: 在数据库中，枚举值通常存储为字符串（`VARCHAR`）或整数（`INT`）。如果存储为字符串，应确保大小写一致性。