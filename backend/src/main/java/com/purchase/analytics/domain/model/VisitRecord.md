# VisitRecord.md

## 1. 文件概述

`VisitRecord` 是分析模块中的一个聚合根（Aggregate Root），位于 `com.purchase.analytics.domain.model` 包中。它封装了用户访问网站的每一次记录，包括访问的用户信息、会话信息、网络信息、访问页面以及访问时间等。作为一个聚合根，它不仅包含了数据属性，还内聚了与访问记录相关的业务逻辑，如创建不同类型的访问记录（注册用户或匿名用户）以及标记为非唯一访问。它是用户行为分析和数据统计的最小业务单元。

## 2. 核心功能

*   **数据封装**: 封装了用户访问事件的所有相关数据，包括用户身份、会话信息、网络信息和访问页面信息。
*   **聚合根特性**: 作为聚合根，它确保了 `VisitRecord` 内部数据的一致性，并通过工厂方法控制其创建过程。
*   **安全创建**: 提供了 `createForRegisteredUser` 和 `createForAnonymousUser` 两个静态工厂方法，用于创建不同类型的访问记录，确保了记录的业务含义和数据完整性。
*   **访问类型区分**: 通过 `VisitorType` 枚举区分了已注册用户和匿名用户的访问，便于后续的精细化分析。
*   **唯一访问标记**: 提供了 `markAsNonUniqueVisit()` 方法，用于在去重逻辑中将重复访问标记为非唯一，确保统计的准确性。
*   **不可变性**: 除了 `uniqueVisit` 字段外，其他核心属性在创建后不可更改，保证了访问记录的原始性。

## 3. 接口说明

作为聚合根，`VisitRecord` 主要通过其工厂方法和Getter方法与外部交互。

### 3.1 静态工厂方法

#### createForRegisteredUser - 为已注册用户创建访问记录
*   **方法签名**: `static VisitRecord createForRegisteredUser(Long userId, String sessionId, String ipAddress, String pageUrl, LocalDateTime visitTime)`
*   **描述**: 用于创建已注册用户的访问记录。`userId` 必须提供。
*   **参数**:
    *   `userId` (Long): 已注册用户的ID。
    *   `sessionId` (String): 用户的会话ID。
    *   `ipAddress` (String): 客户端的IP地址。
    *   `pageUrl` (String): 用户访问的页面URL。
    *   `visitTime` (LocalDateTime): 访问发生的时间。
*   **返回值**: `VisitRecord` - 一个新的 `VisitRecord` 实例，`visitorType` 默认为 `REGISTERED`。

#### createForAnonymousUser - 为匿名用户创建访问记录
*   **方法签名**: `static VisitRecord createForAnonymousUser(String sessionId, String ipAddress, String userAgent, String pageUrl, LocalDateTime visitTime)`
*   **描述**: 用于创建匿名用户的访问记录。`userAgent` 必须提供。
*   **参数**:
    *   `sessionId` (String): 用户的会话ID。
    *   `ipAddress` (String): 客户端的IP地址。
    *   `userAgent` (String): 客户端的用户代理字符串。
    *   `pageUrl` (String): 用户访问的页面URL。
    *   `visitTime` (LocalDateTime): 访问发生的时间。
*   **返回值**: `VisitRecord` - 一个新的 `VisitRecord` 实例，`visitorType` 默认为 `ANONYMOUS`。

### 3.2 核心业务方法

#### markAsNonUniqueVisit - 标记为非唯一访问
*   **方法签名**: `void markAsNonUniqueVisit()`
*   **描述**: 将当前访问记录标记为非唯一访问。通常在去重逻辑中调用。
*   **返回值**: `void`

## 4. 业务规则

*   **唯一性**: `VisitRecord` 实例的唯一性由其内部数据（如 `sessionId`, `pageUrl`, `visitTime`）共同决定，而不是一个简单的ID。
*   **数据完整性**: 在创建时，必须提供所有必要的参数。例如，匿名用户必须提供 `userAgent`。
*   **去重逻辑**: `uniqueVisit` 字段的维护是核心业务逻辑。在持久化之前，需要通过去重服务来判断当前访问是否为独立访问。
*   **时间戳**: `visitTime` 字段应在后端接收到请求时生成，以确保时间戳的准确性。

## 5. 使用示例

```java
// 1. 在 VisitTrackingApplicationService 中创建 VisitRecord
@Service
public class VisitTrackingApplicationServiceImpl implements VisitTrackingApplicationService {
    @Autowired
    private VisitRecordRepository visitRecordRepository;
    @Autowired
    private VisitDeduplicationService deduplicationService;

    @Override
    @Transactional
    public TrackVisitResult trackVisit(TrackVisitCommand command) {
        VisitRecord visitRecord;
        if (command.getUserId() != null) {
            visitRecord = VisitRecord.createForRegisteredUser(
                command.getUserId(), command.getSessionId(), command.getIpAddress(), 
                command.getPageUrl(), command.getVisitTime()
            );
        } else {
            visitRecord = VisitRecord.createForAnonymousUser(
                command.getSessionId(), command.getIpAddress(), command.getUserAgent(), 
                command.getPageUrl(), command.getVisitTime()
            );
        }

        // 执行去重逻辑
        boolean isUnique = deduplicationService.isUniqueVisit(visitRecord);
        if (!isUnique) {
            visitRecord.markAsNonUniqueVisit();
        }

        visitRecordRepository.save(visitRecord);
        return TrackVisitResult.success(visitRecord.getId().toString(), isUnique); // 假设VisitRecord有getId()
    }
}

// 2. 在 VisitRecordRepository 中持久化 VisitRecord
public interface VisitRecordRepository {
    VisitRecord save(VisitRecord record);
    // ... 其他查询方法 ...
}

// 3. 测试示例
@SpringBootTest
class VisitRecordTest {
    @Test
    void testCreateForRegisteredUser() {
        Long userId = 1L;
        String sessionId = "session123";
        String ipAddress = "***********";
        String pageUrl = "/home";
        LocalDateTime visitTime = LocalDateTime.now();

        VisitRecord record = VisitRecord.createForRegisteredUser(userId, sessionId, ipAddress, pageUrl, visitTime);

        assertThat(record.getUserId()).isEqualTo(userId);
        assertThat(record.getSessionId()).isEqualTo(sessionId);
        assertThat(record.getIpAddress()).isEqualTo(ipAddress);
        assertThat(record.getPageUrl()).isEqualTo(pageUrl);
        assertThat(record.getVisitTime()).isEqualTo(visitTime);
        assertThat(record.getVisitorType()).isEqualTo(VisitorType.REGISTERED);
        assertThat(record.isUniqueVisit()).isTrue(); // 默认为true
    }

    @Test
    void testCreateForAnonymousUser() {
        String sessionId = "session456";
        String ipAddress = "********";
        String userAgent = "Mozilla/5.0";
        String pageUrl = "/products";
        LocalDateTime visitTime = LocalDateTime.now();

        VisitRecord record = VisitRecord.createForAnonymousUser(sessionId, ipAddress, userAgent, pageUrl, visitTime);

        assertThat(record.getUserId()).isNull();
        assertThat(record.getSessionId()).isEqualTo(sessionId);
        assertThat(record.getIpAddress()).isEqualTo(ipAddress);
        assertThat(record.getUserAgent()).isEqualTo(userAgent);
        assertThat(record.getPageUrl()).isEqualTo(pageUrl);
        assertThat(record.getVisitTime()).isEqualTo(visitTime);
        assertThat(record.getVisitorType()).isEqualTo(VisitorType.ANONYMOUS);
        assertThat(record.isUniqueVisit()).isTrue();
    }

    @Test
    void testMarkAsNonUniqueVisit() {
        VisitRecord record = VisitRecord.createForRegisteredUser(1L, "s1", "ip", "/p", LocalDateTime.now());
        record.markAsNonUniqueVisit();
        assertThat(record.isUniqueVisit()).isFalse();
    }
}
```

## 6. 注意事项

*   **聚合根**: `VisitRecord` 作为聚合根，其内部状态的改变应通过其自身的方法来完成，而不是直接通过Setter。这保证了业务规则的封装和一致性。
*   **不可变性**: 除了 `uniqueVisit` 字段外，其他核心属性在创建后不可更改，这对于历史记录的准确性至关重要。
*   **工厂方法**: 强制使用工厂方法创建实例，可以在对象创建时执行必要的校验和初始化，保证了对象的有效状态。
*   **持久化**: `VisitRecord` 实体需要通过一个仓储（Repository）接口进行持久化操作，例如 `VisitRecordRepository`。
*   **去重逻辑**: `uniqueVisit` 字段的维护是核心业务逻辑。在持久化之前，需要通过去重服务来判断当前访问是否为独立访问，并相应地更新此字段。
*   **数据量**: 访问记录是典型的海量数据。需要考虑数据库的分库分表策略，以及历史数据的归档和清理机制。
*   **性能**: 访问记录的写入通常是高并发操作。持久化层需要优化写入性能，例如批量插入、异步写入等。
