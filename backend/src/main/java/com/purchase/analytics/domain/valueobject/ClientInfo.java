package com.purchase.analytics.domain.valueobject;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 客户端信息值对象
 * 用于封装访问者的客户端相关信息
 */
public class ClientInfo {
    
    @JsonProperty("userAgent")
    private String userAgent;
    
    @JsonProperty("screenResolution")
    private String screenResolution;
    
    @JsonProperty("timezone")
    private String timezone;
    
    @JsonProperty("language")
    private String language;
    
    @JsonProperty("platform")
    private String platform;

    // 默认构造函数
    public ClientInfo() {}

    // 全参构造函数
    public ClientInfo(String userAgent, String screenResolution, String timezone, String language, String platform) {
        this.userAgent = userAgent;
        this.screenResolution = screenResolution;
        this.timezone = timezone;
        this.language = language;
        this.platform = platform;
    }

    // 静态工厂方法：从JSON字符串创建
    public static ClientInfo fromJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new ClientInfo();
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(json, ClientInfo.class);
        } catch (JsonProcessingException e) {
            // 如果JSON解析失败，返回空对象而不是抛出异常
            return new ClientInfo();
        }
    }

    // 转换为JSON字符串
    public String toJson() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            // 如果序列化失败，返回空JSON对象
            return "{}";
        }
    }

    // Getters
    public String getUserAgent() { return userAgent; }
    public String getScreenResolution() { return screenResolution; }
    public String getTimezone() { return timezone; }
    public String getLanguage() { return language; }
    public String getPlatform() { return platform; }

    // Setters
    public void setUserAgent(String userAgent) { this.userAgent = userAgent; }
    public void setScreenResolution(String screenResolution) { this.screenResolution = screenResolution; }
    public void setTimezone(String timezone) { this.timezone = timezone; }
    public void setLanguage(String language) { this.language = language; }
    public void setPlatform(String platform) { this.platform = platform; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        ClientInfo that = (ClientInfo) o;
        
        if (userAgent != null ? !userAgent.equals(that.userAgent) : that.userAgent != null) return false;
        if (screenResolution != null ? !screenResolution.equals(that.screenResolution) : that.screenResolution != null) return false;
        if (timezone != null ? !timezone.equals(that.timezone) : that.timezone != null) return false;
        if (language != null ? !language.equals(that.language) : that.language != null) return false;
        return platform != null ? platform.equals(that.platform) : that.platform == null;
    }

    @Override
    public int hashCode() {
        int result = userAgent != null ? userAgent.hashCode() : 0;
        result = 31 * result + (screenResolution != null ? screenResolution.hashCode() : 0);
        result = 31 * result + (timezone != null ? timezone.hashCode() : 0);
        result = 31 * result + (language != null ? language.hashCode() : 0);
        result = 31 * result + (platform != null ? platform.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ClientInfo{" +
                "userAgent='" + userAgent + '\'' +
                ", screenResolution='" + screenResolution + '\'' +
                ", timezone='" + timezone + '\'' +
                ", language='" + language + '\'' +
                ", platform='" + platform + '\'' +
                '}';
    }
}
