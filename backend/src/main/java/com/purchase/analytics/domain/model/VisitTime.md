# VisitTime.md

## 1. 文件概述

`VisitTime` 是分析模块中的一个值对象（Value Object），位于 `com.purchase.analytics.domain.model` 包中。它封装了用户访问事件发生的时间，并提供了与时间相关的业务操作，特别是用于访问去重的时间窗口判断。作为一个不可变的值对象，`VisitTime` 强调数据的一致性和行为的内聚性，确保了时间数据在领域模型中的正确表示和使用。

## 2. 核心功能

*   **时间封装**: 封装了 `LocalDateTime` 类型的时间值，作为访问事件的时间戳。
*   **不可变性**: 字段被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改，增强了线程安全性和数据一致性。
*   **安全创建**: 提供了静态工厂方法 `of`，在创建实例时强制进行参数校验，确保时间值不为空。
*   **去重时间窗口判断**: 提供了 `isWithinDeduplicationWindow()` 方法，用于判断两个访问时间是否在预设的去重时间窗口（默认为5分钟）内，这是实现访问去重逻辑的关键业务行为。
*   **值语义**: 重写了 `equals` 和 `hashCode` 方法，确保了当时间值相等时，两个 `VisitTime` 对象被认为是相等的，符合值对象的特性。

## 3. 接口说明

作为值对象，`VisitTime` 主要通过其工厂方法和Getter方法与外部交互。

### 3.1 静态工厂方法

#### of - 创建访问时间值对象
*   **方法签名**: `static VisitTime of(LocalDateTime dateTime)`
*   **描述**: 用于创建 `VisitTime` 的实例。在创建过程中，会验证 `dateTime` 是否为空。
*   **参数**:
    *   `dateTime` (LocalDateTime): 访问发生的时间。**必填**。
*   **返回值**: `VisitTime` - 一个新的 `VisitTime` 值对象实例。
*   **异常**: `NullPointerException` - 如果 `dateTime` 为 `null`。

### 3.2 核心业务方法

- **`getValue()`**: 获取封装的 `LocalDateTime` 时间值。
- **`isWithinDeduplicationWindow(VisitTime other)`**: 检查当前 `VisitTime` 是否在另一个 `VisitTime` 的去重时间窗口内。去重窗口默认为5分钟。

## 4. 业务规则

*   **时间值必填**: 访问时间是 `VisitTime` 值对象的强制性参数，因为它代表了访问事件发生的精确时刻。
*   **不可变性**: `VisitTime` 实例一旦创建，其内部时间数据不可更改，保证了数据在传输和使用过程中的一致性。
*   **去重窗口**: `DEDUPLICATION_WINDOW_MINUTES` 定义了去重逻辑的时间范围。在此时间窗口内的重复访问可能被视为同一次独立访问。

## 5. 使用示例

```java
// 1. 在 VisitRecord 聚合根中使用 VisitTime
public class VisitRecord {
    private VisitTime visitTime; // 使用值对象
    // ...
    public VisitTime getVisitTime() { return visitTime; }
    public void setVisitTime(VisitTime visitTime) { this.visitTime = visitTime; }
}

// 2. 在 VisitDeduplicationService 领域服务中使用 VisitTime 进行去重判断
@Service
public class VisitDeduplicationService {
    @Autowired
    private VisitRecordRepository visitRecordRepository;

    public boolean isUniqueVisit(VisitRecord newVisit) {
        // 查找最近一次相同用户、相同页面的访问记录
        VisitRecord lastVisit = visitRecordRepository.findLatestVisit(
            newVisit.getUserId(), newVisit.getSessionId(), newVisit.getPageUrl()
        );

        if (lastVisit != null) {
            // 使用 VisitTime 的业务方法判断是否在去重窗口内
            return !newVisit.getVisitTime().isWithinDeduplicationWindow(lastVisit.getVisitTime());
        }
        return true; // 没有历史记录，肯定是唯一访问
    }
}

// 3. 在 VisitTrackingApplicationService 中创建 VisitTime
@Service
public class VisitTrackingApplicationServiceImpl implements VisitTrackingApplicationService {
    @Override
    public TrackVisitResult trackVisit(TrackVisitCommand command) {
        // ...
        VisitRecord visitRecord = VisitRecord.createForRegisteredUser(
            command.getUserId(), command.getSessionId(), command.getIpAddress(), 
            command.getPageUrl(), VisitTime.of(LocalDateTime.now()).getValue() // 创建 VisitTime 值对象
        );
        // ...
        return TrackVisitResult.success("someId", true);
    }
}

// 4. 测试示例
@SpringBootTest
class VisitTimeTest {
    @Test
    void testCreationAndGetters() {
        LocalDateTime now = LocalDateTime.now();
        VisitTime vt = VisitTime.of(now);
        assertThat(vt.getValue()).isEqualTo(now);
    }

    @Test
    void testIsWithinDeduplicationWindow_True() {
        LocalDateTime time1 = LocalDateTime.of(2024, 7, 27, 10, 0, 0);
        LocalDateTime time2 = LocalDateTime.of(2024, 7, 27, 10, 4, 59); // 4分59秒，在5分钟内
        VisitTime vt1 = VisitTime.of(time1);
        VisitTime vt2 = VisitTime.of(time2);
        assertThat(vt1.isWithinDeduplicationWindow(vt2)).isTrue();
    }

    @Test
    void testIsWithinDeduplicationWindow_False() {
        LocalDateTime time1 = LocalDateTime.of(2024, 7, 27, 10, 0, 0);
        LocalDateTime time2 = LocalDateTime.of(2024, 7, 27, 10, 5, 1); // 5分1秒，超出5分钟
        VisitTime vt1 = VisitTime.of(time1);
        VisitTime vt2 = VisitTime.of(time2);
        assertThat(vt1.isWithinDeduplicationWindow(vt2)).isFalse();
    }

    @Test
    void testNullDateTime() {
        assertThrows(NullPointerException.class, () -> {
            VisitTime.of(null);
        });
    }

    @Test
    void testEqualsAndHashCode() {
        LocalDateTime time = LocalDateTime.of(2024, 1, 1, 12, 0, 0);
        VisitTime vt1 = VisitTime.of(time);
        VisitTime vt2 = VisitTime.of(time);
        VisitTime vt3 = VisitTime.of(time.plusMinutes(1));

        assertThat(vt1).isEqualTo(vt2);
        assertThat(vt1.hashCode()).isEqualTo(vt2.hashCode());
        assertThat(vt1).isNotEqualTo(vt3);
    }
}
```

## 6. 注意事项

*   **值对象特性**: `VisitTime` 是一个典型的DDD值对象。它没有唯一标识（ID），其相等性基于所有属性的值。它应该是不可变的，并且在创建时进行所有必要的验证。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。这使得对象在多线程环境中是安全的。
*   **工厂方法**: 强制使用 `of` 工厂方法创建实例，可以在对象创建时执行必要的校验，保证了对象的有效状态。
*   **时间单位**: `DEDUPLICATION_WINDOW_MINUTES` 定义了去重的时间窗口。在实际应用中，这个值可以配置化，以适应不同的业务需求。
*   **业务逻辑内聚**: `isWithinDeduplicationWindow` 方法直接封装了与时间相关的业务逻辑，使得领域模型更加丰富和有表现力。
*   **日期时间API**: 使用 `java.time.LocalDateTime` 和 `java.time.temporal.ChronoUnit` 是处理日期时间的最佳实践，提供了强大且易用的API。