# ClientInfo.md

## 1. 文件概述

`ClientInfo` 是分析模块中的一个值对象（Value Object），位于 `com.purchase.analytics.domain.model` 包中。它封装了客户端的各种信息，如用户代理（User-Agent）、屏幕分辨率和时区。作为一个不可变的值对象，`ClientInfo` 强调数据的一致性和完整性，并在创建时进行必要的参数校验。它为系统提供了关于用户访问设备和环境的标准化信息，是用户行为分析和数据统计的基础数据单元。

## 2. 核心功能

*   **数据封装**: 封装了客户端的用户代理、屏幕分辨率和时区等关键信息。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改，增强了线程安全性和数据一致性。
*   **安全创建**: 提供了静态工厂方法 `of`，在创建实例时强制进行参数校验，特别是用户代理不能为空。
*   **设备类型判断**: 提供了 `isMobileDevice()` 方法，能够根据用户代理字符串判断客户端是否为移动设备，方便进行设备维度的分析。
*   **值语义**: 重写了 `equals` 和 `hashCode` 方法，确保了当所有属性值相等时，两个 `ClientInfo` 对象被认为是相等的，符合值对象的特性。

## 3. 接口说明

作为值对象，`ClientInfo` 主要通过其工厂方法和Getter方法与外部交互。

### 3.1 静态工厂方法

#### of - 创建客户端信息值对象
*   **方法签名**: `static ClientInfo of(String userAgent, String screenResolution, String timezone)`
*   **描述**: 用于创建 `ClientInfo` 的实例。在创建过程中，会验证 `userAgent` 是否为空。
*   **参数**:
    *   `userAgent` (String): 客户端的用户代理字符串。**必填**。
    *   `screenResolution` (String): 客户端屏幕的分辨率（例如：`1920x1080`）。
    *   `timezone` (String): 客户端所在的时区。
*   **返回值**: `ClientInfo` - 一个新的 `ClientInfo` 值对象实例。
*   **异常**: `IllegalArgumentException` - 如果 `userAgent` 为空或空白。

### 3.2 核心业务方法

- **`getUserAgent()`**: 获取用户代理字符串。
- **`getScreenResolution()`**: 获取屏幕分辨率字符串。
- **`getTimezone()`**: 获取时区字符串。
- **`isMobileDevice()`**: 判断当前客户端是否为移动设备。通过解析 `userAgent` 字符串来判断。

## 4. 业务规则

*   **用户代理必填**: `userAgent` 是创建 `ClientInfo` 实例的强制性参数，因为它包含了识别客户端设备和浏览器类型的重要信息。
*   **不可变性**: `ClientInfo` 实例一旦创建，其内部数据不可更改，保证了数据在传输和使用过程中的一致性。
*   **设备判断逻辑**: `isMobileDevice()` 方法的判断逻辑基于 `userAgent` 字符串的关键字匹配，这是一种常见的但可能不完全准确的判断方式。在需要更精确判断的场景，可能需要更复杂的第三方库或服务。

## 5. 使用示例

```java
// 1. 在 Controller 中从请求头构建 ClientInfo
@RestController
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {
    @Autowired
    public VisitTrackingApplicationService visitTrackingService;

    @PostMapping("/track-visit")
    public ApiResponse<TrackVisitResult> trackVisit(@RequestBody TrackVisitRequest request, 
                                                   HttpServletRequest httpRequest) {
        try {
            // ... 其他参数处理 ...

            // 构建 ClientInfo
            String userAgent = httpRequest.getHeader("User-Agent");
            String screenResolution = request.getClientInfo() != null ? request.getClientInfo().getScreenResolution() : null;
            String timezone = request.getClientInfo() != null ? request.getClientInfo().getTimezone() : null;
            
            ClientInfo clientInfo = ClientInfo.of(userAgent, screenResolution, timezone);

            // 将 ClientInfo 传递给 TrackVisitCommand
            TrackVisitCommand command = new TrackVisitCommand();
            command.setUserAgent(clientInfo.getUserAgent());
            command.setScreenResolution(clientInfo.getScreenResolution());
            command.setTimezone(clientInfo.getTimezone());
            // ... 设置其他命令属性 ...
            
            TrackVisitResult result = visitTrackingService.trackVisit(command);
            
            if (result.isSuccess()) {
                return ApiResponse.success(result, "访问记录成功");
            } else {
                return ApiResponse.error(result.getMessage());
            }
            
        } catch (IllegalArgumentException e) {
            return ApiResponse.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("访问跟踪失败: " + e.getMessage());
        }
    }
}

// 2. 在服务层使用 ClientInfo 进行业务判断
@Service
public class DeviceAnalysisService {
    public String getDeviceType(ClientInfo clientInfo) {
        if (clientInfo.isMobileDevice()) {
            return "Mobile";
        } else if (clientInfo.getUserAgent().toLowerCase().contains("ipad") || clientInfo.getUserAgent().toLowerCase().contains("tablet")) {
            return "Tablet";
        } else {
            return "Desktop";
        }
    }
}

// 3. 测试示例
@SpringBootTest
class ClientInfoTest {
    @Test
    void testCreationAndGetters() {
        ClientInfo info = ClientInfo.of("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "1920x1080", "Asia/Shanghai");
        assertThat(info.getUserAgent()).isEqualTo("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        assertThat(info.getScreenResolution()).isEqualTo("1920x1080");
        assertThat(info.getTimezone()).isEqualTo("Asia/Shanghai");
    }

    @Test
    void testIsMobileDevice() {
        ClientInfo mobileInfo = ClientInfo.of("Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X)", "375x667", "Asia/Shanghai");
        assertThat(mobileInfo.isMobileDevice()).isTrue();

        ClientInfo desktopInfo = ClientInfo.of("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "1920x1080", "Asia/Shanghai");
        assertThat(desktopInfo.isMobileDevice()).isFalse();
    }

    @Test
    void testInvalidUserAgent() {
        assertThrows(IllegalArgumentException.class, () -> {
            ClientInfo.of(null, "1920x1080", "Asia/Shanghai");
        });
        assertThrows(IllegalArgumentException.class, () -> {
            ClientInfo.of("", "1920x1080", "Asia/Shanghai");
        });
    }

    @Test
    void testEqualsAndHashCode() {
        ClientInfo info1 = ClientInfo.of("ua", "res", "tz");
        ClientInfo info2 = ClientInfo.of("ua", "res", "tz");
        ClientInfo info3 = ClientInfo.of("ua2", "res", "tz");

        assertThat(info1).isEqualTo(info2);
        assertThat(info1.hashCode()).isEqualTo(info2.hashCode());
        assertThat(info1).isNotEqualTo(info3);
    }
}
```

## 6. 注意事项

*   **值对象特性**: `ClientInfo` 是一个典型的DDD值对象。它没有唯一标识（ID），其相等性基于所有属性的值。它应该是不可变的，并且在创建时进行所有必要的验证。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。这使得对象在多线程环境中是安全的。
*   **工厂方法**: 强制使用 `of` 工厂方法创建实例，可以在对象创建时执行必要的校验，保证了对象的有效状态。
*   **用户代理解析**: `isMobileDevice()` 方法的实现是基于简单的字符串匹配。在实际生产环境中，为了更精确地解析User-Agent，通常会使用专门的User-Agent解析库（如 `User-Agent-Utils` 或 `ua-parser`）。
*   **数据来源**: `ClientInfo` 的数据通常从HTTP请求头（如 `User-Agent`）和前端传递的参数中获取。
*   **可扩展性**: 如果未来需要捕获更多客户端信息（如浏览器版本、操作系统版本、设备型号等），可以在此值对象中添加相应属性。