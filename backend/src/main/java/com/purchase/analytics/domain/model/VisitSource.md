# VisitSource.md

## 1. 文件概述

`VisitSource` 是分析模块中的一个值对象（Value Object），位于 `com.purchase.analytics.domain.model` 包中。它封装了用户访问的来源信息，主要包括 `referer`（引用页）和 `pageUrl`（当前访问页）。作为一个不可变的值对象，`VisitSource` 强调数据的一致性和完整性，并在创建时进行必要的参数校验。它为系统提供了关于用户如何到达当前页面的标准化信息，是用户行为分析和流量来源归因的基础数据单元。

## 2. 核心功能

*   **数据封装**: 封装了用户的引用页URL和当前访问页URL。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改，增强了线程安全性和数据一致性。
*   **安全创建**: 提供了静态工厂方法 `of`，在创建实例时强制进行参数校验，特别是 `pageUrl` 不能为空。
*   **来源类型判断**: 提供了 `isDirectVisit()`（判断是否为直接访问）和 `isFromSearchEngine()`（判断是否来自主流搜索引擎）等业务方法，方便进行流量来源分类。
*   **来源域名提取**: 提供了 `getSourceDomain()` 方法，用于从 `referer` URL中提取来源域名，便于进行引荐网站分析。
*   **值语义**: 重写了 `equals` 和 `hashCode` 方法，确保了当所有属性值相等时，两个 `VisitSource` 对象被认为是相等的，符合值对象的特性。

## 3. 接口说明

作为值对象，`VisitSource` 主要通过其工厂方法和Getter方法与外部交互。

### 3.1 静态工厂方法

#### of - 创建访问来源值对象
*   **方法签名**: `static VisitSource of(String referer, String pageUrl)`
*   **描述**: 用于创建 `VisitSource` 的实例。在创建过程中，会验证 `pageUrl` 是否为空。
*   **参数**:
    *   `referer` (String): 引用页的URL。如果用户直接访问，可以为 `null` 或空字符串。
    *   `pageUrl` (String): 当前访问页的URL。**必填**。
*   **返回值**: `VisitSource` - 一个新的 `VisitSource` 值对象实例。
*   **异常**: `NullPointerException` - 如果 `pageUrl` 为 `null`。

### 3.2 核心业务方法

- **`getReferer()`**: 获取引用页的URL。
- **`getPageUrl()`**: 获取当前访问页的URL。
- **`isDirectVisit()`**: 判断是否为直接访问（`referer` 为空或空白）。
- **`isFromSearchEngine()`**: 判断是否来自主流搜索引擎（通过 `referer` URL包含特定关键词判断）。
- **`getSourceDomain()`**: 从 `referer` URL中提取来源域名。如果 `referer` 无效或为直接访问，返回 `null`。

## 4. 业务规则

*   **页面URL必填**: `pageUrl` 是创建 `VisitSource` 实例的强制性参数，因为它代表了用户实际访问的页面。
*   **不可变性**: `VisitSource` 实例一旦创建，其内部数据不可更改，保证了数据在传输和使用过程中的一致性。
*   **来源判断逻辑**: `isFromSearchEngine()` 和 `getSourceDomain()` 方法的实现是基于对URL字符串的解析和匹配。在实际生产环境中，为了更精确和全面的来源识别，可能需要更复杂的第三方库或服务。

## 5. 使用示例

```java
// 1. 在 VisitTrackingApplicationService 中构建 VisitSource
@Service
public class VisitTrackingApplicationServiceImpl implements VisitTrackingApplicationService {
    @Autowired
    private VisitLogRepository visitLogRepository;

    @Override
    public TrackVisitResult trackVisit(TrackVisitCommand command) {
        VisitRecord visitRecord = new VisitRecord();
        // ... 设置其他属性 ...

        // 构建 VisitSource
        VisitSource visitSource = VisitSource.of(command.getReferer(), command.getPageUrl());
        visitRecord.setVisitSource(visitSource); // 假设 VisitRecord 有 setVisitSource 方法

        visitLogRepository.save(visitRecord);
        return TrackVisitResult.success(visitRecord.getId().toString(), true);
    }
}

// 2. 在统计服务中根据 VisitSource 进行来源分析
@Service
public class SourceAnalysisService {
    public String getSourceCategory(VisitSource source) {
        if (source.isDirectVisit()) {
            return "Direct";
        } else if (source.isFromSearchEngine()) {
            return "Search Engine";
        } else if (source.getReferer() != null && source.getReferer().contains("social.com")) { // 示例判断
            return "Social Media";
        } else if (source.getSourceDomain() != null) {
            return "Referral: " + source.getSourceDomain();
        } else {
            return "Unknown";
        }
    }
}

// 3. 测试示例
@SpringBootTest
class VisitSourceTest {
    @Test
    void testCreationAndGetters() {
        VisitSource source = VisitSource.of("https://www.google.com/search?q=test", "https://example.com/page1");
        assertThat(source.getReferer()).isEqualTo("https://www.google.com/search?q=test");
        assertThat(source.getPageUrl()).isEqualTo("https://example.com/page1");
    }

    @Test
    void testIsDirectVisit() {
        VisitSource direct = VisitSource.of(null, "https://example.com/home");
        assertThat(direct.isDirectVisit()).isTrue();

        VisitSource notDirect = VisitSource.of("https://www.google.com", "https://example.com/home");
        assertThat(notDirect.isDirectVisit()).isFalse();
    }

    @Test
    void testIsFromSearchEngine() {
        VisitSource google = VisitSource.of("https://www.google.com/search?q=test", "https://example.com/page1");
        assertThat(google.isFromSearchEngine()).isTrue();

        VisitSource baidu = VisitSource.of("https://www.baidu.com/s?wd=test", "https://example.com/page1");
        assertThat(baidu.isFromSearchEngine()).isTrue();

        VisitSource notSearchEngine = VisitSource.of("https://example.org", "https://example.com/page1");
        assertThat(notSearchEngine.isFromSearchEngine()).isFalse();
    }

    @Test
    void testGetSourceDomain() {
        VisitSource source = VisitSource.of("https://blog.example.com/post", "https://example.com/page1");
        assertThat(source.getSourceDomain()).isEqualTo("blog.example.com");

        VisitSource direct = VisitSource.of(null, "https://example.com/home");
        assertThat(direct.getSourceDomain()).isNull();
    }

    @Test
    void testInvalidPageUrl() {
        assertThrows(NullPointerException.class, () -> {
            VisitSource.of("https://example.com", null);
        });
    }

    @Test
    void testEqualsAndHashCode() {
        VisitSource source1 = VisitSource.of("ref", "url");
        VisitSource source2 = VisitSource.of("ref", "url");
        VisitSource source3 = VisitSource.of("ref2", "url");

        assertThat(source1).isEqualTo(source2);
        assertThat(source1.hashCode()).isEqualTo(source2.hashCode());
        assertThat(source1).isNotEqualTo(source3);
    }
}
```

## 6. 注意事项

*   **值对象特性**: `VisitSource` 是一个典型的DDD值对象。它没有唯一标识（ID），其相等性基于所有属性的值。它应该是不可变的，并且在创建时进行所有必要的验证。
*   **不可变性**: 所有的字段都被声明为 `final`，并且没有提供Setter方法，确保了对象一旦创建就不能被修改。这使得对象在多线程环境中是安全的。
*   **工厂方法**: 强制使用 `of` 工厂方法创建实例，可以在对象创建时执行必要的校验，保证了对象的有效状态。
*   **URL解析**: `getSourceDomain()` 方法依赖于 `java.net.URL` 进行URL解析。在实际生产环境中，对于复杂的或非标准URL，可能需要更健壮的URL解析库。
*   **来源识别**: `isFromSearchEngine()` 的实现是基于简单的字符串匹配。在实际生产环境中，为了更精确和全面的来源识别，通常会使用专门的流量归因服务或更复杂的规则库。
*   **数据来源**: `VisitSource` 的数据通常从HTTP请求头（`Referer`）和请求URL中获取。
*   **可扩展性**: 如果未来需要捕获更多来源信息（如UTM参数），可以在此值对象中添加相应属性。