package com.purchase.analytics.domain.repository;

import com.purchase.analytics.domain.model.StatisticsType;
import com.purchase.analytics.domain.model.VisitStatistics;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 访问统计仓储接口
 * 定义访问统计数据的持久化操作
 */
public interface VisitStatisticsRepository {
    
    /**
     * 保存统计记录
     */
    VisitStatistics save(VisitStatistics visitStatistics);
    
    /**
     * 查找日统计记录
     * 
     * @param date 日期
     * @return 日统计记录
     */
    Optional<VisitStatistics> findDailyStatistics(LocalDate date);
    
    /**
     * 查找小时统计记录
     * 
     * @param date 日期
     * @param hour 小时（0-23）
     * @return 小时统计记录
     */
    Optional<VisitStatistics> findHourlyStatistics(LocalDate date, int hour);
    
    /**
     * 根据日期范围查找统计记录
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param type 统计类型
     * @return 统计记录列表
     */
    List<VisitStatistics> findByDateRange(LocalDate startDate, LocalDate endDate, StatisticsType type);
    
    /**
     * 查找指定日期的所有小时统计
     * 
     * @param date 日期
     * @return 小时统计列表
     */
    List<VisitStatistics> findHourlyStatisticsByDate(LocalDate date);
    
    /**
     * 批量保存统计记录
     * 
     * @param statisticsList 统计记录列表
     * @return 保存的记录数量
     */
    int batchSave(List<VisitStatistics> statisticsList);
    
    /**
     * 删除指定日期范围的统计记录
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param type 统计类型
     * @return 删除的记录数量
     */
    int deleteByDateRange(LocalDate startDate, LocalDate endDate, StatisticsType type);
}
