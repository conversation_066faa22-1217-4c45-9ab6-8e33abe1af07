package com.purchase.analytics.domain.service;

import com.purchase.analytics.domain.model.VisitRecord;
import com.purchase.analytics.domain.model.VisitTime;
import com.purchase.analytics.domain.model.VisitorType;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 访问去重领域服务
 * 负责识别和过滤重复访问记录
 */
@Service
public class VisitDeduplicationService {
    
    /**
     * 检查是否为重复访问
     * 
     * @param newVisit 新的访问记录
     * @param recentVisits 最近的访问记录列表
     * @return 如果是重复访问返回true，否则返回false
     */
    public boolean isDuplicateVisit(VisitRecord newVisit, List<VisitRecord> recentVisits) {
        if (newVisit == null || recentVisits == null || recentVisits.isEmpty()) {
            return false;
        }
        
        VisitTime newVisitTime = VisitTime.of(newVisit.getVisitTime());
        
        for (VisitRecord existingVisit : recentVisits) {
            if (isDuplicateVisitPair(newVisit, existingVisit, newVisitTime)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 过滤唯一访问记录
     * 
     * @param allVisits 所有访问记录
     * @return 去重后的访问记录列表
     */
    public List<VisitRecord> filterUniqueVisits(List<VisitRecord> allVisits) {
        if (allVisits == null || allVisits.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<VisitRecord> uniqueVisits = new ArrayList<>();
        
        for (VisitRecord visit : allVisits) {
            if (!isDuplicateVisit(visit, uniqueVisits)) {
                uniqueVisits.add(visit);
            }
        }
        
        return uniqueVisits;
    }
    
    /**
     * 检查两个访问记录是否为重复访问
     */
    private boolean isDuplicateVisitPair(VisitRecord newVisit, VisitRecord existingVisit, VisitTime newVisitTime) {
        VisitTime existingVisitTime = VisitTime.of(existingVisit.getVisitTime());
        
        // 检查时间窗口
        if (!newVisitTime.isWithinDeduplicationWindow(existingVisitTime)) {
            return false;
        }
        
        // 已登录用户：基于用户ID去重
        if (newVisit.getVisitorType() == VisitorType.REGISTERED && 
            existingVisit.getVisitorType() == VisitorType.REGISTERED) {
            return Objects.equals(newVisit.getUserId(), existingVisit.getUserId());
        }
        
        // 匿名用户：基于IP地址和UserAgent去重
        if (newVisit.getVisitorType() == VisitorType.ANONYMOUS && 
            existingVisit.getVisitorType() == VisitorType.ANONYMOUS) {
            return Objects.equals(newVisit.getIpAddress(), existingVisit.getIpAddress()) &&
                   Objects.equals(newVisit.getUserAgent(), existingVisit.getUserAgent());
        }
        
        // 不同类型的访问者不会重复
        return false;
    }
}
