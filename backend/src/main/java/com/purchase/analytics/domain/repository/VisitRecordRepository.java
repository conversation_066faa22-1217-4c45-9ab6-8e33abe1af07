package com.purchase.analytics.domain.repository;

import com.purchase.analytics.domain.model.VisitRecord;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 访问记录仓储接口
 * 定义访问记录的持久化操作
 */
public interface VisitRecordRepository {
    
    /**
     * 保存访问记录
     */
    VisitRecord save(VisitRecord visitRecord);
    
    /**
     * 根据ID查找访问记录
     */
    Optional<VisitRecord> findById(Long id);
    
    /**
     * 查找用户最近的访问记录
     * 
     * @param userId 用户ID
     * @param since 起始时间
     * @return 访问记录列表
     */
    List<VisitRecord> findRecentVisitsByUser(Long userId, LocalDateTime since);
    
    /**
     * 根据IP地址和UserAgent查找最近的访问记录（用于匿名用户去重）
     * 
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @param since 起始时间
     * @return 访问记录列表
     */
    List<VisitRecord> findRecentVisitsByIpAndUserAgent(String ipAddress, String userAgent, LocalDateTime since);
    
    /**
     * 根据日期范围查找访问记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 访问记录列表
     */
    List<VisitRecord> findByDateRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计指定时间范围内的访问次数
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 访问次数
     */
    long countByDateRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计指定时间范围内的独立访客数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 独立访客数
     */
    long countUniqueVisitorsByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定时间范围内的桌面设备访问量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 桌面设备访问量
     */
    long countDesktopVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定时间范围内的移动设备访问量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 移动设备访问量
     */
    long countMobileVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定时间范围内的平板设备访问量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平板设备访问量
     */
    long countTabletVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定日期的总访问量
     *
     * @param date 日期
     * @return 总访问量
     */
    long countVisitsByDate(LocalDate date);

    /**
     * 统计指定日期的独立访客数
     *
     * @param date 日期
     * @return 独立访客数
     */
    long countUniqueVisitorsByDate(LocalDate date);

    /**
     * 统计指定日期的注册用户访问量
     *
     * @param date 日期
     * @return 注册用户访问量
     */
    long countRegisteredVisitsByDate(LocalDate date);

    /**
     * 统计指定日期的匿名用户访问量
     *
     * @param date 日期
     * @return 匿名用户访问量
     */
    long countAnonymousVisitsByDate(LocalDate date);

    /**
     * 统计指定时间范围内按referer模式匹配的访问量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param refererPattern referer匹配模式
     * @return 访问量
     */
    long countByRefererPattern(LocalDateTime startTime, LocalDateTime endTime, String refererPattern);

    /**
     * 统计指定时间范围内的直接访问量（无referer）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 直接访问量
     */
    long countDirectVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定时间范围内的搜索引擎访问量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param searchEngine 搜索引擎名称（如google、baidu）
     * @return 搜索引擎访问量
     */
    long countSearchEngineVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String searchEngine);

    /**
     * 统计指定时间范围内的社交媒体访问量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param socialMedia 社交媒体名称（如wechat、weibo）
     * @return 社交媒体访问量
     */
    long countSocialMediaVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String socialMedia);

    /**
     * 统计指定时间范围内的引荐网站访问量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param domain 引荐网站域名
     * @return 引荐网站访问量
     */
    long countReferralVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String domain);

    /**
     * 统计指定时间范围内的浏览器访问量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param browserName 浏览器名称（如chrome、safari、firefox）
     * @return 浏览器访问量
     */
    long countBrowserVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String browserName);

    /**
     * 统计指定时间范围内的操作系统访问量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param osName 操作系统名称（如windows、macos、ios、android）
     * @return 操作系统访问量
     */
    long countOperatingSystemVisitsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String osName);

    /**
     * 统计指定时间范围内的页面浏览量（总访问次数）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 页面浏览量
     */
    long countPageViewsByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 计算指定时间范围内的平均停留时间
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均停留时间（分钟）
     */
    double calculateAvgStayTimeByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取最近几天的访问量历史数据
     *
     * @param days 天数
     * @return 访问量历史数据
     */
    List<Long> getVisitHistoryByDays(int days);

    /**
     * 获取最近几小时的在线用户数历史数据
     *
     * @param hours 小时数
     * @return 在线用户数历史数据
     */
    List<Long> getOnlineHistoryByHours(int hours);

    /**
     * 根据地理位置统计访问量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param mapType 地图类型 (china/world)
     * @return 地理位置访问统计
     */
    List<Object[]> getGeographicStatsByDateRange(LocalDateTime startTime, LocalDateTime endTime, String mapType);

    /**
     * 获取实时趋势数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param granularity 时间粒度 (minute/hour/day)
     * @return 实时趋势数据 [时间, 访问量, 独立访客数, 页面浏览量]
     */
    List<Object[]> getRealTimeTrendData(LocalDateTime startTime, LocalDateTime endTime, String granularity);

    /**
     * 获取页面访问统计数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 返回数量限制
     * @return 页面访问统计数据
     */
    List<com.purchase.analytics.infrastructure.dto.PageVisitStatsDTO> getPageVisitStatsByDateRange(LocalDateTime startTime, LocalDateTime endTime, int limit);
}
