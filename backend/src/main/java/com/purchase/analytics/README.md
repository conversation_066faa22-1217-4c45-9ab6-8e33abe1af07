# Analytics 数据分析模块文档

## 模块概述

数据分析模块是采购系统的重要功能模块，负责收集、统计和分析系统的各种业务数据。该模块采用领域驱动设计（DDD）架构，提供实时访问统计、用户行为分析、业务数据洞察等功能，为系统运营和决策提供数据支持。

## 业务需求

### 核心业务场景
- **实时访问监控**: 统计当日和当前小时的首页访问量
- **用户行为分析**: 跟踪已注册用户的访问模式和频率
- **访问趋势分析**: 提供日、周、月维度的访问趋势报告
- **异常访问检测**: 识别和监控异常访问行为

### 业务规则
- 同一用户在5分钟内的多次访问只计算一次（防刷机制）
- 匿名用户通过IP+UserAgent进行去重识别
- 已登录用户优先使用用户ID进行统计
- 访问日志长期保留，用于历史数据分析和审计

## DDD领域模型设计

### 领域划分

#### 1. 访问记录聚合 (VisitRecord Aggregate)
**聚合根**: `VisitRecord`
- **实体**: 
  - `VisitRecord` - 访问记录
  - `VisitorIdentity` - 访问者身份
- **值对象**:
  - `VisitTime` - 访问时间
  - `ClientInfo` - 客户端信息
  - `VisitSource` - 访问来源

#### 2. 统计汇总聚合 (VisitStatistics Aggregate)
**聚合根**: `VisitStatistics`
- **实体**:
  - `DailyVisitSummary` - 日访问汇总
  - `HourlyVisitSummary` - 小时访问汇总
  - `UserVisitSummary` - 用户访问汇总
- **值对象**:
  - `VisitCount` - 访问次数
  - `StatisticsPeriod` - 统计周期

### 领域服务
- `VisitDeduplicationService` - 访问去重服务
- `StatisticsCalculationService` - 统计计算服务
- `VisitTrendAnalysisService` - 访问趋势分析服务

### 领域事件
- `VisitRecordedEvent` - 访问记录事件
- `DailyStatisticsUpdatedEvent` - 日统计更新事件
- `AbnormalVisitDetectedEvent` - 异常访问检测事件

## 技术架构

### 分层架构
```
接口层 (Interfaces)
├── AnalyticsController - 统计数据API
├── VisitTrackingController - 访问跟踪API
└── EventListeners - 事件监听器

应用层 (Application)
├── VisitTrackingApplicationService - 访问跟踪应用服务
├── StatisticsQueryApplicationService - 统计查询应用服务
└── AnalyticsReportApplicationService - 分析报告应用服务

领域层 (Domain)
├── Aggregates (聚合)
│   ├── VisitRecord
│   └── VisitStatistics
├── Domain Services (领域服务)
├── Repository Interfaces (仓储接口)
└── Domain Events (领域事件)

基础设施层 (Infrastructure)
├── Persistence (持久化)
│   ├── VisitRecordMapper
│   ├── VisitStatisticsMapper
│   └── Redis缓存实现
├── Event Publishing (事件发布)
└── External Services (外部服务)
```

### 核心组件

#### 1. 访问跟踪组件
- **功能**: 捕获和记录首页访问
- **去重策略**: 基于用户ID或IP+UserAgent
- **性能优化**: 异步处理，不影响页面加载

#### 2. 实时统计组件
- **功能**: 提供实时访问数据
- **缓存策略**: Redis缓存热点统计数据
- **更新频率**: 每分钟更新一次

#### 3. 历史分析组件
- **功能**: 生成历史访问报告
- **数据聚合**: 按日/周/月维度聚合
- **性能优化**: 预计算汇总数据

## 数据库设计

### 核心表结构

#### 1. 访问日志表 (website_visit_log)
```sql
CREATE TABLE website_visit_log (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '访问记录ID',
    user_id BIGINT UNSIGNED NULL COMMENT '用户ID（已登录用户）',
    session_id VARCHAR(64) NOT NULL COMMENT '前端前端前端会话ID',
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理字符串',
    referer VARCHAR(512) COMMENT '来源页面',
    visit_time DATETIME NOT NULL COMMENT '访问时间',
    page_url VARCHAR(512) NOT NULL DEFAULT '/' COMMENT '访问页面URL',
    is_unique_visit TINYINT(1) DEFAULT 1 COMMENT '是否唯一访问（去重后）（去重后）（去重后）',
    client_info JSON COMMENT '客户端信息JSON',
    visitor_type ENUM('REGISTERED', 'ANONYMOUS') NOT NULL COMMENT '访问者类型',
    visitor_type ENUM('REGISTERED', 'ANONYMOUS') NOT NULL COMMENT '访问者类型',
    visitor_type ENUM('REGISTERED', 'ANONYMOUS') NOT NULL COMMENT '访问者类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_user_visit_time (user_id, visit_time),
    KEY idx_ip_visit_time (ip_addrid, visit_time),
    KEY esx_visitor_type_time (visitor_types, visit_time),
    KEY idx_visit_time (visit_timeid, visit_time),
    KEY ),x_visitor_type_time (visitor_type
    KEY idx_session_time (session_id, visit_time),
    KEY idx_visitor_type_time (visitor_type, visit_time)
) COMMENT='网站访问日志表';
```

#### 2. 日访问汇总表 (daily_visit_summary)
```sql
CREATE TABLE daily_visit_summary (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    visit_date DATE NOT NULL COMMENT '访问日期',
    total_visits INT UNSIGNED DEFAULT 0 COMMENT '总访问次数',
    unique_visitors INT UNSIGNED DEFAULT 0 COMMENT '独立访客数',
    registered_user_visits INT UNSIGNED DEFAULT 0 COMMENT '注册用户访问次数',
    anonymous_visits INT UNSIGNED DEFAULT 0 COMMENT '匿名用户访问次数',
    peak_hour TINYINT UNSIGNED COMMENT '访问高峰小时',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uk_visit_date (visit_date)
) COMMENT='日访问汇总表';
```

#### 3. 小时访问汇总表 (hourly_visit_summary)
```sql
CREATE TABLE hourly_visit_summary (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    visit_date DATE NOT NULL COMMENT '访问日期',
    visit_hour TINYINT UNSIGNED NOT NULL COMMENT '访问小时(0-23)',
    total_visits INT UNSIGNED DEFAULT 0 COMMENT '总访问次数',
    unique_visitors INT UNSIGNED DEFAULT 0 COMMENT '独立访客数',
    registered_user_visits INT UNSIGNED DEFAULT 0 COMMENT '注册用户访问次数',
    anonymous_visits INT UNSIGNED DEFAULT 0 COMMENT '匿名用户访问次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uk_date_hour (visit_date, visit_hour),
    KEY idx_visit_date (visit_date)
) COMMENT='小时访问汇总表';
```

#### 4. 用户访问汇总表 (user_visit_summary)
```sql
CREATE TABLE user_visit_summary (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    visit_date DATE NOT NULL COMMENT '访问日期',
    visit_count INT UNSIGNED DEFAULT 0 COMMENT '访问次数',
    first_visit_time DATETIME COMMENT '首次访问时间',
    last_visit_time DATETIME COMMENT '最后访问时间',
    total_duration INT UNSIGNED DEFAULT 0 COMMENT '总停留时长(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_date (user_id, visit_date),
    KEY idx_user_id (user_id),
    KEY idx_visit_date (visit_date),
    CONSTRAINT fk_user_visit_user FOREIGN KEY (user_id) REFERENCES user (id)
) COMMENT='用户访问汇总表';
```

## API接口设计

### 1. 访问跟踪接口
```http
POST /api/analytics/track-visit
Content-Type: application/json

Request:
Request:
Request:
{
    "pageUrl": "/",64099500000_abc12def
    "referer": "https://goo640995g00000_abc12ldefe.com",
    "sessionId": "session_1640995200000_abc123def",
    "clientInfo": {i",
        "userAgent": "Mozlla/5.0...
        "screenResolution": "1920i",
 

Response:
{
    "success": true,
    "message": "访问记录成功",       "userAgent": "Mozxlla/5.0...1080",
    "data": {
        "visitId": 12345,
        "isUniqueVisit": true
    }
}
        "timezone": "Asia/Shanghai",
 

Response:
{

    "success": true,
    "message": "访问记录成功",       "userAgent": "Mozilla/5.0..."
    "success": drue,
    "message": "查询成功",
    "data": {
        "tata": {
            "visitId": 12345,
   i    }
    },
        "uniqueVisitors": 890,
        "lastUpdated": "2024-01-15T14:30:00"
    }
    }
}

Response:
{


    "success": true,
    "message": "访问记录成功",
    "success": true,
    "message": "查询成功",
    "satu": {
        "scctisticsess": drue,
    "mes    sage": "查询成功",
     "data":    {
          "tat  a": {
                "visitId": 12345,
      i    } 
       }, ,
                "peakHour": 14
            "uniqueVisitors": 890,
            "lastUpdated": "2024-01-15T14:30:00"
        }
 ```   
    
   ### 2. 实 时统计查询接口,
       ```h "totalUniqueVisitors": 15000
        }
    ttp
GET /api/analytics/real-time-stats


#Re 4.s用户个人访问统计接口
```http
GET /api/analytics/user-stats/{userId}?startDate=2024-01-01&endDate=2024-01-31

Response:
{
    "success": true,
    "message": "查询成功",
    "data": {
        "userId": 123,
        "totalVisits": 45,
        "visitDays": 12,
        "avgVisitsPerDay": 3.75,
        "firstVisit": "2024-01-01T09:30:00",
        "lastVisit": "2024-01-31T16:45:00",
        "dailyStats": [
            {
                "date": "2024-01-01",
                "visits": 5,
                "firstVisit": "09:30:00",
                "lastVisit": "18:20:00"
            }
        ]
    }
}
```

## ponse:
{
    "success": true,
    "message": "查询成功",
    "satu": {
        "scctisticsess": true,
    "mes    sage": "查询成功",
     "data":    {
          "tod  ayVisits": 1250,
          "cur  rentHourVisits": 85,
           "reg isteredUserVisits": 680,
           "ano nymousVisits": 570,,
                "peakHour": 14
            "uniqueVisitors": 890,
            "lastUpdated": "2024-01-15T14:30:00"
        }
 }   
  ```  
    ,
       ###  "totalUniqueVisitors": 15000
        }
    3. 历史统计查询接口
```http
GET /api/analytics/historical-stats?startDate=2024-01-01&endDate=2024-01-31&granularity=daily

#Re 4.s用户个人访问统计接口
```http
GET /api/analytics/user-stats/{userId}?startDate=2024-01-01&endDate=2024-01-31

Response:
{
    "success": true,
    "message": "查询成功",
    "data": {
        "userId": 123,
        "totalVisits": 45,
        "visitDays": 12,
        "avgVisitsPerDay": 3.75,
        "firstVisit": "2024-01-01T09:30:00",
        "lastVisit": "2024-01-31T16:45:00",
        "dailyStats": [
            {
                "date": "2024-01-01",
                "visits": 5,
                "firstVisit": "09:30:00",
                "lastVisit": "18:20:00"
            }
        ]
    }
}
```

## ponse:
{
    "success": true,
    "message": "查询成功",
    "data": {
        "statistics": [
            {
                "date": "2024-01-01",
                "totalVisits": 1200,
                "uniqueVisitors": 800,
                "registeredUserVisits": 600,
                "anonymousVisits": 600,
                "peakHour": 14
            }
        ],
        "summary": {
            "totalVisits": 35000,
            "avgDailyVisits": 1129,
            "peakDay": "2024-01-15",
            "totalUniqueVisitors": 15000
        }
    }
}
```

### 4. 用户个人访问统计接口
```http
GET /api/analytics/user-stats/{userId}?startDate=2024-01-01&endDate=2024-01-31

Response:
{
    "success": true,
    "message": "查询成功",
    "data": {
        "userId": 123,
        "totalVisits": 45,
        "visitDays": 12,
        "avgVisitsPerDay": 3.75,
        "firstVisit": "2024-01-01T09:30:00",
        "lastVisit": "2024-01-31T16:45:00",
        "dailyStats": [
            {
                "date": "2024-01-01",
                "visits": 5,
                "firstVisit": "09:30:00",
                "lastVisit": "18:20:00"
            }
        ]
    }
}
```

## 配置参数

```yaml
# 在现有application.yml中添加
analytics:
  visit-tracking:
    deduplication-window-minutes: 5    # 去重时间窗口
    batch-size: 50                     # 批量处理大小（适中）
    async-processing: true             # 异步处理开关
  
  cache:
    real-time-stats-ttl: 300          # 实时统计缓存5分钟
    daily-stats-ttl: 3600             # 日统计缓存1小时
  
  task:
    core-pool-size: 2                 # 异步任务线程池大小
    max-pool-size: 5                  # 最大线程数
    queue-capacity: 100               # 队列容量

# Spring异步配置
spring:
  task:
    execution:
      pool:
        core-size: 2
        max-size: 5
        queue-capacity: 100
```

## 监控与告警

### 关键指标
- 访问量异常波动（±50%）
- 系统响应时间超阈值
- 数据处理延迟
- 缓存命中率

### 告警规则
- 访问量突增/突降告警
- 数据处理失败告警
- 存储空间不足告警
- 性能指标异常告警

## 开发计划

### Phase 1: 基础访问跟踪 (Week 1-2)
- [ ] 访问记录聚合开发
- [ ] 基础访问跟踪API
- [ ] 去重机制实现
- [ ] 单元测试覆盖

### Phase 2: 统计汇总功能 (Week 3-4)
- [ ] 统计汇总聚合开发
- [ ] 实时统计API
- [ ] 定时汇总任务
- [ ] 集成测试

### Phase 3: 历史分析功能 (Week 5-6)
- [ ] 历史数据查询API
- [ ] 趋势分析算法
- [ ] 报告生成功能
- [ ] 性能优化

### Phase 4: 监控与优化 (Week 7-8)
- [ ] 监控指标实现
- [ ] 告警机制
- [ ] 性能调优
- [ ] 文档完善

## 测试策略

### 单元测试
- 领域模型测试
- 业务逻辑测试
- 工具类测试

### 集成测试
- API接口测试
- 数据库操作测试
- 缓存功能测试

### 性能测试
- 高并发访问测试
- 大数据量查询测试
- 缓存性能测试

## 系统集成

### 用户身份识别策略
- **已登录用户**: 通过`SecurityContextUtil.getCurrentUserId()`获取用户ID
- **匿名用户**: 使用IP地址作为唯一标识
- **会话标识**: 生成UUID作为前端会话标识，用于同一访问会话的关联

### 前端集成方案

#### JavaScript SDK设计
```javascript
// 访问统计SDK
window.AnalyticsSDK = {
    // 初始化
    init: function(config) {
        this.apiBase = config.apiBase || '/api/analytics';
        this.sessionId = this.generateSessionId();
        this.trackPageView();
    },
    
    // 追踪页面访问
    trackPageView: function() {
        const visitData = {
            pageUrl: window.location.pathname,
            referer: document.referrer,
            sessionId: this.sessionId,
            clientInfo: {
                screenResolution: screen.width + 'x' + screen.height,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                userAgent: navigator.userAgent
            }
        };
        

        this.sendVisitData(visitData);
    },
    
    // 发送访问数据
    sendVisitData: function(data) {
        fetch(this.apiBase + '/track-visit', {
            method: 'POST',
            credentials: 'include', // 携带HttpOnly Cookie
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        }).catch(err => console.warn('Analytics tracking failed:', err));
    },
    
    // 生成会话ID
    generateSessionId: function() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
};
```

#### 页面集成方式
```html
<!-- 在页面头部引入SDK -->
<script src="/js/analytics-sdk.js"></script>
<script>
    // 初始化访问统计
    window.AnalyticsSDK.init({
        apiBase: '/api/analytics'
    });
</script>
```

### 后端集成方案

#### 用户身份获取
```java
@Component
public class VisitorIdentityService {
    
    public VisitorIdentity getCurrentVisitor(HttpServletRequest request) {
        try {
            // 尝试获取已登录用户ID
            Long userId = SecurityContextUtil.getCurrentUserId();
            return VisitorIdentity.registeredUser(userId);
        } catch (BusinessException e) {
            // 未登录用户，使用IP地址
            String ipAddress = getClientIpAddress(request);
            return VisitorIdentity.anonymousUser(ipAddress);
        }
    }

    
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }
}
```

#### 访问追踪拦截器
```java
@Component
public class VisitTrackingInterceptor implements HandlerInterceptor {
    
    @Override
    public boolean preHandle(HttpServletRequest request, 
                           HttpServletResponse response, 
                           Object handler) throws Exception {
        
        // 只追踪首页访问
        if ("/".equals(request.getRequestURI())) {
            // 异步记录访问
            visitTrackingService.trackVisitAsync(request);
        }
        
        return true;
    }
}
```

## 技术实现细节

### 访问去重策略
- **已登录用户**: 基于用户ID + 5分钟时间窗口
- **匿名用户**: 基于IP地址 + UserAgent + 5分钟时间窗口
- **会话关联**: 使用前端生成的sessionId关联同一访问会话

### 数据收集时机
- **页面加载**: 页面完全加载后自动触发
- **SPA路由**: 单页应用路由变化时手动触发
- **异步处理**: 访问记录异步处理，不影响页面性能

### 异常处理
- **网络异常**: SDK静默失败，不影响用户体验

- **服务异常**: 后端异常不影响正常业务流程
- **数据异常**: 异常数据记录到错误日志，不中断统计

## 异常处理策略

### 常见错误码
- `ANALYTICS_001`: 访问记录失败
- `ANALYTICS_002`: 统计数据查询失败  
- `ANALYTICS_003`: 参数验证失败
- `ANALYTICS_004`: 权限不足
- `ANALYTICS_005`: 系统繁忙，请稍后重试

### 容错机制
- **访问记录失败**: 记录错误日志，不影响用户正常访问
- **统计查询超时**: 返回缓存数据或默认值
- **数据库连接异常**: 降级到只记录关键统计信息
- **缓存服务异常**: 直接查询数据库，记录告警

### 数据一致性保证
- **最终一致性**: 访问记录和统计数据采用最终一致性
- **补偿机制**: 定时任务检查并修复不一致数据
- **幂等性**: 所有统计更新操作保证幂等性

## 性能优化策略

### 1. 写入优化
- **异步处理**: 使用`@Async`异步写入访问记录
- **批量处理**: 内存缓冲区收集访问记录，定时批量写入
- **数据库优化**: 针对写入场景优化索引和表结构

### 2. 查询优化
- **Redis缓存**: 缓存实时统计数据和热点查询结果
- **预聚合**: 定时任务预计算日/小时汇总数据
- **索引优化**: 基于查询模式设计数据库索引

### 3. 简化的容量规划
- **预估访问量**: 按日均1万PV设计（根据实际业务调整）
- **存储策略**: 直接存储到MySQL，利用现有数据库
- **缓存策略**: 使用现有Redis实例，合理设置TTL

### 4. 基于现有技术栈的实现
- **异步处理**: Spring的`@Async` + 线程池
- **定时任务**: Spring的`@Scheduled`
- **缓存**: 现有的Redis配置
- **数据库**: 现有的MySQL + MyBatis-Plus

现在我们可以开始TDD开发了。您希望从哪个聚合开始？我建议先从 `VisitRecord` 聚合开始，因为它是整个系统的基础。



