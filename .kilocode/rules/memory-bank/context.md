剩余未翻译文档数：0

文档翻译状态清单：

```
docs/
├── advanced-usage/
│   ├── available-tools/
│   │   └── codebase-search.md ✓ [zh-CN] advanced-usage/available-tools/codebase-search.md
│   ├── custom-instructions.md ✓ [zh-CN] advanced-usage/custom-instructions.md
│   ├── custom-rules.md ✓ [zh-CN] advanced-usage/custom-rules.md
│   ├── large-projects.md ✓ [zh-CN] advanced-usage/large-projects.md
│   ├── local-models.md ✓ [zh-CN] advanced-usage/local-models.md
│   ├── memory-bank.md ✓ [zh-CN] advanced-usage/memory-bank.md
│   ├── prompt-engineering.md ✓ [zh-CN] advanced-usage/prompt-engineering.md
│   └── rate-limits-costs.md ✓ [zh-CN] advanced-usage/rate-limits-costs.md
├── basic-usage/
│   ├── adding-tokens.md ✓ [zh-CN] basic-usage/adding-tokens.md
│   ├── context-mentions.md ✓ [zh-CN] basic-usage/context-mentions.md
│   ├── how-tools-work.md ✓ [zh-CN] basic-usage/how-tools-work.md
│   ├── orchestrator-mode.md ✓ [zh-CN] basic-usage/orchestrator-mode.md
│   ├── the-chat-interface.md ✓ [zh-CN] basic-usage/the-chat-interface.md
│   ├── typing-your-requests.md ✓ [zh-CN] basic-usage/typing-your-requests.md
│   └── using-modes.md ✓ [zh-CN] basic-usage/using-modes.md
├── extending/
│   ├── contributing-to-kilo.md ✓ [zh-CN] extending/contributing-to-kilo.md
│   └── development-environment.md ✓ [zh-CN] extending/development-environment.md
├── faq.md ✓ [zh-CN] faq.md
├── features/
│   ├── api-configuration-profiles.md ✓ [zh-CN] features/api-configuration-proforms.md
│   ├── auto-approving-actions.md ✓ [zh-CN] features/auto-approving-actions.md
│   ├── browser-use.md ✓ [zh-CN] features/browser-use.md
│   ├── checkpoints.md ✓ [zh-CN] features/checkpoints.md
│   ├── code-actions.md ✓ [zh-CN] features/code-actions.md
│   ├── custom-modes.md ✓ [zh-CN] features/custom-modes.md
│   ├── enhance-prompt.md ✓ [zh-CN] features/enhance-prompt.md
│   ├── experimental/
│   │   ├── codebase-indexing.md ✓ [zh-CN] features/experimental/codebase-indexing.md
│   │   └── experimental-features.md ✓ [zh-CN] features/experimental/experimental-features.md
│   ├── fast-edits.md ✓ [zh-CN] features/fast-edits.md
│   ├── footgun-prompting.md ✓ [zh-CN] features/fotgun-prompting.md
│   ├── mcp/
│   │   ├── mcp-vs-api.md ✓ [zh-CN] features/mcp/mcp-vs-api.md
│   │   ├── overview.md ✓ [zh-CN] features/mcp/overview.md
│   │   ├── server-transports.md ✓ [zh-CN] features/mcp/server-transports.md
│   │   ├── using-mcp-in-kilo-code.md ✓ [zh-CN] features/mcp/using-mcp-in-kilo-code.md
│   │   └── what-is-mcp.md ✓ [zh-CN] features/mcp/what-is-mcp.md
│   ├── model-temperature.md ✓ [zh-CN] features/model-temperature.md
│   ├── more-features.md ✓ [zh-CN] features/more-features.md
│   ├── settings-management.md ✓ [zh-CN] features/settings-management.md
│   ├── shell-integration.md ✓ [zh-CN] features/shell-integration.md
│   ├── suggested-responses.md ✓ [zh-CN] features/suggested-responses.md
│   └── tools/
│       ├── access-mcp-resource.md ✓ [zh-CN] features/tools/access-mcp-resource.md
│       ├── apply-diff.md ✓ [zh-CN] features/tools/apply-diff.md
│       ├── ask-followup-question.md ✓ [zh-CN] features/tools/ask-followup-question.md
│       ├── attempt-completion.md ✓ [zh-CN] features/tools/attempt-completion.md
│       ├── browser-action.md ✓ [zh-CN] features/tools/browser-action.md
│       ├── execute-command.md ✓ [zh-CN] features/tools/execute-command.md
│       ├── list-code-definition-names.md ✓ [zh-CN] features/tools/list-code-definition-names.md
│       ├── list-files.md ✓ [zh-CN] features/tools/list-files.md
│       ├── new-task.md ✓ [zh-CN] features/tools/new-task.md
│       ├── read-filforms.md ✓ [zh-CN] features/tools/read-file.md
│       ├── search-files.md ✓ [zh-CN] features/tools/search-files.md
│       ├── switch-mode.md ✓ [zh-CN] features/tools/switch-mode.md
│       ├── tool-use-overview.md ✓ [zh-CN] features/tools/tool-use-overview.md
│       ├── use-mcp-tool.md ✓ [zh-CN] features/tools/use-mcp-tool.md
│       ├── write-to-file.md ✓ [zh-CN] features/tools/write-to-file.md
├── getting-started/
│   ├── connecting-api-provider.md ✓ [zh-CN] getting-started/connecting-api-provider.md
│   ├── installing.md ✓ [zh-CN] getting-started/installing.md
│   ├── setting-up.mdx ✓ [zh-CN] getting-started/setting-up.mdx
│   └── your-first-task.md ✓ [zh-CN] getting-started/your-first-task.md
├── index.mdx ✓ [zh-CN] index.mdx
├── providers/
│   ├── anthropic.md ✓ [zh-CN] providers/anthropic.md
│   ├── bedrock.md ✓ [zh-CN] providers/bedrock.md
│   ├── deepseek.md ✓ [zh-CN] providers/deepseek.md
│   ├── gemini.md ✓ [zh-CN] providers/gemini.md
│   ├── openrouter.md ✓ [zh-CN] providers/openrouter.md
│   ├── glama.md ✓ [zh-CN] providers/glama.md
│   ├── human-relay.md ✓ [zh-CN] providers/human-relay.md
│   ├── kilocode.md ✓ [zh-CN] providers/kilocode.md
│   ├── lmstudio.md ✓ [zh-CN] providers/lmstudio.md
│   ├── mistral.md ✓ [zh-CN] providers/mistral.md
│   ├── ollama.md ✓ [zh-CN] providers/ollama.md
│   ├── openai-compatible.md ✓ [zh-CN] providers/openai-compatible.md
│   ├── openai.md ✓ [zh-CN] providers/openai.md
│   ├── requesty.md ✓ [zh-CN] providers/requesty.md
│   ├── unbound.md ✓ [zh-CN] providers/unbound.md
│   ├── vertex.md ✓ [zh-CN] providers/vertex.md
│   └── vscode-lm.md ✓ [zh-CN] providers/vscode-lm.md
└── tips-and-tricks.md ✓ [zh-CN] tips-and-tricks.md
```
