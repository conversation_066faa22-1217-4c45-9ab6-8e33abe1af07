# 文档翻译管理规范

1. **目录结构同步**
   ```bash
   # 查询目录结构示例
   tree docs/zh-CN/
   ```

2. **翻译存储路径**
   - 所有中文翻译必须保持与英文文档相同的目录结构
   - 存储根目录：`docs/zh-CN/`
   - 示例对应关系：
     ```
     en-US: docs/getting-started.md
     zh-CN: docs/zh-CN/getting-started.md
     ```

3. **完整性校验**
   ```bash
   # 校验脚本执行示例
   node scripts/find-missing-translations.js --report
   ```
   - 每次构建时自动执行校验

4. **状态标记规范**
   - 完整翻译：`✓ [zh-CN] path/to/file.md`
   - 待更新翻译：`⚠ [zh-CN] path/to/file.md (v1.2→v1.3)`
   - 缺失翻译：`✗ [zh-CN] path/to/file.md`

5. **最佳实践**
   - 使用`i18n-alias`工具管理路径映射
   - 文档版本需与源文件保持同步 (±0.0.1)
   - 主版本发布后72小时内完成翻译更新

6. **翻译状态更新规则**
   - 每次完成翻译后，需立即更新 `.kilocode/rules/memory-bank/context.md` 中的翻译状态及 `docs/zh-CN` 开头的文件位置
   - 更新状态时需遵循以下步骤：
     1. 确认翻译文件已保存并提交
     2. 在 `context.md` 中定位对应文件
     3. 根据翻译完成情况更新状态标记：
        - 完整翻译：`✓ [zh-CN] path/to/file.md`
        - 待更新翻译：`⚠ [zh-CN] path/to/file.md (v1.2→v1.3)`
        - 缺失翻译：`✗ [zh-CN] path/to/file.md`
     4. 更新剩余未翻译文档计数器（自动减 1）
     5. 提交 `context.md` 的更改